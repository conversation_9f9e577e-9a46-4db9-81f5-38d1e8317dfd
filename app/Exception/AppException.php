<?php
/**
 * APP异常类
 * User: dhq
 * Date: 2018/10/11
 */

namespace App\Exception;

use App\Constant\ResponseCode;
use Throwable;

class AppException extends \RuntimeException
{
    public function __construct(string $message = "", int $code = 0, Throwable $previous = null)
    {
        if ($code === 0) {
            $code = ResponseCode::FAILURE;
        }
        parent::__construct($message, $code, $previous);
    }
}
