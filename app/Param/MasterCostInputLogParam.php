<?php


namespace App\Param;


use App\Logic\DMS\PermissionLogic;
use App\Service\PermissionService;
use App\Utils\DimensionTool;
use App\Utils\SqlParser;

class MasterCostInputLogParam extends AbstractParam
{
    public $platform = '';           // 平台
    public $apportion_agent_id = 0;  // 被分摊渠道ID
    public $clique_id = 0;           // 游戏id
    public $agent_group_id = 0;      // 被分摊渠道组ID
    public $start_date;              // 开始日期
    public $end_date;                // 结束日期
    public $money = 0;               // 返点后消耗
    public $page = 1;                // 页码
    public $rows = 200;              // 分页大小
    public $operator;                // 最后操作人

    public $game_permission = [];                   // 游戏权限
    public $agent_permission = [];                  // agent权限
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标


    public function setOperator($operator)
    {
        $this->operator = $operator;
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;

        $dimension_filter = [];
        // 处理维度筛选  就三个
        foreach ($this->dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value'] || $key === 'selected') {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'clique_id':
                    $dimension_filter[] = SqlParser::get($dimension, '', 't');
                    break;
                case 'agent':
                    $dimension_filter[] = SqlParser::get($dimension, '', 'agent');
                    break;
            }
        }
        $this->dimension_filter = $dimension_filter;

    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }
}
