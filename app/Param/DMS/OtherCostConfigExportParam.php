<?php

namespace App\Param\DMS;

use App\Container;
use App\Model\SqlModel\Zeda\OtherCostDetailModel;
use App\Model\SqlModel\Zeda\ProjectTeamModel;
use App\Param\AbstractParam;
use Common\EnvConfig;

class OtherCostConfigExportParam extends AbstractParam
{
    public $other_cost_config_id = 0;

    public $money_type;

    public $money;

    public $platform;

    public $proxy_type;

    public $start_month;

    public $end_month;

    public $business_type;

    public $root_games;

    public $project_teams;

    public $divide_money;

    public $divide = '0.0000';

    public $remark = '';

    public $agency = '';

    public $money_type_name;
    public $platform_name;
    public $proxy_type_name;
    public $business_type_name;
    public $sub_index;

    public function paramHook()
    {
        $this->platform_name = EnvConfig::PLATFORM_MAP[$this->platform] ?? ' ';
        $this->proxy_type_name = $this->proxy_type == 1 ? '买量' : $this->proxy_type == 2 ? '发行' : "买量/发行";
        $this->business_type_name = ProjectTeamModel::BUSINESS_TYPE_MAP[$this->business_type] ?? ' ';
    }

    /**
     * @param $other_cost_config_id
     * @return void
     */
    public function setOtherCostConfigId($other_cost_config_id)
    {
        $this->other_cost_config_id = $other_cost_config_id;
    }

    /**
     * @param $money
     * @return void
     */
    public function setOtherMoney($money)
    {
        $this->money = $money;
    }

    /**
     * @param $index
     * @return void
     */
    public function setSubConfigIndex($index)
    {
        $this->sub_index = $index + 1;
    }

    /**
     * @param $money_type
     * @return void
     */
    public function setOtherMoneyType($money_type)
    {
        $this->money_type = $money_type;
        $this->money_type_name = OtherCostDetailModel::MONEY_TYPE[$money_type] ?? '未知';
    }

    /**
     * @return string
     */
    public function getSaveFilePath()
    {
        return SRV_DIR . '/market/' . date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.xlsx';
    }

    /**
     * 获取项目组和根游戏项的展开数据
     * 背景：项目组和根游戏为多选，所以在展开数据时，不是一行一行的数据，数据个数不对等，会影响后面表格的展示
     * 处理：为了利于表格数据的查询，把他们的数据展开。找出项目组和根游戏哪个的数据个数比较多，取多的作为总行数，然后平铺处数据。
     *      例子：project_teams:[{"project_team_id":"1","project_team_name":"SLGSBU-北极星项目组"}, {"project_team_id":"2","project_team_name":"SLGSBU-皎月项目组"}]
     *          root_games:[{"root_game_id":"1502","root_game_name":"野兽领主：新世界"}]
     * 上面的项目组选了两项，根游戏一项，项目组作为总行数，对应行数的根游戏没有的话置空，得出下面的数据
     * |        项目组         |  根游戏          |
     * |   SLGSBU-北极星项目组  |  野兽领主：新世界  |
     * |   SLGSBU-皎月项目组   |                  |
     * @return array
     */
    public function getMaxCountData(): array
    {
        $list = [];
        if (count($this->root_games) > count($this->project_teams)) {
            foreach ($this->root_games as $index => $item) {
                $list[] = [
                    'project_team_item' => isset($this->project_teams[$index]) ? $this->project_teams[$index]['project_team_id'] . '/' . "{$this->project_teams[$index]['project_team_name']}" : ' ',
                    'root_game_item' => $item['root_game_id'] . '-' . $item['root_game_name'],
                ];
            }
        } else {
            foreach ($this->project_teams as $index => $item) {
                $list[] = [
                    'project_team_item' => $item['project_team_id'] . '/' . $item['project_team_name'],
                    'root_game_item' => isset($this->root_games[$index]) ? $this->root_games[$index]['root_game_id'] . '-' . $this->root_games[$index]['root_game_name'] : ' ',
                ];
            }
        }

        return $list;
    }
}