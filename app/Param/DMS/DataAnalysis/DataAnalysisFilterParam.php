<?php
/**
 * 数据分析报表参数的父类
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Container;
use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Model\SqlModel\Tanwan\DataAnalysis\DataAnalysisModel;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Utils\DimensionTool;
use App\Utils\Helpers;
use Illuminate\Support\Collection;

class DataAnalysisFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度
    public $target = [];                            // 所有指标
    public $user_id = 0;                            // 用户 id
    public $level = 0;
    public $rank_id = 0;

    public $start_time = 0;
    public $end_time = 0;
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $aggregation_time = '按日';
    public $dimension_type = 1;                     // SQL维度类型。1=按子 2=按根 3=回流
    public $show_week = 0;                          // 是否显示周几
    public $limit = 10000000;
    public $official_apportion = 0;                  // 自然量（官网）分摊
    public $flow_apportion = 0;                      // 直播间分摊
    public $preregister_apportion = 0;               // 预约分摊分摊
    public $apportion_type = 0;                      // 查询类型 1=自然量（官网）分摊 2=直播间分摊 3=预约分摊
    public $date_range = [];                         // 格式化后的时间数组 精确到天
    public $time_range = [];                         // 格式化后的时间数组 精确到时分秒
    public $database_type = 'mysql';                 // 数据库类型 mysql 和 clickhouse
    public $sub_table_is_use = 0;                    // 实时查询 分摊时，pay_test表或者 cost 表在子查询时，特殊处理的标识。DataAnalysisModel->handlerPayTestGroupBy()方法
    public $has_join_acc = 0;                        // 0 未join 1 已join


    /**
     * @var Collection
     */
    public $apportion_game_id;                       // 自然量分摊预先查出来的 game_id


    public function aggregationTimeFormat()
    {
        if (!in_array($this->aggregation_time, ['按日', '按月', '按周', '聚合'])) {
            throw new AppException('聚合参数异常');
        }
    }


    public function dimensionFormat()
    {
        if (in_array('date', $this->dimension)) {
            $key = array_search('date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (in_array('game_reg_date', $this->dimension)) {
            $key = array_search('game_reg_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (in_array('create_date', $this->dimension)) {
            $key = array_search('create_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (DimensionTool::needPushPlatform($this->dimension)) {
            array_unshift($this->dimension, 'platform');
        }
        // 去重
        $this->dimension = array_unique($this->dimension);
    }

    public function paramHook()
    {
        if ($this->start_time && Helpers::isDatetime($this->start_time) && $this->end_time && Helpers::isDatetime($this->end_time)) {
            $this->start_time = strtotime($this->start_time);
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399;
            $this->date_range = [date("Y-m-d", $this->start_time), date("Y-m-d", $this->end_time)];
            $this->time_range = [date("Y-m-d 00:00:00", $this->start_time), date("Y-m-d 23:59:59", $this->end_time)];
        } else {
            throw new AppException('缺失时间参数');
        }

        $this->handleDimension();
    }

    public function handleDimension()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);              // 维度处理 各种group by
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
        $this->user_id = Container::getSession()->user_id;
        [$this->level, $this->rank_id] = UserService::getLoginUserLevelAndRankId('dms');
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
        $this->level = $user_permission['level'];
        $this->rank_id = $user_permission['rank_id'];
        $this->user_id = $user_id;
    }


    public function setApportionType($apportion_type)
    {
        $this->apportion_type = $apportion_type;
    }


    /**
     * 标记本次查询为特殊子查询
     * @return void
     */
    public function setSubTableIsUse($sub_table_is_use = 1)
    {
        $this->sub_table_is_use = $sub_table_is_use;
    }

    public function setApportionGame()
    {
        $this->apportion_game_id = (new DataAnalysisModel())->getApportionGameID($this);

    }

}
