<?php
/**
 * 投放数据总览列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Constant\ApportionOverviewSqlMap;
use App\Constant\HourOverviewSqlMap;
use App\Constant\ServerOverviewSqlMap;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\CustomizedTargetModel;

class OverviewListFilterParam extends DataAnalysisFilterParam
{

    public $export_target = [];                     // 导出的指标
    public $compute_target = [];                    // 处理后需要计算的指标
    public $click_query_target = [];                // click表的指标
    public $action_query_target = [];               // action表的指标
    public $reg_query_target = [];                  // reg表的指标
    public $old_query_target = [];                  // old表的指标
    public $pay_query_target = [];                  // pay表的指标
    public $official_pay_query_target = [];          // official_pay表的指标
    public $cost_query_target = [];                 // cost表的指标
    public $profit_query_target = [];                // profit表的指标
    public $show_query_target = [];                 // show表的指标
    public $inspire_query_target = [];              // inspire表的指标
    public $media_query_target = [];                // 媒体表的指标
    public $agent_abnormal = false;                 // 是否选中了渠道异常这个特殊指标
    public $limit = 2500;
    public $hour_type = 'LTV';
    public $date_hour = -1;
    public $condition = [];                          // 维度筛选
    public $continuity_date = 0;                     // 连续时间区间 0=连续 1=不连续
    public $open_days = [1, 360];                     // open_days 只有运营区服总览需要用上
    public $time_type = 1;                           // 1=时间 2=开服时间
    public $combine_times = 0;                       // 合服次数
    public $hour_range = '';                         // 运营总览的小时范围
    public $true_cost = 0;                           // 是否实际消耗1是0否
    public $map_type = 0;                            // 是否地图分析1是0否
    public $exception_target = '';                   // 异常的指标
    public $cost_money = 0;                          // 分析异常记录的当前消耗
    public $customized_target = [];                  // 自定义指标
    public $reg_source_query_target = [];            // reg_source表的指标
    public $financial_cost = 0;                      // 财务消耗 1是0否
    public $exclude_anchor_cost = 0;                 // 是否剔除消耗类型add_type=18的成本
    public $login_query_target = [];
    public $customized_target_get = false;            // 是否查询过自定义指标
    public $select_type = 0;                          // 用户画像分析的类型 0=地域分析 2=性别分析 3=年龄分析
    public $analysis_type = 0;                        // 分析类型（仅用于性别/年龄分析）0=新增累计付费类型  1=同期新增并付费类型 2=同期活跃并付费

    // reg表的分表指标
    public $reg_target_especial_keys = [
        'reg_total_pay_count',
        'reg_total_pay_money',
        'lifetime_money',
        'pre_pay_way_ip_money',
        'pre_channel_pay',
        'pre_ios_pay',
        'pre_divide_pay_money',
        'pre_dehan_divide_money',
        'pre_server_money',
        'pre_applet_divide_money',
        'other_cost',
        'first_day_pay_times',
        'total_pay_times',
        'true_uid_count',
        'max_first_day_pay_money',
        'is_seventh_login',
        'is_fifteenth_login',
        'is_third_login',
        'is_thirty_login',
        'second_day_pay_money',
        'third_day_pay_money',
        'seventh_day_pay_money',
        'fifteenth_day_pay_money',
        'thirty_day_pay_money',
        'first_day_pay_money_within_6',
        'first_day_pay_money_within_18',
        'first_day_pay_once_within_30',
        'first_pay_24_hour_pay_count',
        'first_pay_24_hour_pay_money',
        'first_pay_24_hour_pay_times',
        'is_second_muid_login',
        'is_third_muid_login',
        'is_seventh_muid_login',
        'is_fifteenth_muid_login',
        'is_thirty_muid_login',
        "profit_day_360_pay",
        "profit_month_360_pay",
        "month_total_pay_money",
        "month_money",
        "key_role_level_count",
        "first_day_pay_once_key_money_count",
    ];

    // 预估的几个指标，需要一点特殊操作
    public $pre_target = [
        "lifetime_money",
        "pre_channel_pay",
        "pre_ios_pay",
        "pre_divide_pay_money",
        "pre_dehan_divide_money",
        "pre_server_money",
        "pre_applet_divide_money",
        "pre_pay_way_ip_money",
        "other_cost",
    ];


    public function paramHook()
    {
        parent::paramHook();
        $this->export_target = $this->target;    // 导出的指标先存起来
    }


    /**
     * 处理指标（普通投放总览）
     *
     * @param int $type 1=投放总览 2=运营总览
     * @param int $apportion_type 是否使用分摊SQL  0=否 1=是
     */
    public function handleTarget(int $type, int $apportion_type = 0)
    {
        $target_list = [];

        if ($apportion_type === 0) {
            $this->handleCustomizedTarget();
        }


        // 去重
        $this->target = array_values(array_unique($this->target));

        // 如果有首日回本率并且是投放总览 就要加上标准值这些
        if (count(array_intersect(['first_day_roi', 'first_day_standard_rate', 'seventh_day_standard_rate', 'thirty_day_standard_rate'], $this->target)) > 0 && $type === 1) {
            $this->target[] = 'max_first_day_pay_money';
        }

        foreach ($this->target as $item) {
            if (isset(ApportionOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(ApportionOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, ApportionOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
            if ($item === 'agent_abnormal') $this->agent_abnormal = true;
        }


        // uid_count.reg_uid_count,cost_money,ori_money搞成必有
        $target_list = array_merge($target_list, ['reg_uid_count', 'cost_money', 'ori_money']);
        // 去重
        $target_list = array_unique($target_list);

        $sql_map = new ApportionOverviewSqlMap();
        if ($apportion_type !== 0) {
            $sql_map->setApportion($apportion_type, $this->preregister_apportion);
        } else {
            $sql_map->setApportion(0);
        }

        // 获取各个表的指标
        $root_game_login_table_target = $sql_map->getRootGameLoginTable();
        $login_table_target = $sql_map->getLoginTable();
        $click_table_target = $sql_map->getClickTable();
        $media_show_table_target = $sql_map->getMediaShowTable();
        $action_table_target = $sql_map->getActionTable();
        $reg_table_target = $sql_map->getRegTable();
        $root_game_reg_table_target = $sql_map->getRootGameRegTable();
        $old_table_target = $sql_map->getOldTable();
        $root_game_old_table_target = $sql_map->getRootGameOldTable();
        $pay_table_target = $sql_map->getPayTable();
        $cost_table_target = $sql_map->getCostTable();
        $official_pay_table_target = $sql_map->getOfficialPayTable();
        $profit_table_target = $sql_map->getProfitTable();
        $inspire_table_target = $sql_map->getInspireTable();
        $reg_source_table_target = $sql_map->getRegSourceTable();
        $media_table_target = $sql_map->getMediaAD3Table();

        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset($click_table_target[$item]):
                    $this->click_query_target[$item] = $click_table_target[$item];
                    break;
                case isset($media_show_table_target[$item]):
                    $this->show_query_target[$item] = $media_show_table_target[$item];
                    break;
                case isset($action_table_target[$item]):
                    $this->action_query_target[$item] = $action_table_target[$item];
                    break;
                // 按根的reg 放在按子的前面
                case isset($root_game_reg_table_target[$item]) && ($this->dimension_type == 2 || $this->dimension_type == 3):
                    $this->reg_query_target[$item] = $root_game_reg_table_target[$item . '_' . $this->dimension_type] ?? $root_game_reg_table_target[$item];
                    break;
                case isset($reg_table_target[$item]):
                    $this->reg_query_target[$item] = $reg_table_target[$item];
                    break;
                // 按根的old 放按子的前面
                case isset($root_game_old_table_target[$item]) && ($this->dimension_type == 2 || $this->dimension_type == 3):
                    $this->old_query_target[$item] = $root_game_old_table_target[$item . '_' . $this->dimension_type] ?? $root_game_old_table_target[$item];
                    break;
                case isset($old_table_target[$item]):
                    $this->old_query_target[$item] = $old_table_target[$item];
                    break;
                case isset($pay_table_target[$item]):
                    $this->pay_query_target[$item] = $pay_table_target[$item];
                    break;
                case isset($official_pay_table_target[$item]):
                    $this->official_pay_query_target[$item] = $official_pay_table_target[$item];
                    break;
                case isset($cost_table_target[$item]):
                    $this->cost_query_target[$item] = $cost_table_target[$item];
                    break;
                case isset($profit_table_target[$item]):
                    $this->profit_query_target[$item] = $profit_table_target[$item];
                    break;
                case isset($reg_source_table_target[$item]):
                    $this->reg_source_query_target[$item] = $reg_source_table_target[$item];
                    break;
                case isset($root_game_login_table_target[$item]) && ($this->dimension_type == 2 || $this->dimension_type == 3):
                    $this->login_query_target[$item] = $root_game_login_table_target[$item];
                    break;
                case isset($login_table_target[$item]):
                    $this->login_query_target[$item] = $login_table_target[$item];
                    break;
                case isset($inspire_table_target[$item]):
                    $this->inspire_query_target[$item] = $inspire_table_target[$item];
                    break;
                case isset($media_table_target[$item]):
                    $this->media_query_target[$item] = $media_table_target[$item];
                    break;
            }
        }
    }


    /**
     * 处理指标（区服投放总览）
     */
    public function handleServerTarget()
    {
        $target_list = [];
        $this->handleCustomizedTarget();
        // 去重
        $this->target = array_values(array_unique($this->target));
        foreach ($this->target as $item) {
            if (isset(ServerOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(ServerOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, ServerOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // create_role_uid_count搞成必有
        $target_list = array_merge($target_list, ['create_role_uid_count']);
        // 去重
        $target_list = array_unique($target_list);
        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset(ServerOverviewSqlMap::NEED_REG_TABLE[$item]):
                    $this->reg_query_target[$item] = ServerOverviewSqlMap::NEED_REG_TABLE[$item];
                    break;
                case isset(ServerOverviewSqlMap::NEED_OLD_TABLE[$item]):
                    $this->old_query_target[$item] = ServerOverviewSqlMap::NEED_OLD_TABLE[$item];
                    break;
                case isset(ServerOverviewSqlMap::NEED_PAY_TABLE[$item]):
                    $this->pay_query_target[$item] = ServerOverviewSqlMap::NEED_PAY_TABLE[$item];
                    break;
            }
        }
    }

    /**
     * 处理指标（分时投放总览）
     */
    public function handleHourTarget()
    {
        $this->handleCustomizedTarget();
        $target_list = [];
        foreach ($this->target as $item) {
            if (isset(HourOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(HourOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, HourOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // first_pay_uid_count pay_money pay_uid_count reg_uid_count cost_money 必有
        $target_list = array_merge($target_list, ['first_pay_uid_count', 'pay_money', 'pay_times', 'pay_uid_count', 'reg_uid_count', 'cost_money']);
        // 去重
        $target_list = array_unique($target_list);
        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset(HourOverviewSqlMap::NEED_CLICK_TABLE[$item]):
                    $this->click_query_target[$item] = HourOverviewSqlMap::NEED_CLICK_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_ACTION_TABLE[$item]):
                    $this->action_query_target[$item] = HourOverviewSqlMap::NEED_ACTION_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_REG_TABLE[$item]):
                    $this->reg_query_target[$item] = HourOverviewSqlMap::NEED_REG_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_PAY_TABLE[$item]):
                    $this->pay_query_target[$item] = HourOverviewSqlMap::NEED_PAY_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_MEDIA_TABLE[$item]):
                    $this->media_query_target[$item] = HourOverviewSqlMap::NEED_MEDIA_TABLE[$item];
                    break;
            }
        }
    }

    public function handleExceptionTarget()
    {
        switch ($this->exception_target) {
            case 'first_day_roi':
                $this->compute_target = [
                    'select' => [
                        'reg' => ['sum( day_first_day_pay_money ) AS first_day_pay_money'],
                        't1'  => ['cast(IFNULL( reg.first_day_pay_money / cost.cost_money, 0 ) as DECIMAL(12,4)) first_day_roi'],
                        't2'  => ['cast(IFNULL(sum(first_day_pay_money)/sum(money),0) as DECIMAL(12,4)) first_day_roi'],
                    ],
                    'where'  => [
                        't1' => ['cost.cost_money >= 1000'],
                        't'  => ['t1.first_day_roi < t2.first_day_roi * 0.8'],
                    ],
                    'having' => [
                        't2' => ['sum( money ) >= 10000'],
                    ],
                ];
                break;
            case 'cost_per_reg':
                $this->compute_target = [
                    'select' => [
                        'reg' => ['sum( day_reg_uid_count ) AS reg_uid_count'],
                        't1'  => [
                            'reg.reg_uid_count',
                            'cast(IFNULL(cost.cost_money / reg.reg_uid_count, 0 ) as DECIMAL(12,4)) reg_uid_cost_money'
                        ],
                        't2'  => ['cast(IFNULL(sum(money) / sum(day_reg_uid_count), 0) as DECIMAL(12,4)) reg_uid_cost_money'],
                    ],
                    'where'  => [
                        't1' => ['reg.reg_uid_count >= 10'],
                        't'  => ['t1.reg_uid_cost_money > t2.reg_uid_cost_money * 1.2'],
                    ],
                    'having' => [
                        't2' => ['sum(day_reg_uid_count) >= 100'],
                    ],
                ];
                break;
            case 'first_day_pay_percent':
                $this->compute_target = [
                    'select' => [
                        'reg' => [
                            'sum( day_reg_uid_count ) AS reg_uid_count',
                            'sum( day_first_day_pay_count ) AS first_day_pay_count'],
                        't1'  => [
                            'reg.reg_uid_count',
                            'cast(IFNULL(reg.first_day_pay_count / reg.reg_uid_count, 0 ) as DECIMAL(12,4)) first_day_pay_rate'
                        ],
                        't2'  => ['cast(IFNULL(sum(first_day_pay_count) / sum(day_reg_uid_count), 0) as DECIMAL(12,4)) first_day_pay_rate'],
                    ],
                    'where'  => [
                        't1' => ['reg.reg_uid_count >= 10'],
                        't'  => ['t1.first_day_pay_rate < t2.first_day_pay_rate * 0.8'],
                    ],
                    'having' => [
                        't2' => ['sum(day_reg_uid_count) >= 100'],
                    ],
                ];
                break;
            case 'first_day_arppu':
                $this->compute_target = [
                    'select' => [
                        'reg' => [
                            'sum( day_first_day_pay_count ) AS first_day_pay_count',
                            'sum( day_first_day_pay_money ) AS first_day_pay_money'],
                        't1'  => [
                            'reg.first_day_pay_count',
                            'cast(IFNULL(reg.first_day_pay_money / reg.first_day_pay_count, 0 ) as DECIMAL(12,4)) first_day_pay_arpu'
                        ],
                        't2'  => ['cast(IFNULL(sum(first_day_pay_money) / sum(first_day_pay_count), 0) as DECIMAL(12,4)) first_day_pay_arpu'],
                    ],
                    'where'  => [
                        't1' => ['reg.first_day_pay_count >= 10'],
                        't'  => ['t1.first_day_pay_arpu < t2.first_day_pay_arpu * 0.8'],
                    ],
                    'having' => [
                        't2' => ['sum(first_day_pay_count) >= 100'],
                    ],
                ];
                break;
            default :
                break;
        }
    }

    public function handleExceptionTargetDetail()
    {
        switch ($this->exception_target) {
            case 'first_day_roi':
                $this->compute_target = [
                    'select' => [
                        'reg' => ['sum( day_first_day_pay_money ) AS first_day_pay_money'],
                        't1'  => [
                            'reg.first_day_pay_money',
                            'cast(IFNULL( reg.first_day_pay_money / cost.cost_money, 0 ) as DECIMAL(12,4)) first_day_roi'
                        ],
                        't2'  => [
                            'IFNULL(first_day_pay_money , 0 ) first_day_pay_money',
                            'cast(IFNULL(first_day_pay_money/money,0) as DECIMAL(12,4)) first_day_roi'
                        ],
                        'tt'  => [
                            't1.first_day_pay_money',
                            't2.first_day_pay_money mean_first_day_pay_money',
                            't1.first_day_roi',
                            't2.first_day_roi mean_first_day_roi',
                            'if(t1.first_day_roi < t2.first_day_roi * 0.8 and t1.cost_money>=1000 ,1,0) abnormal'
                        ],
                        'tt1' => [
                            'first_day_pay_money',
                            'mean_first_day_pay_money',
                            'first_day_roi',
                            'mean_first_day_roi'
                        ],
                        'tt2' => [
                            'cast(IFNULL( sum(first_day_pay_money) / sum(cost_money), 0 ) as DECIMAL(12,4)) first_day_roi',
                            'cast(IFNULL( sum(mean_first_day_pay_money) / sum(mean_cost_money), 0 ) as DECIMAL(12,4)) mean_first_day_roi',
                        ]
                    ],
                    'where'  => [],
                    'having' => [
                        'tt2' => ['cost_money >=1000 and first_day_roi < mean_first_day_roi * 0.8']
                    ],
                    'order'  => [
                        'tt2' => ['cost_money desc']
                    ]
                ];
                break;
            case 'cost_per_reg':
                $this->compute_target = [
                    'select' => [
                        'reg' => ['sum( day_reg_uid_count ) AS reg_uid_count'],
                        't1'  => [
                            'reg.reg_uid_count',
                            'cast(IFNULL(cost.cost_money / reg.reg_uid_count, 0 ) as DECIMAL(12,4)) reg_uid_cost_money'
                        ],
                        't2'  => [
                            'IFNULL( day_reg_uid_count, 0 )  reg_uid_count',
                            'cast(IFNULL(money/ day_reg_uid_count, 0) as DECIMAL(12,4)) reg_uid_cost_money'
                        ],
                        'tt'  => [
                            't1.reg_uid_count',
                            't2.reg_uid_count mean_reg_uid_count',
                            't1.reg_uid_cost_money',
                            't2.reg_uid_cost_money mean_reg_uid_cost_money',
                            'if(t1.reg_uid_cost_money > t2.reg_uid_cost_money * 1.2 and t1.reg_uid_count>=10 ,1,0) abnormal'
                        ],
                        'tt1' => [
                            'reg_uid_count',
                            'mean_reg_uid_count mean_reg_uid_count',
                            'reg_uid_cost_money',
                            'mean_reg_uid_cost_money'
                        ],
                        'tt2' => [
                            'sum(reg_uid_count) reg_uid_count',
                            'cast(IFNULL(sum(cost_money) / sum(reg_uid_count), 0) as DECIMAL(12,4)) reg_uid_cost_money',
                            'cast(IFNULL(sum(mean_cost_money) / sum(mean_reg_uid_count), 0) as DECIMAL(12,4)) mean_reg_uid_cost_money',
                        ]
                    ],
                    'where'  => [],
                    'having' => [
                        'tt2' => ['reg_uid_count>=10 and reg_uid_cost_money > mean_reg_uid_cost_money * 1.2']
                    ],
                    'order'  => [
                        'tt2' => ['reg_uid_count desc']
                    ]
                ];
                break;
            case 'first_day_pay_percent':
                $this->compute_target = [
                    'select' => [
                        'reg' => [
                            'sum( day_reg_uid_count ) AS reg_uid_count',
                            'sum( day_first_day_pay_count ) AS first_day_pay_count'],
                        't1'  => [
                            'reg.reg_uid_count',
                            'reg.first_day_pay_count',
                            'cast(IFNULL(reg.first_day_pay_count / reg.reg_uid_count, 0 ) as DECIMAL(12,4)) first_day_pay_rate'
                        ],
                        't2'  => [
                            'IFNULL( day_reg_uid_count, 0 )  reg_uid_count',
                            'IFNULL( first_day_pay_count, 0 ) first_day_pay_count',
                            'cast(IFNULL(first_day_pay_count / day_reg_uid_count, 0) as DECIMAL(12,4)) first_day_pay_rate'
                        ],
                        'tt'  => [
                            't1.reg_uid_count',
                            't2.reg_uid_count mean_reg_uid_count',
                            't1.first_day_pay_count',
                            't2.first_day_pay_count mean_first_day_pay_count',
                            't1.first_day_pay_rate',
                            't2.first_day_pay_rate mean_first_day_pay_rate',
                            'if(t1.first_day_pay_rate < t2.first_day_pay_rate * 0.8 and t1.reg_uid_count>=10 ,1,0) abnormal'
                        ],
                        'tt1' => [
                            'first_day_pay_count',
                            'mean_first_day_pay_count mean_first_day_pay_count',
                            'reg_uid_count',
                            'mean_reg_uid_count mean_reg_uid_count',
                            'first_day_pay_rate',
                            'mean_first_day_pay_rate'
                        ],
                        'tt2' => [
                            'sum(reg_uid_count) reg_uid_count',
                            'cast(IFNULL(sum(first_day_pay_count) / sum(reg_uid_count), 0) as DECIMAL(12,4)) first_day_pay_rate',
                            'cast(IFNULL(sum(mean_first_day_pay_count) / sum(mean_reg_uid_count), 0) as DECIMAL(12,4)) mean_first_day_pay_rate',
                        ]
                    ],
                    'where'  => [],
                    'having' => [
                        'tt2' => ['reg_uid_count>=10 and first_day_pay_rate < mean_first_day_pay_rate * 0.8']
                    ],
                    'order'  => [
                        'tt2' => ['reg_uid_count desc']
                    ]
                ];
                break;
            case 'first_day_arppu':
                $this->compute_target = [
                    'select' => [
                        'reg' => [
                            'sum( day_first_day_pay_count ) AS first_day_pay_count',
                            'sum( day_first_day_pay_money ) AS first_day_pay_money'],
                        't1'  => [
                            'reg.first_day_pay_money',
                            'reg.first_day_pay_count',
                            'cast(IFNULL(reg.first_day_pay_money / reg.first_day_pay_count, 0 ) as DECIMAL(12,4)) first_day_pay_arpu'
                        ],
                        't2'  => [
                            'IFNULL(first_day_pay_count, 0 ) first_day_pay_count',
                            'IFNULL(first_day_pay_money, 0 ) first_day_pay_money',
                            'cast(IFNULL(first_day_pay_money / first_day_pay_count, 0) as DECIMAL(12,4)) first_day_pay_arpu'
                        ],
                        'tt'  => [
                            't1.first_day_pay_count',
                            't2.first_day_pay_count mean_first_day_pay_count',
                            't1.first_day_pay_money',
                            't2.first_day_pay_money mean_first_day_pay_money',
                            't1.first_day_pay_arpu',
                            't2.first_day_pay_arpu mean_first_day_pay_arpu',
                            'if(t1.first_day_pay_arpu < t2.first_day_pay_arpu * 0.8 and t1.first_day_pay_count>=10,1,0) abnormal'
                        ],
                        'tt1' => [
                            'first_day_pay_money',
                            'mean_first_day_pay_money',
                            'first_day_pay_count',
                            'mean_first_day_pay_count mean_first_day_pay_count',
                            'first_day_pay_arpu',
                            'mean_first_day_pay_arpu'
                        ],
                        'tt2' => [
                            'sum(first_day_pay_count) first_day_pay_count',
                            'cast(IFNULL(sum(first_day_pay_money) / sum(first_day_pay_count), 0) as DECIMAL(12,4)) first_day_pay_arpu',
                            'cast(IFNULL(sum(mean_first_day_pay_money) / sum(mean_first_day_pay_count), 0) as DECIMAL(12,4)) mean_first_day_pay_arpu',
                        ]
                    ],
                    'where'  => [],
                    'having' => [
                        'tt2' => ['first_day_pay_count>=10 and first_day_pay_arpu < mean_first_day_pay_arpu * 0.8']
                    ],
                    'order'  => [
                        'tt2' => ['first_day_pay_count desc']
                    ]
                ];
                break;
            case 'cost_per_first_day_pay':
                $this->compute_target = [
                    'select' => [
                        'reg' => [
                            'sum( day_first_day_pay_count ) AS first_day_pay_count'],
                        't1'  => [
                            'reg.first_day_pay_count',
                            'cast(IFNULL( cost.cost_money / reg.first_day_pay_count, 0 ) as DECIMAL(12,4)) first_day_pay_cost_money'
                        ],
                        't2'  => [
                            'IFNULL(first_day_pay_count , 0 ) first_day_pay_count',
                            'cast(IFNULL(money/first_day_pay_count,0) as DECIMAL(12,4)) first_day_pay_cost_money'
                        ],
                        'tt'  => [
                            't1.first_day_pay_cost_money',
                            't2.first_day_pay_cost_money mean_first_day_pay_cost_money',
                            't1.first_day_pay_count',
                            't2.first_day_pay_count mean_first_day_pay_count',
                            'if(t1.first_day_pay_cost_money > t2.first_day_pay_cost_money * 1.2 and t1.first_day_pay_count>=10,1,0) abnormal'
                        ],
                        'tt1' => [
                            'first_day_pay_count',
                            'mean_first_day_pay_count',
                            'first_day_pay_cost_money',
                            'mean_first_day_pay_cost_money'
                        ],
                        'tt2' => [
                            'sum(first_day_pay_count) first_day_pay_count',
                            'cast(IFNULL( sum(cost_money) / sum(first_day_pay_count), 0 ) as DECIMAL(12,4)) first_day_pay_cost_money',
                            'cast(IFNULL( sum(mean_cost_money) / sum(mean_first_day_pay_count), 0 ) as DECIMAL(12,4)) mean_first_day_pay_cost_money',
                        ]
                    ],
                    'where'  => [],
                    'having' => [
                        'tt2' => ['first_day_pay_count>=10  and first_day_pay_cost_money > mean_first_day_pay_cost_money * 1.2']
                    ],
                    'order'  => [
                        'tt2' => ['first_day_pay_count desc']
                    ]
                ];
                break;
            case 'first_day_ltv':
                $this->compute_target = [
                    'select' => [
                        'reg' => [
                            'sum( day_reg_uid_count ) AS reg_uid_count',
                            'sum( day_first_day_pay_money ) AS first_day_pay_money'],
                        't1'  => [
                            'reg.reg_uid_count',
                            'reg.first_day_pay_money',
                            'cast(IFNULL(reg.first_day_pay_money /reg.reg_uid_count, 0 ) as DECIMAL(12,4)) first_day_ltv'
                        ],
                        't2'  => [
                            'IFNULL( day_reg_uid_count, 0 )  reg_uid_count',
                            'IFNULL(first_day_pay_money, 0 ) first_day_pay_money',
                            'cast(IFNULL(first_day_pay_money / day_reg_uid_count, 0) as DECIMAL(12,4)) first_day_ltv'
                        ],
                        'tt'  => [
                            't1.reg_uid_count',
                            't2.reg_uid_count mean_reg_uid_count',
                            't1.first_day_pay_money',
                            't2.first_day_pay_money mean_first_day_pay_money',
                            't1.first_day_ltv',
                            't2.first_day_ltv mean_first_day_ltv',
                            'if(t1.first_day_ltv < t2.first_day_ltv * 0.8 and t1.reg_uid_count>=10,1,0) abnormal'
                        ],
                        'tt1' => [
                            'reg_uid_count',
                            'mean_reg_uid_count',
                            'first_day_pay_money',
                            'mean_first_day_pay_money',
                            'first_day_ltv',
                            'mean_first_day_ltv'
                        ],
                        'tt2' => [
                            'sum(reg_uid_count) reg_uid_count',
                            'cast(IFNULL(sum(first_day_pay_money) / sum(reg_uid_count), 0) as DECIMAL(12,4)) first_day_ltv',
                            'cast(IFNULL(sum(mean_first_day_pay_money) / sum(mean_reg_uid_count), 0) as DECIMAL(12,4)) mean_first_day_ltv',
                        ]
                    ],
                    'where'  => [],
                    'having' => [
                        'tt2' => ['reg_uid_count>=10 and first_day_ltv < mean_first_day_ltv * 0.8']
                    ],
                    'order'  => [
                        'tt2' => ['reg_uid_count desc']
                    ]
                ];
                break;
            default :
                break;
        }
    }

    public function switchColumnKey()
    {
        switch ($this->hour_type) {
            case 'LTV':
                $key_prefix = 'ltv_';
                break;
            case '回本率':
                $key_prefix = 'roi_';
                break;
            case '每小时新付费人数':
                $key_prefix = 'hncount_';
                break;
            case '每小时付费人数':
                $key_prefix = 'hcount_';
                break;
            case '每小时付费率':
                $key_prefix = 'hrate_';
                break;
            case '每小时付费金额':
                $key_prefix = 'hpay_';
                break;
            case '每小时ltv':
                $key_prefix = 'hltv_';
                break;
            case '累计付费率':
                $key_prefix = 'trate_';
                break;
            case '累计付费金额':
                $key_prefix = 'tpay_';
                break;
            case '累计付费arpu':
                $key_prefix = 'tarpu_';
                break;
            case '媒体点击人数':
                $key_prefix = 'click_count_';
                break;
            case '落地页点击人数':
                $key_prefix = 'click_count_ldy_';
                break;
            case '激活人数':
                $key_prefix = 'action_count_';
                break;
            case '注册人数':
                $key_prefix = 'reg_uid_count_';
                break;
            case '注册付费人数':
                $key_prefix = 'reg_pay_uid_count_';
                break;
            case '注册付费金额':
                $key_prefix = 'pay_money_';
                break;
            case '总付费人数':
                $key_prefix = 'pay_uid_count_';
                break;
            case '总付费金额':
                $key_prefix = 'total_pay_money_';
                break;
            case '累计总付费金额':
                $key_prefix = 'sum_total_pay_money_';
                break;
            case '累计新付费人数':
                $key_prefix = 'tcount_';
                break;
            case '消耗':
                $key_prefix = 'cost_money_';
                break;
            case '注册付费人数(首次)':
                $key_prefix = 'reg_first_pay_uid_count_';
                break;
            case '每小时付费次数':
                $key_prefix = 'pay_times_';
                break;
            case '累计付费次数':
                $key_prefix = 'tpay_times_';
                break;
            case '付费次数成本':
                $key_prefix = 'pay_times_cost_';
                break;
            default:
                throw new AppException('汇总指标不支持下载，请选择其他指标');
        }

        return $key_prefix;
    }

    private function handleCustomizedTarget()
    {
        // 把自定义指标里面需要算的指标拿出来
        $customized_target = [];
        if ($this->customized_target && !$this->customized_target_get) {
            $this->customized_target_get = true;
            $customized_target_list = (new CustomizedTargetModel())->getTargetById($this->customized_target);
            foreach ($customized_target_list as &$target_item) {
                $formula = json_decode($target_item->formula, true);
                if (isset($formula['column']) && is_array($formula['column'])) {
                    $customized_target = array_merge($customized_target, $formula['column']);
                }
                $target_item->formula = $formula;
            }
            $this->customized_target = $customized_target_list;  // 存起来，计算的时候使用
            $this->target = array_merge($this->target, $customized_target);
        }
    }

    // 转换指标为用户分群的指标
    public function switchUserGroupTarget($dimension_type)
    {
        // 按回流的特除处理，因为按回流跟按根字段一样 所以当做按根来处理就好了
        if ($dimension_type == 3) {
            $dimension_type = 2;
        }


        // 需要把action、click、show表置空
        $this->action_query_target = [];
        $this->click_query_target = [];
        $this->show_query_target = [];

        // 获取各个表的指标
        $sql_map = new ApportionOverviewSqlMap();
        $sql_map->setApportion($this->apportion_type, $this->preregister_apportion);
        $user_group_pay_table_target = $sql_map->getUserGroupPayTable();
        $user_group_root_game_pay_table_target = $sql_map->getUserGroupRootGamePayTable();
        $user_group_reg_table_target = $sql_map->getUserGroupRegTable();
        $user_group_root_game_reg_table_target = $sql_map->getUserGroupRootGameRegTable();

        // 转换一下查询指标的SQL
        foreach ($this->reg_query_target as $key => $item) {
            if (isset($user_group_reg_table_target[$key])) {
                $this->reg_query_target[$key] = $dimension_type == 2 ? $user_group_root_game_reg_table_target[$key] : $user_group_reg_table_target[$key];
            }
        }
        foreach ($this->pay_query_target as $key => $item) {
            if (isset($user_group_pay_table_target[$key])) {
                $this->pay_query_target[$key] = $dimension_type == 2 ? $user_group_root_game_pay_table_target[$key] : $user_group_pay_table_target[$key];
            }
        }
    }

    /**
     * 标记本次查询为分摊查询
     *
     * @param $target_type 1=投放总览 2=运营总览
     * @param $apportion_type 1=自然量分摊 2=直播间分摊 3=预约分摊
     */
    public function markApportion($target_type, $apportion_type)
    {
        $this->setApportionType($apportion_type);
        $this->handleTarget($target_type, $apportion_type);
    }
}
