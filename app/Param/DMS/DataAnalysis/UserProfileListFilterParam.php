<?php
/**
 * 投放数据总览列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Exception\AppException;


class UserProfileListFilterParam extends DataAnalysisFilterParam
{

    public $age_filter = [];                        // 年龄区间筛选
    public $age_filter_format = [];                 // 年龄区间筛选格式化


    /**
     * 处理开服区间筛选 "加层级"
     */
    public function ageFilterFormat()
    {
//        $this->age_filter = [[1, 100], [101, 200], [201]];
        $open_days_filter_format = [];
        $index = 0;
        $last_level_value = 0;
        $last_index = count($this->age_filter) - 1;
        foreach ($this->age_filter as $index => $item) {
            // 最后一个区间没关闭的情况，比如：[100,无穷大)
            if ($index === $last_index && !isset($item[1])) {
                // 判断付费区间
                if ($last_level_value > $item[0]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-无穷大";
                $open_days_filter_format[$key] = $item;
            } else {
                // 判断付费区间
                if ($last_level_value > $item[1]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-{$item[1]}";
                $open_days_filter_format[$key] = $item;
                // 判断开服区间
                if ($item[1] < $item[0]) {
                    throw new AppException('错误的区间范围');
                }

                $last_level_value = $item[1];
            }

        }
        $index++;
        $key = "{$index}-others";
        $open_days_filter_format[$key] = [];
        $this->age_filter_format = $open_days_filter_format;
    }



    public function paramHook()
    {
        $this->ageFilterFormat();
        parent::paramHook();
    }
}
