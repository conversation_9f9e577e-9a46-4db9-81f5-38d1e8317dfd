<?php
/**
 * 付费情况自然月 参数对象
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2021-06-18
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Exception\AppException;
use App\Utils\Helpers;

class PaymentMonthListParam extends DataAnalysisFilterParam
{

    public $month_list = [];                        // 处理后的指标数组

    public $day_reg_uid_count = 0;                  // 注册数筛选
    public $default_sort = 1;                       // 1 指标排序 2默认排序 3维度排序

    public $type = '回本率';                         // 列表数据类型
    public $true_cost = 0;                          // 真实消耗
    public $blocked = 0;                            //  是否屏蔽当月
    public $to_month = 0;                           // 所选的开始日期到今天所能达到最远的自然月数
    public $target = [
        1, 2, 3, 4, 5, 6, 7, 8,
        9, 10, 11, 12, 13, 14, 15,
        16, 17, 18, 19, 20, 21, 22, 23, 24
    ];                                               // 指标固定
    public $financial_cost = 0;                      // 财务消耗 1是0否
    public $money_type = 0;                         // 0=总付费，1=游戏充值，2激励广告





    /**
     *
     * @throws \Exception
     */
    public function setMonthList()
    {
        if ($this->target) {
            foreach ($this->target as $item) {
                if ($this->to_month >= $item) {
                    $this->month_list[] = intval($item);
                }
            }

            // 排序
            asort($this->month_list);
            $this->month_list = array_values($this->month_list);
        }
    }

    /**
     * @throws \Exception
     */
    public function paramHook()
    {
        parent::paramHook();

        // 判断要计算到多少月的付费
        $start_date = date("Y-m-d", $this->start_time);
        $this->to_month = Helpers::diffMonth($start_date);
        $this->setMonthList();

        // 注册数、创角数
        if (!is_numeric($this->day_reg_uid_count)) {
            throw new AppException('注册数过滤请输入数字');
        }
    }




    /**
     * 导出时，处理一下固定列
     *
     * @return array
     */
    public function fixedHeader()
    {
        // 日期、注册数等固定列
        $header = ['game_reg_date'];
        $header[] = 'day_cost_money';
        $header[] = 'reg_cost';
        $header[] = 'day_reg_uid_count';

        switch ($this->type) {
            case '累计付费':
                $header[] = 'current_tpay';
                break;
            case 'LTV':
                $header[] = 'current_ltv';
                break;
            case '回本率':
                $header[] = 'current_roi';
                break;
            case '累计付费率':
                $header[] = 'current_trate';
                break;
            case '累计付费ARPU':
                $header[] = 'current_tarpu';
                break;
            case '累计付费成本':
                $header[] = 'current_cost';
                break;
        }

        return $header;
    }


    /**
     * 导出时，列的key值选择
     * 根据选择的type来决定
     */
    public function switchColumnKey()
    {
        switch ($this->type) {
            case '回本率':
                $key_prefix = 'roi_';
                break;
            case '每月回本率':
                $key_prefix = 'proi_';
                break;
            case 'LTV':
                $key_prefix = 'ltv_';
                break;
            case '每月LTV':
                $key_prefix = 'pltv_';
                break;
            case '每月付费':
                $key_prefix = 'ppay_';
                break;
            case '累计付费':
                $key_prefix = 'tpay_';
                break;
            case '每月付费人数':
                $key_prefix = 'pcount_';
                break;
            case '每月付费ARPU':
                $key_prefix = 'parpu_';
                break;
            case '累计付费率':
                $key_prefix = 'trate_';
                break;
            case '每月付费率':
                $key_prefix = 'prate_';
                break;
            case '累计付费ARPU':
                $key_prefix = 'tarpu_';
                break;
            case '累计付费成本':
                $key_prefix = 'cost_';
                break;
            case '累计付费人数':
                $key_prefix = 'tcount_';
                break;
            case '每月ARPU':
                $key_prefix = 'arpu_';
                break;
            case '每月付费次数':
                $key_prefix = 'dpaytimes_';
                break;
            case '累计付费次数':
                $key_prefix = 'tpaytimes_';
                break;
            default:
                $key_prefix = 'roi_';
        }

        return $key_prefix;
    }




}
