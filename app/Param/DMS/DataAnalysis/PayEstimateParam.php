<?php
/**
 * 付费预估列表筛选条件
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class PayEstimateParam extends DataAnalysisFilterParam
{

    public $dimension_raw = [];                     // 最原始的维度，去除分成的时候需要用到这个玩意
    public $day_list = [];                          // 处理后的指标（天数）数组
    public $header_day_list = [];                   // 导出需要的指标（天数）数组
    public $type = '回本率';                         // 列表数据类型
    public $ios_addition = 0;                       // IOS加成
    public $rm_divide = 0;                          // 去除分成
    public $filter_toutiao = 0;                     // 是否过滤头条
    public $true_cost = 0;                          // 是否实际消耗1是0否
    public $financial_cost = 0;                      // 财务消耗 1是0否
    public $rm_dim_main_game_id_by_divide = 0;       // 去除分成时，是否删除默认添加的main_game_id维度。 0不删除 1删除


    /**
     * 把各种需要算的天数提取出来
     * target是类似这种：day_{num} 或 day_{num1}_{num2}
     * 需要把num提出来放day_list数组
     * 需要判断真实需要到达的天数 比如开始日期是今天 就不可能存在2日 3日这中
     * 导出的header_day_list不需要判断
     */
    public function setDayList()
    {
        if ($this->target) {
            foreach ($this->target as $item) {
                $tmp = explode('_', $item);
                $tmp[1] = intval($tmp[1]);
                $this->day_list[] = $tmp[1];
                $this->header_day_list[] = $tmp[1];
                if (isset($tmp[2])) {
                    for ($i = $tmp[1] + 1; $i <= $tmp[2]; $i++) {
                        $this->day_list[] = $i;
                        $this->header_day_list[] = $i;
                    }
                }
            }

            // xx日排序
            asort($this->header_day_list);
            $this->header_day_list = array_values($this->header_day_list);
            $this->day_list = array_values($this->day_list);
        }
    }


    public function dimensionFormat()
    {
        if (in_array('game_reg_date', $this->dimension)) {
            $key = array_search('game_reg_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }

        if (DimensionTool::needPushPlatform($this->dimension)) {
            array_unshift($this->dimension, 'platform');
        }
        // 付费预估去除分成的时候按这个维度聚合
        $this->dimension_raw = $this->dimension;
        $this->dimension_raw = array_unique($this->dimension_raw);

        //背景：去除分成查询条件下，必须增加main_game_id的维度才能查到数据
        //解决：去除分成必须选中main_game_id维度。
        if ($this->rm_divide && !in_array('main_game_id', $this->dimension)) {
            $this->dimension[] = 'main_game_id';
            // 背景：假设用户没选中main_game_id维度查询，而是通过上面array_push进去的，会导致后面数据以month + main_game_id维度展现，并不是按月展现
            // 解决：rm_dim_main_game_id_by_divide 赋值 1 ，在后面维度计算时，删除main_game_id的维度
            $this->rm_dim_main_game_id_by_divide = 1;
        }
        // 去重
        $this->dimension = array_unique($this->dimension);
    }


    public function paramHook()
    {
        $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
//        $to_day = strtotime(date("Y-m-d 23:59:59")); // 当天23:59:59
//        $max_end_date = $to_day - 86400 * 8;
//        $this->end_time = min($this->end_time, $max_end_date);
        $this->date_range = [date("Y-m-d", $this->start_time), date("Y-m-d", $this->end_time)];

        $this->handleDimension();
        $this->setDayList();
    }

    /**
     * 导出时，处理一下固定列
     */
    public function fixedHeader()
    {
        // 日期、注册数等固定列
        if ($this->aggregation_time !== '聚合') {
            $header = ['game_reg_date'];
        }
        // 注册数
        $header[] = 'day_reg_uid_count';
        // 消耗
        $header[] = 'cost_money';
        $header[] = 'reg_cost';
        // 当前累计付费、当前回本率
        $header[] = 'current_tpay';
        $header[] = 'current_roi';

        return $header;
    }

    /**
     * 导出时，列的key值选择
     * 根据选择的type来决定
     */
    public function switchColumnKey()
    {
        switch ($this->type) {
            case '回本率':
                $key_prefix = 'roi_';
                break;
            case 'LTV':
                $key_prefix = 'ltv_';
                break;
            case '累计付费':
                $key_prefix = 'tpay_';
                break;
            case '付费ARPU':
                $key_prefix = 'tarpu_';
                break;
            default:
                $key_prefix = 'roi_';
        }

        return $key_prefix;
    }

}
