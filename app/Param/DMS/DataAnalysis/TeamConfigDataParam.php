<?php


namespace App\Param\DMS\DataAnalysis;


use App\Constant\ApportionOverviewSqlMap;
use App\Exception\AppException;
use App\Utils\DimensionTool;

class TeamConfigDataParam extends DataAnalysisFilterParam
{
    public $config_ids = [];

    public $aggregation_type = '';                  // 时间聚合类型 按日 按月 按周 聚合
    public $start_date;                             // 开始时间
    public $end_date;                               // 结束时间
    public $true_cost = 0;                          // 是否实际消耗1是0否
    public $compute_target = [];                    // 处理后需要计算的指标
    public $reg_query_target = [];                  // reg表的指标
    public $old_query_target = [];                  // old表的指标
    public $pay_query_target = [];                  // pay表的指标
    public $cost_query_target = [];                 // cost表的指标
    public $profit_query_target = [];               // profit表的指标
    public $root_game_reg_query_target = [];        // root_game_reg表的指标
    public $predict_query_target = [];              // predict表的指标
    public $team = null;

    /**
     * 兼容一下旧参数
     * @return void
     */
    public function handleAggregationTime()
    {
        // 如果前端传了一个aggregation_type进来，则用这个
        if ($this->aggregation_type) {
            $this->aggregation_time = $this->aggregation_type;
        }

    }




    public function setDimensionFilter($dimension_filter)
    {
        $this->dimension_filter_raw = $dimension_filter; // 处理前的维度
        $this->dimension_filter = DimensionTool::handleDimensionFilter($dimension_filter); // 处理维度筛选
    }


    public function paramHook()
    {
        $this->handleAggregationTime();
        if ($this->start_date && $this->end_date) {
            $this->start_date = strtotime(date("Ymd", strtotime($this->start_date)));
            $this->end_date = strtotime(date("Ymd", strtotime($this->end_date))) + 86399;
        } else {
            throw new AppException('请选择日期');
        }

        // 指标验证
        $allow_fields = ['cost_money', 'reg_uid_count', 'cost_per_reg', 'first_day_pay_percent', 'first_day_arppu', 'total_pay_money',
            'first_day_pay_money', 'first_day_ltv', 'ltv', 'total_pay_percent', 'arppu', 'reg_total_pay_money',
            'reg_uid_new_pay_percent', 'range_new_arppu', 'reg_uid_new_pay_money', 'old_uid_pay_percent',
            'range_old_arppu', 'roi', 'first_day_roi', 'old_uid_pay_money', 'range_uid_pay_percent', 'range_arppu',
            'game_pay_money', 'predic_roi', 'pay_way_pay_money', 'divide_pay_money', 'profit', 'second_login_percent', 'tpay_360',
            'is_third_login_percent', 'is_seventh_login_percent', 'first_day_standard_rate', 'roi_2', 'roi_3', 'roi_7', 'roi_15', 'roi_30',
            'pre_game_360_pay', 'pre_channel_pay', 'pre_ios_pay', 'pre_divide_pay_money', 'pre_game_360_profit', 'ori_money'
        ];

        if (empty($this->target)) {
            throw new AppException('查询指标不能为空');
        }
        if (array_diff($this->target, $allow_fields)) {
            throw new AppException('查询指标不存在');
        }
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension); // 处理维度
    }

    public function setTimeRange($time_range)
    {
        if (is_array($time_range) && count($time_range) === 2 && $time_range[0] && $time_range[1]) {
            $this->time_range[0] = strtotime(date("Ymd", strtotime($time_range[0])));
            $this->time_range[1] = strtotime(date("Ymd", strtotime($time_range[1]))) + 86399;
        }
    }

    /**
     * 处理指标归类
     */
    public function handleTarget($is_apportion = 0)
    {
        $target_list = [];
        // 去重
        $this->target = array_unique($this->target);

        foreach ($this->target as $item) {
            if (isset(ApportionOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(ApportionOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, ApportionOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // 去重
        $target_list = array_unique($target_list);

        $sql_map = new ApportionOverviewSqlMap();
        $sql_map->setApportion($is_apportion);

        // 获取各个表的指标
        $reg_table_target = $sql_map->getRegTable();
        // 特殊处理三个指标，因为跟投放总览同名，而且计算方式不一样。所以这里重新定义一下SQL
        $reg_table_target['pre_channel_pay'] = "sum(day_first_day_pay_money * ifnull(multiple, 0) * ifnull(channel_rate,  0) $sql_map->apportion_sum_str) as pre_channel_pay";
        $reg_table_target['pre_ios_pay'] = "sum(day_first_day_pay_money * ifnull(multiple, 0) * ifnull(ios_percent,  0) $sql_map->apportion_sum_str) as pre_ios_pay";
        $reg_table_target['pre_divide_pay_money'] = "sum(day_first_day_pay_money * ifnull(multiple, 0) *  ( ifnull( t4.divide_percent, 0 ) ) $sql_map->apportion_sum_str ) as pre_divide_pay_money";


        $root_game_reg_table_target = $sql_map->getRootGameRegTable();
        $old_table_target = $sql_map->getOldTable();
        $root_game_old_table_target = $sql_map->getRootGameOldTable();
        $pay_table_target = $sql_map->getPayTable();
        $cost_table_target = $sql_map->getCostTable();
        $profit_table_target = $sql_map->getProfitTable();
        $pre_dict_pay_table_target = $sql_map->getPreDictPayTable();
        $team_config_data_table_target = $sql_map->getTeamConfigDataTable();


        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset($team_config_data_table_target[$item]):
                    $this->root_game_reg_query_target[$item] = $team_config_data_table_target[$item];
                    break;
                // 按根的reg 放在按子的前面
                case isset($root_game_reg_table_target[$item]):
                    $this->root_game_reg_query_target[$item] = $root_game_reg_table_target[$item];
                    break;
                case isset($reg_table_target[$item]):
                    $this->reg_query_target[$item] = $reg_table_target[$item];
                    break;
                // 按根的old 放按子的前面
                case isset($root_game_old_table_target[$item]):
                    $this->old_query_target[$item] = $root_game_old_table_target[$item];
                    break;
                case isset($old_table_target[$item]):
                    $this->old_query_target[$item] = $old_table_target[$item];
                    break;
                case isset($pay_table_target[$item]):
                    $this->pay_query_target[$item] = $pay_table_target[$item];
                    break;
                case isset($cost_table_target[$item]):
                    $this->cost_query_target[$item] = $cost_table_target[$item];
                    break;
                case isset($profit_table_target[$item]):
                    $this->profit_query_target[$item] = $profit_table_target[$item];
                    break;
                case isset($pre_dict_pay_table_target[$item]):
                    $this->predict_query_target[$item] = $pre_dict_pay_table_target[$item];
                    break;
            }
        }
    }

    public function markApportion($apportion_type)
    {
        $this->setApportionType($apportion_type);
        $this->handleTarget($apportion_type);
    }
}
