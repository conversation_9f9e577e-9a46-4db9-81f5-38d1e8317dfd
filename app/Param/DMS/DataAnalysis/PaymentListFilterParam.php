<?php
/**
 * 付费情况列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Constant\RouteID;
use App\Exception\AppException;

class PaymentListFilterParam extends DataAnalysisFilterParam
{

    public $day_list = [];                          // 处理后的指标（天数）数组
    public $header_day_list = [];                   // 导出需要的指标（天数）数组
    public $day_reg_uid_count = 0;                  // 注册数筛选，要>=这个数
    public $day_create_role_id_count = 0;           // 创角数筛选，要>=这个数
    public $rm_divide = 0;                          // 去除分成 1表示去除
    public $type = '回本率';                         // 列表数据类型
    public $days = 0;                               // 所选的开始日期到今天一共需要算多少天的付费情况
    public $day_cost_money = 0;                     // 是否有消耗
    public $platform_belong = '';                   // 平台归属
    public $group_belong = '';                      // 集团归属
    public $default_sort = 1;                       // 1 指标排序 2默认排序 3维度排序
    public $blocked = false;                        // 是否屏蔽当日
    public $open_days = [1, 34];                    // open_days 只有区服需要用上 开服时间
    public $combine_times = 0;                      // 合服次数
    public $export_type = 1;                        // 导出类型  1=账号 2=区服
    public $true_cost = 0;                           // 是否实际消耗1是0否
    public $financial_cost = 0;                      // 财务消耗 1是0否
    public $money_type = 0;                         // 0=总付费，1=游戏充值，2激励广告


    /**
     * 把各种需要算的天数提取出来
     * target是类似这种：day_{num} 或 day_{num1}_{num2}
     * 需要把num提出来放day_list数组
     * 需要判断真实需要到达的天数 比如开始日期是今天 就不可能存在2日 3日这中
     * 导出的header_day_list不需要判断
     */
    public function setDayList()
    {
        if ($this->target) {
            foreach ($this->target as $item) {
                // 消耗指标特殊处理一下
                if ($item === 'day_cost_money') {
                    $this->day_cost_money = 1;
                    continue;
                }
                $tmp = explode('_', $item);
                $tmp[1] = intval($tmp[1]);
                if ($this->days >= $tmp[1]) {
                    $this->day_list[] = $tmp[1];
                }
                $this->header_day_list[] = $tmp[1];
                if (isset($tmp[2])) {
                    $tmp[2] = intval($tmp[2]);
                    for ($i = $tmp[1] + 1; $i <= $tmp[2]; $i++) {
                        if ($this->days >= $i) $this->day_list[] = intval($i);
                        $this->header_day_list[] = intval($i);
                    }
                }
            }

            // xx日排序
            asort($this->header_day_list);
            $this->header_day_list = array_values($this->header_day_list);
            $this->day_list = array_values($this->day_list);
        }
    }


    public function paramHook()
    {
        parent::paramHook();
        $this->days = floor((time() - $this->start_time) / 86400) + 1;
        $this->setDayList();

        // 注册数、创角数
        if (!is_numeric($this->day_reg_uid_count)) {
            throw new AppException('注册数过滤请输入数字');
        }
        if (!is_numeric($this->day_create_role_id_count)) {
            throw new AppException('创角数过滤请输入数字');
        }

    }

    /**
     * 导出时，处理一下固定列
     *
     * @param $export_type
     * @param $route_id
     *
     * @return string[]
     */
    public function fixedHeader($export_type, $route_id)
    {
        // 日期、注册数等固定列
        if ($this->aggregation_time !== '聚合') {
            if ($export_type != 2) {
                $header = ['game_reg_date'];
            }
        }
        if ($export_type != 2) {
            if ($this->type !== '回本率' || $this->type !== '每日回本率' && $this->type !== '累计付费成本') {
                if ($this->day_cost_money || $route_id == RouteID::MARKET_PAYMENT) {
                    $header[] = 'day_cost_money';
                }

                $header[] = 'reg_cost';
            }
            $header[] = 'day_reg_uid_count';
        } else {
            $header[] = 'open_days';
            $header[] = 'day_create_role_id_count';
        }

        $current_column = $this->getCurrentColumn($this->type);
        if ($current_column) {
            $header[] = $current_column;
        }

        return $header;
    }


    /**
     * 画图的固定列
     *
     * @return array
     */
    public function drawFixedHeader()
    {
        // fixed_target固定要出现所有
        $fixed_target = ['day_cost_money', 'day_reg_uid_count', 'reg_cost'];
        $header = [];
        if ($this->aggregation_time !== '聚合') {
            $header = ['game_reg_date'];
        }
        $header = array_merge($header, $fixed_target);

        $current_column = $this->getCurrentColumn($this->type);
        if ($current_column) {
            $header[] = $current_column;
        }

        return $header;
    }

    public function getCurrentColumn($type)
    {
        switch ($type) {
            case '累计付费':
                $header = 'current_tpay';
                break;
            case 'LTV':
                $header = 'current_ltv';
                break;
            case '回本率':
                $header = 'current_roi';
                break;
            case '累计付费率':
                $header = 'current_trate';
                break;
            case '累计付费ARPU':
                $header = 'current_tarpu';
                break;
            case '累计付费成本':
                $header = 'current_cost';
                break;
        }
        return $header ?? '';
    }

    /**
     * 导出时，列的key值选择
     * 根据选择的type来决定
     */
    public function switchColumnKey()
    {
        switch ($this->type) {
            case '回本率':
                $key_prefix = 'roi_';
                break;
            case '每日回本率':
                $key_prefix = 'proi_';
                break;
            case 'LTV':
                $key_prefix = 'ltv_';
                break;
            case '每日LTV':
                $key_prefix = 'pltv_';
                break;
            case '每日付费':
                $key_prefix = 'ppay_';
                break;
            case '累计付费':
                $key_prefix = 'tpay_';
                break;
            case '每日付费人数':
                $key_prefix = 'pcount_';
                break;
            case '每日付费ARPU':
                $key_prefix = 'parpu_';
                break;
            case '累计付费率':
                $key_prefix = 'trate_';
                break;
            case '每日付费率':
                $key_prefix = 'prate_';
                break;
            case '累计付费ARPU':
                $key_prefix = 'tarpu_';
                break;
            case '累计付费成本':
                $key_prefix = 'cost_';
                break;
            case '累计付费人数':
                $key_prefix = 'tcount_';
                break;
            case '每日ARPU':
                $key_prefix = 'arpu_';
                break;
            case '每日付费次数':
                $key_prefix = 'dpaytimes_';
                break;
            case '累计付费次数':
                $key_prefix = 'tpaytimes_';
                break;
            case '累计付费次数成本':
                $key_prefix = 'tpaytimes_cost_';
                break;
            default:
                throw new AppException('错误的type值');
        }

        return $key_prefix;
    }


}
