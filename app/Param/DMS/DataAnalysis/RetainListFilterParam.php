<?php
/**
 * 投放数据总览列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS\DataAnalysis;


use App\Exception\AppException;


class RetainListFilterParam extends DataAnalysisFilterParam
{

    public $retain_list = [];                       // 处理后的留存数组
    public $export_retain_list = [];                // 导出的指标
    public $reg_uid_count = 1;                      // 注册数筛选，要>=这个数
    public $role_create_count = 1;                  // 角色创角数筛选，要>=这个数
    public $aggregation_retain = '';                // 留存聚合类型 不同类型SQL不一样
    public $pay_type_filter = [];                    // 付费金额区间筛选
    public $pay_type_filter_format = [];             // 付费金额区间筛选
    public $is_rate = 0;                            // 默认查看xx留率 0表示查看xx留数 1=留存率 2=有效留存率 3=留存成本 4=2日有效留存
    public $platform_belong = '';                   // 平台归属
    public $group_belong = '';                      // 集团归属
    public $default_sort = 1;                       // 1默认排序 2指标排序 3维度排序
    public $blocked = false;                        // 是否屏蔽当日
    public $pay_diff_type = [];                     // 注充间隔筛选
    public $retain_type = 1;                        // 1=账号留存 2=设备留存
    public $server_type = '';                       // 区服的留存类型
    public $open_days = [1, 34];                     // open_days 只有区服需要用上 开服时间
    public $combine_times = 0;                      // 合服次数
    public $aggregation_retain_arr = ['新增账号登录留存', '付费账号登录留存', '付费账号付费留存', '非付费账号登录留存', '付费账号留存(注册)', '付费留存/注册', '首日新增新付费用户留存'];
    public $export_type = 1;                         // 导出类型  1=普通留存（也就是注册留存） 2=区服留存
    public $pay_retain = ['付费账号登录留存', '付费账号付费留存', '付费账号留存(注册)', '付费留存/注册', '首日新增新付费用户留存', '付费角色登录留存', '付费角色付费留存']; // 有付费区间的留存类型
    public $public_beta_date_amend = false;         // 公测日期修正 现在只支持 "新增账号登录留存" 的修正


    /**
     * 处理开服区间筛选 "加层级"
     */
    public function payTypeFilterFormat()
    {
//        $this->pay_type_filter = [[1, 100], [101, 200], [201]];
        $open_days_filter_format = [];
        $index = 0;
        $last_level_value = 0;
        $last_index = count($this->pay_type_filter) - 1;
        foreach ($this->pay_type_filter as $index => $item) {
            // 最后一个区间没关闭的情况，比如：[100,无穷大)
            if ($index === $last_index && !isset($item[1])) {
                // 判断付费区间
                if ($last_level_value > $item[0]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-无穷大";
                $open_days_filter_format[$key] = $item;
            } else {
                // 判断付费区间
                if ($last_level_value > $item[1]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-{$item[1]}";
                $open_days_filter_format[$key] = $item;
                // 判断开服区间
                if ($item[1] < $item[0]) {
                    throw new AppException('错误的区间范围');
                }

                $last_level_value = $item[1];
            }

        }
        $index++;
        $key = "{$index}-others";
        $open_days_filter_format[$key] = [];
        $this->pay_type_filter_format = $open_days_filter_format;
    }




    public function regUidCountFormat()
    {
        if (!is_numeric($this->reg_uid_count)) {
            throw new AppException('注册数过滤请输入数字');
        }
        $this->reg_uid_count = max($this->reg_uid_count, 1);
    }

    /**
     * 把各种需要算的留存数提取出来
     * target是类似这种：rate_day_stay_{num} 或 rate_day_stay_{num1}_{num2}
     * 需要把num提出来放retain_list数组
     * 为了计算2日有效留存， 需要把N-1拿出来
     */
    public function targetFormat()
    {
        if ($this->target) {
            foreach ($this->target as $item) {
                $tmp = explode('_', $item);
                $tmp[3] = intval($tmp[3]);
                $this->retain_list[] = $tmp[3];
                $this->export_retain_list[] = $tmp[3];
                if ($tmp[3] > 2) $this->retain_list[] =$tmp[3] - 1;
                if (isset($tmp[4]) && is_numeric($tmp[4])) {
                    for ($i = $tmp[3] + 1; $i <= $tmp[4]; $i++) {
                        $this->retain_list[] = $i;
                        $this->export_retain_list[] = $i;
                        if ($i > 2) $this->retain_list[] = $i - 1;
                    }
                }
            }

            // xx留排序
            asort($this->retain_list);
            asort($this->export_retain_list);
            $this->retain_list = array_values(array_unique($this->retain_list));
            $this->export_retain_list = array_values(array_unique($this->export_retain_list));
        } else {
            throw new AppException('查询的指标不能为空');
        }
    }




    public function paramHook()
    {
        $this->payTypeFilterFormat();
        if (!in_array($this->is_rate, [0, 1, 2, 3, 4])) {
            throw new AppException('留存类型错误');
        }
        parent::paramHook();
    }





    // 判断是否需要消耗
    public function needCostMoney()
    {
        // 用户以下几种维度是不需要消耗的：
        // 模拟器、系统版本、手机型号、手机价格、平台归属、集团归属
        $no_cost_dimension = [
            'is_simulator', 'system_version', 'device_name', 'device_price_section',
            'is_old_clique_game_muid', 'is_old_clique_pay_muid', 'is_old_root_game_muid', 'is_old_root_pay_muid',
            'pay_diff_type'
        ];

        return empty(array_intersect($no_cost_dimension, $this->dimension));
    }
}
