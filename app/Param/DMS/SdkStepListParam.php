<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/8
 * Time: 10:36
 */

namespace App\Param\DMS;


use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class SdkStepListParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度
    public $target = [];                            // 所有指标
    public $start_time = 0;
    public $end_time = 0;
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $limit = 2500;
    public $condition = [];                         // 维度筛选
    public $sdk_action_id;                          // 这里不能单纯保存sdk_action_id，需要加上平台字段[{"platform":"TW","sdk_action_id":10}]
    public $aggregation_time = '按日';
    public $show_week = 0;                          // 是否显示周几
    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);   // 维度处理 各种group by
        if ($this->start_time && $this->end_time) {
            $this->start_time = strtotime($this->start_time);
            $this->end_time = strtotime($this->end_time);
        }
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    /**
     * 设置sdk_action_id 平台级别的组装
     */
    public function setSdkActionId()
    {
        foreach ($this->target as $item) {
            $this->sdk_action_id[$item['platform']][] = $item['sdk_action_id'];
        }
    }
}
