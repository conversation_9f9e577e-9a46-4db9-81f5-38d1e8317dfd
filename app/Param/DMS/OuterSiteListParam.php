<?php
/**
 * 对外后台广告位配置
 * User: Melody
 * Date: 2020/5/20
 * Time: 11:14
 */

namespace App\Param\DMS;


use App\Container;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;

class OuterSiteListParam extends AbstractParam
{
    public $platform;
    public $site_id;
    public $agent_id;
    public $game_id;
    public $settlement_type;        // 结算类型 cps cpa cpt 免费
    public $cps_divide_rate = -1;   // cps分成比例 小数
    public $ad_pop_zk_type = -1;    // 1=扣注册数 2=扣订单数 3=扣订单金额
    public $ad_pop_zk = -1;         // 扣量百分比
    public $ad_price = -1;          // 注册单价
    public $editor_id;              // 编辑者id
    public $editor;                 // 编辑者
    public $bank_holder = '';       // 开户人
    public $tax_rate = -1;          // 税率
    public $channel_fee_rate = -1;  // 渠道费率
    public $is_edit = 0;            // 是否编辑 1=是 0=否
    public $start_date;             // 结算开始日期 默认开始是2010-01-01 结束是2030-01-01
    public $last_ad_pop_zk = 0;     // 上一次扣量比例
    public $agent_permission;       // 渠道权限
    public $page = 1;               // 分页参数
    public $rows = 50;              // 分页参数
    public $pay_type = 0;           // 筛选的时候使用 settlement_type的整型翻译
    public $start_time = 0;         // 筛选的时候使用 时间范围
    public $end_time = 0;           // 筛选的时候使用 时间范围
    public $contract_game_name;     // 合同游戏名
    public $bank_name = '';         // 开户行
    public $bank_card_number = '';  // 银行卡号

    public function paramHook()
    {
        $server = Container::getServer();
        if (!$server) {
            $this->ad_price = intval($this->ad_price);
        } else {
            $this->agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
            $this->ad_price = intval($this->ad_price);
        }

    }
}
