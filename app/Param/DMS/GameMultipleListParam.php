<?php

namespace App\Param\DMS;

use App\Param\AbstractParam;
use App\Service\PermissionService;

class GameMultipleListParam extends AbstractParam
{
    public $platform = '';                           // 平台
    public $start_time = '';                         // 开始时间
    public $end_time = '';                           // 结束时间
    public $config_type = 0;                         // 1:按集团,2:按根
    public $clique_root_id = [];                     // 集团/根游戏ID
    public $game_permission = [];                   // 游戏权限
    public $agent_permission = [];                  // 渠道权限
    public $rows = 50;
    public $page = 1;

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }
}