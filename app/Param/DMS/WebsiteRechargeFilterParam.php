<?php
/**
 * 官网充值列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2021-02-08
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class WebsiteRechargeFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];              // 处理前的维度筛选数组
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度数组
    public $start_time = 0;                         // 开始时间
    public $end_time = 0;                           // 结束时间
    public $aggregation_type = '按日';               // 时间聚合类型
    public $game_permission = '';                   // 权限
    public $agent_permission = '';                  // 权限
    public $limit = 10000;                          // SQL查询的limit

    public $show_week = 0;                          // 是否显示周几
    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
        }
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);   // 维度处理 各种group by
    }



}