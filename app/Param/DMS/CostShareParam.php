<?php


namespace App\Param\DMS;


use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class CostShareParam extends AbstractParam
{

    public $dimension_filter_raw = [];              // 处理前的维度筛选数组

    public $dimension_filter = [];                  // 处理后的维度筛选数组

    public $dimension = [];                         // 维度筛选数组

    public $dimension_group_by = [];                // 处理后的维度筛选数组

    public $aggregation_type = '聚合';               // 时间聚合类型 按日 按月 按周 聚合

    public $start_date;                             // 开始时间

    public $end_date;                               // 结束时间

    public $game_permission = '';                   // 权限

    public $agent_permission = '';                  // 权限

    public $financial_cost = 0;                      // 财务消耗 1是0否

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }


    public function setDimensionFilter($dimension_filter)
    {
        $this->dimension_filter_raw = $dimension_filter; // 处理前的维度
        $this->dimension_filter = DimensionTool::handleDimensionFilter($dimension_filter); // 处理维度筛选
    }


    public function paramHook()
    {
        if ($this->start_date && $this->end_date) {
            $this->start_date = strtotime(date("Ymd", strtotime($this->start_date)));
            $this->end_date = strtotime(date("Ymd", strtotime($this->end_date))) + 86399;
        } else {
            throw new AppException('请选择日期');
        }

        $this->dimension_filter_raw = $this->dimension_filter;
        $this->setDimensionFilter($this->dimension_filter); // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);  // 维度处理 各种group by
    }
}
