<?php
/**
 * 投放数据总览列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Constant\OuterOverViewSqlMap;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Utils\DimensionTool;
use App\Utils\SqlParser;

class OuterOverViewListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度
    public $target = [];                            // 所有指标
    public $compute_target = [];                    // 处理后需要计算的指标
    public $click_query_target = [];                // click表的指标
    public $action_query_target = [];               // action表的指标
    public $reg_query_target = [];                  // reg表的指标
    public $old_query_target = [];                  // old表的指标
    public $pay_query_target = [];                  // pay表的指标
    public $start_time = 0;
    public $end_time = 0;
    public $agent_permission = -1;                   // 渠道权限
    public $aggregation_time = '按日';
    public $limit = 2500;
    public $deduction = 0;                           // 是否扣量 1=是 0=否
    public $condition = [];                          // 维度筛选
    public $statistic_caliber = 1;                   // 1按平台 2按游戏 3按根 4按回流


    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime($this->start_time);
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399;
        }
    }


    public function dimensionFormat()
    {
        if (in_array('date', $this->dimension)) {
            $key = array_search('date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }

        if (DimensionTool::needPushPlatform($this->dimension)) {
            array_unshift($this->dimension, 'platform');
        }
        // 去重
        $this->dimension = array_unique($this->dimension);
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = $this->handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = $this->handleDimension($this->dimension);    // 维度处理 各种group by
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $this->agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
    }

    public function setUserPermission($user_id)
    {
        // 获取权限
        $data = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->agent_permission = $data['agent_permission'];
    }


    /**
     * 处理指标（普通投放总览）
     */
    public function handleTarget()
    {
        $target_list = [];
        foreach ($this->target as $item) {
            if (isset(OuterOverViewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(OuterOverViewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, OuterOverViewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // uid_count.reg_uid_count搞成必有
        $target_list = array_merge($target_list, ['reg_uid_count']);
        // 去重
        $target_list = array_unique($target_list);
        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset(OuterOverViewSqlMap::NEED_CLICK_TABLE[$item]):
                    $this->click_query_target[$item] = OuterOverViewSqlMap::NEED_CLICK_TABLE[$item];
                    break;
                case isset(OuterOverViewSqlMap::NEED_ACTION_TABLE[$item]):
                    $this->action_query_target[$item] = OuterOverViewSqlMap::NEED_ACTION_TABLE[$item];
                    break;
                case isset(OuterOverViewSqlMap::NEED_REG_TABLE[$item]):
                    $this->reg_query_target[$item] = OuterOverViewSqlMap::NEED_REG_TABLE[$item];
                    break;
                case isset(OuterOverViewSqlMap::NEED_OLD_TABLE[$item]):
                    $this->old_query_target[$item] = OuterOverViewSqlMap::NEED_OLD_TABLE[$item];
                    break;
                case isset(OuterOverViewSqlMap::NEED_PAY_TABLE[$item]):
                    if ($this->statistic_caliber == 2) {
                        $this->pay_query_target[$item] = OuterOverViewSqlMap::NEED_PAY_TABLE[$item];
                    } else if ($this->statistic_caliber == 1) {
                        $this->pay_query_target[$item] = OuterOverViewSqlMap::PLAT_GAME_NEED_PAY_TABLE[$item];
                    } else {
                        $this->pay_query_target[$item] = OuterOverViewSqlMap::ROOT_GAME_NEED_PAY_TABLE[$item];
                    }
                    break;
            }
        }
    }

    /**
     * 处理维度筛选，按表归类
     *
     * @param array $dimension_filter
     *
     * @return array
     */
    private function handleDimensionFilter(array $dimension_filter)
    {
        $main = $agent_id = $game_id = [];
        foreach ($dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value']) {
                continue;
            }
            if ($key === 'platform') {
                $main[] = SqlParser::get($dimension);
            } elseif (in_array($key, ['plat_id', 'root_game', 'main_game', 'game', 'os'])) {
                $game_id[] = SqlParser::get($dimension, '', 'game');
            } else {
                $agent_id[] = SqlParser::get($dimension, '', 'agent');
            }
        }

        return [
            'main'     => $main,
            'agent_id' => $agent_id,
            'game_id'  => $game_id,
        ];
    }

    /**
     * 处理维度聚合分组，按表归类
     *
     * @param $dimension
     *
     * @return array
     */
    private function handleDimension(&$dimension)
    {
        $agent_id = $main = $game_id = [];
        foreach ($dimension as $key) {
            if ($key === 'platform') {
                $main[] = $key;
            } elseif (in_array($key, ['plat_id', 'root_game_id', 'main_game_id', 'game_id', 'os'])) {
                $game_id[] = $key;
            } else {
                $agent_id[] = $key;
            }
        }

        return [
            'main'     => $main,
            'agent_id' => $agent_id,
            'game_id'  => $game_id
        ];
    }
}
