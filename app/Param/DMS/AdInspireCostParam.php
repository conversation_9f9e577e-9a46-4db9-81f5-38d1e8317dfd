<?php

namespace App\Param\DMS;

use App\Constant\AdInspireSqlMap;
use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;
use App\Utils\Helpers;

class AdInspireCostParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度
    public $target = [];                            // 所有指标
    public $compute_target = [];                    // 计算指标
    public $query_target =[];                       // 查询指标
    public $start_time = 0;
    public $end_time = 0;
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $aggregation_type = '按日';
    public $show_week = 0;
    public $limit = 2500;

    public function paramHook()
    {
        if ($this->start_time && Helpers::isDatetime($this->start_time) && $this->end_time && Helpers::isDatetime($this->end_time)) {
            $this->start_time = strtotime($this->start_time);
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399;
            $this->date_range = [date("Y-m-d", $this->start_time), date("Y-m-d", $this->end_time)];
            $this->time_range = [date("Y-m-d 00:00:00", $this->start_time), date("Y-m-d 23:59:59", $this->end_time)];
        } else {
            throw new AppException('缺失时间参数');
        }
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);              // 维度处理 各种group by
    }

    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    public function handleTarget()
    {
        $target_list = [];

        // 去重
        $this->target = array_values(array_unique($this->target));

        foreach ($this->target as $item) {
            if (isset(AdInspireSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(AdInspireSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, AdInspireSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        $target_list = array_unique($target_list);
        foreach ($target_list as $item) {
            if (isset(AdInspireSqlMap::QUERY_TARGET[$item]))
            $this->query_target[$item] = AdInspireSqlMap::QUERY_TARGET[$item];
        }
    }
}
