<?php
/**
 * 流失情况列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2020-02-19
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class WastageListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];              // 处理前的维度筛选数组
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度数组
    public $target = [3, 7];                        // 选中的指标，也就是展示天数，默认3，7
    public $stats_start_time = 0;                   // 统计开始时间
    public $stats_end_time = 0;                     // 统计结束时间
    public $recharge_start = 1;                     // 充值区间开始值
    public $recharge_end = 10000000;                // 充值区间结束值
    public $detail_day = 0;                         // 详情的天数
    public $game_permission = '';                   // 权限
    public $agent_permission = '';                  // 权限
    public $is_rate = 1;                            // 1=流失率 0=流失数
    public $export_type = 0;                        // 导出时需要判断是详情还是列表


    public function statsStartTimeFormat()
    {
        $this->stats_start_time = date("Y-m-d", strtotime($this->stats_start_time));
    }

    public function statsEndTimeFormat()
    {
        $this->stats_end_time = date("Y-m-d", strtotime($this->stats_end_time));
    }

    public function rechargeStartFormat()
    {
        if (!$this->recharge_start) $this->recharge_start = 1;
    }

    public function rechargeEndFormat()
    {
        if (!$this->recharge_end) $this->recharge_end = 10000000;
    }

    /**
     * 必有game_id和platform
     */
    public function dimensionFormat()
    {
        if (!in_array('platform', $this->dimension)) {
            if (!empty($this->dimension)) {
                array_unshift($this->dimension, 'platform');
            }
        }
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter); // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);  // 维度处理 各种group by
    }

    /**
     * 把各种需要算的留存数提取出来
     * target是类似这种：rate_day_stay_{num} 或 rate_day_stay_{num1}_{num2}
     */
    public function targetFormat()
    {
        $target = [];
        if ($this->target) {
            foreach ($this->target as $item) {
                if (is_numeric($item)) {
                    $target[] = intval($item);
                    continue;
                }
                $tmp = explode('_', $item);
                $target[] = intval($tmp[3]);
                if (isset($tmp[4]) && is_numeric($tmp[4])) {
                    for ($i = $tmp[3] + 1; $i <= $tmp[4]; $i++) {
                        $target[] = intval($i);
                    }
                }
            }

            // xx留排序
            asort($target);
            $this->target = array_values(array_unique($target));
        } else {
            throw new AppException('查询的指标不能为空');
        }
    }
}
