<?php
/**
 * 订单统计情况列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2020-05-14
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Logic\DMS\PermissionLogic;
use App\Model\SqlModel\Zeda\RankPermissionAllModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Utils\DimensionTool;

class OrderCountListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];              // 处理前的维度筛选数组
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度数组
    public $aggregation_time = '按日';               // 时间聚合类型
    public $start_time = 0;                         // 开始时间
    public $end_time = 0;                           // 结束时间
    public $reg_start_time = 0;                     // 注册起始时间
    public $reg_end_time = 0;                       // 注册结束时间
    public $dimension_type = 2;                     // 按子/按根
    public $game_permission = '';                   // 权限
    public $agent_permission = '';                  // 权限
    public $is_select_all = false;
    public $is_super_manager = false;
    public $limit = 20000;                          // limit


    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
        }
    }

    public function regStartTimeFormat()
    {
        if ($this->reg_start_time) {
            $this->reg_start_time = strtotime(date("Ymd", strtotime($this->reg_start_time)));
        }
    }

    public function regEndTimeFormat()
    {
        if ($this->reg_end_time) {
            $this->reg_end_time = strtotime(date("Ymd", strtotime($this->reg_end_time))) + 86399; // 要到当天23：59：59
        }
    }

    /**
     * 必有game_id和platform
     */
    public function dimensionFormat()
    {
        if (!in_array('platform', $this->dimension)) {
            if (!empty($this->dimension)) {
                array_unshift($this->dimension, 'platform');
            }
        }
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];

        $this->is_super_manager = UserService::isSuperManager();
        if (!$this->is_super_manager) {
            $this->is_select_all = UserService::isPermissionSelectAll();
        }

    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];

        // 判断用户是否是超管
        $user_info = (new UserModel())->getData($user_id);
        $this->is_super_manager = $user_info->super_manager == 1;
        if (!$this->is_super_manager) {
            $rank_permission_model = new RankPermissionAllModel();
            $user_detail_info = (new UserModel())->getDataByUserIdNormal($user_id);

            $column_name = UserService::getColumnNameByLevel($user_detail_info->level);
            $rank_id = $user_detail_info->$column_name;


            $permission_all_list = $rank_permission_model->getAllByRank($user_detail_info->level, $rank_id);
            $this->is_select_all = UserService::isPermissionSelectAll($permission_all_list);
        }
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter); // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);  // 维度处理 各种group by
    }
}