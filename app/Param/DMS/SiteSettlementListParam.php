<?php
/**
 * Created by PhpStorm.
 * User: Melody
 * Date: 2020/6/22
 * Time: 11:30
 */

namespace App\Param\DMS;


use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Utils\SqlParser;

class SiteSettlementListParam extends AbstractParam
{
    public $page = 1;
    public $rows = 50;
    public $agent_permission;
    public $start_date;
    public $end_date;
    public $settlement_type = '';
    public $type = 1;                     // 1=未结算 2=已结算
    public $dimension_filter = [];
    public $limit = 100000;
    public $live_order_ids = ''; // 关联的直播订单
    public $bank_holder = ''; // 开户人
    public $our_company = ''; // 结算主体


    public function setLoginUserPermission()
    {
        $this->agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
    }

    /**
     * 设置某个用户的agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->agent_permission = $user_permission['agent_permission'];
    }


    public function dimensionFilterFormat()
    {
        $dimension_filter = [];
        foreach ($this->dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value']) {
                continue;
            }
            if ($key == 'agent_group_name') {
                $dimension_filter[] = SqlParser::get($dimension, '', 'agent');
            } elseif ($key == 'agent_leader_group_id') {
                $dimension_filter[] = SqlParser::get($dimension, '', 'alg');
            } elseif (in_array($key, ['root_game', 'main_game', 'clique_id'])) {
                $dimension_filter[] = SqlParser::get($dimension, '', 'game');
            } else {
                $dimension_filter[] = SqlParser::get($dimension, '', 'site');
            }
        }

        $this->dimension_filter = $dimension_filter;
    }

}
