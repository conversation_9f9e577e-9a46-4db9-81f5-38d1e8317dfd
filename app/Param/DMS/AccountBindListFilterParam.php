<?php
/**
 * 账号绑定情况列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2020-05-13
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;
use App\Utils\Helpers;

class AccountBindListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];              // 处理前的维度筛选数组
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度数组
    public $aggregation_time = '按日';               // 时间聚合类型
    public $show_week = 0;                          // 是否显示周几
    public $start_time = 0;                         // 开始时间
    public $end_time = 0;                           // 结束时间
    public $game_permission = '';                   // 权限
    public $agent_permission = '';                  // 权限
    public $limit = 5000;                           // limit
    public $money_quantum = [];                     // 付费金额分段
    public $sort_money_quantum = [];                // 金额分段排序
    public $time_range_type = '';                   // 时间范围类型，当日，当月，当周，最近X天 day, month, week, current
    public $current_days = 0;                       // time_range_type 为current是传
    public $export_type = '';                       // 导出任务类型区分 reg注册用户 active活跃用户


    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
        }
    }

    /**
     * 必有game_id和platform
     */
    public function dimensionFormat()
    {
        if (!in_array('platform', $this->dimension)) {
            if (!empty($this->dimension)) {
                array_unshift($this->dimension, 'platform');
            }
        }
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter); // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);  // 维度处理 各种group by
    }

    /**
     * 格式化金额范围
     * @return string
     */
    public function formatMoneyQuantum()
    {
        if (empty($this->money_quantum)) {
            return '';
        }
        $field = '(CASE ';
        $sort_money_quantum = [];
        foreach ($this->money_quantum as $item) {
            if (strpos($item, '-')) {
                $foo = explode('-', $item);
                $first = $foo[0];
            }else {
                $first = rtrim($item, '+');
            }
            $sort_money_quantum[$first] = $item;
        }
        ksort($sort_money_quantum);
        $sort_money_quantum = array_values($sort_money_quantum);
        $this->sort_money_quantum = $sort_money_quantum;

        foreach ($sort_money_quantum as $key => $item) {
            if (strpos($item, '-')) {
                $foo = explode('-', $item);
                $field .= "WHEN total_pay_money BETWEEN {$foo[0]} and {$foo[1]} then '{$key}' ";
            }else {
                if (strpos($item, '+') > 0) {
                    $bar = rtrim($item, '+');
                    $field .= "WHEN total_pay_money >= {$bar} then '{$key}'";
                }
            }
        }
        $field .= " ELSE '其他' END) AS money_type";
        return $field;
    }

    public function formatTimeRange($alias = '')
    {
        switch ($this->time_range_type) {
            case 'week':
                $start = date('Y-m-d H:i:s', mktime(0,0,0,date('m'),date('d')-date('w'),date('Y')));
                $end = date('Y-m-d') . ' 23:59:59';
                break;
            case 'month':
                $start = date('Y-m-d H:i:s', mktime(0,0,0,date('m'),1,date('Y')));
                $end = date('Y-m-d') . ' 23:59:59';
                break;
            case 'current':
                $start = date('Y-m-d H:i:s', time() - $this->current_days * 86400);
                $end = date('Y-m-d') . ' 23:59:59';
                break;
            default:
                $start = date('Y-m-d');
                $end = $start . ' 23:59:59';
                break;
        }
        if ($alias == '') {
            $between = "last_login_time BETWEEN '{$start}' AND '{$end}'";
        }else {
            $between = "{$alias}.last_login_time BETWEEN '{$start}' AND '{$end}'";
        }
        return $between;
    }
}
