<?php
/**
 * 流水预估列表筛选条件
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2020-08-12
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Utils\DimensionTool;
use App\Utils\Helpers;
use App\Utils\SqlParser;

class FlowEstimateParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_zeda = [];             // 针对zeda库的维度筛选
    public $dimension = [];                         // 处理前的维度
    public $target_date_list = [];                  // 处理后的指标（天或者月）数组
    public $date_list = [];                         // 需要预估的展开后的每一天的数组
    public $before_date_list = [];                  // 不需要预估的展开后的每一天的数组
    public $start_date = 0;                         // 预估开始时间
    public $end_date = 0;                           // 预估结束时间
    public $before_today_start = 0;                 // 预估时间在今天之前的开始时间
    public $before_today_end = 0;                   // 预估时间在今天之前的结束时间
    public $is_estimate = true;                     // 是否需要预估
    public $cost_start_date = 0;                    // 消耗和开始时间
    public $cost_end_date = 0;                      // 消耗结束时间
    public $aggregation_time = '按日';               // 时间聚合类型 只有按日和按月
    public $game_permission;                        // 游戏权限
    public $flow_type = 1;                          // 1=总流水 2=单日流水
    public $estimate_trend = 1;                     // 预估趋势， 1=实际趋势，2=回本达标 3=倍数
    public $dimension_type = 1;                     // SQL维度类型。1=按子 2=按根 3=回流
    public $fixed_dimension_group_by = [];          // 查询SQL时的固定维度group by
    public $fixed_dimension = ['platform', 'plat_id', 'clique_id', 'contract_game_name', 'root_game_id', 'main_game_id'];   // 查询SQL时的固定维度

    /*--- 分割出来的消耗区间 --*/
    public $day_ago_67_start = 0;
    public $day_ago_67_end = 0;
    public $day_later_67_start = 0;
    public $day_later_67_end = 0;
    public $day_ago_7_start = 0;
    public $day_ago_7_end = 0;
    public $day_future_start = 0;
    public $day_future_end = 0;


    /**
     * 指标日期，按前端选中的日期
     *
     * @throws \Exception
     */
    public function setDayList()
    {
        if (!(Helpers::isDatetime($this->start_date) && Helpers::isDatetime($this->end_date)
            && Helpers::isDatetime($this->cost_start_date) && Helpers::isDatetime($this->cost_end_date))) {
            throw new AppException('时间格式错误');
        }
        $this->start_date = strtotime(date("Ymd", strtotime($this->start_date)));
        $this->end_date = strtotime(date("Ymd", strtotime($this->end_date)));

        $start_date = $this->start_date; // 真正去预估的开始时间
        $end_date = $this->end_date;     // 真正去预估的结束时间

        $today = strtotime(date("Ymd"));
        // 1. 预估开始时间小于今天但是结束时间大于等于今天
        if ($this->start_date < $today && $this->end_date >= $today) {
            $start_date = $today; // 直接等于今天
            $end_date = $this->end_date;
            $this->before_today_start = $this->start_date;
            $this->before_today_end = $today - 86400; // 今天的前一天
        }

        // 2. 预估开始时间小于今天而且结束时间小于今天 也就是不用预估了
        if ($this->start_date < $today && $this->end_date < $today) {
            $this->before_today_start = $this->start_date;
            $this->before_today_end = $this->end_date;
            $this->is_estimate = false;
        }

        // 需要预估的展开的天列表
        if ($this->is_estimate) {
            for ($day = $start_date; $day <= $end_date; $day += 86400) {
                $this->date_list[] = date("Y-m-d", $day);
            }
        }
        // 不需要预估的展开的天列表
        if ($this->before_today_start) {
            for ($day = $this->before_today_start; $day <= $this->before_today_end; $day += 86400) {
                $this->before_date_list[] = date("Y-m-d", $day);
            }
        }

        // 列的天列表
        if ($this->aggregation_time === '按日') {
            for ($day = $this->start_date; $day <= $this->end_date; $day += 86400) {
                $this->target_date_list[] = date("Y-m-d", $day);
            }
        } else {
            $target_list = [];
            $end_date = strtotime('last day of this month', $this->end_date);
            for ($day = $this->start_date; $day <= $end_date; $day = strtotime(Helpers::getSameDayOfNextMonth(date("Y-m-d", $day)))) {
                $target_list[] = date("Y-m", $day);
            }
            $this->target_date_list = $target_list;
        }

    }

    /**
     * 判断各种情况，分割出时间区间
     * 总共有4个区间,分多种情况分割
     */
    public function setCostDate()
    {
        $this->cost_start_date = strtotime(date("Ymd", strtotime($this->cost_start_date)));
        $this->cost_end_date = strtotime(date("Ymd", strtotime($this->cost_end_date)));

        $day_ago_67 = strtotime(date('Y-m-d', strtotime('-67 day'))); // 67天前
        $day_ago_7 = strtotime(date('Y-m-d', strtotime('-7 day')));  // 7天前
        $to_day = strtotime(date("Y-m-d"));

        //  1. 消耗开始时间和结束时间都小于67天前
        if ($this->cost_start_date < $day_ago_67 && $this->cost_end_date < $day_ago_67) {
            $this->day_ago_67_start = $this->cost_start_date;
            $this->day_ago_67_end = $this->cost_end_date;
            return;
        }

        // 2. 消耗开始时间小于67，但是结束时间大于等于67,并且结束时间小于等于7天前
        if ($this->cost_start_date < $day_ago_67 && $this->cost_end_date >= $day_ago_67 && $this->cost_end_date <= $day_ago_7) {
            $this->day_ago_67_start = $this->cost_start_date;
            $this->day_ago_67_end = $day_ago_67 - 86400; // 67天前的前一天
            $this->day_later_67_start = $day_ago_67; // 67天前
            $this->day_later_67_end = $this->cost_end_date;
            return;
        }

        // 3.消耗开始时间小于67，结束时间大于7天前，但是小于今天
        if ($this->cost_start_date < $day_ago_67 && $this->cost_end_date > $day_ago_7 && $this->cost_end_date < $to_day) {
            $this->day_ago_67_start = $this->cost_start_date;
            $this->day_ago_67_end = $day_ago_67 - 86400; // 67天前的前一天
            $this->day_later_67_start = $day_ago_67;// 67天前
            $this->day_later_67_end = $day_ago_7; // 7天前
            $this->day_ago_7_start = $day_ago_7 + 86400; // 7天前的后一天
            $this->day_ago_7_end = $this->cost_end_date;
            return;
        }

        // 4.消耗开始时间小于67，但是结束时间大于等于今天
        if ($this->cost_start_date < $day_ago_67 && $this->cost_end_date >= $to_day) {
            $this->day_ago_67_start = $this->cost_start_date;
            $this->day_ago_67_end = $day_ago_67 - 86400; // 67天前的前一天
            $this->day_later_67_start = $day_ago_67;// 67天前
            $this->day_later_67_end = $day_ago_7;// 7天前
            $this->day_ago_7_start = $day_ago_7 + 86400; // 7天前的后一天
            $this->day_ago_7_end = $to_day - 86400; // 昨天
            $this->day_future_start = $to_day;
            $this->day_future_end = $this->cost_end_date;
            return;
        }

        // 5.消耗时间开始大于等于67天前且结束时间小于等于7天前
        if ($this->cost_start_date >= $day_ago_67 && $this->cost_start_date <= $day_ago_7 && $this->cost_end_date <= $day_ago_7) {
            $this->day_later_67_start = $this->cost_start_date;
            $this->day_later_67_end = $this->cost_end_date;
            return;
        }

        // 6.消耗时间开始大于等于67天前且小于等于7天前且结束时间大于7天前且小于今天
        if ($this->cost_start_date >= $day_ago_67 && $this->cost_start_date <= $day_ago_7 && $this->cost_end_date > $day_ago_7 && $this->cost_end_date < $to_day) {
            $this->day_later_67_start = $this->cost_start_date;
            $this->day_later_67_end = $day_ago_7; // 7天前
            $this->day_ago_7_start = $day_ago_7 + 86400; // 7天前的后一天
            $this->day_ago_7_end = $this->cost_end_date;
            return;
        }

        // 7.消耗时间开始大于等于67天前且小于等于7天前且结束时间大于等于今天
        if ($this->cost_start_date >= $day_ago_67 && $this->cost_start_date <= $day_ago_7 && $this->cost_end_date >= $to_day) {
            $this->day_later_67_start = $this->cost_start_date;
            $this->day_later_67_end = $day_ago_7;// 7天前
            $this->day_ago_7_start = $day_ago_7 + 86400; // 7天前的后一天
            $this->day_ago_7_end = $to_day - 86400; // 昨天
            $this->day_future_start = $to_day;
            $this->day_future_end = $this->cost_end_date;
            return;
        }

        // 8.消耗开始时间大于7天前且小于今天 并且结束时间也小于今天
        if ($this->cost_start_date > $day_ago_7 && $this->cost_start_date < $to_day && $this->cost_end_date < $to_day) {
            $this->day_ago_7_start = $this->cost_start_date;
            $this->day_ago_7_end = $this->cost_end_date;
            return;
        }

        // 9.消耗开始时间大于7天前且小于今天 并且结束时间大于等于今天
        if ($this->cost_start_date > $day_ago_7 && $this->cost_start_date < $to_day && $this->cost_end_date >= $to_day) {
            $this->day_ago_7_start = $this->cost_start_date;
            $this->day_ago_7_end = $to_day - 86400; // 昨天
            $this->day_future_start = $to_day;
            $this->day_future_end = $this->cost_end_date;
            return;
        }

        // 10.消耗开始时间大于等于今天
        if ($this->cost_start_date >= $to_day && $this->cost_end_date >= $to_day) {
            $this->day_future_start = $this->cost_start_date;
            $this->day_future_end = $this->cost_end_date;
            return;
        }

        // 其他情况讲道理是不可能存在的
        throw new AppException('消耗时间区间异常');

    }


    public function dimensionFormat()
    {
        if (DimensionTool::needPushPlatform($this->dimension)) {
            array_unshift($this->dimension, 'platform');
        }
        // 去重
        $this->dimension = array_unique($this->dimension);
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $this->game_permission = (new PermissionLogic())->getLoginUserGamePermission();
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->game_permission = $user_permission['game_permission'];
    }

    public function paramHook()
    {

        foreach ($this->dimension_filter as $item) {
            // 过滤空value
            if (!$item['value']) {
                continue;
            }
            $this->dimension_filter_zeda[] = SqlParser::get($item);
        }
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->fixed_dimension_group_by = DimensionTool::handleDimension($this->fixed_dimension); // 维度处理 各种group by

    }
}