<?php
namespace App\Param\DMS;

use App\Constant\ProfitStatementSqlMap;
use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Model\SqlModel\Zeda\CustomizedTargetModel;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;
use App\Utils\SqlParser;

class ProfitStatementListParam extends AbstractParam
{
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $start_time = '';                         // 开始时间
    public $end_time = '';                           // 结束时间
    public $run_date_start_time = '';                // 开始时间
    public $run_date_end_time = '';                  // 结束时间
    public $aggregation_type = '按月';               // 聚合类型
    public $game_permission = [];                   // 游戏权限
    public $agent_permission = [];                  // 渠道权限
    public $target = [];                            // 所有指标
    public $target_select = [];                     // 处理过后的所有指标
    public $target_raw = [];                        // 所有指标
    public $profit_builder_target = [];             // 子查询所需指标
    public $profit_aggregation_builder_target = []; // 聚合查询时所需指标
    public $compute_target = [];                    // 处理后需要计算的指标
    public $compute_children_target = [];           // 计算的指标的子指标
    public $is_all_cost = 0;                        // 计入全成本 0不计入 1计入
    public $official_apportion = 0;                 // 自然量（官网）分摊
    public $has_cumulative_target = 0;              // 是否含有累计指标
    public $customized_target = [];                 // 处理后的自定义指标
    public $customized_target_raw = [];             // 处理前的自定义指标
    public $customized_target_column_collect = [];  // 处理后的具体字段的自定义指标
    public $limit = 1000000;

    public function paramHook()
    {
        $this->formatTargetData();
        $this->formatFilterData();
        $this->formatDimensionData();
    }

    public function dimensionFormat()
    {
        if (!in_array('platform', $this->dimension)) {
            throw new AppException("平台维度必选");
        }
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        // 获取权限
        $permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * @return void
     */
    private function formatTargetData()
    {
        $this->target_raw = $this->target;
        $target = [];
        foreach ($this->target as $item) {
            // 计算指标
            if (isset(ProfitStatementSqlMap::COMPUTE_TARGET[$item])) {
                if (count(ProfitStatementSqlMap::COMPUTE_TARGET[$item]) > 1) {
                    $this->compute_target[$item] = ProfitStatementSqlMap::COMPUTE_TARGET[$item];
                    $this->compute_children_target = array_unique(array_merge($this->compute_children_target, ProfitStatementSqlMap::COMPUTE_TARGET[$item]));
                }
                $target = array_merge($target, ProfitStatementSqlMap::COMPUTE_TARGET[$item]);
            }
        }

        // 把自定义指标里面需要算的指标拿出来
        $customized_target = [];
        if ($this->customized_target) {
            $this->customized_target_raw = $this->customized_target;
            $customized_target_list = (new CustomizedTargetModel())->getTargetById($this->customized_target);
            foreach ($customized_target_list as &$target_item) {
                $formula = json_decode($target_item->formula, true);
                if (isset($formula['column']) && is_array($formula['column'])) {
                    $customized_target = array_merge($customized_target, $formula['column']);
                }
                $target_item->formula = $formula;
            }
            $this->customized_target = $customized_target_list;  // 存起来，计算的时候使用
            $this->customized_target_column_collect = $customized_target;  // 存起来，计算合计的时候使用
        }

        $this->target = array_unique(array_merge($target, $this->target, $this->dimension, ['total_pay_money'], $customized_target));//有聚合的维度，自定义指标里必须有此字段，故合并处理; total_pay_money是用来排序的

        foreach ($this->target as $item) {
            // 整体情况指标
            if (isset(ProfitStatementSqlMap::OVERALL_SITUATION_TARGET[$item])) {
                $this->target_select[] = ProfitStatementSqlMap::OVERALL_SITUATION_TARGET[$item];
            }

            // 预估情况指标
            if (isset(ProfitStatementSqlMap::ESTIMATED_SITUATION_TARGET[$item])) {
                $this->target_select[] = ProfitStatementSqlMap::ESTIMATED_SITUATION_TARGET[$item];
            }

            // 累计指标
            if (isset(ProfitStatementSqlMap::CUMULATIVE_TARGET[$item])) {
                $this->target_select[] = ProfitStatementSqlMap::CUMULATIVE_TARGET[$item];
                $this->has_cumulative_target = 1;
            }

            // TODO V2版本上线后删除
//            // 子查询所需指标
//            if (isset(ProfitStatementSqlMap::All_TARGET[$item])) {
//                $this->profit_builder_target[] = ProfitStatementSqlMap::All_TARGET[$item];
//                if ($this->aggregation_type == '聚合' && isset(ProfitStatementSqlMap::AGGREGATION_TARGET[$item])) {
//                    $this->profit_aggregation_builder_target[] = ProfitStatementSqlMap::AGGREGATION_TARGET[$item];
//                } else {
//                    $this->profit_aggregation_builder_target[] = ProfitStatementSqlMap::All_TARGET[$item];
//                }
//            }

        }

        // 分摊标识符&&计入全本标识符转换
        foreach ($this->target_select as &$target_sql_expression) {
            // 分摊标识符
            if ($this->official_apportion) {
                $target_sql_expression = str_replace("{apportion_str}", ProfitStatementSqlMap::APPORTION_STR, $target_sql_expression);
            } else {
                $target_sql_expression = str_replace("{apportion_str}", '', $target_sql_expression);
            }

            // 计入全本标识符
            if ($this->is_all_cost === 1) {
                $target_sql_expression = str_replace("{is_all_cost}_", 'all_', $target_sql_expression);
            } else {
                $target_sql_expression = str_replace("{is_all_cost}_", '', $target_sql_expression);
            }
        }

        // TODO V2版本上线后删除
//        // is_all_cost 计入全成本时，指标转换
//        // @doc https://lx3qcyzne8.feishu.cn/wiki/USgEwWUMBiDiKakZxrHcEvGrnSZ
//        foreach ($this->profit_builder_target as &$sql_expression) {
//            if ($this->is_all_cost == 1) {
//                $sql_expression = str_replace("{is_all_cost}_", 'all_', $sql_expression);
//            } else {
//                $sql_expression = str_replace("{is_all_cost}_", '', $sql_expression);
//            }
//        }
//
//        foreach ($this->profit_aggregation_builder_target as &$sql_expression) {
//            if ($this->is_all_cost == 1) {
//                $sql_expression = str_replace("{is_all_cost}_", 'all_', $sql_expression);
//            } else {
//                $sql_expression = str_replace("{is_all_cost}_", '', $sql_expression);
//            }
//        }

    }

    private function formatFilterData()
    {
        // todo
    }

    private function formatDimensionData()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter); // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension); // 维度处理 各种group by
    }
}