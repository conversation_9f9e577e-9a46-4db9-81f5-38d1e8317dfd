<?php


namespace App\Param\DMS;


use App\Param\AbstractParam;
use App\Service\PermissionService;

class AgentRebateParam extends AbstractParam
{
    public $platform = '';
    public $agent_id = [];
    public $agent_group_id = [];
    public $agent_group_name = '';
    public $type = 1;               // 1=渠道返点录入 2=渠道组返点录入
    public $start_date = 0;
    public $end_date = 0;
    public $id = '';
    public $rebate = 0;
    public $true_rebate = 0;
    public $creator = '';
    public $operator = '';
    public $remark = '';
    public $is_all = 0;            // 1全选 2非全选
    public $agent_keyword = '';    // 搜索关键字
    public $agent_permission;
    public $account_id = [];
    public $account_list = [];
    public $media_type_id;
    public $media_type_name = [];
    public $rebate_type = 0; // 返点类型，真实返点渠道、真实返点渠道组使用，0-市场返点；1-财务返点


    public function paramHook()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->agent_permission = $permission['agent_permission'];
    }
}
