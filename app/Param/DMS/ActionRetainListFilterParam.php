<?php
/**
 * 激活留存参数筛选
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2021-01-07
 * Time: 11:24
 */

namespace App\Param\DMS;


use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;
use App\Utils\Helpers;

class ActionRetainListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 维度筛选数组
    public $dimension_filter_raw;                   // 维度筛选数组原始值
    public $dimension = [];                         // 维度
    public $target = [];                            // 指标 也就是各种留存
    public $retain_list = [];                       // 处理后的留存数组
    public $export_retain_list = [];                // 导出的指标
    public $start_time = 0;                         // 开始时间
    public $end_time = 0;                           // 结束时间
    public $action_count = 1;                       // 激活数筛选，要>=这个数
    public $aggregation_time = '聚合';               // 时间聚合类型
    public $dimension_type = 1;                     // SQL维度类型。1=按子 2=按根
    public $aggregation_retain = '';                // 留存聚合类型 不同类型SQL不一样
    public $pay_start = -1;                         // 付费金额区间
    public $pay_end = -1;                           // 付费金额区间
    public $pay_type_filter = [];                    // 付费金额区间筛选
    public $pay_type_filter_format = [];             // 付费金额区间筛选
    public $is_rate = 0;                            // 默认查看xx留率 0表示查看xx留数 1=留存率 2=有效留存率 3=留存成本 4=2日有效留存
    public $dimension_group_by = [];                // 处理后的维度数组
    public $platform_belong = '';                   // 平台归属
    public $group_belong = '';                      // 集团归属
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $default_sort = 1;                       // 1默认排序 2指标排序 3维度排序
    public $blocked = false;                        // 是否屏蔽当日
    public $limit = 10000000;                       // SQL查询的limit
    public $aggregation_retain_arr = ['激活设备登录留存', '激活设备付费留存'];
    public $pay_retain = ['激活设备付费留存'];
    public $show_week = 0;                          // 是否显示周几
    public function startTimeFormat()
    {
        if ($this->start_time && Helpers::isDatetime($this->start_time)) {
            $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        } else {
            throw new AppException('缺失时间参数');
        }
    }


    /**
     * 处理开服区间筛选 "加层级"
     */
    public function payTypeFilterFormat()
    {
//        $this->pay_type_filter = [[1, 100], [101, 200], [201]];
        $open_days_filter_format = [];
        $index = 0;
        $last_level_value = 0;
        $last_index = count($this->pay_type_filter) - 1;
        foreach ($this->pay_type_filter as $index => $item) {
            // 最后一个区间没关闭的情况，比如：[100,无穷大)
            if ($index === $last_index && !isset($item[1])) {
                // 判断付费区间
                if ($last_level_value > $item[0]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-无穷大";
                $open_days_filter_format[$key] = $item;
            } else {
                // 判断付费区间
                if ($last_level_value > $item[1]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-{$item[1]}";
                $open_days_filter_format[$key] = $item;
                // 判断开服区间
                if ($item[1] < $item[0]) {
                    throw new AppException('错误的区间范围');
                }

                $last_level_value = $item[1];
            }

        }
        $index++;
        $key = "{$index}-others";
        $open_days_filter_format[$key] = [];
        $this->pay_type_filter_format = $open_days_filter_format;
    }


    public function endTimeFormat()
    {
        if ($this->end_time && Helpers::isDatetime($this->end_time)) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
        } else {
            throw new AppException('缺失时间参数');
        }
    }

    public function actionCountFormat()
    {
        if (!is_numeric($this->action_count)) {
            throw new AppException('激活数过滤请输入数字');
        }
        $this->action_count = $this->action_count > 1 ? $this->action_count : 1;
    }

    /**
     * 把各种需要算的留存数提取出来
     * target是类似这种：rate_day_stay_{num} 或 rate_day_stay_{num1}_{num2}
     * 需要把num提出来放retain_list数组
     * 为了计算2日有效留存， 需要把N-1拿出来
     */
    public function targetFormat()
    {
        // 默认添加1日
        array_unshift($this->target, 'rate_day_stay_1');
        if ($this->target) {
            foreach ($this->target as $item) {
                $tmp = explode('_', $item);
                $this->retain_list[] = intval($tmp[3]);
                $this->export_retain_list[] = intval($tmp[3]);
                if (intval($tmp[3]) > 2) $this->retain_list[] = intval($tmp[3]) - 1;
                if (isset($tmp[4]) && is_numeric($tmp[4])) {
                    for ($i = $tmp[3] + 1; $i <= $tmp[4]; $i++) {
                        $this->retain_list[] = intval($i);
                        $this->export_retain_list[] = intval($i);
                        if (intval($i) > 2) $this->retain_list[] = intval($i) - 1;
                    }
                }
            }

            // xx留排序
            asort($this->retain_list);
            asort($this->export_retain_list);
            $this->retain_list = array_values(array_unique($this->retain_list));
            $this->export_retain_list = array_values(array_unique($this->export_retain_list));
        } else {
            throw new AppException('查询的指标不能为空');
        }
    }


    public function dimensionFormat()
    {
        // 前端的一些特殊原因，会把game_reg_date这个"不是维度的维度"传过来，所以这里要去掉
        if (in_array('game_reg_date', $this->dimension)) {
            $key = array_search('game_reg_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (in_array('action_date', $this->dimension)) {
            $key = array_search('action_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (DimensionTool::needPushPlatform($this->dimension)) {
            array_unshift($this->dimension, 'platform');
        }

        // 去重
        $this->dimension = array_unique($this->dimension);
    }

    public function paramHook()
    {
        $this->payTypeFilterFormat();
        if (!in_array($this->is_rate, [0, 1, 2, 3, 4])) {
            throw new AppException('留存类型错误');
        }

        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleDimension($this->dimension);              // 维度处理 各种group by
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    // 判断是否需要消耗
    public function needCostMoney()
    {
        // 用户以下几种维度是不需要消耗的：
        // 模拟器、系统版本、手机型号、手机价格、平台归属、集团归属
        $no_cost_dimension = [
            'is_simulator', 'system_version', 'device_name', 'device_price_section',
            'is_old_clique_game_muid', 'is_old_clique_pay_muid', 'is_old_root_game_muid', 'is_old_root_pay_muid',
            'pay_diff_type'
        ];

        return empty(array_intersect($no_cost_dimension, $this->dimension));
    }
}
