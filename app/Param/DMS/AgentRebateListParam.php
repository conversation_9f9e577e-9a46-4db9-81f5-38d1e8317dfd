<?php


namespace App\Param\DMS;


use App\Logic\DMS\PermissionLogic;
use App\Model\SqlModel\Zeda\RankPermissionAllModel;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Utils\DimensionTool;
use App\Utils\Helpers;
use App\Utils\SqlParser;
use Common\EnvConfig;

class AgentRebateListParam extends AbstractParam
{

    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];                  // 处理后的维度筛选数组
    public $start_date = 0;                         // 开始时间
    public $end_date = 0;                           // 结束时间
    public $type = 1;                                // 1渠道 2渠道组
    public $agent_permission;


    public function paramHook()
    {
        $dimension_filter = [];
        $this->dimension_filter_raw = $this->dimension_filter;
        foreach ($this->dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value']) {
                continue;
            }
            if ($key === 'platform') {
                $dimension_filter[] = SqlParser::get($dimension, '', 't');
            } else {
                $dimension_filter[] = SqlParser::get($dimension, '', 'agent');
            }
        }

        $this->dimension_filter = $dimension_filter;
    }


    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->agent_permission = $permission['agent_permission'];
    }
}
