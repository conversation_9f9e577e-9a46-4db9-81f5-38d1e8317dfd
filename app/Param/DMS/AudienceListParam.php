<?php
/**
 * 日志参数
 * User: hejs
 * Date: 2019-10-24
 * Time: 15:08
 */

namespace App\Param\DMS;


use App\Param\AbstractParam;

class AudienceListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $company;
    public $id;
    public $audience_id;
    public $audience_name;
    public $media_type;
    public $device_id;
    public $start_create_date;
    public $end_create_date;
    public $start_update_date;
    public $end_update_date;
    public $state;
    public $creator;

    public function paramHook() {
        if ($this->start_create_date) {
            $this->start_create_date = strtotime($this->start_create_date);
        }
        if ($this->end_create_date) {
            $this->end_create_date = strtotime($this->end_create_date);
        }
        if ($this->start_update_date) {
            $this->start_update_date = strtotime($this->start_update_date);
        }
        if ($this->end_update_date) {
            $this->end_update_date = strtotime($this->end_update_date);
        }
    }
}
