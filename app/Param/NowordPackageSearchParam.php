<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/08/12
 * Time: 11:09
 */

namespace App\Param;


class NowordPackageSearchParam extends AbstractParam
{
    public $page;
    public $rows;
    public $media_type;
    public $noword_type;
    public $id;
    public $create_time;
    public $creator;
    public $name;

    public function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }
        if ($this->create_time) {
            $this->create_time = array_map(function ($time) {
                return (int)$time / 1000;
            }, $this->create_time);
        }
    }
}
