<?php
/**
 * 头条创建转化
 * User: zhangzhen
 * Date: 2020-02-21
 */

namespace App\Param;


use App\Constant\ActionTrackType;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertType;
use App\Constant\DeepExternalAction;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Constant\PlatId;
use App\Exception\AppException;

/**
 * Class ConvertCreateParam
 * @package App\Param
 */
class ConvertCreateParam extends AbstractParam
{
    const GAME_TYPE = [
        MediaType::TOUTIAO => [
            'IOS' => 'APP_IOS',
            '安卓' => 'APP_ANDROID'
        ],
        MediaType::TENCENT => [
            'IOS' => 'CONVERSION_SCENE_IOS',
            '安卓' => 'CONVERSION_SCENE_ANDROID'
        ],
        MediaType::KUAISHOU => [
            'IOS' => 3,
            '安卓' => 1
        ],
        MediaType::BAIDU => [
            'IOS' => 1,
            '安卓' => 2
        ],
        MediaType::UC => [
            'IOS' => '001',
            '安卓' => '010'
        ],
    ];

    const CONVERSION_TEMPLATE_REPORT = [
        '592' => [
            0, 1, 2, 3, 4, 5
        ],
        '606' => [
            0, 1, 2, 3, 4
        ],
        '593' => [
            0, 1
        ],
        '605' => [
            2, 3, 4, 5, 6
        ],
        '604' => [
            0, 1, 2, 3, 4, 5, 6, 7, 8
        ]
    ];

    /**
     * @var MediaAccountInfoParam $media_account media_account的一条记录
     */
    public $media_account;


    public $agent_id;
    public $site_id;
    public $game_id;

    /**
     * @var string $name 转化名称-必填 当没有传入名字时使用规则site_id-app_name-convert_type-deep_external_action
     */
    public $name;

    /**
     * @var string $app_name app name-必填
     */
    public $app_name;

    /**
     * @var string $image_token image_token-必填
     */
    public $image_token;

    /**
     * @var string $convert_source_type 转化来源-必填
     */
    public $convert_source_type;

    /**
     * @var string $convert_type 转化类型-必填
     */
    public $convert_type;

    /**
     * @var string $convert_data_type 转化统计方式 ONLY_ONE | EVERY_ONE
     */
    public $convert_data_type;

    /**
     * @var string $download_url 下载链接-必填
     */
    public $download_url;

    /**
     * @var string $app_id APP ID 必填
     */
    public $app_id;

    /**
     * @var string $app_secret 百度sdk应用密钥
     */
    public $app_secret;

    /**
     * @var string $game_type 系统 enum [IOS, 安卓]
     */
    public $game_type;

    /**
     * @var string $action_track_url 点击监测链接-必填
     */
    public $action_track_url;

    /**
     * @var string $display_track_url 展示监测链接-必填
     */
    public $display_track_url;

    /**
     * @var string $package_name 包名-必填
     */
    public $package_name;

    /**
     * @var string $deep_external_action 深度转化目标
     */
    public $deep_external_action;

    /**
     * @var string
     */
    public $user_action_set_id;

    /**
     * @var string
     */
    public $app_android_channel_package_id;

    /**
     * @var string $plat_id 游戏端类型ID
     */
    public $plat_id;

    /**
     * @var string $external_url 落地页链接(转化来源为落地页H5时使用)
     */
    public $external_url;
    public $action_track_type;
    public $conversion_template_id;

    protected function paramHook()
    {
        if ($this->media_account->account_id <= 0) {
            throw new AppException('账户信息传参有误');
        }
        if (!$this->agent_id) {
            throw new AppException('渠道ID传参有误');
        }
        if (!$this->game_id) {
            throw new AppException('游戏ID传参有误');
        }
        if (!$this->site_id) {
            throw new AppException('广告位ID传参有误');
        }
        if (!$this->app_name) {
            throw new AppException('应用名称传参有误');
        }
        if (!$this->convert_source_type) {
            throw new AppException('转化来源传参有误');
        }
        if (!$this->convert_type) {
            throw new AppException('转化类型传参有误');
        }
        if ($this->convert_source_type !== ConvertSourceType::H5_API
            && !($this->game_type === "安卓" && $this->plat_id == PlatId::MINI)
            && !in_array($this->media_account->media_type, [MediaType::TENCENT]) // 广点通无需下载链接
            && !$this->download_url) {
            throw new AppException('下载链接传参有误');
        }
        if (!$this->action_track_url) {
            throw new AppException('点击监测链接传参有误');
        }
        if (!$this->game_type) {
            throw new AppException('游戏系统有误');
        }
        $method = "param{$this->media_account->media_type}Hook";
        if (method_exists($this, $method)) {
            $this->$method();
        }

        if (!$this->name) {
            $name_arr = [$this->site_id, $this->app_name, ConvertType::MAP[$this->media_account->media_type][$this->convert_type]];
            if (!empty($this->deep_external_action)) {
                $name_arr[] = DeepExternalAction::MAP[$this->media_account->media_type][$this->deep_external_action];
            }
            $this->name = implode('-', $name_arr);
        }
    }

    private function param1Hook()
    {
        if ($this->convert_source_type !== ConvertSourceType::H5_API && !$this->package_name) {
            throw new AppException('包名传参有误');
        }

        if ($this->convert_source_type === ConvertSourceType::SDK) {
            if (!$this->app_id) {
                throw new AppException('转化来源为SDK时必须传入app_id');
            }
        }

        if ($this->convert_source_type === ConvertSourceType::H5_API && !$this->external_url) {
            throw new AppException("转化来源为落地页H5时必须传入落地页链接");
        }

        if ($this->convert_source_type === ConvertSourceType::H5_API && $this->convert_data_type) {
            throw new AppException("转化来源为落地页H5时没有转化统计方式，请使用头条事件管理");
        }
    }

    private function param2Hook()
    {
        if ($this->conversion_template_id && $this->plat_id == PlatId::MINI && $this->action_track_type != ActionTrackType::TENCENT_V3) {
            throw new AppException('使用营销链路时必须选择3.0点击监测类型');
        }
    }

    private function param3Hook()
    {
        if (!$this->image_token) {
            throw new AppException('图片传参有误');
        }
    }

    private function param4Hook()
    {
        if ($this->convert_source_type === ConvertSourceType::SDK) {
            if (!$this->app_id && !$this->app_secret) {
                throw new AppException('转化来源为SDK时必须传入app_id和app_secret');
            }
        }
    }

    private function param6Hook()
    {
        if ($this->plat_id == PlatId::MINI) {
            if (!$this->app_id) {
                throw new AppException('小游戏必须传入app_id');
            }
            if (!$this->external_url) {
                throw new AppException('小游戏必须传入external_url');
            }
        } elseif ($this->game_type === 'IOS') {
            if (!$this->app_id) {
                throw new AppException('IOS必须传入app_id');
            }
        } else {
            if (!$this->package_name) {
                throw new AppException('安卓必须传入包名');
            }
            if (!$this->download_url) {
                throw new AppException('安卓必须传入下载链接');
            }
        }
    }

    /**
     * 把ConvertCreateParam类转化成请求的body
     */
    public function toRequestBody()
    {
        $method = "to{$this->media_account->media_type}RequestBody";
        if (method_exists($this, $method)) {
            return $this->$method();
        }
        throw new AppException("媒体类型:{$this->media_account->media_type}尚未对接转化");
    }

    private function to1RequestBody()
    {
        $body = [
            'advertiser_id' => $this->media_account->account_id,
            'name' => $this->name,
            'convert_source_type' => $this->convert_source_type,
            'convert_type' => ConvertType::MEDIA[$this->media_account->media_type][$this->convert_type],
            'action_track_url' => $this->action_track_url,
            'force_active' => 1,
        ];

        if (!empty($this->deep_external_action)) {
            $body['deep_external_action'] = $this->deep_external_action;
        }

        if (!empty($this->convert_data_type)) {
            $body['convert_data_type'] = $this->convert_data_type;
        }

        if (!empty($this->display_track_url)) {
            $body['display_track_url'] = $this->display_track_url;
        }

        switch ($this->convert_source_type) {
            case ConvertSourceType::SDK:
                $body['app_id'] = (string)(int)$this->app_id;
            case ConvertSourceType::API:
                $body['app_name'] = $this->app_name;
                $body['app_type'] = self::GAME_TYPE[$this->media_account->media_type][$this->game_type];
                $body['package_name'] = $this->package_name;
                $body['download_url'] = $this->download_url;
                break;
            case ConvertSourceType::H5_API:
                $body['external_url'] = $this->external_url;
                break;
            default:
                break;
        }

        return $body;
    }

    private function to2RequestBody()
    {
        $not_mini_game = $this->plat_id != PlatId::MINI;

        $body = [
            'account_id' => $this->media_account->account_id,
            'conversion_name' => $this->name,
            'access_type' => $this->convert_source_type === ConvertSourceType::SDK ? 'ACCESS_TYPE_SDK' : 'ACCESS_TYPE_API',
            'conversion_scene' => $not_mini_game ? self::GAME_TYPE[$this->media_account->media_type][$this->game_type] : 'CONVERSION_SCENE_WECHAT_MINI_GAME',
            'promoted_object_id' => $this->app_id,
            'claim_type' => $not_mini_game ? 'CLAIM_TYPE_ACTIVATION' : 'CLAIM_TYPE_REGISTER',
            'feedback_url' => $this->action_track_url,
            'self_attributed' => !($this->convert_source_type === ConvertSourceType::SDK),
            'optimization_goal' => ConvertType::MEDIA[$this->media_account->media_type][$this->convert_type],
        ];

        if (!empty($this->deep_external_action)) {
            if (in_array($this->deep_external_action, ['GOAL_1DAY_PURCHASE_ROAS', 'GOAL_1DAY_MONETIZATION_ROAS', 'GOAL_7DAY_PURCHASE_ROAS', 'GOAL_7DAY_LONGTERM_PURCHASE_ROAS', 'GOAL_1DAY_PURCHASE_MONETIZATION_ROAS'])) {
                $body['deep_worth_optimization_goal'] = $this->deep_external_action;
            } elseif (strpos($this->deep_external_action, 'ADVANCED_') === 0) {
                $body['deep_worth_advanced_goal'] = str_replace('ADVANCED_', '', $this->deep_external_action);
            } else {
                $body['deep_behavior_optimization_goal'] = $this->deep_external_action;
            }
        }

        if (!empty($this->user_action_set_id)) {
            $body['user_action_set_id'] = $this->user_action_set_id;
        }

        if ($this->convert_source_type === ConvertSourceType::SDK && !empty($this->app_android_channel_package_id) && $not_mini_game) {
            $body['app_android_channel_package_id'] = $this->app_android_channel_package_id;
        }

        if ($not_mini_game) {
            $body['impression_feedback_url'] = $this->display_track_url;
        }

        if ($this->conversion_template_id) {
            $body['conversion_template_id'] = (int)$this->conversion_template_id;
            $body['custom_report_index'] = self::CONVERSION_TEMPLATE_REPORT[$this->conversion_template_id] ?? [];
        }

        return $body;
    }

    private function to3RequestBody()
    {
        $body = [
            'advertiser_id' => $this->media_account->account_id,
            'app_name' => $this->app_name,
            'app_version' => $this->app_name . '-' . $this->site_id,
            'image_token' => $this->image_token,
            'package_name' => $this->package_name,
            'url' => $this->download_url,
            'platform' => self::GAME_TYPE[$this->media_account->media_type][$this->game_type],
            'use_sdk ' => $this->convert_source_type === ConvertSourceType::SDK ? 1 : 0,
        ];

        if ($this->game_type === "安卓") {
            $body['app_privacy_url'] = PlatformAbility::privateURL($this->media_account->platform, $this->game_id);
        }

        return $body;
    }

    private function to4RequestBody()
    {
        $body = [
            'ocpcTransFeedTypes' => [
                [
                    'transFrom' => $this->convert_source_type === ConvertSourceType::SDK ? 13 : 1,
                    'transName' => $this->name,
                    'downloadUrl' => $this->download_url,
                    'transTypes' => [4],
                    'mode' => 1,
                    'monitorUrl' => $this->action_track_url,
                    'appType' => self::GAME_TYPE[$this->media_account->media_type][$this->game_type],
                    'appName' => $this->app_name,
                    'apkName' => $this->package_name,
                ]
            ]
        ];

        if ($this->game_type === 'IOS') {
            $download_url = explode('?', $this->download_url);
            $body['ocpcTransFeedTypes'][0]['downloadUrl'] = $download_url[0];
        }

        if ($this->convert_source_type === ConvertSourceType::SDK) {
            $body['ocpcTransFeedTypes'][0]['sdkAppId'] = (int)$this->app_id;
            $body['ocpcTransFeedTypes'][0]['sdkSecretKey'] = $this->app_secret;
        }

        if (intval($this->convert_type) === 26) {
            $body['ocpcTransFeedTypes'][0]['deepTransTypes'] = [26];
        } else if (intval($this->convert_type) === 25) {
            $body['ocpcTransFeedTypes'][0]['deepTransTypes'] = [25, 26];
        } else if (!empty($this->deep_external_action)) {
            $body['ocpcTransFeedTypes'][0]['deepTransTypes'] = [intval($this->deep_external_action)];
        }

        return $body;
    }

    private function to6RequestBody()
    {
        // UC 视中文冒号为非法字符
        $this->name = str_replace("：", ":", $this->name);

        $body = [
            'adConvertTypes' => [
                [
                    'name' => $this->name,
                    'platform' => self::GAME_TYPE[$this->media_account->media_type][$this->game_type],
                    'convertType' => intval($this->convert_type),
                    'feedbackUrl' => $this->action_track_url,
                    'trackMode' => $this->convert_source_type === ConvertSourceType::SDK ? 5 : 4,
                ]
            ]
        ];

        if ($this->plat_id == PlatId::MINI) {
            // 101：小游戏链路
            $body['adConvertTypes'][0]['appIdStr'] = (string)$this->app_id;
            $body['adConvertTypes'][0]['trackPage'] = $this->external_url;
            $body['adConvertTypes'][0]['convertChainId'] = 101;
            $body['adConvertTypes'][0]['trackTarget'] = 5;
            $body['adConvertTypes'][0]['secondaryType'] = 6;
        } else {
            // 应用类 36：游戏链路
            $body['adConvertTypes'][0]['convertChainId'] = 36;
            $body['adConvertTypes'][0]['trackTarget'] = 1;
            $body['adConvertTypes'][0]['secondaryType'] = 1;

            if ($this->game_type === '安卓') {
                if ($this->convert_source_type === ConvertSourceType::SDK) {
                    $body['adConvertTypes'][0]['appId'] = intval($this->app_id);
                }
                $body['adConvertTypes'][0]['packageName'] = $this->package_name;
                $body['adConvertTypes'][0]['downloadUrl'] = $this->download_url;
            } else {
                $body['adConvertTypes'][0]['appId'] = intval($this->app_id);
            }
        }

        if (!empty($this->deep_external_action)) {
            $deep_external_action = intval($this->deep_external_action);
            $body['adConvertTypes'][0]['deepConvertType'] = $deep_external_action;
        }

        if ($this->convert_data_type > 0) {
            $body['adConvertTypes'][0]['roiAmount'] = intval($this->convert_data_type);
        }

        return $body;
    }
}
