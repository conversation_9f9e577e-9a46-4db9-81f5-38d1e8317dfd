<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADAsset;

use App\Exception\AppException;
use App\Param\AbstractParam;

class ToutiaoNativeAnchorParam extends AbstractParam
{
    public $id = 0;
    public $game_id = 0;
    public $platform = 0;
    public $account_id = 0;
    public $anchor_type = '';
    public $tool_title = '';
    public $game_type = '';
    public $instance_id = "";
    public $anchor_game_type = 'GENERAL';
    public $anchor_image_type = 'WIDE';
    public $path_param = '';
    public $download_url = '';
    public $anchor_title = '';
    public $icon_images;
    public $head_images;
    public $app_tags = [];
    public $guide_text = '';
    public $anchor_image_mode = 200;
    public $app_images = '';
    public $game_description = '';
    public $game_charatoristic = '';
    public $other_description = '';
    public $error_msg = '';
    public $status = 0;
    public $creator = '';

    public function toInsertData()
    {
        $data = $this->toArray();
        unset($data['id']);
        $data['icon_images'] = json_encode($data['icon_images']);
        $data['head_images'] = json_encode($data['head_images']);
        $data['app_tags'] = json_encode($data['app_tags']);
        $data['app_images'] = json_encode($data['app_images']);
        return $data;
    }

    /**
     * @return array
     */
    public function toHttpGmeAnchorData(): array
    {
        $data = [
            'game_type' => $this->anchor_game_type,
            'head_image_list' => [$this->head_images],
            'app_tags' => $this->app_tags,
            'guide_text' => $this->guide_text,
            'anchor_image_mode' => $this->anchor_image_mode,
            'app_images' => $this->app_images,
            'game_description' => $this->game_description,
            'game_charatoristic' => $this->game_charatoristic,
            'other_description' => $this->other_description,
            'ios_anchor_title' => $this->anchor_title,
            'android_anchor_title' => $this->anchor_title,

        ];

        if ($data['game_type'] == 'GENERAL') {
            $data['platform_type'] = $this->game_type == 'IOS' ? 3 : 2;
            if ($this->game_type == 'IOS') {
                $data['ios_download_url'] = $this->download_url;
            } else {
                $data['android_download_url'] = $this->download_url;
            }
        }

        if ($data['game_type'] == 'MICRO_GAME') {
            $data['instance_id'] = json_decode($this->instance_id, true)['instance_id'];
            $data['path_param'] = $this->path_param;
            $data['icon_images'] = [['uri' => $this->icon_images['uri']]];
        }

        return $data;
    }

    public function paramHook()
    {
        if ($this->game_id && !is_numeric($this->game_id)) {
            $this->game_id = explode('-', $this->game_id)[1];
        }
        if (!$this->anchor_image_type) {
            $this->anchor_image_type = 'WIDE';
        }
    }

    public function validate()
    {
        if (!$this->game_id) {
            throw new AppException('游戏不能为空');
        }
        if (!$this->platform) {
            throw new AppException('平台不能为空');
        }
        if (!$this->account_id) {
            throw new AppException('account_id不能为空');
        }
        if (!$this->anchor_type) {
            throw new AppException('类型不能为空');
        }
        if (!$this->tool_title) {
            throw new AppException('工具名字不能为空');
        }
        if (!$this->game_type) {
            throw new AppException('游戏类型不能为空');
        }
        if (!$this->anchor_title) {
            throw new AppException('锚点标题不能为空');
        }
        if (!($this->head_images['uri'] ?? '')) {
            throw new AppException('头部图片不能为空');
        }
        if (!$this->app_tags) {
            throw new AppException('游戏标签不能为空');
        }
        if (!$this->guide_text) {
            throw new AppException('游戏标签不能为空');
        }
        if (!$this->app_images) {
            throw new AppException('游戏图片不能为空');
        }
        if (count($this->app_images) < 3) {
            throw new AppException('游戏图片必须大于三张');
        }
        if (!$this->game_description) {
            throw new AppException('游戏简介不能为空');
        }
        if (!$this->game_charatoristic) {
            throw new AppException('游戏特色不能为空');
        }
        if (!$this->other_description) {
            throw new AppException('其他描述不能为空');
        }
        return $this;
    }
}
