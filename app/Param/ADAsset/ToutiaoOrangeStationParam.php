<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADAsset;

use App\Exception\AppException;
use App\Model\SqlModel\Zeda\ToutiaoOrangeStationModel;
use App\Param\AbstractParam;

class ToutiaoOrangeStationParam extends AbstractParam
{
    public $id = 0;
    public $platform = 0;
    public $account_id = 0;
    public $account_name = '';
    public $target_account_id = 0;
    public $target_account_name = '';
    public $site_ids;
    public $origin_site_ids = [];
    public $origin_site_names = [];
    public $error_msg = '';
    public $task_state = ToutiaoOrangeStationModel::TASK_NO_START;
    public $creator = '';

    public function paramHook()
    {
    }

    public function toInsertData()
    {
        $data = $this->toArray();
        unset($data['id']);
        return $data;
    }

    public function validate()
    {
        if (!$this->target_account_id) {
            throw new AppException('目标账号ID不能为空');
        }

        if (!$this->origin_site_ids) {
            throw new AppException('站点ID不能为空');
        }
    }
}
