<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADAsset;

use App\Exception\AppException;
use App\Param\AbstractParam;

class ToutiaoAvatarParam extends AbstractParam
{
    public $id = 0;
    public $account_id = 0;
    public $image_url = '';
    public $status = 0;

    public function toInsertData()
    {
        $data = $this->toArray();
        unset($data['id']);
        return $data;
    }

    public function validate()
    {
        if (!$this->account_id) {
            throw new AppException('account_id不能为空');
        }
        if (!$this->image_url) {
            throw new AppException('账户头像不能为空');
        }
        return $this;
    }
}
