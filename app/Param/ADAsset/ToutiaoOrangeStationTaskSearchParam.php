<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADAsset;

use App\Param\AbstractParam;

class ToutiaoOrangeStationTaskSearchParam extends AbstractParam
{
    public $page = 0;
    public $rows = 0;
    public $account_id = '';
    public $account_name = '';
    public $target_account_id = '';
    public $target_account_name = '';
    public $platform = '';
    public $creator = '';
    public $create_time = [];
    public $date = [];
    public $task_state = '';

    public function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }

        if ($this->create_time) {
            $this->create_time[1] = $this->create_time[1] . ' 23:59:59';
        }
    }
}
