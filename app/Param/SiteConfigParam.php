<?php

namespace App\Param;

use App\Constant\AgentGroup;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAPPManagementLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\MediaTypeModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\ADServing\AbstractADSettingContentParam;

class SiteConfigParam extends AbstractParam
{
    public $is_concat = 1;
    public $site_id;
    public $site_name = '';
    public $site_suffix_name = '';
    public $full_site_suffix_name;
    public $platform;
    public $media_type;
    public $media_type_name;
    public $agent_group;
    public $account_id = 0;
    public $agent_id = 0;
    public $game_id;
    public $game_type;
    public $ori_game_id = 0;
    public $game_name;
    public $game_pack = 1;
    public $ori_game_pack = 0;
    public $plat_id;
    public $package;
    public $appid = '';
    public $app_secret = '';
    public $akey = '';
    public $convert_id = '';
    public $convert_type = '';
    public $convert_source_type = '';
    public $convert_data_type = '';
    public $pay_type;
    public $upt_state = 1;
    public $upt_rate = 0;
    public $deep_external_action = '';
    public $is_third = 0;
    public $download_url = '';
    public $action_track_url = '';
    public $display_track_url = '';
    public $action_track_type = 0;
    public $ad_turn = 0;
    public $ad_price = 0.00;
    public $ad_pop_zk = 0;
    public $ad_pop_zk_type = 0;
    public $auto_download;
    public $auto_download_second;
    public $cps_divide_rate;
    public $forbid_tuitan;
    public $template_type;
    public $template_address;
    public $add_number = 1;
    public $pay_discount = 0;
    public $reg_discount = 0;
    public $image_token = '';
    public $app_name = '';
    public $beian = '';
    public $state;
    public $statistic_caliber = 0;
    public $app_android_channel_package_id = '';
    public $ext = [];
    public $sdk_ext = [];
    public $android_union_channel_package_account_id = 0;
    public $user_action_set_id = '';
    public $account_type;
    public $convert_toolkit = '';
    public $is_callback_with_money_amount = 0;
    public $audit_type;
    public $money_level;
    public $money_range;
    public $skew_type;
    public $skew_num;
    public $strategy_json;
    public $site_from = 0; // 1：由批量广告创建
    /**
     * @var AbstractADSettingContentParam $setting
     */
    public $setting;

    public function mediaTypeFormat()
    {
        if (isset(MediaType::MEDIA_TYPE_MAP[$this->media_type])) {
            $this->media_type_name = MediaType::MEDIA_TYPE_MAP[$this->media_type];
        } else {
            $media_type_info = (new MediaTypeModel())->getData($this->media_type);
            if (empty($media_type_info)) {
                throw new AppException('查无此媒体类型');
            }
            $this->media_type_name = $media_type_info->name;
        }
    }

    public function convertTypeFormat()
    {
        if (!$this->convert_type) {
            $this->convert_type = 0;
        }
    }

    public function adTurnFormat()
    {
        if (!$this->ad_turn) {
            $this->ad_turn = 0;
        }
    }

    public function adPriceFormat()
    {
        if (!$this->ad_price) {
            $this->ad_price = 0;
        }
    }

    public function adPopZkFormat()
    {
        if (!$this->ad_pop_zk) {
            $this->ad_pop_zk = 0;
        }
    }

    public function adPopZkTypeFormat()
    {
        if (!$this->ad_pop_zk_type) {
            $this->ad_pop_zk_type = 0;
        }
    }

    public function uptRateFormat()
    {
        if (!$this->upt_rate) {
            $this->upt_rate = 0;
        }
    }

    public function payDiscountFormat()
    {
        if (!$this->pay_discount) {
            $this->pay_discount = 0;
        }
    }

    public function regDiscountFormat()
    {
        if (!$this->reg_discount) {
            $this->reg_discount = 0;
        }
    }

    public function autoDownloadSecondFormat()
    {
        if (!$this->auto_download_second) {
            $this->auto_download_second = 0;
        }
    }

    public function cpsDivideRateFormat()
    {
        if (!$this->cps_divide_rate) {
            $this->cps_divide_rate = 0;
        }
    }

    public function extFormat()
    {
        if (is_string($this->ext)) {
            $this->ext = json_decode($this->ext, true);
        } elseif (is_null($this->ext)) {
            $this->ext = [];
        }

        if (isset($this->ext['money_level'])) {
            if (is_array($this->ext['money_level']) && !empty($this->ext['money_level'])) {
                // money_level 保留2位小数，整数也要补齐0
                $tmp_money_level = [];
                foreach ($this->ext['money_level'] as $key => $value) {
                    $tmp_money_level[number_format($key, 2, '.', '')] = number_format($value, 2, '.', '');
                }
                $this->ext['money_level'] = json_encode($tmp_money_level);
            } else if (empty($this->ext['money_level'])) {
                // 兼容空数组[]
                $this->ext['money_level'] = json_encode((object)[]);
            }
        }

        if (isset($this->ext['money_range'])) {
            $this->ext['money_range'] = is_array($this->ext['money_range']) && !empty($this->ext['money_range'])
                ? json_encode($this->ext['money_range']) : $this->ext['money_range'];
        }

        if (isset($this->ext['strategy_json'])) {
            $this->ext['strategy_json'] = is_array($this->ext['strategy_json']) && !empty($this->ext['strategy_json'])
                ? json_encode($this->ext['strategy_json']) : $this->ext['strategy_json'];
        }
    }

    public function paramHook()
    {
        if (empty($this->game_name) ||
            empty($this->package) ||
            empty($this->app_name) ||
            empty($this->game_type) ||
            empty($this->plat_id) ||
            empty($this->audit_type) ||
            ($this->game_type === 'IOS' && $this->plat_id != PlatId::MINI && empty($this->appid))
        ) {
            $game_info = (new V2DimGameIdModel())->getDataByGameId($this->platform, $this->game_id);
            if ($game_info->os === 'IOS' && $game_info->plat_id != PlatId::MINI) {
                if (empty($game_info->app_id)) {
                    throw new AppException("缺少游戏{$game_info->game_id}-{$game_info->game_name}的appid，请{$this->platform}平台补充上传");
                } else {
                    $this->appid = $game_info->app_id;
                    !$this->download_url && $this->download_url = "https://itunes.apple.com/cn/app/id{$this->appid}?mt=8";
                }
            }
            if (empty($this->game_name)) {
                $this->game_name = $game_info->game_name ?? '';
            }
            if (empty($this->package)) {
                $this->package = $game_info->app_package_name ?? '';
            }
            if (empty($this->game_type)) {
                $this->game_type = $game_info->os ?? '';
            }
            if (empty($this->app_name)) {
                $this->app_name = $game_info->app_name ?? '';
            }
            if (empty($this->plat_id)) {
                $this->plat_id = $game_info->plat_id ?? '';
            }
            if (empty($this->audit_type)) {
                $this->audit_type = $game_info->audit_type ?? 0;
            }
            // 特殊游戏 需要读取sdk的app_name dim_game上的app_name作为兜底
            if (in_array($game_info->root_game_id, [1632,1528,1402,1438,1214,1516,1546,1578,1488])) {
                $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, $this->media_type, $this->game_id);
                if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                    $sdk_ext = json_decode($game_sdk_info->ext, true);
                    $app_name = $sdk_ext["app_name"] ?? "";
                    $this->app_name = $app_name ?: $this->app_name;
                }
            }
        }

        // 小游戏不用打包
        if ($this->plat_id == PlatId::MINI) {
            $this->game_pack = 0;
        }

        $method = "param{$this->media_type}Hook";
        if (method_exists($this, $method)) {
            $this->$method();
        }

        $method = "param{$this->platform}Hook";
        if (method_exists($this, $method)) {
            $this->$method();
        }

        if ($this->is_concat === 1) {
            $full_site_suffix_name = [$this->game_name];
            if (isset(ConvertType::MAP[$this->media_type])) {
                $full_site_suffix_name[] = ConvertType::MAP[$this->media_type][$this->convert_type];
            }
            if (!empty($this->site_suffix_name)) {
                $full_site_suffix_name[] = $this->site_suffix_name;
            }
            $this->full_site_suffix_name = implode('-', $full_site_suffix_name);
        } else {
            $this->full_site_suffix_name = $this->site_suffix_name;
        }

//        if ($this->convert_source_type === ConvertSourceType::H5_API &&
//            ((!in_array($this->media_type, [
//                        MediaType::TOUTIAO, MediaType::DOUYIN_STAR, MediaType::TOUTIAO_APP, MediaType::BILIBILI,
//                        MediaType::QIHU_SEARCH, MediaType::HUYA, MediaType::IQIYI, MediaType::NETEASE,
//                        MediaType::WEIBO, MediaType::KUAISHOU_PINPAI, MediaType::TENCENT, MediaType::DOUYU,
//                        MediaType::JUGAO, MediaType::HONOR, MediaType::SIGMOB, MediaType::OTHER,
//                        MediaType::UC
//                    ])
//                    && ($this->agent_group && !in_array($this->agent_group, [
//                            AgentGroup::KUAISHOU_MINI_GAME, AgentGroup::BAIDU_MINI_GAME, AgentGroup::UC_MINI_GAME, AgentGroup::KUAISHOU_LIVE,
//                            AgentGroup::BAIDU_SEARCH_MINI_GAME, AgentGroup::KUAISHOU_HOT, AgentGroup::KUAISHOU_STAR, AgentGroup::KUAISHOU_SEARCH,
//                            AgentGroup::KUAISHOU_NATIVE, AgentGroup::CHENZHONG_LIVE
//                        ])))
//                || !in_array((int)$this->plat_id, [PlatId::MINI, PlatId::DY_MINI, PlatId::MT_MINI, PlatId::KS_MINI, PlatId::QQ_MINI, PlatId::DY_MINI_PROGRAM]))
//        ) {
//            throw new AppException("转化来源落地页H5暂不支持此渠道投放小游戏");
//        }
    }

    private function param1Hook()
    {
        if (in_array($this->deep_external_action, ['AD_CONVERT_TYPE_PURCHASE_ROI', 'AD_CONVERT_TYPE_PURCHASE_ROI_7D'])
            && $this->convert_data_type === 'EVERY_ONE') {
            throw new AppException('付费ROI下,不允许转化统计方式选择每一次');
        }

        if ($this->game_type === '安卓' && in_array($this->agent_group,
                [AgentGroup::TOUTIAO_LIVE, AgentGroup::TOUTIAO_UOP, AgentGroup::DOUYIN_UOP, AgentGroup::DOUYIN_MINI_GAME])
        ) {
            // API转化链路不需建转化，事件资产需要建资产等一系列东西
            if (
                in_array($this->convert_source_type, [ConvertSourceType::H5_API])
                || ($this->convert_source_type === ConvertSourceType::API && $this->convert_toolkit === ConvertToolkit::TOUTIAO_CONVERT)
            ) {
                $this->game_pack = 0;
            } else {
                // 头条直播导流
                $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->game_pack = 0;
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->package = $game_sdk_info->package_name;
            }
        } else {
            if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {

                $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
                if ($this->convert_source_type === ConvertSourceType::SDK) {
                    if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                        throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                    }
                    $this->appid = $game_sdk_info->appid;
                }

                if ($this->game_pack === 2) {
                    if (empty($game_sdk_info->account_id)) {
                        throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                    }
                    if (empty($game_sdk_info->app_secret)) {
                        throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                    }
                    $this->appid = $game_sdk_info->appid;
                    $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                    $this->app_secret = $game_sdk_info->app_secret;
                    $this->package = $game_sdk_info->package_name;
                }

                // 不打包也要拿下载链接
                if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                    if (empty($game_sdk_info->download_url)) {
                        throw new AppException('暂无头条应用下载链接');
                    }
                    $this->download_url = $game_sdk_info->download_url;
                    $this->appid = $game_sdk_info->appid;
                    $this->app_secret = $game_sdk_info->app_secret;
                    $this->package = $game_sdk_info->package_name;
                }

                $this->account_type = $game_sdk_info->account_type ?? 'AD';
            }
        }
        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    private function param2Hook()
    {
        if ($this->game_type === '安卓' || $this->plat_id == PlatId::MINI) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TENCENT, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充腾讯的appid');
            }

            if ($this->convert_source_type === ConvertSourceType::SDK && $game_sdk_info->user_action_set_id <= 0) {
                throw new AppException('请前往广告投放-sdk管理补充腾讯的数据源id');
            }

            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            // 小游戏分包特殊处理
            if ($this->plat_id == PlatId::MINI && !empty($this->app_android_channel_package_id)) {
                $this->appid = "{$game_sdk_info->appid};{$this->app_android_channel_package_id}";
            }
        }
    }

    private function param3Hook()
    {
        if ($this->game_type === '安卓' || in_array($this->plat_id, [PlatId::MINI, PlatId::KS_MINI])) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::KUAISHOU, $this->game_id);

            if ($this->plat_id == PlatId::KS_MINI) {
                if (empty($game_sdk_info)) {
                    throw new AppException('请前往广告投放-sdk管理补充快手小游戏的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info)) {
                    throw new AppException('请前往广告投放-sdk管理补充快手的appid');
                }
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充快手的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
            }

            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    private function param4Hook()
    {
        if ($this->game_type === '安卓' || $this->plat_id == PlatId::MINI) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::BAIDU, $this->game_id);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info)) {
                    throw new AppException('请前往广告投放-sdk管理补充媒体的appid');
                }
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
            }
            if ($this->app_android_channel_package_id === SiteModel::CHANNEL_PACKAGE_WAITING && strlen($game_sdk_info->ext) < 3) {
                throw new AppException('请前往广告投放-sdk管理补充媒体的应用信息');
            } else {
                $this->sdk_ext = json_decode($game_sdk_info->ext ?? '', true);
            }
        }
    }

    private function param5Hook()
    {
        if ($this->game_type === '安卓' || $this->plat_id == PlatId::MINI) {
            $game_sdk_info =
                (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TENCENT, $this->game_id)
                    ?: (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::MP, $this->game_id);;
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充媒体的appid');
            }

            if (($this->convert_source_type === ConvertSourceType::SDK || $this->plat_id == PlatId::MINI) && $game_sdk_info->user_action_set_id <= 0) {
                throw new AppException('请前往广告投放-sdk管理补充媒体的数据源id');
            }

            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->user_action_set_id = $game_sdk_info->user_action_set_id;
        }
    }

    private function param6Hook()
    {
        if (($this->game_type === '安卓' && $this->convert_source_type === ConvertSourceType::SDK) || $this->plat_id == PlatId::MINI) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::UC, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充媒体的appid');
            }
            $this->appid = $game_sdk_info->appid;
            if ($this->plat_id == PlatId::MINI) {
                if (strlen($game_sdk_info->ext) < 3) {
                    throw new AppException('请前往广告投放-sdk管理补充小程序信息');
                } else {
                    $this->sdk_ext = json_decode($game_sdk_info->ext, true);
                    if (empty($this->sdk_ext['mini_game_original_id'] ?? '')) {
                        throw new AppException('请前往广告投放-sdk管理补充小程序的原始ID');
                    }
                }
            }
        }
    }

    private function param10Hook()
    {
        if ($this->plat_id == PlatId::MINI) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::IQIYI, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充媒体信息');
            }
            if (strlen($game_sdk_info->ext) < 3) {
                throw new AppException('请前往广告投放-sdk管理补充小程序信息');
            } else {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
                if (empty($this->sdk_ext['mini_game_original_id'] ?? '')) {
                    throw new AppException('请前往广告投放-sdk管理补充小程序的原始ID');
                }
            }
        }
    }

    private function param14Hook()
    {
        if ($this->game_type === '安卓') {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::BAIDU, $this->game_id);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info)) {
                    throw new AppException('请前往广告投放-sdk管理补充媒体的appid');
                }
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
            }
            if ($this->app_android_channel_package_id === SiteModel::CHANNEL_PACKAGE_WAITING && strlen($game_sdk_info->ext) < 3) {
                throw new AppException('请前往广告投放-sdk管理补充媒体的应用信息');
            } else {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    // 抖音红人
    private function param21Hook()
    {
        if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {
            $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            // 不打包也要拿下载链接
            if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    // 头条应用管理中心
    private function param23Hook()
    {
        if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {
            $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            // 不打包也要拿下载链接
            if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
        // 抖音小手柄渠道组IOS 强制使用事件管理
        if ($this->agent_group === AgentGroup::DOUYIN_UOP && $this->game_type === 'IOS') {
            $this->convert_toolkit = ConvertToolkit::TOUTIAO_ASSET;
        }

        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    private function param30Hook()
    {
        if ($this->game_type === '安卓' && $this->game_pack === 2) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::KUAISHOU, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充快手的appid');
            }
            if (empty($game_sdk_info->account_id)) {
                throw new AppException('请前往广告投放-sdk管理补充快手的主线包账户id');
            }
            if (empty($game_sdk_info->app_secret)) {
                throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
            }
            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->app_secret = $game_sdk_info->app_secret;
        }
    }

    private function param22Hook()
    {
        if ($this->game_type === '安卓' && $this->game_pack === 2) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::KUAISHOU, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充快手的appid');
            }
            if (empty($game_sdk_info->account_id)) {
                throw new AppException('请前往广告投放-sdk管理补充快手的主线包账户id');
            }
            if (empty($game_sdk_info->app_secret)) {
                throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
            }
            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->app_secret = $game_sdk_info->app_secret;
        }
    }

    private function param108Hook()
    {
        if ($this->game_type === '安卓' && $this->game_pack === 2) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            if (empty($game_sdk_info->account_id)) {
                throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
            }
            if (empty($game_sdk_info->app_secret)) {
                throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
            }
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->app_secret = $game_sdk_info->app_secret;
            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
    }

    private function param28Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param29Hook()
    {
        if ($this->plat_id == PlatId::MINI) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::HUYA, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充媒体信息');
            }
            if (strlen($game_sdk_info->ext) < 3) {
                throw new AppException('请前往广告投放-sdk管理补充小程序信息');
            } else {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
                if (empty($this->sdk_ext['mini_game_original_id'] ?? '')) {
                    throw new AppException('请前往广告投放-sdk管理补充小程序的原始ID');
                }
            }
        }
    }

    private function param49Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param99Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param103Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param163Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }

        if (in_array($this->game_id, [18180, 16962]) && ($this->game_type === '安卓' || $this->plat_id == PlatId::MINI)) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::BILIBILI, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充信息');
            }

            if ($this->plat_id == PlatId::MINI) {
                $this->sdk_ext = json_decode($game_sdk_info->ext ?? '', true);
                if (empty($this->sdk_ext['mini_game_original_id'])) {
                    throw new AppException('请前往广告投放-sdk管理补充小程序原始ID');
                }
            }

            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充快手的主线包账户id');
                }
                if (empty($game_sdk_info->appid)) {
                    throw new AppException('请检查sdk管理账号是否正确，重新获取APPID');
                }
                $this->appid = $game_sdk_info->appid;
            }
        }
    }

    private function param164Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }

        if ($this->game_type === '安卓' && $this->game_pack === 2) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::KUAISHOU, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充快手的appid');
            }
            if (empty($game_sdk_info->account_id)) {
                throw new AppException('请前往广告投放-sdk管理补充快手的主线包账户id');
            }
            if (empty($game_sdk_info->app_secret)) {
                throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
            }
            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->app_secret = $game_sdk_info->app_secret;
        }
    }

    private function param195Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param196Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param197Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param198Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param199Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param200Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param201Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param202Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param203Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param204Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param207Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param209Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param210Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    private function param213Hook()
    {
        if (!$this->convert_source_type) {
            $this->convert_source_type = ConvertSourceType::API;
        }
    }

    // 星图
    private function param221Hook()
    {
        if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {
            $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            // 不打包也要拿下载链接
            if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    // 星广联投AD版
    private function param295Hook()
    {
        if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {
            $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            // 不打包也要拿下载链接
            if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    // 星图红人
    private function param303Hook()
    {
        if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {
            $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            // 不打包也要拿下载链接
            if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    // 抖音企业号
    private function param280Hook()
    {
        if ($this->game_type === '安卓' && ($this->convert_source_type === ConvertSourceType::SDK || in_array($this->game_pack, [0, 2]))) {
            $game_sdk_info = (new V2DimGameIdModel())->getToutiaoAppInfoByGameId($this->platform, $this->game_id, MediaType::TOUTIAO);
            if ($this->convert_source_type === ConvertSourceType::SDK) {
                if (empty($game_sdk_info) || empty($game_sdk_info->appid)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的appid');
                }
                $this->appid = $game_sdk_info->appid;
            }

            if ($this->game_pack === 2) {
                if (empty($game_sdk_info->account_id)) {
                    throw new AppException('请前往广告投放-sdk管理补充头条的主线包账户id');
                }
                if (empty($game_sdk_info->app_secret)) {
                    throw new AppException('请检查sdk管理APPID与账号是否正确，如果APP密钥为空请点击获取按钮');
                }
                $this->appid = $game_sdk_info->appid;
                $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            // 不打包也要拿下载链接
            if ($this->game_pack === 0 && $this->convert_source_type !== ConvertSourceType::H5_API) {
                if (empty($game_sdk_info->download_url)) {
                    throw new AppException('暂无头条应用下载链接');
                }
                $this->download_url = $game_sdk_info->download_url;
                $this->appid = $game_sdk_info->appid;
                $this->app_secret = $game_sdk_info->app_secret;
                $this->package = $game_sdk_info->package_name;
            }

            $this->account_type = $game_sdk_info->account_type ?? 'AD';
        }
        // 事件管理IOS 需要资产应用名称 即苹果商店的应用名
        if ($this->game_type === 'IOS' && $this->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TOUTIAO, $this->game_id);
            // 暂时不开启检查 等大部分应用补齐了再开启
//            if (empty($game_sdk_info) || strlen($game_sdk_info->ext) < 3) {
//                throw new AppException('请前往广告投放-sdk管理补充头条的资产应用名称');
//            }
            if (!empty($game_sdk_info) && strlen($game_sdk_info->ext) > 2) {
                $this->sdk_ext = json_decode($game_sdk_info->ext, true);
            }
        }
    }

    // 微信视频号直播
    private function param277Hook()
    {
        // 使用腾讯的应用信息
        if ($this->game_type === '安卓' && $this->game_pack == 2) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TENCENT, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充腾讯的appid');
            }

            if (($this->convert_source_type === ConvertSourceType::SDK || $this->plat_id == PlatId::MINI) && $game_sdk_info->user_action_set_id <= 0) {
                throw new AppException('请前往广告投放-sdk管理补充腾讯的数据源id');
            }

            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->user_action_set_id = $game_sdk_info->user_action_set_id;
        }
    }

    // 排期媒体
    private function param17Hook()
    {
        // 使用腾讯的应用信息
        if ($this->game_type === '安卓' && $this->game_pack == 2) {
            $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($this->platform, MediaType::TENCENT, $this->game_id);
            if (empty($game_sdk_info)) {
                throw new AppException('请前往广告投放-sdk管理补充腾讯的appid');
            }

            if (($this->convert_source_type === ConvertSourceType::SDK || $this->plat_id == PlatId::MINI) && $game_sdk_info->user_action_set_id <= 0) {
                throw new AppException('请前往广告投放-sdk管理补充腾讯的数据源id');
            }

            $this->appid = $game_sdk_info->appid;
            $this->android_union_channel_package_account_id = $game_sdk_info->account_id;
            $this->user_action_set_id = $game_sdk_info->user_action_set_id;
        }
    }

    private function paramZWHook()
    {
        if ($this->setting instanceof AbstractADSettingContentParam) {
            try {
                $this->template_type = $this->setting->getLDYContent();
            } catch (\Throwable $e) {
                // 不管发生什么错误，都给个默认值,改模板为测试1
                $this->template_type = 25;
            }
        } else {
            $this->template_type = 25;
        }
    }
}
