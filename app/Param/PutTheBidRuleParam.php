<?php

namespace App\Param;


use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsMediaGamePriceRangeLogModel;

class PutTheBidRuleParam extends AbstractParam
{
    public $platform = '';
    public $target_id = '';
    public $game_type = '';
    //飞书webhook推送地址
    public $feishu_webhook_url = '';
    //规则数据组
    public $execution_account = [];
    //媒体类型
    public $media_type = '';
    public $creator;
    public $exclude_game_id = [];
    public $aweme_account = '';

    protected function paramHook()
    {
        if (!$this->target_id || $this->target_id < 1 ) {
            throw new AppException('集团游戏id,(标识ID)必选');
        }

        if (!$this->feishu_webhook_url) {
            throw new AppException('飞书webhook推送地址,不能为空');
        }

        if (!$this->media_type) {
            throw new AppException('媒体类型不能为空');
        }

        if (!$this->execution_account) {
            throw new AppException('投放范围对象不能为空');
        }

        if (!$this->game_type) {
            throw new AppException('游戏类型不能为空');
        }

        if (!$this->platform && in_array($this->game_type, [OdsMediaGamePriceRangeLogModel::GAME_TYPE_ROOT_GAME, OdsMediaGamePriceRangeLogModel::GAME_TYPE_GAME])) {
            throw new AppException('根游戏的平台不能为空');
        }

        // todo  sort_start_and_end_time 使用数据降序处理拿值,得到收尾相连时间
        $start_data = array_column($this->execution_account, 'start_time');

        foreach ($start_data as $start_data_value){
            if (empty($start_data_value)){
                throw new AppException('生效时间不能为空');
            }
        }

        $start_data_unique = array_unique($start_data);
        if (count($start_data) != count($start_data_unique)){
            throw new AppException('生效时间,必须是唯一的,请修改重复的生效时间');
        }

        // 集团游戏没有平台
        if ($this->game_type == OdsMediaGamePriceRangeLogModel::GAME_TYPE_CLIQUE) {
            $this->platform = '';
        }
    }

}
