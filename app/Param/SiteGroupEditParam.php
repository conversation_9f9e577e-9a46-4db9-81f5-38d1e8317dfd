<?php

namespace App\Param;

class SiteGroupEditParam extends AbstractParam
{
    public $platform;
    public $agent_id;
    public $site_ids;           // 数组
    public $game_id;            // 批量编辑扣量 需要传(已弃用，不需要传)
    public $site_suffix_name;
    public $pay_type;           // 6=cps 3=cpa 5免费 7cpt
    public $upt_state;
    public $ad_turn;
    public $ad_price;           // cpa注册单价
    public $ad_pop_zk;          // 扣量百分比 整型
    public $pay_discount;       // 付费上报扣量百分比 整型
    public $reg_discount;       // 注册上报扣量百分比 整型
    public $ad_pop_zk_type;     // 1:扣注册;2:扣订单数;3:扣订单金额
    public $cps_divide_rate;    // cps分成比例 整型
    public $statistic_caliber;  // 统计口径 1=按平台 2=按子游戏 3=按根游戏
    public $auto_download;
    public $auto_download_second;
    public $forbid_tuitan;
    public $template_type;
    /**
     * 删除备案号 1：删除 0：不操作
     * @var int
     */
    public $beian_type;
    public $beian;
    public $state;
    public $money_range;
    public $strategy_json;
    public $ext;

    public function extFormat()
    {
        if (is_string($this->ext)) {
            $this->ext = json_decode($this->ext, true);
        } elseif (is_null($this->ext)) {
            $this->ext = [];
        }

        if (isset($this->ext['money_level'])) {
            if (is_array($this->ext['money_level']) && !empty($this->ext['money_level'])) {
                // money_level 保留2位小数，整数也要补齐0
                $tmp_money_level = [];
                foreach ($this->ext['money_level'] as $key => $value) {
                    $tmp_money_level[number_format($key, 2, '.', '')] = number_format($value, 2, '.', '');
                }
                $this->ext['money_level'] = json_encode($tmp_money_level);
            } else if (empty($this->ext['money_level'])) {
                // 兼容空数组[]
                $this->ext['money_level'] = json_encode((object)[]);
            }
        }

        if (isset($this->ext['money_range'])) {
            $this->ext['money_range'] = is_array($this->ext['money_range']) && !empty($this->ext['money_range'])
                ? json_encode($this->ext['money_range']) : $this->ext['money_range'];
        }

        if (isset($this->ext['strategy_json'])) {
            $this->ext['strategy_json'] = is_array($this->ext['strategy_json']) && !empty($this->ext['strategy_json'])
                ? json_encode($this->ext['strategy_json']) : $this->ext['strategy_json'];
        }
    }
}
