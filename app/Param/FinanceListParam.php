<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param;

use App\Constant\MediaType;
use App\Logic\DSP\FinanceLogic;
use App\Logic\DSP\PermissionLogic;
use App\Utils\SqlParser;
use Common\EnvConfig;

class FinanceListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $fund_media_type;
    public $media_type;
    public $aggregation_time;
    public $date;
    public $platform;
    public $account_id;
    public $agent_leader;
    public $creator;
    public $company;
    public $majordomo_name;
    public $remitter;
    public $payee;
    public $fund_type;
    public $trade_type;
    public $account_fund_name;
    public $fund_purpose;
    public $pay_method_name;
    public $product_name;
    public $act_flag;
    public $cache_type_name;
    public $agency_name;
    public $account_type;
    public $settle_company;
    public $is_recharge;
    public $ori_cost_account;
    public $ori_cost_creative;
    public $cost_difference;
    public $cost;
    public $ori_cost;
    public $target;
    public $agent_permission;
    public $deposit;
    public $paid;
    public $trans_in;
    public $trans_out;
    public $credit_modify;
    public $balance;
    public $preauth_balance;
    public $agency_short_name;
    public $search_time;
    public $wallet_id;
    public $true_account_id;
    public $operation_type_desc;
    public $wallet_agency_name;
    public $wallet_agency_short_name;
    public $wallet_settle_company;
    public $majordomo_agency_name;
    public $majordomo_agency_short_name;
    public $majordomo_settle_company;
    public $contract_game_name;
    public $clique_id;
    public $root_game_id;
    public $main_game_id;
    public $game_id;
    public $max_payway_company;

    public $game_filter = [];

    protected function paramHook()
    {
        switch ($this->fund_media_type) {
            case FinanceLogic::FUND_MEDIA_TYPE_TOUTIAO :
                $this->media_type = MediaType::TOUTIAO;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_TENCENT :
            case FinanceLogic::FUND_MEDIA_TYPE_TENCENT_DAILY :
                $this->media_type = MediaType::TENCENT;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_KUAISHOU :
                $this->media_type = MediaType::KUAISHOU;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_BAIDU :
            case FinanceLogic::FUND_MEDIA_TYPE_BAIDU_CACHE :
            case FinanceLogic::FUND_MEDIA_TYPE_BAIDU_CASH :
                $this->media_type = MediaType::BAIDU;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_BAIDU_SEARCH :
            case FinanceLogic::FUND_MEDIA_TYPE_BAIDU_SEARCH_CACHE :
            case FinanceLogic::FUND_MEDIA_TYPE_BAIDU_SEARCH_CASH :
                $this->media_type = MediaType::BAIDU_SEARCH;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_WECHAT :
                $this->media_type = MediaType::MP;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_OPPO_CASH :
                $this->media_type = MediaType::OPPO;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_360_SEARCH :
                $this->media_type = MediaType::QIHU_SEARCH;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_360_BID :
                $this->media_type = MediaType::QIHU_JINGJIA;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_QIANCHUAN :
                $this->media_type = MediaType::QIANCHUAN;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_FACEBOOK :
                $this->media_type = MediaType::FACEBOOK;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_GOOGLE :
                $this->media_type = MediaType::GOOGLE_ADWORDS;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_TIKTOK :
                $this->media_type = MediaType::TIKTOK;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_ASA :
                $this->media_type = MediaType::ASA;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_IQIYI :
                $this->media_type = MediaType::IQIYI;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_WEIBO :
                $this->media_type = MediaType::WEIBO;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_UC :
                $this->media_type = MediaType::UC;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_TAPTAP :
                $this->media_type = MediaType::TAPTAP;
                break;
            case FinanceLogic::FUND_MEDIA_TYPE_HUAWEI_ADS :
                $this->media_type = MediaType::HUAWEI_JINGHONG;
                break;
            default:
                $this->media_type = 0;
        }

        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 50;
        }

        if ($this->platform) {
            $this->platform = array_map(function ($platform) {return array_search($platform, EnvConfig::PLATFORM_MAP);}, $this->platform);
        }

        foreach (['contract_game_name', 'root_game_id', 'main_game_id', 'game_id'] as $item) {
            if ($this->$item) {
                $this->game_filter[] = SqlParser::get([
                    'column' => "platform-{$item}",
                    'condition' => 'in',
                    'value' => $this->$item
                ], '', 'game');
            }
        }
    }

    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->agent_permission = $user_permission['agent_permission'];
    }
}
