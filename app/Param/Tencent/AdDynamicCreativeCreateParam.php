<?php

/**
 * 腾讯创建广告动态创意
 * User: 张中昊
 * Date: 2020-07-13
 */

namespace App\Param\Tencent;

use App\Param\AbstractParam;

/**
 * Class AdDynamicCreativeCreateParam
 * @package App\Param\Tencent
 */
class AdDynamicCreativeCreateParam extends AbstractParam
{
    /* 必填 */
    public $account_id;
    public $access_token;
    public $dynamic_creative_name;
    public $dynamic_creative_template_id;
    public $dynamic_creative_elements = [];
    public $campaign_type = 'CAMPAIGN_TYPE_NORMAL';
    public $promoted_object_type = '';
    public $page_type = '';

    /* 选填 */
    public $link_name_type;
    public $link_page_type;
    public $link_page_spec;
    public $profile_id = '';
    public $site_set = '';
    public $automatic_site_enabled;
    public $promoted_object_id = '';
    public $page_spec = [];
    public $feeds_video_comment_switch = '';
    public $enable_breakthrough_siteset;
    public $dynamic_creative_type;
    public $program_creative_info;
    public $app_gift_pack_code;
    public $head_click_type;
    public $head_click_spec;

    public $impression_tracking_url = '';

    public $click_tracking_url = '';

    public $smart_delivery_platform;
    public $campaign_id;

    public $dynamic_creative_group_used;

    public function toBody()
    {
        $this->campaign_type = 'CAMPAIGN_TYPE_SEARCH';
        $this->dynamic_creative_type = 'DYNAMIC_CREATIVE_TYPE_COMPONENT';
        return $this->toParam();
    }
}
