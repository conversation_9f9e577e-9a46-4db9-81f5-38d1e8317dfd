<?php

/**
 * 腾讯创建项目
 * Date: 2020-07-13
 */

namespace App\Param\Tencent;

use App\Param\AbstractParam;
use App\Param\ADServing\Tencent\ADTargetingContentParam;

/**
 * Class AdCreateParam
 * @package App\Param\Tencent
 */
class ProjectParam extends AbstractParam
{
    public $account_id;
    public $app_android_channel_package_id;
    public $automatic_site_enabled;
    public $begin_date;
    public $bid_amount;
    public $bid_mode;
    public $bid_scene;
    public $bid_strategy;
    public $project_name;
    public $configured_status;
    public $conversion_id;
    public $daily_budget;
    public $deep_conversion_behavior_bid; // 包含字段 deep_conversion_type deep_conversion_behavior_spec
    public $deep_conversion_worth_advanced_rate;
    public $deep_conversion_worth_rate;
    public $dynamic_creative_id;
    public $end_date;
    public $first_day_begin_time;
    public $flow_optimization_enabled;
    public $marketing_scene;
    public $optimization_goal;
    public $promoted_object_id;
    public $promoted_object_type;
    public $scene_spec;
    public $site_set;
    public $smart_delivery_platform;
    public $speed_mode;
    public $targeting;
    public $time_series;
    public $total_budget;

    public function setTargeting(ADTargetingContentParam $targeting)
    {
        $data = $targeting->toArray();
        $data['user_os'] = array_merge($data['user_os_ios'], $data['user_os_android']);

        // 流量包
        if ($targeting->flow_package) {
            $this->scene_spec['union_position_package'] = $targeting->flow_package;
        }
        if ($targeting->exclude_flow_package) {
            $this->scene_spec['exclude_union_position_package'] = $targeting->exclude_flow_package;
        }

        // 小游戏
        if (in_array($this->promoted_object_type, ['PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT', 'PROMOTED_OBJECT_TYPE_APP_IOS'])) {
            $data['app_install_status'] = '';
        }

        if ($targeting->device_brand_model_type == 'include' && $targeting->device_brand_model_list) {
            $data['device_brand_model']['included_list'] = array_column($targeting->device_brand_model_list, 'id');
        } elseif ($targeting->device_brand_model_type == 'exclude' && $targeting->device_brand_model_list) {
            $data['device_brand_model']['excluded_list'] = array_column($targeting->device_brand_model_list, 'id');
        }

        if ($targeting->wechat_ad_behavior_status == 'include' && $targeting->wechat_ad_behavior_behavior) {
            $data['wechat_ad_behavior']['actions'] = $targeting->wechat_ad_behavior_behavior;
        } elseif ($targeting->wechat_ad_behavior_status == 'exclude' && $targeting->wechat_ad_behavior_behavior) {
            $data['wechat_ad_behavior']['excluded_actions'] = $targeting->wechat_ad_behavior_behavior;
        }

        $this->targeting = (object)(new AudiencePackageCreateParam($data))->toBody();
    }
}
