<?php
/**
 * 腾讯创建广告组
 * User: hejin<PERSON>ong
 * Date: 2020-07-11
 */

namespace App\Param\Tencent;

use App\Exception\AppException;
use App\Model\HttpModel\Tencent\Campaign\CampaignModel;
use App\Param\AbstractParam;

/**
 * Class CampaignCreateParam
 * @package App\Param\Tencent
 */
class CampaignCreateParam extends AbstractParam
{
    public $account_id;
    public $access_token;
    public $campaign_id;
    public $campaign_name;
    public $campaign_type;
    public $promoted_object_type;
    public $daily_budget;
    public $configured_status;
    public $speed_mode;

    protected function paramHook()
    {
        if (empty($this->account_id) || empty($this->access_token)) {
            throw new AppException('缺少必要参数.');
        }

        $len = mb_strlen($this->campaign_name);
        if ($len < 1 || $len > 60) {
            throw new AppException('推广计划名长度最小1个等宽字符，长度最大60等宽字符');
        }

        // 写死 目前只有搜索广告在用
        $this->campaign_type = 'CAMPAIGN_TYPE_SEARCH';

//        if (!in_array($this->campaign_type, CampaignModel::VALID_TYPES)) {
//            throw new AppException('推广计划类型不合法。');
//        }

//        if (!in_array($this->promoted_object_type, CampaignModel::VALID_OBJECT_TYPES[$this->campaign_type])) {
//            throw new AppException('推广目标类型不合法。');
//        }

        // 元 转 分
        if ($this->daily_budget) {
            $this->daily_budget = $this->daily_budget * 100;
            if ($this->daily_budget < 5000 || $this->daily_budget > **********) {
                throw new AppException('日预算需介于50元 - 40,000,000元之间');
            }
        }

        if ($this->configured_status) {
            if (!in_array($this->configured_status, CampaignModel::VALID_STATUS)) {
                throw new AppException('客户设置的状态不合法');
            }
        }

        if ($this->speed_mode) {
            if (!in_array($this->speed_mode, CampaignModel::VALID_SPEED_MODE)) {
                throw new AppException('投放速度模式不合法');
            }
        }
    }

    public function toRequestBody()
    {
        $body = [
            "account_id" => $this->account_id,
            "access_token" => $this->access_token
        ];

        if ($this->campaign_id) {
            $body['campaign_id'] = $this->campaign_id;
        }

        if ($this->campaign_name) {
            $body['campaign_name'] = $this->campaign_name;
        }

        if ($this->campaign_type) {
            $body['campaign_type'] = $this->campaign_type;
        }

        if ($this->promoted_object_type) {
            $body['promoted_object_type'] = $this->promoted_object_type;
        }

        if ($this->daily_budget) {
            $body['daily_budget'] = $this->daily_budget;
        } else {
            $body['daily_budget'] = 0;
        }

        if ($this->configured_status) {
            $body['configured_status'] = $this->configured_status;
        }

        if ($this->speed_mode) {
            $body['speed_mode'] = $this->speed_mode;
        }

        return $body;
    }
}