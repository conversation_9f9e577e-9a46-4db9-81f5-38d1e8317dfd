<?php

/**
 * 腾讯创建广告动态创意
 * User: 张中昊
 * Date: 2020-07-13
 */

namespace App\Param\Tencent\V3;

use App\Param\AbstractParam;

/**
 * Class AdDynamicCreativeCreateParam
 * @package App\Param\Tencent
 */
class AdDynamicCreativeCreateParam extends AbstractParam
{
    public $material_derive_id;
    public $auto_derived_program_creative_switch;
    /* 必填 */
    public $account_id;
    public $configured_status;
    public $adgroup_id;
    public $access_token;
    public $delivery_mode;
    public $dynamic_creative_type;
    public $dynamic_creative_name;
    public $creative_template_id;
    public $creative_components = [];
//    public $page_type = '';

    /* 选填 */
    public $link_name_type;
    public $link_page_type;
    public $link_page_spec;
    public $profile_id = '';
    public $site_set = '';
    public $automatic_site_enabled;
    public $page_spec = [];
    public $feeds_video_comment_switch = '';
    public $enable_breakthrough_siteset;
    public $program_creative_info;
    public $app_gift_pack_code;
    public $head_click_type;
    public $head_click_spec;

    public $impression_tracking_url = '';

    public $click_tracking_url = '';

    public $smart_delivery_platform;

    public $dynamic_creative_group_used;

    public function toBody()
    {
        return $this->toParam();
    }
}
