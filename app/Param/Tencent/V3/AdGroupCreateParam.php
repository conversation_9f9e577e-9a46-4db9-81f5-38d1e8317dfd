<?php

/**
 * 腾讯创建广告计划
 * User: hejingsong
 * Date: 2020-07-11
 */

namespace App\Param\Tencent\V3;

use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\AdGroup\AdGroupModel;
use App\Param\AbstractParam;
use App\Param\ADServing\Tencent\ADTargetingContentParam;
use App\Param\Tencent\AudiencePackageCreateParam;
use DateTime;

/**
 * Class AdGroupCreateParam
 * @package App\Param\Tencent
 * 传入参数金额单位为元
 */
class AdGroupCreateParam extends AbstractParam
{
    public $marketing_goal;

    public $marketing_sub_goal;
    public $marketing_target_id;
    public $marketing_carrier_type;

    public $marketing_carrier_detail;

    public $marketing_asset_outer_spec;

    public $marketing_asset_id;

    public $marketing_target_attachment;


    public $account_id;
    public $access_token;
    public $adgroup_name;
    public $begin_date;
    public $end_date;
//    public $billing_event 不能与$bid_mode一起;
    public $bid_mode;
    public $bid_amount;
    public $marketing_scene = 'DEFAULT';
    // 出价类型
    public $smart_bid_type;
    public $time_series;

    public $optimization_goal;
    public $site_set;
    public $daily_budget;
    public $promoted_object_id;
//    public $app_android_channel_package_id;
    public $targeting;
    public $scene_spec;
    public $configured_status;
    public $customized_category;
    public $flow_optimization_enabled;
    public $dynamic_ad_spec;
    public $user_action_sets;
    public $additional_user_action_sets;
    public $dynamic_creative_id;
    public $is_rewarded_video_ad;
    public $bid_strategy;
    public $cold_start_audience;
    public $auto_audience;
    public $expand_enabled;
    public $expand_targeting;
    public $deep_conversion_spec;
    public $deep_optimization_action_type;
    public $conversion_id;
    public $deep_conversion_behavior_bid;
    public $deep_conversion_worth_rate;
    public $deep_conversion_worth_advanced_rate;
    public $bid_adjustment;
    public $automatic_site_enabled;

    public $first_day_begin_time;
    public $adgroup_id;

    public $auto_acquisition_enabled;
    public $auto_acquisition_budget;
    public $bid_scene;
    public $search_expand_targeting_switch;
    public $ecom_pkam_switch;

    public $live_video_mode;

    public $live_video_sub_mode;

    public $priority_site_set;

    public $exploration_strategy;

    public $cost_constraint_scene;

    public $custom_cost_cap;

    public $custom_cost_roi_cap;

    public $smart_delivery_platform;

    public $smart_delivery_scene;

    public $smart_delivery_scene_spec;

    public $auto_derived_creative_enabled;

    public $pc_scene;

    public function __construct(array $property = [])
    {
        parent::__construct($property);
    }

    /**
     * @throws \Exception
     */
    protected function paramHook()
    {
//        foreach (self::REQUIRED_FIELDS as $field => $e_msg) {
//            if (empty($this->$field)) {
//                throw new AppException($e_msg);
//            }
//        }

        $this->bid_amount = (float)strval(((float)$this->bid_amount) * 100);

        $this->deep_conversion_behavior_bid && $this->deep_conversion_behavior_bid = $this->deep_conversion_behavior_bid * 100;
//        $this->begin_date = date("Y-m-d", $this->begin_date);

        $len = mb_strlen($this->adgroup_name);
        if ($len < 1 || $len > 180) {
            throw new AppException('广告组名长度最小 1 字节，长度最大 180 字节');
        }

        if (strtotime($this->begin_date) < strtotime(date('Y-m-d'))) {
            $this->begin_date = date('Y-m-d');
        }

        if (!$this->end_date) {
            $this->end_date = date('Y-m-d', strtotime($this->begin_date . ' +1 year'));
        } else {

            if (floor((strtotime($this->end_date) - strtotime($this->begin_date)) / (60 * 60 * 24)) > 365) {
                $this->end_date = date('Y-m-d', strtotime($this->begin_date . ' +1 year') - 1);
            }
//            $date1 = new DateTime($this->begin_date);
//            $date2 = new DateTime($this->end_date);
//
//            $yearDiff = $date2->format('Y') - $date1->format('Y');
//
//            if ($yearDiff >= 1) {
//                $this->end_date = date('Y-m-d', strtotime($this->begin_date . ' +1 year') - 1);
//            }
        }

        if ($this->optimization_goal && !in_array($this->optimization_goal, AdGroupModel::OPTIMIZATION_GOAL_LIST)) {
            throw new AppException("广告优化目标类型不合法");
        }

        if (strlen($this->time_series) != 336) {
            throw new AppException("投放时间段不合法");
        }

//        if ($this->site_set && !in_array($this->site_set, AdGroupModel::SITE_SET_LIST)) {
//            throw new AppException("投放站点集合不合法");
//        }

        if ($this->daily_budget) {
            $this->daily_budget *= 100;
        }

        if ($this->configured_status && !in_array($this->configured_status, AdGroupModel::CONFIGURED_STATUS_LIST)) {
            throw new AppException("客户设置的状态不合法");
        }

        if ($this->customized_category) {
            $len = mb_strlen($this->customized_category);
            if ($len > 200) {
                throw new AppException("自定义分类长度最小 0 字节，长度最大 200 字节");
            }
        }

        if (isset($this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount']) &&
            $this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'] > 0) {
            $this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'] = $this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'] * 100;
        }

        if (isset($this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']) &&
            $this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'] > 0) {
            $this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'] = (double)$this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'];
        }

        // 自动出价类型不可传入bid_amount
        if ($this->smart_bid_type == 'SMART_BID_TYPE_SYSTEMATIC') {
            $this->bid_amount = [];
        }

    }

    public function setSmartDeliveryScene($smart_delivery_platform, $smart_delivery_scene_spec)
    {
        $this->smart_delivery_platform = $smart_delivery_platform;
        $this->smart_delivery_scene = 'SMART_DELIVERY_SCENE_STANDARD';
        $this->smart_delivery_scene_spec = $smart_delivery_scene_spec;
    }

    public function setTargeting(ADTargetingContentParam $targeting, $exp = [])
    {
        $data = $targeting->toArray();
        $data['user_os'] = array_merge($data['user_os_ios'], $data['user_os_android'], $data['user_os_other']);

        if ($targeting->excluded_os) {
            $data['excluded_os'] = $data['user_os'];
            $data['user_os'] = [];
        }

        // 流量包
        if ($targeting->flow_package) {
            $this->scene_spec['union_position_package'] = array_map(function ($el) {
                return (int)$el;
            }, $targeting->flow_package);
        }
        if ($targeting->exclude_flow_package) {
            $this->scene_spec['exclude_union_position_package'] = array_map(function ($el) {
                return (int)$el;
            }, $targeting->exclude_flow_package);
        }

        // 小游戏
        if (in_array($this->marketing_carrier_type, ['MARKETING_CARRIER_TYPE_WECHAT_MINI_GAME', 'MARKETING_CARRIER_TYPE_APP_IOS'])) {
            $data['app_install_status'] = '';
        }

        if ($targeting->device_brand_model_type == 'include' && $targeting->device_brand_model_list) {
            $data['device_brand_model']['included_list'] = array_column($targeting->device_brand_model_list, 'id');
        } elseif ($targeting->device_brand_model_type == 'exclude' && $targeting->device_brand_model_list) {
            $data['device_brand_model']['excluded_list'] = array_column($targeting->device_brand_model_list, 'id');
        }

        if ($targeting->wechat_ad_behavior_status == 'include' && $targeting->wechat_ad_behavior_behavior) {
            $data['wechat_ad_behavior']['actions'] = $targeting->wechat_ad_behavior_behavior;
        } elseif ($targeting->wechat_ad_behavior_status == 'exclude' && $targeting->wechat_ad_behavior_behavior) {
            $data['wechat_ad_behavior']['excluded_actions'] = $targeting->wechat_ad_behavior_behavior;
        }

        if ($exp) {
            $data = array_merge($data, $exp);
        }

        $this->targeting = (new AudiencePackageCreateParam($data))->toBody();
    }

    /**
     * @return array
     */
    public function toRequestBody()
    {
        $body = $this->toParam();

        if ($body['custom_cost_cap'] ?? false) {
            $body['custom_cost_cap'] = (int)$body['custom_cost_cap'];
        }

        if ($body['custom_cost_roi_cap'] ?? false) {
            $body['custom_cost_roi_cap'] = (double)$body['custom_cost_roi_cap'];
        }

        if ($this->auto_acquisition_enabled) {
            $body['auto_acquisition_enabled'] = $this->auto_acquisition_enabled;
            $body['auto_acquisition_budget'] = $this->auto_acquisition_budget * 100;
        } else {
            unset($body['auto_acquisition_budget']);
        };
        if (!$this->deep_conversion_spec) {
            unset($body['deep_conversion_spec']);
        }

        if (!$this->scene_spec) {
            unset($body['scene_spec']);
        } else {
            unset($body['scene_spec']['display_scene_none']);
            if (isset($body['scene_spec']['display_scene']) && !$body['scene_spec']['display_scene']) {
                unset($body['scene_spec']['display_scene']);
            }
        }

        isset($body['user_action_sets']) && $body['user_action_sets'] = [$body['user_action_sets']];
        !$this->targeting && $body['targeting'] = [];

        if ($this->optimization_goal == 'OPTIMIZATIONGOAL_FIRST_PURCHASE') {
            unset($body['deep_conversion_spec']);
        }

        if (!isset($body['time_series']) || !$body['time_series'] || $body['time_series'] == str_pad('', 336, 0)) {
            $body['time_series'] = str_pad('', 336, 1);
        }

        !isset($body['end_date']) && $body['end_date'] = '';

        $body['account_id'] = (int)$body['account_id'];
        $body['expand_enabled'] = $body['expand_enabled'] === 'false' ? false : true;
        if ($body['expand_enabled']) {
            if (!isset($body['expand_targeting']) || !$body['expand_targeting']) {
                $body['expand_targeting'] = [];
            }
        } else {
            unset($body['expand_targeting']);
            unset($body['cold_start_audience']);
        }

        if (in_array('SITE_SET_MOMENTS', $this->site_set) || in_array('SITE_SET_WECHAT', $this->site_set)) {
            unset($body['targeting']['gamer_consumption_ability']);
            unset($body['targeting']['consumption_status']);
            unset($body['targeting']['consumption_type']);
            isset($body['targeting']['geo_location']) && $body['targeting']['geo_location']['location_types'] = ['LIVE_IN'];
            if ($body['scene_spec']['wechat_position']) {
                $body['scene_spec'] = ['wechat_position' => array_map(function ($e) {
                    return (int)$e;
                }, $body['scene_spec']['wechat_position'])];
            } else {
                if (!in_array(TencentEum::SITE_SET_MOBILE_UNION, $this->site_set)) {
                    unset($body['scene_spec']);
                }
            }
        }

        if (!$body['scene_spec']) {
            unset($body['scene_spec']);
        }

        if (!$body['targeting'] && in_array('SITE_SET_MOMENTS', $this->site_set)) {
            $body['targeting'] = json_decode("{}", true);
        }
        $body['targeting'] = (object)($body['targeting']);
        return $body;
    }
}
