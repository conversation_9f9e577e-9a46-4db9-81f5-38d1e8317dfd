<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/4/1
 * Time: 14:17
 */

namespace App\Param\Tencent\V3;

use App\Constant\TencentEum;
use App\Param\AbstractParam;

class CreativeTemplateParam extends AbstractParam
{
    public $account_id = '';
    public $marketing_goal = '';
    public $marketing_sub_goal = '';
    public $marketing_target_type = '';
    public $marketing_carrier_type = '';
    public $automatic_site_enabled = '';
    public $site_set = [];
    public $delivery_mode = '';
    public $dynamic_creative_type = '';
    public $creative_template_id = '';

    public function paramHook()
    {
        if ($this->is_dynamic_creative === 'false') {
            $this->dynamic_creative_type = '';
        } else {
            if ($this->dynamic_creative_type == TencentEum::DYNAMIC_CREATIVE_TYPE_PROGRAM) {
                $this->adcreative_template_id = '';
            }
        }
    }

    public function toRequestBody()
    {
        $body = $this->toParam();
        if (($body['automatic_site_enabled'] ?? 'false') == 'false') {
            unset($body['automatic_site_enabled']);
        }
//        $body['marketing_goal'] = 'MARKETING_GOAL_USER_GROWTH';
//        $body['marketing_sub_goal'] = 'MARKETING_SUB_GOAL_MINI_GAME_NEW_CUSTOMER_GROWTH';
//        $body['marketing_target_type'] = 'MARKETING_TARGET_TYPE_WECHAT_MINI_GAME';
//        $body['marketing_carrier_type'] = 'MARKETING_CARRIER_TYPE_WECHAT_MINI_GAME';
//        $body['delivery_mode'] = 'DELIVERY_MODE_COMPONENT';
//        $body['dynamic_creative_type'] = 'DYNAMIC_CREATIVE_TYPE_PROGRAM';
//        $body['creative_template_id'] = '1701';
//        $body['automatic_site_enabled'] = 'true';
//        $body['site_set'] = json_encode($body['site_set']);
//        unset($body['adcreative_template_id']);
//        unset($body['promoted_object_type']);
//        unset($body['is_dynamic_creative']);
//        unset($body['fields']);
        return $body;
    }
}
