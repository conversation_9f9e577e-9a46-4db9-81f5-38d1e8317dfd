<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/02/24
 * Time: 14:08
 */

namespace App\Param\Tencent;

use App\Param\AbstractParam;

class AudiencePackageCreateParam extends AbstractParam
{
    /**
     * ----------------------------age
     * @var array $age 年龄{min:int,max:int}
     */
    public $age;

    /**
     * ----------------------------gender
     * @var array $gender 性别
     */
    public $gender;

    /**
     * @var array $education 用户学历
     */
    public $education;

    /**
     * @var array $marital_status 婚恋育儿状态
     */
    public $marital_status;

    /**
     * @var array $working_status 工作状态
     */
    public $working_status;

    /**
     * @var object $geo_location 地理位置定向 {location_types:array,regions:array}
     */
    public $geo_location;

    /**
     * @var array $excluded_os 排除操作系统
     */
    public $excluded_os;

    /**
     * @var array $user_os 操作系统
     */
    public $user_os;

    /**
     * @var array $new_device 新设备，最近三个月第一次使用该设备的用户,可选{ IOS, ANDROID }
     */
    public $new_device;

    /**
     * @var array $device_price 设备价格 { PRICE_1500_LESS, PRICE_1500_2500, PRICE_2500_3500, PRICE_3500_4500, PRICE_4500_MORE }
     */
    public $device_price;

    /**
     * @var array $excluded_converted_audience 排除已转化
     */
    public $excluded_converted_audience;

    /**
     * @var object $device_brand_model {included_list:array,excluded_list:array}
     */
    public $device_brand_model;

    /**
     * @var array $network_type 联网方式
     */
    public $network_type;

    /**
     * @var array $network_operator 移动运营商
     */
    public $network_operator;

    /**
     * @var array $network_scene 上网场景
     */
    public $network_scene;

    /**
     * @var array $dressing_index 穿衣指数
     */
    public $dressing_index;

    /**
     * @var array $uv_index 紫外线指数
     */
    public $uv_index;

    /**
     * @var array $makeup_index 化妆指数
     */
    public $makeup_index;

    /**
     * @var array $climate 气象
     */
    public $climate;

    /**
     * @var object $temperature 温度{min:int,max:int}
     */
    public $temperature;

    /**
     * @var array $air_quality_index 空气质量指数
     */
    public $air_quality_index;

    /**
     * @var array $app_install_status 应用安装
     */
    public $app_install_status;

    /**
     * @var array $consumption_status 消费水平
     */
    public $consumption_status;

//    /**
//     * @var array $gamer_consumption_ability 游戏消费能力
//     */
//    public $gamer_consumption_ability;

    /**
     * @var array $game_consumption_level 游戏消费能力
     */
    public $game_consumption_level;

    /**
     * @var object $residential_community_price 居住社区价格 {min:int,max:int}
     */
    public $residential_community_price;

    /**
     * @var array $financial_situation 财产状态
     */
    public $financial_situation;

    /**
     * @var array $consumption_type 消费类型
     */
    public $consumption_type;

    /**
     * @var object $behavior_or_interest
     * {
     *  interest:{
     *      targeting_tags:array,
     *      category_id_list:array,
     *      keyword_list:array
     *  },
     *  behavior:{
     *      targeting_tags:array,
     *      category_id_list:array,
     *      keyword_list:array,
     *      scene:array,
     *      time_window:string,
     *      intensity:array
     *  },
     *  intention:{
     *      targeting_tags:array
     *  },
     * }
     */
    public $behavior_or_interest;

    /**
     * ----------------------------retargeting_tags
     * @var array $custom_audience 自定义定向用户群
     */
    public $custom_audience;

    /**
     * ----------------------------retargeting_tags_exclude
     * @var array $excluded_custom_audience 自定义排除用户群
     */
    public $excluded_custom_audience;

    /**
     * ----------------------------mobile_union_category
     *
     * @var array<int> mobile_union_category 移动媒体类型定向
     */
    public $mobile_union_category;

    /**
     * ----------------------------mini_game_qq_status
     *
     * @var array<string> mini_game_qq_status QQ 小游戏使用定向
     */
    public $mini_game_qq_status;

    /**
     * 微信再营销
     * @var array
     */
    public $wechat_ad_behavior;


    public function paramHook()
    {
//        if (
//            $this->age['min'] < AbstractTargetingsModel::MIN_AGE_MIN
//            || $this->age['min'] > AbstractTargetingsModel::MIN_AGE_MAX
//            || in_array($this->age['min'], AbstractTargetingsModel::INVALID_MIN_AGE)
//        ) {
//            $this->age['min'] = 14;
//            throw new AppException("年龄最小值限制。最小值 14，最大值 66（其中 15、16、17 不可以使用）");
//        }
//        if (
//            $this->age['max'] < AbstractTargetingsModel::MAX_AGE_MIN
//            || $this->age['max'] > AbstractTargetingsModel::MAX_AGE_MIN
//        ) {
//            $this->age['max'] = 66;
//            throw new AppException("年龄最大值限制。最小值 18，最大值 66");
//        }

//        if ($this->gender) {
//            $this->gender = [$this->gender];
//        }
//
//        if ($this->education) {
//            $len = count($this->education);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("用户学历：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->marital_status) {
//            $len = count($this->marital_status);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("婚恋育儿状态：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->working_status) {
//            $len = count($this->working_status);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("工作状态：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->user_os) {
//            $len = count($this->user_os);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("操作系统定向：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->new_device) {
//            $len = count($this->new_device);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("新设备：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->device_price) {
//            $len = count($this->device_price);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("设备价格定向：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->network_type) {
//            $len = count($this->network_type);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("联网方式定向：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->network_operator) {
//            $len = count($this->network_operator);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("移动运营商定向：最小长度 1，最大长度 100");
//            }
//        }
//
//        if ($this->network_scene) {
//            $len = count($this->network_scene);
//            if ($len < 1 || $len > 100) {
//                throw new AppException("上网场景：最小长度 1，最大长度 100");
//            }
//        }
    }

    /**
     * @return array
     */
    public function toBody()
    {
        $body = [];
        //组装所有定向包字段的数组
        $paramsArray = get_object_vars($this);
        foreach ($paramsArray as $key => $value) {
            if ($value) {
                $body[$key] = $value;
            }
        }

        if ($body['excluded_custom_audience'] ?? '') {
            $body['excluded_custom_audience'] = array_map(function ($el) {
                return (int)$el;
            }, $body['excluded_custom_audience']);
        }

        if ($body['custom_audience'] ?? '') {
            $body['custom_audience'] = array_map(function ($el) {
                return (int)$el;
            }, $body['custom_audience']);
        }

        if (!isset($body['age']['min']) || !isset($body['age']['max']) || !$body['age']['min'] || !$body['age']['max']) {
            unset($body['age']);
        }

//        if (!isset($body['gamer_consumption_ability']['min']) ||
//            !isset($body['gamer_consumption_ability']['max']) ||
//            !$body['gamer_consumption_ability']['min'] ||
//            !$body['gamer_consumption_ability']['max']) {
//            unset($body['gamer_consumption_ability']);
//        }

        if (!isset($body['geo_location']['regions']) || !$body['geo_location']['regions']) {
            unset($body['geo_location']);
//            $time = time();
//            if ($time >= 1602669600) {
//                $body['geo_location']['regions'] = LimitADRegionTask::TENCENT_COUNTRY_EXCEPT_GUANGDONG;
//                $body['geo_location']['location_types'] = ['LIVE_IN'];
//            }
        }
//
//        if ($body['geo_location']['regions'] && in_array('1156', $body['geo_location']['regions'])) {
//            $time = time();
//            if ($time >= 1602669600) {
//                $body['geo_location']['regions'] = LimitADRegionTask::TENCENT_COUNTRY_EXCEPT_GUANGDONG;
//            }
//        }
//
//        if ($body['geo_location']['regions']) {
//            $time = time();
//            if ($time >= 1602669600) {
//                $body['geo_location']['regions'] = array_diff($body['geo_location']['regions'], [LimitADRegionTask::TENCENT_CHOSEN_GUANGDONG]);
//                $body['geo_location']['regions'] = array_diff($body['geo_location']['regions'], LimitADRegionTask::TENCENT_GUANGDONG_CITY);
//            }
//        }

        if (!$this->gender) {
            unset($body['gender']);
        }

        if (!$this->app_install_status) {
            unset($body['app_install_status']);
        }

        unset($body['age']['age_none']);
        unset($body['gamer_consumption_ability']['gamer_consumption_ability_none']);
        unset($body['geo_location']['regions_map']);
        unset($body['geo_location']['regions_none']);
        unset($body['behavior_or_interest']['interest']['category_id_list_map']);
        unset($body['behavior_or_interest']['behavior']['category_id_list_map']);
        unset($body['behavior_or_interest']['intention']['targeting_tags_map']);

//        if ($body['geo_location']['regions']) {
//            $time = time();
//            if ($time >= 1602669600) {
//                $not_need_city = [
//                    5101,
//                    510100,
//                    510104,
//                    510105,
//                    510106,
//                    510107,
//                    510108,
//                    510112,
//                    510113,
//                    510114,
//                    510115,
//                    510116,
//                    510117,
//                    510121,
//                    510122,
//                    510124,
//                    510129,
//                    510131,
//                    510132,
//                    510181,
//                    510182,
//                    510183,
//                    510184,
//                    510185
//                ];
//                $body['geo_location']['regions'] = array_diff($body['geo_location']['regions'], $not_need_city);
//                if (!$body['geo_location']['regions']) {
//                    unset($body['geo_location']);
//                }
//            }
//
//        }

        if (isset($body['behavior_or_interest']['interest']['category_id_list'])) {
            if (count($body['behavior_or_interest']['interest']['category_id_list']) <= 0) {
                unset($body['behavior_or_interest']['interest']['category_id_list']);
            } else {
                $body['behavior_or_interest']['interest']['category_id_list'] = array_map('intval', $body['behavior_or_interest']['interest']['category_id_list']);
            }
        }
        if (isset($body['behavior_or_interest']['interest']['keyword_list']) && count($body['behavior_or_interest']['interest']['keyword_list']) <= 0) {
            unset($body['behavior_or_interest']['interest']['keyword_list']);
        }
        if (isset($body['behavior_or_interest']['behavior']['category_id_list'])) {
            if(count($body['behavior_or_interest']['behavior']['category_id_list']) <= 0) {
                unset($body['behavior_or_interest']['behavior']['category_id_list']);
            } else {
                $body['behavior_or_interest']['behavior']['category_id_list'] = array_map('intval', $body['behavior_or_interest']['behavior']['category_id_list']);
            }
        }
        if (isset($body['behavior_or_interest']['behavior']['keyword_list']) && count($body['behavior_or_interest']['behavior']['keyword_list']) <= 0) {
            unset($body['behavior_or_interest']['behavior']['keyword_list']);
        }
        if (!isset($body['behavior_or_interest']['behavior']['category_id_list']) && !isset($body['behavior_or_interest']['behavior']['keyword_list'])) {
            $body['behavior_or_interest']['behavior'] = [];
        }
        if (!isset($body['behavior_or_interest']['behavior']) || !$body['behavior_or_interest']['behavior']) {
            unset($body['behavior_or_interest']['behavior']);
        } else {
            $body['behavior_or_interest']['behavior']['intensity'] = [$body['behavior_or_interest']['behavior']['intensity']];
        }
        if (!isset($body['behavior_or_interest']['interest']) || !$body['behavior_or_interest']['interest']) {
            unset($body['behavior_or_interest']['interest']);
        }
        if (!isset($body['behavior_or_interest']['intention']) || (isset($body['behavior_or_interest']['intention']['targeting_tags']) && count($body['behavior_or_interest']['intention']['targeting_tags']) <= 0)) {
            unset($body['behavior_or_interest']['intention']);
        }
        if ((isset($body['behavior_or_interest']) && !$body['behavior_or_interest'])) {
            unset($body['behavior_or_interest']);
        }

        isset($body['age']) && $body['age'] = [$body['age']];
        isset($body['gender']) && $body['gender'] = [$body['gender']];
        isset($body['gamer_consumption_ability']) && $body['gamer_consumption_ability'] = [$body['gamer_consumption_ability']];
        isset($body['app_install_status']) && $body['app_install_status'] = [$body['app_install_status']];
        isset($body['behavior_or_interest']['behavior']) && $body['behavior_or_interest']['behavior'] = [$body['behavior_or_interest']['behavior']];

        if (isset($body['game_consumption_level']) && isset($body['gamer_consumption_ability'])) {
            unset($body['gamer_consumption_ability']);
        }
        return $body;
    }
}
