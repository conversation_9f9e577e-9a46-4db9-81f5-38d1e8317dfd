<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/4/1
 * Time: 14:17
 */

namespace App\Param\Tencent;

use App\Constant\TencentEum;
use App\Param\AbstractParam;

class AdCreativeTemplateParam extends AbstractParam
{
    public $account_id = '';
    public $site_set = [];
    public $promoted_object_type = '';
    public $automatic_site_enabled = '';
    public $is_dynamic_creative = '';
    public $adcreative_template_id = '';
    public $dynamic_creative_type = '';

    public $fields = [
        'adcreative_template_id',
        'adcreative_template_style',
        'adcreative_template_appellation',
        'adcreative_elements',
        'landing_page_config',
        'adcreative_attributes'
    ];

    public function paramHook()
    {
        if ($this->is_dynamic_creative === 'false') {
            $this->dynamic_creative_type = '';
        } else {
            if ($this->dynamic_creative_type == TencentEum::DYNAMIC_CREATIVE_TYPE_PROGRAM) {
                $this->adcreative_template_id = '';
            }
        }
    }

    public function toRequestBody()
    {
        $body = $this->toParam();
        if (($body['automatic_site_enabled'] ?? 'false') == 'false') {
            unset($body['automatic_site_enabled']);
        }
        return $body;
    }
}
