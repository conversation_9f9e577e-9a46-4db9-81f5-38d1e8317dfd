<?php

namespace App\Param\Tencent;

use App\Param\AbstractParam;


class ProgrammedAddParam extends AbstractParam
{
    public $account_id;
    public $access_token;
    public $ad_metadata;
    public $create_material_groups;
    public $auto_derived_program_creative_switch;
    public $standard_switch;

    public function addMaterials($materials)
    {
        $this->create_material_groups[] = ['materials' => $materials];
    }

    public function setAdMetaData(array $data)
    {
        $this->ad_metadata = $data;
    }

    public function toRequestBody()
    {
        $this->create_material_groups = json_encode(array_unique($this->create_material_groups));
        $this->ad_metadata = json_encode($this->ad_metadata);
        return $this->toParam();
    }
}