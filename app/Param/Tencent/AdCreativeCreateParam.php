<?php

/**
 * 腾讯创建广告创意
 * User: hejingsong
 * Date: 2020-07-13
 */

namespace App\Param\Tencent;

use App\Exception\AppException;
use App\Model\HttpModel\Tencent\Creative\CreativeModel;
use App\Param\AbstractParam;

/**
 * Class AdCreativeCreateParam
 * @package App\Param\Tencent
 */
class AdCreativeCreateParam extends AbstractParam
{
    /* 必填 */
    public $account_id;
    public $access_token;
    public $campaign_id;
    public $adcreative_name;
    public $adcreative_template_id;
    public $adcreative_elements;
    public $promoted_object_type;
    public $page_type;

    /* 选填 */
    public $automatic_site_enabled;
    public $site_set;
    public $page_spec;
    public $link_page_type;
    public $link_name_type;
    public $link_page_spec;
    public $qq_mini_game_tracking_query_string;
    public $deep_link_url;
    public $universal_link_url;
    public $promoted_object_id;
    public $profile_id;
    public $share_content_spec;
    public $dynamic_adcreative_spec;
    public $multi_share_optimization_enabled;
    public $component_id;
    public $category;
    public $label;
    public $union_market_switch;
    public $playable_page_material_id;
    public $video_end_page;
    public $feeds_video_comment_switch;
    public $webview_url;
    public $conversion_data_type;
    public $conversion_target_type;
    public $enable_breakthrough_siteset;
    public $head_click_type;
    public $head_click_spec;
    public $app_gift_pack_code;

    public $wechat_channels_tracking_spec;
    public $live_video_mode;
    public $live_video_sub_mode;

    const REQUIRED_FIELDS = [
        'account_id' => 'account_id不能为空',
        'access_token' => 'access_token不能为空',
        'campaign_id' => '推广计划 id不能为空',
        'adcreative_name' => '广告创意名称不能为空',
        'adcreative_template_id' => '创意规格 id不能为空',
        'adcreative_elements' => '创意元素不能为空',
        'promoted_object_type' => '推广目标类型d不能为空',
        'page_type' => '落地页类型不能为空'
    ];

    protected function paramHook()
    {
        foreach (self::REQUIRED_FIELDS as $field => $e_msg) {
            if (empty($this->$field)) {
                throw new AppException($e_msg);
            }
        }

        $name_len = mb_strlen($this->adcreative_name);
        if ($name_len > 180) {
            throw new AppException("广告创意名称：最小 1 字节，长度最大 180 字节");
        }
    }

    public function toBody()
    {
        $body = $this->toParam();
        $body['campaign_id'] = (int)$body['campaign_id'];
        $body['promoted_object_id'] = (string)$body['promoted_object_id'];
        return $body;
    }
}
