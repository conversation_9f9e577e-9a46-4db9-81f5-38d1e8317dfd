<?php

/**
 * 腾讯创建广告
 * User: hejingsong
 * Date: 2020-07-13
 */

namespace App\Param\Tencent;

use App\Exception\AppException;
use App\Model\HttpModel\Tencent\Ads\AdsModel;
use App\Param\AbstractParam;

/**
 * Class AdCreateParam
 * @package App\Param\Tencent
 */
class AdCreateParam extends AbstractParam
{
    /* 必填字段 */
    public $account_id;
    public $access_token;
    public $adgroup_id;
    public $adcreative_id;
    public $ad_name;

    /* 选填字段 */
    public $configured_status;
    public $impression_tracking_url;
    public $click_tracking_url;
    public $feeds_interaction_enabled;
    public $app_gift_pack_code;

    /* 修改时需要 */
    public $ad_id;

    protected function paramHook()
    {
        if (empty($this->account_id) || empty($this->access_token)) {
            throw new AppException('缺少必要参数.');
        }

        if ($this->ad_name) {
            $len = mb_strlen($this->ad_name);
            if ($len < 1 || $len > 60) {
                throw new AppException('广告名长度最小1个等宽字符，长度最大60等宽字符');
            }
        }

        if ($this->configured_status) {
            if (!in_array($this->configured_status, AdsModel::CONFIGURED_STATUS_LIST)) {
                throw new AppException('客户设置的状态不合法');
            }
        }

        if ($this->impression_tracking_url && strlen($this->impression_tracking_url) > 1024) {
            throw new AppException('曝光监控地址过长');
        }

        if ($this->click_tracking_url && strlen($this->click_tracking_url) > 1024) {
            throw new AppException('点击监控地址过长');
        }
    }

    public function toRequestBody()
    {
        $body = [];
        $arr = get_object_vars($this);
        foreach ($arr as $key => $value) {
            if ($value) {
                $body[$key] = $value;
            }
        }
        return $body;
    }
}
