<?php
/**
 * 基本报表筛选参数
 */

namespace App\Param;


use App\Constant\ADAnalysisSqlMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\CustomizedTargetModel;
use App\Utils\SqlParser;
use Common\EnvConfig;

class ADAnalysisReportFilterParam extends AbstractParam
{
    //数据库类型 1:ADB 2:ClickHouse
    public $database_type = '';
    public $media_type = 0;                  // 所选媒体
    public $dimension = [];                  // 处理后的维度
    public $target = [];                     // 所有指标
    public $filter = [];                     // 所有筛选
    public $filter_fields = [];
    public $calc = [];                       // 所有数值条件
    public $calc_fields = [];
    public $level = 0;                       //当前聚合等级
    public $calc_condition = '';             //数值条件之间关系
    public $order = [];                      //所排序条件
    public $cost_date;                       //消耗日期
    public $attribution_type;                //归因方式
    public $statistic_base_on = 2;           //统计方式，1：按子 2：按根 3:回流
    public $ad2_create_time;                 //创建日期
    public $ad3_create_time;                 //广告三级创建日期
    public $customized_target_ids = [];      //用户自定义指标id集合
    public $customized_target = [];          //用户自定义指标
    public $game_permission;                 //游戏权限
    public $agent_permission;                //渠道权限
    public $material_effect_permission;      //素材效果权限
    public $material_effect_visible_permission; //素材可见性权限
    public $user_type;                       //用户类型（区分市场和设计）
    public $user_list;                       //下属权限
    public $true_cost;                       //真实消耗
    public $live_apportion = 0;              //直播分摊
    public $official_apportion = 0;          //官网分摊
    public $natural_apportion = 0;           //自然量分摊
    public $limit = 50000;                   //每次获取条数
//    public $limit = 500;                     //每次获取条数测试用
    public $all_fields;
    //头条data_log sql map
    public $toutiao_data_log_map = [];
    //媒体类型为头条时外层select字段 sql map
    public $toutiao_main_compute_map = [];
    public $switch_select = [];
    //需要连接三级表聚合的字段
    public $need_join_third_ad_log_fields =  ['ad3_id', 'ad3_name', 'gdt_adcreative_template_id','gdt_is_playable', 'talent_account', 'overseas_ad3_name'];
    //需要ad_log连接data_log的字段
    public $need_ad_log_join_data_log = ['tt_inventory_subdivision', 'gdt_inventory_subdivision','gdt_xqxs_inventory_subdivision',
        'gdt_is_expand_targeting', 'tt_rit', 'agency_full_name', 'mini_game_os'];
    //需要以account_log来进行权限控制并right join ad_log的聚合字段
    public $need_permission_control_by_account_log_dimension = ['platform', 'agent_leader', 'account_id', 'account_name', 'company', 'agency_full_name'];
    //需要以account_log来进行权限控制并right join ad_log的自定义指标字段
    public $need_permission_control_by_account_log_target = ['balance', 'tt_valid_cash', 'tt_valid_grant',
        'tt_valid_return_goods_abs', 'tt_default_balance', 'tt_search_balance', 'tt_union_balance', 'tt_common_balance'];
    //rta_bind需要查询的字段
    //开关字段
    public $first_ad_switch;
    public $second_ad_switch;
    public $third_ad_switch;
    //头条各级广告开关
    public $toutiao_first_ad_switch = ['first_ad_Log.status as switch'];
    public $toutiao_second_ad_switch = ['ad_log.opt_status as switch'];
    public $toutiao_third_ad_switch = ['third_ad_log.opt_status as switch'];
    //腾讯各级广告开关
    public $tencent_first_ad_switch = ['first_ad_Log.configured_status as switch'];
    public $tencent_second_ad_switch = ['ad_log.configured_status as switch'];
    public $tencent_third_ad_switch = ['third_ad_log.configured_status as switch'];
    //快手各级广告开关
    public $kuaishou_first_ad_switch = ['first_ad_Log.campaign_put_status as switch'];
    public $kuaishou_second_ad_switch = ['ad_log.ad_put_status as switch'];
    public $kuaishou_third_ad_switch = ['third_ad_log.put_status as switch'];
    //百度各级广告开关
    public $baidu_first_ad_switch = ['first_ad_Log.campaign_pause as switch'];
    public $baidu_second_ad_switch = ['ad_log.ad_pause as switch'];
    public $baidu_third_ad_switch = ['third_ad_log.pause as switch'];
    //通用各级广告开关
//    public $common_first_ad_switch = ['first_ad_Log.ad1_status as switch'];
    public $common_first_ad_switch = ['first_ad_Log.ad1_configured_status as switch'];
    public $common_second_ad_switch = ['ad_log.opt_status as switch'];
    public $common_third_ad_switch = ['third_ad_log.opt_status as switch'];
    //需要三级维度表当左表的字段
//    public $third_class_dimension = ['ad3_id', 'ad3_name', 'count_ad3'];
    //排除不需要作为主SQL连接条件的字段
    public $exclude_join_conditions = ['cost_date', 'cost_week', 'tt_auto_extend_enabled'];
    //ad_log连表所需字段
//    public $ad_log_total_fields;
    public $ad_create_time_field;
    public $ad3_create_time_field;
    //join ad_log后要从ad_log中select的字段
    public $join_ad_log_select = [];
    //join ad_log后要group by的ad_log中的字段
    public $join_ad_log_group_by = [];

    //data_log被选时order by字段
    public $data_log_order_by_select = [];
    //reg_log被选时order by字段
//    public $reg_log_order_by_select = [];
    //一级维度id/name被选时order by字段
    public $first_ad_order_by;
    public $first_ad_order_by_select;
    //二级维度id/name被选时order by字段
    public $second_ad_order_by;
    public $second_ad_order_by_select;
    //三级维度id/name被选时order by字段
    public $third_ad_order_by;
    public $third_ad_order_by_select;

    //头条一级维度id/name被选时order by字段
    public $toutiao_first_ad_order_by = 'ad_log.ad1_create_time';
    public $toutiao_first_ad_order_by_select = ['first_ad_log.campaign_create_time as ad1_create_time'];
    //头条二级维度id/name被选时order by字段
    public $toutiao_second_ad_order_by = 'ad_log.ad2_create_time';
    public $toutiao_second_ad_order_by_select = ['ad_log.ad_create_time as ad2_create_time'];
    //头条三级维度id/name被选时order by字段
    public $toutiao_third_ad_order_by = 'ad_log.creative_create_time';
    public $toutiao_third_ad_order_by_select = ['third_ad_log.creative_create_time'];
    //腾讯一级维度id/name被选时order by字段
    public $tencent_first_ad_order_by = 'ad_log.ad1_create_time';
    public $tencent_first_ad_order_by_select = ['first_ad_log.created_time as ad1_create_time'];
    //腾讯二级维度id/name被选时order by字段
    public $tencent_second_ad_order_by = 'ad_log.ad2_create_time';
    public $tencent_second_ad_order_by_select = ['ad_log.created_time as ad2_create_time'];
    //腾讯三级维度id/name被选时order by字段
    public $tencent_third_ad_order_by = [];
    public $tencent_third_ad_order_by_select = [];
    //快手一级维度id/name被选时order by字段
    public $kuaishou_first_ad_order_by = 'ad_log.ad1_create_time';
    public $kuaishou_first_ad_order_by_select = ['first_ad_log.campaign_create_time as ad1_create_time'];
    //快手二级维度id/name被选时order by字段
    public $kuaishou_second_ad_order_by = 'ad_log.ad2_create_time';
    public $kuaishou_second_ad_order_by_select = ['ad_log.ad_create_time as ad2_create_time'];
    //快手三级维度id/name被选时order by字段
    public $kuaishou_third_ad_order_by = 'ad_log.creative_create_time';
    public $kuaishou_third_ad_order_by_select = ['third_ad_log.creative_create_time'];
    //百度一级维度id/name被选时order by字段
    public $baidu_first_ad_order_by = 'ad_log.ad1_create_time';
    public $baidu_first_ad_order_by_select = ['"1970-01-01 08:00:00" as ad1_create_time'];
    //百度二级维度id/name被选时order by字段
    public $baidu_second_ad_order_by = 'ad_log.ad2_create_time';
    public $baidu_second_ad_order_by_select = ['ad_log.ad_create_time as ad2_create_time'];
    //快手三级维度id/name被选时order by字段
    public $baidu_third_ad_order_by = 'ad_log.creative_create_time';
    public $baidu_third_ad_order_by_select = ['third_ad_log.creative_create_time'];
    //common一级维度id/name被选时order by字段
    public $common_first_ad_order_by = 'ad_log.ad1_create_time';
    public $common_first_ad_order_by_select = ['first_ad_log.ad1_create_time as ad1_create_time'];
    //common二级维度id/name被选时order by字段
    public $common_second_ad_order_by = 'ad_log.ad2_create_time';
    public $common_second_ad_order_by_select = ['ad_log.ad2_create_time as ad2_create_time'];
    //common三级维度id/name被选时order by字段
    public $common_third_ad_order_by = 'ad_log.creative_create_time';
    public $common_third_ad_order_by_select = ['third_ad_log.creative_create_time'];
    //主sql的数值计算及筛选字段
    public $main_compute = [], $main_dimension, $main_filter = [], $main_calc = [], $main_order_select = [],
        $main_order_by = [];
//    //reg_log表数据库及表名
//    public $reg_log_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dwd_game_uid_reg_log';

    //不同广告的select字段集
    public $toutiao_ad_log = [], $tencent_ad_log = [], $kuaishou_ad_log = [], $baidu_ad_log = [],$common_ad_log = [], $toutiao_data_log = [],
        $tencent_data_log = [], $kuaishou_data_log = [], $baidu_data_log = [], $common_data_log = [], $rta_bind_log = [], $compress_toutiao_data_log = [],
        $compress_tencent_data_log = [], $compress_kuaishou_data_log = [], $compress_baidu_data_log = [], $compress_common_data_log = [],
//        $reg_log = [],
        $overview_log = [], $toutiao_account_log = [], $tencent_account_log = [], $kuaishou_account_log = [], $baidu_account_log = [], $common_account_log = [], $overview_apportion_log = [], $data_apportion_log = [];
    //不同广告的group by字段集
    public $toutiao_ad_log_group_by = [], $tencent_ad_log_group_by = [], $kuaishou_ad_log_group_by = [], $baidu_ad_log_group_by = [], $common_ad_log_group_by = [],
        $toutiao_data_log_group_by = [], $tencent_data_log_group_by = [], $kuaishou_data_log_group_by = [], $baidu_data_log_group_by = [], $common_data_log_group_by = [],
//        $reg_log_group_by = [],
        $overview_log_group_by = [], $account_log_group_by = [],
        $first_ad_log_group_by = [], $third_ad_log_group_by = [];
    //不同广告的where条件集
    public $toutiao_ad_log_filter = [], $tencent_ad_log_filter = [], $kuaishou_ad_log_filter = [], $baidu_ad_log_filter = [], $common_ad_log_filter = [],
        $toutiao_data_log_filter = [], $tencent_data_log_filter = [], $kuaishou_data_log_filter = [], $baidu_data_log_filter = [], $common_data_log_filter = [],
        $toutiao_account_log_filter = [], $tencent_account_log_filter = [], $kuaishou_account_log_filter = [], $baidu_account_log_filter = [], $common_account_log_filter = [],
        $toutiao_convert_log_filter = [],
//        $reg_log_filter = [],
        $overview_log_filter = [], $account_log_filter = [];

    //overview_log表数据库及表名
    public $overview_log_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_ads_day_overview_log';
    //不同广告的ad_log表和data_log表
    public $toutiao_data_log_table = 'ods_toutiao_creative_hour_data_log',
        $toutiao_account_log_table = 'ods_toutiao_account_log',
        $toutiao_ad_log_table = 'ods_toutiao_ad_log',
        $toutiao_first_ad_log_table = 'ods_toutiao_campaign_log',
        $toutiao_third_ad_log_table = 'ods_toutiao_creative_log',
        $toutiao_convert_log_table = 'ods_toutiao_convert_log',

        $tencent_data_log_table = 'ods_tencent_ad_hour_data_log',
        $ad_expand_target_log_table = 'ods_tencent_ad_expand_target_day_data_log',
        $tencent_account_log_table = 'ods_tencent_account_log',
        $tencent_ad_log_table = 'ods_tencent_adgroup_log',
        $tencent_first_ad_log_table = 'ods_tencent_campaign_log',
        $tencent_third_ad_log_table = 'ods_tencent_ad_log',

        $kuaishou_data_log_table = 'ods_kuaishou_creative_hour_data_log',
        $kuaishou_account_log_table = 'ods_kuaishou_account_log',
        $kuaishou_ad_log_table = 'ods_kuaishou_unit_log',
        $kuaishou_first_ad_log_table = 'ods_kuaishou_campaign_log',
        $kuaishou_third_ad_log_table = 'ods_kuaishou_creative_log',

        $baidu_data_log_table = 'ods_baidu_creative_hour_data_log',
        $baidu_account_log_table = 'ods_baidu_account_log',
        $baidu_ad_log_table = 'ods_baidu_ad_log',
        $baidu_first_ad_log_table = 'ods_baidu_campaign_log',
        $baidu_third_ad_log_table = 'ods_baidu_creative_log',

        $common_data_log_table = 'dwd_media_ad3_common_hour_data_log',
        $common_account_log_table = 'dwd_media_account_common_log',
        $common_ad_log_table = 'dwd_media_ad2_common_log',
        $common_first_ad_log_table = 'dwd_media_ad1_common_log',
        $common_third_ad_log_table = 'dwd_media_ad3_common_log',

        $material_file_log = 'ods_material_file_log',
        $material_log = 'ods_material_log',
        $material_label_log = 'ods_material_label',
        $toutiao_video_log = 'ods_toutiao_video_log',
        $toutiao_inefficient_material_log = 'ods_toutiao_inefficient_material_log',

        $calc_bind_table = 'ods_ad_bind_calc_rule_log',
        $agency_change_log = 'ods_media_account_agency_change_log',
        $rta_bind_table = '',
        $game_standard_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_game_standard_value_days_incolumn',
        $agent_group_true_rebate = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_agent_group_true_rebate_id',
        $agent_true_rebate = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_agent_true_rebate_id',
        $live_table = 'ods_live_user_name_log',
        $aweme_list_table = 'ods_toutiao_aweme_auth_detail_log',
        $person_group_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_interface_person_group',
        $agent_site_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_agent_site_id',
        $agent_leader_group_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_agent_leader_group',
        $live_apportion_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dwd_flow_data_apportion_log',
        $official_apportion_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dwd_official_game_data_apportion_log',
        $natural_apportion_table = EnvConfig::MYSQL['datahub']['database'] . '.v3_dwd_official_data_apportion_log',
        $manuel_day_cost_log = EnvConfig::MYSQL['datahub']['database'] . '.v2_dwd_day_cost_log',
        $team_agent_config_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_team_agent_config',
        $agent_id_official_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_agent_id_official',
        $official_filter_table = EnvConfig::MYSQL['datahub']['database'] . '.v3_dwd_official_filter_log';
    //不同广告的连接字段
    //material_file_log表连接字段
    public $join_material_file_log_on = [
            ['material_file_log.platform', '=', 'third_ad_log.platform'],
            ['material_file_log.signature', '=', 'third_ad_log.signature'],
        ],
        //material_log表连接字段
        $join_material_log_on = [
            ['material_log.platform', '=', 'material_file_log.platform'],
            ['material_log.material_id', '=', 'material_file_log.material_id']
        ],
        //material_label表连接字段
        $join_material_label_on = [
            ['material_label.platform', '=', 'material_file_log.platform'],
            ['material_label.material_id', '=', 'material_file_log.material_id']
        ],
        //不同广告ad_log和first_ad_log连接条件
        $toutiao_join_first_ad_log_on = [
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        $tencent_join_first_ad_log_on = [
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        $kuaishou_join_first_ad_log_on = [
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        $baidu_join_first_ad_log_on = [
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        $common_join_first_ad_log_on = [
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.ad1_id', '=', 'ad_log.ad1_id'],
            ['first_ad_log.media_type', '=', 'ad_log.media_type'],
        ],
        //不同广告ad_log和third_ad_log连接条件
        $toutiao_join_third_ad_log_on = [
            ['third_ad_log.platform', '=', 'ad_log.platform'],
            ['third_ad_log.ad_id', '=', 'ad_log.ad_id']
        ],
        $tencent_join_third_ad_log_on = [
            ['third_ad_log.platform', '=', 'ad_log.platform'],
            ['third_ad_log.adgroup_id', '=', 'ad_log.adgroup_id']
        ],
        $kuaishou_join_third_ad_log_on = [
            ['third_ad_log.platform', '=', 'ad_log.platform'],
            ['third_ad_log.ad_id', '=', 'ad_log.ad_id']
        ],
        $baidu_join_third_ad_log_on = [
            ['third_ad_log.platform', '=', 'ad_log.platform'],
            ['third_ad_log.ad_id', '=', 'ad_log.ad_id']
        ],
        $common_join_third_ad_log_on = [
            ['third_ad_log.platform', '=', 'ad_log.platform'],
            ['third_ad_log.ad2_id', '=', 'ad_log.ad2_id'],
            ['third_ad_log.media_type', '=', 'ad_log.media_type'],
        ],

        //不同广告二级维度ad_log和account_log连接条件
        $ad2_join_account_on = [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ],
        //不同广告一级维度ad_log和account_log连接条件
        $ad1_join_account_on = [
            ['account_log.platform', '=', 'first_ad_log.platform'],
            ['account_log.account_id', '=', 'first_ad_log.account_id']
        ],
        //不同广告ad_log和data_log连接条件
        $toutiao_join_data_on = [
            ['data_log.platform', '=', 'ad_log.platform'],
            ['data_log.ad_id', '=', 'ad_log.ad_id']
        ],

        $tencent_join_data_on = [
            ['data_log.platform', '=', 'ad_log.platform'],
            ['data_log.adgroup_id', '=', 'ad_log.adgroup_id'],
        ],

        $kuaishou_join_data_on = [
            ['data_log.platform', '=', 'ad_log.platform'],
            ['data_log.ad_id', '=', 'ad_log.ad_id']
        ],

        $baidu_join_data_on = [
            ['data_log.platform', '=', 'ad_log.platform'],
            ['data_log.ad_id', '=', 'ad_log.ad_id']
        ],

        $common_join_data_on = [
            ['data_log.platform', '=', 'ad_log.platform'],
            ['data_log.ad2_id', '=', 'ad_log.ad2_id'],
            ['data_log.media_type', '=', 'ad_log.media_type']
        ],

        //连接数值规则
        $toutiao_join_calc_rule_on = [
            ['calc_bind.ad2_id', '=', 'ad_log.ad_id'],
            ['calc_bind.platform', '=', 'ad_log.platform']
        ],

        $tencent_join_calc_rule_on = [
            ['calc_bind.ad2_id', '=', 'ad_log.adgroup_id'],
            ['calc_bind.platform', '=', 'ad_log.platform']
        ],

        $kuaishou_join_calc_rule_on = [
            ['calc_bind.ad2_id', '=', 'ad_log.ad_id'],
            ['calc_bind.platform', '=', 'ad_log.platform']
        ],

        $baidu_join_calc_rule_on = [
            ['calc_bind.ad2_id', '=', 'ad_log.ad_id'],
            ['calc_bind.platform', '=', 'ad_log.platform']
        ],

        $common_join_calc_rule_on = [
            ['calc_bind.ad2_id', '=', 'ad_log.ad2_id'],
            ['calc_bind.platform', '=', 'ad_log.platform']
        ],

        //连接rta绑定
        $toutiao_join_rta_bind_on = [
        ['rta_bind.binding_target_id', '=', 'account_log.account_id'],
//        ['rta_bind.platform', '=', 'ad_log.platform']
    ],

        $tencent_join_rta_bind_on = [
        ['rta_bind.binding_target_id', '=', 'ad_log.adgroup_id'],
//        ['rta_bind.platform', '=', 'ad_log.platform']
    ],

        $kuaishou_join_rta_bind_on = [
        ['rta_bind.binding_target_id', '=', 'account_log.account_id'],
//        ['rta_bind.platform', '=', 'ad_log.platform']
    ],

        $baidu_join_rta_bind_on = [
        ['rta_bind.binding_target_id', '=', 'account_log.account_id'],
//        ['rta_bind.platform', '=', 'ad_log.platform']
    ],

        $common_join_rta_bind_on = [
        ['rta_bind.binding_target_id', '=', 'ad_log.ad2_id'],
//        ['rta_bind.platform', '=', 'ad_log.platform']
    ],

        $toutiao_third_join_data_log_on = [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.creative_id', '=', 'third_ad_log.creative_id']
        ],
        //连接rit细分所用连接条件
        $toutiao_third_join_rit_data_log_on = [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.ad_id', '=', 'third_ad_log.ad_id']
        ],

        $tencent_third_join_data_log_on = [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.ad_id', '=', 'third_ad_log.ad_id']
        ],

        $kuaishou_third_join_data_log_on = [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.creative_id', '=', 'third_ad_log.creative_id']
        ],

        $baidu_third_join_data_log_on = [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.creative_id', '=', 'third_ad_log.creative_id']
        ],

        $common_third_join_data_log_on = [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.ad3_id', '=', 'third_ad_log.ad3_id'],
            ['data_log.media_type', '=', 'third_ad_log.media_type']
        ],

        $toutiao_third_join_overview_log_on = [
            ['overview_log.platform', '=', 'third_ad_log.platform'],
            ['overview_log.ad3_id', '=', 'third_ad_log.creative_id']
        ],

        $tencent_third_join_overview_log_on = [
            ['overview_log.platform', '=', 'third_ad_log.platform'],
            ['overview_log.ad3_id', '=', 'third_ad_log.ad_id']
        ],

        $kuaishou_third_join_overview_log_on = [
            ['overview_log.platform', '=', 'third_ad_log.platform'],
            ['overview_log.ad3_id', '=', 'third_ad_log.creative_id']
        ],

        $baidu_third_join_overview_log_on = [
            ['overview_log.platform', '=', 'third_ad_log.platform'],
            ['overview_log.ad3_id', '=', 'third_ad_log.creative_id']
        ],

        $common_third_join_overview_log_on = [
            ['overview_log.platform', '=', 'third_ad_log.platform'],
            ['overview_log.ad3_id', '=', 'third_ad_log.ad3_id'],
            ['agent_site.media_type_id', '=', 'third_ad_log.media_type']
        ],

        $toutiao_rta_join_data_log_on = [
            ['rta_bind.binding_target_id', '=', 'account_log.account_id']
        ],

        $tencent_rta_join_data_log_on = [
            ['rta_bind.binding_target_id', '=', 'data_log.adgroup_id']
        ],

        $kuaishou_rta_join_data_log_on = [
            ['rta_bind.binding_target_id', '=', 'account_log.account_id']
        ],

        $baidu_rta_join_data_log_on = [
            ['rta_bind.binding_target_id', '=', 'account_log.account_id']
        ],

        $common_rta_join_data_log_on = [
            ['rta_bind.binding_target_id', '=', 'account_log.account_id']
        ],


        $toutiao_rta_join_overview_log_on = [
            ['rta_bind.binding_target_id', '=', 'overview_log.account_id']
        ],

        $tencent_rta_join_overview_log_on = [
            ['rta_bind.binding_target_id', '=', 'overview_log.ad2_id']
        ],

        $kuaishou_rta_join_overview_log_on = [
            ['rta_bind.binding_target_id', '=', 'overview_log.account_id']
        ],

        $baidu_rta_join_overview_log_on = [
            ['rta_bind.binding_target_id', '=', 'overview_log.account_id']
        ],

        $common_rta_join_overview_log_on = [
            ['rta_bind.binding_target_id', '=', 'overview_log.account_id']
        ],

        //不同广告的ad_log表和overview_log连接字段不同(overview_log要查的字段)
        $toutiao_ad_join_overview_on = [
            ['ad_log.platform', '=', 'overview_log.platform'],
            ['ad_log.ad_id', '=', 'overview_log.ad2_id'],
//            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],
        $tencent_ad_join_overview_on = [
            ['ad_log.platform', '=', 'overview_log.platform'],
            ['ad_log.adgroup_id', '=', 'overview_log.ad2_id'],
//            ['overview_log.media_type', '=', MediaType::TENCENT]
        ],
        $kuaishou_ad_join_overview_on = [
            ['ad_log.platform', '=', 'overview_log.platform'],
            ['ad_log.ad_id', '=', 'overview_log.ad2_id'],
//            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],
        $baidu_ad_join_overview_on = [
            ['ad_log.platform', '=', 'overview_log.platform'],
            ['ad_log.ad_id', '=', 'overview_log.ad2_id'],
    //            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],
        $common_ad_join_overview_on = [
            ['ad_log.platform', '=', 'overview_log.platform'],
            ['ad_log.ad2_id', '=', 'overview_log.ad2_id'],
            ['agent_site.media_type_id', '=', 'ad_log.media_type']
        ],

        //不同广告的ad_log表和overview_log连接字段不同(overview_log要查的字段)
        $toutiao_rit_data_log_join_overview_on = [
            ['data_log.platform', '=', 'overview_log.platform'],
            ['data_log.ad_id', '=', 'overview_log.ad2_id'],
//            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],

        $toutiao_data_log_join_overview_on = [
            ['data_log.platform', '=', 'overview_log.platform'],
            ['data_log.ad_id', '=', 'overview_log.ad2_id'],
            ['data_log.creative_id', '=', 'overview_log.ad3_id'],
//            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],
        $tencent_data_log_join_overview_on = [
            ['data_log.platform', '=', 'overview_log.platform'],
            ['data_log.adgroup_id', '=', 'overview_log.ad2_id'],
            ['data_log.ad_id', '=', 'overview_log.ad3_id'],
//            ['overview_log.media_type', '=', MediaType::TENCENT]
        ],
        $kuaishou_data_log_join_overview_on = [
            ['data_log.platform', '=', 'overview_log.platform'],
            ['data_log.ad_id', '=', 'overview_log.ad2_id'],
            ['data_log.creative_id', '=', 'overview_log.ad3_id'],
//            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],
        $baidu_data_log_join_overview_on = [
            ['data_log.platform', '=', 'overview_log.platform'],
            ['data_log.ad_id', '=', 'overview_log.ad2_id'],
            ['data_log.creative_id', '=', 'overview_log.ad3_id'],
    //            ['overview_log.media_type', '=', MediaType::TOUTIAO]
        ],
        $common_data_log_join_overview_on = [
            ['data_log.platform', '=', 'overview_log.platform'],
            ['data_log.ad2_id', '=', 'overview_log.ad2_id'],
            ['data_log.ad3_id', '=', 'overview_log.ad3_id'],
            ['agent_site.media_type_id', '=', 'ad_log.media_type']
        ],

        $ad_log_join_ad_expand_target_on = [
            ['ad_log.adgroup_id', '=', 'ad_expand_target_log.adgroup_id'],
            ['ad_log.platform', '=', 'ad_expand_target_log.platform']
        ],
        $data_log_join_ad_expand_target_on = [
            ['data_log.adgroup_id', '=', 'ad_expand_target_log.adgroup_id'],
            ['data_log.platform', '=', 'ad_expand_target_log.platform']
        ];
    //统一的select，group by，where字段集
    public $data_log_table, $ad_log_table, $account_log_table, $first_ad_log_table, $third_ad_log_table;
    public $ad_log_fields = [], $data_log_fields = [], $compress_data_log_fields = [], $compress_overview_log_fields = [], $account_log_fields = [], $first_ad_log_fields = [],
        $third_ad_log_fields = [];
    public $ad_log_group_by = [], $data_log_group_by = [], $data_log_filter = [], $ad_log_filter = [],
        $first_ad_log_filter = [], $third_ad_log_filter = [], $ad_log_filter_for_data_log = [];
    // 独立的ad_log_filter
    public $ad_log_private_filter = [];
    //各表连接所用字段
    public $data_log_join_fields, $ad_log_join_fields, $account_log_join_fields, $first_ad_log_join_fields,
        $third_ad_log_join_fields;
    //不同广告时ad_log和data_log之间连接条件的不同
    public $join_data_on, $join_overview_on, $overview_join_data_log_on, $join_account_log_on,
        $join_first_ad_log_on, $join_third_ad_log_on, $third_join_on, $third_join_data_log_on,
        $third_join_overview_log_on, $join_calc_bind_on, $join_rta_bind_on, $rta_join_data_log_on,
        $rta_join_overview_log_on
//        $third_join_reg_log_on
    ;


    /**
     * 之后按照各个表来排列各属性，上面的之后再整理
     * site_game:
     */
    public $site_game_group_by, $site_game_filter = [];
    /**
     * game
     */
    public $game_fields, $game_group_by, $game_filter = [];
    /**
     * agent_site:
     */
    public $agent_site_fields, $agent_site_group_by, $agent_site_filter = [];
    /**
     * material_file_log
     */
    public $material_file_log_fields = [], $material_file_log_filter = [], $material_file_log_sub_filter = [];
    /**
     * material_log
     */
    public $material_log_fields = [], $material_log_filter = [];
    /**
     * material_label
     */
    public $material_label_fields = [], $material_label_filter = [];
    /**
     * agency_change_Log
     */
    public $agency_change_log_filter = [];
    /**
     * calc_rule
     */
    public $calc_bind_rule_filter = [];
    public $join_calc_bind = false;
    public $join_calc_bind_type = '';
    public $join_rta_bind = false;

    public $judge_rta_bind_status = false;
    public $join_rta_bind_type = '';
    public $aweme_list_filter = [];
    public $toutiao_is_inefficient_filter = [];
    public $agent_leader_group_filter = [];
    public $team_agent_group_filter = [];
//    public $person_group_filter = [];
    //需要用计算百分比的字段
    public $all_calculate_percentage = [
        'ks_ad_photo_played_2s_ratio', 'ks_play_3s_ratio', 'ks_play_5s_ratio', 'ks_ad_photo_played_10s_ratio', 'ks_play_end_ratio', 'ks_ad_photo_played_75percent_ratio', 'tt_play_duration_3s_rate', 'action_uid_reg_rate', 'action_reg_rate', 'action_reg_rate_device', 'first_day_pay_rate', 'click_rate', 'reg_rate', 'active_rate', 'convert_rate',
        'first_day_roi', 'total_roi', 'cost_process', 'pay_rate', 'tt_wifi_play_rate', 'tt_play_over_rate',
        'tt_valid_play_rate', 'tt_loan_completion_rate', 'tt_install_finish_rate', 'tt_download_start_rate',
        'tt_deep_convert_rate', 'tt_download_finish_rate', 'tt_next_day_open_rate', 'tt_game_addiction_rate',
        'rate_day_stay_2', 'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3',
        'rate_day_roi_7','rate_day_roi_15','rate_day_roi_30', 'reg_old_muid_percentage', 'reg_old_clique_muid_percentage', 'day_three_login_uid_percentage', 'first_day_roi_standard_value', 'rate_day_roi_2_standard_value', 'rate_day_roi_3_standard_value', 'rate_day_roi_7_standard_value', 'xcx_ori_reg_count_role_create',  'gdt_video_outer_play_time_avg_rate', 'gdt_video_outer_play_rate', 'rate_day_pay_uid_second_day_login', 'rate_day_pay_uid_third_day_login', 'rate_day_pay_uid_seventh_day_login', 'rate_day_pay_uid_fifteenth_day_login', 'rate_day_pay_uid_thirty_day_login', 'first_day_pay_money_within_6_rate', 'baidu_reserve_download_conversions_click_rate', 'first_pay_24_hour_pay_roi', 'tt_first_pay_intra_24hour_roi', 'baidu_aggr_form_submit_success_click_rate',
        'key_role_level_count_rate', 'gdt_minigame_24h_pay_roi'
    ];

    //需要相除的字段
    public $all_calculate_division = [
        'cost_per_reg', 'first_day_ltv', 'cost_per_first_day_pay', 'first_day_arppu', 'cpc', 'cpm',
        'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'media_cost_per_reg', 'tt_average_play_time_per_play',
        'tt_valid_play_cost', 'tt_loan_completion_cost', 'tt_download_finish_cost', 'tt_download_start_cost',
        'tt_install_finish_cost', 'tt_deep_convert_cost', 'tt_next_day_open_cost', 'tt_game_addiction_cost',
        'cost_first_day_pay_times', 'cost_total_pay_times', 'cost_day_second_login', 'gdt_video_outer_play_cost', 'gdt_video_outer_play_time_count', 'ks_impression_1k_cost', 'baidu_cost_reserve_download_conversions', 'cost_first_pay_24_hour_pay_times', 'cost_first_pay_24_hour_pay_count', 'baidu_cost_aggr_form_submit_success', 'cost_key_role_level_count',
        'gdt_minigame_24h_pay_arppu'
    ];
    //需要计算平均值的字段
    public $all_calculate_average = ['cpa_bid', 'deep_cpabid', 'roi_goal', 'ad1_budget', 'ks_bid', 'tt_ad1_cpa_bid'];
    //需要单纯累加但是要保留两位小数的字段
    public $all_need_keep_two_decimal = ['cost', 'ori_cost', 'budget', 'balance', 'tt_valid_cash', 'tt_valid_grant',
        'tt_valid_return_goods_abs', 'gdt_wechat_cost_stage1', 'gdt_wechat_cost_stage2', 'tt_default_balance', 'tt_search_balance', 'tt_union_balance', 'tt_common_balance'];
    public $all_need_keep_integer = ['reg_uid_count','first_day_pay_count','first_day_pay_money','seventh_day_pay_money','fifteenth_day_pay_money','thirty_day_pay_money','reg_muid_count','action_muid_count','first_day_pay_times','total_pay_times','click_count','total_pay_count','total_pay_money','reg_old_clique_muid_count','reg_old_muid_count','day_three_login_uid_count','new_pay_day_count','new_pay_day_money','callback_first_day_pay_money','callback_first_day_pay_uid_count','xcx_ori_reg_count'];
    //所有需要计算的字段
    public $all_need_calculate = [
        'ks_ad_photo_played_2s_ratio', 'ks_play_3s_ratio', 'ks_play_5s_ratio', 'ks_ad_photo_played_10s_ratio', 'ks_play_end_ratio', 'ks_ad_photo_played_75percent_ratio', 'tt_play_duration_3s', 'tt_play_duration_3s_rate', 'count', 'count_ad2', 'count_ad2_deliveried', 'count_ad2_delivering', 'count_ad2_undeliveried',
        'count_cost_date', 'count_ad3', 'cost', 'reg_uid_count', 'reg_muid_count', 'action_muid_count', 'first_day_ltv',
        'first_day_pay_count', 'first_day_pay_money','seventh_day_pay_money','fifteenth_day_pay_money','thirty_day_pay_money', 'first_day_arppu', 'first_day_roi', 'cost_process', 'click',
        'show', 'ori_cost', 'click_rate', 'convert', 'pay_count', 'active_count', 'convert_rate', 'reg_count',
        'media_cost_per_reg', 'pay_rate', 'deep_cpabid', 'roi_goal', 'tt_attribution_next_day_open_cnt', 'tt_loan_completion',
        'tt_download_finish', 'tt_install_finish', 'tt_download_start', 'tt_click_install', 'tt_deep_convert',
        'tt_next_day_open', 'tt_game_addiction', 'tt_play_75_feed_break', 'tt_play_100_feed_break',
        'tt_play_duration_sum', 'tt_valid_play', 'tt_play_25_feed_break', 'tt_total_play', 'tt_wifi_play',
        'tt_play_50_feed_break', 'tt_play_over', 'tt_location_click', 'tt_comment', 'tt_share', 'tt_follow',
        'tt_home_visited', 'tt_like', 'tt_ies_music_click', 'tt_ies_challenge_click', 'cpa_bid', 'budget',
        'action_uid_reg_rate', 'action_reg_rate', 'action_reg_rate_device', 'cost_per_reg', 'first_day_pay_rate', 'cost_per_first_day_pay', 'total_roi', 'cpc',
        'cpm', 'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'reg_rate', 'active_rate',
        'tt_average_play_time_per_play', 'tt_wifi_play_rate', 'tt_play_over_rate', 'tt_valid_play_cost',
        'tt_valid_play_rate', 'tt_loan_completion_cost', 'tt_loan_completion_rate', 'tt_download_finish_cost',
        'tt_install_finish_rate', 'tt_download_start_rate', 'tt_download_start_cost', 'tt_install_finish_cost',
        'tt_deep_convert_rate', 'tt_deep_convert_cost', 'tt_download_finish_rate', 'tt_next_day_open_rate',
        'tt_next_day_open_cost', 'tt_game_addiction_rate', 'tt_game_addiction_cost',  'rate_day_stay_2',
        'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7','rate_day_roi_15','rate_day_roi_30',
        'first_day_pay_times', 'total_pay_times', 'cost_first_day_pay_times', 'cost_total_pay_times', 'click_count',
        'cost_day_second_login', 'total_pay_count', 'total_pay_money', 'callback_first_day_pay_money', 'callback_first_day_pay_uid_count', 'reg_old_muid_count', 'reg_old_clique_muid_count',
        'day_three_login_uid_count', 'reg_old_muid_percentage', 'reg_old_clique_muid_percentage',
        'day_three_login_uid_percentage', 'new_pay_day_money', 'new_pay_day_count', 'balance', 'tt_valid_cash', 'tt_valid_grant',
        'tt_valid_return_goods_abs', 'first_day_roi_standard_value', 'rate_day_roi_2_standard_value', 'rate_day_roi_3_standard_value', 'rate_day_roi_7_standard_value', 'standard_reached_cost', 'standard_unreached_cost', 'gdt_wechat_cost_stage1', 'gdt_wechat_cost_stage2',
         'ad1_budget', 'xcx_ori_reg_count_role_create', 'tt_default_balance', 'tt_search_balance', 'tt_union_balance', 'tt_common_balance',
        'gdt_video_outer_play10_count', 'gdt_video_outer_play25_count', 'gdt_video_outer_play50_count', 'gdt_video_outer_play75_count', 'gdt_video_outer_play95_count', 'gdt_video_outer_play100_count', 'gdt_video_outer_play_time_count', 'gdt_video_outer_play_time_avg_rate', 'gdt_video_outer_play_rate', 'gdt_video_outer_play_cost', 'gdt_video_outer_play3s_count', 'gdt_video_outer_play5s_count', 'gdt_video_outer_play7s_count', 'rate_day_pay_uid_second_day_login', 'rate_day_pay_uid_third_day_login', 'rate_day_pay_uid_seventh_day_login', 'rate_day_pay_uid_fifteenth_day_login', 'rate_day_pay_uid_thirty_day_login', 'ks_impression_1k_cost', 'ks_aclick', 'ks_share', 'ks_comment', 'ks_like', 'ks_follow', 'ks_cancel_follow', 'first_day_pay_money_within_6_count', 'first_day_pay_money_within_6_rate', 'baidu_reserve_download_conversions_click_rate', 'baidu_cost_reserve_download_conversions', 'first_pay_24_hour_pay_money', 'first_pay_24_hour_pay_count', 'first_pay_24_hour_pay_times', 'first_pay_24_hour_pay_roi', 'cost_first_pay_24_hour_pay_times', 'cost_first_pay_24_hour_pay_count', 'tt_first_pay_intra_24hour_amount', 'tt_first_pay_intra_24hour_roi', 'baidu_aggr_form_submit_success_click_rate', 'baidu_cost_aggr_form_submit_success', 'tt_ad1_cpa_bid', 'baidu_aggr_form_submit_success', 'day_third_day_pay_count', 'day_seventh_day_pay_count', 'key_role_level_count', 'key_role_level_count_rate', 'cost_key_role_level_count', 'gdt_minigame_24h_pay_amount', 'gdt_minigame_24h_pay_uv', 'gdt_minigame_24h_pay_roi', 'gdt_minigame_24h_pay_arppu'
    ];

    //需要转化成中文的字段
    public $all_translate_to_cn = [
        'tt_smart_bid_type', 'tt_download_type', 'tt_auto_extend_targets', 'tt_gender', 'ad2_status',
        'inventory_type', 'account_status', 'ad1_status', 'advertising_opt_status', 'ad3_status', 'ad3_opt_status',
        'tt_learning_phase', 'tt_schedule_type', 'tt_flow_control_mode', 'tt_union_video_type',
        'tt_creative_material_mode', 'tt_deep_bid_type', 'tt_inventory_type', 'tt_ad1_budget_mode',
        'tt_ad2_budget_mode', 'tt_auto_extend_enabled', 'gdt_deep_optimization_action_type', 'ad1_type',
        'flow_control_mode', 'is_mix_os', 'pricing', 'gdt_adcreative_template_id', 'gdt_expand_enabled', 'gdt_optimization_goal',
        'gdt_deep_conversion_type', 'gdt_deep_conversion_behavior_goal', 'gdt_deep_conversion_worth_goal','gdt_smart_delivery_platform',
        'tt_inventory_subdivision', 'gdt_inventory_subdivision','gdt_xqxs_inventory_subdivision', 'gdt_is_expand_targeting', 'gdt_ad2_promoted_object_type',
        'gdt_location_types', 'gdt_bid_strategy', 'gdt_regions', 'gdt_consumption_type', 'gdt_paid_user',
        'gdt_deprecated_region', 'gdt_mobile_union', 'gdt_exclude_mobile_union', 'gdt_display_scene',
        'gdt_is_rewarded_video_ad', 'gdt_automatic_site_enabled', 'gdt_ad1_speed_mode', 'create_type', 'gdt_ad3_audit_spec',
        'gdt_ad3_site_set', 'gdt_ad3_page_type', 'gdt_ad3_promoted_object_type', 'tt_convert_type',
        'baidu_campaign_schedule', 'baidu_ad_bidtype', 'ad2_os', 'port_version', 'tt_account_status_version_one', 'tt_account_status_version_two', 'tt_ad1_budget_optimization', 'tt_campaign_delivery_mode', 'is_inefficient', 'gdt_ecom_pkam_switch', 'gdt_adgroup_status', 'ks_ad_type', 'ks_smart_bid', 'ks_ocpx_action_type', 'ks_deep_conversion_type', 'ks_show_mode', 'ks_unit_type', 'ks_convert_id', 'ks_creative_material_type', 'ks_creative_status_type', 'tt_is_similar_material', 'tt_is_ad_high_quality', 'tt_is_first_publish_material', 'gdt_bid_scene', 'baidu_trans_type', 'baidu_deep_trans_type', 'tt_raise_status', 'gdt_auto_acquisition_status', 'gdt_is_playable'
    ];

    //业务指标字段
    public $business_target = ['reg_uid_count', 'action_uid_reg_rate', 'action_reg_rate', 'action_reg_rate_device',
        'reg_muid_count', 'action_muid_count',
        'first_day_ltv', 'first_day_pay_count', 'first_day_pay_rate', 'first_day_pay_money','seventh_day_pay_money','fifteenth_day_pay_money','thirty_day_pay_money', 'first_day_arppu',
        'first_day_roi', 'rate_day_roi_2','rate_day_roi_3', 'rate_day_stay_2', 'rate_day_stay_3', 'rate_day_stay_7',
        'rate_day_stay_15', 'first_day_pay_times', 'total_pay_times', 'click_count', 'total_pay_count', 'total_pay_money',
        'callback_first_day_pay_money', 'callback_first_day_pay_uid_count',
        'reg_old_muid_count', 'reg_old_clique_muid_count', 'day_three_login_uid_count', 'reg_old_muid_percentage',
        'reg_old_clique_muid_percentage', 'day_three_login_uid_percentage', 'new_pay_day_money', 'new_pay_day_count',
        'rate_day_roi_7','rate_day_roi_15','rate_day_roi_30', 'xcx_ori_reg_count_role_create', 'rate_day_pay_uid_second_day_login', 'rate_day_pay_uid_third_day_login', 'rate_day_pay_uid_seventh_day_login', 'rate_day_pay_uid_fifteenth_day_login', 'rate_day_pay_uid_thirty_day_login', 'first_day_pay_money_within_6_rate', 'first_pay_24_hour_pay_money', 'first_pay_24_hour_pay_count', 'first_pay_24_hour_pay_times', 'first_pay_24_hour_pay_roi', 'cost_first_pay_24_hour_pay_times', 'cost_first_pay_24_hour_pay_count'];

    //聚合穿山甲流量需要过滤的聚合字段
    public $filter_dimension_for_rit = [
        'ad3_id', 'ad3_name',
    ];
    //聚合穿山甲流量需要过滤的自定义指标字段
    public $filter_target_for_rit = [
        'tt_play_duration_3s', 'tt_play_duration_3s_rate', 'count_ad3', 'cpc', 'click', 'cpm', 'show', 'click_rate', 'reg_rate', 'active_rate', 'convert_rate',
        'tt_location_click', 'tt_comment', 'tt_share', 'tt_follow', 'tt_home_visited', 'tt_like', 'tt_ies_music_click',
        'tt_ies_challenge_click', 'tt_play_75_feed_break', 'tt_play_100_feed_break', 'tt_average_play_time_per_play',
        'tt_play_duration_sum', 'tt_wifi_play_rate', 'tt_valid_play', 'tt_play_25_feed_break', 'tt_play_over_rate',
        'tt_total_play', 'tt_valid_play_cost', 'tt_valid_play_rate', 'tt_wifi_play', 'tt_play_50_feed_break',
        'tt_play_over', 'tt_quality_score', 'tt_ctr_score', 'tt_web_score', 'tt_cvr_score', 'tt_loan_completion_rate',
        'tt_download_finish', 'tt_install_finish', 'tt_download_finish_cost', 'tt_install_finish_rate',
        'tt_download_start_rate', 'tt_download_start_cost', 'tt_download_start', 'tt_install_finish_cost',
        'tt_click_install', 'tt_deep_convert_rate', 'tt_deep_convert', 'tt_deep_convert_cost', 'tt_download_finish_rate',
        'tt_next_day_open_rate', 'tt_next_day_open_cost', 'tt_next_day_open', 'tt_game_addiction', 'tt_game_addiction_rate',
        'tt_game_addiction_cost'
    ];

    public $material_log_with_as_fields = ['material_file_log.id', 'material_file_log.filename', 'material_file_log.width', 'material_file_log.height', 'material_file_log.material_id', 'material_file_log.signature', 'material_file_log.platform',
        'material_file_log.platform AS material_platform', 'material_log.name', 'ROW_NUMBER() over ( PARTITION BY material_file_log.signature ORDER BY material_file_log.id, material_log.is_del ASC ) AS row_rank'];

    //需要给前端x日回本标准值的指标
    public $calc_standard_value = ['first_day_roi', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7', 'rate_day_roi_15','rate_day_roi_30',];

    //需要计算有效消耗、无效消耗
    public $calc_standard_reached_cost = ['standard_reached_cost', 'standard_unreached_cost'];

    //data_log计算x日回本的字段
    public $sub_data_log_calc_standard_fields = [];
    public $main_calc_standard_fields = [];
    public $sub_main_calc_standard_fields = [];
    //sub_data_log的group by 字段
    public $sub_data_log_groups = ['cost_date'];
    //sub_overview_log的group by 字段
    public $sub_overview_log_groups = ['cost_date'];
    //sub_main_log的group by字段
    public $sub_main_log_groups = [];
    //sub_main_log的select字段
    public $sub_main_select = [];
    //sub_main builder中子查询连接字段
    public $sub_main_join = ['cost_date'];

    public $calc_standard_ad2_field = [];
    //需要连接aweme_list的字段
    public $need_join_aweme_list = ['item_id', 'aweme_title', 'aweme_account', 'aweme_name', 'interface_person', 'interface_person_group_name'];
    public $aweme_list_with_as_field = [
        'aweme_list.title AS aweme_title',
        'aweme_list.item_id AS item_id',
        "IFNULL( person_group.interface_person_group_name, '未分组' ) AS interface_person_group_name",
        'agent_site.interface_person AS interface_person',
        'aweme_list.aweme_name AS aweme_name',
        'aweme_list.aweme_id AS aweme_account'
    ];
    public $join_aweme_log_on = [];
    //是否需要连接
    public $need_join_inefficient = false;
    //直播分摊， 2按子 3按根 4回流
    public $live_statistics_type = 2;
    //官网分摊 5按子 6按根 7回流
    public $official_statistics_type = 5;
    //自然量分摊 2按子 3按根 4回流
    public $natural_statistics_type = 2;

    public $overview_live_apportion_default_fields = [];
    public $overview_official_apportion_default_fields = [];
    public $overview_natural_apportion_default_fields = [];
    public $data_live_apportion_default_fields = [];

    // 计算分摊拓展的连表字段
    public $data_ad3_apportion_join_field = '';

    /**
     * 处理不同media_type的select group by where数据
     */
    public function paramHook()
    {
        if (3 === (int)$this->level && in_array('mini_game_os', $this->dimension)) {
            throw new AppException('小游戏系统细分不可与三级维度同时选择');
        }

        if (0 === (int)$this->media_type &&
            isset($this->filter['media_type']) && 1 === count($this->filter['media_type']['value']) &&
            in_array((int)$this->filter['media_type']['value'][0], [MediaType::TOUTIAO, MediaType::TENCENT, MediaType::BAIDU])) {
            $this->media_type = (int)$this->filter['media_type']['value'][0];
        } else {
            $this->media_type = (int)$this->media_type;
        }

        $this->handleCustomizedTarget();
//        $filter_fields = $calc_fields = [];
        if (!empty($this->filter)) {
            foreach ($this->filter as $value) {
                $this->filter_fields[] = $value['column'];
            }
            unset($value);
            if (isset($this->filter['port_version'])) {
                if (!in_array($this->media_type, [0, MediaType::TOUTIAO, MediaType::TENCENT])) {
                    throw new AppException('非通用或头条媒体, 不可筛选版本条件');
                }

                $key = array_search('1', $this->filter['port_version']['value']);
                if (false !== $key) {
                    $this->filter['port_version']['value'][$key] = '';
                }
            }
        }
        //组合having语句并设置参数绑定
        if (!empty($this->calc)) {
            $this->calc_condition = $this->calc['condition'];
            unset($this->calc['condition']);
            foreach ($this->calc as $v) {
                $this->calc_fields[] = $v['column'];
                $v['condition'] = $v['operator'];
                $this->main_calc[] = SqlParser::get($v);
            }
            unset($v);
        }

        $this->all_fields = array_merge($this->target, $this->dimension, $this->filter_fields, $this->calc_fields);

        //小游戏系统细分不可与三级维度聚合/筛选同时选择
        if (in_array('mini_game_os', $this->dimension) && array_intersect($this->all_fields, ADAnalysisSqlMap::THIRD_LEVEL_FIELDS)) {
            throw new AppException('小游戏系统细分不可与三级维度聚合/筛选同时选择');
        }
        //检查media_type
        $fields_str = implode(',', $this->all_fields);
        if (false !== strpos($fields_str, 'gdt_') && false !== strpos($fields_str, 'tt_')) {
            throw new AppException('media_type参数错误');
        } elseif (false !== strpos($fields_str, 'gdt_')) {
            $this->media_type = MediaType::TENCENT;
        } elseif (false !== strpos($fields_str, 'tt_') || false !== strpos($fields_str, 'is_inefficient')) {
            $this->media_type = MediaType::TOUTIAO;
        }

        if (!in_array($this->media_type, [0, MediaType::TOUTIAO, MediaType::TENCENT, MediaType::KUAISHOU, MediaType::BAIDU])) {
            throw new AppException('media_type参数错误');
        }

        if (in_array('aweme_account_id', $this->all_fields) && !in_array($this->media_type, [0, MediaType::TOUTIAO, MediaType::KUAISHOU])) {
            throw new AppException('直播间聚合仅"通用媒体"、"头条"、"快手"可用');
        }

        if (in_array('mini_game_os', $this->all_fields) && !in_array($this->media_type, [0, MediaType::TOUTIAO, MediaType::KUAISHOU])) {
            throw new AppException('小游戏系统细分仅"通用媒体"、"头条"、"快手"可用');
        }

        //如果是按根统计，更换overview_log表
        if (2 === (int)$this->statistic_base_on) {
            $this->live_statistics_type = 3;
            $this->official_statistics_type = 6;
            $this->natural_statistics_type = 3;
            $this->overview_log_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_ads_day_root_game_overview_log';
        } elseif (3 === (int)$this->statistic_base_on) {
            $this->live_statistics_type = 4;
            $this->official_statistics_type = 7;
            $this->natural_statistics_type = 4;
            $this->overview_log_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_ads_day_root_game_back_overview_log';
        }

        //分摊

        //头条穿山甲流量细分
//        if (in_array('tt_rit', $this->dimension)) {
//            $this->toutiao_data_log_map = ADAnalysisSqlMap::TOUTIAO_RIT_DATA_LOG;
//            $this->toutiao_main_compute_map = ADAnalysisSqlMap::TOUTIAO_RIT_MAIN_COMPUTE;
//            $this->toutiao_data_log_table = 'ods_toutiao_rit_day_data_log';
//            $this->toutiao_data_log_join_overview_on = $this->toutiao_rit_data_log_join_overview_on;
//            if ($need_filter_fields = array_intersect($this->target, $this->filter_target_for_rit)) {
//                $this->target = array_diff($this->target, $need_filter_fields);
//            }
//        } else {
        $this->toutiao_data_log_map = ADAnalysisSqlMap::TOUTIAO_DATA_LOG;
        $this->toutiao_main_compute_map = ADAnalysisSqlMap::TOUTIAO_MAIN_COMPUTE;
        if (!in_array('mini_game_os', $this->all_fields)) {
            $this->calc_standard_ad2_field = ADAnalysisSqlMap::CALC_STANDARD_AD3_FIELD[$this->media_type];
            $this->sub_data_log_groups[] = 'standard_ad3_id';
            $this->sub_overview_log_groups[] = 'standard_ad3_id';
            $this->sub_main_join[] = 'standard_ad3_id';
        }
//        }

        if (array_intersect($this->dimension, ['agent_leader', 'site_id', 'os', 'game_id', 'main_game_id', 'root_game_id']) &&
            !in_array('platform', $this->dimension)) {
            $this->dimension[] = 'platform';
        }

        if (in_array('gdt_inventory_subdivision', $this->all_fields) &&
            in_array('gdt_is_expand_targeting', $this->all_fields)) {
            throw new AppException('广点通资源位细分和自动扩量细分维度不可同时聚合查看');
        }

        if (($this->natural_apportion || $this->live_apportion) && array_intersect(['mini_game_os', 'tt_inventory_subdivision', 'gdt_inventory_subdivision', 'gdt_xqxs_inventory_subdivision', 'gdt_is_expand_targeting'], $this->all_fields)) {
            throw new AppException('细分维度和分摊不可同时选择');
        }
        //手动放入company, 用于批量修改定向功能
//        $this->all_fields[] = 'company';
        //始终返回account_id
        $this->all_fields[] = 'account_id';
        //如果有广告名称、账户名称，手动放入广告id和account_id
        if (in_array('ad1_name', $this->dimension) || in_array('ad1_id', $this->dimension)) {
            $this->all_fields[] = 'port_version';
            $this->all_fields[] = 'project_id';
            if ( in_array( 'ad1_name', $this->all_fields ) && ! in_array( 'ad1_id', $this->all_fields ) ) {
                $this->all_fields[] = 'ad1_id';
            } elseif ( ! in_array( 'ad1_name', $this->all_fields ) && in_array( 'ad1_id', $this->all_fields ) ) {
                $this->all_fields[] = 'ad1_name';

            }
        }

        if (in_array('ad2_name', $this->dimension) || in_array('ad2_id', $this->dimension)) {
            $this->all_fields[] = 'site_id';
            if (in_array('ad2_name', $this->dimension) && !in_array('ad2_id', $this->dimension)) {
                $this->all_fields[] = 'ad2_id';
            } elseif (!in_array('ad2_name', $this->dimension) && in_array('ad2_id', $this->dimension)) {
                $this->all_fields[] = 'ad2_name';
            }
        }
        if (in_array('ad3_name', $this->all_fields) && !in_array('ad3_id', $this->all_fields)) {
            $this->all_fields[] = 'ad3_id';
        } elseif (!in_array('ad3_name', $this->all_fields) && in_array('ad3_id', $this->all_fields)) {
            $this->all_fields[] = 'ad3_name';
        }
        if (in_array('account_name', $this->all_fields) && !in_array('account_id', $this->all_fields)) {
            $this->all_fields[] = 'account_id';
        } elseif (!in_array('account_name', $this->all_fields) &&
            in_array('account_id', $this->all_fields)) {
            $this->all_fields[] = 'account_name';
        }
        if (in_array('ad2_id', $this->all_fields)) {
            $this->all_fields[] = 'ad1_name';
            $this->all_fields[] = 'ad1_id';
            $this->all_fields[] = 'port_version';
        }
        if (in_array('ad3_id', $this->all_fields)) {
            $this->all_fields[] = 'ad1_name';
            $this->all_fields[] = 'ad1_id';
            $this->all_fields[] = 'ad2_name';
            $this->all_fields[] = 'ad2_id';
            $this->all_fields[] = 'port_version';
        }

        //手动把media_type放进去
        if (!in_array('media_type', $this->all_fields)) {
            $this->all_fields[] = 'media_type';
        }
        //手动把放platform进去
        if (!in_array('platform', $this->all_fields)) {
            $this->all_fields[] = 'platform';
        }
        if (in_array('material_file_id', $this->all_fields)) {
            $this->all_fields[] = 'material_file_name';
        }
        if (in_array('material_id', $this->all_fields)) {
            $this->all_fields[] = 'material_name';
        }
        //将生成缩略图所需的字段放入
        if (in_array('urls', $this->all_fields)) {
            if (!in_array('signature', $this->all_fields)) {
                $this->all_fields[] = 'signature';
            }
            $this->all_fields[] = 'file_type';
        }

        //聚合game_id的时候将game_name一起查出来
        if (in_array('game_id', $this->all_fields)) {
            $this->all_fields[] = 'game_name';
        }
        if (in_array('main_game_id', $this->all_fields)) {
            $this->all_fields[] = 'main_game_name';
        }
        if (in_array('root_game_id', $this->all_fields)) {
            $this->all_fields[] = 'root_game_name';
        }
        //有site_id时把site_name也放进去
        if (in_array('site_id', $this->all_fields)) {
            $this->all_fields[] = 'site_name';
        }
        //有agent_id时把agent_name也放进去
        if (in_array('agent_id', $this->all_fields)) {
            $this->all_fields[] = 'agent_name';
        }
        //有aweme_account时把aweme_name也放进去
        if (in_array('aweme_account_id', $this->all_fields)) {
            $this->all_fields[] = 'aweme_account_name';
        }
        //有agent_group_id时把agent_group_name也放进去
        if (in_array('agent_group_id', $this->all_fields)) {
            $this->all_fields[] = 'agent_group_name';
        }
        //有标签或细分标签，就要有material_id
        if (in_array('label_pid', $this->all_fields) || in_array('label', $this->all_fields)) {
            $this->all_fields[] = 'material_id';
        }
        //头条资源位细分
        if (in_array('tt_inventory_subdivision', $this->all_fields)) {
            $this->toutiao_data_log_table = 'ods_toutiao_inventory_hour_data_log';
            $this->sub_data_log_groups[] = 'tt_inventory_subdivision';
            $this->sub_overview_log_groups[] = 'tt_inventory_subdivision';
            $this->sub_main_join[] = 'tt_inventory_subdivision';
        }
        //腾讯资源位细分
        if (in_array('gdt_inventory_subdivision', $this->all_fields)) {
            $this->tencent_data_log_table = 'ods_tencent_ad_site_set_day_data_log';
            $this->sub_data_log_groups[] = 'gdt_inventory_subdivision';
            $this->sub_overview_log_groups[] = 'gdt_inventory_subdivision';
            $this->sub_main_join[] = 'gdt_inventory_subdivision';
        }
        //腾讯资源位xqxs细分
        if (in_array('gdt_xqxs_inventory_subdivision', $this->all_fields)) {
            $this->tencent_data_log_table = 'ods_tencent_ad_site_set_day_data_log';
            $this->sub_data_log_groups[] = 'gdt_xqxs_inventory_subdivision';
            $this->sub_overview_log_groups[] = 'gdt_xqxs_inventory_subdivision';
            $this->sub_main_join[] = 'gdt_xqxs_inventory_subdivision';
        }
        //腾讯是否自动扩量
        if (in_array('gdt_is_expand_targeting', $this->all_fields)) {
            //自动扩量无法有媒体通用转化付费相关数据
            $this->all_fields = array_diff($this->all_fields, ['cost_per_pay', 'pay_count', 'pay_rate']);
            $this->tencent_data_log_table = 'ods_tencent_ad_expand_target_day_data_log';
            $this->sub_data_log_groups[] = 'gdt_is_expand_targeting';
            $this->sub_overview_log_groups[] = 'gdt_is_expand_targeting';
            $this->sub_main_join[] = 'gdt_is_expand_targeting';
        }

        //特殊的构建where条件时要在前面加platform的字段
        $special_fields = ['game_id', 'site_id', 'main_game_id', 'root_game_id', 'material_id', 'agent_id', 'agent_group_id'];
        //如果选择细分标签并且选择主标签，则将主标签unset掉
        if (isset($this->filter['label_pid']) && isset($this->filter['label'])) {
            unset($this->filter['label_pid']);
        }
        //存在广告一二三级的时候才允许有switch
        if (!array_intersect(['ad1_id', 'ad1_name', 'ad2_id', 'ad2_name', 'ad3_id', 'ad3_name'], $this->dimension) &&
            $switch_pos = array_search('switch', $this->all_fields)) {
            unset($this->all_fields[$switch_pos]);
        }

        //塞入解绑rta所需字段
        if (in_array('rta_bind_status', $this->all_fields)) {
            $this->all_fields = array_merge($this->all_fields, ['rta_id', 'target_name']);
        }

        //根据所选指标放入扩展字段
        foreach ($this->all_fields as $item) {
            if (isset(ADAnalysisSqlMap::EXTENSION_FIELDS[$item])) {
                $this->all_fields = array_merge(
                    $this->all_fields,
                    ADAnalysisSqlMap::EXTENSION_FIELDS[$item]
                );
            }
        }
        //去重
        $this->all_fields = array_flip(array_flip($this->all_fields));

        switch ($this->media_type) {
            case MediaType::TOUTIAO:
                //获取要select的字段
                foreach ($this->all_fields as $item) {
                    if (isset(ADAnalysisSqlMap::TOUTIAO_AD_LOG[$item])) {
                        $this->toutiao_ad_log[$item] = ADAnalysisSqlMap::TOUTIAO_AD_LOG[$item];
                    }
                    if (isset($this->toutiao_data_log_map[$item])) {
                        $this->toutiao_data_log[$item] = $this->toutiao_data_log_map[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMPRESS_TOUTIAO_DATA_LOG[$item])) {
                        $this->compress_toutiao_data_log[$item] = ADAnalysisSqlMap::COMPRESS_TOUTIAO_DATA_LOG[$item];
                    }
                    if (isset($this->toutiao_main_compute_map[$item])) {
                        $this->main_compute[$item] = $this->toutiao_main_compute_map[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_ACCOUNT_LOG[$item])) {
                        $this->toutiao_account_log[$item] = ADAnalysisSqlMap::TOUTIAO_ACCOUNT_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_FIRST_AD_LOG[$item])) {
                        $this->first_ad_log_fields[$item] = ADAnalysisSqlMap::TOUTIAO_FIRST_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_THIRD_AD_LOG[$item])) {
                        $this->third_ad_log_fields[$item] = ADAnalysisSqlMap::TOUTIAO_THIRD_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::JOIN_TOUTIAO_AD_LOG_SELECT[$item])) {
                        $this->join_ad_log_select[$item] = ADAnalysisSqlMap::JOIN_TOUTIAO_AD_LOG_SELECT[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_RTA_BIND_LOG[$item])) {
                        $this->rta_bind_log[$item] = ADAnalysisSqlMap::TOUTIAO_RTA_BIND_LOG[$item];
                    }

                    $this->commonPartSelect($item);
                }
                unset($item);
                //获取要group by的字段
                foreach ($this->dimension as $v) {
                    if (isset(ADAnalysisSqlMap::TOUTIAO_AD_LOG_GROUP_BY[$v])) {
                        $this->toutiao_ad_log_group_by[$v] = ADAnalysisSqlMap::TOUTIAO_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_DATA_LOG_GROUP_BY[$v])) {
                        $this->toutiao_data_log_group_by[$v] = ADAnalysisSqlMap::TOUTIAO_DATA_LOG_GROUP_BY[$v];
                    }
//                    if (isset(ADAnalysisSqlMap::TOUTIAO_MAIN_DIMENSION[$v])) {
//                        $this->main_dimension[$v] = ADAnalysisSqlMap::TOUTIAO_MAIN_DIMENSION[$v];
//                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_ACCOUNT_LOG_GROUP_BY[$v])) {
                        $this->account_log_group_by[$v] = ADAnalysisSqlMap::TOUTIAO_ACCOUNT_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_FIRST_AD_LOG_GROUP_BY[$v])) {
                        $this->first_ad_log_group_by[$v] = ADAnalysisSqlMap::TOUTIAO_FIRST_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_THIRD_AD_LOG_GROUP_BY[$v])) {
                        $this->third_ad_log_group_by[$v] = ADAnalysisSqlMap::TOUTIAO_THIRD_AD_LOG_GROUP_BY[$v];
                    }

                    $this->commonPartGroupBy($v);
                }
                unset($v);
                //获取要where的字段
                foreach ($this->filter as $filter) {
                    if (in_array($filter['column'], $special_fields)) {
                        $filter['column'] = 'platform-' . $filter['column'];
                    }

                    if (isset(ADAnalysisSqlMap::TOUTIAO_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::TOUTIAO_AD_LOG_FILTER[$filter['column']],
                            'ad_log'
                        );
                        $this->toutiao_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::TOUTIAO_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']],
                            'data_log'
                        );
                        $this->ad_log_filter_for_data_log[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_DATA_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::TOUTIAO_DATA_LOG_FILTER[$filter['column']],
                            'data_log');
                        $this->toutiao_data_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_FIRST_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::TOUTIAO_FIRST_AD_LOG_FILTER[$filter['column']], 'first_ad_log');
                        $this->first_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_THIRD_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::TOUTIAO_THIRD_AD_LOG_FILTER[$filter['column']], 'third_ad_log');
                        $this->third_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_MAIN_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, '', 'ad_log');
                        $this->main_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_ACCOUNT_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::TOUTIAO_ACCOUNT_LOG_FILTER[$filter['column']],
                            'account_log');
                        $this->account_log_filter[] = $where;
                    }
                    $this->commonPartWhere($filter);
                }
                unset($filter);

                $this->commonPartOrderBy();

                $this->data_log_table = $this->toutiao_data_log_table;
                $this->ad_log_table = $this->toutiao_ad_log_table;
                $this->ad_log_fields = $this->toutiao_ad_log;
                $this->data_log_fields = $this->toutiao_data_log;
                $this->compress_data_log_fields = $this->compress_toutiao_data_log;
                $this->account_log_fields = $this->toutiao_account_log;
                $this->data_log_group_by = $this->toutiao_data_log_group_by;
                $this->ad_log_group_by = $this->toutiao_ad_log_group_by;
                $this->data_log_filter = $this->toutiao_data_log_filter;
                $this->ad_log_filter = $this->toutiao_ad_log_filter;
//                $this->data_log_join_fields = $this->toutiao_data_log_join_fields;
//                $this->ad_log_join_fields = $this->toutiao_ad_log_join_fields;
                $this->join_data_on = $this->toutiao_join_data_on;
//                $this->join_reg_on = $this->toutiao_ad_join_reg_on;
                $this->join_overview_on = $this->toutiao_ad_join_overview_on;
                $this->account_log_table = $this->toutiao_account_log_table;
//                $this->account_log_join_fields = $this->toutiao_account_join_fields;
                $this->first_ad_log_table = $this->toutiao_first_ad_log_table;
                $this->third_ad_log_table = $this->toutiao_third_ad_log_table;
//                $this->first_ad_log_join_fields = $this->toutiao_first_ad_log_join_fields;
//                $this->third_ad_log_join_fields = $this->toutiao_third_ad_Log_join_fields;
                $this->join_first_ad_log_on = $this->toutiao_join_first_ad_log_on;
                $this->join_third_ad_log_on = $this->toutiao_join_third_ad_log_on;
//                $this->ad_log_total_fields = $this->toutiao_all_join_fields;
                $this->ad_create_time_field = 'ad_log.ad_create_time';
                $this->ad3_create_time_field = 'third_ad_log.creative_create_time';
//                if (in_array('tt_rit', $this->dimension)) {
//                    $this->third_join_data_log_on = $this->toutiao_third_join_rit_data_log_on;
//                } else {
                $this->third_join_data_log_on = $this->toutiao_third_join_data_log_on;
                $this->rta_join_data_log_on = $this->toutiao_rta_join_data_log_on;
                $this->rta_join_overview_log_on = $this->toutiao_rta_join_overview_log_on;

//                }
//                $this->third_join_reg_log_on = $this->toutiao_third_join_reg_log_on;
                $this->third_join_overview_log_on = $this->toutiao_third_join_overview_log_on;
                $this->first_ad_order_by = $this->toutiao_first_ad_order_by;
                $this->second_ad_order_by = $this->toutiao_second_ad_order_by;
                $this->third_ad_order_by = $this->toutiao_third_ad_order_by;
                $this->first_ad_order_by_select = $this->toutiao_first_ad_order_by_select;
                $this->second_ad_order_by_select = $this->toutiao_second_ad_order_by_select;
                $this->third_ad_order_by_select = $this->toutiao_third_ad_order_by_select;
                $this->first_ad_switch = $this->toutiao_first_ad_switch;
                $this->second_ad_switch = $this->toutiao_second_ad_switch;
                $this->third_ad_switch = $this->toutiao_third_ad_switch;
                $this->join_calc_bind_on = $this->toutiao_join_calc_rule_on;
                $this->overview_join_data_log_on = $this->toutiao_data_log_join_overview_on;
                $this->join_rta_bind_on = $this->toutiao_join_rta_bind_on;
                $this->rta_bind_table = 'ods_toutiao_rta_target_bind_list_log';
                $this->join_aweme_log_on = ADAnalysisSqlMap::JOIN_AWEME_LOG_ON[$this->media_type];
                break;
            case MediaType::TENCENT:
                //获取要select的字段
                foreach ($this->all_fields as $item) {
                    if (isset(ADAnalysisSqlMap::TENCENT_AD_LOG[$item])) {
                        $this->tencent_ad_log[$item] = ADAnalysisSqlMap::TENCENT_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_DATA_LOG[$item])) {
                        $this->tencent_data_log[$item] = ADAnalysisSqlMap::TENCENT_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMPRESS_TENCENT_DATA_LOG[$item])) {
                        $this->compress_tencent_data_log[$item] = ADAnalysisSqlMap::COMPRESS_TENCENT_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_MAIN_COMPUTE[$item])) {
                        $this->main_compute[$item] = ADAnalysisSqlMap::TENCENT_MAIN_COMPUTE[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_ACCOUNT_LOG[$item])) {
                        $this->tencent_account_log[$item] = ADAnalysisSqlMap::TENCENT_ACCOUNT_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_FIRST_AD_LOG[$item])) {
                        $this->first_ad_log_fields[$item] = ADAnalysisSqlMap::TENCENT_FIRST_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_THIRD_AD_LOG[$item])) {
                        $this->third_ad_log_fields[$item] = ADAnalysisSqlMap::TENCENT_THIRD_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::JOIN_TENCENT_AD_LOG_SELECT[$item])) {
                        $this->join_ad_log_select[$item] = ADAnalysisSqlMap::JOIN_TENCENT_AD_LOG_SELECT[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_RTA_BIND_LOG[$item])) {
                        $this->rta_bind_log[$item] = ADAnalysisSqlMap::TENCENT_RTA_BIND_LOG[$item];
                    }
                    $this->commonPartSelect($item);
                }
                unset($item);
                //获取要group by的字段
                foreach ($this->dimension as $v) {
                    if (isset(ADAnalysisSqlMap::TENCENT_AD_LOG_GROUP_BY[$v])) {
                        $this->tencent_ad_log_group_by[$v] = ADAnalysisSqlMap::TENCENT_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_DATA_LOG_GROUP_BY[$v])) {
                        $this->tencent_data_log_group_by[$v] = ADAnalysisSqlMap::TENCENT_DATA_LOG_GROUP_BY[$v];
                    }
//                    if (isset(ADAnalysisSqlMap::TENCENT_MAIN_DIMENSION[$v])) {
//                        $this->main_dimension[$v] = ADAnalysisSqlMap::TENCENT_MAIN_DIMENSION[$v];
//                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_ACCOUNT_LOG_GROUP_BY[$v])) {
                        $this->account_log_group_by[$v] = ADAnalysisSqlMap::TENCENT_ACCOUNT_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_FIRST_AD_LOG_GROUP_BY[$v])) {
                        $this->first_ad_log_group_by[$v] = ADAnalysisSqlMap::TENCENT_FIRST_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_THIRD_AD_LOG_GROUP_BY[$v])) {
                        $this->third_ad_log_group_by[$v] = ADAnalysisSqlMap::TENCENT_THIRD_AD_LOG_GROUP_BY[$v];
                    }
                    $this->commonPartGroupBy($v);
                }
                unset($v);

                //获取要where的字段
                foreach ($this->filter as $filter) {
                    if (in_array($filter['column'], $special_fields)) {
                        $filter['column'] = 'platform-' . $filter['column'];
                    }

                    if (isset(ADAnalysisSqlMap::TENCENT_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::TENCENT_AD_LOG_FILTER[$filter['column']],
                            'ad_log'
                        );
                        $this->tencent_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::TENCENT_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']],
                            'data_log'
                        );
                        $this->ad_log_filter_for_data_log[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_DATA_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::TENCENT_DATA_LOG_FILTER[$filter['column']],
                            'data_log');
                        $this->tencent_data_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_FIRST_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::TENCENT_FIRST_AD_LOG_FILTER[$filter['column']], 'first_ad_log');
                        $this->first_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_THIRD_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::TENCENT_THIRD_AD_LOG_FILTER[$filter['column']], 'third_ad_log');
                        $this->third_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_MAIN_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, '', 'ad_log');
                        $this->main_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_ACCOUNT_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::TENCENT_ACCOUNT_LOG_FILTER[$filter['column']],
                            'account_log');
                        $this->account_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_OVERVIEW_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::TENCENT_OVERVIEW_LOG_FILTER[$filter['column']],
                            'overview_log');
                        $this->overview_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::AD_LOG_PRIVATE_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::AD_LOG_PRIVATE_FILTER[$filter['column']], 'ad_log');
                        $this->ad_log_private_filter[] = $where;
                    }
                    $this->commonPartWhere($filter);
                }
                unset($filter);

                $this->commonPartOrderBy();

                $this->data_log_table = $this->tencent_data_log_table;
                $this->ad_log_table = $this->tencent_ad_log_table;
                $this->ad_log_fields = $this->tencent_ad_log;
                $this->data_log_fields = $this->tencent_data_log;
                $this->compress_data_log_fields = $this->compress_tencent_data_log;
                $this->account_log_fields = $this->tencent_account_log;
                $this->data_log_group_by = $this->tencent_data_log_group_by;
                $this->ad_log_group_by = $this->tencent_ad_log_group_by;
                $this->data_log_filter = $this->tencent_data_log_filter;
                $this->ad_log_filter = $this->tencent_ad_log_filter;
                $this->rta_join_data_log_on = $this->tencent_rta_join_data_log_on;
                $this->rta_join_overview_log_on = $this->tencent_rta_join_overview_log_on;
//                $this->data_log_join_fields = $this->tencent_data_log_join_fields;
//                $this->ad_log_join_fields = $this->tencent_ad_log_join_fields;
                $this->join_data_on = $this->tencent_join_data_on;
//                $this->join_reg_on = $this->tencent_ad_join_reg_on;
                $this->join_overview_on = $this->tencent_ad_join_overview_on;
                $this->account_log_table = $this->tencent_account_log_table;
//                $this->account_log_join_fields = $this->tencent_account_join_fields;
                $this->first_ad_log_table = $this->tencent_first_ad_log_table;
                $this->third_ad_log_table = $this->tencent_third_ad_log_table;
//                $this->first_ad_log_join_fields = $this->tencent_first_ad_log_join_fields;
//                $this->third_ad_log_join_fields = $this->tencent_third_ad_log_join_fields;
                $this->join_first_ad_log_on = $this->tencent_join_first_ad_log_on;
                $this->join_third_ad_log_on = $this->tencent_join_third_ad_log_on;
//                $this->ad_log_total_fields = $this->tencent_all_join_fields;
                $this->ad_create_time_field = 'ad_log.created_time';
                $this->ad3_create_time_field = 'third_ad_log.adcreative_created_time';
                $this->third_join_data_log_on = $this->tencent_third_join_data_log_on;
//                $this->third_join_reg_log_on = $this->tencent_third_join_reg_log_on;
                $this->third_join_overview_log_on = $this->tencent_third_join_overview_log_on;
                $this->first_ad_order_by = $this->tencent_first_ad_order_by;
                $this->second_ad_order_by = $this->tencent_second_ad_order_by;
                $this->third_ad_order_by = $this->tencent_third_ad_order_by;
                $this->first_ad_order_by_select = $this->tencent_first_ad_order_by_select;
                $this->second_ad_order_by_select = $this->tencent_second_ad_order_by_select;
                $this->third_ad_order_by_select = $this->tencent_third_ad_order_by_select;
                $this->first_ad_switch = $this->tencent_first_ad_switch;
                $this->second_ad_switch = $this->tencent_second_ad_switch;
                $this->third_ad_switch = $this->tencent_third_ad_switch;
                $this->join_calc_bind_on = $this->tencent_join_calc_rule_on;
                $this->overview_join_data_log_on = $this->tencent_data_log_join_overview_on;
                $this->join_rta_bind_on = $this->tencent_join_rta_bind_on;
                $this->rta_bind_table = 'ods_tencent_rta_target_bind_list_log';
                break;
            case MediaType::KUAISHOU:
                //获取要select的字段
                foreach ($this->all_fields as $item) {
                    if (isset(ADAnalysisSqlMap::KUAISHOU_AD_LOG[$item])) {
                        $this->kuaishou_ad_log[$item] = ADAnalysisSqlMap::KUAISHOU_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_DATA_LOG[$item])) {
                        $this->kuaishou_data_log[$item] = ADAnalysisSqlMap::KUAISHOU_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMPRESS_KUAISHOU_DATA_LOG[$item])) {
                        $this->compress_kuaishou_data_log[$item] = ADAnalysisSqlMap::COMPRESS_KUAISHOU_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_MAIN_COMPUTE[$item])) {
                        $this->main_compute[$item] = ADAnalysisSqlMap::KUAISHOU_MAIN_COMPUTE[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_ACCOUNT_LOG[$item])) {
                        $this->kuaishou_account_log[$item] = ADAnalysisSqlMap::KUAISHOU_ACCOUNT_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_FIRST_AD_LOG[$item])) {
                        $this->first_ad_log_fields[$item] = ADAnalysisSqlMap::KUAISHOU_FIRST_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_THIRD_AD_LOG[$item])) {
                        $this->third_ad_log_fields[$item] = ADAnalysisSqlMap::KUAISHOU_THIRD_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::JOIN_KUAISHOU_AD_LOG_SELECT[$item])) {
                        $this->join_ad_log_select[$item] = ADAnalysisSqlMap::JOIN_KUAISHOU_AD_LOG_SELECT[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_RTA_BIND_LOG[$item])) {
                        $this->rta_bind_log[$item] = ADAnalysisSqlMap::KUAISHOU_RTA_BIND_LOG[$item];
                    }
                    $this->commonPartSelect($item);
                }
                unset($item);
                //获取要group by的字段
                foreach ($this->dimension as $v) {
                    if (isset(ADAnalysisSqlMap::KUAISHOU_AD_LOG_GROUP_BY[$v])) {
                        $this->kuaishou_ad_log_group_by[$v] = ADAnalysisSqlMap::KUAISHOU_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_DATA_LOG_GROUP_BY[$v])) {
                        $this->kuaishou_data_log_group_by[$v] = ADAnalysisSqlMap::KUAISHOU_DATA_LOG_GROUP_BY[$v];
                    }
//                    if (isset(ADAnalysisSqlMap::KUAISHOU_MAIN_DIMENSION[$v])) {
//                        $this->main_dimension[$v] = ADAnalysisSqlMap::KUAISHOU_MAIN_DIMENSION[$v];
//                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_ACCOUNT_LOG_GROUP_BY[$v])) {
                        $this->account_log_group_by[$v] = ADAnalysisSqlMap::KUAISHOU_ACCOUNT_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_FIRST_AD_LOG_GROUP_BY[$v])) {
                        $this->first_ad_log_group_by[$v] = ADAnalysisSqlMap::KUAISHOU_FIRST_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_THIRD_AD_LOG_GROUP_BY[$v])) {
                        $this->third_ad_log_group_by[$v] = ADAnalysisSqlMap::KUAISHOU_THIRD_AD_LOG_GROUP_BY[$v];
                    }
                    $this->commonPartGroupBy($v);
                }
                unset($v);

                //获取要where的字段
                foreach ($this->filter as $filter) {
                    if (in_array($filter['column'], $special_fields)) {
                        $filter['column'] = 'platform-' . $filter['column'];
                    }

                    if (isset(ADAnalysisSqlMap::KUAISHOU_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::KUAISHOU_AD_LOG_FILTER[$filter['column']],
                            'ad_log'
                        );
                        $this->kuaishou_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::KUAISHOU_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']],
                            'data_log'
                        );
                        $this->ad_log_filter_for_data_log[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_DATA_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::KUAISHOU_DATA_LOG_FILTER[$filter['column']],
                            'data_log');
                        $this->kuaishou_data_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_FIRST_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::KUAISHOU_FIRST_AD_LOG_FILTER[$filter['column']], 'first_ad_log');
                        $this->first_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_THIRD_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::KUAISHOU_THIRD_AD_LOG_FILTER[$filter['column']], 'third_ad_log');
                        $this->third_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_MAIN_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, '', 'ad_log');
                        $this->main_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_ACCOUNT_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::KUAISHOU_ACCOUNT_LOG_FILTER[$filter['column']],
                            'account_log');
                        $this->account_log_filter[] = $where;
                    }
                    $this->commonPartWhere($filter);
                }
                unset($filter);

                $this->commonPartOrderBy();

                $this->data_log_table = $this->kuaishou_data_log_table;
                $this->ad_log_table = $this->kuaishou_ad_log_table;
                $this->ad_log_fields = $this->kuaishou_ad_log;
                $this->data_log_fields = $this->kuaishou_data_log;
                $this->compress_data_log_fields = $this->compress_kuaishou_data_log;
                $this->account_log_fields = $this->kuaishou_account_log;
                $this->data_log_group_by = $this->kuaishou_data_log_group_by;
                $this->ad_log_group_by = $this->kuaishou_ad_log_group_by;
                $this->data_log_filter = $this->kuaishou_data_log_filter;
                $this->ad_log_filter = $this->kuaishou_ad_log_filter;
                $this->rta_join_data_log_on = $this->kuaishou_rta_join_data_log_on;
                $this->rta_join_overview_log_on = $this->kuaishou_rta_join_overview_log_on;
//                $this->data_log_join_fields = $this->kuaishou_data_log_join_fields;
//                $this->ad_log_join_fields = $this->kuaishou_ad_log_join_fields;
                $this->join_data_on = $this->kuaishou_join_data_on;
//                $this->join_reg_on = $this->kuaishou_ad_join_reg_on;
                $this->join_overview_on = $this->kuaishou_ad_join_overview_on;
                $this->account_log_table = $this->kuaishou_account_log_table;
//                $this->account_log_join_fields = $this->kuaishou_account_join_fields;
                $this->first_ad_log_table = $this->kuaishou_first_ad_log_table;
                $this->third_ad_log_table = $this->kuaishou_third_ad_log_table;
//                $this->first_ad_log_join_fields = $this->kuaishou_first_ad_log_join_fields;
//                $this->third_ad_log_join_fields = $this->kuaishou_third_ad_log_join_fields;
                $this->join_first_ad_log_on = $this->kuaishou_join_first_ad_log_on;
                $this->join_third_ad_log_on = $this->kuaishou_join_third_ad_log_on;
//                $this->ad_log_total_fields = $this->kuaishou_all_join_fields;
                $this->ad_create_time_field = 'ad_log.ad_create_time';
                $this->ad3_create_time_field = 'third_ad_log.creative_create_time';
                $this->third_join_data_log_on = $this->kuaishou_third_join_data_log_on;
//                $this->third_join_reg_log_on = $this->kuaishou_third_join_reg_log_on;
                $this->third_join_overview_log_on = $this->kuaishou_third_join_overview_log_on;
                $this->first_ad_order_by = $this->kuaishou_first_ad_order_by;
                $this->second_ad_order_by = $this->kuaishou_second_ad_order_by;
                $this->third_ad_order_by = $this->kuaishou_third_ad_order_by;
                $this->first_ad_order_by_select = $this->kuaishou_first_ad_order_by_select;
                $this->second_ad_order_by_select = $this->kuaishou_second_ad_order_by_select;
                $this->third_ad_order_by_select = $this->kuaishou_third_ad_order_by_select;
                $this->first_ad_switch = $this->kuaishou_first_ad_switch;
                $this->second_ad_switch = $this->kuaishou_second_ad_switch;
                $this->third_ad_switch = $this->kuaishou_third_ad_switch;
                $this->join_calc_bind_on = $this->kuaishou_join_calc_rule_on;
                $this->overview_join_data_log_on = $this->kuaishou_data_log_join_overview_on;
                $this->join_rta_bind_on = $this->kuaishou_join_rta_bind_on;
                $this->rta_bind_table = 'ods_kuaishou_rta_target_bind_list_log';
                break;
            case MediaType::BAIDU:
                //获取要select的字段
                foreach ($this->all_fields as $item) {
                    if (isset(ADAnalysisSqlMap::BAIDU_AD_LOG[$item])) {
                        $this->baidu_ad_log[$item] = ADAnalysisSqlMap::BAIDU_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_DATA_LOG[$item])) {
                        $this->baidu_data_log[$item] = ADAnalysisSqlMap::BAIDU_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMPRESS_BAIDU_DATA_LOG[$item])) {
                        $this->compress_baidu_data_log[$item] = ADAnalysisSqlMap::COMPRESS_BAIDU_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_MAIN_COMPUTE[$item])) {
                        $this->main_compute[$item] = ADAnalysisSqlMap::BAIDU_MAIN_COMPUTE[$item];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_ACCOUNT_LOG[$item])) {
                        $this->baidu_account_log[$item] = ADAnalysisSqlMap::BAIDU_ACCOUNT_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_FIRST_AD_LOG[$item])) {
                        $this->first_ad_log_fields[$item] = ADAnalysisSqlMap::BAIDU_FIRST_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_THIRD_AD_LOG[$item])) {
                        $this->third_ad_log_fields[$item] = ADAnalysisSqlMap::BAIDU_THIRD_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::JOIN_BAIDU_AD_LOG_SELECT[$item])) {
                        $this->join_ad_log_select[$item] = ADAnalysisSqlMap::JOIN_BAIDU_AD_LOG_SELECT[$item];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_RTA_BIND_LOG[$item])) {
                        $this->rta_bind_log[$item] = ADAnalysisSqlMap::BAIDU_RTA_BIND_LOG[$item];
                    }
                    $this->commonPartSelect($item);
                }
                unset($item);
                //获取要group by的字段
                foreach ($this->dimension as $v) {
                    if (isset(ADAnalysisSqlMap::BAIDU_AD_LOG_GROUP_BY[$v])) {
                        $this->baidu_ad_log_group_by[$v] = ADAnalysisSqlMap::BAIDU_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_DATA_LOG_GROUP_BY[$v])) {
                        $this->baidu_data_log_group_by[$v] = ADAnalysisSqlMap::BAIDU_DATA_LOG_GROUP_BY[$v];
                    }
//                    if (isset(ADAnalysisSqlMap::BAIDU_MAIN_DIMENSION[$v])) {
//                        $this->main_dimension[$v] = ADAnalysisSqlMap::BAIDU_MAIN_DIMENSION[$v];
//                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_ACCOUNT_LOG_GROUP_BY[$v])) {
                        $this->account_log_group_by[$v] = ADAnalysisSqlMap::BAIDU_ACCOUNT_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_FIRST_AD_LOG_GROUP_BY[$v])) {
                        $this->first_ad_log_group_by[$v] = ADAnalysisSqlMap::BAIDU_FIRST_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_THIRD_AD_LOG_GROUP_BY[$v])) {
                        $this->third_ad_log_group_by[$v] = ADAnalysisSqlMap::BAIDU_THIRD_AD_LOG_GROUP_BY[$v];
                    }
                    $this->commonPartGroupBy($v);
                }
                unset($v);

                //获取要where的字段
                foreach ($this->filter as $filter) {
                    if (in_array($filter['column'], $special_fields)) {
                        $filter['column'] = 'platform-' . $filter['column'];
                    }

                    if (isset(ADAnalysisSqlMap::BAIDU_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::BAIDU_AD_LOG_FILTER[$filter['column']],
                            'ad_log'
                        );
                        $this->baidu_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::BAIDU_AD_LOG_FILTER_FOR_DATA_LOG[$filter['column']],
                            'data_log'
                        );
                        $this->ad_log_filter_for_data_log[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_DATA_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::BAIDU_DATA_LOG_FILTER[$filter['column']],
                            'data_log');
                        $this->baidu_data_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_FIRST_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::BAIDU_FIRST_AD_LOG_FILTER[$filter['column']], 'first_ad_log');
                        $this->first_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_THIRD_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::BAIDU_THIRD_AD_LOG_FILTER[$filter['column']], 'third_ad_log');
                        $this->third_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_MAIN_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, '', 'ad_log');
                        $this->main_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::BAIDU_ACCOUNT_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::BAIDU_ACCOUNT_LOG_FILTER[$filter['column']],
                            'account_log');
                        $this->account_log_filter[] = $where;
                    }
                    $this->commonPartWhere($filter);
                }
                unset($filter);

                $this->commonPartOrderBy();

                $this->data_log_table = $this->baidu_data_log_table;
                $this->ad_log_table = $this->baidu_ad_log_table;
                $this->ad_log_fields = $this->baidu_ad_log;
                $this->data_log_fields = $this->baidu_data_log;
                $this->compress_data_log_fields = $this->compress_baidu_data_log;
                $this->account_log_fields = $this->baidu_account_log;
                $this->data_log_group_by = $this->baidu_data_log_group_by;
                $this->ad_log_group_by = $this->baidu_ad_log_group_by;
                $this->data_log_filter = $this->baidu_data_log_filter;
                $this->ad_log_filter = $this->baidu_ad_log_filter;
                $this->rta_join_data_log_on = $this->baidu_rta_join_data_log_on;
                $this->rta_join_overview_log_on = $this->baidu_rta_join_overview_log_on;
//                $this->data_log_join_fields = $this->baidu_data_log_join_fields;
//                $this->ad_log_join_fields = $this->baidu_ad_log_join_fields;
                $this->join_data_on = $this->baidu_join_data_on;
//                $this->join_reg_on = $this->baidu_ad_join_reg_on;
                $this->join_overview_on = $this->baidu_ad_join_overview_on;
                $this->account_log_table = $this->baidu_account_log_table;
//                $this->account_log_join_fields = $this->baidu_account_join_fields;
                $this->first_ad_log_table = $this->baidu_first_ad_log_table;
                $this->third_ad_log_table = $this->baidu_third_ad_log_table;
//                $this->first_ad_log_join_fields = $this->baidu_first_ad_log_join_fields;
//                $this->third_ad_log_join_fields = $this->baidu_third_ad_log_join_fields;
                $this->join_first_ad_log_on = $this->baidu_join_first_ad_log_on;
                $this->join_third_ad_log_on = $this->baidu_join_third_ad_log_on;
//                $this->ad_log_total_fields = $this->baidu_all_join_fields;
                $this->ad_create_time_field = 'ad_log.ad_create_time';
                $this->ad3_create_time_field = 'third_ad_log.creative_create_time';
                $this->third_join_data_log_on = $this->baidu_third_join_data_log_on;
//                $this->third_join_reg_log_on = $this->baidu_third_join_reg_log_on;
                $this->third_join_overview_log_on = $this->baidu_third_join_overview_log_on;
                $this->first_ad_order_by = $this->baidu_first_ad_order_by;
                $this->second_ad_order_by = $this->baidu_second_ad_order_by;
                $this->third_ad_order_by = $this->baidu_third_ad_order_by;
                $this->first_ad_order_by_select = $this->baidu_first_ad_order_by_select;
                $this->second_ad_order_by_select = $this->baidu_second_ad_order_by_select;
                $this->third_ad_order_by_select = $this->baidu_third_ad_order_by_select;
                $this->first_ad_switch = $this->baidu_first_ad_switch;
                $this->second_ad_switch = $this->baidu_second_ad_switch;
                $this->third_ad_switch = $this->baidu_third_ad_switch;
                $this->join_calc_bind_on = $this->baidu_join_calc_rule_on;
                $this->overview_join_data_log_on = $this->baidu_data_log_join_overview_on;
                $this->join_rta_bind_on = $this->baidu_join_rta_bind_on;
                $this->rta_bind_table = 'ods_baidu_rta_target_bind_list_log';
                break;
            default:
                //获取要select的字段
                foreach ($this->all_fields as $item) {
                    if (isset(ADAnalysisSqlMap::COMMON_AD_LOG[$item])) {
                        $this->common_ad_log[$item] = ADAnalysisSqlMap::COMMON_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_DATA_LOG[$item])) {
                        $this->common_data_log[$item] = ADAnalysisSqlMap::COMMON_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMPRESS_COMMON_DATA_LOG[$item])) {
                        $this->compress_common_data_log[$item] = ADAnalysisSqlMap::COMPRESS_COMMON_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_MAIN_COMPUTE[$item])) {
                        $this->main_compute[$item] = ADAnalysisSqlMap::COMMON_MAIN_COMPUTE[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_ACCOUNT_LOG[$item])) {
                        $this->common_account_log[$item] = ADAnalysisSqlMap::COMMON_ACCOUNT_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_FIRST_AD_LOG[$item])) {
                        $this->first_ad_log_fields[$item] = ADAnalysisSqlMap::COMMON_FIRST_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_THIRD_AD_LOG[$item])) {
                        $this->third_ad_log_fields[$item] = ADAnalysisSqlMap::COMMON_THIRD_AD_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::JOIN_COMMON_AD_LOG_SELECT[$item])) {
                        $this->join_ad_log_select[$item] = ADAnalysisSqlMap::JOIN_COMMON_AD_LOG_SELECT[$item];
                    }
                    $this->commonPartSelect($item);
                }
                unset($item);
                //获取要group by的字段
                foreach ($this->dimension as $v) {
                    if (isset(ADAnalysisSqlMap::COMMON_AD_LOG_GROUP_BY[$v])) {
                        $this->common_ad_log_group_by[$v] = ADAnalysisSqlMap::COMMON_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_DATA_LOG_GROUP_BY[$v])) {
                        $this->common_data_log_group_by[$v] = ADAnalysisSqlMap::COMMON_DATA_LOG_GROUP_BY[$v];
                    }
//                    if (isset(ADAnalysisSqlMap::COMMON_MAIN_DIMENSION[$v])) {
//                        $this->main_dimension[$v] = ADAnalysisSqlMap::COMMON_MAIN_DIMENSION[$v];
//                    }
                    if (isset(ADAnalysisSqlMap::COMMON_ACCOUNT_LOG_GROUP_BY[$v])) {
                        $this->account_log_group_by[$v] = ADAnalysisSqlMap::COMMON_ACCOUNT_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_FIRST_AD_LOG_GROUP_BY[$v])) {
                        $this->first_ad_log_group_by[$v] = ADAnalysisSqlMap::COMMON_FIRST_AD_LOG_GROUP_BY[$v];
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_THIRD_AD_LOG_GROUP_BY[$v])) {
                        $this->third_ad_log_group_by[$v] = ADAnalysisSqlMap::COMMON_THIRD_AD_LOG_GROUP_BY[$v];
                    }
                    $this->commonPartGroupBy($v);

                }
                unset($v);

                //获取要where的字段
                foreach ($this->filter as $filter) {
                    if (in_array($filter['column'], $special_fields)) {
                        $filter['column'] = 'platform-' . $filter['column'];
                    }

                    if (isset(ADAnalysisSqlMap::COMMON_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get(
                            $filter,
                            ADAnalysisSqlMap::COMMON_AD_LOG_FILTER[$filter['column']],
                            'ad_log'
                        );
                        $this->common_ad_log_filter[$filter['column']] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_DATA_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::COMMON_DATA_LOG_FILTER[$filter['column']],
                            'data_log');
                        $this->common_data_log_filter[$filter['column']] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_FIRST_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::COMMON_FIRST_AD_LOG_FILTER[$filter['column']], 'first_ad_log');
                        $this->first_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_THIRD_AD_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter,
                            ADAnalysisSqlMap::COMMON_THIRD_AD_LOG_FILTER[$filter['column']], 'third_ad_log');
                        $this->third_ad_log_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_MAIN_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, '', 'ad_log');
                        $this->main_filter[] = $where;
                    }
                    if (isset(ADAnalysisSqlMap::COMMON_ACCOUNT_LOG_FILTER[$filter['column']])) {
                        $where = SqlParser::get($filter, ADAnalysisSqlMap::COMMON_ACCOUNT_LOG_FILTER[$filter['column']],
                            'account_log');
                        $this->account_log_filter[] = $where;
                    }
                    $this->commonPartWhere($filter);
                }
                unset($filter);

                $this->commonPartOrderBy();

                $this->data_log_table = $this->common_data_log_table;
                $this->ad_log_table = $this->common_ad_log_table;
                $this->ad_log_fields = $this->common_ad_log;
                $this->data_log_fields = $this->common_data_log;
                $this->compress_data_log_fields = $this->compress_common_data_log;
                $this->account_log_fields = $this->common_account_log;
                $this->data_log_group_by = $this->common_data_log_group_by;
                $this->ad_log_group_by = $this->common_ad_log_group_by;
                $this->data_log_filter = $this->common_data_log_filter;
                $this->ad_log_filter = $this->common_ad_log_filter;
                $this->rta_join_data_log_on = $this->common_rta_join_data_log_on;
                $this->rta_join_overview_log_on = $this->common_rta_join_overview_log_on;
//                $this->data_log_join_fields = $this->common_data_log_join_fields;
//                $this->ad_log_join_fields = $this->common_ad_log_join_fields;
                $this->join_data_on = $this->common_join_data_on;
//                $this->join_reg_on = $this->common_ad_join_reg_on;
                $this->join_overview_on = $this->common_ad_join_overview_on;
                $this->account_log_table = $this->common_account_log_table;
//                $this->account_log_join_fields = $this->common_account_join_fields;
                $this->first_ad_log_table = $this->common_first_ad_log_table;
                $this->third_ad_log_table = $this->common_third_ad_log_table;
//                $this->first_ad_log_join_fields = $this->common_first_ad_log_join_fields;
//                $this->third_ad_log_join_fields = $this->common_third_ad_log_join_fields;
                $this->join_first_ad_log_on = $this->common_join_first_ad_log_on;
                $this->join_third_ad_log_on = $this->common_join_third_ad_log_on;
//                $this->ad_log_total_fields = $this->common_all_join_fields;
                $this->ad_create_time_field = 'ad_log.ad2_create_time';
                $this->ad3_create_time_field = 'third_ad_log.creative_create_time';
                $this->third_join_data_log_on = $this->common_third_join_data_log_on;
//                $this->third_join_reg_log_on = $this->common_third_join_reg_log_on;
                $this->third_join_overview_log_on = $this->common_third_join_overview_log_on;
                $this->first_ad_order_by = $this->common_first_ad_order_by;
                $this->second_ad_order_by = $this->common_second_ad_order_by;
                $this->third_ad_order_by = $this->common_third_ad_order_by;
                $this->first_ad_order_by_select = $this->common_first_ad_order_by_select;
                $this->second_ad_order_by_select = $this->common_second_ad_order_by_select;
                $this->third_ad_order_by_select = $this->common_third_ad_order_by_select;
                $this->first_ad_switch = $this->common_first_ad_switch;
                $this->second_ad_switch = $this->common_second_ad_switch;
                $this->third_ad_switch = $this->common_third_ad_switch;
                $this->join_calc_bind_on = $this->common_join_calc_rule_on;
                $this->overview_join_data_log_on = $this->common_data_log_join_overview_on;
                $this->join_rta_bind_on = $this->common_join_rta_bind_on;
                $this->join_aweme_log_on = ADAnalysisSqlMap::JOIN_AWEME_LOG_ON[$this->media_type];
        }

        //放入计算回本标准值所需字段
        $this->handleStandardCalc();
        //处理计算有效无效消耗字段
        $this->handleStandardReachedCostCalc();
        //特殊处理业务
        $this->handleSpecialLogic();
    }

    private function handleCustomizedTarget()
    {
        // 把自定义指标里面需要算的指标拿出来
        $customized_target = [];
        if ($this->customized_target_ids) {
            $this->customized_target = (new CustomizedTargetModel())->getTargetById($this->customized_target_ids);
            foreach ($this->customized_target as $target_item) {
                $formula = json_decode($target_item->formula, true);
                if (isset($formula['column']) && is_array($formula['column'])) {
                    $customized_target = array_merge($customized_target, $formula['column']);
                }
                $target_item->formula = $formula;
            }

            $this->target = array_merge($this->target, $customized_target);
//            var_dump($this->customized_target, $this->target);
        }
    }

    private function handleSpecialLogic()
    {
        if (in_array('mini_game_os', $this->all_fields)) {
            if (isset($this->ad_log_fields['game_id'])) {
                $this->ad_log_fields['game_id'] = ['data_log.game_id as game_id', 'game.game_name'];
            }
            $this->data_log_table = ADAnalysisSqlMap::TABLE['day_data_log'][$this->media_type];
        }

        if (array_intersect(ADAnalysisSqlMap::NEED_JOIN_INEFFICIENT_MATERIAL, $this->all_fields)) {
            $this->need_join_inefficient = true;
        }

        if (array_intersect(ADAnalysisSqlMap::NEED_JOIN_RTA_BIND, $this->all_fields) && in_array((int)$this->media_type, [MediaType::TOUTIAO, MediaType::TENCENT])) {
            $this->join_rta_bind = true;
            $this->join_rta_bind_type = $this->join_rta_bind_type ?: 'LEFT';
        }

        $this->overview_live_apportion_default_fields = ADAnalysisSqlMap::OVERVIEW_LIVE_APPORTION_DEFAULT_FIELDS;
        $this->data_live_apportion_default_fields = ADAnalysisSqlMap::DATA_LIVE_APPORTION_DEFAULT_FIELDS;
//        $this->overview_official_apportion_default_fields = ADAnalysisSqlMap::OVERVIEW_OFFICIAL_APPORTION_DEFAULT_FIELDS;
        $this->overview_natural_apportion_default_fields = ADAnalysisSqlMap::OVERVIEW_NATURAL_APPORTION_DEFAULT_FIELDS;
    }

    /**
     * 放入计算回本标准值所需字段
     */
    private function handleStandardCalc()
    {
        if ($calc_standard = array_intersect($this->all_fields, $this->calc_standard_value)) {
            array_map(function ($item) {
                $this->sub_data_log_calc_standard_fields[] = ADAnalysisSqlMap::SUB_DATA_LOG_CALC_STANDARD_FIELDS[$item];
                $this->main_calc_standard_fields[] = ADAnalysisSqlMap::MAIN_CALC_STANDARD_FIELDS[$item];
            }, $calc_standard);
        }
    }

    private function handleStandardReachedCostCalc()
    {
        if ($calc_field = array_intersect($this->all_fields, $this->calc_standard_reached_cost)) {
            array_map(function ($item) {
                $this->sub_data_log_calc_standard_fields[] = ADAnalysisSqlMap::SUB_DATA_LOG_CALC_STANDARD_FIELDS[$item];
                $this->main_calc_standard_fields[] = ADAnalysisSqlMap::MAIN_CALC_STANDARD_FIELDS[$item];
            }, $calc_field);
        }
    }

    /**
     * 公共排序字段
     */
    private function commonPartOrderBy()
    {
        if ($this->order) {
            $business_dimension = ['agent_leader', 'site_id', 'os', 'game_id', 'main_game_id', 'root_game_id', 'clique_id'];
            foreach ($this->order as $order) {
                if (isset($order['target']) && isset($order['dimension']) && isset($order['sort'])) {
                    if (isset(ADAnalysisSqlMap::ORDER_SELECT[$order['target']])) {
                        $field_take_from = 'ad_log';
                        if (in_array($order['target'], $this->business_target)) {
                            $field_take_from = 'overview_log';
                        }
                        $partition_by_str = $field_take_from . '.' . $order['dimension'];

                        if (in_array($order['dimension'], $business_dimension)) {
                            $partition_by_str =
                                $field_take_from . '.' . $order['dimension'] . ',' . $field_take_from . '.platform';
                        }

                        $this->main_order_select[] =
                            'SUM(' . ADAnalysisSqlMap::ORDER_SELECT[$order['target']][0] . ') OVER ( PARTITION BY ' .
                            $partition_by_str . ') AS ' . $order['target'] . '_partition_by_' . $order['dimension'];

                        $this->main_order_by[] =
                            $order['target'] . '_partition_by_' . $order['dimension'] . ' ' . $order['sort'];
                    }
                }
            }
        }
    }

    /**
     * 公共select字段筛选
     * @param $select
     */
    private function commonPartSelect($select)
    {
        if (isset(ADAnalysisSqlMap::SUB_MAIN_LOG_SELECT[$select])) {
            $this->sub_main_select[] = ADAnalysisSqlMap::SUB_MAIN_LOG_SELECT[$select];
        }

        if ($this->live_apportion || $this->natural_apportion) {
            if (isset(ADAnalysisSqlMap::NEED_APPORTION_OVERVIEW_LOG[$select])) {
                $this->overview_log[$select] = ADAnalysisSqlMap::NEED_APPORTION_OVERVIEW_LOG[$select];
            }
            if (isset(ADAnalysisSqlMap::NEED_APPORTION_OVERVIEW_APPORTION_LOG[$select])) {
                $this->overview_apportion_log[$select] = ADAnalysisSqlMap::NEED_APPORTION_OVERVIEW_APPORTION_LOG[$select];
            }
            $this->data_ad3_apportion_join_field = ADAnalysisSqlMap::COMPRESS_DATA_LOG_APPORTION_AD3_JOIN[$this->media_type];
        } else {
            if (isset(ADAnalysisSqlMap::OVERVIEW_LOG[$select])) {
                $this->overview_log[$select] = ADAnalysisSqlMap::OVERVIEW_LOG[$select];
            }
            if (isset(ADAnalysisSqlMap::OVERVIEW_APPORTION_LOG[$select])) {
                $this->overview_apportion_log[$select] = ADAnalysisSqlMap::OVERVIEW_APPORTION_LOG[$select];
            }
        }

        if (isset(ADAnalysisSqlMap::COMPRESS_OVERVIEW_LOG[$select])) {
            $this->compress_overview_log_fields[$select] = ADAnalysisSqlMap::COMPRESS_OVERVIEW_LOG[$select];
        }
//        if (isset(ADAnalysisSqlMap::REG_LOG[$select])) {
//            $this->reg_log[$select] = ADAnalysisSqlMap::REG_LOG[$select];
//        }
        if (isset(ADAnalysisSqlMap::AGENT_SITE[$select])) {
            $this->agent_site_fields[$select] = ADAnalysisSqlMap::AGENT_SITE[$select];
        }
        if (isset(ADAnalysisSqlMap::GAME[$select])) {
            $this->game_fields[$select] = ADAnalysisSqlMap::GAME[$select];
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_FILE_LOG[$select])) {
            $this->material_file_log_fields[$select] = ADAnalysisSqlMap::MATERIAL_FILE_LOG[$select];
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_LOG[$select])) {
            $this->material_log_fields[$select] = ADAnalysisSqlMap::MATERIAL_LOG[$select];
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_LABEL[$select])) {
            $this->material_label_fields[$select] = ADAnalysisSqlMap::MATERIAL_LABEL[$select];
        }
    }

    /**
     * 公共group by字段筛选
     * @param $group_by
     */
    private function commonPartGroupBy($group_by)
    {

        if ('media_type' !== $group_by || ('media_type' === $group_by && 0 === $this->media_type)) {
            $this->sub_main_log_groups[] = 'sub_data_log.' . $group_by;
            $this->sub_main_select[] = ['sub_data_log.' . $group_by];
        }

        if (isset(ADAnalysisSqlMap::OVERVIEW_LOG_GROUP_BY[$group_by])) {
            $this->overview_log_group_by[] = ADAnalysisSqlMap::OVERVIEW_LOG_GROUP_BY[$group_by];
        }
//        if (isset(ADAnalysisSqlMap::REG_LOG_GROUP_BY[$group_by])) {
//            $this->reg_log_group_by[] = ADAnalysisSqlMap::REG_LOG_GROUP_BY[$group_by];
//        }
        if (isset(ADAnalysisSqlMap::SITE_GAME_GROUP_BY[$group_by])) {
            $this->site_game_group_by[] = ADAnalysisSqlMap::SITE_GAME_GROUP_BY[$group_by];
        }
        if (isset(ADAnalysisSqlMap::AGENT_SITE_GROUP_BY[$group_by])) {
            $this->agent_site_group_by[] = ADAnalysisSqlMap::AGENT_SITE_GROUP_BY[$group_by];
        }
        if (isset(ADAnalysisSqlMap::GAME_GROUP_BY[$group_by])) {
            $this->game_group_by[] = ADAnalysisSqlMap::GAME_GROUP_BY[$group_by];
        }
        if (isset(ADAnalysisSqlMap::JOIN_AD_LOG_GROUP_BY[$group_by])) {
            $this->join_ad_log_group_by[] = ADAnalysisSqlMap::JOIN_AD_LOG_GROUP_BY[$group_by];
        }
    }

    /**
     * 公共where字段筛选
     * @param $filter
     */
    private function commonPartWhere($filter)
    {
        if (isset(ADAnalysisSqlMap::OVERVIEW_LOG_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, '', 'overview_log');
            $this->overview_log_filter[$filter['column']] = $where;
        }
        if (isset(ADAnalysisSqlMap::SITE_GAME_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, '', 'site_game');
            $this->site_game_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::AGENT_SITE_FILTER[$filter['column']])) {
            if ('aweme_account_id' === $filter['column']) {
                $filter['value'] = array_map(function ($item) {
                    return strtolower($item);
                }, $filter['value']);
            }
            $where = SqlParser::get($filter, ADAnalysisSqlMap::AGENT_SITE_FILTER[$filter['column']], 'agent_site');
            $this->agent_site_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::AGENT_SITE_GROUP_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::AGENT_SITE_GROUP_FILTER[$filter['column']], 'agent_leader_group');
            $this->agent_leader_group_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::TEAM_AGENT_GROUP_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::TEAM_AGENT_GROUP_FILTER[$filter['column']], 'team_agent_config');
            $this->team_agent_group_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::GAME_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::GAME_FILTER[$filter['column']], 'game');
            $this->game_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_FILE_LOG_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::MATERIAL_FILE_LOG_FILTER[$filter['column']],
                'material_file_log');
            $this->material_file_log_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_FILE_LOG_SUB_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::MATERIAL_FILE_LOG_SUB_FILTER[$filter['column']]);
            $this->material_file_log_sub_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_LOG_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::MATERIAL_LOG_FILTER[$filter['column']],
                'material_log');
            $this->material_log_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::MATERIAL_LABEL_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::MATERIAL_LABEL_FILTER[$filter['column']],
                'material_label');
            $this->material_label_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::CALC_BIND_RULE_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::CALC_BIND_RULE_FILTER[$filter['column']]);
            $this->calc_bind_rule_filter[] = $where;
        }
        if (isset(ADAnalysisSqlMap::AGENCY_CHANGE_LOG_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::AGENCY_CHANGE_LOG_FILTER[$filter['column']],
                'agency_change_log');
            $this->agency_change_log_filter[] = $where;
        }
        if ('calc_rule_bind_status' === $filter['column']) {
            $this->join_calc_bind = true;
            if (0 === (int)$filter['value']) {
                $this->join_calc_bind_type = 'LEFT';
            } else {
                $this->join_calc_bind_type = 'INNER';
            }
        }
        if ('rta_bind_status' === $filter['column'] && in_array((int)$this->media_type, [MediaType::TOUTIAO, MediaType::TENCENT])) {
            $this->join_rta_bind = true;
            $this->judge_rta_bind_status = true;
            if (0 === (int)$filter['value']) {
                $this->join_rta_bind_type = 'LEFT';
            } else {
                $this->join_rta_bind_type = 'INNER';
            }
        }
        if (isset(ADAnalysisSqlMap::TOUTIAO_AWEME_LIST_FILTER[$filter['column']])) {
            if ($filter['column'] !== 'interface_person' || 0 === (int)$filter['value']) {
                if ($filter['column'] === 'aweme_name') {
                    $filter['value'] = array_map(function ($item) {
                        if ((int)$item === 0) {
                            return '';
                        }
                        return $item;
                    }, $filter['value']);
                }
                $where = SqlParser::get($filter, ADAnalysisSqlMap::TOUTIAO_AWEME_LIST_FILTER[$filter['column']]);
                $this->aweme_list_filter[] = $where;
            }
        }
        if (isset(ADAnalysisSqlMap::TOUTIAO_IS_INEFFICIENT_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, ADAnalysisSqlMap::TOUTIAO_IS_INEFFICIENT_FILTER[$filter['column']]);
            $this->toutiao_is_inefficient_filter[] = $where;
        }
//        if (isset(ADAnalysisSqlMap::INTERFACE_PERSON_GROUP[$filter['column']])) {
//            $where = SqlParser::get($filter, ADAnalysisSqlMap::INTERFACE_PERSON_GROUP[$filter['column']],
//                'person_group');
//            $this->person_group_filter[] = $where;
//        }
    }
}
