<?php
/**
 * 头条创建转化
 * User: zhangzhen
 * Date: 2020-02-21
 */

namespace App\Param;


use App\Constant\ConvertSourceType;
use App\Constant\ConvertType;
use App\Constant\DeepExternalAction;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsTencentUserActionSetLogModel;
use Common\EnvConfig;

/**
 * Class ConvertCreateParam
 * @package App\Param
 */
class ChannelPackageParam extends AbstractParam
{
    /**
     * @var MediaAccountInfoParam $media_account media_account的一条记录
     */
    public $media_account;

    public $game_id;
    public $site_id = 0;
    public $prefix_name = '';
    public $suffix_name = '';

    /**
     * @var string $name 渠道包名称，当没有传入名字时使用规则prefix_name-site_id-suffix_name
     */
    public $name;
    /**
     * @var string $download_url 下载链接-必填
     */
    public $download_url;

    /**
     * @var string $app_id APP ID 必填
     */
    public $app_id;

    /**
     * @var string $sdk_ext ods_media_sdk ext字段
     */
    public $sdk_ext = [];

    /**
     * @var string $account_type ods_media_sdk account_type 字段
     */
    public $account_type;

    /**
     * @var int $game_pack 打包选项
     */
    public $game_pack;

    protected function paramHook()
    {
        if ($this->media_account->account_id <= 0) {
            throw new AppException('账户信息传参有误');
        }

        if (!$this->game_id) {
            throw new AppException('游戏传参有误');
        }

        $method = "param{$this->media_account->media_type}Hook";
        if (method_exists($this, $method)) {
            $this->$method();
        }

        if (!$this->name) {
            if (!$this->site_id) {
                throw new AppException('分包广告位ID传参有误');
            }
            $name_arr = [$this->site_id];
            if (!empty($this->prefix_name)) {
                array_unshift($name_arr, $this->prefix_name);
            }
            if (!empty($this->suffix_name)) {
                array_push($name_arr, $this->suffix_name);
            }
            $this->name = implode('-', $name_arr);
        }
    }

    private function param2Hook()
    {
        if (!$this->download_url && $this->game_pack !== 2) {
            throw new AppException('下载链接传参有误。');
        }
    }

    /**
     * 把ConvertCreateParam类转化成请求的body
     */
    public function toRequestBody()
    {
        $method = "to{$this->media_account->media_type}RequestBody";
        if (method_exists($this, $method)) {
            return $this->$method();
        }
        throw new AppException("媒体类型:{$this->media_account->media_type}尚未对接渠道包");
    }

    private function to1RequestBody()
    {
        $body = [
            'advertiser_id' => $this->media_account->account_id,
            'package_id' => $this->app_id,
            'mode' => 'Manual',
            'channel_count' => 1,
            'channel_list' => [
                [
                    'channel_id' => (string)$this->site_id,
                    'remark' => $this->name,
                ]
            ],
        ];

        if ($this->account_type === 'BP') {
            $body = [
                'account_id' => $this->media_account->account_id,
                'account_type' => $this->account_type,
                'package_id' => $this->app_id,
                'mode' => 'Manual',
                'channel_count' => 1,
                'channel_list' => [
                    [
                        'channel_id' => (string)$this->site_id,
                        'remark' => $this->name,
                    ]
                ],
            ];
        }

        return $body;
    }

    private function to2RequestBody()
    {
        if ($this->game_pack === 2) {
            $body = [
                'account_id' => $this->media_account->account_id,
                'package_id' => (int)$this->app_id,
                'channel_list' => [
                    [
                        'channel_id' => (string)$this->site_id,
                        'channel_name' => $this->name
                    ]
                ]
            ];
        } else {
            $body = [
                'account_id' => $this->media_account->account_id,
                'android_union_app_id' => $this->app_id,
                'package_name' => $this->name,
                'package_origin_url' => $this->download_url,
                'customized_channel_id' => $this->site_id
            ];
        }

        return $body;
    }

    private function to3RequestBody()
    {
        return [
            'advertiser_id' => $this->media_account->account_id,
            'parent_package_id' => $this->app_id,
            'type' => 2,
            'channel_id' => [(string)$this->site_id],
        ];
    }

    private function to4RequestBody()
    {
        if (empty($this->sdk_ext['summary'] ?? '')) {
            throw new AppException('请到广告投放-SDK管理补充一句话描述');
        }

        $job = [
            'packageId' => 0,
            'channelName' => $this->name . '_' . rand(1000, 9999),
            'packageLink' => $this->download_url,
            'appLogo' => $this->sdk_ext['app_logo'],
            'appScreenshots' => $this->sdk_ext['app_screen_shots'],
            'appIntroduce' => $this->sdk_ext['app_introduce'],
            'developerName' => $this->sdk_ext['developer_name'],
            'privacyProtectionAgreement' => true,
            'autoUpdate' => true,
            'summary' => $this->sdk_ext['summary']
        ];
        if (isset($this->sdk_ext['copyright']) && !empty($this->sdk_ext['copyright'])) {
            $job['copyright'] = $this->sdk_ext['copyright'];
        }

        // 新应用分类与游戏玩法
        if ($this->sdk_ext['category_info'] ?? false) {
            $job['cateGoryInfo'] = $this->sdk_ext['category_info'];
        } else {
            $job['category'] = (int)$this->sdk_ext['category'];
        }

        $job['privateUrl'] = PlatformAbility::privateURL($this->media_account->platform, $this->game_id);
        $job_list[] = $job;
        $unique_id = md5(json_encode($job_list) . time());
        $body = [
            'uniqueId' => $unique_id,
            'jobList' => $job_list,
        ];
        return $body;
    }

    private function to14RequestBody()
    {
        if (empty($this->sdk_ext['summary'] ?? '')) {
            throw new AppException('请到广告投放-SDK管理补充一句话描述');
        }

        $job = [
            'packageId' => 0,
            'channelName' => $this->name . '_' . rand(1000, 9999),
            'packageLink' => $this->download_url,
            'appLogo' => $this->sdk_ext['app_logo'],
            'appScreenshots' => $this->sdk_ext['app_screen_shots'],
            'appIntroduce' => $this->sdk_ext['app_introduce'],
            'developerName' => $this->sdk_ext['developer_name'],
            'privacyProtectionAgreement' => true,
            'autoUpdate' => true,
            'summary' => $this->sdk_ext['summary']
        ];
        if (isset($this->sdk_ext['copyright']) && !empty($this->sdk_ext['copyright'])) {
            $job['copyright'] = $this->sdk_ext['copyright'];
        }

        // 新应用分类与游戏玩法
        if ($this->sdk_ext['category_info'] ?? false) {
            $job['cateGoryInfo'] = $this->sdk_ext['category_info'];
        } else {
            $job['category'] = (int)$this->sdk_ext['category'];
        }

        $job['privateUrl'] = PlatformAbility::privateURL($this->media_account->platform, $this->game_id);
        $job_list[] = $job;
        $unique_id = md5(json_encode($job_list) . time());
        $body = [
            'uniqueId' => $unique_id,
            'jobList' => $job_list,
        ];
        return $body;
    }
}
