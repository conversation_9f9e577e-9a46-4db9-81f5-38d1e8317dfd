<?php


namespace App\Param\ADLab\Tencent;


use App\Exception\AppException;
use App\Param\ADLab\AbstractADLabOtherSettingParam;
use App\Param\ADLab\ADLabParam;

class ADLabOtherSettingParam extends AbstractADLabOtherSettingParam
{
    public $ad_type = 'default';
    public $site_set_mode = 0;
    public $site_set = '';
    public $data_time_scope = 7;
    public $day_test_budget = 0;
    public $activate_ad_num = 3;
    public $ldy_mode = 0;
    public $ldy_map = [];
    public $ad_budget_mode = 0;
    public $ad_budget = 0;
    public $external_action_mode = 0;
    public $external_action = '';
    public $cpa_bid_mode = 0;
    public $cpa_bid_mode_type = 0;
    public $cpa_bid = 0;
    public $cpa_bid_list = [];
    public $cpa_bid_scope = [0, 0];
    public $deep_external_action_mode = 0;
    public $deep_external_action = '';
    public $deep_cpa_bid_mode = 0;
    public $deep_cpa_bid_mode_type = 0;
    public $deep_cpa_bid = 0;
    public $deep_cpa_bid_list = [];
    public $deep_cpa_bid_scope = [0, 0];
    public $create_ad_start_time = '';
    public $create_ad_end_time = '';
    public $cost_ad_time_mode = 0;
    public $cost_ad_start_time = '';
    public $cost_ad_end_time = '';
    public $time_series_mode = 0;
    public $time_series = [];
    public $is_exclude_audience = 0;
    public $is_audience = 0;
    public $is_extend = 0;
    public $extend_content = [];
    public $rta_mode = 0;
    public $rta_list = [];
    public $first_day_roi_stander_mode = 0;
    public $first_day_roi_stander = 0;
    public $auto_acquisition_enabled = 2;
    public $material_prob = [
        ['type' => 'OLD_GOOD_MATERIAL', 'name' => '优秀老素材', 'prob' => 0.2],
        ['type' => 'GOOD_CREATE_MATERIAL', 'name' => '优秀新素材（设计维度）', 'prob' => 0.5],
        ['type' => 'GOOD_TOUTIAO_MATERIAL', 'name' => '优秀新素材（头条维度）', 'prob' => 0.3]
    ];
    public $auto_audience = 0;
    public $expand_targeting = ['age', 'gender'];
    public $game_pack = 1;
    public $strategy_info = 0;

    public $callback_statistical_type = 1;

    public $strategy_type = 1;

    public function validate()
    {
        if ($this->first_day_roi_stander_mode == 1 && $this->first_day_roi_stander <= 0) {
            throw new AppException('首日回本标准不能为0');
        }
    }

    public function format()
    {
        $this->ldy_map = $this->tranObject($this->ldy_map);

        if (!$this->create_ad_start_time) {
            $this->create_ad_start_time = '09:00:00';
        }

        if (!$this->create_ad_end_time) {
            $this->create_ad_end_time = '23:59:59';
        }

        if (!$this->day_test_budget) {
            $this->day_test_budget = 20000;
        }

        if ($this->first_day_roi_stander_mode == 0) {
            $this->first_day_roi_stander = 0;
        }
    }

    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    public function labFormat(ADLabParam $param)
    {
        // TODO: Implement labFormat() method.
    }

    public function labValidate(ADLabParam $param)
    {
        // TODO: Implement labValidate() method.
    }
}