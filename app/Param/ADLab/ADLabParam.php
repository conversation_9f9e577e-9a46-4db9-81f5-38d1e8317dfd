<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADLab;

use App\Constant\ADLabClassMap;
use App\Container;
use App\Param\AbstractParam;

class ADLabParam extends AbstractParam
{
    public $id = 0;
    public $name;
    public $media_type = 0;
    public $platform = '';
    public $os = '';
    public $clique_id = '';
    public $root_game_id_list = [];
    public $main_game_id_list = [];
    public $game_mode = 0;
    public $game_list = [];
    public $account_list = [];
    public $creative_word_mode = 0;
    public $creative_list = [];
    public $word_list = [];
    /**
     * @var AbstractADLabOtherSettingParam $other_setting
     */
    public $other_setting;
    public $notice_url = '';
    public $notice_sercet = '';
    public $creator = '';
    public $creator_id = 0;
    public $create_time = '';
    public $update_time = '';
    public $status_code = '';

    /**
     * ADTaskParam constructor.
     * @param array $property
     */
    public function __construct(array $property = [])
    {
        unset($property['other_setting']);
        if (!($property['creator'] ?? '')) {
            $property['creator'] = Container::getSession()->name;
        }
        if (!($property['creator_id'] ?? '')) {
            $property['creator_id'] = Container::getSession()->user_id;
        }
        parent::__construct($property);
    }

    /**
     * @param $media_type
     * @param $data
     */
    public function setOtherSetting($media_type, array $data)
    {
        $other_setting_class_name = (ADLabClassMap::getClassName(
            (int)$media_type,
            ADLabClassMap::OTHER_SETTING_CLASS_NAME
        ));
        $this->other_setting = new $other_setting_class_name($data);
    }

    /**
     * @return array
     */
    public function toDBData()
    {
        $db_data = $this->toArray();
        unset($db_data['id']);
        unset($db_data['update_time']);
        unset($db_data['create_time']);
        $db_data['root_game_id_list'] = json_encode($this->root_game_id_list, JSON_UNESCAPED_UNICODE);
        $db_data['main_game_id_list'] = json_encode($this->main_game_id_list, JSON_UNESCAPED_UNICODE);
        $db_data['game_list'] = json_encode($this->game_list, JSON_UNESCAPED_UNICODE);
        $db_data['account_list'] = json_encode($this->account_list, JSON_UNESCAPED_UNICODE);
        $db_data['creative_list'] = json_encode($this->creative_list, JSON_UNESCAPED_UNICODE);
        $db_data['word_list'] = json_encode($this->word_list, JSON_UNESCAPED_UNICODE);
        $db_data['other_setting'] = json_encode($this->other_setting, JSON_UNESCAPED_UNICODE);

        return $db_data;
    }

    /**
     * @param $data
     * @return $this
     */
    public function initBySqlData($data)
    {
        $other_setting_class_name = (ADLabClassMap::getClassName(
            (int)$data['media_type'],
            ADLabClassMap::OTHER_SETTING_CLASS_NAME
        ));
        $this->other_setting = new $other_setting_class_name(json_decode($data['other_setting'], true));

        $data['root_game_list'] = json_decode($data['root_game_list'], true);
        $data['main_game_list'] = json_decode($data['main_game_list'], true);
        $data['game_list'] = json_decode($data['game_list'], true);
        $data['account_list'] = json_decode($data['account_list'], true);
        $data['creative_list'] = json_decode($data['creative_list'], true);
        $data['word_list'] = json_decode($data['word_list'], true);

        parent::__construct($data);

        return $this;
    }

}