<?php


namespace App\Param\ADLab\Kuaishou;

use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsKuaishouImageLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\ADLab\AbstractADLabOtherSettingParam;
use App\Param\ADLab\ADLabParam;

class ADLabOtherSettingParam extends AbstractADLabOtherSettingParam
{
    public $new_material_type = 0;
    public $new_material_ratio = 0.0;
    public $site_config_img_url = '';
    public $convert_source_type = '';
    public $scene_id_mode = '';
    public $addition_population = 0;
    public $schedule_type = 0;
    public $schedule_time = [];
    public $ad_day_budget = 0;
    public $ad_bid_mode = 0;
    public $day_limit = 0;
    public $first_day_roi_stander_mode = 0;
    public $first_day_roi_stander = 0.00;
    public $campaign_ad_num = 1;
    public $system_type = [1, 2];
    public $outer_loop_native = 0;
    public $create_non_native_ads = 0;
    public $talent_type = 1;
    public $blue_v_talent_map = [];
    public $require_population = 0;

    public function validate()
    {
        if ($this->ad_day_budget <= 0) {
            throw new AppException('每日预算必须大于0');
        }

        if ($this->outer_loop_native == 1) {
            foreach ($this->blue_v_talent_map as $account_id => $v) {
                if (empty($v)) {
                    throw new AppException("$account_id 没有选择达人帐号");
                }
            }
        }
    }

    public function format()
    {
        if ($this->schedule_type != 1) {
            $this->schedule_time = [];
        }
        $this->blue_v_talent_map = $this->tranObject($this->blue_v_talent_map);
    }

    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    public function labFormat(ADLabParam $param)
    {
        $game_info = $param->game_list[0];
        $data = (new SiteModel())->getDataByGameId($param->platform, $game_info['game_id'], MediaType::KUAISHOU);
        if ($data && $data->image_token) {
            $this->site_config_img_url = $data->image_token;
        } else {
            throw new AppException("找不到game_id({$game_info['game_id']}-{$game_info['game_name']})的历史投放广告位");
        }

        if ($param->os == 'IOS' && $this->convert_source_type == 'AD_CONVERT_SOURCE_TYPE_SDK') {
            throw new AppException("IOS的情况下，不允许使用SDK转化来源");
        }
        $game_info = (new V2DimGameIdModel())->getDataByGameId($param->platform, $game_info['game_id']);
        if ($game_info->plat_id == PlatId::MINI && $this->convert_source_type != 'AD_CONVERT_SOURCE_TYPE_H5_API') {
            throw new AppException("小游戏的情况下，只能使用H5_API的转化来源");
        }
    }

    public function labValidate(ADLabParam $param)
    {
        if ($param->game_list) {
            if (count($param->game_list) > 1) {
                throw new AppException('只能选择一个game_id');
            }
        } else {
            throw new AppException('需要选择一个game_id');
        }
    }
}
