<?php

namespace App\Param\ADLab\ToutiaoProject;

use App\Exception\AppException;
use App\Param\ADLab\AbstractADLabOtherSettingParam;
use App\Param\ADLab\ADLabParam;

class ADLabOtherSettingParam extends AbstractADLabOtherSettingParam
{
    public $material_prob = [
        ['type' => 'OLD_GOOD_MATERIAL', 'name' => '优秀老素材', 'prob' => 0.5],
        ['type' => 'GOOD_CREATE_MATERIAL', 'name' => '优秀新素材（设计维度）', 'prob' => 0.2],
    ];
    public $provisions_account_list = [];
    public $web_url_mode = 0;
    public $web_url_map = [];
    public $create_ad_start_time = '09:00:00';
    public $create_ad_end_time = '23:59:59';
    public $activate_ad_num = 3;
    public $game_pack = 0;
    public $start_time = '';
    public $end_time = '';
    public $schedule_type = 0;
    public $schedule_time = [];
    public $data_time_scope = 7;
    public $day_test_total_budget = 20000;
    public $day_test_budget_mode = 0;
    public $day_test_budget = 0;
    public $external_action_mode = 0;
    public $external_action = '';
    public $deep_external_action_mode = 0;
    public $deep_external_action = '';
    public $ad_budget_mode = 0;
    public $ad_budget = 0;
    public $inventory_type_mode = 0;
    public $inventory_type = '';
    public $cpabid_mode = 0;
    public $cpabid_mode_type = 0;
    public $cpabid = 0;
    public $cpabid_list = [];
    public $cpabid_scope = [0, 0];
    public $deep_cpabid_mode = 0;
    public $deep_cpabid_mode_type = 0;
    public $deep_cpabid = 0;
    public $deep_cpabid_list = [];
    public $deep_cpabid_scope = [0, 0];
    public $first_day_roi_stander_mode = 0;
    public $first_day_roi_stander = 0;
    public $created_ad_start_time = '';
    public $created_ad_end_time = '';
    public $is_audience = 0;
    public $is_exclude_audience = 0;
    public $smart_bid_type_mode = 0;
    public $smart_bid_type = '';

    public function validate()
    {
    }

    public function format()
    {
        $this->provisions_account_list = $this->tranObject($this->provisions_account_list);
    }

    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    public function labFormat(ADLabParam $param)
    {
        if (!in_array($this->game_pack, [0, 2])) {
            throw new AppException("打包逻辑需要是打包与媒体分包");
        }
    }

    public function labValidate(ADLabParam $param)
    {
        // TODO: Implement labValidate() method.
    }
}