<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/07/10
 * Time: 22:05
 */

namespace App\Param\Baidu;


use App\Param\AbstractParam;
use App\Task\LimitADRegionTask;

class AudienceParam extends AbstractParam
{
    /**
     * 年龄。默认为全部。
     * 取值范围：
     * 0:全部
     * 1:<18
     * 2:18-24
     * 3:25-34
     * 4:35-44
     * 5:>44
     * 如果选择多个年龄段，使用英文逗号分隔
     * @var string $age
     */
    public $age;

    /**
     * 性别。默认为全部。
     * 取值范围：
     * 0:全部
     * 1:女
     * 2:男
     * @var int $sex
     */
    public $sex;

    /**
     * 学历。默认为全部。
     * 取值范围：
     * 0:全部
     * 1:大学及以上
     * 2:高中及以下
     * 3:大专
     * 如果选择多个年龄段，使用英文逗号分隔
     * @var string $education
     */
    public $education;

    /**
     * 地域（省市）。默认为不限。
     * 不限省市用"9999999"表示。如果选择多个省市，使用英文逗号分隔省市ID。
     * 使用省级ID表示选用该省级行政区下的所有市级行政区。
     * @var string $region
     */
    public $region;

    /**
     * 长期兴趣。默认为不限。
     * 不限长期兴趣用"0"表示。如果选择多个长期兴趣，使用英文逗号分隔长期兴趣ID。
     * 长期兴趣分为一级、二级，使用一级长期兴趣ID表示选用该一级长期兴趣下的所有二级长期兴趣。
     * @var string $interests
     */
    public $interests;

    /**
     * 意图标签。默认为不限。
     * 该字段值为string形式的json数组，元素字段为：
     * "name":意图标签名称
     * "industry":意图标签行业
     * 示例："[{\"name\":\"名称\",\"industry\":\"行业\"}]"
     * @var string $intent_tag
     */
    public $intent_tag;

    /**
     * 意图标签的意图强弱控制。
     * 取值范围：
     * 30:较强
     * 20:中等
     * 10:较弱
     * @var int $intent_intension
     */
    public $intent_intension;

    /**
     * 意图词。默认为不限。
     * 不限意图词用空字符串（""）表示。
     * 每个意图词不得超过40字节(每个汉字占2个字节)，
     * 如果使用多个意图词，使用英文逗号分隔。
     * 最多不超过2000个。
     * @var string $keywords
     */
    public $keywords;

    /**
     * 意图词用户行为。
     * 取值范围：
     * 1:全网行为
     * 2:历史搜索
     * @var int $keywords_extend
     */
    public $keywords_extend;

    /**
     * 贴吧信息流吧主题。仅当投放流量选择自定义流量且单选贴吧信息流时可用。默认为不限。
     * 不限吧主题用"0"表示。如果选择多个吧主题，使用英文逗号分隔吧主题ID。
     * 吧主题分为一级、二级，使用一级吧主题ID表示选用该一级吧主题下的所有二级吧主题。
     * @var string $directories
     */
    public $directories;

    /**
     * 百度信息流文章分类。
     * 文章分类仅适用于百度信息流详情页。
     * 仅当投放流量选择自定义流量且单选百度信息流，
     * 并且投放版位选择不限版位或者选择自定义版位且包含详情页时可用。
     * 默认为不限。
     * 不限文章分类用"0"表示。如果选择多个文章分类，使用英文逗号分隔文章分类ID。
     * @var string $articleType
     */
    public $article_type;

    /**
     * 场所。默认为不限。
     * 不限场所用"0"表示。
     * 如果选择多个场所，使用英文逗号分隔场所ID。
     * 场所分为一级、二级，使用一级场所ID表示选用该一级场所下的所有二级场所。
     * 请参考场所列表。
     * @var string $place
     */
    public $place;

    /**
     * 默认为不限。
     * 限商圈（选择）用"0"表示。
     * 如果选择多个商圈（选择），使用英文逗号分隔商圈或区县ID。
     * 使用区县级ID表示选用该区县级行政区下的所有商圈。
     * @var string $biz_area
     */
    public $biz_area;

    /**
     * LBS类型。
     * 取值只能是1和（或）4，用字符串表示，英文逗号分割。
     * 1：实时地址
     * 4：历史到访
     * @var string $useractiontype
     */
    public $useractiontype;

    /**
     * 平台。默认为全部。
     * 取值范围：
     * 0:全部
     * 1:iOS
     * 2:Android
     * 4:计算机
     * 3:其他
     * 如果选择多个平台，使用英文逗号分隔。
     * 当所属计划的推广对象为应用下载（iOS）或应用下载（Android）时，
     * 平台定向只能设置为对应的平台。
     * @var string $device
     */
    public $device;

    /**
     * 网络。默认为全部。
     * 取值范围：
     * 0:全部
     * 1:wifi
     * 2:移动网络
     * @var string $net
     */
    public $net;

    /**
     * APP偏好。默认为不限。
     * 该字段值为string形式的json数组，元素字段为：
     * type：APP偏好类型，
     *  "all"：不限（此为该字段默认值）
     *  "category"：APP分类
     *  "custom"：自定义APP
     * list：选择的APP分类或自定义APP列表，列表元素为对象，对象有以下属性：
     *  "id"：APP分类ID或自定义APPID
     *  "name"：查询单元时，若APP偏好类型为自定义APP，此字段将返回选择自定义APP名称，新增/修改单元时请求参数无需传此字段。APP偏好类型为APP分类时此字段不用。
     * behavior：用户行为，需选择1至多种。取值范围：
     *  2：安装
     *  4：排除安装
     * 其中安装与排除安装不能同时选择。
     * @var string $app
     */
    public $app;

    /**
     * 自定义人群（定向人群包）。
     * 默认为不限。
     * 不限自定义人群（包含人群）用""表示。
     * 如果选择多个自定义人群（包含人群），使用英文逗号分自定义人群ID。
     * @var string $crowd
     */
    public $crowd;

    /**
     * 自定义人群（排除人群包）上限250个。
     * 不限自定义人群（排除人群）用""表示。
     * 如果选择多个自定义人群（排除人群），使用英文逗号分自定义人群ID。
     * @var string $excludeCrowd
     */
    public $exclude_crowd;

    /**
     * 百青藤媒体分类定向方式
     * 取值范围：
     * 0:不限
     * 1:定向媒体分类
     * 2:屏蔽媒体分类
     * @var int $media_categories_bind_type
     */
    public $media_categories_bind_type;

    /**
     * 百青藤媒体分类
     * 不限分类用""表示。
     * 如果选择多个媒体分类，使用英文逗号分隔媒体分类ID。
     * 百青藤媒体分类分为一级、二级，使用一级分类ID表示选用该一级分类下的所有二级分类。
     * @var string $media_categories
     */
    public $media_categories;

    /**
     * 百青藤媒体ID定向方式
     * 取值范围：
     * 0:不限
     * 1:定向媒体ID
     * 2:屏蔽媒体ID
     * @var int $media_ids_bind_type
     */
    public $media_ids_bind_type;

    /**
     * 百青藤媒体ID。默认为不限。
     * 不限百青藤媒体ID用""表示。
     * 如果选择多个百青藤媒体，使用英文逗号分隔百青藤媒体ID。
     * 最多允许使用1000个媒体ID。
     * @var string $mediaids
     */
    public $mediaids;

    /**
     * 百青藤媒体包。
     * 默认为不限。
     * 不限百青藤媒体ID用""表示。
     * 如果选择多个百青藤媒体，使用英文逗号分隔百青藤媒体ID。
     * 最多允许使用1000个媒体ID。
     * @var string $media_package
     */
    public $media_package;

    /**
     * 新兴趣（兴趣2.0）。
     * 默认为不限。
     * 不限新兴趣用"0"表示。
     * 如果选择多个新兴趣，使用英文逗号分隔兴趣ID。
     * 新兴趣分为一级、二级、三级，使用一级新兴趣ID表示选用该一级新兴趣下的所有二级新兴趣。
     * @var string $new_interests
     */
    public $new_interests;

    /**
     * Android系统版本定向，默认为不限。
     * "0":表示不限Android系统版本 。
     * "4": Android系统版本4.x+
     * "5": Android系统版本5.x+
     * "6": Android系统版本6.x+
     * "7": Android系统版本7.x+
     * "8": Android系统版本8.x+
     * "9": Android系统版本9.x+
     * @var string $android_version
     */
    public $android_version;

    /**
     * iOS系统版本定向，默认为不限。
     * "0":表示不限iOS系统版本 。
     * "8": iOS系统版本8.x+
     * "9": iOS系统版本9.x+
     * "10": iOS系统版本10.x+
     * "11": iOS系统版本11.x+
     * "12": iOS系统版本12.x+
     * @var string $ios_version
     */
    public $ios_version;

    /**
     * 人生阶段，默认为不限。
     * "0":不限。
     * "1":未婚
     * "2":已婚
     * "3":孕期
     * "4":家有儿女（全部）
     * "401":家有儿女（0-3岁）
     * "402":家有儿女（3-6岁）
     * "403":家有儿女（小学）
     * "404":家有儿女（中学）
     * 使用4相当于同时使用401、402、403、404。
     * @var string $lifeStage
     */
    public $life_stage;

    /**
     * 百青藤自定义媒体包。默认为不限。
     * @var string $custom_media_package
     */
    public $custom_media_package;

    /**
     * 自定义年龄，默认为不限。
     * 取值说明：
     * 用4位数字的字符串表示。
     * 数字的前两位表示年龄范围的最小值。（最小值必须大于17）
     * 数字的后两位表示年龄范围的最大值。（最大值大于26小于55，用99表示最大值为56岁及以上）
     * 为了覆盖更多的受众，仅提供≥10岁间隔自定义年龄范围能力。（最小年龄差>=9）
     * 不限用"0"表示。
     * 举例：
     * "1827"，表示18到27岁
     * "1855"，表示18岁到55岁
     * "1899"，表示18岁到56岁及以上
     * "0"，表示不限
     * 仅支持网站链接和小程序
     * 目前为小流量阶段
     * @var array $custom_age
     */
    public $custom_age;

    /**
     * 自动扩量，默认为0，原字段isOpenOcpcLab（是否开启oCPC实验室）下线
     * 取值说明：
     * 0表示关闭自动扩量。
     * 1表示开启自动扩量。
     * @var int $auto_expansion
     */
    public $auto_expansion;

    /**
     * 排除已转化人群，默认为0，代表不限
     * 取值说明：
     * 0: 不限。
     * 1: 排除同账户下已转化的人群。
     * 2: 排除同计划下已转化的人群。
     * 3: 排除同单元下已转化的人群。
     * @var int $exclude_trans
     */
    public $exclude_trans;

    public $exclude_trans_filter_time;

    /**
     * 手机价格 默认为0，代表不限
     * @var string $mobile_phone_price
     * 取值范围：
     * 0：不限
     * 1：1000元以下
     * 2：1000-2000元
     * 3：2000-3000元
     * 4：3000-4000元
     * 5：4000元以上
     */
    public $mobile_phone_price;

    /**
     * 手机品牌 默认为0 代表不限
     * @var string $android_brands
     * 取值范围：
     * 0：不限
     * 1：OPPO
     * 2：VIVO
     * 3：华为
     * 4：小米
     * 6：三星
     * 8：魅族
     * 10: 其他
     */
    public $android_brands;

    /**
     * @return array
     */
    public function toRequestBody()
    {
        $data = [
            'sex' => (int)$this->sex,
            'region' => '9999999',
            'interests' => '0',
            'articleType' => '0',
            'device' => 0,
            'net' => (int)$this->net,
            'newInterests' => '0',
            'autoExpansion' => 0,
        ];

//        $this->custom_age && $data['customAge'] = $this->custom_age;

        if ($this->custom_age &&
            isset($this->custom_age['max']) && $this->custom_age['max'] &&
            isset($this->custom_age['min']) && $this->custom_age['min']
        ) {
            $data['customAge'] = "{$this->custom_age['min']},{$this->custom_age['max']}";
        }

        $this->age && $data['age'] = $this->age;

        $this->education && $data['education'] = $this->education;

        $this->region && $data['region'] = $this->region;

//        if ($data['region']) {
//            $time = time();
//            if ($time >= 1602669600) {
//                $region = explode(',', $data['region']);
//
//                $region = array_diff($region, [LimitADRegionTask::BAIDU_CHOSEN_GUANGDONG]);
//                $region = array_diff($region, LimitADRegionTask::BAIDU_GUANGDONG_CITY);
//
//                $data['region'] = implode(',', $region);
//            }
//        }
//
//        if ($data['region'] == '9999999') {
//            $time = time();
//            if ($time >= 1602669600) {
//                $data['region'] = implode(',', LimitADRegionTask::BAIDU_COUNTRY_EXCEPT_GUANGDONG);
//            }
//        }

        if ($this->new_interests) {
            $data['newInterests'] = $this->new_interests;
            $this->crowd = '';
            $this->interests = '';
        }

        if ($this->intent_tag) {
            $data['intentTag'] = $this->intent_tag;
            $data['intentIntension'] = $this->intent_intension;
            $this->keywords = '';
            $this->crowd = '';
            $this->exclude_crowd = '';
        }

        if ($this->keywords) {
            $data['keywords'] = $this->keywords;
            $data['keywordsExtend'] = (int)$this->keywords_extend;
            $this->intent_tag = '';
        }

        if ($this->article_type) {
            $data['articleType'] = $this->article_type;
        }

        if ($this->device) {
            $data['device'] = $this->device;
            if ($this->device == 1) {
                $this->android_version = '0';
            }
            if ($this->device == 2) {
                $this->ios_version = '0';
            }
            if ($this->device == 4) {
                $this->ios_version = '0';
                $this->android_version = '0';
            }
        }

        if ($this->app) {
            $data['app'] = $this->app;
        }

        if ($this->crowd) {
            $data['crowd'] = $this->crowd;
            $this->interests = '';
            $this->intent_tag = '';
            $this->keywords = '';
        }

        if ($this->exclude_crowd) {
            $data['excludeCrowd'] = $this->exclude_crowd;
            $this->interests = '';
            $this->intent_tag = '';
            $this->keywords = '';
        }

        if ($this->ios_version) {
            $data['iosVersion'] = $this->ios_version;
        }

        if ($this->android_version) {
            $data['androidVersion'] = $this->android_version;
        }

        if ($this->life_stage) {
            $data['lifeStage'] = $this->life_stage;
        }

        if ($this->mobile_phone_price) {
            $data['mobilePhonePrice'] = $this->mobile_phone_price;
        }

        if ($this->android_brands) {
            $data['androidBrands'] = $this->android_brands;
        }

        if ($this->auto_expansion) {
            $data['autoExpansion'] = $this->auto_expansion;
        }

        if ($this->exclude_trans) {
            $data['excludeTrans'] = (int)$this->exclude_trans;

            if (in_array($data['excludeTrans'], [1, 4, 5])) {
                $data['excludeTransFilterTime'] = (int)$this->exclude_trans_filter_time;
            }
        }

        unset($data['interests']);

        return $data;
    }
}