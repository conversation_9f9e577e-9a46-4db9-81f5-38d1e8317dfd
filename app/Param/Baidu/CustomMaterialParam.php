<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/07/11
 * Time: 11:04
 */

namespace App\Param\Baidu;


class CustomMaterialParam extends AbstractMaterial
{
    /**
     * 标题
     * @var string $title
     */
    public $title;

    /**
     * 品牌名
     * @var string $brand
     */
    public $brand;

    /**
     * 副标题
     * @var string $subtitle
     */
    public $subtitle;

    /**
     * 头像图片URL
     * @var string $user_portrait
     */
    public $user_portrait;

    /**
     * 创意图片
     * @var array $pictures
     */
    public $pictures = [];

    /**
     * 创意图片宽度
     * @var int $pictureWidth
     */
    public $picture_width = 0;

    /**
     * 创意图片高度
     * @var int $pictureHeight
     */
    public $picture_height = 0;

    /**
     * 落地页URL
     * @var string $url
     */
    public $url;

    /**
     * 下载落地页URL
     * @var string $downloadUrl
     */
    public $downloadUrl;

    /**
     * 视频ID
     * @var string $videoid
     */
    public $videoid;

    /**
     * 封面地址
     * @var string $poster
     */
    public $poster;

    /**
     * 竖版视频横版封面
     * @var $horizontal_cover
     */
    public $horizontal_cover;

    /**
     * 点击监测URL
     * @var string $monitor_url
     */
    public $monitor_url;
    /**
     * 点击检测类型
     * @var string $downloadUrl
     */
    public $monitor_url_type;
    /**
     * 曝光监测URL
     * @var string $exposure_url
     */
    public $exposure_url;

    /**
     * 微信小程序
     * @var array $idea_plugin_group
     */
    public $idea_plugin_group = [];


    public function toRequestBody()
    {
        $data = [
            'title' => $this->title,
            'brand' => $this->brand,
            'subtitle' => $this->subtitle,
            'userPortrait' => $this->user_portrait,
            'pictures' => $this->pictures,
//            'pictureWidth' => (int)$this->picture_width,
//            'pictureHeight' => (int)$this->picture_height,
            'url' => $this->url,
            'videoid' => $this->videoid,
            'poster' => $this->poster,

        ];

        if ($this->horizontal_cover) {
            $data['horizontalCover'] = $this->horizontal_cover;
        }

        if ($this->monitor_url_type) {
            $data['monitorUrlType'] = $this->monitor_url_type;
        }

        if ($this->monitor_url) {
            $data['monitorUrl'] = $this->monitor_url;
        }

        if ($this->exposure_url) {
            $data['exposureUrl'] = $this->exposure_url;
        }

        if ($this->idea_plugin_group) {
            $data['ideaPluginGroup'] = $this->idea_plugin_group;
        }

//        if (!$this->pictures) {
//            unset($data['pictures']);
//            unset($data['pictureWidth']);
//            unset($data['pictureHeight']);
//        } else {
//            unset($data['videoid']);
//            unset($data['poster']);
//        }

        return $data;
    }

    public function setIdeaPluginGroup(IdeaPluginGroupParam $param)
    {
        $this->idea_plugin_group = [$param->toBody()];
    }

    public function setMonitorInfo(array $param)
    {
        $this->monitor_url_type = 1; // 自定义监测
        $this->monitor_url = $param['monitor_url'] ?? '';
        $this->exposure_url = $param['exposure_url'] ?? '';
    }

}
