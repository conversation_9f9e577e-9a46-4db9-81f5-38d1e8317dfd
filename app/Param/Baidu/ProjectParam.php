<?php

namespace App\Param\Baidu;

use App\Param\AbstractParam;

class ProjectParam extends AbstractParam
{
    public $project_feed_id = '';
    public $project_feed_name = '';
    public $subject = '';
    public $app_info = [];
    public $bid_mode = 1;
    public $ocpc = [];
    public $campaign_feed_ids = [];

    public function setAppInfo($app_info)
    {
        $this->app_info = $app_info;
    }

    public function setOcpc($ocpc)
    {
        $this->ocpc = $ocpc;
    }

    public function toCreateParam()
    {
        return [
            'projectFeedName' => $this->project_feed_name,
            'subject' => $this->subject,
            'appInfo' => $this->app_info,
            'bidMode' => $this->bid_mode,
            'ocpc' => $this->ocpc,
        ];
    }

}