<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/07/11
 * Time: 11:04
 */

namespace App\Param\Baidu;


class ProgramMaterialParam extends AbstractMaterial
{
    /**
     * 品牌名
     * @var string $brand
     */
    public $brand;

    /**
     * 副标题
     * @var string $subtitle
     */
    public $subtitle;

    /**
     * 头像图片URL
     * @var string $user_portrait
     */
    public $user_portrait;

    /**
     * 落地页URL
     * @var string $url
     */
    public $url;

    /**
     * 下载落地页URL
     * @var string $downloadUrl
     */
    public $download_url;

    /**
     * 点击检测类型
     * @var string $downloadUrl
     */
    public $monitor_url_type;

    /**
     * 点击监测URL
     * @var string $monitor_url
     */
    public $monitor_url;

    /**
     * 曝光监测URL
     * @var string $exposure_url
     */
    public $exposure_url;

    /**
     * 序化创意元素信息
     * @var array $elements
     */
    public $elements;

    /**
     * 微信小程序
     * @var array $idea_plugin_group
     */
    public $idea_plugin_group = [];

    /**
     * @param ElementsParam $data
     */
    public function setElements(ElementsParam $data)
    {
        $this->elements = $data->toRequestBody();
    }

    public function toRequestBody()
    {
        $data = [
            'brand' => $this->brand,
            'subtitle' => $this->subtitle,
            'userPortrait' => $this->user_portrait,
            'url' => $this->url,
            'elements' => $this->elements,
        ];

        if ($this->monitor_url_type) {
            $data['monitorUrlType'] = $this->monitor_url_type;
        }
        if ($this->monitor_url) {
            $data['monitorUrl'] = $this->monitor_url;
        }
        if ($this->exposure_url) {
            $data['exposureUrl'] = $this->exposure_url;
        }
        if ($this->idea_plugin_group) {
            $data['ideaPluginGroup'] = $this->idea_plugin_group;
        }

        return $data;
    }

    public function setIdeaPluginGroup(IdeaPluginGroupParam $param)
    {
        $this->idea_plugin_group = [$param->toBody()];
    }

    public function setMonitorInfo(array $param)
    {
        $this->monitor_url_type = 1; // 自定义监测
        $this->monitor_url = $param['monitor_url'] ?? '';
        $this->exposure_url = $param['exposure_url'] ?? '';
    }

}
