<?php
/**
 * 百度创建广告组
 * User: zhangzhonghao
 * Date: 2020-02-21
 */

namespace App\Param\Baidu;

use App\Constant\BaiduEnum;
use App\Param\AbstractParam;

/**
 * Class CampaignCreateParam
 * @package App\Param\TouTiao
 */
class CampaignCreateParam extends AbstractParam
{
    /**
     * 计划名称 - 1级名称
     * @var string $campaign_feed_name
     */
    public $campaign_feed_name;

    public $bid_position;

    public $ftypes;

    public $bidtype;

    public $bid;

    /**
     * @var OcpcSettingParam $ocpc
     */
    private $ocpc;

    /**
     * 推广对象
     * 1：应用下载（IOS）
     * 2：应用下载（Android）
     * @var string $subject
     */
    public $subject;

    /**
     * 推广app信息
     * subject=1时，该字段无效。对象定义参考下文推广app信息
     * @var AppInfoParam $appinfo
     */
    public $appinfo;

    /**
     * 推广计划预算。
     * @var string $budget
     */
    public $budget;

    /**
     * 推广开始日期。
     * 默认为null，表示长期投放
     * @var string
     */
    public $starttime;

    /**
     * 推广结束日期。
     * 默认为null，表示长期投放
     * @var string
     */
    public $endtime;

    /**
     * 暂停时段设置
     * @var array $schedule
     */
    public $schedule;

    /**
     * 预算分配控制方式
     * 0：匀速。根据流量波动，让预算在整个投放日程中较为平稳的消耗。
     * 1：标准。尽快将广告投放出去，预算可能会在短时间内消耗完
     * 2：加速。尽可能获得更多展现，对比标准投放预算消耗更快
     * @var int $bgtctltype
     */
    public $bgtctltype;

    /**
     * 是否暂停推广
     * @var bool $pause
     */
    public $pause;

    /**
     * bstype
     * 1：普通计划
     * 3：闪投计划
     * 7：原生RTA
     * @var int $bstype
     */
    public $bstype;

    /**
     * 计划类型
     * 1：普通计划
     * 2：UFC
     * @var int $campaignType
     */
    public $campaign_type;

    /**
     * 推广目标，用于区分不同计划类型下推广目标
     * 0：无效占位值
     * 1：应用推广
     * @var int $objective
     */
    public $objective;

    public $inherit_ascription_type = 0;

    public $inherit_user_ids = [];

    public function setAppInfo(array $data)
    {
        $this->appinfo = new AppInfoParam($data);
    }

    /**
     * @param array $data
     */
    public function setOcpc(array $data)
    {
        $this->ocpc = new OcpcSettingParam($data);
    }

    /**
     * 返回新增广告组的请求体
     * @return array
     */
    public function addCampaignFeedRequestBody()
    {
        $data = [
            'campaignFeedName' => $this->campaign_feed_name,
            'subject' => (int)$this->subject,
            'appinfo' => $this->appinfo->toRequestBody(),
            'pause' => $this->pause === 'true' ? true : false,
            'budget' => $this->budget,
            'bgtctltype' => (int)$this->bgtctltype,
            'bstype' => (int)$this->bstype,
            'campaignType' => $this->campaign_type,
        ];

        if ($this->bid_position == 1) {
            $data['ftypes'] = $this->ftypes;
            $data['bidtype'] = $this->bidtype;
            $data['bid'] = $this->bid;
            $data['ocpc'] = $this->ocpc->toRequestBody();
        }

        if ($this->inherit_ascription_type) {
            $data['inheritAscriptionType'] = $this->inherit_ascription_type;
            $data['inheritUserids'] = $this->inherit_user_ids;
        }

        if ($this->schedule) {
            $data['schedule'] = $this->schedule;
        }

        if ($this->starttime) {
            $data['starttime'] = $this->starttime;
        }

        if ($this->endtime) {
            $data['endtime'] = $this->endtime;
        }

        //基木鱼落地页
        if ($data['subject'] == BaiduEnum::SUBJECT_PAGE_URL) {
            $data['subject'] = BaiduEnum::SUBJECT_SITE_URL;
        }

        //IOS
        if ($data['subject'] == BaiduEnum::SUBJECT_APP_IOS) {
            unset($data['appinfo']['apkName']);
        }


        return $data;
    }
}
