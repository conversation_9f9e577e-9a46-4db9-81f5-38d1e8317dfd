<?php
/**
 * 百度创建广告计划
 * User: zhang<PERSON>hong<PERSON>
 * Date: 2020-02-21
 */

namespace App\Param\Baidu;


use App\Param\AbstractParam;

/**
 * Class AdCreateParam
 * @package App\Param\TouTiao
 */
class AdCreateParam extends AbstractParam
{

    /**
     * 所属推广计划ID - 1级id
     * @var string $campaign_feed_id
     */
    public $campaign_feed_id;

    /**
     * 推广单元名称 - 2级名称
     * @var string $adgroup_feed_name
     */
    public $adgroup_feed_name;

    public $bid_position;

    public $ftype_selection;

    public $bid_source;

    /**
     * 投放流量。
     * 可以选择优选流量或自定义流量，优选流量用空数组（[]）表示，自定义流量在数组中填写1个或多个流量取值，取值范围如下：
     *
     * 1:百度信息流 - 按媒体名称
     * 2:贴吧信息流 - 按媒体名称
     * 4:百青藤 - 按媒体名称
     * 8:好看视频（好看视频流量目前在实验阶段，仅限已开通该流量的账户使用） - 按媒体名称
     *
     * 32: 爱奇艺（爱奇艺流量目前在实验阶段，仅限已开通该流量的账户使用） - 推荐流量
     *
     * 65536: 信息流 - 按流量类型
     * 131072:插屏和横幅 - 按流量类型
     * 262144:app开屏 - 按流量类型
     * 524288:激励视频 - 按流量类型
     *
     * 注意：
     * 信息流、插屏和横幅、app开屏、激励视频 这四类属于“按流量类型”类
     * 爱奇艺属于“推荐流量”类
     * 其他取值属于“按媒体名称”类
     * “按流量类型”、“按媒体名称”、“推荐流量”类不可以同时选择
     * @var array $ftypes
     */
    public $ftypes;

    /**
     * 投放版位。
     * 1:列表页
     * 2:详情页
     * 百度信息流和贴吧信息流各自有列表页和详情页两个版位，其他流量无版位概念。
     * 当选择优选流量，或选择自定义流量但不包含百度信息流、贴吧信息流以外的流量时，可选择不限页面或自定义版位；
     * 当选择自定义流量且包含百度信息流、贴吧信息流以外的流量时，仅可选择不限页面。
     * @var array $producttypes
     */
    public $producttypes;

    /**
     * 是否暂停推广
     * @var bool $pause
     */
    public $pause;

    /**
     * 定向设置
     * @var AudienceParam $audience
     */
    public $audience;

    /**
     * 优化目标、付费模式。取值范围如下：
     * 1:点击（CPC）
     * 2:曝光（CPM）
     * 3:转化（oCPC/oCPM）
     * 5:eCPC
     * @var int $bidtype
     */
    public $bidtype;

    /**
     * 出价，单位为元，可精确到分。根据优化目标不同，具体定义如下：
     * CPC：单次点击出价
     * CPM：千次展现出价
     * oCPC：第一阶段单次点击出价
     * eCPC：单次点击出价
     * 取值范围如下：
     * 当优化目标选择CPC/oCPC/eCPC，投放流量选择优选流量或者自定义流量且包含百青藤以外的流量时：[0.3,999.99]
     * 当优化目标选择CPC/oCPC/eCPC，投放流量选择自定义流量且仅选择百青藤流量时：[0.2,999.99]
     * 当优化目标选择CPM时：[2.0,9999.99]
     * @var string $bid
     */
    public $bid;

    /**
     * 本字段只有当bidtype=3有效。请参考oCPC设置对象。
     * @var OcpcSettingParam $ocpc
     */
    public $ocpc;

    /**
     * 自动账户优化设置。
     * 设置自动优化后，系统将针对该推广单元，以最大化提升优质展现或点击为目标，按照设置的策略为您持续优化高质量定向、创意和降低成本。
     * @var AutoOptParam $auto_opt
     */
    public $auto_opt;

    /**
     * @param array $data
     */
    public function setAudience(array $data)
    {
        $this->audience = new AudienceParam($data);
    }

    /**
     * @param array $data
     */
    public function setOcpc(array $data)
    {
        $this->ocpc = new OcpcSettingParam($data);
    }

    /**
     * @param array $data
     */
    public function setAutoOpt(array $data)
    {
        $this->auto_opt = new AutoOptParam($data);
    }

    /**
     * @return array
     */
    public function addAdgroupFeedRequestBody()
    {
        if ($this->ftypes) {
            $this->ftypes = array_map(function ($element) {
                return (int)$element;
            }, $this->ftypes);
        }
        $data = [
            "campaignFeedId" => (int)$this->campaign_feed_id,
            "adgroupFeedName" => $this->adgroup_feed_name,
            "ftypes" => $this->ftypes,
            "producttypes" => $this->producttypes,
            "pause" => $this->pause === 'true',
            "audience" => $this->audience->toRequestBody(),
            "bidtype" => (int)$this->bidtype,
            "bid" => 0,
            'autoOpt' => $this->auto_opt->toRequestBody()
        ];

        if ($this->bid_position == 1) {
            $data['ftypeSelection'] = $this->ftype_selection == 1 ? 2 : 1;
            $data['bidSource'] = $this->bid_source == 1 ? 2 : 1;;
            if ($this->ftype_selection == 1) {
                unset($data['ftypes']);
            }
            unset($data['bidtype']);
            unset($data['bid']);
        } else {
            $data['ocpc'] = $this->ocpc->toRequestBody();
        }

        if (!$this->auto_opt->auto_opt_strategies) {
            unset($data['autoOpt']);
        }

        if ($this->ocpc->pay_mode == 2) {
            if ($data['autoOpt']) {
                $auto_opt_strategies = $data['autoOpt']['autoOptStrategies'];
                $auto_opt_strategies = array_filter($auto_opt_strategies, function ($element) {
                    return $element != 3;
                });
                $data['autoOpt']['autoOptStrategies'] = array_values($auto_opt_strategies);
                unset($data['autoOpt']['autoOptMaxBid']);
            }
        }

        return $data;
    }
}