<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/07/11
 * Time: 10:19
 */

namespace App\Param\Baidu;


use App\Constant\BaiduEnum;
use App\Param\AbstractParam;

class OcpcSettingParam extends AbstractParam
{
    public $app_trans_id;

    public $trans_from;

    public $ocpc_bid;

    public $lp_url;

    public $trans_type;

    public $is_skip_stage_one;

    public $pay_mode;

    public $optimize_deep_trans;

    public $deep_ocpc_bid;

    public $deep_trans_type;

    public $use_roi = false;

    public $roi_ratio;

    /**
     * @var int $campaign_type 计划类型 1普通 4放量
     */
    public $campaign_type = BaiduEnum::CAMPAIGN_TYPE_NORMAL;

    /**
     * @var int $is_manual_bid_for_max_mode 浅度转化出价模式
     */
    public $is_manual_bid_for_max_mode = 0;

    /**
     * @var int $is_manual_deep_bid_for_max_mode 深度转化出价模式
     */
    public $is_manual_deep_bid_for_max_mode = 0;

    /**
     * @var string $trans_type_attribute 1 - 付费人数优化 2 - 付费次数优化
     */
    public $trans_type_attribute = '1';

    public function toRequestBody()
    {
        $data = [
//            'appTransId' => (int)$this->app_trans_id,
            'transFrom' => (int)$this->trans_from,
            'ocpcBid' => (float)$this->ocpc_bid,
            'lpUrl' => $this->lp_url,
            'transType' => (int)$this->trans_type,
            'isSkipStageOne' => true,
            'payMode' => (int)$this->pay_mode,
            'useRoi' => $this->use_roi,
            'roiRatio' => (float)$this->roi_ratio,
        ];

        if ($this->app_trans_id) {
            $data['appTransId'] = (int)$this->app_trans_id;
        }

        if (in_array($this->trans_type, [26]) && (int)$this->trans_type_attribute === 2) {
            $data['transTypeAttribute'] = $this->trans_type_attribute;
        }

        if ($this->optimize_deep_trans === 'true') {
            $data['optimizeDeepTrans'] = true;
            $data['deepOcpcBid'] = (float)$this->deep_ocpc_bid;
            $data['deepTransType'] = (int)$this->deep_trans_type;

            //放量模式+深度付费
            if ($this->campaign_type == BaiduEnum::CAMPAIGN_TYPE_MAX_MODE && $this->deep_trans_type == BaiduEnum::DEEP_TRANS_TYPES_PAY) {
                unset($this->is_manual_bid_for_max_mode);
                unset($data['ocpcBid']);
                $this->is_manual_deep_bid_for_max_mode = BaiduEnum::IS_MAX_MODE_TRUE;
            }
        }

        if ($this->use_roi) {
            unset($data['deepTransType']);
            unset($data['optimizeDeepTrans']);
            unset($data['deepOcpcBid']);
            unset($this->is_manual_deep_bid_for_max_mode);
        }

        if (!$this->lp_url) {
            unset($data['lpUrl']);
        }

        //如果浅度出价为空，则改为浅度自动出价
        if ($this->is_manual_bid_for_max_mode == BaiduEnum::IS_MAX_MODE_TRUE) {
            if ($this->ocpc_bid > 0) {
                $data['isManualBidForMaxMode'] = BaiduEnum::IS_MAX_MODE_TRUE;
            } else {
                $data['isManualBidForMaxMode'] = BaiduEnum::IS_MAX_MODE_FALSE;
                unset($data['ocpcBid']);
            }
        }

        //如果深度出价为空，则改为自动深度出价
        if ($this->is_manual_deep_bid_for_max_mode == BaiduEnum::IS_MAX_MODE_TRUE) {
            if ($this->deep_ocpc_bid > 0) {
                $data['isManualDeepBidForMaxMode'] = BaiduEnum::IS_MAX_MODE_TRUE;
            } else {
                $data['isManualDeepBidForMaxMode'] = BaiduEnum::IS_MAX_MODE_FALSE;
                unset($data['deepOcpcBid']);
            }
        }

        return $data;
    }

}