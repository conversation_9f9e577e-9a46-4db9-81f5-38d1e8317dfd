<?php

namespace App\Param;


use App\Container;
use App\Exception\AppException;
use App\Utils\Helpers;

class ToutiaoStarComponentTaskParam extends AbstractParam
{
    public $platform = '';
    public $company = '';
    public $account_id_list = [];
    public $account_id = '';
    public $component_type = '';
    public $component_name = '';
    public $component_data = [];
    public $component_id = 0;
    public $game_component_id = 0;
    public $error_msg = '';
    public $status = 0;
    public $creator = '';

    public function validate()
    {
        if (!$this->component_name) {
            throw new AppException('组件名称不能为空');
        }
        if (mb_strlen($this->component_name) > 15) {
            throw new AppException('组件名称超过15个字');
        }
        if (!$this->component_data['android_download_url'] && !$this->component_data['ios_download_url']) {
            throw new AppException('安卓下载链接/ios下载链接至少填一个');
        }
        if ($this->component_data['android_download_url']) {
            if (!$this->component_data['android_point_text']) {
                throw new AppException('请填写Android宣传语');
            }
        }
        if ($this->component_data['ios_download_url']) {
            if (!$this->component_data['ios_point_text']) {
                throw new AppException('请填写ios宣传语');
            }
        }

        $image_list_count = count($this->component_data['image_list'] ?? []);
        if ($image_list_count < 4 || $image_list_count > 6) {
            throw new AppException('请使用4-6张介绍图');
        }

        if (!$this->component_data['description']) {
            throw new AppException('请填写游戏介绍');
        }
    }

    public function toInsertData()
    {
        $this->creator = Container::getSession()->name;
        $data = $this->toArray();
        unset($data['account_id_list']);
        $data['component_data'] = $data['component_data'] ? json_encode($data['component_data']) : '{}';
        return $data;
    }

}
