<?php

namespace App\Param\BiliBili;

use App\Param\AbstractParam;

class CampaignParam extends AbstractParam
{
    public $campaign_name;
    public $promotion_purpose_type;

    private $budget;

    public $ad_type;
    public $support_auto;

    public function setBudget($budget_limit_type, $budget)
    {
        $this->budget = [
            'budget_limit_type' => $budget_limit_type,
        ];

        if ($budget) {
            $this->budget['budget'] = $budget;
        }
    }

    public function getRequestBody()
    {
        $data = $this->toParam();
        $data['budget'] = $this->budget;
        return $data;
    }
}
