<?php

namespace App\Param\BiliBili;

use App\Param\AbstractParam;

class UnitParam extends AbstractParam
{
    public $campaign_id;
    public $unit_name;
    public $promotion_content_type;
    private $game;
    private $target;
    public $launch_begin_date;
    public $launch_end_date;
    public $launch_time;
    private $sales_mode;
    public $budget;
    public $speed_mode;

    public function setGame($game_base_id, $sub_pkg)
    {
        $this->game = [
            'game_base_id' => $game_base_id,
            'sub_pkg' => $sub_pkg,
        ];
    }

    public function setTarget($data)
    {
        $this->target = [
            'age_list' => $data['age_list'],
            'gender_list' => $data['gender_list'],
            'area_list' => array_map(function ($ele) {
                return end($ele);
            }, $data['area_list']),
            'area_type' => 0,
            'os_list' => [
                [
                    'os_target' => $data['os_target'],
                    'device_brand_list' => $data['device_brand_list'],
                    'os_version_list' => array_map(function ($ele) {
                        return end($ele);
                    }, $data['os_version_list']),
                ]
            ],
            'intelligent_mass' => [
                'intelligent_mass_target_type_list' => $data['intelligent_mass_target_type_list'],
            ]
        ];

        ($data['installed_user_filter'] ?? false) && $this->target['installed_user_filter'] = $data['installed_user_filter'];
        ($data['converted_user_filter'] ?? false) && $this->target['converted_user_filter'] = $data['converted_user_filter'];
        ($data['extra_crowd_pack_id_list'] ?? false) && $this->target['intelligent_mass']['extra_crowd_pack_id_list'] = $data['extra_crowd_pack_id_list'];
        if ((($data['os_target'] ?? false) == 'nolimit')) unset($this->target['os_list']);
        if (!($data['area_list'] ?? false)) unset($this->target['area_list']);
        if (!($data['gender_list'] ?? false)) unset($this->target['gender_list']);
        if (!($data['age_list'] ?? false)) unset($this->target['age_list']);
        if (!($data['intelligent_mass_target_type_list'] ?? false)) unset($this->target['intelligent_mass']);
    }

    public function setSalesMode($data)
    {
        $sm = [
            'is_no_bid' => $data['is_no_bid'],
            'base_target' => $data['base_target'],
            'cpa_target' => $data['cpa_target'],
            'cpa_bid' => $data['cpa_bid'],
        ];

        if ($data['deep_cpa_target'] ?? false) {
            $sm['deep_cpa_target'] = $data['deep_cpa_target'];
            $sm['dual_bid_two_stage_optimization'] = $data['dual_bid_two_stage_optimization'];
            $sm['deep_cpa_bid'] = $data['deep_cpa_bid'];
        }

        $this->sales_mode = $sm;
    }

    public function getRequestBody()
    {
        $data = $this->toParam();

        $data['game'] = $this->game;
        $data['target'] = $this->target;
        $data['sales_mode'] = $this->sales_mode;

        return $data;
    }
}
