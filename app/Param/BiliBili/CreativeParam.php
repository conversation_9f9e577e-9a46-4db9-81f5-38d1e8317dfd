<?php

namespace App\Param\BiliBili;

use App\Param\AbstractParam;

class CreativeParam extends AbstractParam
{
    private $unit;
    public $creative_list;

    public function setUnit($data)
    {
        $this->unit = [
            'campaign_id' => $data['campaign_id'],
            'unit_id' => $data['unit_id'],
            'is_bili_native' => $data['is_bili_native'],
            'channel_id' => $data['channel_id'],
            'scene_id_list' => $data['scene_id_list'],
            'is_programmatic' => $data['is_programmatic'],
            'tag_list' => $data['tag_list'],
            'is_smart_material' => $data['is_smart_material'],
            'monitor_list' => $data['monitor_list'],
            'business_category' => $data['business_category'],
        ];
    }

    public function getRequestBody()
    {
        return [
            'unit' => $this->unit,
            'creative_list' => $this->creative_list,
        ];
    }
}
