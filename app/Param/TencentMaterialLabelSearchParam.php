<?php

namespace App\Param;


use App\Model\SqlModel\Database\ZDBuilder;

class TencentMaterialLabelSearchParam extends AbstractParam
{
    public $label = '';
    public $account_id = '';
    public $creator = '';
    public $page;
    public $rows;

    public function paramHook()
    {
        !$this->page && $this->page = 1;
        !$this->rows && $this->rows = 50;
    }

    public function getCondition(ZDBuilder $builder): ZDBuilder
    {
        if ($this->label) {
            $builder->where('label', 'like', "%$this->label%");
        }

        if ($this->creator) {
            $builder->where('creator', 'like', "%$this->creator%");
        }

        if ($this->account_id) {
            $builder->whereIn('account_id', $this->account_id);
        }
        return $builder;
    }

}
