<?php


namespace App\Param;


use App\Service\PermissionService;
use App\Utils\DimensionTool;

class OperationCostFieldParam extends AbstractParam
{
    public $platform = '';                  // 平台
    public $root_game_id = 0;               // 根游戏
    public $main_game_id = 0;               // 主游戏
    public $authorization_money = 0;        // 授权金
    public $endorsement_money = 0;          // 代言费
    public $server_cost = 0;                // 服务器成本
    public $cost_month;                     // 年月
    public $module = '';                    // 模块，这里dms和ly公用
    public $page = 1;                       // 页码
    public $rows = 20;                      // 分页大小
    public $start_time;
    public $end_time;
    public $game_permission = [];                   // 游戏权限
    public $agent_permission = [];                  // agent权限

    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $proxy_type = 1;               // 1买量 2发行

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选
        if ($this->start_time && $this->start_time) {
            $this->start_time = strtotime(date("Y-m-d", strtotime($this->start_time)));
            $this->end_time = strtotime(date("Y-m-d", strtotime($this->end_time)));
        }
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserLYDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }
}
