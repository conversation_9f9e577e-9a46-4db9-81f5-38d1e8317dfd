<?php


namespace App\Param;


use App\Constant\MediaType;
use App\Logic\DSP\PermissionLogic;

class FundBalanceLogParam extends AbstractParam
{
    const FUND_CHECK_TYPE_NORMAL = '正常转账';
    const FUND_CHECK_TYPE_MISS_AUTH_AND_PAY = '漏授权+大客户付款';
    const FUND_CHECK_TYPE_MISS_AUTH_AND_REFUND = '漏授权+大客户退款';

    const AGENCY_TYPE_CHARGE = 1; // 充值结算类代理
    const AGENCY_TYPE_COST = 2; // 消耗结算类代理

    const AUDIT_TYPE_CASH_COST = '现金消耗';
    const AUDIT_TYPE_GRANT_COST = '赠款消耗';
    const AUDIT_TYPE_RETURN_COST = '返货消耗';

    const FUND_TYPE_AUDIT_MAP = [
        MediaType::TOUTIAO => [
            '现金' => self::AUDIT_TYPE_CASH_COST,
            '赠款' => self::AUDIT_TYPE_GRANT_COST,
            '共享钱包' => self::AUDIT_TYPE_RETURN_COST,
            '子钱包共享余额' => self::AUDIT_TYPE_CASH_COST,
        ],
        MediaType::TENCENT => [
            'FUND_TYPE_CREDIT_ROLL' => self::AUDIT_TYPE_CASH_COST,
            'FUND_TYPE_MP_GAME_DEVELOPER_WORKING_FUND' => self::AUDIT_TYPE_CASH_COST,
            'FUND_TYPE_GIFT' => self::AUDIT_TYPE_CASH_COST,
            'FUND_TYPE_CONTRACT_CREDIT' => self::AUDIT_TYPE_CASH_COST,
            'FUND_TYPE_ANDROID_ORIENTED_GIFT' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_COMPENSATE_VIRTUAL' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_MP_GAME_DEVELOPER_GIFT' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_INTERNAL_QUOTA' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_TEST_VIRTUAL' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_GAME_CHARGE_INDIRECT_REWARDS_GIFT' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_GAME_CHARGE_DIRECT_REWARDS_GIFT' => self::AUDIT_TYPE_GRANT_COST,
            'FUND_TYPE_CASH' => self::AUDIT_TYPE_CASH_COST,
            '返货' => self::AUDIT_TYPE_RETURN_COST,
            '搜狗激励' => self::AUDIT_TYPE_GRANT_COST,
            '子钱包共享余额' => self::AUDIT_TYPE_CASH_COST,
        ]
    ];

    const LEADER_AGENCY_SHORT_NAME_MAP = [
        '1' => 'SYSBU01',
        '2' => 'SYSBU02',
        'SLG' => 'SLGSBU'
    ];

    const AGENCY_TYPE_MAP = [
        self::AGENCY_TYPE_CHARGE => '充值结算',
        self::AGENCY_TYPE_COST => '消耗结算'
    ];

    public $platform;
    public $account_id;
    public $date;
    public $balance;
    public $fund_type;
    public $rebate;
    public $priority_level;
    public $media_type;
    public $agency_name = '';
    public $start_time = 0;
    public $end_time = 0;
    public $begin_date;
    public $end_date;
    public $aggregation_time = '';
    public $creator = '';
    public $company = '';
    public $account_leader = '';
    public $collection_amount;
    public $page = 1;
    public $rows = 120000;
    public $is_same_agency;

    public $agent_permission;
    public $target;

    # 转账校验部分
    public $account_id_in;
    public $account_id_out;
    public $agency_full_name_in;
    public $agency_full_name_out;
    public $company_in;
    public $company_out;
    public $time_difference = 10; //  转账时间差
    public $fund_check_type = [];       // 流水类型，不同于上面的fund_type
    public $transaction_cash;
    public $transaction_grant;
    public $transaction_return_goods;
    public $is_export = false;    // 是否导出

    public $is_check = false; // 是否为检验账号

    public $group_by = '';

    public $agency_type = self::AGENCY_TYPE_CHARGE;
    public $tencent_fund_type = [];
    public $exclude_fund_type = [];

    public $report_is_freeze = false;

    public $entry_start_date = '';
    public $entry_end_date = '';

    public function paramHook()
    {
        $this->dateFormat();

        if (empty($this->agency_type)) {
            $this->agency_type = self::AGENCY_TYPE_CHARGE;
        }
    }

    public function dateFormat()
    {
        $this->start_time = strtotime($this->begin_date);
        $this->end_time = strtotime($this->end_date);
    }

    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->agent_permission = $user_permission['agent_permission'];
    }
}
