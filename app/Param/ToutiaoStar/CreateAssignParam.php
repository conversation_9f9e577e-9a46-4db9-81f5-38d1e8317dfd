<?php

namespace App\Param\ToutiaoStar;

use App\Param\AbstractParam;

class CreateAssignParam extends AbstractParam
{

    public $star_id = '';

    public $demand_info = [];

    public $order_item_info = [];

    public function validate()
    {

    }

    public function setDemand(DemandParam $param)
    {
        $this->demand_info = $param->toRequestParam();
    }

    public function setOrderItem(OrderItemParam $param)
    {
        $this->order_item_info = $param->toRequestParam();
    }

    public function toRequestBody() {
        return $this->toParam();
    }
}