<?php

namespace App\Param\ToutiaoStar;

use App\Param\AbstractParam;

class DemandParam extends AbstractParam
{

    public $demand_name = '';

    public $product_name = '';

    public $product_information = '';

    public $brand_name = '';

    public $product_industry = [];

    public $product_category = '';

    public $contact_phone = '';

    public $contact_name = '';

    public $expiration_time = '';

    public $expiration_time_end = '';

    public $expect_remain_time = '';

    public $ignore_script = 0;

    public $attachments = [];

    public $attachment_text = '';

    public $ad_sync_conf = [];

    public $item_show_monitor_url = '';

    public $component_click_monitor_url = '';

    public $demand_requirement = [];

    public $component_info = [];

    public $accept_expiration_day = '';

    public function setAttachments($list)
    {
    }

    public function setAdSyncConfEngine($conf)
    {
        $this->ad_sync_conf = $conf ? ['ocean_engine' => $conf] : [];
    }

    public function setRequirement($requirement)
    {
        $this->demand_requirement = $requirement;
    }

    public function setComponentInfo($info)
    {
        $this->component_info = $info;
    }

    public function toRequestParam()
    {
        $this->ignore_script = intval($this->ignore_script);
        $this->expiration_time = $this->expiration_time ? strtotime($this->expiration_time) : '';
        $this->expiration_time_end = $this->expiration_time_end ? strtotime($this->expiration_time_end) : '';
        return $this->toParam();
    }
}