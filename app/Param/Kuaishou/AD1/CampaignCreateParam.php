<?php

namespace App\Param\Kuaishou\AD1;

use App\Constant\KuaishouEnum;
use App\Param\AbstractParam;

/**
 * 快手广告1级-广告计划
 * Class CampaignCreateParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/24 0024
 * Time: 11:04
 */
class CampaignCreateParam extends AbstractParam
{
    /**
     * @var string $access_token
     */
    public $access_token = '';

    /**
     * @var int $advertiser_id 广告主 ID
     */
    public $advertiser_id = 0;

    /**
     * @var string $campaign_name 广告计划名称
     */
    public $campaign_name = '';

    public $auto_manage;

    public $auto_adjust;

    public $auto_build;

    public $auto_build_name_rule;

    /**
     * @var int $type 计划类型
     */
    public $type = 0;

    /**
     * @var int $day_budget 单日预算金额
     */
    public $day_budget = 0;

    /**
     * @var int $bid_type 优化目标出价类型
     */
    public $bid_type = 0;

    /**
     * 钩子函数
     */
    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    /**
     * 格式化数据
     */
    private function format()
    {
        $this->day_budget = 1000 * $this->day_budget;


    }

    /**
     * 校验数据
     */
    private function validate()
    {

    }

}
