<?php
namespace App\Param\Kuaishou\Search;
use App\Param\AbstractParam;

/**
 * 快手定向参数
 * Class TargetParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/28 0028
 * Time: 14:25
 */
class TargetSearchParam extends AbstractParam
{
    /**
     * @var array $region 地域-可选
     */
    public $region;

    /**
     * @var array $district_ids 商圈定向-可选
     */
    public $district_ids;

    /**
     * @var int $user_type 用户类型-可选
     */
    public $user_type;

    /**
     * @var array $age 自定义年龄段-可选
     */
    public $age;

    /**
     * @var array $ages_range 固定年龄段-可选
     */
    public $ages_range;

    /**
     * @var int $gender 性别-可选
     */
    public $gender;

    /**
     * @var int $platform_os 操作系统-可选
     */
    public $platform_os;

    /**
     * @var int $android_osv Android 版本-可选
     */
    public $android_osv;

    /**
     * @var int $ios_osv iOS 版本-可选
     */
    public $ios_osv;

    /**
     * @var int $network 网络环境-可选
     */
    public $network;

    /**
     * @var array $device_brand 设备品牌-可选
     */
    public $device_brand;

    /**
     * @var array $device_price 设备价格-可选
     */
    public $device_price;

    /**
     * @var int $business_interest_type 商业兴趣类型-可选
     */
    public $business_interest_type;

    /**
     * @var array $business_interest 商业兴趣-
     */
    public $business_interest;

    /**
     * @var array $fans_star 网红粉丝-可选
     */
    public $fans_star;

    /**
     * @var array $interest_video 兴趣视频用户-可选
     */
    public $interest_video;

    /**
     * @var array $app_interest APP 行为-按分类-可选
     */
    public $app_interest;

    /**
     * @var array $app_interest_ids APP 行为-按分类-可选（新）
     */
    public $app_interest_ids;

    /**
     * @var array $app_ids APP 行为-按 APP 名称-可选
     */
    public $app_ids;

    /**
     * @var int $filter_converted_level 过滤已转化人群纬度-可选
     */
    public $filter_converted_level;

    /**
     * @var array $population 人群包定向-可选
     */
    public $population;

    /**
     * @var array $exclude_population 人群包排除-可选
     */
    public $exclude_population;

    /**
     * @var array $paid_audience 付费人群包 id-可选
     */
    public $paid_audience;

    /**
     * @var array $seed_population 种子人群包-选填
     */
    public $seed_population;

    /**
     * @var array $intelli_extend 智能扩量-可选
     */
    public $intelli_extend;

    /**
     * @var array $behavior_interest 行为兴趣定向-非必填
     */
    public $behavior_interest;

    /**
     * @var int $disable_installed_app_switch 过滤已安装人群维度-非必填
     */
    public $disable_installed_app_switch;

    /**
     * @var int $filter_time_range 用户的转化时间范围-非必填
     */
    public $filter_time_range;

    /**
     * 钩子函数
     */
    protected function paramHook()
    {
        $this->validate();
        $this->format();
    }

    /**
     * 数据验证
     */
    private function validate(){

    }

    /**
     * 格式化
     */
    private function format(){
        //与 age 不能同时传
        if($this->ages_range){
            $this->age = [];
        }

        if((!isset($this->age['min']) && empty($this->age['min'])) || (!isset($this->age['max']) && empty($this->age['max']))){
            $this->age = [];
        }

        if (!($this->intelli_extend['is_open'] ?? false)) {
            $this->intelli_extend = [];
        }
    }
}
