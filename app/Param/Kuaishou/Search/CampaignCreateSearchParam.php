<?php

namespace App\Param\Kuaishou\Search;
use App\Constant\KuaishouEnum;
use App\Param\AbstractParam;

/**
 * 快手广告1级-搜索广告计划
 * Class CampaignCreateSearchParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/24 0024
 * Time: 11:04
 */
class CampaignCreateSearchParam extends AbstractParam
{

    /**
     * 计划类型，0:信息流，1:搜索
     * @var int
     */
    public $ad_type = 1;

    /**
     * @var string $access_token
     */
    public $access_token = '';

    /**
     * @var int $advertiser_id 广告主 ID
     */
    public $advertiser_id = 0;

    /**
     * @var string $campaign_name 广告计划名称
     */
    public $campaign_name = '';

    /**
     * @var int $type 计划类型
     */
    public $type = 0;

    /**
     * @var int $day_budget 单日预算金额
     */
    public $day_budget = 0;

    /**
     * 分日预算
     * @var array
     */
    public $day_budget_schedule = [];

    /**
     * 钩子函数
     */
    public function paramHook(){
        $this->format();
        $this->validate();
    }

    /**
     * 格式化数据
     */
    private function format(){
        $this->day_budget = 1000 * $this->day_budget;
    }

    /**
     * 校验数据
     */
    private function validate(){

    }

}
