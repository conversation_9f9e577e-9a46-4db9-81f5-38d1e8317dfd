<?php
namespace App\Param\Kuaishou\Search;
use App\Param\AbstractParam;

/**
 * 快手批量自定义创意PA
 * Class CreativeCreateParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/31 0031
 * Time: 15:21
 */
class CreativeBatchCreateSearchParam extends AbstractParam
{
    /**
     * @var int $advertiser_id 广告主 ID
     */
    public $advertiser_id = 0;

    /**
     * @var string $access_token
     */
    public $access_token = '';

    /**
     * @var int $unit_id 广告组 ID
     */
    public $unit_id = 0;

    /**
     * @var string $click_track_url 第三方点击检测链接
     */
    public $click_track_url = '';

    /**
     * @var  $live_creative_type int 直播创意类型
     */
    public $live_creative_type;

    /**
     * @var array $creatives 批量参数
     */
    public $creatives = [];

    /**
     * @var int $creative_category 创意分类
     */
    public $creative_category = 0;

    /**
     * @var array $creative_tag 创意标签
     */
    public $creative_tag = [];

    /**
     * 钩子函数
     */
    protected function paramHook(){
        $this->format();
    }

    /**
     * 格式化数据
     */
    private function format(){

    }
}
