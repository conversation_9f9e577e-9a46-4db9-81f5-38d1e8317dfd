<?php

namespace App\Param\Kuaishou\AD2;

use App\Constant\KuaishouEnum;
use App\Param\AbstractParam;

/**
 * 快手广告组PA(广告2级)
 * Class AdUnitCreateParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/24 0024
 * Time: 15:44
 */
class AdUnitCreateParam extends AbstractParam
{
    /**
     * @var string $access_token
     */
    public $access_token = '';
    /**
     * @var int $advertiser_id 广告主 ID
     */
    public $advertiser_id = 0;

    /**
     * @var int $campaign_id 广告计划 ID
     */
    public $campaign_id = 0;

    /**
     * @var string $unit_name 广告组名称
     */
    public $unit_name = '';

    /**
     * @var int $put_status 广告组的投放状态
     */
    public $put_status = 0;

    /**
     * @var int $bid_type 优化目标出价类型
     */
    public $bid_type = KuaishouEnum::BID_TYPE_OCPM;

    /**
     * @var int $use_app_market 优先从系统应用商店下载
     */
    public $use_app_market = 0;

    /**
     * @var array $app_store 应用商店列表
     */
    public $app_store = [];

    /**
     * @var float $cpa_bid 出价
     */
    public $cpa_bid = 0;

    /**
     * @var int $ocpx_action_type 优化目标
     */
    public $ocpx_action_type = 0;

    /**
     * @var int $deep_conversion_type 深度转化目标
     */
    public $deep_conversion_type = 0;

    /**
     * @var float $roi_ratio 付费 ROI 系数
     */
    public $roi_ratio = 0.0;

    /**
     * @var float $deep_conversion_bid 深度转化目标出价
     */
    public $deep_conversion_bid = 0;

    /**
     * @var array $scene_id 资源位置
     */
    public $scene_id = [];

    /**
     * @var int $unit_type 创意制作方式
     */
    public $unit_type = 0;

    /**
     * @var int $begin_time 投放开始时间
     */
    public $begin_time = 0;

    /**
     * @var int $end_time 投放结束时间
     */
    public $end_time = 0;

    /**
     * @var string $schedule_time 投放时间段
     */
    public $schedule_time = "";

    /**
     * @var int $day_budget 单日预算
     */
    public $day_budget = 0;

    /**
     * @var array $day_budget_schedule 分日预算
     */
    public $day_budget_schedule = [];

    /**
     * @var int $url_type url 类型
     */
    public $url_type = 0;

    /**
     * @var int $web_uri_type url 类型
     */
    public $web_uri_type = 0;

    /**
     * @var int $url 投放链接
     */
    public $url = '';

    public $site_id = '';

    /**
     * @var int $package_id 新版应用中心 ID
     */
    public $package_id = 0;

    /**
     * @var int $app_id 应用 ID
     */
    public $app_id = 0;

    /**
     * @var int $show_mode 创意展现方式
     */
    public $show_mode = 0;

    /**
     * @var int $speed 投放方式
     */
    public $speed = 0;

    /**
     * @var array $gift_data 游戏礼包码
     */
    public $gift_data = [];

    /**
     * @var bool $auto_target 智能定向
     */
    public $auto_target = false;

    /**
     * @var bool $auto_create_photo 是否开启自动生成视频
     */
    public $auto_create_photo = false;

    /**
     * @var bool $smart_cover 程序化创意 2.0 智能抽帧
     */
    public $smart_cover = false;

    /**
     * @var bool $asset_mining 程序化创意 2.0 素材挖掘
     */
    public $asset_mining = false;

    /**
     * @var int $consult_id 咨询组件 id
     */
    public $consult_id = 0;

    /**
     * @var int $adv_card_option 高级创意开关
     */
    public $adv_card_option = 0;

    /**
     * @var array $adv_card_list 绑定卡片 id
     */
    public $adv_card_list = [];

    /**
     * @var int $card_type 卡片类型
     */
    public $card_type = 0;

    /**
     * @var bool $intention_target 行为意向-系统优选
     */
    public $intention_target = false;

    /**
     * @var int $playable_id 试玩 ID
     */
    public $playable_id = 0;

    /**
     * @var string $play_button 试玩按钮文字内容
     */
    public $play_button = '';

    /**
     * @var array $dpa_unit_param DPA 相关商品信息
     */
    public $dpa_unit_param = [];

    /**
     * @var bool $splash_ad_switch 是否投放开屏广告位
     */
    public $splash_ad_switch = false;

    /**
     * @var int $live_user_id 主播id
     */
    public $live_user_id = 0;

    /**
     * @var int $jingle_bell_id 小铃铛组件id
     */
    public $jingle_bell_id = 0;

    /**
     * @var int $conversion_type 转化途径
     */
    public $conversion_type = 0;

    /**
     * @var int $ad_type 计划类型
     */
    public $ad_type = 0;

    /**
     * @var int $extend_search 智能扩词开启状态
     */
    public $extend_search = 0;

    /**
     * @var int $style_type 高级创意样式类型
     */
    public $style_type = 0;

    /**
     * @var array $target 定向数据
     */
    public $target = [];

//    public $bid;

    /**
     * @var int $platform_os 1：Android，2：iOS，0 表示不限
     */
    public $platform_os = 0;

    /**
     * @var int $group_id 程序化落地页组ID
     */
    public $group_id = 0;

    /**
     * @var int $site_type 预约广告类型
     */
    public $site_type = 0;

    /**
     * @var string $schema_id 小程序调起ID
     */
    public $schema_id = '';

    /**
     * @var string $schema_uri 小程序调起url
     */
    public $schema_uri = '';

    /**
     * int $outer_loop_native 是否开启原生
     * @var
     */
    public $outer_loop_native = 0;

    /**
     * @var int 原生达人用户类型
     */
    public $kol_user_type = '';

    /**
     * 是否开启快投，0：关闭，1：开启
     * @var int
     */
    public $quick_search = 0;

    /**
     * 是否开启搜索人群探索，0：关闭，1：开启，开启后会在搜索流量上进行定向突破
     * @var int
     */
    public $target_explore = 0;

    /**
     * 广告标的物类型
     * @var int
     */
    public $unit_material_type = 0;

    /**
     * 自定义落地页
     * @var int
     */
    public $download_page_url = '';

    public $custom_mini_app_data;

    public function __construct($property = [])
    {
        parent::__construct($property);
    }

    protected function paramHook()
    {
        $this->validate();
        $this->format();

    }

    /**
     * 格式化数据
     */
    private function format()
    {
        $this->begin_time = $this->begin_time ? $this->formatTime($this->begin_time) : $this->formatTime(time());

        if (strtotime($this->begin_time) < strtotime(date('Y-m-d'))) {
            $this->begin_time = date('Y-m-d');
        }

        $this->end_time = $this->formatTime($this->end_time);
        $this->unit_type = $this->unit_type != KuaishouEnum::UNIT_TYPE_PROGRAM3_0 ? $this->unit_type : KuaishouEnum::UNIT_TYPE_PROGRAM2_0;//历史原因,程序化3.0改为2.0,媒体去掉了3.0的枚举值
        $this->schedule_time = mb_substr($this->schedule_time, 0, 168);
        $this->day_budget = floatval($this->day_budget) * 1000;
        $this->cpa_bid = $this->cpa_bid * 1000;
        $this->deep_conversion_bid = $this->deep_conversion_bid * 1000;
        $this->bid_type = empty($this->bid_type) ? KuaishouEnum::BID_TYPE_OCPM : $this->bid_type;//兼容不传bid_type的情况

        //兼容历史数据
        if ($this->ocpx_action_type == KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY_HISTORY) {
            $this->ocpx_action_type = KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY;
        }

        //`付费 7日付费 首日ROI 7日ROI 小程序ROI 行为`没有深度转化
        if (in_array($this->ocpx_action_type, [
            KuaishouEnum::OCPX_ACTION_TYPE_PAY_7DAY
            , KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY
            , KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY
            , KuaishouEnum::OCPX_ACTION_TYPE_ENTER_MINI_PROGRAM
            , KuaishouEnum::OCPX_ACTION_TYPE_ENTER_MINI_PROGRAM_EXT
            , KuaishouEnum::OCPX_ACTION_TYPE_BEHAVIOR])) {
            $this->deep_conversion_bid = 0;
            $this->deep_conversion_type = 0;
        }

        //不满足提交深度出价
        if (in_array($this->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_PAY]) ||
            (in_array($this->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_ACTIVE]) &&
                in_array($this->deep_conversion_type, [KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI, KuaishouEnum::DEEP_CONVERSION_TYPE_ENTER_MINI_PROGRAM]))
        ) {
            $this->deep_conversion_bid = 0;
        }

        //不满足提交ROI系数
        if (!in_array($this->ocpx_action_type, [
                KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY,
                KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY,
                KuaishouEnum::OCPX_ACTION_TYPE_ENTER_MINI_PROGRAM,
                KuaishouEnum::OCPX_ACTION_TYPE_ENTER_MINI_PROGRAM_EXT,
            ]) && !(in_array($this->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_PAY, KuaishouEnum::OCPX_ACTION_TYPE_ACTIVE]) &&
                in_array($this->deep_conversion_type, [KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI, KuaishouEnum::DEEP_CONVERSION_TYPE_ENTER_MINI_PROGRAM]))) {
            $this->roi_ratio = 0;
        }

    }

    /**
     * 格式化开始/结束时间 毫秒取秒
     * @param $time
     * @return string
     */
    private function formatTime($time)
    {
        if (empty($time)) {
            return 0;
        }
        return date('Y-m-d', substr($time, 0, 10));
    }


    /**
     * 校验数据
     */
    private function validate()
    {
    }
}
