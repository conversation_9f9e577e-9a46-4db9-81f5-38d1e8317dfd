<?php

namespace App\Param\Kuaishou\AD2;

use App\Param\AbstractParam;

/**
 * 智能定向
 * Class AutoTargetParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/29 0029
 * Time: 15:40
 */
class AutoTargetParam extends AbstractParam
{
    public $intelli_extend_option;

    public $behavior_type;

    /**
     * @var array $region 地域-可选
     */
    public $region;

    /**
     * @var array $age 自定义年龄段-可选
     */
    public $age;

    /**
     * @var array $ages_range 固定年龄段-可选
     */
    public $ages_range;

    /**
     * @var int $gender 性别-可选
     */
    public $gender;

    /**
     * @var int $platform_os 操作系统-可选
     */
    public $platform_os;

    /**
     * @var array $population 人群包定向-可选
     */
    public $population;

    /**
     * @var array $exclude_population 人群包排除-可选
     */
    public $exclude_population;

    /**
     * @var array $exclude_population 种子人群包
     */
    public $seed_population;

    /**
     * @var int $android_osv Android 版本-可选
     */
    public $android_osv;

    /**
     * @var int $ios_osv iOS 版本-可选
     */
    public $ios_osv;

    /**
     * @var array $intelli_extend 智能扩量(文档不用该字段,但媒体回复没有智能扩量搞不了智能定向)
     */
    public $intelli_extend;

    /**
     * @var int $filter_converted_level 过滤已转化人群纬度(文档不用该字段,手动投放有该字段)
     */
    public $filter_converted_level;

    /**
     * 钩子函数
     */
    protected function paramHook()
    {
        $this->validate();
        $this->format();
    }

    /**
     * 数据验证
     */
    private function validate()
    {

    }

    /**
     * 格式化
     */
    private function format()
    {
        //与 age 不能同时传
        if ($this->ages_range) {
            $this->age = [];
        }

        if (!isset($this->age['max']) || !$this->age['max']) {
            $this->age = [];
        }

        if (!($this->intelli_extend['is_open'] ?? false)) {
            $this->intelli_extend = [];
        }
    }
}
