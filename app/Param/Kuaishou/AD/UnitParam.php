<?php

namespace App\Param\Kuaishou\AD;

use App\Constant\KuaishouEnum;
use App\Param\AbstractParam;

class UnitParam extends AbstractParam
{

    /**
     * @var string $name 广告组名称
     */
    public $name = '';

    /**
     * @var int $put_status 广告组的投放状态
     */
    //public $put_status = 0;

    /**
     * @var int $bid_type 优化目标出价类型
     */
    public $bid_type = KuaishouEnum::BID_TYPE_OCPM;

    /**
     * @var int $use_app_market 优先从系统应用商店下载
     */
    public $use_app_market = 0;

    /**
     * @var array $app_store 应用商店列表
     */
    public $app_store = [];

    /**
     * @var float $cpa_bid 出价
     */
    public $cpa_bid = 0;

    /**
     * @var int $ocpx_action_type 优化目标
     */
    public $ocpx_action_type = 0;

    /**
     * @var int $deep_conversion_type 深度转化目标
     */
    public $deep_conversion_type = 0;

    /**
     * @var float $roi_ratio 付费 ROI 系数
     */
    public $roi_ratio = 0.0;

    /**
     * @var float $deep_conversion_bid 深度转化目标出价
     */
    public $deep_conversion_bid = 0;

    /**
     * @var array $scene_ids 资源位置
     */
    public $scene_ids = [];

    /**
     * @var int $unit_type 创意制作方式
     */
    //public $unit_type = 0;

    /**
     * @var int $begin_time 投放开始时间
     */
    public $begin_time = 0;

    /**
     * @var int $end_time 投放结束时间
     */
    public $end_time = 0;

    /**
     * @var string $schedule 投放时间段
     */
    public $schedule = [];

    /**
     * @var int $day_budget 单日预算
     */
    public $day_budget = 0;

    /**
     * @var array $day_budget_schedule 分日预算
     */
    public $day_budget_schedule = [];

    /**
     * @var int $web_uri_type url 类型
     */
    public $web_uri_type = 0;

    /**
     * @var int $web_uri 投放链接
     */
    public $web_uri = '';

    /**
     * @var string $app_id 应用 ID
     */
    public $app_id = '';

    /**
     * @var string $package_id 分包 ID
     */
    public $package_id = '';

    /**
     * @var string $convert_id 转化ID
     */
    public $convert_id = '';

    /**
     * @var int $show_mode 创意展现方式
     */
    public $show_mode = 1;

    /**
     * @var int $speed 投放方式
     */
    public $speed = 0;

    /**
     * @var bool $auto_create_photo 是否开启自动生成视频
     */
    public $auto_create_photo = false;

    /**
     * @var bool $smart_cover 程序化创意 2.0 智能抽帧
     */
    public $smart_cover = false;

    /**
     * @var bool $asset_mining 程序化创意 2.0 素材挖掘
     */
    public $asset_mining = false;

    /**
     * @var int $consult_id 咨询组件 id
     */
    public $consult_id = 0;

    /**
     * @var int $adv_card_option 高级创意开关
     */
    public $adv_card_option = 0;

    /**
     * @var array $adv_card_list 绑定卡片 id
     */
    public $adv_card_list = [];

    /**
     * @var int $card_type 卡片类型
     */
    public $card_type = 0;

    /**
     * @var bool $intention_target 行为意向-系统优选
     */
    public $intention_target = false;

    /**
     * @var int $playable_id 试玩 ID
     */
    public $playable_id = 0;

    /**
     * @var string $play_button 试玩按钮文字内容
     */
    public $play_button = '';

    /**
     * @var array $dpa_unit_param DPA 相关商品信息
     */
    public $dpa_unit_param = [];

    /**
     * @var bool $splash_ad_switch 是否投放开屏广告位
     */
    public $splash_ad_switch = false;

    /**
     * @var int $live_user_id 主播id
     */
    public $live_user_id = 0;

    /**
     * @var int $jingle_bell_id 小铃铛组件id
     */
    public $jingle_bell_id = 0;

    /**
     * @var int $conversion_type 转化途径
     */
    public $conversion_type = 0;

    /**
     * @var int $ad_type 计划类型
     */
    public $ad_type = 0;

    /**
     * @var int $extend_search 智能扩词开启状态
     */
    public $extend_search = 0;

    /**
     * @var int $style_type 高级创意样式类型
     */
    public $style_type = 0;

    /**
     * @var array $target_info 定向数据
     */
    public $target_info = [];

    /**
     * @var string $group_id 程序化落地页组ID
     */
    public $group_id = '';

    /**
     * @var string $site_id 建站ID
     */
    public $site_id = '';

    /**
     * @var int $target_type 0：不限；1：智能定向
     */
    public $target_type;

    /**
     * @var string $schema_id 小程序调起ID
     */
    public $schema_id = '';

    /**
     * @var string $schema_uri 小程序调起url
     */
    public $schema_uri = '';

    public function toRequestParam()
    {
        if (!$this->begin_time) {
            $this->begin_time = strtotime(date('Y-m-d'));
        }
        $this->begin_time && $this->begin_time *= 1000;
        $this->end_time && $this->end_time *= 1000;
        if (!$this->end_time) {
            $this->end_time = '';
        }

        //`付费 7日付费 首日ROI 7日ROI 行为`没有深度转化
        if (in_array($this->ocpx_action_type, [
            KuaishouEnum::OCPX_ACTION_TYPE_PAY_7DAY
            , KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY
            , KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY
            , KuaishouEnum::OCPX_ACTION_TYPE_BEHAVIOR])) {
            $this->deep_conversion_bid = 0;
            $this->deep_conversion_type = 0;
        }

        if (in_array($this->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_PAY])) {
            $this->deep_conversion_bid = 0;
        }

        //ROI提交ROI系数
        if (!in_array($this->ocpx_action_type, [
                KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY,
                KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY
            ]) && !(in_array($this->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_PAY, KuaishouEnum::OCPX_ACTION_TYPE_ACTIVE]) && $this->deep_conversion_type == KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI)) {
            $this->roi_ratio = 0;
        }

        return $this->toParam();
    }

    public function setTargeting(TargetParam $target_param, $auto_target = false)
    {

        $this->target_info = $target_param->toRequestParam();
        if ($auto_target) {
            $auto_field = ['region_type', 'region_category_ids', 'age', 'gender', 'exclude_population', 'filter_converted_level', 'target_type'];
            foreach ($this->target_info as $key => $value) {
                if (!in_array($key, $auto_field)) {
                    unset($this->target_info[$key]);
                }
            }
        }
    }
}
