<?php
namespace App\Param\Kuaishou\AD3;
use App\Param\AbstractParam;
use App\Utils\Helpers;

/**
 * 快手程序化2.0 3.0创意
 * Class ProCreative2Param
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/4/1 0001
 * Time: 11:44
 */
class CreativeProgram2Param extends AbstractParam
{
    /**
     * @var int $advertiser_id 	广告主 ID
     */
    public $advertiser_id = 0;

    /**
     * @var string $access_token
     */
    public $access_token = '';


    /**
     * @var int $unit_id 广告组 ID
     */
    public $unit_id = 0;

    /**
     * @var string $click_url 第三方点击检测链接
     */
    public $click_url = '';

    /**
     * @var string $package_name 程序化创意名称
     */
    public $package_name = '';

    /**
     * @var int $site_id 落地页 ID
     */
    public $site_id = 0;

    /**
     * @var string $action_bar 行动号召按钮
     */
    public $action_bar = '';

    /**
     * @var array $captions 作品广告语
     */
    public $captions = [];

    /**
     * @var int $creative_category 创意分类
     */
    public $creative_category = 0;

    /**
     * @var array $creative_tag creative_tag
     */
    public $creative_tag = [];

    /**
     * @var array $photo_list 素材列表
     */
    public $photo_list = [];

    /**
     * @var array $new_expose_tag 推荐理由
     */
    public $new_expose_tag = [];

    public $outer_loop_native = 0;
    public $kol_user_type;
    public $kol_user_id;
    public $recommendation;

    /**
     * 钩子函数
     */
    protected function paramHook()
    {
        $this->format();
    }

    /**
     * 格式化数据
     */
    private function format(){
        $this->site_id = 0;
    }
}
