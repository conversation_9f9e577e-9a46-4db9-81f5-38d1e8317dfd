<?php

namespace App\Param\Kuaishou\AD3;

use App\Param\AbstractParam;
use App\Utils\Helpers;

/**
 * 快手自定义创意-批量参数PA
 * Class CreativeCreativesParam
 * @package App\Param\Kuaishou
 * User: zsp
 * Date: 2022/3/31 0031
 * Time: 16:01
 */
class CreativeCreativesParam extends AbstractParam
{
    /**
     * @var string $creative_name 创意名称-必填
     */
    public $creative_name;

    /**
     * int $outer_loop_native 是否开启原生
     * @var
     */
    public $outer_loop_native = 0;

    /**
     * string $recommendation 自定义文案
     * @var
     */
    public $recommendation;

    /**
     * int $kol_user_id 达人id
     * @var
     */
    public $kol_user_id;

    /**
     * int $kol_user_type 达人类型
     * @var
     */
    public $kol_user_type;

    /**
     * @var string $photo_id 视频 id-可选
     */
    public $photo_id;

    /**
     * @var string $image_token 封面图片 token-可选
     */
    public $image_token;

    /**
     * @var int $creative_material_type 素材类型-必填
     */
    public $creative_material_type;

    /**
     * @var array $image_tokens 便利贴单图图片创意 token-
     */
    public $image_tokens;

    /**
     * @var string $action_bar_text 行动号召按钮文案-必填
     */
    public $action_bar_text;

    /**
     * @var string $description 广告语-必填
     */
    public $description;

    /**
     * @var array $new_expose_tag 广告标签 2 期-非必填
     */
    public $new_expose_tag;

    /**
     * @var int $site_id 安卓下载中间页 ID-
     */
    public $site_id;

    /**
     * @var array $splash_photo_ids 开屏视频 id-
     */
    public $splash_photo_ids;

    /**
     * @var array $splash_image_tokens 开屏图片 token-
     */
    public $splash_image_tokens;

    /**
     * 钩子函数
     */
    protected function paramHook()
    {
        $this->format();
    }

    /**
     * 格式化数据
     */
    private function format()
    {
        if (Helpers::ADServingStrLen($this->creative_name) > 100) {
            $this->creative_name = mb_substr($this->creative_name, mb_strpos($this->creative_name, '创意_'));
        }
        $this->description = trim($this->description);
        $this->action_bar_text = trim($this->action_bar_text);
    }

    public function toRequestData()
    {
        return $this->toParam();
    }


}
