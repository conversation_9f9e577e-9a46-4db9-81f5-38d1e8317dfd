<?php

namespace App\Param\Material;

use App\Model\HttpModel\FeishuProject\FileModel;
use App\Param\AbstractParam;
use Common\EnvConfig;

class MaterialStoreApiCreateParam extends AbstractParam
{
    public $file_type;
    public $platform;
    public $uploader;
    public $theme_id;
    public $theme_pid;
    public $theme_id_name;
    public $theme_pid_name;
    public $name = '';

    public $file_list = [];

    public function storeData()
    {
        return [
            [
                'media_type' => 0,
                'original' => 1,
                'general' =>0,
                'is_3d' =>0,
                'is_immortal' =>0,
                'is_public' =>1,
                'is_expand' =>0,
                'working_hours' =>0,
                'expand_type' =>1,
                'platform' => $this->platform,
                'file_type' => $this->file_type,
                'uploader' => $this->uploader,
                'author' => $this->uploader,
                'theme_id' => $this->theme_id,
                'theme_pid' => $this->theme_pid,
                'create_time' => time(),
                'update_time' => time(),
                'material_id' => 0,
                'extend_size_icon' => [],
                'extend_size_video' => [],
                'extend_size' => [],
                'label' => [],
                'size' => 0,
                'name' => $this->name,
                'is_aep' => 0,
                'aep' => [],
                'is_txt' => 0,
                'txt' => [],
            ],  // material_input
            $this->file_list, // file_list
            [], // aep_list
            [], // txt_list
        ];
    }
}
