<?php

namespace App\Param\Material;

use App\Param\AbstractParam;

class VideoScriptListParam extends AbstractParam
{
    public $script_name;
    public $synopsis;
    public $page;
    public $rows;
    public $date;
    public $insert_time;
    public $update_time;

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }

        if ($this->date) {
            $this->insert_time = [date("Y-m-d 00:00:00", strtotime($this->date[0])), date("Y-m-d 23:59:59", strtotime($this->date[1]))];
            $this->update_time = $this->insert_time;
        }
    }
}
