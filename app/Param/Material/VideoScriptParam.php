<?php

namespace App\Param\Material;

use App\Constant\ChatGpt\ChatGpt;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\DataMedia\OdsVideoScriptLogModel;
use App\Param\AbstractParam;


class VideoScriptParam extends AbstractParam
{
    const SCRIPT_WORD_NUMBER_OPTIONS = [
        "不限",
        "50~75字",
        "75~150字",
        "150~300字",
        "大于等于300字",
    ];

    public $id;
    public $clique_id = 0;
    public $clique_name = "";
    public $gpt_id = "";
    public $material_file_id = 0;
    public $signature = "";
    public $script_name = "";
    public $selling_points = [];
    public $promotions = [];
    public $targets = [];
    public $pain_points = [];
    public $extra_require = ""; // 其他要求
    public $synopsis = [];
    public $content = [];
    public $reference = []; // 入库参考列表 ["material_file_id" => [], "video_script" => []]
    public $word_number = 0;
    public $state;
    public $insert_time;
    public $update_time;
    public $creator;
    public $editor;

    public $evaluate; // 评价
    public $reason = ""; // 重新生成原因
    public $script_index; // 单次所生成脚本的索引 例如脚本1 2 3

    protected function paramHook()
    {
        if (!$this->insert_time) {
            $this->insert_time = date("Y-m-d H:i:s");
        }
        if (!$this->update_time) {
            $this->update_time = date("Y-m-d H:i:s");
        }
        if (!isset($this->reference["material_file_ids"])) {
            $this->reference["material_file_ids"] = [];
        }
        if (!isset($this->reference["video_script_ids"])) {
            $this->reference["video_script_ids"] = [];
        }
    }

    public function toInsertData()
    {
        return [
            "signature" => $this->signature,
            "clique_id" => $this->clique_id,
            "clique_name" => $this->clique_name,
            "script_name" => $this->script_name,
            "selling_points" => json_encode($this->selling_points, JSON_UNESCAPED_UNICODE),
            "promotions" => json_encode($this->promotions, JSON_UNESCAPED_UNICODE),
            "targets" => json_encode($this->targets, JSON_UNESCAPED_UNICODE),
            "pain_points" => json_encode($this->pain_points, JSON_UNESCAPED_UNICODE),
            "extra_require" => $this->extra_require,
            "synopsis" => json_encode($this->synopsis, JSON_UNESCAPED_UNICODE),
            "content" => json_encode($this->content, JSON_UNESCAPED_UNICODE),
            "content_text" => implode("", array_values(array_column($this->content, "text"))),
            "reference" => json_encode($this->reference, JSON_UNESCAPED_UNICODE),
            "word_number" => $this->word_number,
            "gpt_id" => $this->gpt_id,
            "state" => 1,
            "creator" => $this->creator,
            "editor" => $this->editor
        ];
    }

    public function toUpdateData()
    {
        return [
            "synopsis" => json_encode(array_values(array_column($this->content, "synopsis")), JSON_UNESCAPED_UNICODE),
            "content" => json_encode($this->content, JSON_UNESCAPED_UNICODE),
            "content_text" => implode("", array_values(array_column($this->content, "text"))),
            "editor" => $this->editor,
            "update_time" => date("Y-m-d H:i:s")
        ];
    }

    public function getGenerateTools()
    {
        $tools = [
            [
                "type" => "function",
                "function" => [
                    "name" => "video_script_generate",
                    "description" => "生成视频脚本",
                    "parameters" => [
                        "type" => "object",
                        "properties" => [
                            "synopsis_list" => [
                                "description" => "根据需求生成的每一个视频脚本的视频框架",
                                "type" => "array",
                                "items" => [
                                    "type" => "object",
                                    "properties" => [
                                        "script_index" => [
                                            "type" => "string",
                                            "description" => "生成的视频脚本索引"
                                        ],
                                        "synopsis" => [
                                            "type" => "array",
                                            "description" => "视频框架",
                                            "items" => [
                                                "type" => "string",
                                                "description" => "视频框架"
                                            ]
                                        ]
                                    ],
                                    "required" => [
                                        "script_index",
                                        "synopsis"
                                    ]
                                ]
                            ],
                            "check_list" => [
                                "description" => "不同脚本之间的视频框架名称校验结果",
                                "type" => "array",
                                "items" => [
                                    "type" => "object",
                                    "properties" => [
                                        "script_list" => [
                                            "type" => "array",
                                            "description" => "校验的视频脚本索引",
                                            "items" => [
                                                "type" => "string",
                                                "description" => "校验的视频脚本索引"
                                            ]
                                        ],
                                        "num" => [
                                            "type" => "number",
                                            "description" => "校验的视频脚本之间相同视频框架数量"
                                        ],
                                        "same_synopsis" => [
                                            "type" => "array",
                                            "description" => "校验的视频脚本之间相同视频框架名称",
                                            "items" => [
                                                "type" => "string",
                                                "description" => "相同视频框架名称"
                                            ]
                                        ]
                                    ],
                                    "required" => [
                                        "script_list",
                                        "num",
                                        "same_synopsis"
                                    ]
                                ]
                            ],
                            "list" => [
                                "description" => "根据视频框架生成详细的视频脚本",
                                "type" => "array",
                                "items" => [
                                    "type" => "object",
                                    "properties" => [
                                        "script_content" => [
                                            "type" => "array",
                                            "description" => "视频脚本的内容",
                                            "items" => [
                                                "type" => "object",
                                                "properties" => [
                                                    "synopsis" => [
                                                        "type" => "string",
                                                        "description" => "视频框架名称"
                                                    ],
                                                    "text" => [
                                                        "type" => "string",
                                                        "description" => "口播内容"
                                                    ]
                                                ],
                                                "required" => [
                                                    "synopsis",
                                                    "text"
                                                ]
                                            ]
                                        ],
                                        "deconstruct" => [
                                            "type" => "object",
                                            "properties" => [
                                                "selling_points" => [
                                                    "type" => "array",
                                                    "items" => [
                                                        "type" => "string",
                                                        "description" => "解构视频的营销卖点"
                                                    ]
                                                ],
                                                "cool_points" => [
                                                    "type" => "array",
                                                    "items" => [
                                                        "type" => "string",
                                                        "description" => "解构视频的爽点"
                                                    ]
                                                ]
                                            ],
                                            "required" => [
                                                "selling_points",
                                                "cool_points"
                                            ]
                                        ]
                                    ],
                                    "required" => [
                                        "script_content",
                                        "deconstruct"
                                    ]
                                ]
                            ]
                        ],
                        "required" => [
                            "list",
                            "synopsis_list",
                            "check_list"
                        ]
                    ]
                ]
            ]
        ];

        return $tools;
    }

    public function getGenerateWithVectorTools()
    {
        $tools = [
            [
                "type" => "function",
                "function" => [
                    "name" => "story_generate",
                    "description" => "创意脚本拼接",
                    "parameters" => [
                        "type" => "object",
                        "properties" => [
                            "stories" => [
                                "type" => "array",
                                "items" => [
                                    "type" => "object",
                                    "properties" => [
                                        "new_story_id" => [
                                            "type" => "string",
                                            "description" => "新的分镜编号，继承new_story的信息,每个新脚本的new_story_id 重新计数"
                                        ],
                                        "storyboard_name" => [
                                            "type" => "string",
                                            "description" => "从new_story中提取"
                                        ],
                                        "script_content" => [
                                            "type" => "string",
                                            "description" => "从source_data的script中挑选"
                                        ],
                                        "signature" => [
                                            "type" => "string",
                                            "description" => "从source_data中提取"
                                        ],
                                        "breakdown_request_id" => [
                                            "type" => "string",
                                            "description" => "从source_data中提取"
                                        ],
                                        "source_story_id" => [
                                            "type" => "string",
                                            "description" => "从source_data的source_story_id中提取一个，并不是单纯递增计数"
                                        ]
                                    ],
                                    "required" => [
                                        "new_story_id",
                                        "storyboard_name",
                                        "script_content",
                                        "source_story_id"
                                    ]
                                ]
                            ]
                        ],
                        "required" => ["stories"]
                    ]
                ]
            ]
        ];

        return $tools;
    }

    public function getGenSynopsisTools()
    {
        $tools = [
            [
                "type" => "function",
                "function" => [
                    "name" => "video_script_analyse",
                    "description" => "分析视频脚本内容",
                    "parameters" => [
                        "type" => "object",
                        "properties" => [
                            "script_content" => [
                                "type" => "array",
                                "description" => "视频脚本的内容",
                                "items" => [
                                    "type" => "object",
                                    "properties" => [
                                        "synopsis" => [
                                            "type" => "string",
                                            "description" => "视频框架名称"
                                        ],
                                        "text" => [
                                            "type" => "string",
                                            "description" => "口播内容"
                                        ]
                                    ],
                                    "required" => [
                                        "synopsis",
                                        "text"
                                    ]
                                ]
                            ]
                        ],
                        "required" => ["script_content"]
                    ]
                ]
            ]
        ];

        return $tools;
    }

    public function getGenerateContent()
    {
        $word_number = self::SCRIPT_WORD_NUMBER_OPTIONS[$this->word_number] ?? "不限";
        $selling_points = implode("、", $this->selling_points);
        $promotions = implode("、", $this->promotions);
        $targets = implode("、", $this->targets);
        $pain_points = implode("、", $this->pain_points);

        $content = "请为我提供3个{$this->clique_name}游戏的视频脚本，脚本字数为{$word_number}，游戏卖点为{$selling_points}";
        if ($promotions) {
            $content .= "，优惠活动为{$promotions}";
        }

        if ($targets) {
            $content .= "，目标人群为{$targets}";
        }

        if ($pain_points) {
            $content .= "，用户痛点为{$pain_points}";
        }

        if ($this->extra_require) {
            $content .= "，其他要求为{$this->extra_require}";
        }

        $content .= "。生成脚本后请解构脚本。最终输出3个新视频脚本";

        // 处理参考脚本或素材文件
        $reference = [];
        if ($this->reference["material_file_ids"]) {
            $ods_material_file_log_model = new OdsMaterialFileLogModel();
            $reference_list = $ods_material_file_log_model->getScriptContentByIds($this->reference["material_file_ids"]);
            $tmp_reference = [];
            foreach ($reference_list as $item) {
                if (!isset($tmp_reference[$item->material_file_id])) {
                    $tmp_reference[$item->material_file_id] = [];
                }

                $tmp_reference[$item->material_file_id][] = [
                    $item->storyboard_name => $item->script_content
                ];
            }
            $reference = array_merge($reference, array_values($tmp_reference));
        }

        if ($this->reference["video_script_ids"]) {
            $reference_list = (new OdsVideoScriptLogModel())->getListByIds($this->reference["video_script_ids"]);
            foreach ($reference_list as $item) {
                $tmp_content = json_decode($item->content, true);
                $tmp_reference = [];
                foreach ($tmp_content as $value) {
                    $tmp_reference[] = [
                        $value["synopsis"] => $value["text"]
                    ];
                }
                $reference[] = $tmp_reference;
            }
        }

        $reference = array_filter($reference);
        foreach ($reference as $key => $item) {
            $tmp_item = json_encode($item, JSON_UNESCAPED_UNICODE);
            $index = $key + 1;
            $content .= "
                参考脚本{$index}：
                $tmp_item
            ";
        }

        return [
            "messages" => [
                [
                    "role" => "system",
                    "content" => ChatGpt::VIDEO_SCRIPT_PROMPT
                ],
                [
                    "role" => "user",
                    "content" => $content
                ]
            ],
            "tools" => $this->getGenerateTools(),
            "tool_choice" => "auto",
            "temperature" => 1
        ];
    }

    public function getGenerateContentNew($index_list, $source_list, $n)
    {
        $index_list = json_encode($index_list, JSON_UNESCAPED_UNICODE);
        $source_list = json_encode($source_list, JSON_UNESCAPED_UNICODE);
        $content = "
            你是一个视频内容创作者，现在你有一个新的创意思路如下，并命名为new_story：
            {$index_list}
            你完成了新的创作思路的结构，提取出了每段脚本的分镜名字，同时在你的过去的创作内容中，你为每段分镜找到有相似点的脚本内容，记作source_data
            {$source_list}
            
            你这个时候又有了新的思路，从历史的脚本内容中按照 new_story 的 storyboard_name
            找到可以让上下文连贯的脚本，裂变出{$n}个新的脚本
            
            你完成了如下的任务：
            1. 从new_story中提取每段脚本的分镜名称 storyboard_name
            2. 按照new_story的storyboard结构，从source_data中分别挑选一段脚本，拼接成一个上下文连贯的故事
            3. 重复任务{$n}次，最终输出多个新脚本,每个故事的new_story_id 重新计数

            要求：
            1.不能改变source_data中的文本内容
            2.分镜结构需要与new_story一致
        ";

        $data = [
            "messages" => [
                [
                    "role" => "system",
                    "content" => ChatGpt::VIDEO_SCRIPT_PROMPT_NEW
                ],
                [
                    "role" => "user",
                    "content" => $content
                ]
            ],
            "tools" => $this->getGenerateWithVectorTools(),
            "tool_choice" => "auto"
        ];

        return $data;
    }

    public function getGenSynopsisContent()
    {
        $data = [
            "messages" => [
                [
                    "role" => "system",
                    "content" => ChatGpt::VIDEO_SYNOPSIS_PROMPT
                ],
                [
                    "role" => "user",
                    "content" => "请帮我解构以下视频脚本内容：" . implode("", array_values(array_column($this->content, "text")))
                ]
            ],
            "tools" => $this->getGenSynopsisTools(),
            "tool_choice" => "auto"
        ];

        return $data;
    }
}
