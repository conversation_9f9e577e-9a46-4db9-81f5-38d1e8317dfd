<?php
/**
 * 分镜列表查询参数
 */

namespace App\Param\Material;

use App\Param\AbstractParam;

class StoryboardListParam extends AbstractParam
{
    public $platform;
    public $material_name;
    public $synopsis;
    public $storyboard_name;
    public $page;
    public $rows;

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }
    }
}
