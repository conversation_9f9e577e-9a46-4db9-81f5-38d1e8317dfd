<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Exception\AppException;
use App\Param\AbstractParam;


class MaterialThemeParam extends AbstractParam
{

    public $theme_pid = 0;
    public $platform = '';
    public $name;
    public $create_time;
    public $update_time;
    public $sort = 0;
    public $game_list = [];

    protected function paramHook()
    {
        if (!$this->game_list) {
            $this->game_list = (object)[];
        } else {
            if (is_string($this->game_list)) {
                throw new AppException('题材类型中的游戏信息值不合法');
            }
        }

        if (!is_numeric($this->sort)) {
            throw new AppException('排序字段请输入数值');
        }
        $this->update_time = time();
        if (!$this->create_time) {
            $this->create_time = time();
        }
    }

    public function toArray()
    {
        $data = parent::toArray();
        $data['game_list'] = json_encode($data['game_list'], JSON_UNESCAPED_UNICODE);
        return $data;
    }
}
