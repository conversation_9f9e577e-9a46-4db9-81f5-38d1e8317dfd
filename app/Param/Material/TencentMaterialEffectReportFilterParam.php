<?php

namespace App\Param\Material;

use App\Constant\TencentMaterialEffectSqlMap;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\CustomizedTargetModel;
use App\Param\AbstractParam;
use App\Utils\SqlParser;

class TencentMaterialEffectReportFilterParam extends AbstractParam
{
//    public $media_type = 0;                  // 所选媒体
    public $platform = '';                   // 所选平台
    public $dimension = [];                  // 处理后的维度
    public $target = [];                     // 所有指标
    public $filter = [];                     // 所有筛选
    public $calc = [];                       // 所有数值条件
    public $customized_target_ids = [];      //用户自定义指标id集合
    public $customized_target = [];          //用户自定义指标
    public $calc_condition = 'and';          // 数值筛选的逻辑
    public $author;                          //
    public $c_author;                        //
    public $a_author;                        //
    public $m1_author;                       //
    public $m2_author;                       //
    public $m3_author;                       //
    public $m4_author;                       //
    public $m5_author;                       //
    public $actor;                           //
    public $shoot;                           //
    public $material_create_date;            // 素材创建日期
    public $cost_date;                       // 消耗日期
    public $ad_create_time;                  // 创建日期
    public $game_permission;                 // 游戏权限
    public $agent_permission;                // 渠道权限
    public $account_leader;                  // 账户负责人权限
    public $material_permission;             // 素材权限
    public $user_list;                       // 下属权限
    public $user;
    public $user_type;
    public $order;                           //
    public $limit = 50000;
    public $true_cost = 0;                   //实际消耗0否1是
    public $statistic_base_on = 0;           //统计方式，3:回流
    public $publish_time;                    // 视频发布时间
    //主sql的数值计算及筛选字段
    public $main_compute = [],
        $material_log_fields = [],
        $main_filter = [],
        $main_calc = [];
    //统一的select，group by，where字段集
    public $data_log_table,
        $ad_log_table,
        $account_log_table,
        $video_log_table;
    public $material_group_by = [],
        $material_file_group_by = [],
        $aweme_group_by = [],
        $data_log_filter = [],
        $data_log_group_by = [],
        $material_filter = [],
        $material_file_filter = [],
        $material_label_filter = [],
        $site_game_filter = [],
        $agent_site_filter = [],
        $agent_leader_group_filter = [],
        $game_filter = [],
        $account_log_filter = [],
        $aweme_log_filter = [],
        $inefficient_material_log_filter = [];
    public $data_log_fields = [],
        $account_log_fields;
    //各表连接所用字段
    public $data_log_join_fields,
        $ad_log_join_fields,
        $account_log_join_fields,
        $first_ad_log_join_fields,
        $third_ad_log_join_fields;

    //不同广告时ad_log和data_log之间连接条件的不同
    public $join_data_on,
        $join_reg_on,
        $join_overview_on,
        $join_account_log_on,
        $join_first_ad_log_on,
        $join_ad_log_on,
        $join_ad_log_select = [],
        $join_ad_log_group_by;

    public $join_standard = false;
    //是否需要连接
    public $need_join_inefficient = false;

    //
    public $field_list = [];
    //需要用计算百分比的字段
    public $all_calculate_percentage = [
        'click_rate', 'reg_rate', 'active_rate', 'convert_rate', 'pay_rate', 'click_activated_rate', 'reg_clk_rate', 'activate_register_rate', 'mini_game_register_rate', 'first_pay_rate', 'key_behavior_conversions_rate', 'retention_rate', 'app_retention_d3_rate', 'app_retention_d5_rate', 'app_retention_d7_rate', 'mini_game_retention_d1_rate', 'purchase_clk_rate', 'purchase_act_rate', 'purchase_roi', 'cheout_1d_rate', 'cheout_fd_reward', 'cheout_3d_rate', 'cheout_td_reward', 'cheout_5d_rate', 'cheout_7d_rate', 'cheout_ow_reward', 'cheout_tw_reward', 'cheout_15d_reward', 'cheout_om_reward'
    ];
    public $all_calculate_division = [
        'cpc', 'cpm', 'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'media_cost_per_reg', 'activated_cost', 'reg_cost', 'mini_game_register_cost', 'first_pay_cost', 'key_behavior_conversions_cost', 'mini_game_create_role_cost', 'retention_cost', 'app_retention_d3_cost', 'app_retention_d5_cost', 'app_retention_d7_cost', 'app_retention_lt7_cost', 'mini_game_retention_d1_cost', 'purchase_cost', 'purchase_act_arpu', 'purchase_reg_arpu', 'purchase_reg_arppu', 'cheout_1d_cost', 'cheout_3d_cost', 'cheout_5d_cost', 'cheout_7d_cost', 'mini_game_paying_arpu'

    ];
    //需要计算平均值的字段
    public $all_calculate_average = ['cpa_bid', 'deep_cpabid'];
    //所有需要计算的字段
    public $all_need_calculate = [
        'cpc', 'click', 'cpm', 'show', 'ori_cost', 'click_rate', 'cost_per_convert', 'convert', 'cost_per_active', 'cost_per_pay',
        'pay_count', 'reg_rate', 'active_rate', 'active_count', 'convert_rate', 'reg_count', 'media_cost_per_reg', 'pay_rate',
        'cost', 'activated_count', 'activated_cost', 'click_activated_rate', 'reg_pv', 'register_by_display_count', 'register_by_click_count', 'reg_cost', 'reg_clk_rate', 'activate_register_rate', 'reg_pla_pv', 'web_register_uv', 'mini_game_register_users', 'mini_game_register_cost', 'mini_game_register_rate', 'first_pay_count', 'first_pay_cost', 'first_pay_rate', 'leads_purchase_uv', 'mini_game_first_paying_users', 'key_behavior_conversions_count', 'key_behavior_conversions_cost', 'key_behavior_conversions_rate', 'game_authorize_count', 'game_create_role_count', 'mini_game_create_role_users', 'mini_game_create_role_cost', 'retention_count', 'retention_cost', 'retention_rate', 'app_retention_d2_pv', 'app_retention_d3_pv', 'app_retention_d3_uv', 'app_retention_d3_cost', 'app_retention_d3_rate', 'app_retention_d4_pv', 'app_retention_d5_pv', 'app_retention_d5_uv', 'app_retention_d5_cost', 'app_retention_d5_rate', 'app_retention_d6_pv', 'app_retention_d7_pv', 'app_retention_d7_uv', 'app_retention_d7_cost', 'app_retention_d7_rate', 'app_retention_lt7', 'app_retention_lt7_cost', 'mini_game_retention_d1', 'mini_game_retention_d1_cost', 'mini_game_retention_d1_rate', 'purchase_pv', 'purchase_imp_pv', 'purchase_clk_pv', 'purchase_amount', 'purchase_cost', 'purchase_clk_rate', 'purchase_act_rate', 'purchase_roi', 'purchase_act_arpu', 'purchase_reg_arpu', 'purchase_reg_arppu', 'cheout_pv_1d', 'cheout_fd', 'cheout_1d_cost', 'cheout_1d_rate', 'cheout_fd_reward', 'cheout_pv_3d', 'cheout_td', 'cheout_3d_cost', 'cheout_3d_rate', 'cheout_td_reward', 'cheout_pv_5d', 'cheout_5d_rate', 'cheout_5d_cost', 'cheout_pv_7d', 'cheout_ow', 'cheout_7d_cost', 'cheout_7d_rate', 'cheout_ow_reward', 'cheout_tw', 'cheout_tw_reward', 'purchase_clk_15d_pv', 'cheout_15d', 'cheout_15d_reward', 'purchase_clk_30d_pv', 'cheout_om', 'cheout_om_reward', 'mini_game_paying_arpu'
    ];
    //
    public $all_need_format = ['cost'];

    public $all_translate_to_cn = ['site_set', 'component_type'];

    public $filter_fields = [];
    public $calc_fields = [];

    public function __construct( $property = [] ) {

        $calc_fields = $filter_fields = [];
        if (!empty($property['filter'])) {
            foreach ($property['filter'] as $value) {
                if ($value['column'] != 'label') {
                    $filter_fields[] = $value['column'];
                }
            }
        }
        if (!empty($property['calc'])) {
            foreach ($property['calc'] as $value) {
                if (isset($value['column'])) {
                    $calc_fields[] = $value['column'];
                }
            }
        }

//        $field_list = array_flip(array_flip(array_merge($property['target'], $property['dimension'], $filter_fields, $calc_fields)));
        //检查media_type
//        $fields_str = implode(',', $field_list);
//        if (false !== strpos($fields_str, 'gdt_') && false !== strpos($fields_str, 'tt_')) {
//            throw new AppException('media_type参数错误');
//        } elseif (false !== strpos($fields_str, 'gdt_')) {
//            $this->media_type = MediaType::TENCENT;
//        } elseif (false !== strpos($fields_str, 'tt_') || false !== strpos($fields_str, 'is_inefficient')) {
//            $this->media_type = MediaType::TOUTIAO;
//        } elseif (false !== strpos($fields_str, 'ks_')) {
//            $this->media_type = MediaType::KUAISHOU;
//        }

        unset($property['media_type']);
        parent::__construct( $property );
    }

    private function handleCustomizedTarget()
    {
        // 把自定义指标里面需要算的指标拿出来
        $customized_target = [];
        if ($this->customized_target_ids) {
            $this->customized_target = (new CustomizedTargetModel())->getTargetById($this->customized_target_ids);
            foreach ($this->customized_target as $target_item) {
                $formula = json_decode($target_item->formula, true);
                if (isset($formula['column']) && is_array($formula['column'])) {
                    $customized_target = array_merge($customized_target, $formula['column']);
                }
                $target_item->formula = $formula;
            }

            $this->target = array_merge($this->target, $customized_target);
        }
    }
    /**
     * 获取表名
     * @param $index
     * @return mixed
     */
    public function getTable($index)
    {
        return TencentMaterialEffectSqlMap::TABLE[$index];
    }

    public function targetFormat()
    {
    }

    public function filterFormat()
    {
        foreach ($this->filter as $filter) {
            if ('game_id' === $filter['column'] ||
                'site_id' === $filter['column'] ||
                'theme_pid' === $filter['column'] ||
                'theme_id' === $filter['column'] ||
                'main_game_id' === $filter['column'] ||
                'root_game_id' === $filter['column'] ||
                'agent_id' === $filter['column'] ||
                'agent_group_id' === $filter['column'] ||
                'material_id' === $filter['column']) {
                $filter['column'] = 'platform-' . $filter['column'];
            }
            if (isset(TencentMaterialEffectSqlMap::DATA_LOG_FILTER[$filter['column']])) {
                $alias = explode('.', TencentMaterialEffectSqlMap::DATA_LOG_FILTER[$filter['column']]);
                $this->data_log_filter[] = SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
            }
            $this->commonPartWhere($filter);
        }
    }

    /**
     * 公共where字段筛选
     * @param $filter
     */
    private function commonPartWhere($filter)
    {
        if (isset(TencentMaterialEffectSqlMap::ACCOUNT_LOG_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, TencentMaterialEffectSqlMap::ACCOUNT_LOG_FILTER[$filter['column']]);
            $this->account_log_filter[] = $where;
        }

        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::MATERIAL_FILTER)) {
            $alias = explode('.', TencentMaterialEffectSqlMap::MATERIAL_FILTER[$filter['column']]);
            $rename = $filter['column'] === 'material_name' ? $alias[1] : '';
            $this->material_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::MATERIAL_FILE_FILTER)) {
            if ($filter['column'] === 'size') {
                $this->material_file_filter[] = SqlParser::get($filter, TencentMaterialEffectSqlMap::MATERIAL_FILE_FILTER[$filter['column']], '');
            } else {
                $alias = explode('.', TencentMaterialEffectSqlMap::MATERIAL_FILE_FILTER[$filter['column']]);
                $this->material_file_filter[] = SqlParser::get($filter, $alias[1], $alias[0]);
            }
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::MATERIAL_LABEL_FILTER)) {
            $alias = explode('.', TencentMaterialEffectSqlMap::MATERIAL_LABEL_FILTER[$filter['column']]);
            $this->material_label_filter[] = SqlParser::get($filter, $alias[1], (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::SITE_GAME_FILTER)) {
            $this->site_game_filter[] = SqlParser::get($filter, '', 'site_game');
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::AGENT_SITE_FILTER)) {
            $this->agent_site_filter[] = SqlParser::get($filter, '', 'agent_site');
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::AGENT_LEADER_GROUP_FILTER)) {
            $this->agent_leader_group_filter[] = SqlParser::get($filter, '', 'alg');
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::GAME_FILTER)) {
            $this->game_filter[] = SqlParser::get($filter, '', 'game');
        }
        if (array_key_exists($filter['column'], TencentMaterialEffectSqlMap::AWEME_FILTER)) {
            $alias = explode('.', TencentMaterialEffectSqlMap::AWEME_FILTER[$filter['column']]);
            $this->aweme_log_filter[] = SqlParser::get($filter, $alias[1], $alias[0]);
        }
    }

    public function dimensionFormat()
    {
        if ((in_array('material_id', $this->dimension))
            && !in_array('material_platform', $this->dimension)) {
            $this->dimension[] = 'material_platform';
        }
//        $this->dimension[] = 'ad2_id';
        $dimension = $this->dimension;
//        if (!in_array('media_type', $this->dimension)){
//            $dimension[] = 'media_type';
//        }
        foreach ($dimension as $v) {
            if (array_key_exists($v, TencentMaterialEffectSqlMap::DATA_LOG_GROUP_BY)) {
                $this->data_log_group_by[$v] = TencentMaterialEffectSqlMap::DATA_LOG_GROUP_BY[$v];
            }
            if (array_key_exists($v, TencentMaterialEffectSqlMap::MATERIAL_GROUP_BY)) {
                $this->material_group_by[$v] = TencentMaterialEffectSqlMap::MATERIAL_GROUP_BY[$v];
            }
            if (array_key_exists($v, TencentMaterialEffectSqlMap::MATERIAL_FILE_GROUP_BY)) {
                $this->material_file_group_by[$v] = TencentMaterialEffectSqlMap::MATERIAL_FILE_GROUP_BY[$v];
            }
            if (array_key_exists($v, TencentMaterialEffectSqlMap::AWEME_GROUP_BY)) {
                $this->aweme_group_by[$v] = TencentMaterialEffectSqlMap::AWEME_GROUP_BY[$v];
            }
//            if (array_key_exists($v, TencentMaterialEffectSqlMap::JOIN_AD_LOG_GROUP_BY)) {
//                if ($v !== 'media_type' || $this->media_type === 0) {
//                    $this->join_ad_log_group_by[] = TencentMaterialEffectSqlMap::JOIN_AD_LOG_GROUP_BY[$v];
//                }
//            }
        }
    }

    public function calcFormat()
    {
        if (!empty($this->calc)) {
            unset($this->calc['calc_condition']);
            foreach ($this->calc as $calc) {
                $calc['condition'] = $calc['operator'];
                $this->main_calc[] = SqlParser::get($calc);
            }
        }
    }

    public function paramHook()
    {
        if (!$this->dimension) {
            throw new AppException('请选择聚合维度');
        }
        if (!$this->cost_date) {
            throw new AppException('请选择计划消耗时间');
        }
        $this->data_log_table = $this->getTable('day_data_log');
        $this->ad_log_table = $this->getTable('ad_log');
        $this->account_log_table = $this->getTable('account_log_table');
        $this->video_log_table = $this->getTable('video_log');

//        $this->handleCustomizedTarget();
        $filter_fields = $calc_fields = [];
        if (!empty($this->filter)) {
            foreach ($this->filter as $value) {
                if ($value['column'] != 'label') {
                    $filter_fields[] = $value['column'];
                }
            }
        }
        if (!empty($this->calc)) {
            foreach ($this->calc as $value) {
                if (isset($value['column'])) {
                    $calc_fields[] = $value['column'];
                }
            }
        }
        if (in_array('urls', $this->target)) {
            $this->target = array_merge($this->target, ['signature', 'file_type', 'zx_play_url']);
        }
        if (in_array('game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['game_name']);
        }
        if (in_array('main_game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['main_game_name']);
        }
        if (in_array('root_game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['root_game_name']);
        }
        if (in_array('agent_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['agent_name']);
        }
        if (in_array('agent_group_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['agent_group_name']);
        }
        if (in_array('talent_account', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['talent_name']);
        }
        if (in_array('theme_pid', $this->target) && !in_array('theme_id', $this->target)) {
            $this->target = array_merge($this->target, ['theme_id']);
        }
        if (in_array('material_id', $this->dimension)) {
            $this->target = array_merge($this->target, ['material_name']);
        }
        if (in_array('label_pid', $this->target) && !in_array('label', $this->target)) {
            $this->target = array_merge($this->target, ['label']);
        }
        if (!in_array('material_platform', $this->target)) {
            $this->target[] = 'material_platform';
        }
        //两次array_flip去重性能更高
        $this->field_list = array_flip(array_flip(array_merge($this->target, $this->dimension, $filter_fields, $calc_fields, ['material_id'])));
        if (!in_array('account_name', $this->field_list) && in_array('account_id', $this->field_list)) {
            $this->field_list[] = 'account_name';
        }
        if (!in_array('ad1_id', $this->field_list) && in_array('ad1_name', $this->field_list)) {
            $this->field_list[] = 'ad1_id';
        }
//        $this->add_stand_filed();

        if (array_intersect(TencentMaterialEffectSqlMap::NEED_JOIN_INEFFICIENT_MATERIAL, $this->field_list)) {
            $this->need_join_inefficient = true;
        }
        //获取要select的字段
        foreach ($this->field_list as $item) {
            if (isset(TencentMaterialEffectSqlMap::DATA_LOG[$item])) {
                $this->data_log_fields[$item] = TencentMaterialEffectSqlMap::DATA_LOG[$item];
            }
            if (isset(TencentMaterialEffectSqlMap::MAIN_COMPUTE[$item])) {
                $value = TencentMaterialEffectSqlMap::MAIN_COMPUTE[$item];
                $need_sum_by_material = 1;//in_array('material_id', $this->dimension) && !in_array('signature', $this->dimension) ? 1 : 0;
                $value = is_array($value) ? $value[$need_sum_by_material] : $value;
                if (is_array($value)) {
                    $this->main_compute = array_merge($this->main_compute, $value);
                } else {
                    $this->main_compute[] = $value;
                }
            }
            if (isset(TencentMaterialEffectSqlMap::ACCOUNT_LOG[$item])) {
                $this->account_log_fields[$item] = TencentMaterialEffectSqlMap::ACCOUNT_LOG[$item];
            }
//            if (isset(TencentMaterialEffectSqlMap::JOIN_AD_LOG_SELECT[$item])) {
//                $this->join_ad_log_select[$item] = TencentMaterialEffectSqlMap::JOIN_AD_LOG_SELECT[$item];
//            }
            $this->commonPartSelect($item);
        }

//        $this->account_log_join_fields = TencentMaterialEffectSqlMap::ACCOUNT_JOIN_FIELDS;
//        $this->join_account_log_on = TencentMaterialEffectSqlMap::JOIN_ACCOUNT_ON;

        if ($this->c_author) {
            $this->c_author = array_map(function ($c_author) {
                return '"' . $c_author . '"';
            }, $this->c_author);
            $this->c_author = implode(',', $this->c_author);
        }
        if ($this->a_author) {
            $this->a_author = array_map(function ($a_author) {
                return '"' . $a_author . '"';
            }, $this->a_author);
            $this->a_author = implode(',', $this->a_author);
        }
        if ($this->m1_author) {
            $this->m1_author = array_map(function ($m1_author) {
                return '"' . $m1_author . '"';
            }, $this->m1_author);
            $this->m1_author = implode(',', $this->m1_author);
        }
        if ($this->m2_author) {
            $this->m2_author = array_map(function ($m2_author) {
                return '"' . $m2_author . '"';
            }, $this->m2_author);
            $this->m2_author = implode(',', $this->m2_author);
        }
        if ($this->m3_author) {
            $this->m3_author = array_map(function ($m3_author) {
                return '"' . $m3_author . '"';
            }, $this->m3_author);
            $this->m3_author = implode(',', $this->m3_author);
        }
        if ($this->m4_author) {
            $this->m4_author = array_map(function ($m4_author) {
                return '"' . $m4_author . '"';
            }, $this->m4_author);
            $this->m4_author = implode(',', $this->m4_author);
        }
        if ($this->m5_author) {
            $this->m5_author = array_map(function ($m5_author) {
                return '"' . $m5_author . '"';
            }, $this->m5_author);
            $this->m5_author = implode(',', $this->m5_author);
        }
        if ($this->actor) {
            $this->actor = array_map(function ($actor) {
                return '"' . $actor . '"';
            }, $this->actor);
            $this->actor = implode(',', $this->actor);
        }
        if ($this->shoot) {
            $this->shoot = array_map(function ($shoot) {
                return '"' . $shoot . '"';
            }, $this->shoot);
            $this->shoot = implode(',', $this->shoot);
        }
//        if ($this->material_create_date) {
//            if (array_key_exists('material_create_date',TencentMaterialEffectSqlMap::MATERIAL)) {
//                $this->material_fields['material_create_date'] = TencentMaterialEffectSqlMap::MATERIAL['material_create_date'];
//            }
//        }
    }

    /**
     * 公共select字段筛选
     * @param $select
     */
    private function commonPartSelect($select)
    {
        if (array_key_exists($select, TencentMaterialEffectSqlMap::MATERIAL_LOG)) {
            $this->material_log_fields[$select] = TencentMaterialEffectSqlMap::MATERIAL_LOG[$select];
        }
//        if (array_key_exists($select, TencentMaterialEffectSqlMap::AWEME_LOG)) {
//            $this->aweme_log_fields[$select] = TencentMaterialEffectSqlMap::AWEME_LOG[$select];
//        }
    }

//    private function add_stand_filed()
//    {
//        $items = ['first_day_roi', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7', 'rate_day_roi_15', 'rate_day_roi_30'];
//        foreach ($items as $item) {
//            if (in_array($item, $this->field_list)) {
//                $this->field_list[] = $item . '_standard_value';
//                $this->join_standard = true;
//            }
//        }
//    }
}