<?php
/**
 */

namespace App\Param\Material;

use App\Exception\AppException;
use App\Param\AbstractParam;
use App\Constant\MaterialAuthorEffectReportFilterSqlMap;
use App\Utils\SqlParser;

class MaterialAuthorEffectReportFilterParam extends AbstractParam
{
    public $media_type = 0;                  // 所选媒体
    public $platform = '';                   // 所选平台
    public $dimension = [];                  // 处理后的维度
    public $target = [];                     // 所有指标
    public $filter = [];                     // 所有筛选
    public $calc = [];                       // 所有数值条件
    public $calc_condition = 'and';          // 数值筛选的逻辑
    public $author;                          //
    public $material_create_date;            // 素材创建日期
    public $cost_date;                       // 消耗日期
    public $create_date;                     // 创建日期
    public $game_permission;                 // 游戏权限
    public $agent_permission;                // 渠道权限
    public $material_permission;             // 素材权限
    public $user_list;                       // 下属权限
    public $order;                           //
    public $limit = 5000;
    public $without_weight = 0;              //不算比例
    public $aggregation_time = '聚合';               // 时间聚合类型
    public $aggregation_time_material = '聚合';      // 时间聚合类型
    public $true_cost = 0;                   //实际消耗0否1是
    public $statistic_base_on = 0;           //统计方式，3:回流

    public $ad_create_time;
    //主sql的数值计算及筛选字段
    public $main_compute,
        $main_filter = [],
        $main_calc = [];
    public $overview_log = [];
    //统一的select，group by，where字段集
    public $data_log_table,
        $ad_log_table,
        $account_log_table,
        $first_ad_log_table,
        $third_ad_log_table,
        $forth_ad_log_table,
        $agent_leader_group_table,
        $overview_log_table;
    public $ad_log_fields,
        $data_log_fields=[],
        $account_log_fields,
        $first_ad_log_fields,
        $forth_ad_log_fields;
    public $ad_log_group_by,
        $data_log_filter = [],
        $ad_log_filter = [],
        $forth_ad_log_filter = [],

        $material_filter = [],
        $material_file_filter = [],
        $overview_log_filter,
        $agent_site_filter = [],
        $game_filter = [];

    //不同广告时ad_log和data_log之间连接条件的不同
    public $join_data_on,
        $join_reg_on,
        $join_overview_on,
        $join_account_log_on,
        $join_first_ad_log_on,
        $join_ad_log_on,
        $join_ad_log_select = [],
        $join_ad_log_group_by;

    public $join_standard = false;

    //
    public $field_list = [];
    //需要用计算百分比的字段
    public $all_calculate_percentage = [
        'action_uid_reg_rate', 'first_day_pay_rate', 'click_rate', 'reg_rate', 'active_rate', 'convert_rate', 'first_day_roi',
        'total_roi', 'first_day_roi_standard_value', 'rate_day_roi_7', 'rate_day_roi_30',
        'rate_day_roi_7_standard_value', 'rate_day_roi_30_standard_value', 'rate_day_standard_30', 'lifetime_roi'
    ];
    public $all_calculate_division = [
        'cost_per_reg', 'first_day_ltv', 'cost_per_first_day_pay', 'first_day_arppu',
    ];
    //需要计算平均值的字段
    public $all_calculate_average = ['cpa_bid', 'deep_cpabid'];
    //所有需要计算的字段
    public $all_need_calculate = [
        'cost', 'reg_uid_count', 'reg_muid_count', 'action_muid_count', 'first_day_ltv',
        'first_day_pay_count', 'first_day_pay_money', 'first_day_arppu', 'first_day_roi',  'rate_day_stay_2',
        'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'cost_process', 'click',
        'show', 'ori_cost', 'click_rate', 'convert', 'pay_count', 'active_count', 'convert_rate', 'reg_count',
        'media_cost_per_reg', 'pay_rate', 'action_uid_reg_rate', 'cost_per_reg', 'first_day_pay_rate', 'cost_per_first_day_pay',
        'total_roi', 'cost_in_30', 'cost_first_30_day_this_month', 'cost_first_60_day_this_month', 'first_day_roi_standard_value',
        'rate_day_roi_7', 'rate_day_roi_30', 'rate_day_roi_7_standard_value', 'rate_day_roi_30_standard_value', 'rate_day_standard_30', 'lifetime_money', 'lifetime_roi'
    ];
    //
    public $all_need_format = ['cost', 'reg_uid_count', 'sum_day_reg_uid_count', 'sum_day_action_muid_distinct_count', 'reg_muid_count',
        'action_muid_count', 'first_day_pay_money', 'first_day_pay_count', 'sum_total_pay_money', 'cost_in_30',
        'cost_first_30_day_this_month', 'cost_first_60_day_this_month', 'lifetime_money'];

    public $main_group_by = [];
    /**
     * 获取表名
     * @param $index
     * @return mixed
     */
    public function getTable($index)
    {
        return MaterialAuthorEffectReportFilterSqlMap::TABLE[$index];
    }

    public function targetFormat()
    {
    }

    public function filterFormat()
    {
        foreach ($this->filter as $filter) {
            if ('game_id' === $filter['column'] ||
                'site_id' === $filter['column'] ||
                'theme_pid' === $filter['column'] ||
                'main_game_id' === $filter['column'] ||
                'root_game_id' === $filter['column'] ||
                'material_id' === $filter['column']) {
                $filter['column'] = 'platform-' . $filter['column'];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::AD_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialAuthorEffectReportFilterSqlMap::AD_LOG_FILTER[$this->media_type][$filter['column']]);
                $this->ad_log_filter[] =SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::DATA_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialAuthorEffectReportFilterSqlMap::DATA_LOG_FILTER[$this->media_type][$filter['column']]);
                $this->data_log_filter[] = SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::FORTH_AD_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialAuthorEffectReportFilterSqlMap::FORTH_AD_LOG_FILTER[$this->media_type][$filter['column']]);
                $this->forth_ad_log_filter[] = SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::MAIN_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialAuthorEffectReportFilterSqlMap::MAIN_FILTER[$this->media_type][$filter['column']]);
                $this->main_filter[] = SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
            }
            $this->commonPartWhere($filter);
        }
    }

    /**
     * 公共where字段筛选
     * @param $filter
     */
    private function commonPartWhere($filter)
    {

        if (array_key_exists($filter['column'], MaterialAuthorEffectReportFilterSqlMap::MATERIAL_FILTER)) {
            $alias = explode('.', MaterialAuthorEffectReportFilterSqlMap::MATERIAL_FILTER[$filter['column']]);
            $rename = $filter['column'] === 'material_name' ? $alias[1] : '';
            $this->material_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], MaterialAuthorEffectReportFilterSqlMap::MATERIAL_FILE_FILTER)) {
            $this->material_file_filter[] = SqlParser::get($filter, MaterialAuthorEffectReportFilterSqlMap::MATERIAL_FILE_FILTER[$filter['column']]);
        }
        if (array_key_exists($filter['column'], MaterialAuthorEffectReportFilterSqlMap::OVERVIEW_LOG_FILTER)) {
            $alias = explode('.', MaterialAuthorEffectReportFilterSqlMap::OVERVIEW_LOG_FILTER[$filter['column']]);
            $this->overview_log_filter[] = SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], MaterialAuthorEffectReportFilterSqlMap::AGENT_SITE_FILTER)) {
            $this->agent_site_filter[] = SqlParser::get($filter, '', 'agent_site');
        }
        if (array_key_exists($filter['column'], MaterialAuthorEffectReportFilterSqlMap::GAME_FILTER)) {
            $this->game_filter[] = SqlParser::get($filter, '', 'game');
        }
    }

    public function dimensionFormat()
    {
        if (!in_array('platform', $this->dimension)) {
            $this->dimension[] = 'platform';
        }
        if (!in_array('signature', $this->dimension)) {
            $this->dimension[] = 'signature';
        }
        $this->dimension[] = 'ad2_id';
        $this->dimension[] = 'ad4_id';
        foreach ($this->dimension as $v) {
            if (array_key_exists($v, MaterialAuthorEffectReportFilterSqlMap::AD_LOG_GROUP_BY[$this->media_type])) {
                $this->ad_log_group_by[$v] = MaterialAuthorEffectReportFilterSqlMap::AD_LOG_GROUP_BY[$this->media_type][$v];
            }
            if (array_key_exists($v, MaterialAuthorEffectReportFilterSqlMap::JOIN_AD_LOG_GROUP_BY)) {
                $this->join_ad_log_group_by[] = MaterialAuthorEffectReportFilterSqlMap::JOIN_AD_LOG_GROUP_BY[$v];
            }

            if (isset(MaterialAuthorEffectReportFilterSqlMap::MAIN_GROUP_BY[$v])) {
                $this->main_group_by[] = MaterialAuthorEffectReportFilterSqlMap::MAIN_GROUP_BY[$v];
            }
        }
    }

    public function calcFormat()
    {
        if (!empty($this->calc)) {
            $calc_sql = '';
            $bind_value = [];
            $condition = $this->calc_condition;
            foreach ($this->calc as $v) {
                if (isset($v['column'])) {
                    if ('elt' === $v['operator']) {
                        $operator = '<=';
                    } else {
                        $operator = '>=';
                    }
                    $calc_sql .= $v['column'].$operator.'? '.$condition.' ';
                    $bind_value[] = $v['value'];
                }
            }
            //清除末尾的字符
            $calc_sql = rtrim($calc_sql, $condition.' ');
            $this->main_calc = [$calc_sql, $bind_value];
        }
    }

    public function paramHook()
    {
        if(!$this->dimension){
            throw new AppException('请选择聚合维度');
        }
        if(!$this->cost_date){
            throw new AppException('请选择计划消耗时间');
        }


        $this->data_log_table = $this->getTable('hour_data_log')[$this->media_type];
        $this->ad_log_table = $this->getTable('ad2_common_log')[$this->media_type];
        $this->account_log_table = $this->getTable('account_log_table')[$this->media_type];
        $this->first_ad_log_table = $this->getTable('ad1_common_log')[$this->media_type];
        $this->third_ad_log_table = $this->getTable('ad3_common_log')[$this->media_type];
        $this->forth_ad_log_table = $this->getTable('ad4_common_log')[$this->media_type];
        $this->agent_leader_group_table = $this->getTable('agent_leader_group');
        if (3 === (int)$this->statistic_base_on) {
            $this->overview_log_table = $this->getTable('overview_log')[3];
        } else {
            $this->overview_log_table = $this->getTable('overview_log')[0];
        }

        $filter_fields = $calc_fields = [];
        if (!empty($this->filter)) {
            foreach ($this->filter as $value) {
                $filter_fields[] = $value['column'];
            }
        }
        if (!empty($this->calc)) {
            foreach ($this->calc as $value) {
                if (isset($value['column'])) {
                    $calc_fields[] = $value['column'];
                }
            }
        }
        if (in_array('game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['game_name']);
        }
        if (in_array('main_game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['main_game_name']);
        }
        if (in_array('root_game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['root_game_name']);
        }
        if (in_array('theme_pid', $this->target) && !in_array('theme_id', $this->target)) {
            $this->target = array_merge($this->target, ['theme_id']);
        }
        if (in_array('material_id',$this->dimension )) {
            $this->target = array_merge($this->target, ['material_name']);
        }
        //两次array_flip去重性能更高
        $this->field_list = array_flip(array_flip(array_merge($this->target, $this->dimension, $filter_fields, $calc_fields, ['media_type', 'cost_date', 'material_create_date', 'ad2_id'])));

        $this->add_stand_filed();

        //获取要select的字段
        foreach ($this->field_list as $item) {
            if (isset(MaterialAuthorEffectReportFilterSqlMap::AD_LOG[$this->media_type][$item])) {
                $this->ad_log_fields[$item] = MaterialAuthorEffectReportFilterSqlMap::AD_LOG[$this->media_type][$item];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::DATA_LOG[$this->media_type][$item])) {
                $this->data_log_fields[$item] = MaterialAuthorEffectReportFilterSqlMap::DATA_LOG[$this->media_type][$item];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::MAIN_COMPUTE[$this->media_type][$this->without_weight][$item])) {
                $this->main_compute[$item] = MaterialAuthorEffectReportFilterSqlMap::MAIN_COMPUTE[$this->media_type][$this->without_weight][$item];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::ACCOUNT_LOG[$this->media_type][$item])) {
                $this->account_log_fields[$item] = MaterialAuthorEffectReportFilterSqlMap::ACCOUNT_LOG[$this->media_type][$item];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::FIRST_AD_LOG[$this->media_type][$item])) {
                $this->first_ad_log_fields[$item] = MaterialAuthorEffectReportFilterSqlMap::FIRST_AD_LOG[$this->media_type][$item];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::FORTH_AD_LOG[$this->media_type][$item])) {
                $this->forth_ad_log_fields[$item] = MaterialAuthorEffectReportFilterSqlMap::FORTH_AD_LOG[$this->media_type][$item];
            }
            if (isset(MaterialAuthorEffectReportFilterSqlMap::JOIN_AD_LOG_SELECT[$this->media_type][$item])) {
                $this->join_ad_log_select[$item] = MaterialAuthorEffectReportFilterSqlMap::JOIN_AD_LOG_SELECT[$this->media_type][$item];
            }
            $this->commonPartSelect($item);
        }
        if (in_array('material_num', $this->field_list)){
            $this->field_list[] = 'material_num_del';
        }
        if (in_array('material_file_num', $this->field_list)){
            $this->field_list[] = 'material_file_num_del';
        }
        if (in_array('material_file_ext_num', $this->field_list)){
            $this->field_list[] = 'material_file_ext_num_del';
        }

        $this->join_data_on = MaterialAuthorEffectReportFilterSqlMap::JOIN_DATA_ON[$this->media_type];
        $this->join_overview_on = MaterialAuthorEffectReportFilterSqlMap::JOIN_OVERVIEW_ON[$this->media_type];
        $this->join_account_log_on = MaterialAuthorEffectReportFilterSqlMap::JOIN_ACCOUNT_ON[$this->media_type];
        $this->join_first_ad_log_on = MaterialAuthorEffectReportFilterSqlMap::FIRST_AD_LOG_ON[$this->media_type];
        $this->join_ad_log_on = MaterialAuthorEffectReportFilterSqlMap::AD_LOG_ON[$this->media_type];

        $this->ad_create_time = MaterialAuthorEffectReportFilterSqlMap::AD_CREATE_TIME[$this->media_type];

        if ($this->aggregation_time === '按日') {
            $this->data_log_fields['cost_date'] = ['data_log.cost_date as cost_date'];
            $this->overview_log['log_date'] = ['overview_log.log_date as log_date'];
//            $this->data_log_fields['material_create_date'] = ['date(material_log.insert_time) as material_create_date'];
        } else if ($this->aggregation_time === '按月') {
            $this->data_log_fields['cost_date'] = ['YEAR_MONTH(data_log.cost_date) as cost_date'];
            $this->overview_log['log_date'] =  ['YEAR_MONTH(overview_log.log_date) as log_date'];
//            $this->data_log_fields['material_create_date'] = ['YEAR_MONTH(material_log.insert_time) as material_create_date'];
        } else if ($this->aggregation_time === '按周') {
            $this->data_log_fields['cost_date'] = ['YEARWEEK(data_log.cost_date, 3) as cost_date'];
            $this->overview_log['log_date'] =  ['YEARWEEK(overview_log.log_date, 3) as log_date'];
//            $this->data_log_fields['material_create_date'] = ['YEARWEEK(material_log.insert_time, 3) as material_create_date'];
        }

    }

    /**
     * 公共select字段筛选
     * @param $select
     */
    private function commonPartSelect($select)
    {
        if (array_key_exists($select,MaterialAuthorEffectReportFilterSqlMap::OVERVIEW_LOG)) {
            $this->overview_log[$select] = MaterialAuthorEffectReportFilterSqlMap::OVERVIEW_LOG[$select];
        }
    }

    private function add_stand_filed()
    {
        $items = ['first_day_roi', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7', 'rate_day_roi_15', 'rate_day_roi_30', 'rate_day_standard_30'];
        foreach ($items as $item) {
            if (in_array($item, $this->field_list)) {
                $this->field_list[] = $item . '_standard_value';
                $this->join_standard = true;
            }
        }
    }
}