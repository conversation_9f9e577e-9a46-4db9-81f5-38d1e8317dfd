<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;


class MaterialScoreRuleParam extends AbstractParam
{

    public $pid = 0;
    public $platform = '';
    public $name;
    public $create_date;
    public $create_date_start;
    public $create_date_end;
    public $theme_ids;
    public $target;
    public $is_limit = 0;
    public $cost_days;
    public $weight = 0;
    public $sss_grade;
    public $ss_grade;
    public $s_grade;
    public $a_grade;
    public $b_grade;
    public $c_grade;
    public $d_grade;
    public $creator_id;
    public $creator;
    public $editor_id;
    public $editor;
    public $create_time;
    public $update_time;
    public $is_del = 0;

    protected function paramHook()
    {
        if (!$this->target) {
            throw new AppException('指标不能为空');
        }
        if (!empty($this->theme_ids)) {
            $this->theme_ids = json_encode($this->theme_ids);
        } else {
            $this->theme_ids = json_encode([]);
        }
        if ($this->pid === 0) {
            if ($this->is_limit === 1) {
                if (!$this->cost_days) {
                    throw new AppException('消耗天数不能为空');
                }
            } else {
                $this->cost_days = 0;
            }
            if ($this->sss_grade <= $this->ss_grade) {
                throw new AppException('定档档位越低数值应该越小，数值过大！');
            }
            if ($this->ss_grade <= $this->s_grade) {
                throw new AppException('定档档位越低数值应该越小，数值过大！');
            }
            if ($this->s_grade <= $this->a_grade) {
                throw new AppException('定档档位越低数值应该越小，数值过大！');
            }
            if ($this->a_grade <= $this->b_grade) {
                throw new AppException('定档档位越低数值应该越小，数值过大！');
            }
            if ($this->b_grade <= $this->c_grade) {
                throw new AppException('定档档位越低数值应该越小，数值过大！');
            }
            if ($this->c_grade <= $this->d_grade) {
                throw new AppException('定档档位越低数值应该越小，数值过大！');
            }
            if (!empty($this->create_date)) {
                $this->create_date_start = $this->create_date[0];
                $this->create_date_end = $this->create_date[1];
            }
        } else {
            $this->cost_days = 0;
            if ($this->s_grade <= $this->a_grade) {
                throw new AppException('降档档位跨度越大数值应该越小，数值过大！');
            }
            if ($this->a_grade <= $this->b_grade) {
                throw new AppException('降档档位跨度越大数值应该越小，数值过大！');
            }
            if ($this->b_grade <= $this->c_grade) {
                throw new AppException('降档档位跨度越大数值应该越小，数值过大！');
            }
            if ($this->c_grade <= $this->d_grade) {
                throw new AppException('降档档位跨度越大数值应该越小，数值过大！');
            }
        }
        $this->editor_id = Container::getSession()->user_id;
        $this->editor = Container::getSession()->name;
        $this->update_time = time();
        if (!$this->create_time) {
            $this->creator_id = Container::getSession()->user_id;
            $this->creator = Container::getSession()->name;
            $this->create_time = time();
        } else {
            unset($this->creator_id);
            unset($this->creator);
            unset($this->create_time);
        }
        unset($this->create_date);
    }
}
