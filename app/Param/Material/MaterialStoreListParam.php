<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Exception\AppException;
use App\Param\AbstractParam;

class MaterialStoreListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $max_row;
    public $media_type;
    public $author;
    public $c_author;
    public $a_author;
    public $e_author;
    public $m1_author;
    public $m2_author;
    public $m3_author;
    public $m4_author;
    public $m5_author;
    public $actor;
    public $shoot;
    public $theme_ids;
    public $file_type;
    public $original;
    public $state;
    public $create_time;
    public $insert_time;
    public $width;
    public $height;
    public $no_creative;
    public $material_effect;
    public $platform;
    public $name;
    public $material_id;
    public $material_id_list;
    public $rule_id;
    public $effect_grade;
    public $order;
    public $is_priority;
    public $is_3d;
    public $label;
    public $is_authors;
    public $file_size;
    public $duration;
    public $bitrate;
    public $ext_status;

    public function orderFormat()
    {
        $this->order = $this->order ? json_decode($this->order, true) : [];
    }

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }

        if ($this->material_id) {
            if (!is_numeric($this->material_id)) {
                if (strpos($this->material_id, ',') !== false) {
                    $this->material_id_list = explode(',', $this->material_id);
                    $this->material_id = '';
                } else {
                    throw new AppException('请输入正确素材ID');
                }
            }
        }

        if ($this->width && !is_numeric($this->width)) {
            throw new AppException('请输入正确宽度数值');
        }
        if ($this->height && !is_numeric($this->height)) {
            throw new AppException('请输入正确高度数值');
        }
        if ($this->file_size && $this->file_size[1] && !is_numeric($this->file_size[1])) {
            throw new AppException('请输入正确文件大小数值');
        }
        if ($this->duration && $this->duration[1] && !is_numeric($this->duration[1])) {
            throw new AppException('请输入正确时长数值');
        }
        if ($this->bitrate && $this->bitrate[1] && !is_numeric($this->bitrate[1])) {
            throw new AppException('请输入正确码率数值');
        }

        if ($this->c_author) {
//            $this->c_author = array_map(function ($c_author) {
//                return '"' . $c_author . '"';
//            }, $this->c_author);
            $this->c_author = implode(',', $this->c_author);
        }
        if ($this->a_author) {
//            $this->a_author = array_map(function ($a_author) {
//                return '"' . $a_author . '"';
//            }, $this->a_author);
            $this->a_author = implode(',', $this->a_author);
        }
        if ($this->m1_author) {
//            $this->m1_author = array_map(function ($m1_author) {
//                return '"' . $m1_author . '"';
//            }, $this->m1_author);
            $this->m1_author = implode(',', $this->m1_author);
        }
        if ($this->m2_author) {
//            $this->m2_author = array_map(function ($m2_author) {
//                return '"' . $m2_author . '"';
//            }, $this->m2_author);
            $this->m2_author = implode(',', $this->m2_author);
        }
        if ($this->m3_author) {
//            $this->m3_author = array_map(function ($m3_author) {
//                return '"' . $m3_author . '"';
//            }, $this->m3_author);
            $this->m3_author = implode(',', $this->m3_author);
        }
        if ($this->m4_author) {
//            $this->m4_author = array_map(function ($m4_author) {
//                return '"' . $m4_author . '"';
//            }, $this->m4_author);
            $this->m4_author = implode(',', $this->m4_author);
        }
        if ($this->m5_author) {
//            $this->m5_author = array_map(function ($m5_author) {
//                return '"' . $m5_author . '"';
//            }, $this->m5_author);
            $this->m5_author = implode(',', $this->m5_author);
        }
        if ($this->actor) {
//            $this->actor = array_map(function ($actor) {
//                return '"' . $actor . '"';
//            }, $this->actor);
            $this->actor = implode(',', $this->actor);
        }
        if ($this->shoot) {
//            $this->shoot = array_map(function ($shoot) {
//                return '"' . $shoot . '"';
//            }, $this->shoot);
            $this->shoot = implode(',', $this->shoot);
        }

        if ($this->create_time) {
            $this->insert_time = $this->create_time;
            $this->create_time = [strtotime($this->create_time[0]), strtotime($this->create_time[1])];
        }
    }
}
