<?php

namespace App\Param\Material;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\CustomizedTargetModel;
use App\Param\AbstractParam;
use App\Constant\MaterialEffectReportFilterSqlMap;
use App\Utils\SqlParser;

class MaterialEffectReportFilterParam extends AbstractParam
{
    public $media_type = 0;                  // 所选媒体
    public $platform = '';                   // 所选平台
    public $dimension = [];                  // 处理后的维度
    public $target = [];                     // 所有指标
    public $filter = [];                     // 所有筛选
    public $calc = [];                       // 所有数值条件
    public $customized_target_ids = [];      //用户自定义指标id集合
    public $customized_target = [];          //用户自定义指标
    public $calc_condition = 'and';          // 数值筛选的逻辑
    public $people;                          //
    public $author;                          //
    public $c_author;                        //
    public $a_author;                        //
    public $m1_author;                       //
    public $m2_author;                       //
    public $m3_author;                       //
    public $m4_author;                       //
    public $m5_author;                       //
    public $actor;                           //
    public $shoot;                           //
    public $material_create_date;            // 素材创建日期
    public $cost_date;                       // 消耗日期
    public $create_date;                     // 创建日期
    public $game_permission;                 // 游戏权限
    public $agent_permission;                // 渠道权限
    public $material_permission;             // 素材权限
    public $user_list;                       // 下属权限
    public $user;
    public $user_type;
    public $order;                           //
    public $limit = 50000;
    public $true_cost = 0;                   //实际消耗0否1是
    public $statistic_base_on = 0;           //统计方式，3:回流
    public $publish_time;                    // 视频发布时间
    //需要三级维度表当左表的字段
    //public $third_class_dimension = ['ad3_id', 'ad3_name', 'count_ad3'];
    //ad_log连表所需字段
    public $ad_log_total_fields;
    public $ad_create_time;
    //二级表为了group by要查询的字段
    public $second_table_id;
    //主sql的数值计算及筛选字段
    public $main_compute = [],
        $main_filter = [],
        $main_calc = [];
    public $overview_log = [],
        $material_log_fields = [],
        $aweme_log_fields = [];
//        $site_game_fields,
//        $agent_site_fields,
//        $game_fields,
//        $material_fields,
//        $material_file_fields,
//        $material_label_fields;
    //统一的select，group by，where字段集
    public $data_log_table,
        $ad_log_table,
        $account_log_table,
        $first_ad_log_table,
        $third_ad_log_table,
        $overview_log_table,
        $forth_ad_log_table;
    public $ad_log_fields,
        $data_log_fields = [],
        $account_log_fields,
        $first_ad_log_fields,
        $third_ad_log_fields;
    public $ad_log_group_by,
        $material_group_by = [],
        $material_file_group_by = [],
        $aweme_group_by = [],
        $data_log_filter = [],
        $ad_log_filter = [],
        $first_ad_log_filter = [],
        $third_ad_log_filter = [],

        $material_filter = [],
        $material_file_filter = [],
        $material_label_filter = [],
        $overview_log_filter,
        $site_game_filter = [],
        $agent_site_filter = [],
        $agent_leader_group_filter = [],
        $game_filter = [],
        $account_log_filter = [],
        $aweme_log_filter = [],
        $inefficient_material_log_filter = [],
        $reject_material_log_filter = [];
    //各表连接所用字段
    public $data_log_join_fields,
        $ad_log_join_fields,
        $account_log_join_fields,
        $first_ad_log_join_fields,
        $third_ad_log_join_fields;

    //不同广告时ad_log和data_log之间连接条件的不同
    public $join_data_on,
        $join_reg_on,
        $join_overview_on,
        $join_account_log_on,
        $join_first_ad_log_on,
        $join_ad_log_on,
        $join_ad_log_select = [],
        $join_ad_log_group_by,
        $join_forth_ad_log_on,
        $forth_join_data_on,
        $forth_join_overview_on;

    public $join_standard = false;
    //是否需要连接
    public $need_join_inefficient = false;

    public $need_join_material_reject = false;

    /**
     * @var bool 按日
     */
    public $group_by_cost_date = false;
    /**
     * @var bool 按月
     */
    public $group_by_cost_month = false;

    //
    public $field_list = [];
    //需要用计算百分比的字段
    public $all_calculate_percentage = [
        'ks_ad_photo_played_2s_ratio', 'ks_play_3s_ratio', 'ks_play_5s_ratio', 'ks_ad_photo_played_10s_ratio', 'ks_play_end_ratio', 'ks_ad_photo_played_75percent_ratio', 'true_uid_rate', 'action_uid_reg_rate', 'first_day_pay_rate', 'click_rate', 'reg_rate', 'active_rate', 'convert_rate', 'first_day_roi',
        'total_roi', 'cost_process', 'pay_rate', 'tt_wifi_play_rate', 'tt_play_over_rate', 'tt_valid_play_rate', 'tt_play_duration_3s_rate',
        'tt_loan_completion_rate', 'tt_install_finish_rate', 'tt_download_start_rate', 'tt_deep_convert_rate',
        'tt_download_finish_rate', 'tt_next_day_open_rate', 'tt_game_addiction_rate', 'rate_day_stay_2',
        'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7', 'rate_day_roi_15',
        'rate_day_roi_30', 'reg_old_muid_count_per_reg', 'reg_old_clique_muid_count_per_reg', 'three_login_uid_count_per_reg',
        'first_day_roi_standard_value', 'rate_day_roi_2_standard_value', 'rate_day_roi_3_standard_value', 'rate_day_roi_7_standard_value',
        'rate_day_roi_15_standard_value', 'rate_day_roi_30_standard_value',
        'rate_login_stay_2', 'rate_login_stay_3', 'rate_login_stay_7', 'rate_login_stay_15', 'rate_login_stay_30',
        'rate_pay_login_stay_2', 'rate_pay_login_stay_3', 'rate_pay_login_stay_7', 'rate_pay_login_stay_15', 'rate_pay_login_stay_30',
        'rate_first_pay_login_stay_2', 'rate_first_pay_login_stay_3', 'rate_first_pay_login_stay_7', 'rate_first_pay_login_stay_15', 'rate_first_pay_login_stay_30', 'gdt_video_outer_play_time_avg_rate', 'gdt_video_outer_play_rate', 'first_day_pay_money_within_6_rate', 'lifetime_roi', 'key_role_level_count_rate',
    ];
    public $all_calculate_division = [
        'cost_per_reg', 'first_day_ltv', 'cost_per_first_day_pay', 'first_day_arppu',
        'cpc', 'cpm', 'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'media_cost_per_reg',
        'tt_average_play_time_per_play', 'tt_valid_play_cost', 'tt_loan_completion_cost', 'tt_download_finish_cost',
        'tt_download_start_cost', 'tt_install_finish_cost', 'tt_deep_convert_cost', 'tt_next_day_open_cost',
        'tt_game_addiction_cost', 'first_day_pay_times_cost', 'total_pay_times_cost', 'rate_day_stay_2_cost',
        'ltv', 'arppu', 'gdt_video_outer_play_time_count', 'gdt_video_outer_play_cost', 'cost_key_role_level_count'
    ];
    //需要计算平均值的字段
    public $all_calculate_average = ['cpa_bid', 'deep_cpabid'];
    //所有需要计算的字段
    public $all_need_calculate = [
        'ks_ad_photo_played_2s_ratio', 'ks_play_3s_ratio', 'ks_play_5s_ratio', 'ks_ad_photo_played_10s_ratio', 'ks_play_end_ratio', 'ks_ad_photo_played_75percent_ratio', 'true_uid_rate', 'true_uid_count', 'count', 'count_ad2', 'count_ad2_deliveried', 'count_ad2_delivering', 'count_ad2_undeliveried', 'count_cost_date',
        'count_creative', 'cost', 'reg_uid_count', 'reg_muid_count', 'action_muid_count', 'first_day_ltv',
        'first_day_pay_count', 'first_day_pay_money','seventh_day_pay_money','fifteenth_day_pay_money','thirty_day_pay_money','first_day_arppu', 'first_day_roi', 'rate_day_stay_2',
        'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7', 'rate_day_roi_15',
        'rate_day_roi_30', 'cost_process', 'click', 'show', 'ori_cost', 'click_rate', 'convert', 'pay_count', 'active_count', 'convert_rate', 'reg_count',
        'media_cost_per_reg', 'pay_rate', 'deep_cpabid', 'tt_attribution_next_day_open_cnt', 'tt_loan_completion',
        'tt_download_finish', 'tt_install_finish', 'tt_download_start', 'tt_click_install', 'tt_deep_convert',
        'tt_next_day_open', 'tt_game_addiction', 'tt_play_75_feed_break', 'tt_play_100_feed_break',
        'tt_play_duration_sum', 'tt_valid_play', 'tt_play_25_feed_break', 'tt_total_play', 'tt_wifi_play', 'tt_play_duration_3s',
        'tt_play_50_feed_break', 'tt_play_over', 'tt_location_click', 'tt_comment', 'tt_share', 'tt_follow',
        'tt_home_visited', 'tt_like', 'tt_ies_music_click', 'tt_ies_challenge_click', 'tt_dislike_cnt', 'tt_report_cnt', 'cpa_bid', 'budget',
        'action_uid_reg_rate', 'cost_per_reg', 'first_day_pay_rate', 'cost_per_first_day_pay', 'total_roi', 'cpc',
        'cpm', 'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'reg_rate', 'active_rate',
        'tt_average_play_time_per_play', 'tt_wifi_play_rate', 'tt_play_over_rate', 'tt_valid_play_cost', 'tt_play_duration_3s_rate',
        'tt_valid_play_rate', 'tt_loan_completion_cost', 'tt_loan_completion_rate', 'tt_download_finish_cost',
        'tt_install_finish_rate', 'tt_download_start_rate', 'tt_download_start_cost', 'tt_install_finish_cost',
        'tt_deep_convert_rate', 'tt_deep_convert_cost', 'tt_download_finish_rate', 'tt_next_day_open_rate',
        'tt_next_day_open_cost', 'tt_game_addiction_rate', 'tt_game_addiction_cost',
        'first_day_pay_times', 'total_pay_times', 'first_day_pay_times_cost', 'total_pay_times_cost', 'click_count',
        'rate_day_stay_2_cost', 'total_pay_count', 'total_pay_money', 'reg_old_muid_count', 'reg_old_clique_muid_count',
        'three_login_uid_count', 'reg_old_muid_count_per_reg', 'reg_old_clique_muid_count_per_reg', 'three_login_uid_count_per_reg',
        'new_pay_day_money', 'new_pay_day_count', 'cost_first_30_day', 'ltv', 'arppu', 'cost_first_30_day_this_month', 'cost_first_60_day_this_month',
        'first_day_roi_standard_value', 'rate_day_roi_2_standard_value', 'rate_day_roi_3_standard_value', 'rate_day_roi_7_standard_value',
        'rate_day_roi_15_standard_value', 'rate_day_roi_30_standard_value', 'standard_reached_cost', 'standard_unreached_cost',
        'rate_login_stay_2', 'rate_login_stay_3', 'rate_login_stay_7', 'rate_login_stay_15', 'rate_login_stay_30',
        'rate_pay_login_stay_2', 'rate_pay_login_stay_3', 'rate_pay_login_stay_7', 'rate_pay_login_stay_15', 'rate_pay_login_stay_30',
        'rate_first_pay_login_stay_2', 'rate_first_pay_login_stay_3', 'rate_first_pay_login_stay_7', 'rate_first_pay_login_stay_15', 'rate_first_pay_login_stay_30', 'gdt_video_outer_play10_count', 'gdt_video_outer_play25_count', 'gdt_video_outer_play50_count', 'gdt_video_outer_play75_count', 'gdt_video_outer_play95_count', 'gdt_video_outer_play100_count', 'gdt_video_outer_play_time_count', 'gdt_video_outer_play_time_avg_rate', 'gdt_video_outer_play_rate', 'gdt_video_outer_play_cost', 'gdt_video_outer_play3s_count', 'gdt_video_outer_play5s_count', 'gdt_video_outer_play7s_count', 'first_day_pay_money_within_6_rate', 'first_day_pay_money_within_6_count', 'lifetime_money', 'lifetime_roi', 'key_role_level_count', 'key_role_level_count_rate', 'cost_key_role_level_count',
        'male_count', 'female_count', 'age_18_24_count', 'age_25_30_count', 'age_31_40_count', 'age_41_50_count', 'age_over_50_count'

    ];
    //
    public $all_need_format = ['cost'];

    public $all_translate_to_cn = ['tt_materials_type', 'is_inefficient', 'tt_is_similar_material', 'tt_is_ad_high_quality', 'tt_is_first_publish_material', 'inventory_type', 'tt_is_ad_low_quality_material', 'tt_is_ecp_low_quality_material', 'tt_three_day_cost_five_standard', 'tt_first_day_roi_standard', 'gdt_inventory_subdivision', 'gdt_xqxs_inventory_subdivision'];

    public $filter_fields = [];
    public $calc_fields = [];

    // 腾讯资源位细分表
    public $tencent_site_set_ad4_table = '';

    public function __construct( $property = [] ) {

        $calc_fields = $filter_fields = [];
        if (!empty($property['filter'])) {
            foreach ($property['filter'] as $value) {
                if ($value['column'] != 'label') {
                    $filter_fields[] = $value['column'];
                }
            }
        }
        if (!empty($property['calc'])) {
            foreach ($property['calc'] as $value) {
                if (isset($value['column'])) {
                    $calc_fields[] = $value['column'];
                }
            }
        }

        $field_list = array_flip(array_flip(array_merge($property['target'], $property['dimension'], $filter_fields, $calc_fields)));
        //检查media_type
        $fields_str = implode(',', $field_list);
        if (false !== strpos($fields_str, 'gdt_') && false !== strpos($fields_str, 'tt_')) {
            throw new AppException('media_type参数错误');
        } elseif (false !== strpos($fields_str, 'gdt_')) {
            $this->media_type = MediaType::TENCENT;
        } elseif (false !== strpos($fields_str, 'tt_') || false !== strpos($fields_str, 'is_inefficient')) {
            $this->media_type = MediaType::TOUTIAO;
        } elseif (false !== strpos($fields_str, 'ks_')) {
            $this->media_type = MediaType::KUAISHOU;
        }

        unset($property['media_type']);
        parent::__construct( $property );
    }

    private function handleCustomizedTarget()
    {
        // 把自定义指标里面需要算的指标拿出来
        $customized_target = [];
        if ($this->customized_target_ids) {
            $this->customized_target = (new CustomizedTargetModel())->getTargetById($this->customized_target_ids);
            foreach ($this->customized_target as $target_item) {
                $formula = json_decode($target_item->formula, true);
                if (isset($formula['column']) && is_array($formula['column'])) {
                    $customized_target = array_merge($customized_target, $formula['column']);
                }
                $target_item->formula = $formula;
            }

            $this->target = array_merge($this->target, $customized_target);
//            var_dump($this->customized_target, $this->target);
        }
    }
    /**
     * 获取表名
     * @param $index
     * @return mixed
     */
    public function getTable($index)
    {
        return MaterialEffectReportFilterSqlMap::TABLE[$index];
    }

    public function targetFormat()
    {
    }

    public function filterFormat()
    {
        foreach ($this->filter as $filter) {
            if ('game_id' === $filter['column'] ||
                'site_id' === $filter['column'] ||
                'theme_pid' === $filter['column'] ||
                'theme_id' === $filter['column'] ||
                'main_game_id' === $filter['column'] ||
                'root_game_id' === $filter['column'] ||
                'agent_id' === $filter['column'] ||
                'agent_group_id' === $filter['column'] ||
                'material_id' === $filter['column']) {
                $filter['column'] = 'platform-' . $filter['column'];
            }
            if (isset(MaterialEffectReportFilterSqlMap::AD_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialEffectReportFilterSqlMap::AD_LOG_FILTER[$this->media_type][$filter['column']]);
                $rename = count(explode('-', $filter['column'])) > 1 ? '' : (count($alias) > 1 ? $alias[1] : '');
                $this->ad_log_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialEffectReportFilterSqlMap::DATA_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialEffectReportFilterSqlMap::DATA_LOG_FILTER[$this->media_type][$filter['column']]);
                $rename = count(explode('-', $filter['column'])) > 1 ? '' : (count($alias) > 1 ? implode('.',array_slice($alias,1)) : '');
                $this->data_log_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialEffectReportFilterSqlMap::FIRST_AD_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialEffectReportFilterSqlMap::FIRST_AD_LOG_FILTER[$this->media_type][$filter['column']]);
                $rename = count(explode('-', $filter['column'])) > 1 ? '' : (count($alias) > 1 ? $alias[1] : '');
                $this->first_ad_log_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialEffectReportFilterSqlMap::THIRD_AD_LOG_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialEffectReportFilterSqlMap::THIRD_AD_LOG_FILTER[$this->media_type][$filter['column']]);
                $rename = count(explode('-', $filter['column'])) > 1 ? '' : (count($alias) > 1 ? implode('.',array_slice($alias,1)) : '');
                $this->third_ad_log_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
            }
            if (isset(MaterialEffectReportFilterSqlMap::MAIN_FILTER[$this->media_type][$filter['column']])) {
                $alias = explode('.', MaterialEffectReportFilterSqlMap::MAIN_FILTER[$this->media_type][$filter['column']]);
                $this->main_filter[] = SqlParser::get($filter, '', (count($alias) > 1 ? $alias[0] : ''));
            }
            $this->commonPartWhere($filter);
        }
    }

    /**
     * 公共where字段筛选
     * @param $filter
     */
    private function commonPartWhere($filter)
    {
        if (isset(MaterialEffectReportFilterSqlMap::ACCOUNT_LOG_FILTER[$filter['column']])) {
            $where = SqlParser::get($filter, '', 'account_log');
            $this->account_log_filter[] = $where;
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::MATERIAL_FILTER)) {
            $alias = explode('.', MaterialEffectReportFilterSqlMap::MATERIAL_FILTER[$filter['column']]);
            $rename = $filter['column'] === 'material_name' ? $alias[1] : '';
            $this->material_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::MATERIAL_FILE_FILTER)) {
            if ($filter['column'] === 'size') {
                $this->material_file_filter[] = SqlParser::get($filter, MaterialEffectReportFilterSqlMap::MATERIAL_FILE_FILTER[$filter['column']], '');
            } else {
                $alias = explode('.', MaterialEffectReportFilterSqlMap::MATERIAL_FILE_FILTER[$filter['column']]);
                $this->material_file_filter[] = SqlParser::get($filter, $alias[1], $alias[0]);
            }
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::MATERIAL_LABEL_FILTER)) {
            $alias = explode('.', MaterialEffectReportFilterSqlMap::MATERIAL_LABEL_FILTER[$filter['column']]);
            $this->material_label_filter[] = SqlParser::get($filter, $alias[1], (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::OVERVIEW_LOG_FILTER)) {
            $alias = explode('.', MaterialEffectReportFilterSqlMap::OVERVIEW_LOG_FILTER[$filter['column']]);
            $rename = count(explode('-', $filter['column'])) > 1 ? '' : (count($alias) > 1 ? implode('.',array_slice($alias,1)) : '');
            $this->overview_log_filter[] = SqlParser::get($filter, $rename, (count($alias) > 1 ? $alias[0] : ''));
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::SITE_GAME_FILTER)) {
            $this->site_game_filter[] = SqlParser::get($filter, '', 'site_game');
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::AGENT_SITE_FILTER)) {
            $this->agent_site_filter[] = SqlParser::get($filter, '', 'agent_site');
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::AGENT_LEADER_GROUP_FILTER)) {
            $this->agent_leader_group_filter[] = SqlParser::get($filter, '', 'alg');
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::GAME_FILTER)) {
            $this->game_filter[] = SqlParser::get($filter, '', 'game');
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::AWEME_FILTER)) {
            $alias = explode('.', MaterialEffectReportFilterSqlMap::AWEME_FILTER[$filter['column']]);
            $this->aweme_log_filter[] = SqlParser::get($filter, $alias[1], $alias[0]);
        }
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::TOUTIAO_INEFFICIENT_FILTER)) {
            $alias = explode('.', MaterialEffectReportFilterSqlMap::TOUTIAO_INEFFICIENT_FILTER[$filter['column']]);
            $this->inefficient_material_log_filter[] = SqlParser::get($filter, $alias[1], $alias[0]);
        }
        // 特殊：根据value值匹配筛选条件
        if (array_key_exists($filter['column'], MaterialEffectReportFilterSqlMap::TOUTIAO_REJECT_FILTER)) {
            $this->reject_material_log_filter[] = [MaterialEffectReportFilterSqlMap::TOUTIAO_REJECT_FILTER[$filter['column']][$filter['value']],[]];
        }
    }

    public function dimensionFormat()
    {
        if ((in_array('material_id', $this->dimension))
            && !in_array('material_platform', $this->dimension)) {
            $this->dimension[] = 'material_platform';
        }
        $this->dimension[] = 'ad2_id';
        $dimension = $this->dimension;
        if (!in_array('media_type', $this->dimension)){
            $dimension[] = 'media_type';
        }
        if (($key = array_search('cost_date', $this->dimension)) !== false) {
            $this->group_by_cost_date = true;
            unset($this->dimension[$key]);
        }
        if (($key = array_search('cost_month', $this->dimension)) !== false) {
            $this->group_by_cost_month = true;
            unset($this->dimension[$key]);
        }
        foreach ($dimension as $v) {
            if (array_key_exists($v, MaterialEffectReportFilterSqlMap::AD_LOG_GROUP_BY[$this->media_type])) {
                $this->ad_log_group_by[$v] = MaterialEffectReportFilterSqlMap::AD_LOG_GROUP_BY[$this->media_type][$v];
            }
            if (array_key_exists($v, MaterialEffectReportFilterSqlMap::MATERIAL_GROUP_BY)) {
                $this->material_group_by[$v] = MaterialEffectReportFilterSqlMap::MATERIAL_GROUP_BY[$v];
            }
            if (array_key_exists($v, MaterialEffectReportFilterSqlMap::MATERIAL_FILE_GROUP_BY)) {
                $this->material_file_group_by[$v] = MaterialEffectReportFilterSqlMap::MATERIAL_FILE_GROUP_BY[$v];
            }
            if (array_key_exists($v, MaterialEffectReportFilterSqlMap::AWEME_GROUP_BY)) {
                $this->aweme_group_by[$v] = MaterialEffectReportFilterSqlMap::AWEME_GROUP_BY[$v];
            }
            if (array_key_exists($v, MaterialEffectReportFilterSqlMap::JOIN_AD_LOG_GROUP_BY[$this->media_type])) {
                if ($v !== 'media_type' || $this->media_type === 0) {
                    $this->join_ad_log_group_by[] = MaterialEffectReportFilterSqlMap::JOIN_AD_LOG_GROUP_BY[$this->media_type][$v];
                }
            }
        }
    }

    public function calcFormat()
    {
        if (!empty($this->calc)) {
            unset($this->calc['calc_condition']);
            foreach ($this->calc as $calc) {
                $calc['condition'] = $calc['operator'];
                $this->main_calc[] = SqlParser::get($calc);
            }
        }
    }

    public function paramHook()
    {
        if (!$this->dimension) {
            throw new AppException('请选择聚合维度');
        }
        if (!$this->cost_date) {
            throw new AppException('请选择计划消耗时间');
        }

        if (0 === $this->media_type || MediaType::TENCENT === $this->media_type) {
            $this->forth_ad_log_table = $this->getTable('ad4_common_log')[$this->media_type];
        }

        $this->data_log_table = $this->getTable('hour_data_log')[$this->media_type];
        $this->ad_log_table = $this->getTable('ad2_common_log')[$this->media_type];
        $this->account_log_table = $this->getTable('account_log_table')[$this->media_type];
        $this->first_ad_log_table = $this->getTable('ad1_common_log')[$this->media_type];
        $this->third_ad_log_table = $this->getTable('ad3_common_log')[$this->media_type];
        if (3 === (int)$this->statistic_base_on) {
            $this->overview_log_table = $this->getTable('overview_log')[3][$this->media_type];
        } else {
            $this->overview_log_table = $this->getTable('overview_log')[0][$this->media_type];
        }


        $this->handleCustomizedTarget();
        $filter_fields = $calc_fields = [];
        if (!empty($this->filter)) {
            foreach ($this->filter as $value) {
                if ($value['column'] != 'label') {
                    $filter_fields[] = $value['column'];
                }
            }
        }
        if (!empty($this->calc)) {
            foreach ($this->calc as $value) {
                if (isset($value['column'])) {
                    $calc_fields[] = $value['column'];
                }
            }
        }
        if (array_intersect($this->dimension, ['agent_leader', 'os', 'game_id', 'main_game_id', 'root_game_id']) &&
            !in_array('platform', $this->dimension)) {
            $this->dimension[] = 'platform';
        }
        if (in_array('urls', $this->target)) {
            $this->target = array_merge($this->target, ['signature', 'file_type', 'zx_play_url']);
        }
        if (in_array('game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['game_name']);
        }
        if (in_array('main_game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['main_game_name']);
        }
        if (in_array('root_game_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['root_game_name']);
        }
        if (in_array('agent_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['agent_name']);
        }
        if (in_array('agent_group_id', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['agent_group_name']);
        }
        if (in_array('talent_account', array_merge($this->target, $this->dimension))) {
            $this->target = array_merge($this->target, ['talent_name']);
        }
        if (in_array('theme_pid', $this->target) && !in_array('theme_id', $this->target)) {
            $this->target = array_merge($this->target, ['theme_id']);
        }
        if (in_array('material_id', $this->dimension)) {
            $this->target = array_merge($this->target, ['material_name']);
        }
        if (in_array('label_pid', $this->target) && !in_array('label', $this->target)) {
            $this->target = array_merge($this->target, ['label']);
        }
        if (!in_array('material_platform', $this->target)) {
            $this->target[] = 'material_platform';
        }
        //两次array_flip去重性能更高
        $this->field_list = array_flip(array_flip(array_merge($this->target, $this->dimension, $filter_fields, $calc_fields, ['media_type','ad2_id','material_id'])));
        if (!in_array('account_name', $this->field_list) && in_array('account_id', $this->field_list)) {
            $this->field_list[] = 'account_name';
        }
        if (!in_array('ad1_id', $this->field_list) && in_array('ad1_name', $this->field_list)) {
            $this->field_list[] = 'ad1_id';
        }
        if ($this->group_by_cost_date) {
            $this->field_list[] = 'cost_date';
        }
        if ($this->group_by_cost_month) {
            $this->field_list[] = 'cost_month';
        }
        $this->add_stand_filed();

        if (array_intersect(MaterialEffectReportFilterSqlMap::NEED_JOIN_INEFFICIENT_MATERIAL, $this->field_list)) {
            $this->need_join_inefficient = true;
        }

        if (array_intersect(MaterialEffectReportFilterSqlMap::NEED_JOIN_MATERIAL_REJECT, $this->field_list)) {
            $this->need_join_material_reject = true;
        }

        if (array_intersect(MaterialEffectReportFilterSqlMap::NEED_TENCENT_SITE_SET, $this->field_list)) {
            $this->tencent_site_set_ad4_table = 'ods_tencent_ad4_site_set_day_data_log';
        }

        if ($this->tencent_site_set_ad4_table) {
            $this->data_log_table = $this->tencent_site_set_ad4_table . ' as data_log';
        }

        //获取要select的字段
        foreach ($this->field_list as $item) {
            if (isset(MaterialEffectReportFilterSqlMap::AD_LOG[$this->media_type][$item])) {
                $this->ad_log_fields[$item] = MaterialEffectReportFilterSqlMap::AD_LOG[$this->media_type][$item];
            }
            if (isset(MaterialEffectReportFilterSqlMap::DATA_LOG[$this->media_type][$item])) {
                $this->data_log_fields[$item] = MaterialEffectReportFilterSqlMap::DATA_LOG[$this->media_type][$item];
            }
            if (isset(MaterialEffectReportFilterSqlMap::MAIN_COMPUTE[$this->media_type][$item])) {
                $value = MaterialEffectReportFilterSqlMap::MAIN_COMPUTE[$this->media_type][$item];
                $need_sum_by_material = 1;//in_array('material_id', $this->dimension) && !in_array('signature', $this->dimension) ? 1 : 0;
                $value = is_array($value) ? $value[$need_sum_by_material] : $value;
                if (is_array($value)) {
                    $this->main_compute = array_merge($this->main_compute, $value);
                } else {
                    $this->main_compute[] = $value;
                }
            }
            if (isset(MaterialEffectReportFilterSqlMap::ACCOUNT_LOG[$this->media_type][$item])) {
                $this->account_log_fields[$item] = MaterialEffectReportFilterSqlMap::ACCOUNT_LOG[$this->media_type][$item];
            }
            if (isset(MaterialEffectReportFilterSqlMap::FIRST_AD_LOG[$this->media_type][$item])) {
                $this->first_ad_log_fields[$item] = MaterialEffectReportFilterSqlMap::FIRST_AD_LOG[$this->media_type][$item];
            }
            if (isset(MaterialEffectReportFilterSqlMap::THIRD_AD_LOG[$this->media_type][$item])) {
                $this->third_ad_log_fields[$item] = MaterialEffectReportFilterSqlMap::THIRD_AD_LOG[$this->media_type][$item];
            }
            if (isset(MaterialEffectReportFilterSqlMap::JOIN_AD_LOG_SELECT[$this->media_type][$item])) {
                $this->join_ad_log_select[$item] = MaterialEffectReportFilterSqlMap::JOIN_AD_LOG_SELECT[$this->media_type][$item];
            }
            if (isset(MaterialEffectReportFilterSqlMap::OVERVIEW_LOG[$item])) {
                $this->overview_log[$item] = MaterialEffectReportFilterSqlMap::OVERVIEW_LOG[$item];
            }
            $this->commonPartSelect($item);
        }

        $this->data_log_join_fields = MaterialEffectReportFilterSqlMap::DATA_LOG_JOIN_FIELDS[$this->media_type];
        $this->ad_log_join_fields = MaterialEffectReportFilterSqlMap::AD_LOG_JOIN_FIELDS[$this->media_type];
        $this->join_data_on = MaterialEffectReportFilterSqlMap::JOIN_DATA_ON[$this->media_type];
        $this->join_reg_on = MaterialEffectReportFilterSqlMap::JOIN_REG_ON[$this->media_type];
        $this->join_overview_on = MaterialEffectReportFilterSqlMap::JOIN_OVERVIEW_ON[$this->media_type];
        $this->account_log_join_fields = MaterialEffectReportFilterSqlMap::ACCOUNT_JOIN_FIELDS[$this->media_type];
        $this->join_account_log_on = MaterialEffectReportFilterSqlMap::JOIN_ACCOUNT_ON[$this->media_type];
        $this->first_ad_log_join_fields = MaterialEffectReportFilterSqlMap::FIRST_AD_LOG_JOIN_FIELDS[$this->media_type];
        $this->third_ad_log_join_fields = MaterialEffectReportFilterSqlMap::THIRD_AD_LOG_JOIN_FIELDS[$this->media_type];
        $this->join_first_ad_log_on = MaterialEffectReportFilterSqlMap::FIRST_AD_LOG_ON[$this->media_type];
        $this->join_ad_log_on = MaterialEffectReportFilterSqlMap::AD_LOG_ON[$this->media_type];
        $this->join_forth_ad_log_on = MaterialEffectReportFilterSqlMap::FORTH_AD_LOG_ON[$this->media_type] ?? [];
        $this->forth_join_data_on = MaterialEffectReportFilterSqlMap::FOURTH_JOIN_DATA_LOG_ON;
        $this->forth_join_overview_on = MaterialEffectReportFilterSqlMap::FOURTH_JOIN_OVERVIEW_LOG_ON;
        $this->second_table_id = MaterialEffectReportFilterSqlMap::SECOND_TABLE_ID[$this->media_type];
        $this->ad_log_total_fields = MaterialEffectReportFilterSqlMap::ALL_JOIN_FIELDS[$this->media_type];
        $this->ad_create_time = MaterialEffectReportFilterSqlMap::AD_CREATE_TIME[$this->media_type];


        if ($this->c_author) {
            $this->c_author = array_map(function ($c_author) {
                return '"' . $c_author . '"';
            }, $this->c_author);
            $this->c_author = implode(',', $this->c_author);
        }
        if ($this->a_author) {
            $this->a_author = array_map(function ($a_author) {
                return '"' . $a_author . '"';
            }, $this->a_author);
            $this->a_author = implode(',', $this->a_author);
        }
        if ($this->m1_author) {
            $this->m1_author = array_map(function ($m1_author) {
                return '"' . $m1_author . '"';
            }, $this->m1_author);
            $this->m1_author = implode(',', $this->m1_author);
        }
        if ($this->m2_author) {
            $this->m2_author = array_map(function ($m2_author) {
                return '"' . $m2_author . '"';
            }, $this->m2_author);
            $this->m2_author = implode(',', $this->m2_author);
        }
        if ($this->m3_author) {
            $this->m3_author = array_map(function ($m3_author) {
                return '"' . $m3_author . '"';
            }, $this->m3_author);
            $this->m3_author = implode(',', $this->m3_author);
        }
        if ($this->m4_author) {
            $this->m4_author = array_map(function ($m4_author) {
                return '"' . $m4_author . '"';
            }, $this->m4_author);
            $this->m4_author = implode(',', $this->m4_author);
        }
        if ($this->m5_author) {
            $this->m5_author = array_map(function ($m5_author) {
                return '"' . $m5_author . '"';
            }, $this->m5_author);
            $this->m5_author = implode(',', $this->m5_author);
        }
        if ($this->actor) {
            $this->actor = array_map(function ($actor) {
                return '"' . $actor . '"';
            }, $this->actor);
            $this->actor = implode(',', $this->actor);
        }
        if ($this->shoot) {
            $this->shoot = array_map(function ($shoot) {
                return '"' . $shoot . '"';
            }, $this->shoot);
            $this->shoot = implode(',', $this->shoot);
        }
//        if ($this->material_create_date) {
//            if (array_key_exists('material_create_date',MaterialEffectReportFilterSqlMap::MATERIAL)) {
//                $this->material_fields['material_create_date'] = MaterialEffectReportFilterSqlMap::MATERIAL['material_create_date'];
//            }
//        }
    }

    /**
     * 公共select字段筛选
     * @param $select
     */
    private function commonPartSelect($select)
    {
        if (array_key_exists($select, MaterialEffectReportFilterSqlMap::MATERIAL_LOG)) {
            $this->material_log_fields[$select] = MaterialEffectReportFilterSqlMap::MATERIAL_LOG[$select];
        }
        if (array_key_exists($select, MaterialEffectReportFilterSqlMap::AWEME_LOG)) {
            $this->aweme_log_fields[$select] = MaterialEffectReportFilterSqlMap::AWEME_LOG[$select];
        }
    }

    private function add_stand_filed()
    {
        $items = ['first_day_roi', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7', 'rate_day_roi_15', 'rate_day_roi_30'];
        foreach ($items as $item) {
            if (in_array($item, $this->field_list)) {
                $this->field_list[] = $item . '_standard_value';
                $this->join_standard = true;
            }
        }
    }
}
