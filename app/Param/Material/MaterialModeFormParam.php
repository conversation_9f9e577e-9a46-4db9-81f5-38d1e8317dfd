<?php

namespace App\Param\Material;

use App\Exception\AppException;
use App\Model\SqlModel\Zeda\MaterialModeModel;
use App\Param\AbstractParam;


class MaterialModeFormParam extends AbstractParam
{
    public $id;
    public $media_type;
    public $name;
    public $description;
    public $width;
    public $height;
    public $is_group;
    public $aspect_ratio;
    public $max_file_size;
    public $file_type;
    public $formats;
    public $min_ber;
    public $max_ber;
    public $min_fps;
    public $max_fps;
    public $min_duration;
    public $max_duration;
    public $sequence;
    public $category;
    public $creative_id;
    public $api_site;
    public $word_number;
    public $create_time;

    public function validateFileType()
    {
        //判断图片和视频类型的素材是否选择了错误的文件后缀
        if (!isset($this->file_type)) {
            throw new AppException('参数错误');
        }
        if ($this->file_type == MaterialModeModel::PICTURE_FILE_TYPE &&
            array_intersect($this->formats, MaterialModeModel::VIDEO_FILE_TYPES)) {
            throw new AppException('编辑失败：后缀错误');
        } elseif ($this->file_type == MaterialModeModel::VIDEO_FILE_TYPE &&
            array_intersect($this->formats, MaterialModeModel::PICTURE_FILE_TYPES)) {
            throw new AppException('编辑失败：后缀错误');
        }
    }

    public function toInsertData()
    {
        $this->formats = json_encode($this->formats);
        $this->create_time = time();
        $data = $this->toParam();
        unset($data['id']);
        return $data;
    }

    public function toEditData()
    {
        $this->formats = json_encode($this->formats);
        return $this->toParam();
    }
}