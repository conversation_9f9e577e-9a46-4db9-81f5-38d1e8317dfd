<?php
/**
 * 视频混剪头条评估 方便入表
 */

namespace App\Param\Material;

use App\Param\AbstractParam;


class VideoClipProjectAttributeParam extends AbstractParam
{
    public $task_id = 0;
    public $task_name = "";
    public $project_id = 0;
    public $project_name = "";
    public $compose_id = 0;
    public $compose_name = "";
    public $signature = "";
    public $url = "";
    public $file_name = "";
    public $clique_id;
    public $clique_name;
    public $selling_points = [];
    public $promotions = [];
    public $targets = [];
    public $pain_points = [];
    public $extra_require = "";
    public $reference = [];
    public $synopsis = [];
    public $content = [];
    public $track_list = [];
    public $cover_file = [];
    public $voice_type = [];
    public $analyse = [];
    public $scale = 1; // 视频比例 1: 9:16, 2：16：9
    public $first_gpt_id = "";
    public $second_gpt_id = "";
    public $state = 1;
    public $source_is_inefficient = 0;
    public $source_is_ad_low_quality_material = 0;
    public $is_inefficient = 0;
    public $is_ad_low_quality_material = 0;
    public $clip_mode = 1; // 混剪模式 1：同分组匹配分镜 2：同分组或不同分组匹配分镜
    public $account_id = 0;
    public $ext_type = 0; // 0：基准素材 1: 横屏转竖屏或竖屏转横屏 2：AI口播替换分镜原声
    public $valid_type = 1; // 1.同游戏将基准素材以及包装素材传到一个账号下 2.同游戏将基准素材传到一个账号下，然后再将包装后的素材传到另外一个账号 3.同游戏将基准素材以及包装素材传到两个不同主体

    protected function paramHook()
    {
        if (is_string($this->selling_points)) {
            $this->selling_points = json_decode($this->selling_points, true);
        }

        if (is_string($this->promotions)) {
            $this->promotions = json_decode($this->promotions, true);
        }

        if (is_string($this->targets)) {
            $this->targets = json_decode($this->targets, true);
        }

        if (is_string($this->pain_points)) {
            $this->pain_points = json_decode($this->pain_points, true);
        }

        if (is_string($this->reference)) {
            $this->reference = json_decode($this->reference, true);
        }

        if (is_string($this->track_list)) {
            $this->track_list = json_decode($this->track_list, true);
        }

        if (is_string($this->synopsis)) {
            $this->synopsis = json_decode($this->synopsis, true);
        }

        if (is_string($this->content)) {
            $this->content = json_decode($this->content, true);
        }

        if (is_string($this->cover_file)) {
            $this->cover_file = json_decode($this->cover_file, true);
        }

        if (is_string($this->voice_type)) {
            $this->voice_type = json_decode($this->voice_type, true);
        }

        if (is_string($this->analyse)) {
            $this->analyse = json_decode($this->analyse, true);
        }
    }

    public function toInsertData()
    {
        return [
            "task_id" => $this->task_id,
            "task_name" => $this->task_name,
            "project_id" => $this->project_id,
            "project_name" => $this->project_name,
            "compose_id" => $this->compose_id,
            "compose_name" => $this->compose_name,
            "clique_id" => $this->clique_id,
            "clique_name" => $this->clique_name,
            "selling_points" => json_encode($this->selling_points, JSON_UNESCAPED_UNICODE),
            "promotions" => json_encode($this->promotions, JSON_UNESCAPED_UNICODE),
            "targets" => json_encode($this->targets, JSON_UNESCAPED_UNICODE),
            "pain_points" => json_encode($this->pain_points, JSON_UNESCAPED_UNICODE),
            "extra_require" => $this->extra_require,
            "reference" => json_encode($this->reference, JSON_UNESCAPED_UNICODE),
            "synopsis" => json_encode($this->synopsis, JSON_UNESCAPED_UNICODE),
            "content" => json_encode($this->content, JSON_UNESCAPED_UNICODE),
            "track_list" => json_encode($this->track_list, JSON_UNESCAPED_UNICODE),
            "cover_file" => $this->cover_file ? json_encode($this->cover_file, JSON_UNESCAPED_UNICODE) : '{}',
            "voice_type" => $this->voice_type ? json_encode($this->voice_type, JSON_UNESCAPED_UNICODE) : '{}',
            "analyse" => $this->analyse ? json_encode($this->analyse, JSON_UNESCAPED_UNICODE) : '{}',
            "scale" => $this->scale,
            "first_gpt_id" => $this->first_gpt_id,
            "second_gpt_id" => $this->second_gpt_id,
            "state" => $this->state,
            "source_is_inefficient" => $this->source_is_inefficient,
            "source_is_ad_low_quality_material" => $this->source_is_ad_low_quality_material,
            "is_inefficient" => $this->is_inefficient,
            "is_ad_low_quality_material" => $this->is_ad_low_quality_material,
            "clip_mode" => $this->clip_mode,
            "account_id" => $this->account_id,
            "ext_type" => $this->ext_type,
            "valid_type" => $this->valid_type,
        ];
    }
}
