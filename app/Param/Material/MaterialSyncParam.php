<?php
/**
 * Created by PhpStorm.
 * User: zzh
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\Material;

use App\Param\AbstractParam;

class MaterialSyncParam extends AbstractParam
{
    public $sync_platform = '';
    public $sync_platform_material_id = 0;
    public $sync_theme_id = 0;
    public $sync_create_mode = 0;
    public $sync_material_name = '';
    public $sync_file_type = 0;
    public $sync_file_list = [];
    public $sync_extend_size_video = [];
    public $platform = '';
    public $material_id = 0;
    public $theme_id = 0;
    public $material_name = '';
    public $state_code = 0;
    public $error = '';

    public $ext = [];

    public function toData()
    {
        $data = $this->toArray();
        $data['ext'] = (object)$data['ext'];
        $data['sync_file_list'] = json_encode($data['sync_file_list'], JSON_UNESCAPED_UNICODE);
        $data['sync_extend_size_video'] = json_encode($data['sync_extend_size_video'], JSON_UNESCAPED_UNICODE);
        $data['ext'] = json_encode($data['ext'], JSON_UNESCAPED_UNICODE);
        return $data;
    }
}