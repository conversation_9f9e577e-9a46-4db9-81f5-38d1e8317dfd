<?php
/**
 * 视频混剪组合
 */

namespace App\Param\Material;

use App\Param\AbstractParam;


class VideoClipComposeParam extends AbstractParam
{
    public $id = 0;
    public $name = "";
    public $material_id = 0;
    public $clique_id;
    public $clique_name;
    public $selling_points = [];
    public $promotions = [];
    public $targets = [];
    public $pain_points = [];
    public $extra_require = "";
    public $reference = []; // 脚本参考列表 ["material_file_id" => [], "video_script" => []]
    public $video_script_list = [];
    public $voice_type_list = [];
    public $bgm_list = [];
    public $cover_file = [];
    public $gpt_id = "";
    public $first_day_roi = "";
    public $material_cost_time = [];
    public $scale = 1; // 视频比例 1：9:16, 2：16:9
    public $state = 1;
    public $creator_id = 0;
    public $creator = "";
    public $create_time;
    public $update_time;
    public $error_msg = "";

    protected function paramHook()
    {
        if (is_string($this->selling_points)) {
            $this->selling_points = json_decode($this->selling_points, true);
        }

        if (is_string($this->promotions)) {
            $this->promotions = json_decode($this->promotions, true);
        }

        if (is_string($this->targets)) {
            $this->targets = json_decode($this->targets, true);
        }

        if (is_string($this->pain_points)) {
            $this->pain_points = json_decode($this->pain_points, true);
        }

        if (is_string($this->reference)) {
            $this->reference = json_decode($this->reference, true);
        }

        if (is_string($this->video_script_list)) {
            $this->video_script_list = json_decode($this->video_script_list, true);
        }

        if (is_string($this->voice_type_list)) {
            $this->voice_type_list = json_decode($this->voice_type_list, true);
        }

        if (is_string($this->bgm_list)) {
            $this->bgm_list = json_decode($this->bgm_list, true);
        }

        if (is_string($this->cover_file)) {
            $this->cover_file = json_decode($this->cover_file, true);
        }

        if (is_string($this->material_cost_time)) {
            $this->material_cost_time = json_decode($this->material_cost_time, true);
        }

        if (!$this->create_time) {
            $this->create_time = date("Y-m-d H:i:s");
        }
        if (!$this->update_time) {
            $this->update_time = date("Y-m-d H:i:s");
        }
    }

    public function toInsertData()
    {
        return [
            "name" => $this->name,
            "material_id" => $this->material_id,
            "clique_id" => $this->clique_id,
            "clique_name" => $this->clique_name,
            "selling_points" => json_encode($this->selling_points, JSON_UNESCAPED_UNICODE),
            "promotions" => json_encode($this->promotions, JSON_UNESCAPED_UNICODE),
            "targets" => json_encode($this->targets, JSON_UNESCAPED_UNICODE),
            "pain_points" => json_encode($this->pain_points, JSON_UNESCAPED_UNICODE),
            "extra_require" => $this->extra_require,
            "reference" => json_encode($this->reference, JSON_UNESCAPED_UNICODE),
            "video_script_list" => json_encode($this->video_script_list, JSON_UNESCAPED_UNICODE),
            "voice_type_list" => json_encode($this->voice_type_list, JSON_UNESCAPED_UNICODE),
            "bgm_list" => json_encode($this->bgm_list, JSON_UNESCAPED_UNICODE),
            "cover_file" => $this->cover_file ? json_encode($this->cover_file, JSON_UNESCAPED_UNICODE) : '{}',
            "scale" => $this->scale,
            "gpt_id" => $this->gpt_id,
            "first_day_roi" => $this->first_day_roi,
            "material_cost_time" => json_encode($this->material_cost_time, JSON_UNESCAPED_UNICODE),
            "state" => $this->state,
            "creator_id" => $this->creator_id,
            "creator" => $this->creator,
            "create_time" => $this->create_time,
            "update_time" => $this->update_time,
        ];
    }

    public function toUpdateData()
    {
        $data = [];
        $data["video_script_list"] = $this->video_script_list ? json_encode($this->video_script_list) : '[]';
        $data["state"] = $this->state;
        $data["gpt_id"] = $this->gpt_id;
        $data["error_msg"] = $this->error_msg;
        return $data;
    }
}
