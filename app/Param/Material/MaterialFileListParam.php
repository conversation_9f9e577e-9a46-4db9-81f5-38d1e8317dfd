<?php
/**
 * Created by PhpStorm.
 * User: zzh
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\Material;

use App\Param\AbstractParam;

class MaterialFileListParam extends AbstractParam
{
    public $id;
    public $ids;
    public $effect_grade7;
    public $effect_grade30;
    public $media_type;
    public $file_type;
    public $filename;
    public $name;
    public $width;
    public $scale;
    public $is_vertical;
    public $is_horizontal;
    public $height;
    public $max_size;
    public $material_id;
    public $material_ids;
    public $platform;
    public $no_creative;
    public $create_time;
    public $insert_time;
    public $page;
    public $rows;
    public $uploader;
    public $order_by;
    public $theme_ids;
    public $original;
    public $width_height_group;
    public $is_ext;
    public $min_duration;

    protected function paramHook()
    {
        if ($this->create_time) {
            $this->insert_time = $this->create_time;
            if (!is_numeric($this->create_time[0]) && !is_numeric($this->create_time[1])) {
                $this->create_time = [strtotime($this->create_time[0]), strtotime($this->create_time[1])];
            } else {
                $this->create_time = [$this->create_time[0] / 1000, $this->create_time[1] / 1000];
            }
        }

        if ($this->width_height_group) {
            $result = [];
            foreach ($this->width_height_group as $key => $width_height) {
                $width_height_array = explode('*', $width_height);
                $result[] = $width_height_array;
            }
            $this->width_height_group = $result;
        }

        if ($this->order_by) {
            $this->order_by['order'] = str_replace('ending', '', $this->order_by['order']);
        }

        if (!$this->page) {
            $this->page = 1;
        }

        if (!$this->rows) {
            $this->rows = 10;
        }

        if (strpos($this->id, ',') !== false) {
            $this->ids = explode(',', $this->id);
            $this->ids = array_filter($this->ids);
            $this->id = 0;
        }

        if (strpos($this->material_id, ',') !== false) {
            $this->material_ids = explode(',', $this->material_id);
            $this->material_ids = array_filter($this->material_ids);
            $this->material_id = 0;
        }
    }
}