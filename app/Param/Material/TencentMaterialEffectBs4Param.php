<?php

namespace App\Param\Material;

use App\Param\AbstractParam;

class TencentMaterialEffectBs4Param extends AbstractParam
{
    public $files = [];                      //signature+file_type的列表
    public $files_filtered = [];
    public $group_image_list = [];


    public function paramHook()
    {
        if ($this->files) {
            foreach ($this->files as $file) {
                if ((int)$file['file_type'] === 1) {
                    $this->files_filtered['images'][] = $file['signature'];
                } elseif ((int)$file['file_type'] === 2) {
                    $this->files_filtered['videos'][] = $file['signature'];
                } elseif ((int)$file['file_type'] === 7) {
                    $g_i = json_decode($file['signature'], true);
                    $this->group_image_list[] = $g_i;
                    $this->files_filtered['group_images'] = array_merge($this->files_filtered['group_images'] ?? [], $g_i);
                } else {
                    $this->files_filtered['no_file_type'][] =  $file['signature'];
                }
            }
        }
    }
}