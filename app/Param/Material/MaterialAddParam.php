<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class MaterialAddParam extends AbstractParam
{

    public $material_id;
    public $platform = '';
    public $name;
    public $media_type = 0;
    public $original;
    public $theme_id;
    public $label = [];
    public $attraction = [];
    public $core_elements = [];
    public $core_selling_points = [];
    public $file_type;
    public $author;
    public $c_author;
    public $a_author;
    public $e_author;
    public $system_author;
    public $is_3d;
    public $m1_author;
    public $m2_author;
    public $m3_author;
    public $m4_author;
    public $m5_author;
    public $is_immortal;
    public $actor;
    public $shoot;
    public $is_public = 1;
    public $is_priority = 0;
    public $create_time;
    public $update_time;
    public $last_uploaded_time;

    public $working_hours = 0.0;

    protected function paramHook()
    {
        if (mb_strlen($this->name) > 50) {
            throw new AppException('素材名称限制在50字符以内,请重新输入');
        }
        $special_str = ['<', '>', '&', '‘', '”', '/', '\\', ' ', PHP_EOL];
        foreach ($special_str as $str) {
            if (strpos($this->name, $str) !== false) {
                throw new AppException('素材名称有特殊字符 ' . $str . ' 请重新输入');
            }
        }
        if (!$this->theme_id) {
            throw new AppException('题材类型不能为空');
        }
        if (!$this->author) {
            $this->author = Container::getSession()->name;
        }
        $check_str = ['', 'null', 'NULL', '[]'];
        if (!empty($this->c_author)) {
            $this->c_author = $this->check_authors($this->c_author, $check_str);
            $this->c_author = json_encode($this->c_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->c_author = json_encode([]);
        }
        if (!empty($this->a_author)) {
            $this->a_author = $this->check_authors($this->a_author, $check_str);
            $this->a_author = json_encode($this->a_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->a_author = json_encode([]);
        }
        if (!empty($this->e_author)) {
            $this->e_author = $this->check_authors($this->e_author, $check_str);
            $this->e_author = json_encode($this->e_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->e_author = json_encode([]);
        }
        if (!empty($this->system_author)) {
            $this->system_author = $this->check_authors($this->system_author, $check_str);
            $this->system_author = json_encode($this->system_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->system_author = json_encode([]);
        }
        if (!empty($this->m1_author)) {
            $this->m1_author = $this->check_authors($this->m1_author, $check_str);
            $this->m1_author = json_encode($this->m1_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m1_author = json_encode([]);
        }
        if (!empty($this->m2_author)) {
            $this->m2_author = $this->check_authors($this->m2_author, $check_str);
            $this->m2_author = json_encode($this->m2_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m2_author = json_encode([]);
        }
        if (!empty($this->m3_author)) {
            $this->m3_author = $this->check_authors($this->m3_author, $check_str);
            $this->m3_author = json_encode($this->m3_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m3_author = json_encode([]);
        }
        if (!empty($this->m4_author)) {
            $this->m4_author = $this->check_authors($this->m4_author, $check_str);
            $this->m4_author = json_encode($this->m4_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m4_author = json_encode([]);
        }
        if (!empty($this->m5_author)) {
            $this->m5_author = $this->check_authors($this->m5_author, $check_str);
            $this->m5_author = json_encode($this->m5_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m5_author = json_encode([]);
        }
        if (!empty($this->actor)) {
            $this->actor = $this->check_authors($this->actor, $check_str);
            $this->actor = json_encode($this->actor, JSON_UNESCAPED_UNICODE);
        } else {
            $this->actor = json_encode([]);
        }
        if (!empty($this->shoot)) {
            $this->shoot = $this->check_authors($this->shoot, $check_str);
            $this->shoot = json_encode($this->shoot, JSON_UNESCAPED_UNICODE);
        } else {
            $this->shoot = json_encode([]);
        }
        $this->update_time = time();
        $this->last_uploaded_time = time();
        if (!$this->create_time) {
            $this->create_time = time();
        }
    }

    public function toArray()
    {
        $data = parent::toArray();
        unset($data['attraction']);
        unset($data['core_elements']);
        unset($data['core_selling_points']);
        return $data;
    }

    private function check_authors($authors, $check_str)
    {
        return array_filter($authors, function ($value) use ($check_str) {
            return !in_array($value, $check_str);
        });
    }
}
