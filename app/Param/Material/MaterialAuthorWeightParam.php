<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Exception\AppException;
use App\Param\AbstractParam;


class MaterialAuthorWeightParam extends AbstractParam
{

    public $platform = '';
    public $material_id;
    public $weights;
    public $insert_time;

    protected function paramHook()
    {
        if (!$this->platform) {
            throw new AppException('平台不能为空');
        }
        if (!$this->material_id) {
            throw new AppException('素材ID不能为空');
        }
        if (!$this->weights) {
            throw new AppException('比例不能为空');
        } else {
            $this->weights = array_map(function ($weight){ return json_decode($weight, true); }, $this->weights);
            $total = array_sum(array_map(function ($weight){ return is_numeric($weight['weight']) ? $weight['weight'] : 0 ; }, $this->weights));
            if($total != 100){
                throw new AppException('总比例必须为100%，当前总比例：' . $total . '%');
            }
        }
        $this->insert_time = date('Y-m-d H:i:s');
    }
}
