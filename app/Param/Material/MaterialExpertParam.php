<?php
/**
 * Created by PhpStorm.
 * User: zzh
 * Date: 2020/03/13
 * Time: 16:10
 */


namespace App\Param\Material;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class MaterialExpertParam extends AbstractParam
{
    public $platform;
    public $name;
    public $type;
    public $url_list;
    public $creator;

    public $batch_ad_advance_day = 0;
    public $material_time_list = [];
    public $sign_time_list = [];
    public $insert_time;
    public $update_time;
    public $status;

    public function paramHook()
    {
        if (!$this->creator) {
            $this->creator = Container::getSession()->name;
        }

        if ($this->material_time_list) {
            $this->material_time_list = array_filter($this->material_time_list, function ($t) {
                return ($t['start_time'] ?? '') && ($t['end_time'] ?? '') && ($t['agent_leader_group_list'] ?? '');
            });
        }

        if ($this->sign_time_list) {
            $this->sign_time_list = array_filter($this->sign_time_list, function ($t) {
                return ($t['start_time'] ?? '') && ($t['end_time'] ?? '') && ($t['agent_leader_group_list'] ?? '');
            });
        }
    }

    public function toData()
    {
        $data = $this->toArray();
        if (!$data['url_list']) {
            throw new AppException('达人图片不能为空');
        }
        if (!$data['platform']) {
            throw new AppException('达人平台不能为空');
        }
        if (!$data['name']) {
            throw new AppException('达人名字不能为空');
        }
        if (!$data['sign_time_list']) {
            throw new AppException('达人签约时间不能为空');
        }

        if ($data['sign_time_list']) {
            foreach ($data['sign_time_list'] as $sign_time) {
                if (!isset($sign_time['media_type_list'])) {
                    throw new AppException('媒体类型没有传参');
                }
            }
        }

        $data['url_list'] = json_encode($data['url_list'], JSON_UNESCAPED_UNICODE);
        $data['material_time_list'] = json_encode($data['material_time_list'], JSON_UNESCAPED_UNICODE);
        $data['sign_time_list'] = json_encode($data['sign_time_list'], JSON_UNESCAPED_UNICODE);
        unset($data['insert_time']);
        unset($data['update_time']);
        unset($data['last_editor']);
        return $data;
    }
}
