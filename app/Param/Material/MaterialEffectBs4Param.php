<?php
/**
 */

namespace App\Param\Material;

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\AbstractParam;

class MaterialEffectBs4Param extends AbstractParam
{
    public $files = [];                      // media_type+signature+file_type的列表
    public $files_filtered = [];
    public $group_image_list = [];


    public function paramHook()
    {
        if ($this->files) {
            foreach ($this->files as $file) {
                if (isset($file['item_id']) && (int)$file['item_id']) {
                    $this->files_filtered[$file['media_type']]['item_id'][] = $file['item_id'];
                } elseif (in_array($file['file_type'], ['image', 'image_list'])) {
                    if (strpos($file['signature'], '[') !== false) {
                        $g_i = json_decode($file['signature'], true);
                        $this->group_image_list[] = $g_i;
                        $this->files_filtered[$file['media_type']]['group_images'] = array_merge($this->files_filtered[$file['media_type']]['group_images'] ?? [], $g_i);
                    } else {
                        $this->files_filtered[$file['media_type']]['images'][] = $file['signature'];
                    }
                } elseif ($file['file_type'] === 'video') {
                    $this->files_filtered[$file['media_type']]['videos'][] = $file['signature'];
                } else {
                    $this->files_filtered[$file['media_type']]['no_file_type'][] = $file['signature'];
                }
            }
        }
    }
}
