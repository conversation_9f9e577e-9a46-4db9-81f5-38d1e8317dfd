<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Param\AbstractParam;

class MaterialUploadMediaTaskListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $platform;
    public $media_type;
    public $file_type;
    public $material_name;
    public $material_id;
    public $create_time;
    public $uploader;
    public $upload_state;

    protected function paramHook()
    {
        if ($this->create_time) {
            $this->create_time[0] = mb_strlen($this->create_time[0]) > 10 ? strtotime($this->create_time[0]) : strtotime($this->create_time[0] . ' 00:00:00');
            $this->create_time[1] = mb_strlen($this->create_time[1]) > 10 ? strtotime($this->create_time[1]) : strtotime($this->create_time[1] . ' 23:59:59');
        }

        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }
    }
}
