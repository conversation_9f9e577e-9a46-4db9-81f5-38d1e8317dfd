<?php

namespace App\Param\Material;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class MaterialScriptAddParam extends AbstractParam
{

    public $material_id;
    public $platform = '';
    public $name;
    public $media_type = 0;
    public $original;
    public $theme_id;
    public $label = [];
    public $file_type;
    public $author;
    public $c_author;
    public $a_author;
    public $is_3d = 0;
    public $m1_author;
    public $m2_author;
    public $m3_author;
    public $m4_author;
    public $m5_author;
    public $is_immortal = 0;
    public $actor;
    public $shoot;
    public $is_public = 1;
    public $is_priority = 0;
    public $create_time;
    public $update_time;
    public $last_uploaded_time;

    protected function paramHook()
    {
        $check_str = ['', 'null', 'NULL', '[]'];
        if (!empty($this->c_author)) {
            $this->c_author = $this->check_authors($this->c_author, $check_str);
            $this->c_author = json_encode($this->c_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->c_author = json_encode([]);
        }
        if (!empty($this->a_author)) {
            $this->a_author = $this->check_authors($this->a_author, $check_str);
            $this->a_author = json_encode($this->a_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->a_author = json_encode([]);
        }
        if (!empty($this->m1_author)) {
            $this->m1_author = $this->check_authors($this->m1_author, $check_str);
            $this->m1_author = json_encode($this->m1_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m1_author = json_encode([]);
        }
        if (!empty($this->m2_author)) {
            $this->m2_author = $this->check_authors($this->m2_author, $check_str);
            $this->m2_author = json_encode($this->m2_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m2_author = json_encode([]);
        }
        if (!empty($this->m3_author)) {
            $this->m3_author = $this->check_authors($this->m3_author, $check_str);
            $this->m3_author = json_encode($this->m3_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m3_author = json_encode([]);
        }
        if (!empty($this->m4_author)) {
            $this->m4_author = $this->check_authors($this->m4_author, $check_str);
            $this->m4_author = json_encode($this->m4_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m4_author = json_encode([]);
        }
        if (!empty($this->m5_author)) {
            $this->m5_author = $this->check_authors($this->m5_author, $check_str);
            $this->m5_author = json_encode($this->m5_author, JSON_UNESCAPED_UNICODE);
        } else {
            $this->m5_author = json_encode([]);
        }
        if (!empty($this->actor)) {
            $this->actor = $this->check_authors($this->actor, $check_str);
            $this->actor = json_encode($this->actor, JSON_UNESCAPED_UNICODE);
        } else {
            $this->actor = json_encode([]);
        }
        if (!empty($this->shoot)) {
            $this->shoot = $this->check_authors($this->shoot, $check_str);
            $this->shoot = json_encode($this->shoot, JSON_UNESCAPED_UNICODE);
        } else {
            $this->shoot = json_encode([]);
        }
        $this->update_time = time();
        $this->last_uploaded_time = time();
        if (!$this->create_time) {
            $this->create_time = time();
        }
    }

    private function check_authors($authors, $check_str)
    {
        return array_filter($authors, function ($value) use ($check_str) {
            return !in_array($value, $check_str);
        });
    }
}
