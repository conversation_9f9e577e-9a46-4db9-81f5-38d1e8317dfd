<?php

namespace App\Param\Material;

use App\Param\AbstractParam;

class MaterialSequenceListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $folder_name;
    public $modify_time;
    public $insert_time;
    public $create_time;
    public $folder_id;
    public $folder_id_list;
    public $primary_theme;
    public $secondary_theme;

    public $label;
    public $description;
    public $creator;

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }

        if ($this->create_time) {
            $this->create_time = [date("Y-m-d 00:00:00", strtotime($this->create_time[0])), date("Y-m-d 23:59:59", strtotime($this->create_time[1]))];
            $this->insert_time = $this->create_time;
        }

        if ($this->modify_time) {
            $this->modify_time = [date("Y-m-d 00:00:00", strtotime($this->modify_time[0])), date("Y-m-d 23:59:59", strtotime($this->modify_time[1]))];
        }
    }
}
