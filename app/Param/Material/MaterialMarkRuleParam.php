<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;


class MaterialMarkRuleParam extends AbstractParam
{

    public $platform = '';
    public $name;
    public $theme_id;
    public $creator_id;
    public $creator;
    public $editor_id;
    public $editor;
    public $create_time;
    public $update_time;
    public $is_del = 0;
    public $calc = [];

    protected function paramHook()
    {
        if (!$this->calc) {
            throw new AppException('数值条件不能为空');
        } else {
            $this->calc = json_encode($this->calc);
        }
        $this->editor_id = Container::getSession()->user_id;
        $this->editor = Container::getSession()->name;
        $this->update_time = time();
        if (!$this->create_time) {
            $this->creator_id = Container::getSession()->user_id;
            $this->creator = Container::getSession()->name;
            $this->create_time = time();
        } else {
            unset($this->creator_id);
            unset($this->creator);
            unset($this->create_time);
        }
        unset($this->create_date);
    }
}
