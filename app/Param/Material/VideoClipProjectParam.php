<?php
/**
 * 视频混剪工程
 */

namespace App\Param\Material;

use App\Param\AbstractParam;


class VideoClipProjectParam extends AbstractParam
{
    public $id = 0;
    public $project_name = "";
    public $compose_id = 0;
    public $last_task_id = 0;
    public $signature = "";
    public $file_name = "";
    public $url = "";
    public $width = 0;
    public $height = 0;
    public $file_scale = 0;
    public $bitrate = 0;
    public $size = 0;
    public $duration = 0;
    public $format = "";
    public $synopsis = [];
    public $content = [];
    public $track_list = [];
    public $cover_file = [];
    public $voice_type = [];
    public $analyse = [];
    public $scale = 1; // 视频比例 1: 9:16, 2：16：9
    public $gpt_id = "";
    public $state = 1;
    public $error_msg = "";
    public $creator_id = 0;
    public $creator = "";
    public $editor_id = 0;
    public $editor = "";

    protected function paramHook()
    {
        if (is_string($this->track_list)) {
            $this->track_list = json_decode($this->track_list, true);
        }

        if (is_string($this->synopsis)) {
            $this->synopsis = json_decode($this->synopsis, true);
        }

        if (is_string($this->content)) {
            $this->content = json_decode($this->content, true);
        }

        if (is_string($this->cover_file)) {
            $this->cover_file = json_decode($this->cover_file, true);
        }

        if (is_string($this->voice_type)) {
            $this->voice_type = json_decode($this->voice_type, true);
        }

        if (is_string($this->analyse)) {
            $this->analyse = json_decode($this->analyse, true);
        }
    }

    public function toInsertData()
    {
        return [
            "project_name" => $this->project_name,
            "compose_id" => $this->compose_id,
            "last_task_id" => $this->last_task_id,
            "signature" => $this->signature,
            "duration" => $this->duration,
            "synopsis" => json_encode($this->synopsis, JSON_UNESCAPED_UNICODE),
            "content" => json_encode($this->content, JSON_UNESCAPED_UNICODE),
            "track_list" => json_encode($this->track_list, JSON_UNESCAPED_UNICODE),
            "cover_file" => $this->cover_file ? json_encode($this->cover_file, JSON_UNESCAPED_UNICODE) : '{}',
            "voice_type" => $this->voice_type ? json_encode($this->voice_type, JSON_UNESCAPED_UNICODE) : '{}',
            "analyse" => $this->analyse ? json_encode($this->analyse, JSON_UNESCAPED_UNICODE) : '{}',
            "scale" => $this->scale,
            "gpt_id" => $this->gpt_id,
            "state" => $this->state,
            "error_msg" => $this->error_msg,
            "creator_id" => $this->creator_id,
            "creator" => $this->creator,
            "editor_id" => $this->editor_id,
            "editor" => $this->editor
        ];
    }

    public function toUpdateData()
    {
        $data = $this->toArray();
        $data["track_list"] = $data['track_list'] ? json_encode($this->track_list) : '[]';
        $data["synopsis"] = $data['synopsis'] ? json_encode($this->synopsis) : '[]';
        $data["content"] = $data['content'] ? json_encode($this->content) : '[]';
        $data["cover_file"] = $this->cover_file ? json_encode($this->cover_file, JSON_UNESCAPED_UNICODE) : '{}';
        $data["voice_type"] = $this->voice_type ? json_encode($this->voice_type, JSON_UNESCAPED_UNICODE) : '{}';
        $data["analyse"] = $this->analyse ? json_encode($this->analyse, JSON_UNESCAPED_UNICODE) : '{}';
        unset($data["id"]);
        return $data;
    }
}
