<?php

namespace App\Param\Material;

use App\Param\AbstractParam;

class MaterialSequenceFileListParam extends AbstractParam
{
    public $create_time;
    public $insert_time;

    public $server_id;
    public $folder_path;
    public $signature;
    public $file_name;
    public $format;
    public $signature_list;
    public $page;
    public $rows;


    protected function paramHook()
    {
        if ($this->create_time) {
            $this->insert_time = $this->create_time;
            if (!is_numeric($this->create_time[0]) && !is_numeric($this->create_time[1])) {
                $this->create_time = [strtotime($this->create_time[0]), strtotime($this->create_time[1])];
            } else {
                $this->create_time = [$this->create_time[0] / 1000, $this->create_time[1] / 1000];
            }
        }

        if (!$this->page) {
            $this->page = 1;
        }

        if (!$this->rows) {
            $this->rows = 10;
        }

        if (strpos($this->signature, ',') !== false) {
            $this->signature_list = explode(',', $this->signature);
            $this->signature_list = array_filter($this->signature_list);
            $this->signature = "";
        }
    }
}
