<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Param\AbstractParam;

class MaterialAprovedRateListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $platform;

    public $media_type;

    public $material_id;
    
    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }
    }
}
