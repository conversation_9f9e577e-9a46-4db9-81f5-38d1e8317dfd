<?php
/**
 * User: gzz
 * Date: 2020-03-19
 */

namespace App\Param\Material;

use App\Param\AbstractParam;


class KuaishouMaterialPhotoUploadParam extends AbstractParam
{

    public $platform;
    public $account_id;
    public $file_type;
    public $file_id;
    public $filename;
    public $uploader;
    public $upload_state = 0;
    public $create_time;
    public $create_type;
    public $photo_id = '';
    public $signature;
    public $id;

    protected function paramHook()
    {
        if (!$this->create_time) {
            $this->create_time = time();
        }
    }

    public function toInsertArray()
    {
        $data = $this->toArray();
        unset($data['id']);
        return $data;
    }
}
