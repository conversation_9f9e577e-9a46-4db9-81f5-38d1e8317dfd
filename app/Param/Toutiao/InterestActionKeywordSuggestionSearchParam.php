<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/02/24
 * Time: 14:08
 */

namespace App\Param\Toutiao;

use App\Exception\AppException;
use App\Param\AbstractParam;

class InterestActionKeywordSuggestionSearchParam extends AbstractParam
{
    /**
     * @var string $access_token 必定要的接口头数据
     */
    public $access_token;

    /**
     * @var string $advertiser_id 广告主ID-必填
     */
    public $advertiser_id;

    /**
     * @var int $id 关键词id-必填
     */
    public $id;

    /**
     * @var string $tag_type 查询类型：类目还是关键词,允许值：CATEGORY（类目）、KEYWORD（关键词）
     */
    public $tag_type = 'KEYWORD';

    /**
     * @var string $targeting_type 兴趣还是行为,允许值：ACTION（行为）、INTEREST（兴趣）
     */
    public $targeting_type;

    /**
     * @var string $action_scene 行为场景 允许值: "E-COMMERCE","NEWS","APP" -必填
     */
    public $action_scene;

    /**
     * @var string $action_days 行为天数
     */
    public $action_days;

    public function paramHook()
    {
        if (!$this->access_token) {
            throw new AppException('access_token传参有误');
        }

        if (!$this->advertiser_id) {
            throw new AppException('advertiser_id传参有误');
        }

        if (!$this->targeting_type) {
            throw new AppException('targeting_type传参有误');
        }

        if ($this->targeting_type == 'ACTION' && !$this->action_scene) {
            throw new AppException('action_scene传参有误');
        }

        if ($this->targeting_type == 'ACTION' && !$this->action_days) {
            throw new AppException('action_days传参有误');
        }

    }

    /**
     * InterestActionKeywordSearchParam类生成请求体
     * @return array
     */
    public function toRequestBody()
    {
        $body = [
            'advertiser_id' => $this->advertiser_id,
            'id' => $this->id,
            'tag_type' => $this->tag_type,
            'targeting_type' => $this->targeting_type,
        ];
        if ($this->targeting_type == 'ACTION') {
            $body['action_scene'] = json_encode($this->action_scene);
            $body['action_days'] = $this->action_days;
        }
        return $body;
    }
}