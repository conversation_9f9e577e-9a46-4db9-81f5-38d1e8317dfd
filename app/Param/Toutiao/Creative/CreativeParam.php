<?php

namespace App\Param\Toutiao\Creative;

use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Creative\CreativeModel;
use App\Param\AbstractParam;
use App\Service\MediaAD\MediaToutiao;

/**
 * 创建自定义创意-程序化创意（营销链路）
 * Class CustomCreativeParam
 * @package App\Param\Toutiao
 * User: zsp
 * Date: 2021/11/17 0017
 * Time: 16:01
 */
class CreativeParam extends AbstractParam
{
    //-------------------------------------- 自定义创意 start --------------------------------------
    /**
     * @var array $title_materials 标题素材
     */
    public $title_materials = [];

    /**
     * @var array $image_materials 创意图片素材
     */
    public $image_materials = [];

    /**
     * @var array $video_materials 视频素材信息
     */
    public $video_materials = [];

    /**
     * @var array $component_materials 组件信息
     */
    public $component_materials = [];

    /**
     * @var array $abstract_materials 摘要素材
     */
    public $abstract_materials = [];

    /**
     * @var string $promotion_type 投放内容
     */
    public $promotion_type = '';

    //-------------------------------------- 自定义创意  end  --------------------------------------

    /**
     * 钩子函数
     */
    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    /**
     * 格式化数据
     */
    private function format()
    {
        //直播
        if ($this->promotion_type == ToutiaoEnum::LANDING_TYPE_LIVE) {
            $this->image_mode = ToutiaoEnum::IMAGE_MODE_CREATIVE_IMAGE_MODE_AWEME_LIVE;
        }
    }

    /**
     * 校验数据
     */
    private function validate()
    {
    }

    /**
     * 设置标题素材
     * @param array $word_lists 创意标题
     */
    public function setTitleMaterial($word_lists = [])
    {
        foreach ($word_lists as $title) {
            $material = ['title'=>$title];
            if ($word_ids = (new MediaToutiao())->getWordIds($title)) {
                foreach ($word_ids as $word_id){
                    $material['word_list'][]['word_id'] = $word_id;
                }
            }
            $this->title_materials[] = $material;
        }
    }

    /**
     * 设置图片/视频素材信息
     * @param $creative_lists
     * @param $material_media_id_map
     * @param bool $is_union_splash
     */
    public function setImageOrVideoMaterials($creative_lists, $material_media_id_map,$is_union_splash = false)
    {
        $media_toutiao = (new MediaToutiao());
        foreach ($creative_lists as $material) {
            if (!isset($material['video_info'])) {
                $image = [];
                $image['image_info'][]['image_id'] = isset($material['image_info']['id']) ? $material_media_id_map[$material['image_info']['id']]['id'] : $material_media_id_map[$material['image_info']['image_id']];
                $image['image_mode'] = $media_toutiao->getMaterialFileType(1, $material['image_info']['width'], $material['image_info']['height'], $is_union_splash);
                $this->image_materials[] = $image;
            } else {
                $video = [];
                $video['image_info']['image_id'] = isset($material['cover_info']['id']) ? $material_media_id_map[$material['cover_info']['id']]['id'] : $material_media_id_map[$material['cover_info']['image_id']];
                $video['video_info']['video_id'] = isset($material['video_info']['id']) ? $material_media_id_map[$material['video_info']['id']]['id'] : $material_media_id_map[$material['video_info']['video_id']];
                $video['image_mode'] = $media_toutiao->getMaterialFileType(2, $material['video_info']['width'], $material['video_info']['height']);
                $this->video_materials[] = $video;
            }
        }

    }

    /**
     * 设置组件id
     * @param int $component_id
     */
    public function setComponentMaterials($component_id = 0)
    {
        $component_id && $this->component_materials[] = ['component_id' => $component_id];
    }
}