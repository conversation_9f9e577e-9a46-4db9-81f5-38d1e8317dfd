<?php

namespace App\Param\Toutiao\Creative;

use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Creative\CreativeModel;
use App\Param\AbstractParam;
use App\Service\MediaAD\MediaToutiao;

/**
 * 创建自定义创意-自定义创意（营销链路）
 * Class CustomCreativeParam
 * @package App\Param\Toutiao
 * User: zsp
 * Date: 2021/11/17 0017
 * Time: 16:01
 */
class CreativeListsParam extends AbstractParam
{
    //-------------------------------------- 自定义创意 start --------------------------------------
    /**
     * @var array $title_material 标题素材
     */
    public $title_material = [];

    /**
     * @var array $image_materials 创意图片素材
     */
    public $image_materials = [];

    /**
     * @var array $video_material 视频素材信息
     */
    public $video_material = [];

    /**
     * @var array $sub_title_material 副标题素材
     */
    public $sub_title_material = [];

    /**
     * @var array $playable_material 基础试玩素材
     */
    public $playable_material = [];

    /**
     * @var array $component_materials 组件信息
     */
    public $component_materials = [];

    /**
     * @var array $abstract_materials 摘要素材
     */
    public $abstract_materials = [];

    /**
     * @var int $derive_poster_cid 是否将视频的封面和标题同步到图片创意
     */
    public $derive_poster_cid = 0;

    /**
     * @var string $third_party_id 创意自定义参数
     */
    public $third_party_id = '';

    /**
     * @var string $image_mode 素材类型
     */
    public $image_mode = '';

    /**
     * @var string $promotion_type 投放内容
     */
    public $promotion_type = '';

    //-------------------------------------- 自定义创意  end  --------------------------------------

    /**
     * 钩子函数
     */
    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    /**
     * 格式化数据
     */
    private function format()
    {
        //直播
        if ($this->promotion_type == ToutiaoEnum::LANDING_TYPE_LIVE) {
            $this->image_mode = ToutiaoEnum::IMAGE_MODE_CREATIVE_IMAGE_MODE_AWEME_LIVE;
        }
    }

    /**
     * 校验数据
     */
    private function validate()
    {
    }

    /**
     * 设置试玩素材URL
     * @param string $playable_url 试玩素材URL
     */
    public function setPlayableUrl($playable_url = '')
    {
        if ($playable_url) {
            $this->playable_material['playable_info']['playable_url'] = $playable_url;
        }
    }

    /**
     * 设置标题素材
     * @param string $title 创意标题
     */
    public function setTitleMaterial($title = '')
    {
        $this->title_material['title'] = $title;
        if ($word_ids = (new MediaToutiao())->getWordIds($title)) {
            foreach ($word_ids as $word_id) {
                $this->title_material['word_list'][]['word_id'] = $word_id;
            }
        }
    }

    /**
     * 设置图片/视频素材信息
     * @param $material
     * @param $material_media_id_map
     * @param bool $is_union_splash
     */
    public function setImageOrVideoMaterials($material, $material_media_id_map,$is_union_splash = false)
    {
        $media_toutiao = (new MediaToutiao());
        if (!isset($material['video_info'])) {
            $this->image_materials[]['image_info']['image_id'] = isset($material['image_info']['id']) ? $material_media_id_map[$material['image_info']['id']]['id'] : $material_media_id_map[$material['image_info']['image_id']];
            $this->image_mode = $media_toutiao->getMaterialFileType(1, $material['image_info']['width'], $material['image_info']['height'], $is_union_splash);
        } else {
            $this->video_material['image_info']['image_id'] = isset($material['cover_info']['id']) ? $material_media_id_map[$material['cover_info']['id']]['id'] : $material_media_id_map[$material['cover_info']['image_id']];
            $this->video_material['video_info']['video_id'] = isset($material['video_info']['id']) ? $material_media_id_map[$material['video_info']['id']]['id'] : $material_media_id_map[$material['video_info']['video_id']];
            $this->image_mode = $media_toutiao->getMaterialFileType(2, $material['video_info']['width'], $material['video_info']['height']);
        }
    }

    /**
     * 设置抖音号下视频素材信息
     * @param $aweme_video_id
     * @param $aweme_cover_id
     * @param $image_mode
     */
    public function setAwemeMaterials($aweme_video_id, $aweme_cover_id, $image_mode)
    {
        $this->video_material['image_info']['image_id'] = $aweme_cover_id;
        $this->video_material['aweme_item_id'] = $aweme_video_id;
        $this->image_mode = $image_mode;
    }

    /**
     * 设置组件id
     * @param int $component_id
     */
    public function setComponentMaterials($component_id = 0)
    {
        $component_id && $this->component_materials[] = ['component_id' => $component_id];
    }
}