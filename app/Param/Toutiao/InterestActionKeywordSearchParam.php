<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/02/24
 * Time: 14:08
 */

namespace App\Param\Toutiao;

use App\Exception\AppException;
use App\Param\AbstractParam;

class InterestActionKeywordSearchParam extends AbstractParam
{
    /**
     * @var string $access_token 必定要的接口头数据
     */
    public $access_token;

    /**
     * @var string $advertiser_id 广告主ID-必填
     */
    public $advertiser_id;

    /**
     * @var string $query_words 	关键词-必填
     */
    public $query_words;

    /**
     * @var string $action_scene 行为场景 允许值: "E-COMMERCE","NEWS","APP" -必填
     */
    public $action_scene;

    /**
     * @var string $action_days 行为天数-必填
     */
    public $action_days;

    public function paramHook()
    {
        if (!$this->access_token) {
            throw new AppException('access_token传参有误');
        }

        if (!$this->advertiser_id) {
            throw new AppException('advertiser_id传参有误');
        }

        if (!$this->query_words) {
            throw new AppException('query_words传参有误');
        }

        if (!$this->action_scene) {
            throw new AppException('action_scene传参有误');
        }

        if (!$this->action_days) {
            throw new AppException('action_days传参有误');
        }

    }

    /**
     * InterestActionKeywordSearchParam类生成请求体
     * @return array
     */
    public function toRequestBody()
    {
        return [
            'advertiser_id' => $this->advertiser_id,
            'query_words' => $this->query_words,
            'action_scene' => json_encode($this->action_scene),
            'action_days' => $this->action_days,
        ];
    }
}