<?php

namespace App\Param\Toutiao;


use App\Exception\AppException;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\AbstractParam;
use App\Param\Material\MaterialFileParam;

/**
 * Class FileVideoUploadParam
 * @package App\Param\TouTiao
 */
class FileAudioUploadParam extends AbstractParam
{
    /**
     * @var string $access_token 全部接口都要传access_token-必填放在header
     */
    public $access_token;

    /**
     * @var string $advertiser_id 广告主ID-必填
     */
    public $advertiser_id;

    /**
     * @var string $upload_type 音频上传方式
     */
    public $upload_type;

    /**
     * @var string $audio_signature audio的md5值(用于服务端校验)-必填
     */
    public $audio_signature;

    /**
     * @var string $video_file 视频文件,格式mp4、mpeg、3gp、avi（10s超时限制）
     */
    public $audio_file;

    /**
     * @var string $filename 视频名称
     */
    public $filename;

    /**
     * FileVideoUploadParam constructor.
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     */
    public function __construct(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        $prop = $param->toArray();
        $prop['audio_signature'] = $param->signature;
        $prop['audio_file'] = MaterialFileModel::AUDIO_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
        $prop['access_token'] = $access_token;
        $prop['upload_type'] = 'UPLOAD_BY_FILE';
        $prop['advertiser_id'] = $advertiser_id;
        parent::__construct($prop);
    }

    protected function paramHook()
    {
        //获取文件后缀名
        $file_name_array = explode('.', $this->filename);
        $video_format = strtolower(array_pop($file_name_array));
        if (!in_array($video_format, ['mp3'])) {
            throw new AppException('非法视频格式');
        }
    }

    /**
     * 把FileVideoUploadParam类转化成请求的body
     * @return array
     */
    public function toRequestBody()
    {
        return [
            "advertiser_id" => $this->advertiser_id,
            "audio_signature" => $this->audio_signature,
            "upload_type" => $this->upload_type,
            "audio_file" => new \CURLFile($this->audio_file)
        ];
    }
}