<?php

namespace App\Param\Toutiao;


use App\Exception\AppException;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\AbstractParam;
use App\Param\Material\MaterialFileParam;
use CURLFile;

/**
 * Class FileVideoUploadParam
 * @package App\Param\TouTiao
 */
class FileVideoUploadParam extends AbstractParam
{
    /**
     * @var string $access_token 全部接口都要传access_token-必填放在header
     */
    public $access_token;

    /**
     * @var string $advertiser_id 广告主ID-必填
     */
    public $advertiser_id;

    /**
     * @var string $video_signature 视频的md5值(用于服务端校验)-必填
     */
    public $video_signature;

    /**
     * @var string $video_file 视频文件,格式mp4、mpeg、3gp、avi（10s超时限制）
     */
    public $video_file;

    /**
     * @var string $filename 视频名称
     */
    public $filename;

    public $video_url;

    public $is_guide_video = false;

    /**
     * FileVideoUploadParam constructor.
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     */
    public function __construct(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        $prop = $param->toArray();
        $prop['video_signature'] = $param->signature;
        $prop['video_file'] = MaterialFileModel::VIDEO_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
        $prop['access_token'] = $access_token;
        $prop['advertiser_id'] = $advertiser_id;
        $prop['video_url'] = $param->url;
        parent::__construct($prop);
    }

    protected function paramHook()
    {
        //获取文件后缀名
        $file_name_array = explode('.', $this->filename);
        $video_format = strtolower(array_pop($file_name_array));
        if (!in_array($video_format, ['mp4', 'mpeg', '3gp', 'avi'])) {
            throw new AppException('非法视频格式');
        }
    }

    /**
     * 把FileVideoUploadParam类转化成请求的body
     * @return array
     */
    public function toRequestBody()
    {
        if (strpos($this->video_url, 'dms.zx.com/upload/') === false && strpos($this->video_url, 'dms.zeda.cn/upload/') === false) {
            $data = [
                "advertiser_id" => $this->advertiser_id,
                "upload_type" => 'UPLOAD_BY_URL',
                "filename" => $this->filename,
                "video_url" => $this->video_url,
            ];
        } else {
            $data = [
                "advertiser_id" => $this->advertiser_id,
                "video_signature" => $this->video_signature,
                "video_file" => new CURLFile($this->video_file),
            ];
        }

        if ($this->is_guide_video) {
            $data["is_guide_video"] = 'true';
        }

        return $data;
    }
}
