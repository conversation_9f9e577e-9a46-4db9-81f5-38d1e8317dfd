<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/6/9
 * Time: 11:57
 */

namespace App\Param\Toutiao\V2;

use App\Param\AbstractParam;

class DeliverySettingParam extends AbstractParam
{
    public $schedule_type;
    public $start_time;
    public $end_time;
    public $schedule_time;
    public $deep_bid_type;
    public $bid_type;
    public $budget_mode;
    public $budget;
    public $roi_goal;
    public $cpa_bid;
    public $first_roi_goal;
    public $budget_optimize_switch;

    public function paramHook()
    {
        if (is_array($this->schedule_time) && $this->schedule_time) {
            $this->schedule_time = implode("", ($this->schedule_time));
        } else {
            $this->schedule_time = '';
        }

        if (is_numeric($this->end_time)) {
            $this->end_time = date('Y-m-d', $this->end_time);
        }
        if (is_numeric($this->start_time)) {
            $this->start_time = date('Y-m-d', $this->start_time);
        }
    }

    public function toRequestBody()
    {
        if ($this->deep_bid_type === 'DEEP_BID_DEFAULT') {
            $this->deep_bid_type = '';
        }
        if ($this->deep_bid_type === 'FIRST_AND_SEVEN_PAY_ROI') {
            $this->cpa_bid = '';
        }
        if ($this->bid_type == 'NO_BID') {
            $this->cpa_bid = '';
            $this->roi_goal = '';
        }
        if ($this->budget_mode == 'BUDGET_MODE_INFINITE') {
            $this->budget = '';
        }

        return $this->toParam();
    }
}
