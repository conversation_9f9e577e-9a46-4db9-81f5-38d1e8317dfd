<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/6/9
 * Time: 14:35
 */

namespace App\Param\Toutiao\V2;

use App\Param\AbstractParam;

class PromotionCreateParam extends AbstractParam
{
    /**
     * @var string $advertiser_id 广告主ID
     */
    public $advertiser_id;

    public $guide_video_id;

    public $materials_type;

    /**
     * @var string $project_id 项目ID
     */
    public $project_id;

    /**
     * @var string $name 广告名称
     */
    public $name;

    public $operation;

    /**
     * @var string $name 广告来源
     */
    public $source;

    /**
     * @var string $promotion_materials 广告素材组合
     */
    public $promotion_materials;

    /**
     * @var string $budget 预算
     */
    public $budget;

    /**
     * @var string $cpa_bid 目标转化出价/预期成本
     */
    public $cpa_bid;

    /**
     * @var string $deep_cpabid 深度优化出价
     */
    public $deep_cpabid;

    /**
     * @var string $roi_goal 深度转化ROI系数
     */
    public $roi_goal;

    /**
     * @var string $roi_goal 原生广告设置
     */
    public $native_setting;

    public function toRequestBody()
    {
        $data = $this->toParam();

        $data['promotion_materials'] = $data['promotion_materials'] ?? (object)[];
        return $data;
    }
}
