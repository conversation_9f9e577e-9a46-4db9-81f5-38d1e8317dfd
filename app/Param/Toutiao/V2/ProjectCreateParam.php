<?php
/**
 * 创建项目
 * User: Lin
 * Date: 2022/6/9
 * Time: 11:53
 */

namespace App\Param\Toutiao\V2;

use App\Param\AbstractParam;
use App\Param\Toutiao\AudiencePackageCreateParam;

class ProjectCreateParam extends AbstractParam
{
    /**
     * @var string $advertiser_id 广告主ID
     */
    public $advertiser_id;

    /**
     * @var string $landing_type 推广目的
     */
    public $landing_type;

    /**
     * @var string $app_promotion_type 子目标
     */
    public $app_promotion_type;

    /**
     * @var string $micro_promotion_type 小游戏类型
     */
    public $micro_promotion_type;

    /**
     * @var string $micro_app_instance_id 小游戏
     */
    public $micro_app_instance_id;

    /**
     * @var string $marketing_goal 营销场景
     */
    public $marketing_goal;

    /**
     * @var string $ad_type 广告类型
     */
    public $ad_type;

    /**
     * @var string $name 项目名称
     */
    public $name;

    /**
     * @var string $download_url 下载链接
     */
    public $download_url;

    public $app_name;

    /**
     * @var string $download_type 下载方式
     */
    public $download_type;

    /**
     * @var string $download_mode 下载模式
     */
    public $download_mode;

    /**
     * @var string $launch_type 调起方式
     */
    public $launch_type;

    /**
     * @var string $open_url Deeplink直达链接
     */
    public $open_url;

    /**
     * @var string $ulink_url ulink直达链接
     */
    public $ulink_url;

    /**
     * @var string $subscribe_url 预约下载链接
     */
    public $subscribe_url;

    /**
     * @var string $optimize_goal 优化目标
     */
    public $optimize_goal;

    /**
     * @var string $delivery_mode 投放模式
     */
    public $delivery_mode;

    /**
     * @var string $delivery_range 广告版位
     */
    public $delivery_range;

    /**
     * @var array $audience 定向内容
     */
    public $audience;

    /**
     * @var array $delivery_setting 预算出价
     */
    public $delivery_setting;

    /**
     * @var array $track_url_setting 监测链接
     */
    public $track_url_setting;

    public $asset_type;

    /**
     * @var string $operation 开关
     */
    public $operation;

    public $keywords = [];

    public $search_bid_ratio = '';

    public $audience_extend = '';

    /**
     * @var string 联投任务ID
     */
    public $star_task_id = '';


    public function setAudience(array $targeting_data)
    {
        $this->audience = (new AudiencePackageCreateParam($targeting_data))->toAudiencePackageInfoBody();
        if (isset($this->audience['auto_extend_enabled'])) {
            if ($this->audience['auto_extend_enabled'] == 1) {
                $this->audience['auto_extend_enabled'] = 'ON';
            } else {
                $this->audience['auto_extend_enabled'] = 'OFF';
            }
        }
        if (
            $this->audience['launch_price'] &&
            is_array($this->audience['launch_price']) &&
            count($this->audience['launch_price']) >= 2 &&
            $this->audience['launch_price'][0] == 0 && $this->audience['launch_price'][1] == 0
        ) {
            unset($this->audience['launch_price']);
        }
        // 小游戏只支持传入 UNLIMITED 不限（默认值）
        if ($this->landing_type == 'MICRO_GAME') {
            $this->audience['hide_if_exists'] = 'UNLIMITED';
        }
        if ($this->audience['district'] == 'CITY' || $this->audience['district'] == 'COUNTY') {
            $this->audience['district'] = 'REGION';
        }
        $this->audience['region_version'] = '1.0.0';
    }

    public function setDelivery(array $setting_data)
    {
        $this->delivery_setting = (new DeliverySettingParam($setting_data))->toRequestBody();
    }

    public function setTrackUrl(array $track_url_setting)
    {
        $this->track_url_setting = (new TrackUrlSetting($track_url_setting))->toRequestBody();
    }

    public function toRequestBody()
    {
        return $this->toParam();
    }


}
