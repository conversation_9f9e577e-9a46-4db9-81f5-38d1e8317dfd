<?php

namespace App\Param\Toutiao;

use App\Constant\ConvertSourceType;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Param\AbstractParam;

/**
 * 营销链路的广告计划PA
 * Class AdCreateMarketParam
 * @package App\Param\Toutiao
 * User: zsp
 * Date: 2021/11/5 0005
 * Time: 16:53
 */
class AdCreateMarketParam extends AbstractParam
{

    /**
     * @var string $advertiser_id 广告主ID
     */
    public $advertiser_id = '';

    /**
     * @var string $campaign_id 广告组ID
     */
    public $campaign_id = '';

    /**
     * @var string $name 广告计划名称
     */
    public $name = '';

    /**
     * @var string $ad_name_text 广告计划名字
     */
    public $ad_name_text = '';

    /**
     * @var string $operation 计划状态
     */
    public $operation = ToutiaoEnum::OPERATION_DISABLE;

    /**
     * @var string $ad_operation 计划状态
     */
    public $ad_operation = ToutiaoEnum::OPERATION_DISABLE;

    /**
     * @var string $download_type 下载方式
     */
    public $download_type = ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL;

    /**
     * @var string $download_url 下载链接
     */
    public $download_url = '';

    /**
     * @var string $package 应用包名
     */
    public $package = '';

    /**
     * @var string $app_type 下载的应用类型
     */
    public $app_type = '';

    /**
     * @var string $download_mode 下载模式
     */
    public $download_mode = ToutiaoEnum::DOWNLOAD_MODE_DEFAULT;

    /**
     * @var string $open_url 直达链接
     */
    public $open_url = '';

    /**
     * @var string $ulink 直达备用链接
     */
    public $ulink = '';

    /**
     * @var string $aweme_account 抖音号
     */
    public $aweme_account = '';

    /**
     * @var array $ies_map 抖音号数组
     */
    public $ies_map = [];

    /**
     * @var string $account_id 账号id
     */
    public $account_id = '';

    /**
     * @var string $delivery_range 投放范围
     */
    public $delivery_range = ToutiaoEnum::DELIVERY_RANGE_DEFAULT;

    /**
     * @var string $inventory_catalog 广告位大类
     */
    public $inventory_catalog = ToutiaoEnum::INVENTORY_CATALOG_MANUAL;

    /**
     * @var array $inventory_type 广告投放位置（首选媒体）
     */
    public $inventory_type = [];

    /**
     * @var string $smart_inventory 优选广告位
     */
    public $smart_inventory = ToutiaoEnum::SMART_INVENTORY_NORMAL;

    /**
     * @var string $union_video_type 投放形式（穿山甲视频创意类型）
     */
    public $union_video_type = ToutiaoEnum::UNION_VIDEO_TYPE_ORIGINAL_VIDEO;

    /**
     * @var int $convert_id 自定义转化目标
     */
    public $convert_id = 0;

    /**
     * @var string $external_action 预定义转化目标
     */
    public $external_action = '';

    /**
     * @var string $deep_external_action 预定义深度转化目标
     */
    public $deep_external_action = '';

    /**
     * @var array $external_actions 转化类型列表
     */
    public $external_actions = [];

    /**
     * @var string $convert_source_type 转化类型
     */
    public $convert_source_type = '';

    /**
     * @var array $asset_ids 资产 id
     */
    public $asset_ids = [];

    /**
     * @var string $track_url_group_type
     */
    public $track_url_group_type = '';

    /**
     * @var int $value_optimized_type 目标优化类型
     */
    public $value_optimized_type = 0;

    /**
     * @var int $value_optimized_open 价值优选
     */
    public $value_optimized_open = 0;

    /**
     * @var int $product_platform_id 商品目录ID
     */
    public $product_platform_id = 0;

    /**
     * @var string $product_id 商品ID
     */
    public $product_id = '';

    /**
     * @var int $asset_id 物件ID
     */
    public $asset_id = 0;

    /**
     * @var string $feed_delivery_search 搜索快投关键词功能
     */
    public $feed_delivery_search = ToutiaoEnum::FEED_DELIVERY_SEARCH_HAS_OPEN;

    /**
     * @var string $search_keyword_type
     */
    public $search_keyword_type = '';

    public $search_keyword_status = '';

    public $search_bid_ratio = 1;

    public $audience_extend = ToutiaoEnum::FEED_DELIVERY_SEARCH_AUDIENCE_EXTEND_ON;

    //-------------------------------------- 定向 start --------------------------------------

    /**
     * @var string $district 地域类型
     */
    public $district = ToutiaoEnum::DISTRICT_NONE;

    /**
     * @var string $region_version 行政区域版本号
     */
    public $region_version = '';

    /**
     * @var array $city 地域定向省市或者区县列表
     */
    public $city = [];

    /**
     * @var array $business_ids 商圈ID数组
     */
    public $business_ids = [];

    /**
     * @var string $location_type 位置类型
     */
    public $location_type = ToutiaoEnum::LOCATION_TYPE_ALL;

    /**
     * @var string $gender 性别
     */
    public $gender = ToutiaoEnum::GENDER_NONE;

    /**
     * @var array $age 年龄
     */
    public $age = [];

    /**
     * @var string $age_none 年龄选项 none/zidingyui
     */
    public $age_none = '';

    /**
     * @var array $career 职业选项
     */
    public $career = [];

    /**
     * @var string $zidingyirenqun 人群包
     */
    public $zidingyirenqun = ToutiaoEnum::PEOPLE_PACKAGE_NONE;

    /**
     * @var array $retargeting_tags_include 定向人群包列表（自定义人群）
     */
    public $retargeting_tags_include = [];

    /**
     * @var array $retargeting_tags_exclude 排除人群包列表（自定义人群）
     */
    public $retargeting_tags_exclude = [];

    /**
     * @var string $interest_action_mode 行为兴趣
     */
    public $interest_action_mode = ToutiaoEnum::INTEREST_ACTION_MODE_UNLIMITED;

    /**
     * @var array $action_scene 行为场景
     */
    public $action_scene = [];

    /**
     * @var int $action_days 用户发生行为天数
     */
    public $action_days = ToutiaoEnum::ACTION_DAYS_7;

    /**
     * @var array $action_categories 行为类目词
     */
    public $action_categories = [];

    /**
     * @var array $action_words 行为关键词
     */
    public $action_words = [];

    /**
     * @var array $interest_categories 兴趣类目词
     */
    public $interest_categories = [];

    /**
     * @var array $interest_words 兴趣关键词
     */
    public $interest_words = [];

    /**
     * @var array $aweme_fan_behaviors 抖音达人互动用户行为类型
     */
    public $aweme_fan_behaviors = [];

    /**
     * @var string $aweme_fan_time_scope 抖音达人互动行为时间范围
     */
    public $aweme_fan_time_scope = ToutiaoEnum::AWEME_FAN_TIME_SCOPE_15;

    /**
     * @var array $aweme_fan_categories 抖音达人分类ID列表
     */
    public $aweme_fan_categories = [];

    /**
     * @var array $aweme_fan_accounts 抖音达人ID列表
     */
    public $aweme_fan_accounts = [];

    /**
     * @var string $filter_aweme_abnormal_active （抖音号、直播间推广特有）过滤高活跃用户
     */
    public $filter_aweme_abnormal_active = ToutiaoEnum::FILTER_AWEME_ABNORMAL_ACTIVE_FALSE;

    /**
     * @var int $filter_aweme_fans_count （抖音号、直播间推广特有）过滤高关注数用户
     */
    public $filter_aweme_fans_count = 0;

    /**
     * @var string $filter_own_aweme_fans （抖音号、直播间推广特有）过滤自己的粉丝
     */
    public $filter_own_aweme_fans = ToutiaoEnum::FILTER_OWN_AWEME_FANS_FALSE;

    /**
     * @var string $superior_popularity_type 媒体定向
     */
    public $superior_popularity_type = '';

    /**
     * @var array $flow_package 定向逻辑
     */
    public $flow_package = [];

    /**
     * @var array $exclude_flow_package 排除定向逻辑
     */
    public $exclude_flow_package = [];

    /**
     * @var array $platform 平台
     */
    public $platform = [];

    /**
     * @var string $game_type 游戏类型 安卓/IOS
     */
    public $game_type = '';

    /**
     * @var string $game_id 游戏id
     */
    public $game_id = '';

    /**
     * @var string $android_osv 最低安卓版本
     */
    public $android_osv = '';

    /**
     * @var string $ios_osv 最低IOS版本
     */
    public $ios_osv = '';

    /**
     * @var array $device_type 设备类型
     */
    public $device_type = [];

    /**
     * @var array $ac 网络类型
     */
    public $ac = [];

    /**
     * @var array $carrier 运营商
     */
    public $carrier = [];

    /**
     * @var string $carrier_none 网络类型
     */
    public $carrier_none = ToutiaoEnum::CARRIER_NONE;

    /**
     * @var string $hide_if_exists 过滤已安装，当推广目标为安卓应用下载时可填，0表示不限，1表示过滤，2表示定向。默认为不限
     */
    public $hide_if_exists = ToutiaoEnum::HIDE_IF_EXISTS_NONE;

    /**
     * @var string $converted_time_duration 过滤时间范围
     */
    public $converted_time_duration = '';

    /**
     * @var string $hide_if_converted 过滤已转化用户
     */
    public $hide_if_converted = ToutiaoEnum::HIDE_IF_CONVERTED_NO_EXCLUDE;

    /**
     * @var array $activate_type 新用户(新用户使用头条的时间)
     */
    public $activate_type = [];

    /**
     * @var string $activate_type_none 新用户(新用户使用头条的时间)
     */
    public $activate_type_none = ToutiaoEnum::ACTIVATE_TYPE_NONE;

    /**
     * @var array $article_category 文章分类
     */
    public $article_category = [];

    /**
     * @var array $device_brand 手机品牌
     */
    public $device_brand = [];

    /**
     * @var array $launch_price 手机价格
     */
    public $launch_price = [];

    /**
     * @var string $auto_extend_enabled 是否启用智能放量
     */
    public $auto_extend_enabled = ToutiaoEnum::AUTO_EXTEND_ENABLED_FALSE;

    /**
     * @var array $auto_extend_targets 可放开定向
     */
    public $auto_extend_targets = [];
    //-------------------------------------- 定向 start --------------------------------------


    //-------------------------------------- 预算与出价 start --------------------------------------
    /**
     * @var string $smart_bid_type 投放场景(出价方式)
     */
    public $smart_bid_type = ToutiaoEnum::SMART_BID_TYPE_CUSTOM;

    /**
     * @var int $adjust_cpa 是否调整自动出价
     */
    public $adjust_cpa = ToutiaoEnum::ADJUST_CPA_FALSE;

    /**
     * @var string $flow_control_mode 竞价策略(投放方式)
     */
    public $flow_control_mode = ToutiaoEnum::FLOW_CONTROL_MODE_FAST;

    /**
     * @var string $budget_mode 预算类型
     */
    public $budget_mode = ToutiaoEnum::BUDGET_MODE_DAY;

    /**
     * @var float $budget 预算
     */
    public $budget = 0;

    /**
     * @var string $schedule_type 投放时间类型
     */
    public $schedule_type = '';

    /**
     * @var string $start_time 投放起始时间
     */
    public $start_time = '';

    /**
     * @var string $end_time 投放结束时间
     */
    public $end_time = '';

    /**
     * @var string $schedule_time 投放时段
     */
    public $schedule_time = '';

    /**
     * @var string $pricing 出价方式
     */
    public $pricing = ToutiaoEnum::PRICING_CPC_OCPM;

    /**
     * @var int $bid 点击出价/展示出价
     */
    public $bid = 0;

    /**
     * @var int $cpa_bid 目标转化出价/预期成本
     */
    public $cpa_bid = 0;

    /**
     * @var string $deep_bid_type 深度优化方式
     */
    public $deep_bid_type = '';

    /**
     * @var int $deep_cpabid 深度优化出价
     */
    public $deep_cpabid = 0;

    /**
     * @var int $luban_roi_goal 鲁班目标ROI出价策略系数
     */
    public $luban_roi_goal = 0;

    /**
     * @var int $roi_goal 深度转化ROI系数
     */
    public $roi_goal = 0;
    //-------------------------------------- 预算与出价  end  --------------------------------------


    //-------------------------------------- 检测链接 start --------------------------------------
    /**
     * @var array $track_url 展示（监测链接）
     */
    public $track_url = [];

    /**
     * @var array $action_track_url 点击（监测链接）
     */
    public $action_track_url = [];

    /**
     * @var array $video_play_effective_track_url 视频有效播放（监测链接）
     */
    public $video_play_effective_track_url = [];

    /**
     * @var array $video_play_done_track_url 视频播完（监测链接）
     */
    public $video_play_done_track_url = [];

    /**
     * @var array $video_play_track_url 视频播放（监测链接）
     */
    public $video_play_track_url = [];

    /**
     * @var string $track_url_send_type 数据发送方式
     */
    public $track_url_send_type = ToutiaoEnum::TRACK_URL_SEND_TYPE_SERVER;

    /**
     * @var string $landing_type 投放类型
     */
    public $landing_type = ToutiaoEnum::LANDING_TYPE_APP;

    /**
     * @var string $promotion_type 投放类型 APP LIVE GAME,
     */
    public $promotion_type = ToutiaoEnum::LANDING_TYPE_APP;

    /**
     * @var string[] $os
     */
    public $os = ['ANDROID', 'IOS'];

    /**
     * @var string $inventory_mode 投放场景
     */
    public $inventory_mode = '';

    /**
     * @var  $scene_inventory string  投放场景值
     */
    public $scene_inventory;

    public $auto_inherit_switch = 'OFF';

    public $inherit_type = ToutiaoEnum::AUTO_INHERIT_SWITCH_ACCOUNT;

    public $inherited_advertiser_id = [];

    public $aweme_fan_behaviors_days = 15;

    //-------------------------------------- 检测链接  end  --------------------------------------

    /**
     * AdCreateMarketParam constructor.
     * @param array $property
     */
    public function __construct($property = [])
    {
        if ($property['schedule_time'] && is_array($property['schedule_time'])) {
            $property['schedule_time'] = implode("", $property['schedule_time']);
        }

        if ($property['action_track_url'] && !is_array($property['action_track_url'])) {
            $property['action_track_url'] = [$property['action_track_url']];
        }

        if ($property['display_track_url'] && !is_array($property['display_track_url'])) {
            $property['track_url'] = [$property['display_track_url']];
        }

        if ($property['external_actions'] && is_string($property['external_actions'])) {
            $property['external_action'] = $property['external_actions'];
            $property['external_actions'] = [$property['external_actions']];
        }
        if ($property['device_type'] && is_string($property['device_type'])) {
            $property['device_type'] = [$property['device_type']];
        }
        parent::__construct($property);
    }

    /**
     * 钩子函数
     */
    public function paramHook()
    {
        $this->format();
        $this->validate();
    }

    /**
     * 格式化数据
     */
    private function format()
    {
        //地区
        if ($this->district == ToutiaoEnum::DISTRICT_NONE) {
            $this->district = '';
            $this->location_type = '';
            $this->city = [];
        }

        //性别
        if ($this->age_none == ToutiaoEnum::AGE_NONE) {
            $this->age = [];
        }

        //行为兴趣
        if ($this->interest_action_mode != ToutiaoEnum::INTEREST_ACTION_MODE_CUSTOM) {
            $this->action_days = '';
            $this->action_scene = [];
            $this->action_words = [];
            $this->interest_words = [];
            $this->action_categories = [];
            $this->interest_categories = [];
        }

        //平台类型

        if ($this->promotion_type != ToutiaoEnum::PROMOTION_TYPE_LINK) {
            if ($this->game_type == ToutiaoEnum::GAME_TYPE_ANDROID) {
                $this->platform = [ToutiaoEnum::PLATFORM_ANDROID];
                $this->ios_osv = '';
            } elseif ($this->game_type == ToutiaoEnum::GAME_TYPE_IOS) {
                $this->platform = [ToutiaoEnum::PLATFORM_IOS];
                $this->android_osv = '';
            }
            if (in_array($this->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_HOT, ToutiaoEnum::PROMOTION_TYPE_GAME, ToutiaoEnum::PROMOTION_TYPE_LIVE]) &&
                $this->convert_source_type == ConvertSourceType::H5_API) {
                $this->platform = $this->os;
            }
        } else {
            $this->platform = $this->os;
            // 此处先临时
            if ($this->platform === true) {
                $this->platform = ['ANDROID', 'IOS'];
            }
            if ($this->platform) {
                if (!in_array(ToutiaoEnum::PLATFORM_ANDROID, $this->platform)) {
                    $this->android_osv = '';
                }
                if (!in_array(ToutiaoEnum::PLATFORM_IOS, $this->platform)) {
                    $this->ios_osv = '';
                }
            }
        }

        //运营商
        if ($this->carrier_none = ToutiaoEnum::CARRIER_NONE) {
            $this->carrier = [];
        }

        //用户激活时间
        if ($this->activate_type_none == ToutiaoEnum::ACTIVATE_TYPE_NONE) {
            $this->activate_type = [];
        }

        //手机价格
        if (($this->launch_price[0] ?? 0) == 0 && ($this->launch_price[1] ?? 0) == 0) {
            $this->launch_price = [];
        }

        //智能放量
        if (!$this->auto_extend_enabled) {
            $this->auto_extend_targets = [];
        } else {
            $this->auto_extend_targets = array_values(array_filter($this->auto_extend_targets, function ($e) {
                return $e != 'INTEREST_ACTION';
            }));
        }

        //过滤已经转化
        if (!in_array($this->hide_if_converted, [ToutiaoEnum::HIDE_IF_CONVERTED_APP, ToutiaoEnum::HIDE_IF_CONVERTED_CUSTOMER])) {
            $this->converted_time_duration = '';
        }

        //人群包
        if ($this->zidingyirenqun == ToutiaoEnum::PEOPLE_PACKAGE_NONE) {
            $this->retargeting_tags_exclude = [];
            $this->retargeting_tags_include = [];
        }

        //投放范围
        if ($this->delivery_range == ToutiaoEnum::DELIVERY_RANGE_UNION) {
            //穿山甲
            $this->inventory_catalog = ToutiaoEnum::INVENTORY_CATALOG_MANUAL;
            if ($this->union_video_type == ToutiaoEnum::UNION_VIDEO_TYPE_SPLASH_VIDEO) {
                //开屏
                $this->inventory_type = [ToutiaoEnum::UNION_INVENTORY_TYPE_UNION_SPLASH_SLOT];
            } else {
                //原生
                $this->inventory_type = [ToutiaoEnum::INVENTORY_TYPE_UNION];
            }
            // 穿山甲不能使用搜索快投，直接置空让媒体自己给默认
            $this->feed_delivery_search = '';
            $this->smart_inventory = ToutiaoEnum::SMART_INVENTORY_NORMAL;
        } elseif ($this->delivery_range == ToutiaoEnum::DELIVERY_RANGE_UNIVERSAL) {
            //通投智选
            $this->inventory_catalog = ToutiaoEnum::INVENTORY_CATALOG_UNIVERSAL;
            $this->smart_inventory = ToutiaoEnum::SMART_INVENTORY_UNIVERSAL;
            $this->union_video_type = '';

        } else {
            if ($this->inventory_mode != ToutiaoEnum::INVENTORY_MODE_SCENE) {
                $this->scene_inventory = '';
            } else {
                //投放场景
                $this->inventory_catalog = ToutiaoEnum::INVENTORY_MODE_SCENE;
            }

            $this->union_video_type = '';
            $this->smart_inventory = ToutiaoEnum::SMART_INVENTORY_NORMAL;

            //如果首选媒体只有穿山甲的话
            if (!empty(array_intersect($this->inventory_type, ToutiaoEnum::UNION_INVENTORY_TYPE_LIST)) && empty(array_diff($this->inventory_type, ToutiaoEnum::UNION_INVENTORY_TYPE_LIST))) {
                //投放范围重置为穿山甲
                $this->delivery_range = ToutiaoEnum::DELIVERY_RANGE_UNION;

                //首选媒体并且投放位置穿山甲
                $this->inventory_catalog = ToutiaoEnum::INVENTORY_CATALOG_MANUAL;

                //干掉穿山甲开屏
                foreach ($this->inventory_type as $key => $inventory_type) {
                    if ($inventory_type == ToutiaoEnum::UNION_INVENTORY_TYPE_UNION_SPLASH_SLOT) {
                        unset($this->inventory_type[$key]);
                    }
                }
                $this->feed_delivery_search = '';
            } else {
                if ($this->search_keyword_status != 1) {
                    $this->feed_delivery_search = ToutiaoEnum::FEED_DELIVERY_SEARCH_DISABLED;
                }
            }

        }

        if ($this->feed_delivery_search == ToutiaoEnum::FEED_DELIVERY_SEARCH_DISABLED || !$this->feed_delivery_search) {
            $this->search_bid_ratio = '';
            $this->audience_extend = '';
        }

        if ($this->smart_bid_type == ToutiaoEnum::SMART_BID_TYPE_NO_BID && $this->deep_external_action != 'AD_CONVERT_TYPE_PURCHASE_ROI') {
            $this->deep_external_action = '';
        }

        if ($this->deep_bid_type != 'DEEP_BID_DEFAULT' || $this->smart_bid_type == ToutiaoEnum::SMART_BID_TYPE_NO_BID) {
            $this->search_bid_ratio = '';
//            $this->audience_extend = '';
        }

        //投放时间类型
        if ($this->schedule_type == ToutiaoEnum::SCHEDULE_TYPE_SCHEDULE_FROM_NOW) {
            $this->start_time = '';
            $this->end_time = '';
        } else {
            $this->start_time = date('Y-m-d 00:00', $this->start_time);
            $this->end_time = date('Y-m-d 23:59', $this->end_time);
        }

        //是否调整自动出价
        if ($this->smart_bid_type == ToutiaoEnum::SMART_BID_TYPE_SMART_BID_CUSTOM) {
            // 常规投放
            $this->adjust_cpa = 0;
        }

        //roi
        if ($this->deep_bid_type != ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT) {
            $this->roi_goal = 0;
        }

        //自定义双出价
        if ($this->deep_bid_type != ToutiaoEnum::DEEP_BID_TYPE_DEEP_BID_MIN) {
            $this->deep_cpabid = 0;
        }

        //投放类型,LIVE和GAME都对应landing_type的直播LIVE,APP就还是APP
        switch ($this->promotion_type) {
            case ToutiaoEnum::PROMOTION_TYPE_APP:
                $this->landing_type = ToutiaoEnum::LANDING_TYPE_APP;
                break;
            case ToutiaoEnum::PROMOTION_TYPE_HOT:
                if ($this->convert_source_type == ConvertSourceType::H5_API) {
                    $this->landing_type = ToutiaoEnum::LANDING_TYPE_LINK;
                } else {
                    $this->landing_type = ToutiaoEnum::LANDING_TYPE_APP;
                }
                break;
            case ToutiaoEnum::PROMOTION_TYPE_GAME:
            case ToutiaoEnum::PROMOTION_TYPE_LIVE:
                $this->landing_type = ToutiaoEnum::LANDING_TYPE_LIVE;
                break;
            case ToutiaoEnum::PROMOTION_TYPE_LINK:
                $this->landing_type = ToutiaoEnum::LANDING_TYPE_LINK;
                break;
        }

        //投放类型
        if ($this->landing_type == ToutiaoEnum::LANDING_TYPE_LIVE) {
            //直播
            $this->download_type = '';
            $this->package = '';
            $this->download_mode = '';
            $this->open_url = '';
            $this->ulink = '';
            $this->aweme_account = $this->ies_map[$this->account_id];
            $this->ios_osv = [];
            $this->android_osv = [];

            //优化目标二选一
            if (in_array($this->convert_source_type, [ConvertSourceType::H5_API, ConvertSourceType::API])) {
                //api
                $this->convert_id = 0;
                if ($this->convert_source_type != ConvertSourceType::API) {
                    $this->app_type = '';
                    $this->download_url = '';
                    $this->download_type = '';
                    $this->package = '';
                    $this->download_mode = '';
                    $this->ulink = '';
                }
                if ($this->asset_ids) {
                    if ($this->convert_source_type == ConvertSourceType::API) {
                        $this->track_url_group_type = 'CUSTOM';
                    }
                } else {
                    if ($this->promotion_type != ToutiaoEnum::PROMOTION_TYPE_GAME) {
                        $this->app_type = '';
                        $this->download_url = '';
                    }
                }
                $this->external_actions = [];
            } else {
                if ($this->asset_ids) {
                    //sdk
                    $this->track_url_group_type = 'CUSTOM';
                } elseif ($this->convert_id) {
                    $this->app_type = '';
                    $this->download_url = '';
                    $this->external_action = '';
                    $this->deep_external_action = '';
                }
                $this->external_actions = [];
            }

        } elseif ($this->landing_type == ToutiaoEnum::LANDING_TYPE_APP) {
            //APP
            $this->aweme_account = '';

            if ($this->asset_ids) {
                $this->external_actions = [];
                $this->convert_id = 0;
                $this->track_url_group_type = 'CUSTOM';
            } else {
                $this->external_action = '';
                $this->external_actions = [];
            }
        } elseif ($this->landing_type == ToutiaoEnum::LANDING_TYPE_LINK) {
            //线索
//            $this->open_url = $this->download_url;
            $this->download_type = '';
            $this->download_url = '';
            $this->app_type = '';
            $this->package = '';
            $this->download_mode = '';
            $this->ulink = '';
            $this->aweme_account = '';
            if ($this->asset_ids) {
                $this->external_actions = [];
            }
        }

        if ($this->auto_inherit_switch == 'OFF') {
            $this->inherit_type = '';
            $this->inherited_advertiser_id = [];
        } else {
            if ($this->inherit_type == ToutiaoEnum::AUTO_INHERIT_SWITCH_CUSTOMER && $this->inherited_advertiser_id) {
                $this->inherited_advertiser_id = array_map(function ($ele) {
                    return $ele['account_id'];
                }, $this->inherited_advertiser_id);
            } else {
                $this->inherited_advertiser_id = [];
            }
        }

        if ($this->aweme_fan_behaviors_days) {
            switch ($this->aweme_fan_behaviors_days) {
                case 15:
                    $this->aweme_fan_time_scope = ToutiaoEnum::AWEME_FAN_TIME_SCOPE_15;
                    break;
                case 30:
                    $this->aweme_fan_time_scope = ToutiaoEnum::AWEME_FAN_TIME_SCOPE_30;
                    break;
                case 60:
                    $this->aweme_fan_time_scope = ToutiaoEnum::AWEME_FAN_TIME_SCOPE_60;
                    break;
            }
        }

        //广告计划名
        $this->name = $this->ad_name_text;

        //历史原因
        $this->package = $this->getPackage();

        //广告组开关
        $this->operation = $this->ad_operation;

        //去掉不必要的参数
        $this->landing_type = '';
        $this->ad_name_text = '';
        $this->activate_type_none = '';
        $this->carrier_none = '';
        $this->game_type = '';
        $this->os = '';
        $this->game_id = '';
        $this->zidingyirenqun = '';
        $this->age_none = '';
        $this->aweme_fan_behaviors_days = '';
        $this->superior_popularity_type = '';
        $this->ad_operation = '';
        $this->ies_map = [];
        $this->account_id = '';
        $this->convert_source_type = '';
    }

    /**
     * 数据验证
     */
    private function validate()
    {
        //promotion_type必须在APP LIVE GAME中
        if (!in_array($this->promotion_type, [
            ToutiaoEnum::PROMOTION_TYPE_APP,
            ToutiaoEnum::PROMOTION_TYPE_GAME,
            ToutiaoEnum::PROMOTION_TYPE_LIVE,
            ToutiaoEnum::PROMOTION_TYPE_LINK,
            ToutiaoEnum::PROMOTION_TYPE_HOT,
        ])) {
            throw new AppException('promotion_type非法');
        }
        $this->promotion_type = '';
    }

    /**
     * 历史原因
     * @return string
     */
    private function getPackage()
    {
        return $this->game_id == 1695 ? 'com.Dashu.Legend' : $this->package;
    }

    public function toRequestBody()
    {
        $data = $this->toFilterArray();
        if ($this->budget_mode == ToutiaoEnum::BUDGET_MODE_INFINITE) {
            $data['budget'] = 0;
        }

        return $data;
    }
}