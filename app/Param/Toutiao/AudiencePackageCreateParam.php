<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/02/24
 * Time: 14:08
 */

namespace App\Param\Toutiao;


use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\AudiencePackage\AudiencePackageModel;
use App\Param\AbstractParam;

class AudiencePackageCreateParam extends AbstractParam
{
    /**
     * @var string $access_token
     */
    public $access_token;

    /**
     * @var string $advertiser_id 广告主ID-必填
     */
    public $advertiser_id;

    /**
     * @var string $name 定向包名称-必填
     */
    public $name;

    /**
     * @var string $description 定向包描述-必填
     */
    public $description;

    /**
     * @var string $landing_type 定向包类型-必填
     */
    public $landing_type;

    /**
     * @var number[] $retargeting_tags 定向人群包列表
     */
    public $retargeting_tags;

    /**
     * @var number[] retargeting_tags_exclude 排除人群包列表
     */
    public $retargeting_tags_exclude;

    /**
     * @var number[] $retargeting_tags_include 定向人群包列表，内容为人群包id。
     */
    public $retargeting_tags_include;

    /**
     * @var string $gender 受众性别 可选择"GENDER_FEMALE", "GENDER_MALE", "NONE"
     */
    public $gender;

    /**
     * @var string $age 受众年龄区间
     */
    public $age;

    /**
     * @var string 受众最低android版本 (当推广应用下载Android时选填,其余情况不填)
     */
    public $android_osv;

    /**
     * @var string $ios_osv 受众最低ios版本 (当推广应用下载iOS时选填,其余情况不填)
     */
    public $ios_osv;

    /**
     * @var string[] 受众运营商
     */
    public $carrier;

    /**
     * @var string[] 受众网络类型;
     */
    public $ac;

    public $app_category;

    /**
     * @var string[] $device_brand 受众手机品牌,
     */
    public $device_brand;

    /**
     * @var string[] $article_category 受众文章分类
     */
    public $article_category;

    /**
     * @var string $activate_type 用户首次激活时间
     */
    public $activate_type;

    /**
     * @var string $district 地域类型，前者为省市，后者为区县,允许值:"CITY", "COUNTY" , "BUSINESS_DISTRICT","NONE"
     */
    public $district;

    /**
     * @var number[] $city 地域定向城市或者区县列表(当传递省份ID时,旗下市县ID可省略不传),当district为"CITY"时必填
     */
    public $city;

    /**
     * @var number[] $business_ids 商圈ID数组，district为"BUSINESS_DISTRICT"时必填
     */
    public $business_ids;

    /**
     * @var string $location_type 受众位置类型，详见【附录-受众位置类型】当city和district有值时必填
     */
    public $location_type = 'ALL';

    /**
     * @var number[] $ad_tag 兴趣分类,如果传空数组[] 表示不限，如果只传[0]表示系统推荐,如果按兴趣类型传表示自定义
     */
    public $ad_tag;

    /**
     * @var number[] $interest_tags 兴趣关键词, 传入具体的词id，非兴趣词包id，可以通过词包相关接口或者兴趣关键词word2id接口获取词id，一个计划下最多创建1000个关键词。
     */
    public $interest_tags;

    /**
     * @var string $superior_popularity_type 精选流量包，对于选择自定义流量包，传flow_package或exclude_flow_package字段即可 可选值'NONE','APP','GAME'
     */
    public $superior_popularity_type;

    /**
     * @var array $flow_package
     */
    public $flow_package;

    /**
     * @var array $exclude_flow_package
     */
    public $exclude_flow_package;

    /**
     * @var string $device_type 设备类型。允许值是："MOBILE", "PAD"。缺省表示不限设备类型。目前仅投放范围为穿山甲且账户有白名单权限可传值，否则会报错。
     */
    public $device_type;

    /**
     * @var int $auto_extend_enabled 是否启用智能放量，0关闭1开启
     */
    public $auto_extend_enabled;

    /**
     * @var string[] $auto_extend_targets 可放开定向。当auto_extend_enabled=1 时选填。详见：【附录-可开放定向】。缺省为全不选。
     */
    public $auto_extend_targets;

    /**
     * @var number[] $launch_price 手机价格定向,传入价格区间，最高传入11000（表示1w以上）
     */
    public $launch_price;

    public $interest_action_mode;

    public $action_scene;

    public $action_days;

    public $action_categories;

    public $action_words;

    public $interest_categories;

    public $interest_words;

    public $aweme_fan_behaviors;

    public $aweme_fan_behaviors_days;

    public $aweme_fan_categories;

    public $aweme_fan_accounts;

    /**
     * @var string $hide_if_converted 过滤已转化用户
     */
    public $hide_if_converted;

    public $filter_event;

    /**
     * @var string $converted_time_duration 过滤时间范围
     */
    public $converted_time_duration;

    /**
     * @var string $hide_if_exists 过滤已安装
     */
    public $hide_if_exists;

    public function paramHook()
    {
        //智能放量
        if (!$this->auto_extend_enabled) {
            $this->auto_extend_targets = [];
        } else {
            $this->auto_extend_targets = array_values(array_filter($this->auto_extend_targets, function ($e) {
                return $e != 'INTEREST_ACTION';
            }));
        }

        if ($this->district == AudiencePackageModel::DISTRICT_CITY && !$this->city) {
            throw new AppException('city传参有误');
        }

        if ($this->district == AudiencePackageModel::DISTRICT_BUSINESS_DISTRICT && !$this->business_ids) {
            throw new AppException('business_ids传参有误');
        }

        if ($this->district && $this->city && !$this->location_type) {
            $this->location_type = 'ALL';
        }

        if ($this->district == ToutiaoEnum::DISTRICT_NONE) {
            $this->location_type = '';
        }

        if (($this->launch_price[0] ?? 0) == 0 && ($this->launch_price[1] ?? 0) == 0) {
            $this->launch_price = [];
        }

//        if (!is_array($this->launch_price) && $this->launch_price) {
//            $this->launch_price = json_decode($this->launch_price, true);
//        }

//        if (count($this->launch_price) != 2 || $this->launch_price[1] > 11000) {
//            throw new AppException('launch_price传参有误');
//        }

//        if (!$this->city) {
//            $this->city = [];
//            $time = time();
//            if ($time >= 1602669600) {
//                $this->district = 'CITY';
//                $this->city = LimitADRegionTask::TOUTIAO_COUNTRY_EXCEPT_GUANGDONG;
//            }
//        } else {
//            $time = time();
//            if ($time >= 1602669600) {
//                $this->city = array_diff($this->city, [LimitADRegionTask::TOUTIAO_CHOSEN_GUANGDONG]);
//                $this->city = array_diff($this->city, LimitADRegionTask::TOUTIAO_GUANGDONG_CITY);
//                if (!$this->city) {
//                    $this->district = 'NONE';
//                }
//            }
//        }

        if (!$this->app_category) {
            $this->app_category = [];
        }

        if (!$this->action_scene && ($this->action_categories || $this->action_words)) {
            $this->action_scene = ['APP', 'NEWS', 'E-COMMERCE'];
        } else {
            $this->action_scene = array_filter($this->action_scene, function ($value) {
                return $value != 'SEARCH';
            });
        }

        if (!$this->action_categories) {
            $this->action_categories = [];
        }

        if (!$this->action_words) {
            $this->action_words = [];
        }

        if (!$this->interest_categories) {
            $this->interest_categories = [];
        }

        if (!$this->interest_words) {
            $this->interest_words = [];
        }
        if (!$this->retargeting_tags_exclude) {
            $this->retargeting_tags_exclude = [];
        }

        if (!$this->retargeting_tags_include) {
            $this->retargeting_tags_include = [];
        }

        if (!$this->device_brand) {
            $this->device_brand = [];
        }

        if ($this->device_type) {
            !is_array($this->device_type) && $this->device_type = [$this->device_type];
        } else {
            $this->device_type = [];
        }

        if ($this->superior_popularity_type == 'zidingyi') {
            $this->superior_popularity_type = '';
        }
    }

    /**
     * 生成一个请求信息体给创建广告计划
     */
    public function toAudiencePackageInfoBody()
    {
        $body = [];
        //组装所有定向包字段的数组
        $paramsArray = get_object_vars($this);
        foreach ($paramsArray as $key => $value) {
            if ($value) {
                $body[$key] = $value;
            }
        }
        if (isset($body['auto_extend_enabled'])) {
            $body['auto_extend_enabled'] = (int)($body['auto_extend_enabled']);
        }

        if (isset($body['aweme_fan_behaviors_days'])) {
            switch ($body['aweme_fan_behaviors_days']) {
                case 15:
                    $body['aweme_fan_time_scope'] = 'FIFTEEN_DAYS';
                    break;
                case 30:
                    $body['aweme_fan_time_scope'] = 'THIRTY_DAYS';
                    break;
                case 60:
                    $body['aweme_fan_time_scope'] = 'SIXTY_DAYS';
                    break;
            }
            unset($body['aweme_fan_behaviors_days']);
        }

        return $body;
    }

    public function toAudiencePackageAddBody()
    {

    }
}
