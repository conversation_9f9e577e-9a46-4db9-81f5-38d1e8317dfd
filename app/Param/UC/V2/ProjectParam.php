<?php

namespace App\Param\UC\V2;

use App\Param\AbstractParam;

class ProjectParam extends AbstractParam
{
    public $name = '';

    /**
     * @var TargetingParam $targeting
     */
    private $targeting;

    public $start_date;

    public $end_date;

    public $monday;
    public $tuesday;
    public $wednesday;
    public $thursday;
    public $friday;
    public $saturday;
    public $sunday;
    public $convert_monitor_group_id;
    public $paused;

    public function setTargeting($data)
    {
        $this->targeting = (new TargetingParam($data))->toRequestData();
    }

    public function setSchedule($schedule)
    {
        if (!$schedule) {
            $schedule = [];
            for ($i = 0; $i < 168; $i++) {
                $schedule[] = 1;
            }
        }

        $date_index = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $i = -1;
        foreach ($schedule as $key => $value) {
            if ($key % 24 === 0) {
                $i++;
            }
            $date_string = $this->{$date_index[$i]} ?? '';
            $this->{$date_index[$i]} = "{$value}{$date_string}";
            if ($key == 167) {
                break;
            }
        }
    }

    public function toRequestData()
    {
        return [
            'name' => $this->name,
            'targeting' => $this->targeting,
            'startDate' => (int)$this->start_date,
            'endDate' => (int)$this->end_date,
            'monday' => $this->monday,
            'tuesday' => $this->tuesday,
            'wednesday' => $this->wednesday,
            'thursday' => $this->thursday,
            'saturday' => $this->saturday,
            'friday' => $this->friday,
            'sunday' => $this->sunday,
            'convertMonitorGroupId' => $this->convert_monitor_group_id,
            'paused' => $this->paused,
            'index' => 1,
        ];
    }
}