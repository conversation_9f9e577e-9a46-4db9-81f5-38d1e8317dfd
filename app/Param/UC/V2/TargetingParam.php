<?php

namespace App\Param\UC\V2;

use App\Param\AbstractParam;

class TargetingParam extends AbstractParam
{
    public $manual_targeting;

    public $audience_targeting;

    public $include_audiences;

    public $exclude_audiences;
    public $all_region;

    public $regions;

    public $region_people;

    public $gender;

    public $age;

    public $convert_filter;

    public function toRequestData()
    {
        $data = [
            'manualTargeting' => (int)$this->manual_targeting,
            'audienceTargeting' => $this->audience_targeting,
            'allRegion' => $this->all_region,
            'regions' => $this->regions,
            'regionPeople' => (string)$this->region_people,
            'gender' => (string)$this->gender,
            'age' => (string)$this->age,
            'convertFilter' => (int)$this->convert_filter,
        ];

        $data['audiences'] = [];
        if ($this->include_audiences || $this->exclude_audiences) {
            foreach ($this->include_audiences as $include_data) {
                $data['audiences'][] = [
                    'audience' => (int)$include_data,
                    'mode' => 1
                ];
            }
            foreach ($this->exclude_audiences as $exclude_data) {
                $data['audiences'][] = [
                    'audience' => (int)$exclude_data,
                    'mode' => 2
                ];
            }
        }
       
        if ($this->manual_targeting == 0) {
            unset($data['audiences']);
            unset($data['audienceTargeting']);
        }


        return $data;
    }
}