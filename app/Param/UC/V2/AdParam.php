<?php

namespace App\Param\UC\V2;

use App\Param\AbstractParam;

class AdParam extends AbstractParam
{
    public $name;

    public $project_id;

    public $track_args;

    public $industry;

    public $label;

    public $logo_id;

    private $component_types = [];

    public function setComponentTypes($data)
    {
        $this->component_types = $data;
    }

    private $ad_url_objective_type;

    public function setAdUrlObjectType($site_id)
    {
        if (is_numeric($site_id)) {
            $data = [
                'groupTargetUrl' => [[
                    'siteId' => (int)$site_id,
                ]],
            ];
        } else {
            $data = [
                'groupTargetUrl' => [[
                    'targetUrl' => $site_id,
                ]],
            ];
        }
        $this->ad_url_objective_type = $data;
    }

    public $paused;

    public function toRequestData()
    {
        $data = [
            'name' => $this->name,
            'projectId' => (int)$this->project_id,
            'trackArgs' => $this->track_args,
            'industry' => array_map(function ($data) {
                return (int)$data;
            }, $this->industry),
            'label' => $this->label,
            'logoId' => $this->logo_id,
            'componentTypes' => $this->component_types,
            'adUrlObjectiveType' => $this->ad_url_objective_type,
            'paused' => $this->paused,
            'index' => 0,
        ];
        if (!$data['logoId']) {
            unset($data['logoId']);
        }
        return $data;
    }

}