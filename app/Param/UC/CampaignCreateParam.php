<?php

namespace App\Param\Uc;

use App\Exception\AppException;
use App\Param\AbstractParam;

/**
 * Class CampaignCreateParam
 * @package App\Param\UC
 */
class CampaignCreateParam extends AbstractParam
{
    public $adGroupId;
    public $name;
    public $type;
    public $optTarget;
    public $delivery = 0;
    public $objectives = null;
    public $trackArgs;
    public $targetings = null;
    public $budget;
    public $schedule = null;
    public $chargeType;
    public $bids = null;
    public $anxtStatus;
    public $index = 0;
    public $enableAnxt = false;
    public $paused = false;
    public $cpcBid = '0';
    public $convertInfoType = null;

    public $convertRepeatType = 0;

    public function setObjectives(ObjectivesParam $param)
    {
        $this->objectives[] = $param->toRequestBody();
    }

    public function setTargetings(TargetingsParam $param)
    {
        $this->targetings[] = $param->toRequestBody();
    }

    public function setConvertInfoType(ConvertInfoTypeParam $param)
    {
        $this->convertInfoType = $param->toRequestBody();
    }

    /**
     * @param array $schedule
     * @param string $start_date
     * @param string $end_date
     */
    public function setSchedule(array $schedule, string $start_date, string $end_date)
    {
//        if (count($schedule) != 7 * 24) { //前端传336个值过来，后端处理/2
//            throw new AppException('时间段传参有误');
//        }
        $date_index = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $i = -1;
        $data['startDate'] = (int)($start_date > 0 ? date('Ymd', $start_date) : date('Ymd'));
        $data['endDate'] = (int)($end_date > 0 ? date('Ymd', $end_date) : date('Ymd', strtotime('+30 day')));
        foreach ($schedule as $key => $value) {
            if ($key % 24 === 0) {
                $i++;
            }
            $date_string = $data[$date_index[$i]] ?? '';
            $data[$date_index[$i]] = "{$value}{$date_string}";
            if ($key == 167) {
                break;
            }
        }
        $this->schedule = $data;
    }

    public function setBids(BidParam $param)
    {
        $this->bids[] = $param->toRequestBody();
    }

    protected function paramHook()
    {
        if (!$this->name) {
            throw new AppException('推广组名字不能为空');
        }
    }

    public function toRequestBody()
    {
        $data = [];
        $arr = get_object_vars($this);
        foreach ($arr as $key => $value) {
            if ($value) {
                $data[$key] = $value;
            }
        }
        $data['enableAnxt'] = $this->enableAnxt;
        return $data;
    }
}
