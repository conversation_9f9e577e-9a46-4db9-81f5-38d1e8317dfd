<?php

namespace App\Param\Uc;

use App\Exception\AppException;
use App\Param\AbstractParam;

/**
 * Class AdGroupCreateParam
 * @package App\Param\UC
 */
class ObjectivesParam extends AbstractParam
{
    public $targetUrl;
    public $schemeUrl;
    public $siteId;
    public $appKey;
    public $packageKey;
    public $appName;
    public $convertType;
    public $adConvertId;

    public $deepConvertType;
    public $groupTargetUrl; // 多落地页
    public $admFixedUlk; // 通用链接 多落地页 选填，白名单控制
    public $appLogo;

    public $downloadUrl;
    // downloadUrl 时候必填
    public $packageName;
    public $versionName;
    public $developer;

    public $functionDesc;
    public $permission;
    public $privacy;
    public $updateTime;
    public $miniAppId;
    public $miniAppPath;

    public function toRequestBody()
    {
        $data = [];

        $arr = get_object_vars($this);
        foreach ($arr as $key => $value) {
            if ($value) {
                $data[$key] = $value;
            }
        }
        // 小游戏优先级高于落地页和锦帆落地页,所以没有则unset掉
        if (empty($data['miniAppId'])){
            unset($data['miniAppId']);
            unset($data['miniAppPath']);
        }

        $data['deepConvertType'] = $data['deepConvertType'] ?? 0;
        return $data;
    }
}
