<?php

namespace App\Param;


class AutoTransferRuleParam extends AbstractParam
{
    //规则名称
    public $name = '';
    //规则适用媒体类型
    public $media_type = 1;
    //转账类型
    public $transfer_type = '';
    //转账金额类型
    public $amount_type = '';
    //余额条件
    public $balance_condition = [];
    //数值条件
    public $calc_condition = [];
    //频率
    public $frequency = 0;
    //执行对象
    public $execution_account = [];
    //规则开始时间
    public $start_time = '1970-01-01 08:00:00';
    //规则结束时间
    public $end_time = '1970-01-01 08:00:00';
    //创建人
    public $creator = '';
    //创建时间
    public $create_time = 0;
    //编辑人
    public $editor = '';
    //编辑时间
    public $update_time = 0;
    //是否删除
    public $is_deleted = 0;

    /**
     * 转换处理
     * @return array
     */
    public function toData()
    {
        $data = $this->toArray();
        $data['balance_condition'] = json_encode($data['balance_condition']);
        $data['calc_condition'] = json_encode($data['calc_condition']);
        $data['execution_account'] = json_encode($data['execution_account']);

        return $data;
    }
}