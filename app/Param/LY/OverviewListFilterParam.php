<?php
/**
 * 发行数据总览列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\LY;


use App\Constant\ApportionOverviewSqlMap;
use App\Constant\HourOverviewSqlMap;
use App\Constant\LYServerOverviewSqlMap;
use App\Logic\LY\PermissionLogic;
use App\Param\AbstractParam;
use App\Utils\DimensionTool;

class OverviewListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度
    public $target = [];                            // 所有指标
    public $compute_target = [];                    // 处理后需要计算的指标
    public $click_query_target = [];                // click表的指标
    public $action_query_target = [];               // action表的指标
    public $reg_query_target = [];                  // reg表的指标
    public $old_query_target = [];                  // old表的指标
    public $pay_query_target = [];                  // pay表的指标
    public $start_time = 0;
    public $end_time = 0;
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $aggregation_time = '按日';
    public $limit = 10000;
    public $hour_type = 'LTV';
    public $date_hour = -1;
    public $condition = [];                          // 维度筛选
    public $continuity_date = 0;                     // 连续时间区间 0=连续 1=不连续
    public $open_days = [1, 360];                     // open_days 只有区服总览需要用上
    public $time_type = 1;                           // 1=时间 2=开服时间
    public $combine_times = 0;                       // 合服次数
    public $map_type = 0;                            // 是否地图分析1是0否


    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime($this->start_time);
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399;
        }
    }

    public function openDaysFormat()
    {
        if (empty($this->open_days)) {
            $this->open_days = [1, 2, 3, 4, 5];
        }
    }


    public function dimensionFormat()
    {
        if (in_array('date', $this->dimension)) {
            $key = array_search('date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }

        if (!in_array('platform', $this->dimension)) {
            if (!empty($this->dimension)) {
                array_unshift($this->dimension, 'platform');
            }
        }
        // 去重
        $this->dimension = array_unique($this->dimension);
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleLYDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleLYDimension($this->dimension);              // 维度处理 各种group by
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionLogic())->getLoginUserDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    /**
     * 处理指标（普通投放总览）
     */
    public function handleTarget()
    {
        $target_list = [];
        foreach ($this->target as $item) {
            if (isset(ApportionOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(ApportionOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, ApportionOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // uid_count.reg_uid_count搞成必有
        $target_list = array_merge($target_list, ['reg_uid_count']);
        // 去重
        $target_list = array_unique($target_list);

        // 获取各个表的指标
        $sql_map = new ApportionOverviewSqlMap();
        $click_table_target = $sql_map->getClickTable();
        $action_table_target = $sql_map->getActionTable();
        $reg_table_target = $sql_map->getRegTable();
        $old_table_target = $sql_map->getOldTable();
        $pay_table_target = $sql_map->getPayTable();


        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset($click_table_target[$item]):
                    $this->click_query_target[$item] = $click_table_target[$item];
                    break;
                case isset($action_table_target[$item]):
                    $this->action_query_target[$item] = $action_table_target[$item];
                    break;
                case isset($reg_table_target[$item]):
                    $this->reg_query_target[$item] = $reg_table_target[$item];
                    break;
                case isset($old_table_target[$item]):
                    $this->old_query_target[$item] = $old_table_target[$item];
                    break;
                case isset($pay_table_target[$item]):
                    $this->pay_query_target[$item] = $pay_table_target[$item];
                    break;
            }
        }
    }


    /**
     * 处理指标（区服投放总览）
     */
    public function handleServerTarget()
    {
        $target_list = [];
        foreach ($this->target as $item) {
            if (isset(LYServerOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(LYServerOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, LYServerOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // create_uid_count搞成必有
        $target_list = array_merge($target_list, ['create_uid_count']);
        // 去重
        $target_list = array_unique($target_list);
        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset(LYServerOverviewSqlMap::NEED_REG_TABLE[$item]):
                    $this->reg_query_target[$item] = LYServerOverviewSqlMap::NEED_REG_TABLE[$item];
                    break;
                case isset(LYServerOverviewSqlMap::NEED_OLD_TABLE[$item]):
                    $this->old_query_target[$item] = LYServerOverviewSqlMap::NEED_OLD_TABLE[$item];
                    break;
                case isset(LYServerOverviewSqlMap::NEED_PAY_TABLE[$item]):
                    $this->pay_query_target[$item] = LYServerOverviewSqlMap::NEED_PAY_TABLE[$item];
                    break;
            }
        }
    }

    /**
     * 处理指标（分时投放总览）
     */
    public function handleHourTarget()
    {
        $target_list = [];
        foreach ($this->target as $item) {
            if (isset(HourOverviewSqlMap::ALL_TARGET_MAP[$item])) {
                // 判断这个指标是否需要计算的指标
                if (count(HourOverviewSqlMap::ALL_TARGET_MAP[$item]) > 1) {
                    $this->compute_target[$item] = $item;
                }

                $target_list = array_merge($target_list, HourOverviewSqlMap::ALL_TARGET_MAP[$item]);
            }
        }

        // first_pay_uid_count pay_money pay_uid_count reg_uid_count cost_money 必有
        $target_list = array_merge($target_list, ['first_pay_uid_count', 'pay_money', 'pay_uid_count', 'reg_uid_count', 'cost_money']);
        // 去重
        $target_list = array_unique($target_list);
        // 分表处理
        foreach ($target_list as $item) {
            // 分不同的表指标
            switch ($item) {
                case isset(HourOverviewSqlMap::NEED_CLICK_TABLE[$item]):
                    $this->click_query_target[$item] = HourOverviewSqlMap::NEED_CLICK_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_ACTION_TABLE[$item]):
                    $this->action_query_target[$item] = HourOverviewSqlMap::NEED_ACTION_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_REG_TABLE[$item]):
                    $this->reg_query_target[$item] = HourOverviewSqlMap::NEED_REG_TABLE[$item];
                    break;
                case isset(HourOverviewSqlMap::NEED_PAY_TABLE[$item]):
                    $this->pay_query_target[$item] = HourOverviewSqlMap::NEED_PAY_TABLE[$item];
                    break;
            }
        }
    }

    public function switchColumnKey()
    {
        $key_prefix = '';
        switch ($this->hour_type) {
            case '激活人数':
                $key_prefix = 'action_count_';
                break;
            case '注册人数':
                $key_prefix = 'reg_uid_count_';
                break;
            case '注册付费人数':
                $key_prefix = 'reg_pay_uid_count_';
                break;
            case '注册付费金额':
                $key_prefix = 'pay_money_';
                break;
            case '总付费人数':
                $key_prefix = 'pay_uid_count_';
                break;
            case '总付费金额':
                $key_prefix = 'total_pay_money_';
                break;
        }

        return $key_prefix;
    }
}
