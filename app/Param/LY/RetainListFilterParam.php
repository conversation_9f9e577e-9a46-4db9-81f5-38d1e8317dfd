<?php
/**
 * 总览列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\LY;


use App\Exception\AppException;
use App\Logic\LY\PermissionLogic;
use App\Param\AbstractParam;
use App\Utils\DimensionTool;

class RetainListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];              // 处理前的维度筛选
    public $dimension_group_by = [];                // 处理后的维度
    public $dimension = [];                         // 处理前的维度
    public $target = [];                            // 指标 也就是各种留存
    public $retain_list = [];                       // 处理后的留存数组
    public $start_time = 0;                         // 开始时间
    public $end_time = 0;                           // 结束时间
    public $reg_uid_count = 1;                      // 注册数筛选，要>=这个数
    public $role_create_count = 1;                  // 角色创角数筛选，要>=这个数
    public $aggregation_time = '聚合';               // 时间聚合类型
    public $aggregation_retain = '新增账号登录留存';   // 留存聚合类型 不同类型SQL不一样
    public $pay_start = -1;                         // 付费金额区间
    public $pay_end = -1;                           // 付费金额区间
    public $pay_type_filter = [];                    // 付费金额区间筛选
    public $pay_type_filter_format = [];             // 付费金额区间筛选
    public $is_rate = 0;                            // 默认查看xx留率 0表示查看xx留数
    public $platform_belong = '';                   // 平台归属
    public $group_belong = '';                      // 集团归属
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $default_sort = 1;                       // 2 指标排序 1默认排序 3维度排序
    public $limit = 10000000;                       // SQL查询的limit
    public $aggregation_retain_arr = ['新增账号登录留存', '付费账号登录留存', '付费账号付费留存', '非付费账号登录留存', '付费账号留存(注册)', '付费留存/注册', '首日新增新付费用户留存'];
    public $server_type = '';                        // 区服的留存类型
    public $export_type = 1;                         // 导出类型  1=普通留存（也就是注册留存） 2=区服留存
    public $blocked = 0;                             // 是否屏蔽当日
    public $pay_retain = ['付费账号登录留存', '付费账号付费留存', '付费账号留存(注册)', '首日新增新付费用户留存', '付费角色登录留存', '付费角色付费留存']; // 有付费区间的留存类型


    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
        }
    }

    /**
     * 处理开服区间筛选 "加层级"
     */
    public function payTypeFilterFormat()
    {
//        $this->pay_type_filter = [[1, 100], [101, 200], [201]];
        $open_days_filter_format = [];
        $index = 0;
        $last_level_value = 0;
        $last_index = count($this->pay_type_filter) - 1;
        foreach ($this->pay_type_filter as $index => $item) {
            // 最后一个区间没关闭的情况，比如：[100,无穷大)
            if ($index === $last_index && !isset($item[1])) {
                // 判断付费区间
                if ($last_level_value > $item[0]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-无穷大";
                $open_days_filter_format[$key] = $item;
            } else {
                // 判断付费区间
                if ($last_level_value > $item[1]) {
                    throw new AppException('错误的区间范围');
                }
                $key = "{$index}-{$item['0']}-{$item[1]}";
                $open_days_filter_format[$key] = $item;
                // 判断开服区间
                if ($item[1] < $item[0]) {
                    throw new AppException('错误的区间范围');
                }

                $last_level_value = $item[1];
            }

        }
        $index++;
        $key = "{$index}-others";
        $open_days_filter_format[$key] = [];
        $this->pay_type_filter_format = $open_days_filter_format;
    }


    public function regUidCountFormat()
    {
        $this->reg_uid_count = $this->reg_uid_count > 1 ? $this->reg_uid_count : 1;
    }

    /**
     * 把各种需要算的留存数提取出来
     * target是类似这种：rate_day_stay_{num} 或 rate_day_stay_{num1}_{num2}
     * 需要把num提出来放retain_list数组
     */
    public function targetFormat()
    {
        if ($this->target) {
            foreach ($this->target as $item) {
                $tmp = explode('_', $item);
                $this->retain_list[] = intval($tmp[3]);
                if (isset($tmp[4]) && is_numeric($tmp[4])) {
                    for ($i = $tmp[3] + 1; $i <= $tmp[4]; $i++) {
                        $this->retain_list[] = intval($i);
                    }
                }
            }

            // xx留排序
            asort($this->retain_list);
            $this->retain_list = array_values($this->retain_list);
        }
    }


    public function dimensionFormat()
    {
        // 前端的一些特殊原因，会把game_reg_date和create_date这个"不是维度的维度"传过来，所以这里要去掉
        if (in_array('game_reg_date', $this->dimension)) {
            $key = array_search('game_reg_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (in_array('create_date', $this->dimension)) {
            $key = array_search('create_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }

        if (!in_array('platform', $this->dimension)) {
            if (!empty($this->dimension)) {
                array_unshift($this->dimension, 'platform');
            }
        }

        // 有区服id的话就去掉它
        if (in_array('ori_server_id', $this->dimension)) {
            $key = array_search('ori_server_id', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }

    }


    public function paramHook()
    {
        $this->dimension_filter = DimensionTool::handleLYDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleLYDimension($this->dimension);    // 维度处理 各种group by
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionLogic())->getLoginUserDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }
}
