<?php
/**
 * 付费情况列表筛选参数
 *
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-19
 * Time: 11:24
 */

namespace App\Param\LY;


use App\Exception\AppException;
use App\Logic\LY\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class PaymentListFilterParam extends AbstractParam
{
    public $dimension_filter = [];                  // 处理后的维度筛选数组
    public $dimension_filter_raw = [];              // 处理前的维度筛选数组
    public $dimension = [];                         // 处理前的维度
    public $dimension_group_by = [];                // 处理后的维度数组
    public $target = [];                            // 指标
    public $day_list = [];                          // 处理后的指标（天数）数组
    public $header_day_list = [];                   // 导出需要的指标（天数）数组
    public $start_time = 0;                         // 开始时间
    public $end_time = 0;                           // 结束时间
    public $day_reg_uid_count = 0;                  // 注册数筛选，要>=这个数
    public $day_create_role_id_count = 0;           // 创角数筛选，要>=这个数
    public $aggregation_time = '按月';               // 时间聚合类型
    public $type = '回本率';                         // 列表数据类型
    public $days = 0;                               // 所选的开始日期到今天一共需要算多少天的付费情况
    public $day_cost_money = 0;                     // 是否有消耗
    public $platform_belong = '';                   // 平台归属
    public $group_belong = '';                      // 集团归属
    public $game_permission;                        // 游戏权限
    public $agent_permission;                       // 渠道权限
    public $default_sort = 1;                       // 2 指标排序 1默认排序 3维度排序
    public $export_type = 1;                        // 导出类型 默认是1，1=注册的付费情况 2=区服的付费情况
    public $limit = 10000000;                       // SQL查询的limit
    public $blocked = 0;                            // 是否屏蔽当日
    public $type_arr = [
        '回本率', 'LTV', '累计付费', '每日回本率', '每日LTV', '每日付费', '每日付费人数', '累计付费率', '每日付费ARPU',
        '每日付费率', '累计付费ARPU', '累计付费成本'
    ];


    public function startTimeFormat()
    {
        if ($this->start_time) {
            $this->start_time = strtotime(date("Ymd", strtotime($this->start_time)));
        }
    }


    public function endTimeFormat()
    {
        if ($this->end_time) {
            $this->end_time = strtotime(date("Ymd", strtotime($this->end_time))) + 86399; // 要到当天23：59：59
        }
    }

    public function aggregationPayFormat()
    {
        if (!in_array($this->type, $this->type_arr)) {
            throw new AppException('参数错误');
        }
    }

    /**
     * 把各种需要算的天数提取出来
     * target是类似这种：day_{num} 或 day_{num1}_{num2}
     * 需要把num提出来放day_list数组
     * 需要判断真实需要到达的天数 比如开始日期是今天 就不可能存在2日 3日这中
     * 导出的header_day_list不需要判断
     */
    public function setDayList()
    {
        if ($this->target) {
            foreach ($this->target as $item) {
                // 消耗指标特殊处理一下
                if ($item === 'day_cost_money') {
                    $this->day_cost_money = 1;
                    continue;
                }
                $tmp = explode('_', $item);
                if ($this->days >= $tmp[1]) {
                    $this->day_list[] = intval($tmp[1]);
                }
                $this->header_day_list[] = intval($tmp[1]);
                if (isset($tmp[2])) {
                    for ($i = $tmp[1] + 1; $i <= $tmp[2]; $i++) {
                        if ($this->days >= $i) $this->day_list[] = intval($i);
                        $this->header_day_list[] = intval($i);
                    }
                }
            }

            // xx日排序
            asort($this->header_day_list);
            $this->header_day_list = array_values($this->header_day_list);
            $this->day_list = array_values($this->day_list);
        }
    }


    public function dimensionFormat()
    {
        // 前端的一些特殊原因，会把game_reg_date这个"不是维度的维度"传过来，所以这里要去掉
        if (in_array('game_reg_date', $this->dimension)) {
            $key = array_search('game_reg_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }

        if (!in_array('platform', $this->dimension)) {
            if (!empty($this->dimension)) {
                array_unshift($this->dimension, 'platform');
            }
        }
        // 有区服id的话就去掉它
        if (in_array('ori_server_id', $this->dimension)) {
            $key = array_search('ori_server_id', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        if (in_array('create_date', $this->dimension)) {
            $key = array_search('create_date', $this->dimension);
            array_splice($this->dimension, $key, 1);
        }
        // 去重
        $this->dimension = array_unique($this->dimension);
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionLogic())->getLoginUserDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }


    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);

        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }

    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleLYDimensionFilter($this->dimension_filter);   // 处理维度筛选
        $this->dimension_group_by = DimensionTool::handleLYDimension($this->dimension);              // 维度处理 各种group by
        $this->days = floor((time() - $this->start_time) / 86400) + 1;
        $this->setDayList();
    }

    /**
     * 导出时，处理一下固定列
     *
     * @param $export_type
     *
     * @return string[]
     */
    public function fixedHeader($export_type)
    {
        // 日期、注册数等固定列
        if ($this->aggregation_time !== '聚合') {
            if ($export_type == 2) {
                $header = ['create_date'];
            } else {
                $header = ['game_reg_date'];
            }
        }
        if ($this->type !== '回本率' || $this->type !== '每日回本率' && $this->type !== '累计付费成本') {
            if ($this->day_cost_money) {
                $header[] = 'day_cost_money';
            }
            $header[] = 'reg_cost';
        }
        $header[] = 'day_reg_uid_count';
        switch ($this->type) {
            case '累计付费':
                $header[] = 'current_tpay';
                break;
            case 'LTV':
                $header[] = 'current_ltv';
                break;
            case '回本率':
                $header[] = 'current_roi';
                break;
            case '累计付费率':
                $header[] = 'current_trate';
                break;
            case '累计付费ARPU':
                $header[] = 'current_tarpu';
                break;
            case '累计付费成本':
                $header[] = 'current_cost';
                break;
        }

        return $header;
    }

    /**
     * 导出时，列的key值选择
     * 根据选择的type来决定
     */
    public function switchColumnKey()
    {
        switch ($this->type) {
            case '回本率':
                $key_prefix = 'roi_';
                break;
            case '每日回本率':
                $key_prefix = 'proi_';
                break;
            case 'LTV':
                $key_prefix = 'ltv_';
                break;
            case '每日LTV':
                $key_prefix = 'pltv_';
                break;
            case '每日付费':
                $key_prefix = 'ppay_';
                break;
            case '累计付费':
                $key_prefix = 'tpay_';
                break;
            case '每日付费人数':
                $key_prefix = 'pcount_';
                break;
            case '每日付费ARPU':
                $key_prefix = 'parpu_';
                break;
            case '累计付费率':
                $key_prefix = 'trate_';
                break;
            case '每日付费率':
                $key_prefix = 'prate_';
                break;
            case '累计付费ARPU':
                $key_prefix = 'tarpu_';
                break;
            case '累计付费成本':
                $key_prefix = 'cost_';
                break;
            case '累计付费人数':
                $key_prefix = 'tcount_';
                break;
            case '每日ARPU':
                $key_prefix = 'arpu_';
                break;
            default:
                $key_prefix = 'roi_';
        }

        return $key_prefix;
    }
}