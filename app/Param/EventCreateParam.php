<?php
/**
 * 创建事件
 * User: huangmingcan
 * Date: 2021-11-29
 */

namespace App\Param;


use App\Constant\ConvertSourceType;
use App\Constant\EventManager;
use App\Constant\MediaType;
use App\Exception\AppException;

/**
 * Class EventCreateParam
 * @package App\Param
 */
class EventCreateParam extends AbstractParam
{
    const TRACK_TYPES = [
        MediaType::TOUTIAO => [
            ConvertSourceType::API => 'APPLICATION_API',
            ConvertSourceType::SDK => 'APPLICATION_SDK',
            ConvertSourceType::H5_API => 'EXTERNAL_API',
            'MINI_PROGRAME_API' => 'MINI_PROGRAME_API' // 特殊 头条小游戏集团系统转化来源也用H5_API，但这里需要用到其特有的track_type
        ]
    ];

    /**
     * @var MediaAccountInfoParam $media_account media_account的一条记录
     */
    public $media_account;

    /**
     * @var int $asset_id 资产ID
     */
    public $asset_id = 0;

    /**
     * @var int $event_id 事件ID
     */
    public $event_id = 0;

    /**
     * @deprecated 2022.05.26 下线 由计划「deep_bid_type」深度优化方式字段来选择
     * @var string $statistical_type 统计方式， 允许值：ONLY_ONE 仅一次、EVERY_ONE 每一次
     */
    public $statistical_type = '';

    /**
     * @var string $track_types 事件回传方式列表，允许值：JSSDK：JS埋码 、EXTERNAL_API：API回传 、XPATH：XPath圈选、
     * APPLICATION_API：应用API 、APPLICATION_SDK：应用SDK 、QUICK_APP_API：快应用API
     */
    public $track_types = '';

    /**
     * @var string $convert_source_type 转化来源
     */
    public $convert_source_type = '';

    /**
     * @var string $convert_data_type 转化统计方式
     */
    public $convert_data_type = '';

    protected function paramHook()
    {
        if ($this->media_account->account_id <= 0) {
            throw new AppException('账户信息传参有误');
        }

        if ($this->asset_id <= 0) {
            throw new AppException('资产ID传参有误');
        }

        if ($this->event_id <= 0) {
            throw new AppException('事件ID传参有误');
        }

        if (!$this->convert_source_type) {
            throw new AppException('转化来源传参有误');
        }

        $method = "param{$this->media_account->media_type}Hook";
        if (method_exists($this, $method)) {
            $this->$method();
        }
    }

    private function param1Hook()
    {
        // 只有付费才能用每一次
        if ($this->event_id === EventManager::EVENT_MAP[EventManager::EVENT_PAY] && $this->convert_data_type === 'EVERY_ONE') {
            $this->statistical_type = 'EVERY_ONE';
        }
    }

    /**
     * 把EventCreateParam类转化成请求的body
     */
    public function toRequestBody()
    {
        $method = "to{$this->media_account->media_type}RequestBody";
        if (method_exists($this, $method)) {
            return $this->$method();
        }
        throw new AppException("媒体类型:{$this->media_account->media_type}尚未对接事件");
    }

    /**
     * 头条
     */
    private function to1RequestBody()
    {
        $body = [
            'advertiser_id' => $this->media_account->account_id,
            'asset_id' => $this->asset_id,
            'event_id' => $this->event_id,
            'track_types' => [
                self::TRACK_TYPES[$this->media_account->media_type][$this->convert_source_type]
            ],
        ];

        return $body;
    }
}
