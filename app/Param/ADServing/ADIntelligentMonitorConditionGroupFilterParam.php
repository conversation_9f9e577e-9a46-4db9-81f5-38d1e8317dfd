<?php
/**
 * User: zzh
 * Date: 2019-12-10
 */

namespace App\Param\ADServing;


use App\Constant\InitIntelligentMonitor;
use App\Param\AbstractParam;

/**
 * Class ADIntelligentMonitorConditionGroupFilterParam
 * @package App\Param\ADServing
 */
class ADIntelligentMonitorConditionGroupFilterParam extends AbstractParam
{
    public $media_type = 0;

    public $media_agent_type = 0;
    public $condition_list = [];
    public $group_by_target_dim = '';
    public $condition_group_map = '';

    public function paramHook()
    {
        foreach ($this->condition_list as $condition) {

            $condition_target_level = InitIntelligentMonitor::LEVEL_CONFIG[$this->media_type][$this->media_agent_type][$condition['condition_target_dim']];

            $group_by_target_level = InitIntelligentMonitor::LEVEL_CONFIG[$this->media_type][$this->media_agent_type][$this->group_by_target_dim];

            $map_info = [];

            if ($condition_target_level == $group_by_target_level) {
                $map_info['dim_type'] = 'equip';
                $map_info['select_target'] = $condition['condition_target'];
                $map_info['origin_target'] = [];
                $map_info['limit_target'] = '';
                $map_info['where_action'] = $condition['condition_target_action'];
                $map_info['where_action_value'] = $condition['condition_target_value'];
            } elseif ($condition_target_level < $group_by_target_level) {
                $map_info['dim_type'] = 'up';
                $map_info['select_target'] = $condition['condition_target'];
                $map_info['origin_target'] = [];
                $map_info['limit_target'] = '1';
                $map_info['where_action'] = $condition['condition_target_action'];
                $map_info['where_action_value'] = $condition['condition_target_value'];
            } else {
                $map_info['dim_type'] = 'down';
                $map_info['select_target'] = "COUNT(*) as {$condition['condition_target_dim']}_num";
                $map_info['origin_target'] = [
                    "condition_target" => $condition["condition_target"],
                    "condition_target_value" => $condition["condition_target_value"],
                    "condition_target_action" => $condition["condition_target_action"],
                ];
                $map_info['limit_target'] = '';
                $map_info['where_action'] = '>';
                $map_info['where_action_value'] = '0';
            }

            $this->condition_group_map["{$condition['time_range_type']}_{$condition['time_range']}_{$condition['time_range_unit']}"][] = $map_info;
        }
    }

}
