<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\BaiduSearch;

use App\Constant\BatchAD;
use App\Constant\BsEnum;
use App\Param\ADServing\AbstractADComposeContentParam;
use App\Utils\Helpers;

class ADComposeContentParam extends AbstractADComposeContentParam
{

    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD1Name() method.
    }

    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD2Name() method.
    }

    protected function unionParamValidateJudge()
    {
        // TODO: Implement unionParamValidateJudge() method.
    }

    public function getMaterialFileNormalList(): array
    {
        return [];
    }
}
