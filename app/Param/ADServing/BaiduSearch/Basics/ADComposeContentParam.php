<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\BaiduSearch\Basics;

use App\Constant\BatchAD;
use App\Param\ADServing\BaiduSearch\ADComposeContentParam as BaiduSearchADComposeContentParam;
use App\Utils\Helpers;

class ADComposeContentParam extends BaiduSearchADComposeContentParam
{
    /**
     * @var ADOtherSettingContentParam $other_setting
     */
    public $other_setting;

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];

        // 校验落地页
        foreach ($this->account_list->account_list as $key => $account_info) {
            $page_id = $this->other_setting->getPageInfo($account_info['account_id'])->page_id ?? '';
            if (!$page_id) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'page_map',
                    'value' => $this->other_setting->page_map,
                    'msg' => "请选择{$account_info['account_id']}对应的落地页信息"
                ];
            }
        }

        if ((int) $this->compose_config->word_num_in_ad > 1 && (int)$this->compose_config->word_material_compose_mode === 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'word_material_compose_mode',
                'value' => $this->compose_config->creative_word_num_in_ad,
                'msg' => "文案素材不可乘积"
            ];
        }

        foreach ($this->material_list->all_image_list as $image_info) {
            if ((int)$image_info['width'] !== 800 || (int)$image_info['height'] !== 800) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => $image_info['id'],
                    'msg' => "{$image_info['url']}素材规格不符合800*800规格"
                ];
                break;
            }
        }

        foreach ($this->word_list->getWordContent() as $word) {
            if (Helpers::ADServingStrLen($word) > 60) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'value' => 0,
                    'msg' => "创意要求文案为1-60个字符，汉字占两个字符，请检查"
                ];
                break;
            }
        }

        return $error_msg_list;
    }

//    public function segmentTypeValidate()
//    {
//        $error_msg_list = [];
//
//        if ((int)$this->compose_config->pic_num_in_ad !== 1) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                'key' => 'pic_num_in_ad',
//                'value' => $this->compose_config->pic_num_in_ad,
//                'msg' => "单元创意图片数量只能为1"
//            ];
//        }
//        if (!$this->material_list->group_image_list) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                'value' => 0,
//                'msg' => "素材需要使用3组图(800x800)"
//            ];
//        } else {
//            foreach ($this->material_list->group_image_list as $image_info) {
//                if(count($image_info['image_list']) != $this->compose_config->creative_word_num_in_ad) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                        'key' => 'creative_word_num_in_ad',
//                        'value' => $this->compose_config->creative_word_num_in_ad,
//                        'msg' => "创意文案数需要等于组图图片数"
//                    ];
//                }
//                if ((int)$image_info['width'] !== 800 || (int)$image_info['height'] !== 800) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                        'value' => 0,
//                        'msg' => "{$image_info['url']}素材规格不符合800*800规格"
//                    ];
//                    break;
//                }
//            }
//        }
//        foreach ($this->word_list->getWordContent() as $word) {
//            if (Helpers::ADServingStrLen($word) > 60) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                    'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                    'value' => 0,
//                    'msg' => "创意要求文案为1-60个字符，汉字占两个字符，请检查"
//                ];
//                break;
//            }
//        }
//
//        /*if ((int)$this->other_setting->creative_type === BsEnum::CREATE_TYPE_BASE) {
//
//            if ((int)$this->compose_config->pic_num_in_ad !== 1) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                    'key' => 'pic_num_in_ad',
//                    'value' => $this->compose_config->pic_num_in_ad,
//                    'msg' => "单元创意图片数量只能为1"
//                ];
//            }
//            if (!$this->material_list->group_image_list) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                    'value' => 0,
//                    'msg' => "素材需要使用3组图(800x800)"
//                ];
//            } else {
//                foreach ($this->material_list->group_image_list as $image_info) {
//                    if(count($image_info['image_list']) != $this->compose_config->creative_word_num_in_ad) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                            'key' => 'creative_word_num_in_ad',
//                            'value' => $this->compose_config->creative_word_num_in_ad,
//                            'msg' => "创意文案数需要等于组图图片数"
//                        ];
//                    }
//                    if ((int)$image_info['width'] !== 800 || (int)$image_info['height'] !== 800) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "{$image_info['url']}素材规格不符合800*800规格"
//                        ];
//                        break;
//                    }
//                }
//            }
//            foreach ($this->word_list->getWordContent() as $word) {
//                if (Helpers::ADServingStrLen($word) > 60) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                        'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                        'value' => 0,
//                        'msg' => "创意要求文案为1-60个字符，汉字占两个字符，请检查"
//                    ];
//                    break;
//                }
//            }
//        } else {
//
//            switch ($this->other_setting->segment_type) {
//                case 310: // 移动图集
//                    if ($this->compose_config->creative_word_num_in_ad > 1) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                            'key' => 'creative_word_num_in_ad',
//                            'value' => $this->compose_config->creative_word_num_in_ad,
//                            'msg' => '创意文案数量不能大于1'
//                        ];
//                    }
//                    if (!$this->material_list->group_image_list) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "素材需要使用3组图(800x800)"
//                        ];
//                    } else {
//                        foreach ($this->material_list->group_image_list as $image_info) {
//                            if ((int)$image_info['width'] !== 800 || (int)$image_info['height'] !== 800) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$image_info['url']}素材规格不符合800x800规格"
//                                ];
//                                break;
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 9 || $title_len > 80) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为9-80个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 311: // 计算机图集
//                    if ($this->compose_config->creative_word_num_in_ad > 1) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                            'key' => 'creative_word_num_in_ad',
//                            'value' => $this->compose_config->creative_word_num_in_ad,
//                            'msg' => '创意文案数量不能大于1'
//                        ];
//                    }
//                    if (!$this->material_list->group_image_list) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "素材需要使用4组图(646x400)"
//                        ];
//                    } else {
//                        foreach ($this->material_list->group_image_list as $image_info) {
//                            if ((int)$image_info['width'] !== 646 || (int)$image_info['height'] !== 400) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$image_info['url']}素材规格不符合646x400规格"
//                                ];
//                                break;
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 9 || $title_len > 80) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为9-80个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 312: // 移动图文
//                    if (!$this->material_list->group_image_list) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "素材需要使用3组图(800x800)"
//                        ];
//                    } else {
//                        foreach ($this->material_list->group_image_list as $image_info) {
//                            if(count($image_info['image_list']) != $this->compose_config->creative_word_num_in_ad) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                                    'key' => 'creative_word_num_in_ad',
//                                    'value' => $this->compose_config->creative_word_num_in_ad,
//                                    'msg' => "创意文案数需要等于组图图片数"
//                                ];
//                            }
//                            if ((int)$image_info['width'] !== 800 || (int)$image_info['height'] !== 800) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$image_info['url']}素材规格不符合800x800规格"
//                                ];
//                                break;
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 4 || $title_len > 14) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为4-14个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 313: // 计算机图文
//                    if (!$this->material_list->group_image_list) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "素材需要使用4组图(646x400)"
//                        ];
//                    } else {
//                        foreach ($this->material_list->group_image_list as $image_info) {
//                            if(count($image_info['image_list']) != $this->compose_config->creative_word_num_in_ad) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
//                                    'key' => 'creative_word_num_in_ad',
//                                    'value' => $this->compose_config->creative_word_num_in_ad,
//                                    'msg' => "创意文案数需要等于组图图片数"
//                                ];
//                            }
//                            if ((int)$image_info['width'] !== 646 || (int)$image_info['height'] !== 400) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$image_info['url']}素材规格不符合646x400规格"
//                                ];
//                                break;
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 4 || $title_len > 14) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为4-14个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 320: // 移动大图
//                    foreach ($this->material_list->all_image_list as $image_info) {
//                        if ((int)$image_info['width'] !== 800 || (int)$image_info['height'] !== 267) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'value' => 0,
//                                'msg' => "{$image_info['url']}素材规格不符合800x267规格"
//                            ];
//                            break;
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 1 || $title_len > 60) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为1-60个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 321: // 计算机大图
//                    foreach ($this->material_list->all_image_list as $image_info) {
//                        if ((int)$image_info['width'] !== 518 || (int)$image_info['height'] !== 292) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'value' => 0,
//                                'msg' => "{$image_info['url']}素材规格不符合518x292规格"
//                            ];
//                            break;
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 1 || $title_len > 60) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为1-60个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 10001: // 16:9视频
//                    foreach ($this->material_list->all_video_list as $video_info) {
//                        if ((int)$video_info['width'] !== 1280 || (int)$video_info['height'] !== 720) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'value' => 0,
//                                'msg' => "{$video_info['url']}素材规格不符合1280*720视频组件"
//                            ];
//                        }
//                        foreach ($video_info['cover_list'] as $cover) {
//                            if ($video_info['width'] != $cover['width'] || $video_info['height'] != $cover['height']) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$video_info['url']}封面与视频的尺寸不一致"
//                                ];
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 9 || $title_len > 80) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为9-80个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 10002: // 9:16视频
//                    foreach ($this->material_list->all_video_list as $video_info) {
//                        if ((int)$video_info['width'] !== 720 || (int)$video_info['height'] !== 1280) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'value' => 0,
//                                'msg' => "{$video_info['url']}素材规格不符合720*1280视频组件"
//                            ];
//                        }
//                        foreach ($video_info['cover_list'] as $cover) {
//                            // 3:4封面
//                            if ((int)$cover['width'] !== 540 || (int)$cover['height'] !== 720) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$cover['url']}-9:16视频组件要求封面比例为540:720,请检查"
//                                ];
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 9 || $title_len > 80) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为9-80个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//                case 10003: // 1:1视频
//                    foreach ($this->material_list->all_video_list as $video_info) {
//                        if ((int)$video_info['width'] !== 800 || (int)$video_info['height'] !== 800) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                'value' => 0,
//                                'msg' => "{$video_info['url']}素材规格不符合800*800视频组件"
//                            ];
//                        }
//                        foreach ($video_info['cover_list'] as $cover) {
//                            if ($video_info['width'] != $cover['width'] || $video_info['height'] != $cover['height']) {
//                                $error_msg_list[] = [
//                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                                    'value' => 0,
//                                    'msg' => "{$video_info['url']}封面与视频的尺寸不一致"
//                                ];
//                            }
//                        }
//                    }
//                    foreach ($this->word_list->getWordContent() as $word) {
//                        $title_len = Helpers::ADServingStrLen($word);
//                        if ($title_len < 9 || $title_len > 80) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'value' => 0,
//                                'msg' => "创意要求文案为9-80个字符，汉字占两个字符，请检查"
//                            ];
//                            break;
//                        }
//                    }
//                    break;
//            }
//
//        }*/
//        return $error_msg_list;
//    }
}
