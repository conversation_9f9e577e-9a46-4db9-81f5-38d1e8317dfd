<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\BaiduSearch\Basics;

use App\Constant\BsEnum;
use App\Param\ADServing\BaiduSearch\ADOtherSettingContentParam as BaiduSearchADOtherSettingContentParam;

class ADOtherSettingContentParam extends BaiduSearchADOtherSettingContentParam
{
    public $page_map;

    /**
     * 广告类型
     * @var int
     */
    public $ad_type = 0;

    /**
     * 链接类型
     * @var int
     */
    public $subject = BsEnum::SUBJECT_LINK;

    /**
     * 营销目标类型
     * @var
     */
    public $marketing_target_id;

    /**
     * 落地页地址
     * @var
     */
    public $lp_url;

    /**
     * 显示网址
     * @var
     */
    public $display_url;

    /**
     * 计划出价类型
     * @var int
     */
    public $bid_prefer = 2;


    /**
     * 组件类型
     * @var
     */
    public $segment_type;

    /**
     * 创意类型
     * 1：创意 2：创意组件
     * @var
     */
    public $creative_type;

    /**
     * 视频物料内容
     * @var array
     */
    public $video_image_map = [];

    /**
     * 是否使用关键词出价策略
     * @var int
     */
    public $price_strategy_status = BsEnum::NONE;

    /**
     * 关键词出价策略
     * @var int
     */
    public $price_strategy_map = [];

    /**
     * 是否使用ocpc出价策略
     * @var int
     */
    public $ocpc_status = BsEnum::NONE;

    /**
     * ocpc出价策略
     * @var int
     */
    public $ocpc_map = [];

    /**
     * 推广业务ID
     * 默认为2503003 DSP平台
     * 后续需要哪些ID待补充  不考虑接口拉取
     * @var string
     */
    public $business_point_id = 201802;

    /**
     * 是否绑定组件
     * @var
     */
    public $segments_status = BsEnum::NONE;

    /**
     * 组件列表
     * @var
     */
    public $segments_map;

    /**
     * 组件类型集合
     * @var
     */
    public $segment_types;

    /**
     * 人群列表
     * @var
     */
    public $crowd_map;

    /**
     * 是否绑定人群
     * @var
     */
    public $crowd_status = BsEnum::NONE;



    public function paramHook()
    {
        $this->segments_map = $this->tranObject($this->segments_map);
        $this->crowd_map = $this->tranObject($this->crowd_map);
        $this->ocpc_map = $this->tranObject($this->ocpc_map);
        $this->page_map = $this->tranObject($this->page_map);
        $this->price_strategy_map = $this->tranObject($this->price_strategy_map);
        $this->format();
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        return [];
    }

    /**
     * 参数格式化
     */
    public function format()
    {
        if (in_array($this->subject, [BsEnum::SUBJECT_LINK])) {
            $this->marketing_target_id = 0;
        } else {
            $this->marketing_target_id = 1;
        }
    }

    public function getVideoImageMap()
    {
        switch ((int)$this->segment_type) {
            case 10001:
                return ['video_image_type' => 100, 'video_image_radio' => '16:9'];
            case 10002:
                return ['video_image_type' => 203, 'video_image_radio' => '3:4'];
            case 10003:
                return ['video_image_type' => 300, 'video_image_radio' => '1:1'];
        }
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array|mixed
     */
    public function getPageInfo($account_id)
    {
        if ($page_info = $this->page_map[$account_id] ?? '') {
            $page_info = json_decode($page_info);
            return $page_info;
        }
        return [];
    }

    /**
     * 获取组件绑定信息
     * @param $account_id
     * @return array|mixed
     */
    public function getSegmentsInfo($account_id)
    {
        return $this->segments_map[$account_id] ?? [];
    }

    /**
     * 获取人群绑定信息
     * @param $account_id
     * @return array|mixed
     */
    public function getCrowdInfo($account_id)
    {
        return $this->crowd_map[$account_id] ?? [];
    }

    /**
     * 获取关键词出价策略
     * @param $account_id
     * @return array|mixed
     */
    public function getPriceStrategyInfo($account_id)
    {
        if ($price_strategy_info = $this->price_strategy_map[$account_id] ?? '') {
            $price_strategy_info = json_decode($price_strategy_info);
            return $price_strategy_info;
        }
        return [];
    }

    /**
     * 获取ocpc出价策略
     * @param $account_id
     * @return array|mixed
     */
    public function getOcpcInfo($account_id)
    {
        if ($ocpc_info = $this->ocpc_map[$account_id] ?? '') {
            $ocpc_info = json_decode($ocpc_info);
            return $ocpc_info;
        }
        return [];
    }

    /**
     * 是否为计算机广告
     * @return bool
     */
    public function isPc()
    {
        return (int)$this->bid_prefer === 1;
    }

    /**
     * 图片素材
     * @return bool
     */
    public function isPic()
    {
        return in_array($this->segment_type, [310, 311, 312, 313, 320, 321]);
    }
}
