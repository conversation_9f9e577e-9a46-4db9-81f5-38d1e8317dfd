<?php
namespace App\Param\ADServing;

use App\Container;
use App\Param\AbstractParam;

class ADLiveOrderComposeParam extends AbstractParam
{
    public $id;
    public $name;
    public $dsp_order_type =  2;
    public $platform = 'TW';
    public $live_media_type = 1;
    public $author_setting;

    public function toInsertData()
    {
        $this->creator = Container::getSession()->name;
        $data = $this->toArray();
        $data['author_setting'] = $data['author_setting'] ? json_encode($data['author_setting']) : '{}';
        unset($data['id']);
        return $data;
    }

    public function toEditData()
    {
        $this->editor = Container::getSession()->name;
        $data = $this->toArray();
        $data['author_setting'] = $data['author_setting'] ? json_encode($data['author_setting']) : '{}';
        return $data;
    }
}