<?php

namespace App\Param\ADServing;

use App\Param\AbstractParam;

class ADIntelligentMonitorBindLogSearchParam extends AbstractParam
{

    public $monitor_robot_id;

    public $target_type;

    public $bind_target_value;

    public $target_value;

    public $status;

    public $page = 1;

    public $rows = 50;

    public $create_time;

    public function paramHook()
    {


        if ($this->create_time) {
            $this->create_time = array_map(function ($time) {
                return date('Y-m-d H:i:s', (int)$time / 1000);
            }, $this->create_time);
        }
    }

}
