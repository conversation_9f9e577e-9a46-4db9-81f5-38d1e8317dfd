<?php

namespace App\Param\ADServing;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class ADIntelligentMonitorExecBodyParam extends AbstractParam
{
    public $name;

    public $media_type;

    public $media_agent_type;
    public $exec_target_dim;

    public $exec_body = [];
    public $success_action = [];
    public $fail_action = [];

    public $status = 1;

    public $creator;

    public function validate()
    {
        if (!($this->exec_body['condition_group_list'] ?? [])) {
            throw new AppException('条件组为空');
        } else {
            foreach ($this->exec_body['condition_group_list'] as $cg_key => $condition_group) {
                if (!($condition_group['condition_list'] ?? [])) {
                    throw new AppException('条件为空');
                } else {
                    foreach ($condition_group['condition_list'] as $c_key => $condition) {
                        if (
                            empty($condition['condition_target']) ||
                            empty($condition['condition_target_action']) ||
                            empty($condition['condition_target_dim']) ||
                            (empty($condition['condition_target_value'] ?? '') && !is_numeric($condition['condition_target_value']))
                        ) {
                            throw new AppException('条件组' . ($cg_key + 1) . '的条件' . ($c_key + 1) . '的条件项有空值');
                        }

                        if (
                            (empty($condition['time_range'] ?? '') && !is_numeric($condition['time_range'] ?? '')) ||
                            empty($condition['time_range_type']) ||
                            empty($condition['time_range_unit'])
                        ) {
                            throw new AppException('条件组' . ($cg_key + 1) . '的条件' . ($c_key + 1) . '的数据时间为空');
                        }
                    }
                }
            }
        }

        if (!$this->success_action && !$this->fail_action) {
            throw new AppException('通过事件和不符事件需要有一个不为空');
        } else {
            foreach ($this->success_action as $sa_key => $success_action) {
                if (empty($success_action['target']) || empty($success_action['target_value'])) {
                    throw new AppException('通过事件' . ($sa_key + 1) . '有空值');
                }
            }
            foreach ($this->fail_action as $fa_key => $fail_action) {
                if (empty($success_action['target']) || empty($success_action['target_value'])) {
                    throw new AppException('不符事件' . ($fa_key + 1) . '有空值');
                }
            }
        }
    }

    public function initBySql($object)
    {
        $this->name = $object->name;
        $this->media_type = $object->media_type;
        $this->media_agent_type = $object->media_agent_type;
        $this->exec_target_dim = $object->exec_target_dim;
        $this->exec_body = json_decode($object->exec_body, true);
        $this->success_action = json_decode($object->success_action, true);
        $this->fail_action = json_decode($object->fail_action, true);
        $this->creator = $object->creator;
        $this->status = $object->status;
        return $this;
    }

    public function toArray()
    {
        $this->validate();

        $data = parent::toArray();
        if (!$data['creator']) {
            $data['creator'] = Container::getSession()->name;
        }
        $data['exec_body'] = json_encode($data["exec_body"], JSON_UNESCAPED_UNICODE);
        $data['success_action'] = json_encode($data["success_action"], JSON_UNESCAPED_UNICODE);
        $data['fail_action'] = json_encode($data["fail_action"], JSON_UNESCAPED_UNICODE);
        return $data;
    }

}
