<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Kuaishou;

use App\Exception\AppException;
use App\Param\ADServing\AbstractADTargetingContentParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\Kuaishou\TargetPacketParam;

class ADTargetingContentParam extends AbstractADTargetingContentParam
{
    public $auto_target = '0';

    public $auto_population = 0;
    public $intention_target = 'false';
    public $region_none = 'buxian';
    public $region_map = [];
    public $region = [];
    public $age_none = 'buxian';
    public $age = [
        'min' => '',
        'max' => ''
    ];
    public $ages_range;
    public $gender = '0';
    public $android_osv = '';
    public $ios_osv = '';
    public $network = '';
    public $device_brand_none = 'buxian';
    public $device_brand = [];
    public $device_price_none = 'buxian';
    public $device_price = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];
    public $business_interest_type = '0';
    public $business_interest_map = [];
    public $business_interest = [];
    public $fans_star_none = 'buxian';
    public $fans_star = [];
    public $fans_star_map = [];
    public $interest_video_none = 'buxian';
    public $interest_video_map = [];
    public $interest_video = [];
    public $app_interest_none = 'buxian';
    public $app_interest_map = [];
    public $app_interest = [];
    public $app_interest_ids = [];
    public $zidingyirenqun = 'buxian';
    public $seedrenqun = 'buxian';
    public $population_map = [];
    public $population = [];
    public $exclude_population_map = [];
    public $exclude_population = [];
    public $seed_population_map = [];
    public $seed_population = [];
    public $intelli_extend = [
        'is_open' => '0',
        'no_age_break' => '0',
        'no_gender_break' => '0',
        'no_area_break' => '0'
    ];
    public $filter_converted_level = 0;
    public $behavior_interest_none = 'buxian';
    public $behavior_interest = [
        'behavior' => [
            'keyword' => [],
            'label' => [],
            'label_map' => [],
            'time_type' => 0,
            'strength_type' => 0,
            'scene_type' => [1]
        ],
        'interest' => [
            'label' => [],
            'label_map' => [],
            'strength_type' => 0,
        ]
    ];
    public $behavior_interest_param_show_none = 'buxian';
    public $behavior_interest_param_show = [
        'keyword_list' => [],
        'category_list' => [],
        'custom_behavior' => 0,
        'scene_type' => [],
        'time_type' => 0,
    ];

    public $feed_search_keyword_list = [];
    public $extend_search = 0;
    public $quick_search = 0; // 搜索广告(原搜索快投) 0 关闭 1开启
    public $quick_search_ext = 0; //开启搜索广告  0关闭 1开启
    public $target_explore = 0;
    public $search_keyword_type = '';

    // 否词
    public $feed_search_noword_list = [];
    public $open_noword_search = 0;

    /**
     * @var int $platform_os 操作系统
     */
    public $platform_os = 0;

    public function __construct($property = [])
    {
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === null) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    public function toMd5()
    {
        $md5_array = $this->toArray();
        unset($md5_array['zidingyirenqun']);
        unset($md5_array['region_none']);
        unset($md5_array['age_none']);
        unset($md5_array['device_brand_none']);
        unset($md5_array['device_price_none']);
        unset($md5_array['fans_star_none']);
        unset($md5_array['interest_video_none']);
        unset($md5_array['app_interest_none']);

        unset($md5_array['md5']);
        unset($md5_array['region_map']);
        unset($md5_array['business_interest_map']);
        unset($md5_array['fans_star_map']);
        unset($md5_array['interest_video_map']);
        unset($md5_array['app_interest_map']);
        unset($md5_array['population_map']);
        unset($md5_array['exclude_population_map']);
        ksort($md5_array);
        return md5(json_encode($md5_array, JSON_NUMERIC_CHECK));
    }

    /**
     * 参数格式化
     * @param $state
     */
    public function format($state)
    {
        // 旧定向包种子人群字段切换
        if($this->zidingyirenqun == 'seed') {
            $this->zidingyirenqun = 'buxian';
            $this->seedrenqun = 'seed';
        }

        if($this->behavior_interest_param_show_none == 'zidingyi') {
            // 如果使用新版行为意向，则关闭旧版
            $this->behavior_interest_none = 'buxian';
        }

        if ($this->region_map) {
            $regions_id_list = array_column(
                $this->region_map,
                'id'
            );
            $this->region = $regions_id_list;
            sort($this->region);
        } else {
            if ($this->region) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->region);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->region = [];
                }
            } else {
                $this->region = [];
                $this->region_map = [];
            }
        }

        if ($this->ages_range) {
            sort($this->ages_range);
        }

        ksort($this->age);

        if ($this->device_brand) {
            sort($this->device_brand);
        }

        if ($this->device_price) {
            sort($this->device_price);
        }

        if ($this->business_interest_map) {
            $this->business_interest_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->business_interest_map);
            $business_interest_id_list = array_column(
                $this->business_interest_map,
                'id'
            );
            $this->business_interest = $business_interest_id_list;
            sort($this->business_interest);
        } else {
            if ($this->business_interest) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->business_interest);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->business_interest = [];
                }
            } else {
                $this->business_interest = [];
                $this->business_interest_map = [];
            }
        }

        if ($this->app_interest_map) {
            $this->app_interest_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->app_interest_map);
            $app_interest_id_list = array_column(
                $this->app_interest_map,
                'id'
            );
            $this->app_interest_ids = $app_interest_id_list;
            sort($this->app_interest_ids);
        } else {
            if ($this->app_interest_ids) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->app_interest_ids);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->app_interest_ids = [];
                }
            } else {
                $this->app_interest_ids = [];
                $this->app_interest_map = [];
            }
        }

        if ($this->behavior_interest_none == 'zidingyi') {
            if ($this->behavior_interest['behavior']['label_map']) {
                $id_list = array_column(
                    $this->behavior_interest['behavior']['label_map'],
                    'id'
                );
                $this->behavior_interest['behavior']['label'] = $id_list;
                sort($this->behavior_interest['behavior']['label']);
            } else {
                $this->behavior_interest['behavior']['label_map'] = [];
                $this->behavior_interest['behavior']['label'] = [];
            }

            if (!($this->behavior_interest['behavior']['scene_type'] ?? false)) {
                $this->behavior_interest['behavior']['scene_type'] = [1];
            }

            if ($this->behavior_interest['interest']['label_map']) {
                $id_list = array_column(
                    $this->behavior_interest['interest']['label_map'],
                    'id'
                );
                $this->behavior_interest['interest']['label'] = $id_list;
                sort($this->behavior_interest['interest']['label']);
            } else {
                $this->behavior_interest['interest']['label_map'] = [];
                $this->behavior_interest['interest']['label'] = [];
            }
        } else {
            $this->behavior_interest = [
                'behavior' => [
                    'keyword' => [],
                    'label' => [],
                    'label_map' => [],
                    'time_type' => 0,
                    'strength_type' => 0,
                    'scene_type' => [1]
                ],
                'interest' => [
                    'label' => [],
                    'label_map' => [],
                    'strength_type' => 0,
                ]
            ];
        }


        if ($this->population_map) {
            $this->population_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->population_map);
            $population_id_list = array_column(
                $this->population_map,
                'orientation_id'
            );
            $this->population = $population_id_list;
            sort($this->population);
        } else {
            if ($this->population) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->population);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->population = [];
                }
            } else {
                $this->population = [];
                $this->population_map = [];
            }
        }

        if ($this->exclude_population_map) {
            $this->exclude_population_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->exclude_population_map);
            $exclude_population_id_list = array_column(
                $this->exclude_population_map,
                'orientation_id'
            );
            $this->exclude_population = $exclude_population_id_list;
            sort($this->exclude_population);
        } else {
            if ($this->exclude_population) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->exclude_population);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->exclude_population = [];
                }
            } else {
                $this->exclude_population = [];
                $this->exclude_population_map = [];
            }
        }

        if ($this->seed_population_map) {
            $this->seed_population_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->seed_population_map);
            $seed_population_id_list = array_column(
                $this->seed_population_map,
                'orientation_id'
            );
            $this->seed_population = $seed_population_id_list;
            sort($this->seed_population);
        } else {
            if ($this->seed_population) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->seed_population);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->seed_population = [];
                }
            } else {
                $this->seed_population = [];
                $this->seed_population_map = [];
            }
        }

        ksort($this->intelli_extend);

        if ($this->region_none && $this->region_none == 'buxian') {
            $this->region_map = [];
            $this->region = [];
        }

        if ($this->age_none && $this->age_none == 'buxian') {
            $this->ages_range = [];
            $this->age = [
                'min' => '',
                'max' => ''
            ];
        }
        if ($this->age_none && $this->age_none == 'zidingyi') {
            $this->age = [
                'min' => '',
                'max' => ''
            ];
        }

        if ($this->age_none && $this->age_none == 'custom') {
            $this->ages_range = [];
        }

        if ($this->device_brand_none && $this->device_brand_none == 'buxian') {
            $this->device_brand = [];
        }

        if ($this->device_price_none && $this->device_price_none == 'buxian') {
            $this->device_price = [];
        }

        if ($this->fans_star_none && $this->fans_star_none == 'buxian') {
            $this->fans_star = [];
            $this->fans_star_map = [];
        }

        if ($this->interest_video_none && $this->interest_video_none == 'buxian') {
            $this->interest_video = [];
            $this->interest_video_map = [];
        }

        if ($this->app_interest_none && $this->app_interest_none == 'buxian') {
            $this->app_interest = [];
            $this->app_interest_ids = [];
            $this->app_interest_map = [];
        }

        if ($this->zidingyirenqun) {
            if ($this->zidingyirenqun !== 'zidingyi') {
                $this->exclude_population_map = [];
                $this->exclude_population = [];
                $this->population_map = [];
                $this->population = [];
            }
        }

        if ($this->seedrenqun !== 'seed') {
            $this->seed_population_map = [];
            $this->seed_population = [];
        }

        if ($this->intelli_extend && (int)$this->intelli_extend == 0) {
            $this->intelli_extend = [
                'is_open' => '0',
                'no_age_break' => '1',
                'no_gender_break' => '1',
                'no_area_break' => '1'
            ];
        }

    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if ($this->behavior_interest_param_show_none === 'zidingyi') {
            if ($this->behavior_interest_param_show['custom_behavior'] === 1) {
                if(empty($this->behavior_interest_param_show['scene_type'])) {
                    $error_msg_list[] = [
                        'key' => 'behavior_interest_param_show_none',
                        'value' => 0,
                        'msg' => "行为场景至少选择一个"
                    ];
                }
                if(!is_numeric($this->behavior_interest_param_show['time_type'])) {
                    $error_msg_list[] = [
                        'key' => 'behavior_interest_param_show_none',
                        'value' => 0,
                        'msg' => "时间范围不可为空"
                    ];
                }
                if(!$this->behavior_interest_param_show['category_list'] && !$this->behavior_interest_param_show['keyword_list']){
                    $error_msg_list[] = [
                        'key' => 'behavior_interest_param_show_none',
                        'value' => 0,
                        'msg' => "关键词和类目词不可为空"
                    ];
                }
            }
        }

        if ($this->auto_target == 1) {
            if ($this->intelli_extend['is_open'] != 1) {
                $error_msg_list[] = [
                    'key' => 'is_open',
                    'value' => 0,
                    'msg' => "开启智能定向时，必须选择打开自动扩量"
                ];
            } else {
                if ((($this->age['min'] ?? '' || $this->age['max'] ?? '') || $this->ages_range) && $this->intelli_extend['no_age_break'] != 1) {
                    $error_msg_list[] = [
                        'key' => 'no_age_break',
                        'value' => 0,
                        'msg' => "开启智能定向时，有年龄限制则必须选择不可突破年龄"
                    ];
                }
                if ($this->region && $this->intelli_extend['no_area_break'] != 1) {
                    $error_msg_list[] = [
                        'key' => 'no_area_break',
                        'value' => 0,
                        'msg' => "开启智能定向时，有地域限制则必须选择不可突破地域"
                    ];
                }
                if ($this->gender && $this->intelli_extend['no_gender_break'] != 1) {
                    $error_msg_list[] = [
                        'key' => 'no_gender_break',
                        'value' => 0,
                        'msg' => "开启智能定向时，有性别限制则必须选择不可突破性别"
                    ];
                }
            }
        }

        if ($this->auto_target == 1 && $this->intention_target === 'true') {
            $error_msg_list[] = [
                'key' => 'intention_target',
                'value' => 0,
                'msg' => "智能定向和行为意向系统优选不能同时开启"
            ];
        }

        if ($this->behavior_interest_none == 'zidingyi') {
            if (!$this->behavior_interest['behavior']['keyword'] &&
                !$this->behavior_interest['behavior']['label'] &&
                !$this->behavior_interest['interest']['label']
            ) {
                $error_msg_list[] = [
                    'key' => 'behavior_interest_none',
                    'value' => 0,
                    'msg' => "选择自定义时，行为兴趣内容不能都为空"
                ];
            }
        }

        return $error_msg_list;
    }

    /**
     * 生成定向给mq的数据
     * @return AbstractADTargetingContentParam
     */
    public function toMQData()
    {
        $this->region_map = [];
        $this->business_interest_map = [];
        $this->fans_star_map = [];
        $this->interest_video_map = [];
        $this->app_interest_map = [];
        $this->population_map = [];
        $this->exclude_population_map = [];
        return $this;
    }

    /**
     * 获取所有人群包id
     * @return array
     */
    public function getAllAudienceIdList()
    {
        return array_merge(
            $this->population ?: [],
            $this->exclude_population ?: [],
            $this->seed_population ?: []
        );
    }

    /**
     * 获取所有流量包id
     * @return array
     */
    public function getAllFlowIdMd5MapList()
    {
        return [];
    }

    /**
     * 重置流量包
     * @param array $flow_package_map
     * @return mixed
     */
    public function resetFlowPackage(array $flow_package_map)
    {
        return [];
    }

    /**
     * 获取媒体接口格式的定向包内容数组
     * @return array
     */
    public function getMediaFormatTargetingArray()
    {
        $targeting_data = $this->toArray();
        if ($this->intention_target) {
            $targeting_data['behavior_type'] = $this->intention_target === 'true' ? 0 : ($this->behavior_interest_none == 'buxian' ? 0 : 1);
        }
        $targeting_data['app_interest_ids'] = $this->app_interest;
        $targeting_data['intelli_extend_option'] = $this->auto_target;
        $media_targeting_pa = new TargetPacketParam($targeting_data);
        return $media_targeting_pa->toData();
    }
}
