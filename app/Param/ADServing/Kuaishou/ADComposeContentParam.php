<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Kuaishou;

use App\Constant\BatchAD;
use App\Constant\KuaishouEnum;
use App\Exception\AppException;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Param\ADServing\AbstractADComposeContentParam;
use App\Param\ADServing\Kuaishou\Project\ADOtherSettingContentParam;
use App\Utils\Helpers;
use function AlibabaCloud\Client\json;

class ADComposeContentParam extends AbstractADComposeContentParam
{

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

//        // 销售线索
//        if ((int)$this->site_config->plat_id === 7) {
//            if ($other_setting->campaign_type != KuaishouEnum::TYPE_CLUES) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                    'key' => 'campaign_type',
//                    'value' => $other_setting->campaign_type,
//                    'msg' => '小游戏需要使用收集销售线索营销目的'
//                ];
//            }
//        }

        if (in_array($other_setting->campaign_type,[KuaishouEnum::TYPE_APP, KuaishouEnum::TYPE_CLUES, KuaishouEnum::TYPE_WECHAT_MINI_GAME])) {

            if ((int)$other_setting->page_mode === 1 && !$other_setting->page_url) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'page_url',
                    'value' => $other_setting->page_url,
                    'msg' => '请输入落地页地址'
                ];
            }
            if ((int)$other_setting->page_mode === 2) {
                foreach ($this->account_list->account_list as $key => $account_info) {
                    $page_id = $other_setting->getPageInfo($account_info['account_id'])->id ?? '';
                    if ($page_id) continue;
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'page_map',
                        'value' => $other_setting->page_map,
                        'msg' => "请选择{$account_info['account_id']}对应的落地页信息"
                    ];
                }
                if ($other_setting->page_map) {
                    foreach ($other_setting->page_map as $page_map_value) {
                        $game_name = json_decode($page_map_value, true);
                        if ($game_name['game_name'] ?? false) {
                            $game_down_exist = (new V2DimGameIdModel())->getGameIdByPlatformAndAppName($this->platform, $this->site_config->game_id);
                            if ($game_down_exist) {
                                if ($game_down_exist->app_name != $game_name['game_name']) {
                                    $error_msg_list[] = [
                                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                        'key' => 'page_map',
                                        'value' => $other_setting->page_map,
                                        'msg' => "请选择{$this->site_config->game_id}-{$game_name['game_name']}对应的落地页信息"
                                    ];
                                }
                            } else {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                    'key' => 'page_map',
                                    'value' => $other_setting->page_map,
                                    'msg' => "请刷新页面后在选择落地页"
                                ];
                            }
                            break;
                        }
                    }
                }
            }
            if ((int)$other_setting->page_mode === 4) {
                if (!($this->site_config->sdk_ext['mini_game_original_id'] ?? '')) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'page_mode',
                        'value' => $other_setting->page_mode,
                        'msg' => "没有配置微信小程序调起ID，无法选择调起小程序"
                    ];
                }
            }
        }

        foreach ($this->material_list->all_video_list as $video) {
            foreach ($video['cover_list'] as $cover) {
                if ($video['width'] != $cover['width'] || $video['height'] != $cover['height']) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => "{$video['url']}封面与视频的尺寸不一致"
                    ];
                }
            }
        }

        foreach ($this->word_list->getWordContent() as $word) {
            $len = Helpers::ADServingStrLen($word);
            if ($len > 60) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'value' => $word,
                    'msg' => "{$word},此文案为{$len}个字符，大于30个字符,不符合规范"
                ];
            }
        }

        foreach ($this->material_list->all_video_list as $video_info) {
//            if (((int)$video_info['size'] / (1024 * 1024)) > 100) {
//                $error_msg_list[] = [
//                    'type' => 'material',
//                    'key' => 'material',
//                    'value' => 0,
//                    'msg' => "{$video_info['url']}不符合视频小于100MB的规范"
//                ];
//            }

            // 判定是否5-600s
            if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 600)) {
                $error_msg_list[] = [
                    'type' => 'material',
                    'key' => 'material',
                    'value' => 0,
                    'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~600s的规范"
                ];
            }
        }

        if (!$other_setting->action_bar_text) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'action_bar_text',
                'value' => $other_setting->action_bar_text,
                'msg' => "行动号召文案不能为空"
            ];
        }

        return $error_msg_list;
    }

    /**
     * 判断广告1级名字
     * @param int $num
     * @param string $material_name
     * @param string $targeting_name
     * @return string
     */
    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD1Name() method.
    }

    /**
     * 判断广告2级名字
     * @param int $num
     * @param string $material_name
     * @param string $targeting_name
     * @return string
     */
    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD2Name() method.
    }

    public function getMaterialFileNormalList(): array
    {
        return [];
    }
}
