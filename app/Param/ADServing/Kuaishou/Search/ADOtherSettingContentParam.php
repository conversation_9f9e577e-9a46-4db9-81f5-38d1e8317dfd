<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Kuaishou\Search;

use App\Constant\BatchAD;
use App\Constant\KuaishouEnum;
use App\Exception\AppException;
use App\Param\ADServing\Kuaishou\ADOtherSettingContentParam as KuaishouADOtherSettingContentParam;

class ADOtherSettingContentParam extends KuaishouADOtherSettingContentParam
{
    /**
     * @var int $site_type_mode 投放类型
     */
    public $site_type_mode = 0;

    /**
     * @var int $site_type 投放类型
     */
    public $site_type = 0;


    /**
     * @var int 高级创意开关
     */
    public $adv_card_option = 0;

    /**
     * @var string 图片卡片
     */
    public $adv_card_url_200 = '';

    public $sub_title = '';

    public $title = '';

    public function paramHook()
    {
        parent::paramHook();
        $this->page_map = $this->tranObject($this->page_map);

        if ($this->campaign_type != KuaishouEnum::TYPE_CLUES) {
            $this->site_type_mode = 0;
        }

    }

    public function validate()
    {
        $error_msg_list = parent::validate();

        if ($this->adv_card_option == 1) {
            if (!$this->adv_card_url_200) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'adv_card_url_200',
                    'value' => $this->adv_card_url_200,
                    'msg' => "请选择图片"
                ];
            }
            $title_len = mb_strlen($this->title);
            $sub_title_len = mb_strlen($this->sub_title);
            if ($title_len < 1 || $title_len > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'title',
                    'value' => $this->title,
                    'msg' => "标题1-10字"
                ];
            }
            if ($sub_title_len < 6 || $sub_title_len > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'sub_title',
                    'value' => $this->sub_title,
                    'msg' => "副标题6-10字"
                ];
            }
        }


        return $error_msg_list;
    }

    /**
     * 获取达人视频
     * @param $account_id
     * @return array|mixed
     */
    public function getNativeMaterialByAccountID($account_id)
    {
        if ($this->native_material_map[$account_id] ?? '') {
            return $this->native_material_map[$account_id];
        }
        return [];
    }

}
