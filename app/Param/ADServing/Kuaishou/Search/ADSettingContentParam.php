<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Kuaishou\Search;

use App\Constant\BatchAD;
use App\Param\ADServing\Kuaishou\ADSettingContentParam as KuaishouADSettingContentParam;

class ADSettingContentParam extends KuaishouADSettingContentParam
{
    public $campaign_day_budget_mode = 'buxian';
    public $campaign_day_budget = 0;
    public $bid_type = '6';
    public $ocpx_action_type = '2';
    public $cpa_bid = [];
    public $deep_conversion_type = '';
    public $deep_conversion_bid = [];
    public $roi_ratio = [];
    public $scene_id_mode = 'default';
    public $scene_id = ['1'];
    public $cover_num = 4;
    public $lp_url = '';
    public $lp_url_content = '';
    public $ad_day_budget_mode = 'buxian';
    public $ad_day_budget = '0';
    public $speed = '1';
    public $show_mode = 1;
    public $web_uri_type = 1;
    public $ad_code_type = 'common';
    public $target_action_type = '30';
    public $code = '';
    public $creative_category = 0;
    public $creative_tag = [];
    public $new_expose_tag = [];
    public $ad_operation = 'disable';
    public $bid_scene = '0';

    /**
     * @var array $gift_data 游戏礼包码
     */
    public $gift_data = [];

    /**
     * @var int $conversion_type 转化途径
     */
    public $conversion_type = 0;


    /**
     * @var bool $use_app_market 使用开启应用商店直投
     */
    public $use_app_market = false;

    /**
     * @var array $app_store 应用商店直投列表
     */
    public $app_store = [];


    /**
     * @var array $captions 作品广告语集合
     */
    public $captions = [];

    /**
     * @var string $click_url 第三方点击检测链接
     */
    public $click_url = '';

    /**
     * @var array $schedule 投放排期
     */
    public $schedule = [];

    /**
     * @var int $day_budget 每日预算
     */
    public $day_budget = 0;

    /**
     * @var bool $asset_mining 素材挖掘
     */
    public $asset_mining = false;

    /**
     * @var array $ad_dsp_target
     */
    public $ad_dsp_target = [];

    /**
     * @var bool $smart_cover 抽帧
     */
    public $smart_cover = false;

    /**
     * @var bool $rule_enable 是否开启命名规则
     */
    public $rule_enable = false;


    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = parent::validate();
        if ($this->campaign_day_budget < 500 && $this->campaign_day_budget_mode == 'zidingyi') {
            $error_msg_list[] = [
                'key' => 'campaign_day_budget',
                'value' => $this->campaign_day_budget,
                'msg' => "广告计划单日预算(campaign_day_budget)不能低于500"
            ];
        }

        if ($this->bid_scene == 1 && !$this->campaign_day_budget) {
            $error_msg_list[] = [
                'type' => 'setting',
                'key' => 'campaign_day_budget_mode',
                'value' => $this->campaign_day_budget_mode,
                'msg' => "最大转化广告计划单日预算(campaign_day_budget)不能为0"
            ];
        }

        if ($this->ad_day_budget_mode == 'zidingyi' && $this->ad_day_budget) {
            foreach ($this->cpa_bid as $cpa_bid_value) {
                if ($cpa_bid_value > $this->ad_day_budget) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "广告组出价(cpa_bid)不能大于广告组出价"
                    ];
                    break;
                }
            }
        }
        if ($this->cpa_bid){
            if ($this->ocpx_action_type == 2){
                if($this->cpa_bid[0] > 10 || $this->cpa_bid[1] > 10){
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "当优化目标为行为数时,转化出价不能大于10且小于 0.1"
                    ];
                }
            }

            if ($this->ocpx_action_type == 2){
                if($this->cpa_bid[0] > 10 || $this->cpa_bid[1] > 10){
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "当优化目标为行为数时,转化出价不能大于10且小于 0.1"
                    ];
                }
            }

            if ($this->ocpx_action_type == 180){
                if($this->cpa_bid[0] > 1000 || $this->cpa_bid[1] > 1000){
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "当优化目标为激活时,转化出价不能大于1000且小于1"
                    ];
                }
            }

            if ($this->ocpx_action_type == 190){
                if($this->cpa_bid[0] > 10000 || $this->cpa_bid[1] > 10000 || $this->cpa_bid[0] < 5){
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "当优化目标为付费时,转化出价不能大于10000且小于5"
                    ];
                }
            }

            if ($this->ocpx_action_type == 191){
                if( $this->cpa_bid[1] > 100){
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "当优化目标为首日ROI时,转化出价不能大于100"
                    ];
                }
            }
        }


        if ($this->cpa_bid && $this->deep_conversion_bid && $this->deep_conversion_type == 180) {
            foreach ($this->cpa_bid as $cpa_bid_value) {
                foreach ($this->deep_conversion_bid as $deep_conversion_bid_value) {
                    if ($cpa_bid_value > $deep_conversion_bid_value) {
                        $error_msg_list[] = [
                            'key' => 'cpa_bid',
                            'value' => $this->cpa_bid,
                            'msg' => "广告组出价(cpa_bid)不能大于广告组深度出价(deep_conversion_bid)"
                        ];
                    }
                }
            }
        }

        if ($this->ad_day_budget_mode == 'zidingyi' && !$this->ad_day_budget) {
            $error_msg_list[] = [
                'key' => 'ad_day_budget',
                'value' => $this->ad_day_budget,
                'msg' => "广告单日预算(ad_day_budget)不能为空"
            ];
        }

        if ($this->speed == 3 && in_array(5, $this->scene_id)) {
            $error_msg_list[] = [
                'key' => 'scene_id',
                'value' => $this->scene_id,
                'msg' => '联盟广告不支持优先低成本(speed)'
            ];
        }

        if (count($this->creative_tag) > 10) {
            $error_msg_list[] = [
                'key' => 'creative_tag',
                'value' => $this->creative_tag,
                'msg' => '创意标签最多10个'
            ];
        }

        if ($this->creative_tag) {
            foreach ($this->creative_tag as $tag) {
                if (strpos($tag, '333') !== false) {
                    $error_msg_list[] = [
                        'key' => 'creative_tag',
                        'value' => $this->creative_tag,
                        'msg' => "标签包词:{$tag}包含333字眼，请修改标签包"
                    ];
                    break;
                }
                if (mb_strlen($tag) > 10) {
                    $error_msg_list[] = [
                        'key' => 'creative_tag',
                        'value' => $this->creative_tag,
                        'msg' => "标签包词(ad_keywords):{$tag}已经大于10个字"
                    ];
                }
            }
        }

        if ($this->deep_conversion_type == 3 && !$this->deep_conversion_bid && $this->bid_scene == '0') {
            $error_msg_list[] = [
                'key' => 'deep_conversion_bid',
                'value' => $this->deep_conversion_bid,
                'msg' => '深度优化出价(deep_conversion_bid)不能为空'
            ];
        }

        if (in_array($this->ocpx_action_type, [191]) && !$this->roi_ratio && $this->bid_scene == '0') {
            $error_msg_list[] = [
                'key' => 'roi_ratio',
                'value' => $this->roi_ratio,
                'msg' => 'ROI系数(roi_ratio)不能为空,且不能好过100,最大支持小数点3位'
            ];
        }

        if ($this->deep_conversion_type == 92 && $this->bid_scene == '0') {
            if (!$this->roi_ratio) {
                $error_msg_list[] = [
                    'key' => 'roi_ratio',
                    'value' => $this->roi_ratio,
                    'msg' => '深度转化付费ROI系数(roi_ratio)不能为空'
                ];
            } else {
                foreach ($this->roi_ratio as $roi_xs) {
                    if ($roi_xs > 1) {
                        $error_msg_list[] = [
                            'key' => 'roi_ratio',
                            'value' => $this->roi_ratio,
                            'msg' => '深度转化付费ROI系数(roi_ratio)不能大于0'
                        ];
                    }
                }
            }
        }

        if ($this->ad_code_type == 'gift' && !$this->code) {
            $error_msg_list[] = [
                'key' => 'code',
                'value' => $this->code,
                'msg' => '礼包码广告需要填写礼包码'
            ];
        }

        return $error_msg_list;
    }

    public function format()
    {
        parent::format();

        if (!$this->ad_day_budget) {
            $this->ad_day_budget_mode = 'buxian';
        } else {
            $this->ad_day_budget_mode = 'zidingyi';
        }

        if (!$this->campaign_day_budget) {
            $this->campaign_day_budget = 0;
        } else {
            $this->campaign_day_budget_mode = 'zidingyi';
        }

        // 创意标签与创意分类参数，要么都传，要么都不传
        if (!$this->creative_category || !$this->creative_tag) {
            $this->creative_category = 0;
            $this->creative_tag = [];
        }
    }

}
