<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Kuaishou\Search;

use App\Constant\BatchAD;
use App\Constant\KuaishouEnum;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Logic\DSP\ADServingLogic;
use App\Model\SqlModel\Tanwan\V2DimGameStandardValueDaysIncolumnModel;
use App\Param\ADServing\Kuaishou\ADComposeContentParam as KuaishouADComposeContentParam;
use App\Param\ADServing\Kuaishou\ADTargetingContentParam;
use App\Param\ADServing\Kuaishou\Project\ADSettingContentParam;

class ADComposeContentParam extends KuaishouADComposeContentParam
{
    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = parent::unionParamValidateJudge();

        /** @var ADOtherSettingContentParam $other_setting */
        $other_setting = $this->other_setting;

        // 线索
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            // 预约广告
            if((int)$other_setting->site_type_mode === 1) {
                if($this->site_config->plat_id == PlatId::MINI) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'site_type_mode',
                        'value' => $other_setting->site_type_mode,
                        'msg' => "目前预约广告不支持小游戏"
                    ];
                }
            }
        }

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            /* @var ADSettingContentParam $setting */

            if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
                // 预约广告
                if ((int)$other_setting->site_type_mode === 1) {
                    if (!in_array($setting->ocpx_action_type, [634, 635])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'key' => 'ocpx_action_type',
                            'value' => 0,
                            'source_id' => $setting_id,
                            'msg' => "预约广告需要使用预约表单或者预约点击跳转"
                        ];
                    }
                }
            }

            if ($this->site_config->plat_id != PlatId::MINI) {
                if (in_array($setting->ocpx_action_type, [937])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'ocpx_action_type',
                        'value' => 0,
                        'source_id' => $setting_id,
                        'msg' => "非小游戏不可选择小程序ROI"
                    ];
                }
                if (in_array($setting->ocpx_action_type, [180, 190]) && in_array($setting->deep_conversion_type, [179])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'deep_conversion_type',
                        'value' => 0,
                        'source_id' => $setting_id,
                        'msg' => "非小游戏不可选择小程序ROI"
                    ];
                }
            }

            if (in_array($setting->ocpx_action_type, [634, 635]) && ($other_setting->campaign_type != KuaishouEnum::TYPE_CLUES || (int)$other_setting->site_type_mode !== 1)) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'ocpx_action_type',
                    'value' => 0,
                    'source_id' => $setting_id,
                    'msg' => "预约表单或者预约点击跳转仅支持预约广告类型"
                ];
            }


            //开屏广告位自定义时间(end_time - begin_time)必须超过三天
            if (in_array(27, $other_setting->scene_id) && $setting->end_time - $setting->begin_time < 86400) {
                throw new AppException('开屏广告位自定义时间必须超过三天');
            }

            foreach ($this->word_list->getWordContent() as $word) {
                if (in_array(5, $setting->scene_id) && strpos($word, '[') !== false && strpos($word, ']') !== false) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'key' => $word,
                        'value' => $word,
                        'msg' => "文案:{$word},有动态词包,不符合联盟广告不能有动态词包的规范"
                    ];
                }
            }


            //校验roi的情况
            if (!$this->compose_config->ignore_warning && ($setting->deep_conversion_type == KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI || in_array($setting->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY, KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY]))) {
                $roi_standard_value = (new V2DimGameStandardValueDaysIncolumnModel())->getRoiStandardValue([
                    'platform' => $this->platform,
                    'game_id' => $this->site_config->game_id
                ]);

                //校验首日roi
                if ($setting->deep_conversion_type == KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI || $setting->ocpx_action_type == KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY) {
                    $judge_roi_result = (new ADServingLogic())->judgeRoiValue($setting->roi_ratio, $roi_standard_value, 1);
                    $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                        $ele['source_id'] = $setting_id;
                        return $ele;
                    }, $judge_roi_result);
                    $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
                }

                //校验7天roi
                if ($setting->ocpx_action_type == KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY) {
                    $judge_roi_result = (new ADServingLogic())->judgeRoiValue($setting->roi_ratio, $roi_standard_value, 7);
                    $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                        $ele['source_id'] = $setting_id;
                        return $ele;
                    }, $judge_roi_result);
                    $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
                }

            }
        }

        foreach ($this->targeting_list->getContentList() as $target_id => $targeting) {
            /** @var ADTargetingContentParam $targeting */
            //旧的App行为兴趣，直接报错
            foreach ($targeting->app_interest_ids as $app_interest_ids) {
                if ($app_interest_ids < 1000) {
                    $error_msg_list[] = [
                        'source_id' => $target_id,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'key' => 'app_interest_ids',
                        'value' => $targeting->app_interest_ids,
                        'msg' => "旧版APP行为兴趣已废弃，请重新选择APP行为兴趣"
                    ];
                    break;
                }
            }
            if ($targeting->filter_converted_level == KuaishouEnum::FILTER_CONVERTED_LEVEL_APP) {
                if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
                    $error_msg_list[] = [
                        'source_id' => $target_id,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'key' => 'filter_converted_level',
                        'value' => $targeting->filter_converted_level,
                        'msg' => "销售线索不支持过滤APP的选项"
                    ];
                }
            }
        }



        return $error_msg_list;
    }

}
