<?php
/**
 * Created by Ph<PERSON>Storm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Kuaishou\Basics;

use App\Constant\BatchAD;
use App\Constant\KuaishouEnum;
use App\Exception\AppException;
use App\Param\ADServing\Kuaishou\ADOtherSettingContentParam as KuaishouADOtherSettingContentParam;

class ADOtherSettingContentParam extends KuaishouADOtherSettingContentParam
{
    /**
     * @var string $recommendation
     */
    public $recommendation = '';

    /**
     * @var int $auto_manage
     */
    public $auto_manage = 0;

    /**
     * @var array $live_user_id 主播
     */
    public $live_user_id = [];

    /**
     * @var array $jingle_bell_id 小铃铛
     */
    public $jingle_bell_id = [];

    /**
     * @var int $live_creative_type 直播类型 视频引流/直投直播
     */
    public $live_creative_type = KuaishouEnum::LIVE_CREATIVE_TYPE_LIVE;

    /**
     * @var int $site_type_mode 投放类型
     */
    public $site_type_mode = 0;

    /**
     * @var int $site_type 投放类型
     */
    public $site_type = 0;

    /**
     * @var int $outer_loop_native 是否开启原生广告
     */
    public $outer_loop_native = 0;

    /**
     * @var array $native_material_map 达人视频
     */
    public $native_material_map = []; //

    /**
     * @var array $blue_v_material_map 蓝v达人视频
     */
    public $blue_v_material_map = [];

    /**
     * @var int 蓝v视频类型
     */
    public $talent_video_type = 0;

    /**
     * @var array $native_material_type 达人视频模式
     */
    public $native_material_type = 0;

    /**
     * @var array $native_material_user_map 达人视频
     */
    public $native_material_user_map = [];

    /**
     * @var int 高级创意开关
     */
    public $adv_card_option = 0;

    /**
     * @var string 图片卡片
     */
    public $adv_card_url_200 = '';

    public $sub_title = '';

    public $title = '';

    public $native_material_num = '2';

    public $native_material_type_radio = '0';

    public $talent_type = 0;

    public $shield_backward_switch = false;

    public $blue_v_talent_map = [];

    public $blue_v_talent_video_mode = 2;

    public $unit_material_type = 0;

    public function paramHook()
    {
        parent::paramHook();
        $this->live_user_id = $this->tranObject($this->live_user_id);
        $this->jingle_bell_id = $this->tranObject($this->jingle_bell_id);
        $this->page_map = $this->tranObject($this->page_map);
        $this->native_material_map = $this->tranObject($this->native_material_map);
        $this->blue_v_talent_map = $this->tranObject($this->blue_v_talent_map);
        $this->blue_v_material_map = $this->tranObject($this->blue_v_material_map);

        $this->blue_v_talent_video_mode = 2;

        if (!in_array($this->campaign_type, [KuaishouEnum::TYPE_CLUES])) {
            $this->site_type_mode = 0;
        }

        if ($this->campaign_type == KuaishouEnum::TYPE_WECHAT_MINI_GAME) {
            $this->unit_material_type = 4;
        }

        if (in_array($this->campaign_type, [KuaishouEnum::TYPE_APP, KuaishouEnum::TYPE_CLUES, KuaishouEnum::TYPE_WECHAT_MINI_GAME])) {
//            $this->outer_loop_native = 1;
        } else {
            $this->outer_loop_native = 0;
        }

        // 达人视频map转化
        if (($this->outer_loop_native > 0 && $this->native_material_map) && $this->talent_type == KuaishouEnum::JUXING_TALENT) {
            $this->native_material_user_map = [];
            foreach ($this->native_material_map as $account_id => $item) {
                foreach ($item as $info) {
                    $this->native_material_user_map[$account_id][$info['user_id']][] = $info;
                }
            }
        }

        // 蓝v达人视频map转化
        if (($this->outer_loop_native > 0 && $this->blue_v_material_map) && $this->talent_type == KuaishouEnum::BLUE_V_TALENT && $this->talent_video_type == KuaishouEnum::TALENT_MEDIA) {
            $this->native_material_user_map = [];
            foreach ($this->blue_v_material_map as $account_id => $item) {
                foreach ($item as $info) {
                    $this->native_material_user_map[$account_id][$info['user_id']][] = $info;
                }
            }
        }
    }

    public function validate()
    {
        $error_msg_list = parent::validate();

        if ($this->adv_card_option == 1) {
            if (!$this->adv_card_url_200) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'adv_card_url_200',
                    'value' => $this->adv_card_url_200,
                    'msg' => "请选择图片"
                ];
            }
            $title_len = mb_strlen($this->title);
            $sub_title_len = mb_strlen($this->sub_title);
            if ($title_len < 1 || $title_len > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'title',
                    'value' => $this->title,
                    'msg' => "标题1-10字"
                ];
            }
            if ($sub_title_len < 6 || $sub_title_len > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'sub_title',
                    'value' => $this->sub_title,
                    'msg' => "副标题6-10字"
                ];
            }
        }
        // 蓝V媒体素材视频 和 聚星达人素材视频
        if ((($this->talent_video_type == KuaishouEnum::TALENT_MEDIA && $this->talent_type == KuaishouEnum::BLUE_V_TALENT) || $this->talent_type == KuaishouEnum::JUXING_TALENT) && ($this->outer_loop_native > 0 && $this->native_material_user_map)) {
            foreach ($this->native_material_user_map as $account_id => $item) {
                foreach ($item as $user_id => $video_item) {
                    $video_count = ($this->native_material_type == 0 && $this->native_material_type_radio == 1) ? $this->native_material_num : count($video_item);
                    if ($video_count > 15) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'outer_loop_native',
                            'value' => $this->outer_loop_native,
                            'msg' => "账号{$account_id}下所选达人{$user_id}下选择的视频素材不得超过15个"
                        ];
                        break;
                    }
                }

                if ($this->native_material_type == 0) {

                    $all_info = array_reduce($item, function ($previous, $current) {
                        return array_merge($previous, array_values($current));
                    }, []);
                    if (count(array_unique(array_column($all_info, 'kol_user_type'))) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'outer_loop_native',
                            'value' => $this->outer_loop_native,
                            'msg' => "达人多素材模式下，账号{$account_id}下不能同时选择两种达人类型的素材"
                        ];
                        break;
                    }
                }
            }
        }

        return $error_msg_list;
    }

    /**
     * 获取达人视频
     * @param $account_id
     * @param int $talent_type
     * @return array|mixed
     */
    public function getNativeMaterialByAccountID($account_id, int $talent_type = KuaishouEnum::JUXING_TALENT)
    {
        if ($talent_type == KuaishouEnum::JUXING_TALENT) {
            if ($this->native_material_map[$account_id] ?? '') {
                return $this->native_material_map[$account_id];
            }
        } else {
            if ($this->blue_v_material_map[$account_id] ?? '') {
                return $this->blue_v_material_map[$account_id];
            }
        }
        return [];
    }

}
