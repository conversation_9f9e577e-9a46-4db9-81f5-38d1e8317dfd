<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Kuaishou\Basics;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ConvertType;
use App\Constant\KuaishouEnum;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Logic\DSP\ADServingLogic;
use App\Model\SqlModel\Tanwan\V2DimGameStandardValueDaysIncolumnModel;
use App\Param\ADServing\Kuaishou\ADComposeContentParam as Ku<PERSON>houADComposeContentParam;
use App\Param\ADServing\Kuaishou\ADTargetingContentParam;
use App\Service\GamePriceRangeFilterService;

class ADComposeContentParam extends KuaishouADComposeContentParam
{
    const DEEP_BID_TYPE_MAP = [
        '92' => 'ROI_COEFFICIENT',
        '3' => 'DEEP_BID_TYPE_PAY',
        '7' => 'DEEP_BID_TYPE_NEXT_DAY_OPEN',
        '10' => 'DEEP_BID_TYPE_LOAN_COMPLETION',
        '11' => 'DEEP_BID_TYPE_LOAN_CREDIT',
        '13' => 'DEEP_BID_TYPE_ECOMMERCE_CART',
        '14' => 'DEEP_BID_TYPE_ORDER_SUBMIT',
        '15' => 'DEEP_BID_TYPE_SHOPPING',
        '44' => 'DEEP_BID_TYPE_PAGE_CONFIRM_EFFECTIVE_LEADS',
        '181' => 'DEEP_BID_TYPE_REGISTER_TWENTY_FOUR_HOUR_NEXT_DAY_OPEN',
    ];

    public function getAgentGroup($other_setting)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        if ((int)$other_setting->campaign_type === 16) {
            return AgentGroup::KUAISHOU_LIVE;
        }
        if ($other_setting->outer_loop_native == 1) {
            return AgentGroup::KUAISHOU_HOT;
        }
        if ((int)$other_setting->campaign_type === 5) {
            return AgentGroup::KUAISHOU_MINI_GAME;
        } else {
            if ($other_setting->agent_type == 'default') {
                return AgentGroup::KUAISHOU;
            } else {
                return AgentGroup::KUAISHOU_UNION;
            }
        }
    }

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = parent::unionParamValidateJudge();

        /** @var ADOtherSettingContentParam $other_setting */
        $other_setting = $this->other_setting;

        // 微信小游戏
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_WECHAT_MINI_GAME) {
            if ($this->site_config->plat_id != PlatId::MINI) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'campaign_type',
                    'value' => $other_setting->campaign_type,
                    'msg' => "当前所选游戏不是微信小游戏"
                ];
            }
        }

        // 线索
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            // 预约广告
            if ((int)$other_setting->site_type_mode === 1) {
                if ($this->site_config->plat_id == PlatId::MINI) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'site_type_mode',
                        'value' => $other_setting->site_type_mode,
                        'msg' => "目前预约广告不支持小游戏"
                    ];
                }
            }
        }

//        if (
//            $this->site_config->game_type == '安卓' &&
//            $this->site_config->plat_id == PlatId::MINI &&
//            $other_setting->os != 0
//        ) {
//            $error_msg_list[] = [
//                'key' => 'os',
//                'value' => $other_setting->os,
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                'msg' => "安卓的小游戏的情况下只能选不限"
//            ];
//        }

        if (
            $this->site_config->game_type == 'IOS' &&
            $this->site_config->plat_id == PlatId::MINI &&
            $other_setting->os != 2
        ) {
            $error_msg_list[] = [
                'key' => 'os',
                'value' => $other_setting->os,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'msg' => "IOS的小游戏的情况下只能选IOS"
            ];
        }

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
            /* @var ADSettingContentParam $setting */

            if (!$setting->cpa_bid || !is_array($setting->cpa_bid)) {
                $cpa_bid = 0;
            } else {
                if ((int)$setting->cpa_bid_mode == 1) {
                    $cpa_bid = $setting->cpa_bid[1];
                } else {
                    $cpa_bid = max($setting->cpa_bid);
                }
            }

            if (!$setting->roi_ratio || !is_array($setting->roi_ratio)) {
                $roi_ratio = '';
            } else {
                if ((int)$setting->roi_ratio_mode == 1) {
                    $roi_ratio = $setting->roi_ratio[1];
                } else {
                    $roi_ratio = min($setting->roi_ratio);
                }
            }

            $error = GamePriceRangeFilterService::isCorrectPrice(
                MediaType::KUAISHOU,
                $this->platform,
                $this->site_config->game_id,
                $this->site_config->game_type,
                $this->getAgentGroup($other_setting),
                $setting->ocpx_action_type,
                self::DEEP_BID_TYPE_MAP[$setting->deep_conversion_type] ?? '',
                $cpa_bid,
                $roi_ratio
            );

            if ($error) {
                if ($cpa_bid && isset($error['bid'])) {
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => 0,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => $error['bid']
                    ];
                }

                if ($roi_ratio && isset($error['roi_goal'])) {
                    $error_msg_list[] = [
                        'key' => 'roi_ratio',
                        'value' => 0,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => $error['roi_goal']
                    ];
                }
            }

            /* @var ADSettingContentParam $setting */

            if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
                // 预约广告
                if ((int)$other_setting->site_type_mode === 1) {
                    if (!in_array($setting->ocpx_action_type, [634, 635])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'key' => 'ocpx_action_type',
                            'value' => 0,
                            'source_id' => $setting_id,
                            'msg' => "预约广告需要使用预约表单或者预约点击跳转"
                        ];
                    }
                }
            }

            if ($this->site_config->plat_id != PlatId::MINI) {
                if (in_array($setting->ocpx_action_type, [937])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'ocpx_action_type',
                        'value' => 0,
                        'source_id' => $setting_id,
                        'msg' => "非小游戏不可选择小程序ROI"
                    ];
                }
                if (in_array($setting->ocpx_action_type, [180, 190]) && in_array($setting->deep_conversion_type, [179])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'deep_conversion_type',
                        'value' => 0,
                        'source_id' => $setting_id,
                        'msg' => "非小游戏不可选择小程序ROI"
                    ];
                }
            }

            if (in_array($setting->ocpx_action_type, [634, 635]) && ($other_setting->campaign_type != KuaishouEnum::TYPE_CLUES || (int)$other_setting->site_type_mode !== 1)) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'ocpx_action_type',
                    'value' => 0,
                    'source_id' => $setting_id,
                    'msg' => "预约表单或者预约点击跳转仅支持预约广告类型"
                ];
            }


            //开屏广告位自定义时间(end_time - begin_time)必须超过三天
            if (in_array(27, $other_setting->scene_id) && $setting->end_time - $setting->begin_time < 86400) {
                throw new AppException('开屏广告位自定义时间必须超过三天');
            }

            foreach ($this->word_list->getWordContent() as $word) {
                if (in_array(5, $setting->scene_id) && strpos($word, '[') !== false && strpos($word, ']') !== false) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'key' => $word,
                        'value' => $word,
                        'msg' => "文案:{$word},有动态词包,不符合联盟广告不能有动态词包的规范"
                    ];
                }
            }


            //校验roi的情况
            if (!$this->compose_config->ignore_warning && ($setting->deep_conversion_type == KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI || in_array($setting->ocpx_action_type, [KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY, KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY]))) {
                $roi_standard_value = (new V2DimGameStandardValueDaysIncolumnModel())->getRoiStandardValue([
                    'platform' => $this->platform,
                    'game_id' => $this->site_config->game_id
                ]);

                //校验首日roi
                if ($setting->deep_conversion_type == KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI || $setting->ocpx_action_type == KuaishouEnum::OCPX_ACTION_TYPE_ROI_1DAY) {
                    $judge_roi_result = (new ADServingLogic())->judgeRoiValue($setting->roi_ratio, $roi_standard_value, 1);
                    $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                        $ele['source_id'] = $setting_id;
                        return $ele;
                    }, $judge_roi_result);
                    $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
                }

                //校验7天roi
                if ($setting->ocpx_action_type == KuaishouEnum::OCPX_ACTION_TYPE_ROI_7DAY) {
                    $judge_roi_result = (new ADServingLogic())->judgeRoiValue($setting->roi_ratio, $roi_standard_value, 7);
                    $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                        $ele['source_id'] = $setting_id;
                        return $ele;
                    }, $judge_roi_result);
                    $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
                }

            }

            //非直播不允许选择激活付费
            if ($setting->ocpx_action_type == KuaishouEnum::OCPX_ACTION_TYPE_ACTIVE_PAY && $other_setting->campaign_type != KuaishouEnum::TYPE_LIVE) {
                $error_msg_list[] = [
                    'source_id' => $setting_id,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'ocpx_action_type',
                    'value' => $setting->ocpx_action_type,
                    'msg' => "营销目的不是直播类型的情况下 优化目标不允许选择激活付费"
                ];
            }
        }

        foreach ($this->targeting_list->getContentList() as $target_id => $targeting) {
            /** @var ADTargetingContentParam $targeting */
            //旧的App行为兴趣，直接报错
            foreach ($targeting->app_interest_ids as $app_interest_ids) {
                if ($app_interest_ids < 1000) {
                    $error_msg_list[] = [
                        'source_id' => $target_id,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'key' => 'app_interest_ids',
                        'value' => $targeting->app_interest_ids,
                        'msg' => "旧版APP行为兴趣已废弃，请重新选择APP行为兴趣"
                    ];
                    break;
                }
            }
            if ($targeting->filter_converted_level == KuaishouEnum::FILTER_CONVERTED_LEVEL_APP) {
                if ($other_setting->campaign_type == KuaishouEnum::TYPE_LIVE) {
                    $error_msg_list[] = [
                        'source_id' => $target_id,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'key' => 'filter_converted_level',
                        'value' => $targeting->filter_converted_level,
                        'msg' => "直播不支持过滤APP的选项"
                    ];
                }
                if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
                    $error_msg_list[] = [
                        'source_id' => $target_id,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'key' => 'filter_converted_level',
                        'value' => $targeting->filter_converted_level,
                        'msg' => "销售线索不支持过滤APP的选项"
                    ];
                }
                if ($other_setting->campaign_type == KuaishouEnum::TYPE_WECHAT_MINI_GAME) {
                    $error_msg_list[] = [
                        'source_id' => $target_id,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'key' => 'filter_converted_level',
                        'value' => $targeting->filter_converted_level,
                        'msg' => "微信小游戏不支持过滤APP的选项"
                    ];
                }
            }
        }

        if (in_array($other_setting->campaign_type, [KuaishouEnum::TYPE_APP, KuaishouEnum::TYPE_CLUES, KuaishouEnum::TYPE_WECHAT_MINI_GAME])) {
            // 聚星达人 or 蓝v服务号->媒体素材视频
            if ($other_setting->outer_loop_native && ($other_setting->talent_type == KuaishouEnum::JUXING_TALENT || ($other_setting->talent_type == KuaishouEnum::BLUE_V_TALENT && $other_setting->talent_video_type == KuaishouEnum::TALENT_MEDIA))) {
                foreach ($this->account_list->account_list as $key => $account_info) {
                    if ($other_setting->talent_type == KuaishouEnum::JUXING_TALENT) {
                        $material_info = $other_setting->getNativeMaterialByAccountID($account_info['account_id'], KuaishouEnum::JUXING_TALENT) ?? [];
                    } else {
                        $material_info = $other_setting->getNativeMaterialByAccountID($account_info['account_id'], KuaishouEnum::BLUE_V_TALENT) ?? [];
                    }
                    if ($material_info) continue;
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'outer_loop_native',
                        'value' => $other_setting->outer_loop_native,
                        'msg' => "请选择{$account_info['account_id']}对应的达人视频"
                    ];
                }
            }
            // 蓝V 服务号达人, 且不是媒体素材视频类型
            if ($other_setting->outer_loop_native && $other_setting->talent_type == KuaishouEnum::BLUE_V_TALENT && $other_setting->talent_video_type == KuaishouEnum::TALENT_SYS) {
                foreach ($this->account_list->account_list as $key => $account_info) {
                    if (empty($other_setting->blue_v_talent_map[$account_info['account_id']])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'blue_v_talent_map',
                            'value' => $other_setting->blue_v_talent_map,
                            'msg' => "请选择{$account_info['account_id']}对应的快手号蓝V达人ID"
                        ];
                        break;
                    }
                }
                if (!empty($this->material_list->all_image_list)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => 'material',
                        'value' => 0,
                        'msg' => "其他参数-原生蓝V（服务号达人）-必须使用视频素材"
                    ];
                }
            }
            // 普通快手号
            if ($other_setting->outer_loop_native && $other_setting->talent_type == KuaishouEnum::COMMON_TALENT) {
                foreach ($this->account_list->account_list as $key => $account_info) {
                    if (empty($other_setting->blue_v_talent_map[$account_info['account_id']])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'blue_v_talent_map',
                            'value' => $other_setting->blue_v_talent_map,
                            'msg' => "请选择{$account_info['account_id']}对应的普通快手号"
                        ];
                        break;
                    }
                }
                if (!empty($this->material_list->all_image_list)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => 'material',
                        'value' => 0,
                        'msg' => "其他参数-普通快手号-必须使用视频素材"
                    ];
                }
            }
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && $this->compose_config->video_num_in_ad > 10) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => '程序化创意视频素材最多支持10组'
            ];
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {

            if ($this->compose_config->word_num_in_ad > 3) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => "程序化创意中每个广告组只能小于等于3个文案"
                ];
            }

            if ($this->compose_config->video_num_in_ad > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'video_num_in_ad',
                    'value' => $this->compose_config->video_num_in_ad,
                    'msg' => "程序化创意中每个广告组只能小于等于10个视频"
                ];
            }

            if ($other_setting->campaign_type == KuaishouEnum::TYPE_LIVE) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'campaign_type',
                    'value' => $other_setting->campaign_type,
                    'msg' => "直播不能使用程序化创意"
                ];
            }
        }

        return $error_msg_list;
    }

}
