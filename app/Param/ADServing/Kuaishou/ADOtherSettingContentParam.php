<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 12:02
 */

namespace App\Param\ADServing\Kuaishou;

use App\Constant\KuaishouEnum;
use App\Param\ADServing\AbstractADOtherSettingContentParam;

class ADOtherSettingContentParam extends AbstractADOtherSettingContentParam
{
    public $agent_type = 'default';

    /**
     * @var string $scene_id_mode 资源位置模式
     */
    public $scene_id_mode = '';

    /**
     * @var array $scene_id 资源位置
     */
    public $scene_id = [];

    /**
     * @var string $action_bar_text 行动号召按钮文案
     */
    public $action_bar_text = '';

    /**
     * @var array $page_map 落地页
     */
    public $page_map = [];

    /**
     * @var string $page_url 落地页地址
     */
    public $page_url = '';

    /**
     * @var int 落地页 1：手动输入 2：建站落地页
     */
    public $page_mode = 0;

    /**
     * @var string $campaign_type 广告组类型
     */
    public $campaign_type = KuaishouEnum::TYPE_APP;

    /**
     * @var string
     */
    public $os = 0;

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array|mixed
     */
    public function getPageInfo($account_id)
    {
        if ($page_info = $this->page_map[$account_id] ?? '') {
            $page_info = json_decode($page_info);
            return $page_info;
        }
        return [];
    }

    public function paramHook()
    {
        if (!$this->page_mode) {
            $this->page_mode = 0;
        }
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        return [];
    }

    /**
     * 参数格式化
     */
    public function format()
    {
    }

}
