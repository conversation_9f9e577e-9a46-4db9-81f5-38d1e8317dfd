<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Kuaishou\Project;

use App\Constant\BatchAD;
use App\Constant\KuaishouEnum;
use App\Param\ADServing\Kuaishou\ADComposeContentParam as KuaishouADComposeContentParam;
use App\Param\ADServing\Kuaishou\ADTargetingContentParam;

class ADComposeContentParam extends KuaishouADComposeContentParam
{
    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = parent::unionParamValidateJudge();

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            /**@var ADSettingContentParam $setting */

            foreach ($this->targeting_list->getContentList() as $target_id => $targeting) {

                /**@var ADTargetingContentParam $targeting */

                if ($targeting->age['min'] && $targeting->age['max']) {
                    if ($targeting->age['max'] - $targeting->age['min'] < 5) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                            'source_id' => $target_id,
                            'key' => 'age_none',
                            'value' => $targeting->age,
                            'msg' => '智能托管年龄范围不能小于5岁'
                        ];
                    }
                }

                if ($targeting->filter_converted_level == KuaishouEnum::FILTER_CONVERTED_LEVEL_APP) {
                    if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
                        $error_msg_list[] = [
                            'source_id' => $target_id,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                            'key' => 'filter_converted_level',
                            'value' => $targeting->filter_converted_level,
                            'msg' => "销售线索不支持过滤APP的选项"
                        ];
                    }
                }
            }

            foreach ($this->word_list->getWordContent() as $word) {
                if ($other_setting->scene_id && in_array(5, $other_setting->scene_id) && strpos($word, '[') !== false && strpos($word, ']') !== false) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'value' => $word,
                        'msg' => "文案:{$word},有动态词包,不符合联盟广告不能有动态词包的规范"
                    ];
                }
            }

            if (in_array(10, $other_setting->scene_id) && $setting->hosting_scene != 0) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'value' => $setting->hosting_scene,
                    'msg' => "联盟的情况下无法使用最大转化"
                ];
            }

            if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {

                foreach ($this->material_list->video_vertical_list as $video_key => $video_info) {
                    if (count($video_info['cover_list']) > 1) {
                        $error_msg_list[] = [
                            'type' => 'material',
                            'key' => 'material',
                            'value' => 0,
                            'msg' => "程序化创意的情况下{$video_info['url']}所选封面数量不能大于1，"
                        ];
                    }
                }
            }
        }

        if ($other_setting->campaign_type == KuaishouEnum::TYPE_LIVE) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'campaign_type',
                'value' => 0,
                'msg' => '智能托管不支持直播推广营销目的'
            ];
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && $this->compose_config->video_num_in_ad > 20) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => '程序化创意视频素材最多支持20组'
            ];
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            if ($this->compose_config->video_num_in_ad > 20) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'video_num_in_ad',
                    'value' => $this->compose_config->video_num_in_ad,
                    'msg' => "智能托管程序化创意中每个广告组只能小于等于20个视频"
                ];
            }

            if ($this->compose_config->word_num_in_ad > 12) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => "智能托管程序化创意中每个广告组只能小于等于12个文案"
                ];
            }
        }

        return $error_msg_list;
    }

}
