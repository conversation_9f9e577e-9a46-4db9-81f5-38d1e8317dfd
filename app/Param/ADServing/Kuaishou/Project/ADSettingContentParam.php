<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Kuaishou\Project;

use App\Param\ADServing\Kuaishou\ADSettingContentParam as KuaishouADSettingContentParam;

class ADSettingContentParam extends KuaishouADSettingContentParam
{
    public $use_app_market;
    public $app_store;
    public $hosting_scene = 0;
    public $bid_type = 10;
    public $smart_cover;
    public $asset_mining;
    public $rule_enable;
    public $campaign_name_rule;
    public $unit_name_rule;
    public $creative_name_rule;
    public $ad_operation;
    public $ocpx_action_type;
    public $cpa_bid;
    public $roi_ratio;
    public $deep_conversion_type;
    public $deep_conversion_bid;
    public $action_bar;
    public $day_budget = 0;

    public function format()
    {
        parent::format();
        if (!$this->rule_enable) {
            $this->campaign_name_rule = $this->unit_name_rule = $this->creative_name_rule = [];
        }
        if ($this->hosting_scene == 5) {
            $this->cpa_bid = [0];
            $this->roi_ratio = [0];
        }
    }

    public function validate()
    {
        $error_msg_list = parent::validate();

        if ($this->day_budget < 500) {
            $error_msg_list[] = [
                'key' => 'day_budget',
                'value' => $this->day_budget,
                'msg' => "投放预算(day_budget)不能低于500"
            ];
        }

        if ($this->cpa_bid && $this->deep_conversion_bid && $this->deep_conversion_type == 180) {
            foreach ($this->cpa_bid as $cpa_bid_value) {
                foreach ($this->deep_conversion_bid as $deep_conversion_bid_value) {
                    if ($cpa_bid_value > $deep_conversion_bid_value) {
                        $error_msg_list[] = [
                            'key' => 'cpa_bid',
                            'value' => $this->cpa_bid,
                            'msg' => "广告组出价(cpa_bid)不能大于广告组深度出价(deep_conversion_bid)"
                        ];
                    }
                }
            }
        }

        if (!$this->action_bar) {
            $error_msg_list[] = [
                'key' => 'action_bar',
                'value' => $this->action_bar,
                'msg' => '请选择行动号召文案'
            ];
        }

        if (!in_array($this->ocpx_action_type, [191, 774])) {
            if (!$this->cpa_bid || !is_array($this->cpa_bid) || count($this->cpa_bid) <= 0) {
                $error_msg_list[] = [
                    'key' => 'cpa_bid',
                    'value' => $this->cpa_bid,
                    'msg' => '转化出价(cpa_bid)不能为空'
                ];
            } else {
                foreach ($this->cpa_bid as $cpa_bid_xs) {
                    if (!is_numeric($cpa_bid_xs)) {
                        $error_msg_list[] = [
                            'key' => 'cpa_bid',
                            'value' => $this->cpa_bid,
                            'msg' => '转化出价(cpa_bid)不是数字'
                        ];
                        break;
                    }
                }
            }
        }

        if ($this->deep_conversion_type == 3 && !$this->deep_conversion_bid) {
            $error_msg_list[] = [
                'key' => 'deep_conversion_bid',
                'value' => $this->deep_conversion_bid,
                'msg' => '深度优化出价(deep_conversion_bid)不能为空'
            ];
        } else {
            foreach ($this->deep_conversion_bid as $deep_conversion_bid_xs) {
                if (!is_numeric($deep_conversion_bid_xs)) {
                    $error_msg_list[] = [
                        'key' => 'deep_conversion_bid',
                        'value' => $this->deep_conversion_bid,
                        'msg' => '深度优化出价(deep_conversion_bid)不是数字'
                    ];
                    break;
                }
            }
        }

        if (in_array($this->ocpx_action_type, [191, 774]) && !$this->roi_ratio) {
            $error_msg_list[] = [
                'key' => 'roi_ratio',
                'value' => $this->roi_ratio,
                'msg' => 'ROI系数(roi_ratio)不能为空'
            ];
        }

        if ($this->deep_conversion_type == 92) {
            if (!$this->roi_ratio) {
                $error_msg_list[] = [
                    'key' => 'roi_ratio',
                    'value' => $this->roi_ratio,
                    'msg' => '深度转化付费ROI系数(roi_ratio)不能为空'
                ];
            } else {
                foreach ($this->roi_ratio as $roi_xs) {
                    if ($roi_xs > 1) {
                        $error_msg_list[] = [
                            'key' => 'roi_ratio',
                            'value' => $this->roi_ratio,
                            'msg' => '深度转化付费ROI系数(roi_ratio)不能大于0'
                        ];
                    }
                    if (!is_numeric($roi_xs)) {
                        $error_msg_list[] = [
                            'key' => 'roi_ratio',
                            'value' => $this->roi_ratio,
                            'msg' => '深度转化付费ROI系数(roi_ratio)不是数字'
                        ];
                        break;
                    }
                }
            }
        }

        return $error_msg_list;
    }
}
