<?php
/**
 * Created by Php<PERSON>torm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 15:41
 */

namespace App\Param\ADServing;

use App\Constant\BatchAD;
use App\Constant\BatchADClassMap;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\PermissionLogic;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\AbstractParam;
use App\Param\ADServing\ADComposeContent\ADComposeAccountListParam;
use App\Param\ADServing\ADComposeContent\ADComposeConfigParam;
use App\Param\ADServing\ADComposeContent\ADComposeMaterialListParam;
use App\Param\ADServing\ADComposeContent\ADComposeRuleListParam;
use App\Param\ADServing\ADComposeContent\ADComposeSettingListParam;
use App\Param\ADServing\ADComposeContent\ADComposeTargetingListParam;
use App\Param\ADServing\ADComposeContent\ADComposeWordListParam;
use App\Param\SiteConfigParam;
use App\Service\MaterialExpertFilterService;
use App\Service\MaterialInferiorFilterService;
use App\Service\MaterialThemeFilterService;
use App\Utils\Helpers;
use Common\EnvConfig;

abstract class AbstractADComposeContentParam extends AbstractParam
{
    public $compose_id = 0;

    public $media_type;

    public $media_agent_type;

    public $targeting_compose_type;

    public $setting_compose_type;

    public $dispatch_type;

    public $compose_type;

    public $platform;

    public $company;

    public $creative_mode;

    /**
     * @var ADComposeAccountListParam $account_list
     */
    public $account_list;

    /**
     * @var SiteConfigParam $site_config
     */
    public $site_config;

    /**
     * @var ADComposeTargetingListParam $targeting_list
     */
    public $targeting_list;

    /**
     * @var ADComposeWordListParam $word_list
     */
    public $word_list;

    /**
     * @var ADComposeSettingListParam $setting_list
     */
    public $setting_list;

    /**
     * @var ADComposeRuleListParam $calc_rule_list
     */
    public $calc_rule_list;

    /**
     * @var ADComposeConfigParam $compose_config
     */
    public $compose_config;

    /**
     * @var ADComposeMaterialListParam $material_list
     */
    public $material_list;

    /**
     * @var AbstractADOtherSettingContentParam $other_setting
     */
    public $other_setting;

    /**
     * @var array $rta_list
     */
    public $rta_list = [];

    public $script_leader = '';

    public $script_leader_id = 0;

    public function __construct($property = [])
    {
        // 广告参数
        $property['compose_config'] = new ADComposeConfigParam($property['compose_config']);
        // 账号选择
        $property['account_list'] = new ADComposeAccountListParam($property);
        // 广告配置
        if (($property['media_type'] ?? "") && $property['media_type'] != MediaType::KUAISHOU) {
            unset($property['site_config']['app_name']);
        }
        $property['site_config'] = new SiteConfigParam($property['site_config']);
        // 素材选择
        $property['material_list'] = new ADComposeMaterialListParam($property);
        // 文案选择
        $property['word_list'] = new ADComposeWordListParam($property);
        // 定向选择
        $targeting_list = new ADComposeTargetingListParam();
        if ($property['targeting_compose_type'] == BatchAD::TARGETING_COMPOSE_TYPE_LIST) {
            foreach ($property['targeting_list'] as $targeting_key => $targeting_info) {
                $targeting_list->addTargeting($targeting_info['id']);
            }
            $targeting_list->initTargetingInfo();
        } else {
            $targeting_list->setTargetingInfo((new ADTargetingPacketParam([
                'media_type' => $property['media_type'],
                'creator' => $property['script_leader'] ?? '',
            ]))->setTargetingByArray($property['targeting']));
        }
        $property['targeting_list'] = $targeting_list;
        // 参数选择
        $setting_list = new ADComposeSettingListParam();
        if ($property['setting_compose_type'] == BatchAD::SETTING_COMPOSE_TYPE_LIST) {
            foreach ($property['setting_list'] as $setting_key => $setting_info) {
                $setting_list->addSetting($setting_info['id']);
            }
            $setting_list->initSettingInfo();
        } else {
            $setting_list->setSettingInfo((new ADSettingPacketParam([
                'media_type' => $property['media_type'],
                'media_agent_type' => $property['media_agent_type'],
                'creator' => $property['script_leader'] ?? '',
            ]))->setSettingByArray($property['setting']));
        }
        $property['setting_list'] = $setting_list;
        // 其他参数
        $other_setting_content_class = BatchADClassMap::getClassName(
            $property['media_type'],
            $property['media_agent_type'],
            BatchADClassMap::OTHER_SETTING_CLASS_NAME
        );
        $property['other_setting'] = new $other_setting_content_class($property['other_setting']);
        // 规则选择
        $property['calc_rule_list'] = $property['calc_rule_list'] ?? [];
        $rule_list = new ADComposeRuleListParam();
        foreach ($property['calc_rule_list'] as $rule_key => $rule_info) {
            $rule_list->addCaleRule((int)$rule_info['id'], $rule_info['name']);
        }
        $property['calc_rule_list'] = $rule_list;
        parent::__construct($property);
    }

    /**
     * 获取所有素材的id映射map内容 （暂时没有组素材）
     * @return array
     */
    private function getAllMaterialFileKeyByID()
    {
        $material_file_map = [];

        foreach ($this->material_list->small_image_list as $small_image_file) {
            $material_file_map[(int)$small_image_file['id']] = $small_image_file;
        }
        foreach ($this->material_list->large_image_list as $large_image_file) {
            $material_file_map[(int)$large_image_file['id']] = $large_image_file;
        }
        foreach ($this->material_list->large_vertical_image_list as $large_vertical_image_file) {
            $material_file_map[(int)$large_vertical_image_file['id']] = $large_vertical_image_file;
        }
        foreach ($this->material_list->audio_list as $audio_file) {
            $material_file_map[(int)$audio_file['id']] = $audio_file;
        }
        foreach ($this->material_list->video_list as $video_file) {
            $material_file_map[(int)$video_file['id']] = $video_file;
        }
        foreach ($this->material_list->video_vertical_list as $video_vertical_file) {
            $material_file_map[(int)$video_vertical_file['id']] = $video_vertical_file;
        }

        return $material_file_map;
    }

    public function validateIOS($ios_sure)
    {
        $error_msg_list = [];
        if (!$ios_sure) {

            if ($this->site_config->game_type == 'IOS') {
                $game_info = (new V2DimGameIdModel())->getDataByGameId($this->platform, $this->site_config->game_id);
                if ($game_info->os === 'IOS' && !in_array($game_info->plat_id, [PlatId::MINI, PlatId::DY_MINI])) {
                    if (empty($game_info->app_id)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_IOS_WARNING,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_IOS_WARNING,
                            'value' => '',
                        ];
                    } else {
                        $download_url = "https://apps.apple.com/cn/app/id$game_info->app_id";
                        $http_code = Helpers::getHttpStatus($download_url, ['proxy' => false, 'timeout' => 10]);
                        if ((int)$http_code < 200 || (int)$http_code >= 400) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_IOS_WARNING,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_IOS_WARNING,
                                'value' => '',
                            ];
                        }
                    }
                }
            }
        }

        $error_msg = [];
        foreach ($error_msg_list as $err) {
            $error_msg[$err['type']][] = $err;
        }

        return [
            'state' => !$error_msg_list,
            'num' => 0,
            'result' => 0,
            'list' => [],
            'error_msg' => $error_msg
        ];
    }

    /**
     * 校验低效素材
     * @param array $attempt_material_ids
     * @return array
     */
    public function validateLowMaterial(array $attempt_material_ids = [])
    {

        $error_msg_list = [];

        $material_file_ids_list = array_values(array_keys($this->getAllMaterialFileKeyByID()));

        $material_file_list = (new OdsMaterialFileLogModel())->getListByID($material_file_ids_list);

        $material_file_md5_map = $material_file_list->keyBy('signature')->toArray();

        $material_file_id_map = $material_file_list->keyBy('id')->toArray();

        $attempt_material_signatures = [];

        foreach ($attempt_material_ids as $id) {
            $attempt_material_signatures[] = $material_file_id_map[$id]->signature;
        }

        $material_filter = MaterialInferiorFilterService::inferiorMaterial($this->media_type, $this->platform, $this->site_config->game_id, array_keys($material_file_md5_map));

        if ($material_filter) {
            foreach ($material_filter as $error_material) {
                if (!in_array($error_material->signature, $attempt_material_signatures) || $error_material->is_force == 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL_WARNING,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL_WARNING,
                        'value' => (int)$material_file_md5_map[$error_material->signature]->id,
                        'force' => $error_material->is_force == 1,
                        'ext' => $error_material->ext,
                        'msg' => "{$material_file_md5_map[$error_material->signature]->id}素材被判断为低效素材，不建议投放,原因:{$error_material->reject_reason}"
                    ];
                }
            }
        }

        $material_media_filter = MaterialInferiorFilterService::mediaInferiorMaterial($this->media_type, array_keys($material_file_md5_map));
        if ($material_media_filter) {
            foreach ($material_media_filter as $error_media_material) {
                if (!in_array($error_media_material->signature, $attempt_material_signatures)) {
                    $msg = '';
                    switch ($error_media_material) {
                        case $error_media_material->is_similar_material == 1:
                            $msg = "{$material_file_md5_map[$error_media_material->signature]->id}素材被头条判断为同质化挤压严重素材";
                            break;
                        case $error_media_material->is_similar_expected_queue_material == 1:
                        case $error_media_material->is_similar_queue_material == 1:
                            $msg = "{$material_file_md5_map[$error_media_material->signature]->id}素材被头条判断为同质化风险素材";
                            break;
                        case $error_media_material->is_carry_material == 1:
                            $msg = "{$material_file_md5_map[$error_media_material->signature]->id}素材被头条判断为存在搬运风险素材";
                            break;
                    }
                    if ($msg) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL_MEDIA_WARNING,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL_MEDIA_WARNING,
                            'value' => (int)$material_file_md5_map[$error_media_material->signature]->id,
                            'force' => false,
                            'msg' => $msg
                        ];
                    }
                }
            }
        }

        $error_msg = [];
        foreach ($error_msg_list as $err) {
            $error_msg[$err['type']][] = $err;
        }
        return [
            'state' => !$error_msg_list,
            'num' => 0,
            'result' => 0,
            'list' => [],
            'error_msg' => $error_msg
        ];
    }

    /**
     * @return array
     */
    public function commonValidateMaterial()
    {
        $error_msg_list = [];

        $material_file_map = $this->getAllMaterialFileKeyByID();

        $material_file_id_list = array_values(array_keys($material_file_map));

        $result_game = MaterialThemeFilterService::inCorrectGame($this->platform, $this->site_config->game_id, $material_file_id_list);

        foreach ($result_game as $file_id => $error_game) {
            if (!$error_game) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => $file_id,
                    'msg' => "{$material_file_map[$file_id]['filename']}，素材文件id为{$file_id}，在game_id = {$this->site_config->game_id}的情况下不能使用"
                ];
            }
        }

        // 判断素材人脸识别过期
        $result_expert = MaterialExpertFilterService::incorrectExpert(
            $this->script_leader ?: Container::getSession()->name,
            $this->media_type,
            $this->platform,
            $material_file_id_list,
            'batch_ad'
        );

        foreach ($result_expert as $file_id => $error_expert) {
            if (!$error_expert['status']) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => $file_id,
                    'msg' => $error_expert['content']
                ];
            }
        }

        // 判断原生加热题材的素材只能巨量抖音信息流使用
        if ($this->platform == 'TW') {
            $setting_content_class = BatchADClassMap::getClassName(
                $this->media_type,
                $this->media_agent_type,
                BatchADClassMap::SETTING_CLASS_NAME
            );

            foreach ($this->setting_list->setting_info_list as $setting) {
                $setting_object = new $setting_content_class((array)($setting->setting), true);
                $setting_object->format();
                $result_inventory = MaterialThemeFilterService::inCorrectInventoryType(
                    $this->media_type,
                    $material_file_id_list,
                    $setting_object
                );
                foreach ($result_inventory as $file_id => $err) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $file_id,
                        'msg' => "素材文件id{$file_id}-此文件不能用于投放巨量-抖音信息流以外的版位"
                    ];
                }
                if ($error_msg_list) {
                    break;
                }
            }
        }

        return $error_msg_list;
    }

    public function validateAccount()
    {
        $error_msg_list = [];
        $account_id_list = [];
        foreach ($this->account_list->account_list as $account_info) {
            $account_id_list[] = $account_info['account_id'];
        }
        $account_info_list = (new MediaAccountModel())->getListByMediaTypeAccountIds($this->media_type, $account_id_list);
        if ($account_info_list->count() != count($account_id_list)) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_ACCOUNT,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_ACCOUNT,
                'value' => 0,
                'msg' => "广告组合所选的账号信息不全"
            ];
        }

        $error = '';
        $platform_name = EnvConfig::PLATFORM_MAP[$this->platform] ?? $this->platform;
        foreach ($account_info_list as $account_info) {
            if ($account_info->platform != $this->platform) {
                $error .= "账号{$account_info->account_id}所属平台不是{$platform_name};";
            }
        }
        if ($error) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_ACCOUNT,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_ACCOUNT,
                'value' => 0,
                'msg' => $error
            ];
        }
        return $error_msg_list;
    }

    /**
     * 组合参数校验
     * @return array
     */
    private function paramValidateJudge()
    {
        $validate_msg_list = [];

        foreach ($this->other_setting->validate() as $other_setting_error_param) {
            $other_setting_error_param['type'] = BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING;
            $validate_msg_list[] = $other_setting_error_param;
        }
        foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {
            foreach ($targeting->validate() as $targeting_error_param) {
                $targeting_error_param['source_id'] = $targeting_id;
                $targeting_error_param['type'] = BatchAD::COMPOSE_ERROR_TYPE_TARGETING;
                $validate_msg_list[] = $targeting_error_param;
            }
        }
        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
            foreach ($setting->validate() as $setting_error_param) {
                $setting_error_param['source_id'] = $setting_id;
                $setting_error_param['type'] = BatchAD::COMPOSE_ERROR_TYPE_SETTING;
                $validate_msg_list[] = $setting_error_param;
            }
        }

        foreach ($this->unionParamValidateJudge() as $union_error_param) {
            $validate_msg_list[] = $union_error_param;
        }

        return $validate_msg_list;
    }

    /**
     * 组合参数联合检验
     * @return mixed
     */
    abstract protected function unionParamValidateJudge();

    /**
     * 执行广告组合前输入值的逻辑判断
     * @return array
     */
    public function composeAlgorithmValidate()
    {
        // 检测素材是否可以使用
        $error_msg_list = $this->commonValidateMaterial();

        // 检测账号是否都在一个平台下
        $error_msg_list = array_merge($error_msg_list, $this->validateAccount());

        if ($this->calc_rule_list->getCount() > 0 && !in_array($this->media_type, [MediaType::TOUTIAO, MediaType::TENCENT, MediaType::KUAISHOU])) {
            throw new AppException('规则初始化绑定只支持今日头条和腾讯');
        }

        if ($this->calc_rule_list->getCount() > 10) {
            throw new AppException('初始规则绑定数量不得超过十个');
        }

        if (!$this->site_config->package) {
            throw new AppException('广告配置的包名(package)不能为空，请联系运营配置相关包名!');
        }

        if (!in_array($this->media_type, [MediaType::UC, MediaType::KUAISHOU])) {
            if ($this->site_config->convert_source_type == 'AD_CONVERT_SOURCE_TYPE_SDK' && !$this->site_config->appid) {
                throw new AppException('广告配置的应用ID(appid)不能为空，请联系运营配置相关appid!');
            }
        }

        if (empty($this->platform)) {
            throw new AppException('platform不能为空');
        }

        if (empty($this->media_type)) {
            throw new AppException('media_type不能为空');
        }

        if (!$this->site_config) {
            throw new AppException('上报配置不能为空');
        }

        if ($this->account_list->getCount() <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_ACCOUNT,
                'key' => 'account_list',
                'value' => $this->account_list->getCount(),
                'msg' => "请选择广告组合的账号，每次组合最低需要一个账号"
            ];
        }

        if ($this->targeting_list->getCount() <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                'value' => $this->targeting_list->getCount(),
                'msg' => "请选择广告组合的定向包，每次组合最低需要一个定向包"
            ];
        }

        if ($this->setting_list->getCount() <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'value' => $this->setting_list->getCount(),
                'msg' => "请选择广告组合的参数包，每次组合最低需要一个参数包"
            ];
        }

        if (($this->material_list->getImageNum() + $this->material_list->getVideoNum()) <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'value' => 0,
                'msg' => "请选择广告组合的素材，每次组合最低需要一个素材"
            ];
        }

        if ($this->material_list->audio_list && count($this->material_list->all_video_list) !== count($this->material_list->audio_list)) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'value' => 0,
                'msg' => "视频音频素材只能选一种"
            ];
        }

        if ($this->word_list->getCount() <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                'value' => $this->word_list->getCount(),
                'msg' => "请选择广告组合的文案，每次组合最低需要一条文案"
            ];
        }

        if (((int)$this->compose_config->pic_num_in_ad + (int)$this->compose_config->video_num_in_ad) <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => "每个广告组合的素材数量必须大于0"
            ];
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => "每个广告组合的素材数量必须大于0"
            ];
        }

        if ((int)$this->compose_config->word_num_in_ad <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'word_num_in_ad',
                'value' => $this->compose_config->word_num_in_ad,
                'msg' => "每个广告组合的文案数量必须大于0"
            ];
        }

        $image_num = $this->material_list->getImageNum();
        if ($image_num < $this->compose_config->pic_num_in_ad) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => "已选择图片{$image_num}张,选择的图片数量小于每广告二级所需图片数量"
            ];
        }

        $video_num = $this->material_list->getVideoNum();
        if ($video_num < $this->compose_config->video_num_in_ad) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => "已选择视频{$video_num}部,选择的视频数量小于每广告二级所需视频数量"
            ];
        }

        $word_num = $this->word_list->getCount();
        if ($this->word_list->getCount() < $this->compose_config->word_num_in_ad) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'word_num_in_ad',
                'value' => $this->compose_config->word_num_in_ad,
                'msg' => "选择的文案数量为{$word_num},小于每广告二级所需文案数量"
            ];
        }

        if (!$this->compose_config->ad1_name || count($this->compose_config->ad1_name) <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'ad1_name',
                'value' => $this->compose_config->ad1_name,
                'msg' => "参数包广告一级组合名字为空"
            ];
        }

        if ((int)$this->media_agent_type === 0 && (!$this->compose_config->ad2_name || count($this->compose_config->ad2_name) <= 0)) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'ad2_name',
                'value' => $this->compose_config->ad2_name,
                'msg' => "参数包广告二级组合名字为空"
            ];
        }

        // 多文案跟随创意
        if ($this->compose_config->creative_word_num_in_ad > 1) {
            if ($this->compose_config->word_num_in_ad == 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => "创意文案数大于1时，单元文案数量必须大于1，且为创意文案数量的倍数"
                ];
            }
            if ($this->compose_config->word_num_in_ad % $this->compose_config->creative_word_num_in_ad !== 0) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => "创意文案数大于1时，单元文案数量需要为创意文案数量的倍数"
                ];
            }
            if (!$this->material_list->group_image_list) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'creative_word_num_in_ad',
                    'value' => $this->compose_config->creative_word_num_in_ad,
                    'msg' => "创意文案数大于1，仅限组图素材可用"
                ];
            }
            if ((int)$this->compose_config->word_material_compose_mode === 0) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_material_compose_mode',
                    'value' => $this->compose_config->word_material_compose_mode,
                    'msg' => "创意文案数大于1，文案素材不可乘积"
                ];
            }
        }

        $material_file_model = new MaterialFileModel();
        $video_judge_available_id_list = array_column($this->material_list->all_video_list, 'id');
        if ($video_judge_available_id_list) {
            $video_available_list = $material_file_model->getListByIds($video_judge_available_id_list)
                ->where('is_del', '=', 0)->pluck('id')->toArray();
            foreach ($this->material_list->all_video_list as $video_key => $video_info) {
                if (!in_array($video_info['id'], $video_available_list)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $video_info['id'],
                        'msg' => "{$video_info['filename']}已被删除不可使用，请重新删除"
                    ];
                }
            }
        }

        if ($error_msg_list) {
            return [
                'state' => false,
                'error_list' => $error_msg_list
            ];
        }

        $this->judgeNameConfig();
        $error_msg_list = $this->paramValidateJudge();

        return [
            'state' => !$error_msg_list,
            'error_list' => $error_msg_list
        ];
    }

    /**
     * 判断设置
     */
    private function judgeNameConfig()
    {
        $material_name_len = 0;
        $material_name = '';
        if (in_array('material_name', $this->compose_config->ad1_name) || in_array('material_name', $this->compose_config->ad2_name)) {
            $creative_value = $this->material_list->getAllMaterialList()[0];
            $material_file_info = (new MaterialFileModel())->getData($creative_value['id']);
            if ($material_file_info) {
                $material_info = (new MaterialModel())->get($material_file_info->material_id, $material_file_info->platform);
                if ($material_info) {
                    $material_name = $material_info->name;
                    $material_name_len += (int)(Helpers::ADServingStrLen($material_name) / 2);
                }
            }
        }

        $targeting_name_len = 0;
        $targeting_name = '';
        if (in_array('targeting_name', $this->compose_config->ad1_name) || in_array('targeting_name', $this->compose_config->ad2_name)) {
            foreach ($this->targeting_list->targeting_info_list as $key => $targeting) {
                $len = (int)(Helpers::ADServingStrLen($targeting->name) / 2);
                if ($len > $targeting_name_len) {
                    $targeting_name_len = $len;
                    $targeting_name = $targeting->name;
                }
            }
        }

        $name_array = [
            'judgeAD1Name' => $this->compose_config->ad1_name,
            'judgeAD2Name' => $this->compose_config->ad2_name
        ];
        foreach ($name_array as $config_name => $name_config) {
            $name_len = 0;
            foreach ($name_config as $key => $name_element) {
                switch ($name_element) {
                    case 'targeting_id':
                    case 'date':
                    case 'site_id':
                        $name_len += 10;
                        break;
                    case 'targeting_name':
                        $name_len += $targeting_name_len;
                        break;
                    case 'campaign_id':
                        $name_len += 20;
                        break;
                    case 'random':
                    case 'month_date':
                        $name_len += 4;
                        break;
                    case 'time':
                        $name_len += 8;
                        break;
                    case 'date_time':
                        $name_len += 19;
                        break;
                    case 'material_name':
                        $name_len += $material_name_len;
                        break;
                    default:
                        $name_len += (int)(Helpers::ADServingStrLen($name_element) / 2);
                        break;
                }
            }
            $method_name = $config_name;
            $error_msg = $this->$method_name($name_len, $material_name, $targeting_name);
            if ($error_msg) {
                throw new AppException($error_msg);
            }
        }
    }

    /**
     * 判断广告1级名字
     * @param int $num
     * @param string $material_name
     * @param string $targeting_name
     * @return string
     */
    abstract public function judgeAD1Name(int $num, string $material_name, string $targeting_name);

    /**
     * 判断广告2级名字
     * @param int $num
     * @param string $material_name
     * @param string $targeting_name
     * @return string
     */
    abstract public function judgeAD2Name(int $num, string $material_name, string $targeting_name);

    abstract public function getMaterialFileNormalList(): array;

    /**
     * @return array
     */
    public function getTaskBaseData()
    {
        return [
            'compose_id' => $this->compose_id,
            'company' => $this->company,
            'platform' => $this->platform,
            'media_type' => $this->media_type,
            'creative_mode' => $this->creative_mode,
            'media_agent_type' => $this->media_agent_type,
            'creator' => $this->script_leader ?: '',
            'creator_id' => $this->script_leader_id ?: 0,
        ];
    }


}
