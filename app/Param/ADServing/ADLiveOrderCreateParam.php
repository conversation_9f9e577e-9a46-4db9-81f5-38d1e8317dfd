<?php

namespace App\Param\ADServing;

use App\Exception\AppException;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\AbstractParam;
use App\Utils\Helpers;
use Illuminate\Database\Eloquent\Collection;

class ADLiveOrderCreateParam extends AbstractParam
{
    public $platform = 'TW';
    public $dsp_order_type = 2;
    public $live_media_type = 1;
    public $task_name;
    public $author_id = 0;
    public $author_name = '';
    public $aweme_account;
    public $click_url;
    public $author_mcn;
    public $android_site_id;
    public $site_id_list;
    public $expiration_live_type;
    public $expiration_live_time_list = [];
    public $unite_expiration_date;
    public $unit_expiration_hour;
    public $custom_order_count;
    public $order_count;
    public $live_anchor_list;
    public $interface_person;

    public function paramHook()
    {
        if ($this->author_id && !is_numeric($this->author_id)) {
            throw new AppException('达人ID无效！');
        }

        $price_types = array_column($this->live_anchor_list, 'live_price_type');

        foreach ($this->live_anchor_list as $live_anchor) {
            if (!$live_anchor['live_anchor_id'] || !$live_anchor['live_price_type']) {
                throw new AppException('主播信息未填写完整');
            }
            if (count($this->live_anchor_list) > 1 && (!$live_anchor['live_time'] || !(count(array_filter($live_anchor['live_time'])) == 2))) {
                throw new AppException('预计直播时间不能为空');
            }
            if ($live_anchor['live_price_type'] != 5 && !$live_anchor['live_price']) {
                throw new AppException('主播单价为0，请确认结算方式对应单价是否录入');
            }
        }

        if (count($this->live_anchor_list) > 1) {
            if (Helpers::hasOverlappingIntervals(array_column($this->live_anchor_list, 'live_time'))) {
                throw new AppException('多人直播预计直播时间有交叉，请检查');
            }
            if (in_array(5, $price_types) && count(array_unique($price_types)) > 1) {
                throw new AppException('多人直播结算方式只能同时为CPS结算方式或同时为非CPS结算方式，请检查');
            }
        }

        // 自定义开播时间
        if ($this->expiration_live_type === 2) {
            if (!empty($this->expiration_live_time_list)) {
                $this->expiration_live_time_list = array_slice(array_values(array_filter($this->expiration_live_time_list)), 0, $this->custom_order_count);
            } else {
                $this->expiration_live_time_list = [];
            }
        } else {
            // 统一开播时间
            $start_time = strtotime($this->unite_expiration_date[0]);
            $end_time = strtotime($this->unite_expiration_date[1]);
            $this->expiration_live_time_list = [];
            for ($i = $start_time; $i <= $end_time; $i = $i + 86400) {
                $this->expiration_live_time_list[] = date('Y-m-d', $i) . ' ' . $this->unit_expiration_hour;
            }
            $this->custom_order_count = count($this->expiration_live_time_list);
        }

        $this->site_id_list = array_map('trim', array_values(array_filter($this->site_id_list)));

        if (empty($this->site_id_list)) {
            throw new AppException('请填写广告位');
        } else {
            foreach ($this->site_id_list as $site_id) {
                if (!is_numeric($site_id)) {
                    throw new AppException('广告位不合法');
                }
            }
            $site_id_list = (new SiteModel())->getAllInSite($this->platform, $this->site_id_list)->pluck('site_id');
            $diff_site_id = collect($this->site_id_list)->diff($site_id_list);
            if ($diff_site_id->isNotEmpty()) {
                throw new AppException('广告位不存在： ' . json_encode($diff_site_id));
            }
        }
    }

}
