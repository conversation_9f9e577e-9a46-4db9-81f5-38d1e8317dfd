<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:46
 */

namespace App\Param\ADServing;

use App\Constant\KuaishouEnum;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Constant\TencentEum;
use App\Constant\ToutiaoEnum;
use App\Container;
use App\Exception\AppException;
use App\Model\RedisModel\BatchADTaskQueueTaskNumModel;
use App\Model\SqlModel\DataMedia\OdsMaterialLowEffectRejectLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\DeviceDownloadHistoryModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\AbstractParam;
use App\Param\ADServing\Kuaishou\Basics\ADOtherSettingContentParam as KuaishouBasicsADOtherSettingContentParam;
use App\Param\ADServing\Tencent\V3\ADOtherSettingContentParam;
use App\Param\ADServing\Toutiao\Basics\ADOtherSettingContentParam as ToutiaoBasicsADOtherSettingContentParam;
use App\Param\ADServing\Toutiao\V2\ADOtherSettingContentParam as ToutiaoV2ADOtherSettingContentParam;
use App\Param\ADServing\Tencent\V3\ADOtherSettingContentParam as TencentV3ADOtherSettingContentParam;
use App\Service\MaterialExpertFilterService;
use App\Service\MaterialThemeFilterService;

class ADTaskCreateParam extends AbstractParam
{
    /**
     * @var ADTaskParam[] $list
     */
    private $list = [];

    /**
     * @var array $account_queue_index_map
     */
    private $account_queue_index_map = [];

    public function addADTaskParam(ADTaskParam $param)
    {
        if (!$param->id) {
            $param->id = count($this->list);
        }
        $this->list[] = $param;
    }

    public function validateMaterial()
    {
        if (count($this->list) <= 0) {
            return;
        }
        // 组装账号分配模型
        $task_all_media_type = [];
        $task_all_platform = [];
        $task_all_game_id = [];
        $task_all_material_file_map = [];
        foreach ($this->list as $task_param) {
            $task_all_platform["{$task_param->platform}"] = 1;
            $task_all_media_type["{$task_param->media_type}"] = 1;
            $task_all_game_id["{$task_param->site_config['game_id']}"] = 1;
            foreach ($task_param->creative_list as $creative) {
                if (isset($creative['video_info'])) {
                    $task_all_material_file_map[$creative['video_info']['id']] = $creative['video_info'];
                } else {
                    $task_all_material_file_map[$creative['image_info']['id']] = $creative['image_info'];
                }
            }
        }
        $task_all_media_type = array_keys($task_all_media_type);
        $task_all_platform = array_keys($task_all_platform);
        $task_all_game_id = array_keys($task_all_game_id);

        if (count($task_all_media_type) > 1) {
            throw new AppException('此次所选广告任务中包含多个媒体类型,请每次只选择一种媒体重启广告任务');
        }

        if (count($task_all_platform) > 1) {
            throw new AppException('此次所选广告任务中包含多个平台,请每次只选择一种平台重启广告任务');
        }

        if (count($task_all_game_id) > 1) {
            throw new AppException('此次所选广告任务中包含多个游戏,请每次只选择一种游戏重启广告任务');
        }


        $error_msg_list = '';

        $material_file_id_list = array_values(array_keys($task_all_material_file_map));

        $result_game = MaterialThemeFilterService::inCorrectGame($task_all_platform[0], $task_all_game_id[0], $material_file_id_list);
        $result_expert = MaterialExpertFilterService::incorrectExpert(
            Container::getSession()->name,
            $task_all_media_type[0],
            $task_all_platform[0],
            $material_file_id_list,
            'batch_ad'
        );

        foreach ($result_game as $file_id => $error) {
            if (!$error) {
                $file_name = basename($task_all_material_file_map[$file_id]['url']);
                $error_msg_list .= "{$file_name}，素材文件id为{$file_id}，在game_id = {$task_all_game_id[0]}的情况下不能使用 <br>";
            }
        }

        foreach ($result_expert as $file_id => $error) {
            if (!$error['status']) {
                $error_msg_list .= "{$error['content']} <br>";
            }
        }

        if ($error_msg_list) {
            throw new AppException($error_msg_list);
        }
    }

    public function judgeCreateTaskMaxNum()
    {
        $task_num = count($this->list);
        if ($task_num > 600) {
            throw new AppException("此次预计生成了{$task_num}条广告任务,任务数量过多,请尽量减少参与叉乘的物料数量");
        }
    }

    public function getAllAccountInfoList()
    {
        $result = [];
        foreach ($this->list as $info) {
            $result[$info->account_id] = $info->account_name;
        }
        return $result;
    }

    public function getAllTargetingInfoList()
    {
        $result = [];
        foreach ($this->list as $info) {
            $result[md5(json_encode($info->targeting))] = $info->targeting_name;
        }
        return $result;
    }

    /**
     * @return ADTaskParam[]
     */
    public function getList()
    {
        return $this->list;
    }

    public function getAccountQueueIndexMap()
    {
        return $this->account_queue_index_map;
    }

    public function init()
    {
        foreach ($this->list as &$task_param) {
            // 初始化广告配置
            $task_param->site_config['agent_id'] = '';
            $task_param->site_config['agent_group'] = '';
            $task_param->site_config['convert_id'] = '';
            $task_param->site_config['download_url'] = '';
            if (!($task_param->media_type == MediaType::TENCENT && $task_param->site_config['plat_id'] == PlatId::MINI)) {
                $task_param->site_config['app_android_channel_package_id'] = '';
            }
            if (isset($task_param->site_config['ext']['asset_ids'])) {
                $task_param->site_config['ext']['asset_ids'] = [];
            }
            // 初始化参数
            $task_param->ad1_name_text = '';
            $task_param->ad2_name_text = '';
            $task_param->ad1_id = 0;
            $task_param->ad2_id = 0;
            $task_param->ad3_ids = [];
            $task_param->agent_id = 0;
            $task_param->site_id = 0;
            $task_param->convert_id = 0;
            $task_param->error_msg = '';
            $task_param->state = 1;
            $task_param->state_code = ADTaskModel::WAIT_PUSH_QUEUE;
            $task_param->task_state = ADTaskModel::TASK_STATE_SURE;
        }

    }

    public function setADTaskQueueIndex()
    {
        // 组装账号分配模型
        $task_all_account_id_map = [];
        $task_all_media_type = [];
        $task_all_platform = [];
        foreach ($this->list as $task_param) {
            $task_all_platform["{$task_param->platform}"] = 1;
            $task_all_media_type["{$task_param->media_type}"] = 1;
            $task_all_account_id_map["{$task_param->account_id}"] = (int)($task_all_account_id_map["{$task_param->account_id}"] ?? 0) + 1;
        }
        $task_all_media_type = array_keys($task_all_media_type);
        $task_all_platform = array_keys($task_all_platform);

        if (count($task_all_media_type) > 1) {
            throw new AppException('此次所选广告任务中包含多个媒体类型,请每次只选择一个媒体重启广告任务');
        }

        if (count($task_all_platform) > 1) {
            throw new AppException('此次所选广告任务中包含多个平台,请每次只选择一个平台重启广告任务');
        }

        $this->account_queue_index_map = $this->list ? (new BatchADTaskQueueTaskNumModel())->getAccountIdQueueIndex(
            $task_all_account_id_map,
            $task_all_platform[0],
            (int)$task_all_media_type[0]
        ) : [];
    }

    /**
     * 设置低效素材拓展参数
     * @param $attempt_material_ids
     */
    public function setLowEffectMaterialAttemptExt($attempt_material_ids)
    {
        if ($attempt_material_ids) {
            $low_effect_file_list = (new MaterialFileModel())->getListByIds($attempt_material_ids);
            if ($low_effect_file_list->isNotEmpty()) {
                $low_effect_file_map = $low_effect_file_list->keyBy('id');
                $low_effect_file_md5_list = $low_effect_file_list->pluck('signature');
                $game_info = (new V2DimGameIdModel())->getDataByGameId($this->list[0]->platform, $this->list[0]->site_config['game_id']);
                $low_effect_file_reason_map = (new OdsMaterialLowEffectRejectLogModel())->getLowMaterialByMD5(
                    $this->list[0]->media_type,
                    $game_info->clique_id,
                    $low_effect_file_md5_list
                )->keyBy('signature');
                foreach ($this->list as $task_param) {
                    $use_low_effect_material = [];
                    foreach ($task_param->creative_list as $creative) {
                        if (isset($creative['video_info'])) {
                            $creative_id = $creative['video_info']['id'];
                        } else {
                            $creative_id = $creative['image_info']['id'];
                        }
                        if (in_array($creative_id, $attempt_material_ids) && isset($low_effect_file_map[$creative_id]->signature)) {
                            $use_low_effect_material[] = [
                                'id' => $creative_id,
                                'md5' => $low_effect_file_map[$creative_id]->signature,
                                'reason' => $low_effect_file_reason_map[$low_effect_file_map[$creative_id]->signature]->reject_reason
                            ];
                        }
                    }
                    $task_param->setExt('use_low_effect_material_state', count($use_low_effect_material) > 0 ? 1 : 0);
                    $task_param->setExt('attempt_use_low_effect_material', $use_low_effect_material);
                }
            }
        }
    }

    /**
     * 根据业务逻辑重新分配广告任务结果
     */
    public function resetListForBusinessLogic()
    {
        if (!$this->list) {
            return;
        }
        $media_type = $this->list[0]->media_type;
        $media_agent_type = $this->list[0]->media_agent_type;

        // 头条信息流1.0
        if ($media_type == MediaType::TOUTIAO && $media_agent_type == 0) {
            /* @var ToutiaoBasicsADOtherSettingContentParam $toutiao_other_setting */
            $toutiao_other_setting = $this->list[0]->other_setting;
            if ($toutiao_other_setting['promotion_type'] == ToutiaoEnum::PROMOTION_TYPE_HOT) {
                /* @var ADTaskParam[] $new_list */
                $new_list = [];
                $origin_list = [];
                foreach ($this->list as $task) {
                    $origin_list[$task->account_id] = $task;
                }

                foreach ($origin_list as $account_id => $origin_task) {
                    /* @var ToutiaoBasicsADOtherSettingContentParam $toutiao_origin_task_other_setting */
                    $toutiao_origin_task_other_setting = $origin_task->other_setting;
                    if ($toutiao_origin_task_other_setting['hot_material_map'][$account_id] ?? false) {
                        foreach ($toutiao_origin_task_other_setting['hot_material_map'][$account_id] as $video_info) {
                            $new_task = new ADTaskParam((array)$origin_task);
                            $new_task->targeting = $origin_task->targeting;
                            $new_task->setting = $origin_task->setting;
                            /* @var ToutiaoBasicsADOtherSettingContentParam $toutiao_new_task_other_setting */
                            $toutiao_new_task_other_setting = $new_task->other_setting;
                            $toutiao_new_task_other_setting['hot_material_map'] = [$account_id => [$video_info]];
                            $new_task->other_setting = $toutiao_new_task_other_setting;
                            $new_list[] = $new_task;
                        }
                    } else {
                        throw new AppException("账号{$account_id}没有选择原生加热素材");
                    }
                }

                $this->list = $new_list;
            }
        }

        // 头条信息流2.0
        if ($media_type == MediaType::TOUTIAO && $media_agent_type == 2) {
            /* @var ToutiaoV2ADOtherSettingContentParam $toutiao_v2_other_setting */
            $toutiao_v2_other_setting = $this->list[0]->other_setting;
            if ($toutiao_v2_other_setting['origin_ad_type'] == ToutiaoEnum::ORIGIN_AD_TYPE_HOT) {
                /* @var ADTaskParam[] $new_list */
                $new_list = [];
                $origin_list = [];
                foreach ($this->list as $task) {
                    $origin_list[$task->account_id] = $task;
                }

                foreach ($origin_list as $account_id => $origin_task) {
                    /* @var ToutiaoV2ADOtherSettingContentParam $toutiao_v2_origin_task_other_setting */
                    $toutiao_v2_origin_task_other_setting = $origin_task->other_setting;
                    if ($toutiao_v2_origin_task_other_setting['hot_material_map'][$account_id] ?? false) {
                        foreach ($toutiao_v2_origin_task_other_setting['hot_material_map'][$account_id] as $video_info) {
                            $new_task = new ADTaskParam((array)$origin_task);
                            $new_task->targeting = $origin_task->targeting;
                            $new_task->setting = $origin_task->setting;
                            /* @var ToutiaoV2ADOtherSettingContentParam $toutiao_v2_new_task_other_setting */
                            $toutiao_v2_new_task_other_setting = $new_task->other_setting;
                            $toutiao_v2_new_task_other_setting['hot_material_map'] = [$account_id => [$video_info]];
                            $new_task->other_setting = $toutiao_v2_new_task_other_setting;
                            $new_list[] = $new_task;
                        }
                    } else {
                        throw new AppException("账号{$account_id}没有选择原生加热素材");
                    }
                }

                $this->list = $new_list;
            }
            if ($toutiao_v2_other_setting['origin_ad_type'] == ToutiaoEnum::ORIGIN_AD_TYPE_STAR) {
                /* @var ADTaskParam[] $new_list */
                $new_list = [];
                $origin_list = [];
                foreach ($this->list as $task) {
                    $origin_list[$task->account_id] = $task;
                }
                foreach ($origin_list as $account_id => $origin_task) {
                    /* @var ToutiaoV2ADOtherSettingContentParam $toutiao_v2_origin_task_other_setting */
                    $toutiao_v2_origin_task_other_setting = $origin_task->other_setting;
                    if ($toutiao_v2_origin_task_other_setting['star_deliver_map'][$account_id] ?? false) {
                        $new_video_by_star_task = [];

                        $task_aweme_index_map = [];

                        foreach ($toutiao_v2_origin_task_other_setting['star_deliver_map'][$account_id] as $video_info) {
                            // 联投素材按联投任务ID进行分配
                            $task_aweme_index_map["{$video_info['star_task_id']}-{$video_info['aweme_id']}"] = $task_aweme_index_map["{$video_info['star_task_id']}-{$video_info['aweme_id']}"] ?? 1;

                            $key = "{$video_info['star_task_id']}-{$video_info['aweme_id']}-{$task_aweme_index_map["{$video_info['star_task_id']}-{$video_info['aweme_id']}"]}";

                            $new_video_by_star_task[$key][] = $video_info;

                            if (count($new_video_by_star_task[$key]) >= $toutiao_v2_origin_task_other_setting['star_material_num']) {
                                $task_aweme_index_map["{$video_info['star_task_id']}-{$video_info['aweme_id']}"]++;
                            }
                        }
                        foreach ($new_video_by_star_task as $uni => $video_item) {
                            $star_task_id = explode('-', $uni)[0];
                            $new_task = new ADTaskParam((array)$origin_task);
                            $new_task->targeting = $origin_task->targeting;
                            $new_task->setting = $origin_task->setting;
                            /* @var ToutiaoV2ADOtherSettingContentParam $toutiao_v2_new_task_other_setting */
                            $toutiao_v2_new_task_other_setting = $new_task->other_setting;
                            $toutiao_v2_new_task_other_setting['star_task_id'] = strval($star_task_id);
                            $toutiao_v2_new_task_other_setting['star_deliver_map'] = [$account_id => $video_item];
                            $new_task->other_setting = $toutiao_v2_new_task_other_setting;
                            $new_list[] = $new_task;
                        }
                    } else {
                        throw new AppException("账号{$account_id}没有选择联投素材");
                    }
                }

                $this->list = $new_list;
            }
        }

        if ($media_type == MediaType::TENCENT && $media_agent_type == 2) {
            /* @var TencentV3ADOtherSettingContentParam $tencent_v3_other_setting */
            $tencent_v3_other_setting = $this->list[0]->other_setting;

            // 腾讯视频号互选素材的任务分配
            if (in_array(TencentEum::SITE_SET_CHANNELS, $tencent_v3_other_setting['site_set']) &&
                count($tencent_v3_other_setting['site_set']) === 1 && $tencent_v3_other_setting['huxuan_material_switch']) {

                /* @var ADTaskParam[] $new_list */
                $new_list = [];
                $origin_list = [];
                foreach ($this->list as $task) {
                    $origin_list[$task->account_id] = $task;
                }

                foreach ($origin_list as $account_id => $origin_task) {
                    /* @var ADOtherSettingContentParam $tencent_v3_origin_task_other_setting */
                    $tencent_v3_origin_task_other_setting = $origin_task->other_setting;
                    if ($tencent_v3_origin_task_other_setting['huxuan_material_map'][$account_id] ?? false) {
                        foreach ($tencent_v3_origin_task_other_setting['huxuan_material_map'][$account_id] as $video_info) {
                            $new_task = new ADTaskParam((array)$origin_task);
                            $new_task->targeting = $origin_task->targeting;
                            $new_task->setting = $origin_task->setting;
                            /* @var ADOtherSettingContentParam $tencent_v3_new_task_other_setting */
                            $tencent_v3_new_task_other_setting = $new_task->other_setting;
                            $tencent_v3_new_task_other_setting['huxuan_material_map'] = [$account_id => [$video_info]];
                            $new_task->other_setting = $tencent_v3_new_task_other_setting;
                            $new_list[] = $new_task;
                        }
                    } else {
                        throw new AppException("账号{$account_id}没有选择互选素材");
                    }
                }

                $this->list = $new_list;
            }
        }

        // 快手信息流
        if ($media_type == MediaType::KUAISHOU && $media_agent_type == 0) {
            /* @var KuaishouBasicsADOtherSettingContentParam $toutiao_other_setting */
            $other_setting = $this->list[0]->other_setting;
            if (
                in_array($other_setting['campaign_type'], [KuaishouEnum::TYPE_APP, KuaishouEnum::TYPE_CLUES]) &&
                $other_setting['outer_loop_native'] > 0 && ($other_setting['talent_type'] == KuaishouEnum::JUXING_TALENT || ($other_setting['talent_type'] == KuaishouEnum::BLUE_V_TALENT && $other_setting['talent_video_type'] == KuaishouEnum::TALENT_MEDIA))
            ) {
                /* @var ADTaskParam[] $new_list */
                $new_list = [];
                $origin_list = [];
                foreach ($this->list as $task) {
                    $origin_list[$task->account_id] = $task;
                }
                $ids = 0;
                foreach ($origin_list as $account_id => $origin_task) {
                    /* @var KuaishouBasicsADOtherSettingContentParam $kuaishou_origin_task_other_setting */
                    $kuaishou_origin_task_other_setting = $origin_task->other_setting;
                    // 如果是蓝V视频上传 且是媒体素材视频，blue_v_material_map  否则是聚星 native_material_map
                    $kuaishou_origin_task_other_setting_material_map = ($other_setting['talent_type'] == KuaishouEnum::BLUE_V_TALENT && $other_setting['talent_video_type'] == KuaishouEnum::TALENT_MEDIA)
                        ? $kuaishou_origin_task_other_setting['blue_v_material_map']
                        : $kuaishou_origin_task_other_setting['native_material_map'];
                    if ($kuaishou_origin_task_other_setting_material_map[$account_id] ?? false) {
                        if ($kuaishou_origin_task_other_setting['native_material_type'] == 0) {
                            foreach ($kuaishou_origin_task_other_setting['native_material_user_map'][$account_id] as $user_id => $video_item_list) {
                                $native_material_num = $kuaishou_origin_task_other_setting['native_material_type_radio'] == 1 && $kuaishou_origin_task_other_setting['native_material_num'] ? $kuaishou_origin_task_other_setting['native_material_num'] : count($video_item_list);
                                $video_item_addr = array_chunk($video_item_list, $native_material_num);
                                foreach ($video_item_addr as $video_item) {
                                    // 账号下N个达人生成N条任务
                                    $new_task = new ADTaskParam((array)$origin_task);
                                    $new_task->id = $ids;
                                    $ids++;
                                    $new_task->targeting = $origin_task->targeting;
                                    $new_task->setting = $origin_task->setting;
                                    /* @var KuaishouBasicsADOtherSettingContentParam $kuaishou_new_task_other_setting */
                                    $kuaishou_new_task_other_setting = $new_task->other_setting;
                                    // 蓝v号视频下是媒体素材视频
                                    if ($other_setting['talent_type'] == KuaishouEnum::BLUE_V_TALENT && $other_setting['talent_video_type'] == KuaishouEnum::TALENT_MEDIA) {
                                        $kuaishou_new_task_other_setting['blue_v_material_map'] = [$account_id => $video_item];
                                    } else {
                                        $kuaishou_new_task_other_setting['native_material_map'] = [$account_id => $video_item];
                                    }
                                    $new_task->other_setting = $kuaishou_new_task_other_setting;
                                    $new_list[] = $new_task;
                                }
                            }
                        } else {
                            foreach ($kuaishou_origin_task_other_setting_material_map[$account_id] as $user_id => $video_info) {
                                // 账号下N个达人生成N条任务
                                $new_task = new ADTaskParam((array)$origin_task);
                                $new_task->id = $ids;
                                $ids++;
                                $new_task->targeting = $origin_task->targeting;
                                $new_task->setting = $origin_task->setting;
                                /* @var KuaishouBasicsADOtherSettingContentParam $kuaishou_new_task_other_setting */
                                $kuaishou_new_task_other_setting = $new_task->other_setting;
                                // 蓝v号视频下是媒体素材视频
                                if ($other_setting['talent_type'] == KuaishouEnum::BLUE_V_TALENT && $other_setting['talent_video_type'] == KuaishouEnum::TALENT_MEDIA) {
                                    $kuaishou_new_task_other_setting['blue_v_material_map'] = [$account_id => [$video_info]];
                                } else {
                                    $kuaishou_new_task_other_setting['native_material_map'] = [$account_id => [$video_info]];
                                }
                                $new_task->other_setting = $kuaishou_new_task_other_setting;
                                $new_list[] = $new_task;
                            }
                        }

                    } else {
                        throw new AppException("账号{$account_id}没有选择达人素材");
                    }
                }

                $this->list = $new_list;
            }
        }
    }

}
