<?php
/**
 * Created by PhpStorm.
 * User: zzh
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADServing;

use App\Param\AbstractParam;
use App\Container;
use App\Exception\AppException;

class ADStarMaterialPacketParam extends AbstractParam
{
    public $id;
    public $name;
    public $package;
    public $material_list = [];
    public $creator;
    public $state = 1;
    public $update_time;
    public $create_time;

    public function paramHook()
    {
        $this->creator = Container::getSession()->name;

        if (!$this->name) {
            throw new AppException('联投素材包名称不能为空');
        }
        if (!$this->package) {
            throw new AppException('联投素材包package不能为空');
        }
    }

    public function toInsertData()
    {
        return [
            'material_list' => json_encode($this->material_list, JSON_UNESCAPED_UNICODE),
            'name' => $this->name,
            'package' => $this->package,
            'creator' => $this->creator,
            'state' => $this->state
        ];
    }
}
