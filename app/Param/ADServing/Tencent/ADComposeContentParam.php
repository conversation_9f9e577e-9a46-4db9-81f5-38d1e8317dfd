<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Tencent;

use App\Param\ADServing\AbstractADComposeContentParam;

class ADComposeContentParam extends Abstract<PERSON>ComposeContentParam
{
    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
    }

    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
    }

    protected function unionParamValidateJudge()
    {
        // TODO: Implement unionParamValidateJudge() method.
    }

    public function getMaterialFileNormalList(): array
    {
        return [];
    }
}
