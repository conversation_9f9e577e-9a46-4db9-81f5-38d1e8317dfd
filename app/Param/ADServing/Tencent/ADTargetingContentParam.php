<?php
/**
 * Created by <PERSON>p<PERSON>torm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Tencent;

use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsTencentUnionPositionPackagesLogModel;
use App\Param\ADServing\AbstractADTargetingContentParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\Tencent\AudiencePackageCreateParam;

class ADTargetingContentParam extends AbstractADTargetingContentParam
{

    public $search_scene_switch = '0';
    public $search_expand_targeting_switch = 'SEARCH_EXPAND_TARGETING_SWITCH_UNKNOWN';
    public $search_expand_bidword_switch = 'close';
    public $search_expand_bidword_list = [];

    /**
     *  geo_location: {
     *      regions_none: 'buxian',
     *      regions_map: [],
     *      regions: [],
     *      location_types: ['LIVE_IN']
     *  }
     * @var array
     */
    public $geo_location = [];

    /**
     * age: {
     *      age_none: 'buxian',
     *      min: '',
     *      max: ''
     * }
     * @var array
     */
    public $age = [];
    public $gender = '';
    public $education_none = '';
    public $education = [];
    public $financial_situation_none = '';
    public $financial_situation = [];

//    public $consumption_status_none = '';
//    public $consumption_status = [];
    public $new_device_none = '';
    public $new_device = [];
    public $device_brand_model_type = 'buxian';
    public $device_brand_model_list = [];
    public $zidingyirenqun = '';
    public $custom_audience_map = [];
    public $custom_audience = [];
    public $excluded_custom_audience_map = [];
    public $excluded_custom_audience = [];
    public $behavior_or_interest_none = '';
    /**
     * behavior_or_interest: {
     *      interest: {
     *          category_id_list: [],
     *          category_id_list_map: [],
     *          keyword_list: []
     *      },
     *      behavior: {
     *          category_id_list: [],
     *          category_id_list_map: [],
     *          keyword_list: [],
     *          scene: [],
     *          time_window: '',
     *          intensity: []
     *      },
     *      intention: {
     *          targeting_tags: []
     *      }
     * },
     * @var array
     */
    public $behavior_or_interest = [];
    public $app_install_status = '';
    public $network_scene_none = '';
    public $network_scene = [];
    public $user_os_none = '';
    public $user_os_other = [];
    public $user_os_ios = [];
    public $user_os_android = [];
    public $excluded_os = false;
    public $network_type_none = '';
    public $network_type = [];
    public $network_operator_none = '';
    public $network_operator = [];
    public $device_price_none = '';
    public $device_price = [];
    public $mobile_union_industry_none = [];
    public $mobile_union_industry = '';
    public $expand_enabled = '';
    public $expand_targeting = [];
    public $auto_audience = '';
    public $cold_start_audience = [];
    public $cold_start_audience_map = [];
    // 流量包
    public $flow_package_map = [];
    public $flow_package = [];
    public $exclude_flow_package_map = [];
    public $exclude_flow_package = [];
    public $flow_package_none = '0';

    // 微信再营销
    public $wechat_ad_behavior_status = 'buxian';
    public $wechat_ad_behavior_behavior = [];

    public $game_consumption_level_none = 'buxian';
    public $game_consumption_level = [];

    public function __construct($property = [])
    {
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === null) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if (count($this->cold_start_audience) > 10) {
            $error_msg_list[] = [
                'key' => 'auto_audience',
                'value' => 0,
                'msg' => "扩量种子人群不能超过10个"
            ];
        }

        if ($this->behavior_or_interest['behavior']['category_id_list'] || $this->behavior_or_interest['behavior']['keyword_list']) {
            if (!$this->behavior_or_interest['behavior']['scene']) {
                $error_msg_list[] = [
                    'key' => 'behavior_or_interest_none',
                    'value' => 0,
                    'msg' => "请选择行为场景"
                ];
            }
            if (!$this->behavior_or_interest['behavior']['time_window']) {
                $error_msg_list[] = [
                    'key' => 'behavior_or_interest_none',
                    'value' => 0,
                    'msg' => "请选择行为时效性"
                ];
            }
            if (!$this->behavior_or_interest['behavior']['intensity']) {
                $error_msg_list[] = [
                    'key' => 'behavior_or_interest_none',
                    'value' => 0,
                    'msg' => "请选择行为强度"
                ];
            }
        }

        if ($this->device_brand_model_type != 'buxian' && !$this->device_brand_model_list) {
            $error_msg_list[] = [
                'key' => 'device_brand_model_type',
                'value' => 0,
                'msg' => "设备品牌不能为空"
            ];
        }

        return $error_msg_list;

    }

    /**
     * 参数格式化
     * @param int $state
     */
    public function format($state)
    {
        ksort($this->age);
        ksort($this->geo_location);
        ksort($this->behavior_or_interest);

        // 系统优选的话直接清空所有人群与扩量
        if ($this->auto_audience) {
            $this->custom_audience = [];
            $this->custom_audience_map = [];
            $this->cold_start_audience = [];
            $this->cold_start_audience_map = [];
            $this->expand_targeting = [];
            $this->behavior_or_interest['interest']['category_id_list'] = [];
            $this->behavior_or_interest['interest']['category_id_list_map'] = [];
            $this->behavior_or_interest['interest']['keyword_list'] = [];
            $this->behavior_or_interest['behavior']['category_id_list'] = [];
            $this->behavior_or_interest['behavior']['category_id_list_map'] = [];
            $this->behavior_or_interest['behavior']['keyword_list'] = [];
        }

        if ($this->behavior_or_interest['interest']['category_id_list_map']) {
            $interest_category_id_list = array_column(
                $this->behavior_or_interest['interest']['category_id_list_map'],
                'id'
            );
            $this->behavior_or_interest['interest']['category_id_list'] = $interest_category_id_list;
        } else {
            if ($this->behavior_or_interest['interest']['category_id_list']) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->behavior_or_interest['interest']['category_id_list']);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->behavior_or_interest['interest']['category_id_list'] = [];
                }
            } else {
                $this->behavior_or_interest['interest']['category_id_list'] = [];
                $this->behavior_or_interest['interest']['category_id_list_map'] = [];
            }
        }

        if ($this->behavior_or_interest['behavior']['category_id_list_map']) {
            $behavior_category_id_list = array_column(
                $this->behavior_or_interest['behavior']['category_id_list_map'],
                'id'
            );
            $this->behavior_or_interest['behavior']['category_id_list'] = $behavior_category_id_list;
        } else {
            if ($this->behavior_or_interest['behavior']['category_id_list']) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->behavior_or_interest['behavior']['category_id_list']);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->behavior_or_interest['behavior']['category_id_list'] = [];
                }
            } else {
                $this->behavior_or_interest['behavior']['category_id_list'] = [];
                $this->behavior_or_interest['behavior']['category_id_list_map'] = [];
            }
        }

        if ($this->behavior_or_interest['intention']['targeting_tags_map']) {
            $intention_category_id_list = array_column(
                $this->behavior_or_interest['intention']['targeting_tags_map'],
                'id'
            );
            $this->behavior_or_interest['intention']['targeting_tags'] = $intention_category_id_list;
        } else {
            if ($this->behavior_or_interest['intention']['targeting_tags']) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->behavior_or_interest['behavior']['targeting_tags']);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->behavior_or_interest['intention']['targeting_tags'] = [];
                }
            } else {
                $this->behavior_or_interest['intention']['targeting_tags'] = [];
                $this->behavior_or_interest['intention']['targeting_tags_map'] = [];
            }
        }

        if ($this->geo_location['regions_map']) {
            $regions_id_list = array_column(
                $this->geo_location['regions_map'],
                'id'
            );
            $this->geo_location['regions'] = array_map(function ($ele) {
                return (int)$ele;
            }, $regions_id_list);
            sort($this->geo_location['regions']);
        } else {
            if ($this->geo_location['regions']) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->geo_location['regions']);
                    $this->geo_location['regions'] = array_map(function ($ele) {
                        return (int)$ele;
                    }, $this->geo_location['regions']);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->geo_location['regions'] = [];
                }
            } else {
                $this->geo_location['regions'] = [];
                $this->geo_location['regions_map'] = [];
            }
        }

        if ($this->custom_audience_map) {
            $this->custom_audience_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->custom_audience_map);
            $custom_audience_id_map = array_map(function ($el) {
                return (int)$el;
            }, array_column(
                $this->custom_audience_map,
                'audience_id'
            ));
            sort($custom_audience_id_map);
            $this->custom_audience = $custom_audience_id_map;
        } else {
            if ($this->custom_audience) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->custom_audience);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->custom_audience = [];
                }
            } else {
                $this->custom_audience = [];
                $this->custom_audience_map = [];
            }
        }

        if ($this->excluded_custom_audience_map) {
            $this->excluded_custom_audience_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->excluded_custom_audience_map);
            $excluded_custom_audience_id_map = array_map(function ($el) {
                return (int)$el;
            }, array_column(
                $this->excluded_custom_audience_map,
                'audience_id'
            ));
            sort($excluded_custom_audience_id_map);
            $this->excluded_custom_audience = $excluded_custom_audience_id_map;
        } else {
            if ($this->excluded_custom_audience) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->excluded_custom_audience);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->excluded_custom_audience = [];
                }
            } else {
                $this->excluded_custom_audience = [];
                $this->excluded_custom_audience_map = [];
            }
        }

        if ($this->cold_start_audience_map) {
            $this->cold_start_audience_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->cold_start_audience_map);
            $cold_start_audience_id_map = array_column(
                $this->cold_start_audience_map,
                'audience_id'
            );
            sort($cold_start_audience_id_map);
            $this->cold_start_audience = $cold_start_audience_id_map;
        } else {
            if ($this->cold_start_audience) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->cold_start_audience);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->cold_start_audience = [];
                }
            } else {
                $this->cold_start_audience = [];
                $this->cold_start_audience_map = [];
            }
        }

        if ($this->flow_package_map) {
            $this->flow_package_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->flow_package_map);
            $this->flow_package = array_column(
                $this->flow_package_map,
                'flow_package_id'
            );
            sort($this->flow_package);
        } else {
            if ($this->flow_package) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->flow_package);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->flow_package = [];
                }
            } else {
                $this->flow_package = [];
                $this->flow_package_map = [];
            }
        }

        if ($this->exclude_flow_package_map) {
            $this->exclude_flow_package_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->exclude_flow_package_map);
            $this->exclude_flow_package = array_column(
                $this->exclude_flow_package_map,
                'flow_package_id'
            );
            sort($this->exclude_flow_package);
        } else {
            if ($this->exclude_flow_package) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->exclude_flow_package);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->exclude_flow_package = [];
                }
            } else {
                $this->exclude_flow_package = [];
                $this->exclude_flow_package_map = [];
            }
        }

        if ($this->device_brand_model_list && $this->device_brand_model_type == 'buxian') {
            $this->device_brand_model_list = [];
        }
        if ($this->financial_situation_none && $this->financial_situation_none == 'buxian') {
            $this->financial_situation = [];
        }
//        if ($this->consumption_status_none && $this->consumption_status_none == 'buxian') {
//            $this->consumption_status = [];
//        }
        if ($this->new_device_none && $this->new_device_none == 'buxian') {
            $this->new_device = [];
        }
        if ($this->network_scene_none && $this->network_scene_none == 'buxian') {
            $this->network_scene = [];
        }
        if ($this->user_os_none && $this->user_os_none == 'buxian') {
            $this->user_os_ios = [];
            $this->user_os_android = [];
            $this->user_os_other = [];
        }
        if ($this->network_type_none && $this->network_type_none == 'buxian') {
            $this->network_type = [];
        }
        if ($this->network_operator_none && $this->network_operator_none == 'buxian') {
            $this->network_operator = [];
        }
        if ($this->device_price_none && $this->device_price_none == 'buxian') {
            $this->device_price = [];
        }
    }

    /**
     * 生成此定向包的md5特殊编码
     * @return string
     */
    public function toMd5()
    {
        $md5_array = $this->toArray();
        unset($md5_array['geo_location']['regions_none']);
        unset($md5_array['age']['age_none']);
        unset($md5_array['education_none']);
        unset($md5_array['education_none']);
        unset($md5_array['financial_situation_none']);
        unset($md5_array['consumption_type_none']);
        unset($md5_array['game_consumption_level_none']);
//        unset($md5_array['consumption_status_none']);
        unset($md5_array['new_device_none']);
        unset($md5_array['zidingyirenqun']);
        unset($md5_array['behavior_or_interest_none']);
        unset($md5_array['network_scene_none']);
        unset($md5_array['user_os_none']);
        unset($md5_array['network_type_none']);
        unset($md5_array['network_operator_none']);
        unset($md5_array['device_price_none']);
        unset($md5_array['mobile_union_industry_none']);
        unset($md5_array['geo_location']['regions_map']);
        unset($md5_array['custom_audience_map']);
        unset($md5_array['excluded_custom_audience_map']);
        unset($md5_array['cold_start_audience_map']);
        unset($md5_array['md5']);
        ksort($md5_array);
        return md5(json_encode($md5_array, JSON_NUMERIC_CHECK));
    }

    /**
     * 获取需要推送的人群包id
     * @return array
     */
    public function getAllAudienceIdList()
    {
        $data = array_merge(
            $this->custom_audience ?: [],
            $this->excluded_custom_audience ?: []
        );
        if ($this->expand_enabled === 'true') {
            $data = array_merge($data, $this->cold_start_audience ?: []);
        }
        return $data;
    }

    /**
     * 生成定向给mq的数据
     * @return AbstractADTargetingContentParam
     */
    public function toMQData()
    {
        $this->custom_audience_map = [];
        $this->excluded_custom_audience_map = [];
        $this->cold_start_audience_map = [];
        $this->behavior_or_interest['interest']['category_id_list_map'] = [];
        $this->behavior_or_interest['behavior']['category_id_list_map'] = [];
        return $this;
    }

    /**
     * 获取所有流量包id
     * @return array
     */
    public function getAllFlowIdMd5MapList()
    {
        $data = [];
        foreach ($this->flow_package_map as $key => $value) {
            $data[$value['union_position_id_md5']]['union_package_id'] = $value['union_package_id'];
        }
        foreach ($this->exclude_flow_package_map as $key => $value) {
            $data[$value['union_position_id_md5']]['union_package_id'] = $value['union_package_id'];
        }
        $md5_list = array_keys($data);
        if ($md5_list) {
            $all_data = (new OdsTencentUnionPositionPackagesLogModel())->getListByMd5($md5_list);
            if ($all_data) {
                $all_data = $all_data->keyBy('union_position_id_md5');
                foreach ($all_data as $md5 => $value) {
                    $data[$md5]['union_position_id_list'] = json_decode($value->union_position_id_list);
                    $data[$md5]['union_package_name'] = $value->union_package_name;
                    $data[$md5]['last_modified_time'] = $value->last_modified_time;
                    $data[$md5]['union_package_type'] = $value->union_package_type;
                    $data[$md5]['created_time'] = $value->created_time;
                    $data[$md5]['union_position_id_update_rule_id'] = $value->union_position_id_update_rule_id;
                }
            }
        }
        return $data;
    }

    /**
     * 重置流量包
     * @param array $flow_package_map
     */
    public function resetFlowPackage(array $flow_package_map)
    {
        $this->flow_package_map = array_column($this->flow_package_map, null, 'union_position_id_md5');
        $this->exclude_flow_package_map = array_column($this->exclude_flow_package_map, null, 'union_position_id_md5');
        foreach ($flow_package_map as $key => $value) {
            if (isset($this->flow_package_map[$value['union_position_id_md5']])) {
                $this->flow_package_map[$value['union_position_id_md5']] = $value;
            }
            if (isset($this->exclude_flow_package_map[$value['union_position_id_md5']])) {
                $this->exclude_flow_package_map[$value['union_position_id_md5']] = $value;
            }
        }
        $this->flow_package_map = array_values($this->flow_package_map);
        $this->exclude_flow_package_map = array_values($this->exclude_flow_package_map);

        if ($this->flow_package_map) {
            $this->flow_package = array_column(
                $this->flow_package_map,
                'union_package_id'
            );
            sort($this->flow_package);
        } else {
            $this->flow_package = [];
        }

        if ($this->exclude_flow_package_map) {
            $this->exclude_flow_package = array_column(
                $this->exclude_flow_package_map,
                'union_package_id'
            );
            sort($this->exclude_flow_package);
        } else {
            $this->exclude_flow_package = [];
        }
    }

    /**
     * 获取媒体接口格式的定向包内容数组
     * @return array
     */
    public function getMediaFormatTargetingArray()
    {
        $data = $this->toArray();

        if ($data['wechat_ad_behavior_status'] == 'include' && $data['wechat_ad_behavior_behavior']) {
            $data['wechat_ad_behavior']['actions'] = $this->wechat_ad_behavior_behavior;
        } elseif ($data['wechat_ad_behavior_status'] == 'exclude' && $data['wechat_ad_behavior_behavior']) {
            $data['wechat_ad_behavior']['excluded_actions'] = $this->wechat_ad_behavior_behavior;
        }

        $media_targeting_param = new AudiencePackageCreateParam($data);

        return $media_targeting_param->toBody();
    }
}
