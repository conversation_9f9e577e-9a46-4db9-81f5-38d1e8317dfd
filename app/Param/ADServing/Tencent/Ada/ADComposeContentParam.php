<?php
/**
 * Created by <PERSON>pStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Tencent\Ada;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Constant\TencentEum;
use App\Constant\TencentWeChatADFaceList;
use App\Exception\AppException;
use App\Logic\DSP\ADServingLogic;
use App\Model\SqlModel\Tanwan\V2DimGameStandardValueDaysIncolumnModel;
use App\Param\ADServing\Tencent\ADComposeContentParam as TencentADComposeContentParam;
use App\Param\ADServing\Tencent\ADTargetingContentParam;
use App\Service\GamePriceRangeFilterService;
use App\Service\TencentVerifyService;
use App\Utils\Helpers;
use Closure;


class ADComposeContentParam extends TencentADComposeContentParam
{
    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
    }

    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
    }

    private $template_validate_rules = [];

    public function paramHook($property = [])
    {
        $this->template_validate_rules = [
            '641' => [
                '__validate' => function () {
                    if (!$this->material_list->group_image_list) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => '没有选择组图素材'
                        ];
                    }
                    if ($this->compose_config->pic_num_in_ad != 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                            'key' => 'pic_num_in_ad',
                            'value' => $this->compose_config->pic_num_in_ad,
                            'msg' => '每广告只能1个图片'
                        ];
                    }
                    foreach ($this->material_list->group_image_list as $group_image_info) {
                        if (count($group_image_info['image_list']) != 3) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => 0,
                                'msg' => "请选择3组图素材"
                            ];
                        }
                        if (!($group_image_info['width'] == 800 && $group_image_info['height'] == 800)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => 0,
                                'msg' => "{$group_image_info['url']}不符合800*800的尺寸"
                            ];
                        }
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                        if ($len > 80) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => 0,
                                'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                            ];
                        }
                        if ($this->getFaceNum($value) > 4) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案有大于4个表情"
                            ];
                        }
                    }
                    return $error_msg_list ?? [];
                },
            ],
            '642' => [
                '__validate' => function () {
                    if (!$this->material_list->group_image_list) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => '没有选择组图素材'
                        ];
                    }
                    if ($this->compose_config->pic_num_in_ad != 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                            'key' => 'pic_num_in_ad',
                            'value' => $this->compose_config->pic_num_in_ad,
                            'msg' => '每广告只能1个图片'
                        ];
                    }
                    foreach ($this->material_list->group_image_list as $group_image_info) {
                        if (count($group_image_info['image_list']) != 4) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => 0,
                                'msg' => "请选择4组图素材"
                            ];
                        }
                        if (!($group_image_info['width'] == 800 && $group_image_info['height'] == 800)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $group_image_info['id'],
                                'msg' => "{$group_image_info['url']}不符合800*800的尺寸"
                            ];
                        }
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                        if ($len > 80) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => 0,
                                'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                            ];
                        }
                        if ($this->getFaceNum($value) > 4) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案有大于4个表情"
                            ];
                        }
                    }
                    return $error_msg_list ?? [];
                }
            ],
            '643' => [
                '__validate' => function () {
                    if ($this->compose_config->pic_num_in_ad != 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                            'key' => 'pic_num_in_ad',
                            'value' => $this->compose_config->pic_num_in_ad,
                            'msg' => '每广告只能1个图片'
                        ];
                    }
                    if (!$this->material_list->group_image_list) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => '没有选择组图素材'
                        ];
                    }
                    foreach ($this->material_list->group_image_list as $group_image_info) {
                        if (count($group_image_info['image_list']) != 6) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => 0,
                                'msg' => "请选择6组图素材"
                            ];
                        }
                        if (!($group_image_info['width'] == 800 && $group_image_info['height'] == 800)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $group_image_info['id'],
                                'msg' => "{$group_image_info['url']}不符合800*800的尺寸"
                            ];
                        }
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                        if ($len > 80) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => 0,
                                'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                            ];
                        }
                        if ($this->getFaceNum($value) > 4) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案有大于4个表情"
                            ];
                        }
                    }
                    return $error_msg_list ?? [];
                }
            ],
        ];
    }

    /**
     * @param $template_id
     * @return array|mixed
     */
    private function templateValidate($template_id)
    {
        if ($call = $this->template_validate_rules[$template_id]) {
            if (isset($call['__validate']) && $call['__validate'] instanceof Closure) {
                return call_user_func($call['__validate'], $template_id);
            }
        }
        return [];
    }

    private function getAgentGroup(ADOtherSettingContentParam $other_setting)
    {
        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            return AgentGroup::TENCENT_CHANNELS;
        }
        // 这里是还没有可以建site的，所以先用数组
        if ($this->site_config->plat_id == PlatId::MINI) {
            return AgentGroup::ADQ_WX_MINI_GAME;
        }
        return AgentGroup::TENCENT;
    }

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        if ($other_setting->template_id) {
            $error_msg_list = $this->apiValidate();
        }

        if ($this->site_config->plat_id == 7 && $this->site_config->convert_source_type != 'AD_CONVERT_SOURCE_TYPE_H5_API') {
            throw new AppException('小游戏只能选择落地页API(H5)的转化来源，请重新选择小游戏来刷新转化来源');
        }

        foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {
            /* @var ADTargetingContentParam $targeting */
            if ($other_setting->site_set_none !== 'auto' && !in_array(TencentEum::SITE_SET_MOBILE_UNION, $other_setting->site_set)
                && (int)$targeting->flow_package_none === 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                    'key' => 'flow_package_none',
                    'source_id' => $targeting_id,
                    'value' => 0,
                    'msg' => '非优量汇版位或自动版位，不可使用流量包，请关闭'
                ];
            }
        }

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
            /* @var ADSettingContentParam $setting */

            if (!$setting->bid_amount || !is_array($setting->bid_amount)) {
                $bid_amount_mode = 0;
            } else {
                if ((int)$setting->bid_amount_mode === 1) {
                    $bid_amount_mode = $setting->bid_amount[1];
                } else {
                    $bid_amount_mode = max($setting->bid_amount);
                }
            }

            $expected_roi = $setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'] ?? '';

            $error = GamePriceRangeFilterService::isCorrectPrice(
                MediaType::TENCENT,
                $this->platform,
                $this->site_config->game_id,
                $this->site_config->game_type,
                $this->getAgentGroup($other_setting),
                ConvertType::MEDIA[MediaType::TENCENT][$this->site_config->convert_type],
                $this->site_config->deep_external_action,
                $bid_amount_mode,
                $expected_roi
            );

            if ($error) {
                if ($bid_amount_mode && isset($error['bid'])) {
                    $error_msg_list[] = [
                        'key' => 'bid_amount',
                        'value' => $setting->bid_amount,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => $error['bid']
                    ];
                }

                if ($expected_roi && isset($error['roi_goal'])) {
                    $error_msg_list[] = [
                        'key' => 'deep_conversion_worth_spec.expected_roi',
                        'value' => 0,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => $error['roi_goal']
                    ];
                }
            }

            // 最大转化
            if ($setting->bid_strategy == TencentEum::BID_STRATEGY_MAX_COST) {
                if ($setting->billing_event !== TencentEum::BID_OCPM) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'billing_event',
                        'source_id' => $setting_id,
                        'value' => 0,
                        'msg' => '优化最大转化只支持ocpm出价方式'
                    ];
                }
                if ($setting->deep_conversion_spec_none == 'true' &&
                    !(
                        (in_array($this->site_config->convert_type, ['ACTIVATE_APP', 'OPTIMIZATIONGOAL_APP_REGISTER']) &&
                            $this->site_config->deep_external_action == 'GOAL_1DAY_PURCHASE_ROAS')
                        ||
                        ($this->site_config->convert_type == 'OPTIMIZATIONGOAL_BACK_FLOW')
                    )
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'bid_strategy',
                        'source_id' => $setting_id,
                        'value' => 0,
                        'msg' => '非[激活-首日付费ROI，沉默唤起]或者[注册-首日付费ROI]优化目标不支持优化最大转化'
                    ];
                }
            }

            if ($other_setting->site_set_none == 'datongtou') {

//                if ($setting->auto_acquisition_enabled) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                        'key' => 'auto_acquisition_enabled',
//                        'source_id' => $setting_id,
//                        'value' => 0,
//                        'msg' => '选择大通投时，不支持开启一键起量'
//                    ];
//                }

                if (in_array($other_setting->template_id, [720, 711]) && !$setting->head_line) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'head_line',
                        'source_id' => $setting_id,
                        'value' => 0,
                        'msg' => '请输入首行文案'
                    ];
                }
            }

            // 针对广告配置的深度转化与参数包的深度转化是否一致的判断
            if (!$this->site_config->deep_external_action) {
                if ($setting->deep_conversion_spec_none == 'true') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_conversion_spec_none',
                        'value' => $setting->deep_conversion_spec_none,
                        'msg' => '广告属性未选择深度转化类型，不能开启深度转化'
                    ];
                }
            } else {
                if ($setting->deep_conversion_spec_none == 'false') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_conversion_spec_none',
                        'value' => $setting->deep_conversion_spec_none,
                        'msg' => '广告属性已选择深度转化类型，需要打开深度转化'
                    ];
                }
            }

            if ($this->site_config->convert_type == 'OPTIMIZATIONGOAL_CANVAS_CLICK' && $setting->deep_conversion_spec_none == 'true') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_conversion_spec_none',
                    'value' => $setting->deep_conversion_spec_none,
                    'msg' => '广告属性已选择跳转按钮点击，不能使用深度转化'
                ];
            }

            if ($this->site_config->convert_type == 'OPTIMIZATIONGOAL_PAGE_RESERVATION' && $setting->deep_conversion_spec_none == 'true') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_conversion_spec_none',
                    'value' => $setting->deep_conversion_spec_none,
                    'msg' => '广告属性已选择跳转表单预约，不能使用深度转化'
                ];
            }

            if ($setting->deep_conversion_spec_none == 'true') {
                if ($this->site_config->plat_id != PlatId::MINI) {
                    if (
                        ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_BEHAVIOR &&
                        !in_array($this->site_config->deep_external_action,
                            ['OPTIMIZATIONGOAL_APP_PURCHASE', 'OPTIMIZATIONGOAL_FIRST_PURCHASE']
                        )
                    ) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'deep_conversion_type',
                            'value' => $setting->deep_conversion_spec['deep_conversion_type'],
                            'msg' => '选择优化转化行为时，广告配置中只能选"付费"或"首日付费"的深度转化类型'
                        ];
                    }

                    if (
                        ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_WORTH &&
                        !in_array($this->site_config->deep_external_action,
                            ['GOAL_1DAY_PURCHASE_ROAS', 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS']
                        )
                    ) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'deep_conversion_type',
                            'value' => $setting->deep_conversion_spec['deep_conversion_type'],
                            'msg' => '选择优化转化ROI时，广告配置中只能选则"首日付费ROI"或"强化-首日付费ROI"的深度转化类型'
                        ];
                    }
                } else {
                    if (
                        ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_WORTH &&
                        !in_array($this->site_config->deep_external_action,
                            ['GOAL_1DAY_PURCHASE_ROAS', 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS', 'GOAL_7DAY_PURCHASE_ROAS']
                        )
                    ) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'deep_conversion_type',
                            'value' => $setting->deep_conversion_spec['deep_conversion_type'],
                            'msg' => '选择优化转化ROI时，广告配置中只能选则"首日付费ROI"或"强化-首日付费ROI"或"7日付费ROI"的深度转化类型'
                        ];
                    }

                    if (
                        ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_WORTH &&
                        in_array($this->site_config->deep_external_action,
                            ['GOAL_1DAY_PURCHASE_ROAS', 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS']
                        ) &&
                        ($setting->deep_conversion_spec['deep_conversion_worth_spec']['goal'] ?? '') != 'GOAL_1DAY_PURCHASE_ROAS'
                    ) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'deep_conversion_worth_spec.goal',
                            'value' => '',
                            'msg' => '选择优化转化ROI时，广告配置选"首日付费ROI"或"强化-首日付费ROI"，只能选首次付费ROI'
                        ];
                    }

                    if (
                        ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_WORTH &&
                        $this->site_config->deep_external_action == 'GOAL_7DAY_PURCHASE_ROAS' &&
                        ($setting->deep_conversion_spec['deep_conversion_worth_spec']['goal'] ?? '') != 'GOAL_7DAY_PURCHASE_ROAS'
                    ) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'deep_conversion_worth_spec.goal',
                            'value' => '',
                            'msg' => '选择优化转化ROI时，广告配置选"7日付费ROI"，只能选7日付费ROII'
                        ];
                    }
                }


                if (
                    ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_BEHAVIOR &&
                    in_array($this->site_config->deep_external_action,
                        ['OPTIMIZATIONGOAL_APP_PURCHASE', 'OPTIMIZATIONGOAL_FIRST_PURCHASE']
                    ) &&
                    !($setting->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'] ?? '')
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_conversion_behavior_spec.bid_amount',
                        'value' => $setting->deep_conversion_spec['deep_conversion_type'],
                        'msg' => '"付费"或"首日付费"的深度转化类型,需要填写深度优化行为的出价'
                    ];
                }

                if (
                    ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_WORTH &&
                    in_array($this->site_config->deep_external_action,
                        ['GOAL_1DAY_PURCHASE_ROAS', 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS', 'GOAL_7DAY_PURCHASE_ROAS']
                    )
                    && !($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'] ?? '')
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_conversion_worth_spec.expected_roi',
                        'value' => $setting->deep_conversion_spec['deep_conversion_type'],
                        'msg' => '"首日付费ROI"或"强化-首日付费ROI"的深度转化类型,需要填写深度优化价值效果值'
                    ];
                }
            }


//            if ($this->compose_config->video_num_in_ad > 0 &&
//                !in_array($setting->billing_event, [TencentEum::BID_OCPM])) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'source_id' => $setting_id,
//                    'key' => 'billing_event',
//                    'value' => $setting->billing_event,
//                    'msg' => '投放视频创意时，出价方式请选择oCPM出价',
//                ];
//            }

            // 微信小游戏
            if ($other_setting->is_xijing == 4) {
                if ($setting->conversion_data_type == 'CONVERSION_DATA_ADMETRIC' && $setting->conversion_target_type && $setting->conversion_target_type != 'CONVERSION_TARGET_PLAYING') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'conversion_target_type',
                        'value' => $setting->conversion_target_type,
                        'source_id' => $setting_id,
                        'msg' => '小游戏的情况下，只能选"在玩"'
                    ];
                }
            }

            if ($setting->shop_img && !$other_setting->link_name_type) {
                $error_msg_list[] = [
                    'key' => 'link_name_type',
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'value' => 0,
                    'msg' => '卖点图需要设置文字链接名称'
                ];
            }

            if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set)) {
                foreach ($this->account_list->account_list as $account_info) {
                    $page_id = $other_setting->getPageInfo($account_info['account_id'])->page_id ?? '';
                    if ($page_id) continue;
                    if ($other_setting->is_xijing == 4) continue;
                    if (in_array($other_setting->is_xijing, [1, 2])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'page_map',
                            'value' => $other_setting->page_map,
                            'msg' => "请选择{$account_info['account_id']}对应的蹊径落地页信息"
                        ];
                    } else if ($other_setting->is_xijing == 0 && in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'page_map',
                            'value' => $other_setting->page_map,
                            'msg' => "请选择{$account_info['account_id']}对应的微信原生落地页信息"
                        ];
                    }
                }
            }

            // 蹊径落地页规则验证
            if ($other_setting->is_xijing == 1) {
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) && !in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'site_set',
                        'value' => $other_setting->site_set,
                        'msg' => "蹊径落地页需要选择微信朋友圈或微信公众号投放站点"
                    ];
                }
            }

            if (!$this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
                $material_error_msg_list = $this->videoOrPicValidateInSwitch($setting, $setting_id);
                $error_msg_list = array_merge($error_msg_list, $material_error_msg_list);
            }

            //校验roi的情况
            if (!$this->compose_config->ignore_warning && in_array($this->site_config->deep_external_action, ['GOAL_1DAY_PURCHASE_ROAS', 'GOAL_1DAY_MONETIZATION_ROAS'])) {
                $roi_standard_value = (new V2DimGameStandardValueDaysIncolumnModel())->getRoiStandardValue([
                    'platform' => $this->platform,
                    'game_id' => $this->site_config->game_id
                ]);
                $judge_roi_result = (new ADServingLogic())->judgeRoiValue([$setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']], $roi_standard_value);
                $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                    $ele['source_id'] = $setting_id;
                    return $ele;
                }, $judge_roi_result);
                $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
            }
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            $material_error_msg_list = $this->videoOrPicValidateInProgram();
            $error_msg_list = array_merge($error_msg_list, $material_error_msg_list);
        }

        if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
            foreach ($this->account_list->account_list as $account_info) {
                $profile_id = $other_setting->getProfileInfo($account_info['account_id'])->profile_id;
                if (!$profile_id) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'profile_map',
                        'value' => $other_setting->profile_map,
                        'msg' => "请选择{$account_info['account_id']}对应的微信朋友圈头像信息"
                    ];
                    break;
                }
            }
            if ($other_setting->link_name_type) {
//                if (in_array($other_setting->template_id, [1707, 1708])) {
//                    foreach ($this->word_list->getWordContent() as  $value) {
//                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
//                        if ($len > 20) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                                'key' => $value,
//                                'value' => 0,
//                                'msg' => "$value,此文案为{$len}个字符，选择了文字链接名称，文案长度不能超过10"
//                            ];
//                        }
//                    }
//                }
            }
        }

        if (in_array($other_setting->template_id, [2, 1708, 618, 721, 589, 957, 721, 538, 720, 721, 721, 877]) && empty($this->material_list->all_video_list)) {
            //视频版位
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'value' => 0,
                'msg' => "选择了需要视频的版位但是没有选择视频素材"
            ];
        } elseif (in_array($other_setting->template_id, [450, 1707, 588, 311, 641, 642, 643, 711, 712, 925, 876, 878]) && empty($this->material_list->all_image_list)) {
            //图片版位
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'value' => 0,
                'msg' => "选择了需要图片的版位但是没有选择图片素材"
            ];
        }

        // 微信小游戏
        if ($other_setting->is_xijing == 4) {
            if ($this->site_config->plat_id != PlatId::MINI) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'is_xijing',
                    'value' => 0,
                    'msg' => '当前游戏不支持微信小游戏落地页类型'
                ];
            }
        }

        // 程序化创意的校验
        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            if ($this->compose_config->word_num_in_ad > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => '腾讯动态创意只能选择每广告小于等于10条文案'
                ];
            }
            if ($this->compose_config->video_num_in_ad > 30) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'video_num_in_ad',
                    'value' => $this->compose_config->video_num_in_ad,
                    'msg' => '腾讯动态创意只能选择每广告小于等于30条视频'
                ];
            }

            if (count($this->material_list->video_vertical_list) > 0 && count($this->material_list->video_list) > 0) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => 0,
                    'msg' => '腾讯动态创意只能选横版视频或者只选竖版视频'
                ];
            }

        }

        foreach ($this->material_list->all_video_list as $video) {
            foreach ($video['cover_list'] as $cover) {
                if ($video['width'] != $cover['width'] || $video['height'] != $cover['height']) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => "{$video['url']}封面与视频的尺寸不一致"
                    ];
                }
            }
        }

        if ($this->site_config->android_union_channel_package_account_id <= 0 && $this->site_config->game_type == '安卓') {
            throw new AppException('此游戏没有配置主线包所属账户id');
        }

        if (($this->compose_config->video_num_in_ad + $this->compose_config->pic_num_in_ad) > 30) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => '每广告所需素材物料不能超过30个!'
            ];
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => '每广告所需素材物料不能超过30个!'
            ];
        }

        return $error_msg_list;
    }

    public function videoOrPicValidateInProgram()
    {
        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        $error_msg_list = [];

        foreach ($this->material_list->all_video_list as $video_info) {
            switch (true) {
                case $video_info['width'] == 720 && $video_info['height'] == 1280:
                    break;
                case $video_info['width'] == 1280 && $video_info['height'] == 720:
                    // 1708
                    if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                        $max_desc_length = 30;
                        foreach ($this->word_list->getWordContent() as $value) {
                            $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                            if ($len > $max_desc_length * 2) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                    'key' => $value,
                                    'value' => 0,
                                    'msg' => "$value,文案长度不能超过$max_desc_length"
                                ];
                            }
                        }
                    } else {
                        // 720
                        foreach ($this->word_list->getWordContent() as $value) {
                            $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                            if ($len > 60) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                    'key' => $value,
                                    'value' => 0,
                                    'msg' => "$value,文案长度不能超过30"
                                ];
                            }
                        }
                    }
                    break;
                case $video_info['width'] == 1280 && $video_info['height'] == 960:
                    if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$video_info['filename']}视频素材需要包含朋友圈版位"
                        ];
                    }
                    break;
                case $video_info['width'] == 1080 && $video_info['height'] == 1920:
                    if (!$other_setting->automatic_site_enabled && !array_intersect($other_setting->site_set, [
                            TencentEum::SITE_SET_WECHAT,
                            TencentEum::SITE_SET_KANDIAN,
                            TencentEum::SITE_SET_TENCENT_NEWS,
                            TencentEum::SITE_SET_TENCENT_VIDEO,
                            TencentEum::SITE_SET_QQ_MUSIC_GAME,
                            TencentEum::SITE_SET_MOBILE_UNION,
                        ])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$video_info['filename']}视频素材需要包含不能仅使用朋友圈版位"
                        ];
                    }
                    break;
                default :
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => "{$video_info['filename']}视频素材规格不支持，请删除再重试"
                    ];
                    break;
            }
        }
        foreach ($this->material_list->large_image_list as $image_info) {
            switch (true) {
                case $image_info['width'] == 1280 && $image_info['height'] == 720:
                    // 1707
                    if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                        $max_desc_length = 30;
                        foreach ($this->word_list->getWordContent() as $value) {
                            $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                            if ($len > $max_desc_length * 2) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                    'key' => $value,
                                    'value' => 0,
                                    'msg' => "$value,文案长度不能超过$max_desc_length"
                                ];
                            }
                        }
                    } else {
                        foreach ($this->word_list->getWordContent() as $value) {
                            $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                            if ($len > 60) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                    'key' => $value,
                                    'value' => 0,
                                    'msg' => "$value,文案长度不能超过30"
                                ];
                            }
                        }
                    }
                    // 711
                    break;
                case $image_info['width'] == 800 && $image_info['height'] == 800:
                    // 311
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                        if ($len > 60) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => 0,
                                'msg' => "$value,文案长度不能超过30"
                            ];
                        }
                    }
                    break;
                default:
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $image_info['id'],
                        'msg' => "{$image_info['filename']}图片素材规格不支持，请删除再重试"
                    ];
                    break;
            }
        }
        foreach ($this->material_list->large_vertical_image_list as $image_info) {
            switch (true) {
                case $image_info['width'] == 1080 && $image_info['height'] == 1920:
                    if (!$other_setting->automatic_site_enabled && !array_intersect($other_setting->site_set, [
                            TencentEum::SITE_SET_WECHAT,
                            TencentEum::SITE_SET_KANDIAN,
                            TencentEum::SITE_SET_TENCENT_NEWS,
                            TencentEum::SITE_SET_TENCENT_VIDEO,
                            TencentEum::SITE_SET_QQ_MUSIC_GAME,
                            TencentEum::SITE_SET_MOBILE_UNION,
                        ])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$image_info['filename']}图片素材不能仅使用朋友圈版位"
                        ];
                    }
                    break;
                default:
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $image_info['id'],
                        'msg' => "{$image_info['filename']}图片素材规格不支持，请删除再重试"
                    ];
                    break;
            }
        }
        if (count($this->material_list->small_image_list) > 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                'value' => 0,
                'msg' => "暂时不支持使用小图素材"
            ];
        }

        foreach ($this->material_list->group_image_list as $image_info) {
            switch (true) {
                case $image_info['width'] == 800 && $image_info['height'] == 800:
                    if (count($image_info['image_list']) < 6 && !in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "三组图、四组图素材需要包含使用朋友圈版位"
                        ];
                        break;
                    }
                    break;
                case $image_info['width'] == 480 && $image_info['height'] == 320:
                    if (count($image_info['image_list']) > 3) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "480x320组图素材目前只能使用三组图"
                        ];
                    }
                    if (!$other_setting->automatic_site_enabled && !array_intersect($other_setting->site_set, [
                            TencentEum::SITE_SET_KANDIAN,
                            TencentEum::SITE_SET_TENCENT_NEWS,
                            TencentEum::SITE_SET_TENCENT_VIDEO,
                            TencentEum::SITE_SET_QQ_MUSIC_GAME,
                            TencentEum::SITE_SET_MOBILE_UNION,
                        ])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$image_info['filename']}图片素材需要包含广点通版位或自动版位"
                        ];
                    }
                    break;
                default:
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $image_info['id'],
                        'msg' => "{$image_info['filename']}图片素材规格不支持，请删除再重试"
                    ];
                    break;
            }
        }
        return $error_msg_list;
    }

    /**
     * @param ADSettingContentParam $setting
     * @param $setting_id
     * @return array|void
     */
    public function videoOrPicValidateInSwitch(ADSettingContentParam $setting, $setting_id)
    {
        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        if ($other_setting->site_set_none == 'datongtou' && in_array($other_setting->template_id, [641, 642, 643])) {
            return $this->templateValidate($other_setting->template_id);
        }

        // QQ购物站点
        if (in_array(TencentEum::SITE_SET_QQSHOPPING, $other_setting->site_set)) {
            return $this->videoOrPicValidateInQQShop($setting, $setting_id);
        }
        // 视频号
        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            return $this->videoOrPicValidateInVideoNum();
        }
        // 微信站点
        if (
            in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) ||
            in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set) ||
            in_array(TencentEum::SITE_SET_WECHAT_PLUGIN, $other_setting->site_set)
        ) {
            return $this->videoOrPicValidateInWechat($setting, $setting_id);
        }
        // 其他站点
        return $this->videoOrPicValidateInTencent($setting, $setting_id);
    }

    /**
     * 视频号的校验
     * @return array
     */
    public function videoOrPicValidateInVideoNum()
    {
        $error_msg_list = [];
        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        $other_setting->template_id = $other_setting->template_id ?? 721;
        switch ($other_setting->template_id) {
            case '721':
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 720 && $video_info['height'] == 1280)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合720*1280的尺寸"
                        ];
                    }
                    // 判定是否5-90s
                    if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 180)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~180s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                }
                break;
            case '720':
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 1280 && $video_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合1280*720的尺寸"
                        ];
                    }
                    // 判定是否5-90s
                    if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 180)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~180s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                }
                break;
            default:
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'template_id',
                    'value' => 0,
                    'msg' => "请选择创意模式"
                ];
                break;
        }
        return $error_msg_list;
    }

    /**
     * QQ购物站点
     * @param ADSettingContentParam $setting
     * @param $setting_id
     * @return array
     */
    public function videoOrPicValidateInQQShop(ADSettingContentParam $setting, $setting_id)
    {
        $error_msg_list = [];

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        $other_setting->template_id = $other_setting->template_id ?? 721;
        switch ($other_setting->template_id) {
            case '721':
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 720 && $video_info['height'] == 1280)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合720*1280的尺寸"
                        ];
                    }
                    // 判定是否5-60s
                    if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 60)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~60s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                }
                break;
            case '876':
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                if (Helpers::ADServingStrLen($setting->title) > 30) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于15个字'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 1280 && $image_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合1280*720的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 72) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于36个字符"
                        ];
                    }
                }
                break;
            case '877':
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 1280 && $video_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合1280*720的尺寸"
                        ];
                    }
                    // 判定是否5-30s
                    if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 30)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~30s的规范"
                        ];
                    }
                }
                if (Helpers::ADServingStrLen($setting->title) > 30) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于15个字'
                    ];
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 72) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于36个字符"
                        ];
                    }
                }
                break;
            case '878':
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                if (Helpers::ADServingStrLen($setting->title) > 30) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于15个字'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 1280 && $image_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合1280*720的尺寸 "
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 72) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于36个字符"
                        ];
                    }
                }
                if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'idea_type',
                        'value' => 0,
                        'msg' => "QQ消息-橱窗不支持程序化创意"
                    ];
                }
                break;
            default:
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'template_id',
                    'value' => 0,
                    'msg' => "请选择创意模式"
                ];
                break;

        }

        return $error_msg_list;

    }

    /**
     * 微信站点
     * @param ADSettingContentParam $setting
     * @param $setting_id
     * @return array
     */
    public function videoOrPicValidateInWechat(ADSettingContentParam $setting, $setting_id)
    {

        $error_msg_list = [];
        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        switch ($other_setting->template_id) {
            case '450':// 朋友圈单图(800*450)
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }

                if (Helpers::ADServingStrLen($setting->title) > 28) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于14个字'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 800 && $image_info['height'] == 450)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合800*450的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }

                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            case '1708':// 卡片广告(视频1280*720)
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }

                if (Helpers::ADServingStrLen($setting->title) > 28) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于14个字'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    // 判定长宽640 * 360
                    if (!($video_info['width'] == 1280 && $video_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合1280*720的尺寸"
                        ];
                    }

                    // 判定是否6-30s
                    if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 60)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~60s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    } elseif ($setting->shop_img && $len > 20) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于10个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            case '1707':
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                if (Helpers::ADServingStrLen($setting->title) > 28) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于14个字'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 1280 && $image_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合1280*720的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    } elseif ($setting->shop_img && $len > 20) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于10个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            case '452':// 朋友圈基础卡片横版视频 16:9
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }

                if (Helpers::ADServingStrLen($setting->title) > 20) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于10个字'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    // 判定长宽640 * 360
                    if (!($video_info['width'] == 640 && $video_info['height'] == 360)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合640*360的尺寸"
                        ];
                    }

                    // 判定是否6-30s
                    if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 30)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~30s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            case '588':// 朋友圈标签单图卡片640*360)
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                if (Helpers::ADServingStrLen($setting->title) > 20) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于10个字'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 800 && $image_info['height'] == 450)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合800*450的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            case '311':// 朋友圈单图图片(800*800)
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 800 && $image_info['height'] == 800)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合800*800的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 80) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于4个表情"
                        ];
                    }
                }
                break;
            case '641':// 朋友圈3组图(800*800)
                if (!$this->material_list->group_image_list) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => '没有选择组图素材'
                    ];
                }
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                foreach ($this->material_list->group_image_list as $group_image_info) {
                    if (count($group_image_info['image_list']) < 3) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "组图的素材中不够3张"
                        ];
                    }
                    if (!($group_image_info['width'] == 800 && $group_image_info['height'] == 800)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$group_image_info['url']}不符合800*800的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 80) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于4个表情"
                        ];
                    }
                }
                break;
            case '642':// 朋友圈4组图(800*800)
                if (!$this->material_list->group_image_list) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => '没有选择组图素材'
                    ];
                }
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                foreach ($this->material_list->group_image_list as $group_image_info) {
                    if (count($group_image_info['image_list']) < 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "组图的素材中不够4张"
                        ];
                    }
                    if (!($group_image_info['width'] == 800 && $group_image_info['height'] == 800)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $group_image_info['id'],
                            'msg' => "{$group_image_info['url']}不符合800*800的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 80) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于4个表情"
                        ];
                    }
                }
                break;
            case '643':// 朋友圈6组图(800*800)
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                if (!$this->material_list->group_image_list) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => '没有选择组图素材'
                    ];
                }
                foreach ($this->material_list->group_image_list as $group_image_info) {
                    if (count($group_image_info['image_list']) < 6) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "组图的素材中不够6张"
                        ];
                    }
                    if (!($group_image_info['width'] == 800 && $group_image_info['height'] == 800)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $group_image_info['id'],
                            'msg' => "{$group_image_info['url']}不符合800*800的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 80) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于4个表情"
                        ];
                    }
                }
                break;
            case '618':// 朋友圈4:3常规视频(640*480)
                // 有link_name_type
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 640 && $video_info['height'] == 480)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合640*480的尺寸"
                        ];
                    }
                    // 判定是否6-30s
                    if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 180)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~180s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 80) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于4个表情"
                        ];
                    }
                }
                break;
            case '721':// 朋友圈9:16常规视频(720*1280)
                if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    if ($this->compose_config->video_num_in_ad > 5) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                            'key' => 'video_num_in_ad',
                            'value' => $this->compose_config->video_num_in_ad,
                            'msg' => '每广告不能大于5个视频'
                        ];
                    }
                    foreach ($this->material_list->all_video_list as $video_info) {
                        if (!($video_info['width'] == 720 && $video_info['height'] == 1280)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $video_info['id'],
                                'msg' => "{$video_info['url']}不符合720*1280的尺寸"
                            ];
                        }
                        // 判定是否6-30s
                        if ($other_setting->isWechatSingleSite()) {
                            if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 60)) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                    'value' => $video_info['id'],
                                    'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~60s的规范"
                                ];
                            }
                        }
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                        if ($len > 80) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => 0,
                                'msg' => "$value,此文案为{$len}个字符，大于40个字符"
                            ];
                        }
                    }
                }
                if (in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set) ||
                    in_array(TencentEum::SITE_SET_WECHAT_PLUGIN, $other_setting->site_set)) {
                    if ($this->compose_config->video_num_in_ad > 5) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                            'key' => 'video_num_in_ad',
                            'value' => $this->compose_config->video_num_in_ad,
                            'msg' => '每广告不能大于5个视频'
                        ];
                    }
                    if (!$setting->brand_name) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'brand_name',
                            'value' => 0,
                            'msg' => "品牌名称不能为空"
                        ];
                    }
                    if (!$setting->brand_img) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'brand_img',
                            'value' => 0,
                            'msg' => "品牌icon不能为空"
                        ];
                    }
                    foreach ($this->material_list->all_video_list as $video_info) {
                        if (!($video_info['width'] == 720 && $video_info['height'] == 1280)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $video_info['id'],
                                'msg' => "{$video_info['url']}不符合720*1280的尺寸"
                            ];
                        }
                        // 判定是否6-30s
                        if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 60)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $video_info['id'],
                                'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~60s的规范"
                            ];
                        }
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                        if ($len > 60) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => 0,
                                'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                            ];
                        }
                    }
                }
                break;
            case '720':
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                if (!$setting->brand_name) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'brand_name',
                        'value' => 0,
                        'msg' => "品牌名称不能为空"
                    ];
                }
                if (!$setting->brand_img) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'brand_img',
                        'value' => 0,
                        'msg' => "品牌icon不能为空"
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 1280 && $video_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合1280*720的尺寸"
                        ];
                    }
                    if ($other_setting->isWechatSingleSite()) {
                        // 判定是否6-30s
                        if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 60)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $video_info['id'],
                                'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~60s的规范"
                            ];
                        }
                    } else {
                        // 判定是否6-60s
                        if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 60)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                'value' => $video_info['id'],
                                'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~60s的规范"
                            ];
                        }
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                }
                break;
            case '711':
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 1280 && $image_info['height'] == 720)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合1280*720的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 4) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于4个表情"
                        ];
                    }
                }
                if (Helpers::ADServingStrLen($setting->title) > 24) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于12个字'
                    ];
                }
                break;
            case '712':
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 1080 && $image_info['height'] == 1920)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合1080*1920的尺寸"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 28) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案为{$len}个字符，大于14个字符"
                        ];
                    }
                }
                break;
            case '925':
                if ($this->compose_config->pic_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'msg' => '每广告只能1个图片'
                    ];
                }
                foreach ($this->material_list->all_image_list as $image_info) {
                    if (!($image_info['width'] == 960 && $image_info['height'] == 334)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "{$image_info['url']}不符合960*334的尺寸"
                        ];
                    }
                }
                break;
            case '589':// 朋友圈16:9卡片视频(640*360)
                if (!$setting->label) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'label',
                        'value' => $setting->label,
                        'msg' => '朋友圈16:9卡片视频(640*360)必须选择创意标签'
                    ];
                }
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                if (Helpers::ADServingStrLen($setting->title) > 20) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于10个字'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 640 && $video_info['height'] == 360)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合640*360的尺寸"
                        ];
                    }
                    // 判定是否6-30s
                    if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 30.5)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~30s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 60) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            case '957':// 朋友圈16:9行动卡片视频(640*360)
                if ($this->compose_config->video_num_in_ad > 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'msg' => '每广告不能大于5个视频'
                    ];
                }
                if (Helpers::ADServingStrLen($setting->title) > 20) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'title',
                        'value' => $setting->title,
                        'msg' => '标题不能大于10个字'
                    ];
                }
                foreach ($this->material_list->all_video_list as $video_info) {
                    if (!($video_info['width'] == 640 && $video_info['height'] == 360)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}不符合640*360的尺寸"
                        ];
                    }
                    // 判定是否6-30s
                    if (!($video_info['duration'] >= 6 && $video_info['duration'] <= 30.5)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合6~30s的规范"
                        ];
                    }
                }
                foreach ($this->word_list->getWordContent() as $value) {
                    $len = Helpers::ADServingStrLen(str_replace("\n", '', $value));
                    if ($len > 26) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => 0,
                            'msg' => "$value,此文案为{$len}个字符，大于13个字符"
                        ];
                    }
                    if ($this->getFaceNum($value) > 1) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => $value,
                            'value' => $value,
                            'msg' => "$value,此文案有大于1个表情"
                        ];
                    }
                }
                break;
            default:
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'template_id',
                    'value' => $other_setting->template_id,
                    'msg' => "没有传入正确的创意模型id"
                ];
        }
        return $error_msg_list;
    }

    /**
     * 其他站点
     * @param ADSettingContentParam $setting
     * @param $setting_id
     * @return array
     */
    private function videoOrPicValidateInTencent(ADSettingContentParam $setting, $setting_id)
    {
        $error_msg_list = [];
        foreach ($this->material_list->all_image_list as $image_info) {
            if (isset($image_info['image_list'])) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => 0,
                    'msg' => '广点通版位下不支持组图'
                ];
            }
            switch ($image_info) {
                case ($image_info['width'] == 1280 && $image_info['height'] == 720):
                    if (Helpers::ADServingStrLen($setting->title) > 28) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'title',
                            'value' => $setting->title,
                            'msg' => '标题不能大于14个字'
                        ];
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen($value);
                        if ($len > 60) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                            ];
                        }
                    }
                    break;
                case ($image_info['width'] == 960 && $image_info['height'] == 274):
                case ($image_info['width'] == 1080 && $image_info['height'] == 1920):
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen($value);
                        if ($len > 28) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案为{$len}个字符，大于14个字符"
                            ];
                        }
                    }
                    break;
                default:
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $image_info['id'],
                        'msg' => "{$image_info['url']}不符合1280*720, 1080*1920, 960*274的尺寸"
                    ];
                    break;
            }
        }

        foreach ($this->material_list->all_video_list as $video_info) {
            switch ($video_info) {
                case ($video_info['width'] == 720 && $video_info['height'] == 1280):
                case ($video_info['width'] == 1280 && $video_info['height'] == 720):
                    //   判定是否5-60s
                    if (!($video_info['duration'] >= 5 && $video_info['duration'] <= 60.5)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5~60s的规范"
                        ];
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = Helpers::ADServingStrLen($value);
                        if ($len > 60) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案为{$len}个字符，大于30个字符"
                            ];
                        }
                    }
                    break;
                case ($video_info['width'] == 1080 && $video_info['height'] == 1920):
                    //   判定是否5s
                    if ($video_info['duration'] <= 5) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $video_info['id'],
                            'msg' => "{$video_info['url']}视频{$video_info['duration']}S,不符合5s的规范"
                        ];
                    }
                    foreach ($this->word_list->getWordContent() as $value) {
                        $len = mb_strlen($value);
                        if ($len > 14) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                                'key' => $value,
                                'value' => $value,
                                'msg' => "$value,此文案为{$len}个字，大于14个字"
                            ];
                        }
                    }
                    break;
                default:
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $video_info['id'],
                        'msg' => "{$video_info['url']}不符合1280*720或1080*1920的尺寸"
                    ];
                    break;
            }
        }
        return $error_msg_list;
    }

    /**
     * 获取文案表情数量
     * @param $word
     * @return int
     */
    public function getFaceNum($word)
    {
        if (strpos($word, '[') !== false || strpos($word, ']') !== false) {
            $result = [];
            preg_match_all(/** @lang text */ "/(?<=\[)[^\]]+/", $word, $result);
            $result = $result[0];
            $num = count($result);
            if ($num > 1) {
                foreach ($result as $face) {
                    if (!in_array($face, TencentWeChatADFaceList::LIST)) {
                        throw new AppException("文案中,表情[$face],非法");
                    }
                }
            }
            return $num;
        }
        return 0;
    }

    /**
     * 创意规格接口校验
     * @return array
     */
    public function apiValidate()
    {
        $error_msg_list = [];

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        if ($this->site_config->plat_id == PlatId::MINI) {
            $promoted_object_type = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
        } else {
            $promoted_object_type = $this->site_config->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;
        }

        try {
            $service = (new TencentVerifyService())->init(
                array_rand($other_setting->page_map),
                $other_setting->site_set,
                $promoted_object_type,
                $other_setting->automatic_site_enabled,
                $this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE ? 'true' : 'false',
                $other_setting->template_id);
        } catch (AppException $e) {
            return [];
        }

        // 校验落地页
        if ($resp = $service->verify('page_type', $other_setting->page_type)) {
            $resp->isFail() && $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'is_xijing',
                'msg' => $resp->getMsg()
            ];
        }
        // 按钮文案
        if ($resp = $service->verify('button_text', $other_setting->button_text)) {
            $resp->isFail() && $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'button_text',
                'msg' => $resp->getMsg()
            ];
        }
        // 文字链接名称
        if ($resp = $service->verifyPageLink('link_name_type', $other_setting->link_name_type, $other_setting->page_type)) {
            $resp->isFail() && $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'link_name_type',
                'msg' => $resp->getMsg()
            ];
        }

        // 选择按钮
        if ($other_setting->choose_button_switch) {
            if ($resp = $service->verify('chosen_button_text1', $other_setting->chosen_button_text1, true)) {
                $resp->isFail() && $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'chosen_button_text1',
                    'msg' => $resp->getMsg()
                ];
            }
            if ($resp = $service->verify('chosen_button_text2', $other_setting->chosen_button_text2, true)) {
                $resp->isFail() && $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'chosen_button_text2',
                    'msg' => $resp->getMsg()
                ];
            }
        }

        // 参数包校验
        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
            /* @var ADSettingContentParam $setting */
            // 首部文案
            if ($resp = $service->verify('head_line', $setting->head_line)) {
                $resp->isFail() && $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source' => $setting_id,
                    'key' => 'head_line',
                    'msg' => $resp->getMsg()
                ];
            }
            // 底部文案
            if ($resp = $service->verify('bottom_text', $setting->bottom_text)) {
                $resp->isFail() && $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source' => $setting_id,
                    'key' => 'bottom_text',
                    'msg' => $resp->getMsg()
                ];
            }
        }

        return $error_msg_list;
    }

}
