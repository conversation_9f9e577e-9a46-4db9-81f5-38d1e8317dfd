<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Tencent\Ada;

use App\Param\ADServing\Tencent\ADSettingContentParam as TencentADSettingContentParam;

class ADSettingContentParam extends TencentADSettingContentParam
{
    public $optimization_goal;

    public $begin_date;

    public $billing_event;

    public $bid_scene;

    public $bid_strategy;

    public $configured_status = 'AD_STATUS_SUSPEND';

    public $daily_budget;

    public $deep_conversion_spec_none;

    public $deep_conversion_spec;

    public $end_date;

    public $first_day_begin_time;

    public $time_series;

    public $speed_mode;

    public $total_budget;

    public $brand_img;

    public $brand_name;

    public $title;

    public $label;

    public $head_line;

    public $bottom_text;

    public $conversion_data_type;

    public $conversion_target_type;

    public $shop_img;

    public $excluded_dimension_mode;

    public $excluded_dimension;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        return [];
    }

    /**
     * 参数格式化
     */
    public function format()
    {
    }

    public function paramHook()
    {
        $this->format();
    }

    /**
     * 生成目标转化出价
     * @return mixed
     */
    public function getBidAmount()
    {
        if (!$this->bid_amount || !is_array($this->bid_amount)) {
            return '';
        }
        if ((int)$this->bid_amount_mode === 1) {
            return sprintf("%.2f", $this->bid_amount[0] + mt_rand() / mt_getrandmax() * ($this->bid_amount[1] - $this->bid_amount[0]));
        } else {
            return $this->bid_amount[array_rand($this->bid_amount, 1)];
        }
    }

}
