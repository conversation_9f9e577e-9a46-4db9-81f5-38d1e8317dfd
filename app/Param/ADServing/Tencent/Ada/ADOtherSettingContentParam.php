<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Tencent\Ada;

use App\Constant\TencentEum;
use App\Param\ADServing\Tencent\ADOtherSettingContentParam as TencentADOtherSettingContentParam;
use App\Utils\Helpers;

class ADOtherSettingContentParam extends TencentADOtherSettingContentParam
{
    public $head_click_type = TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE;

    public $automatic_site_enabled = false;

    public $page_type;

    public $chosen_button_text1;

    public $chosen_button_text2;

    public $marketing_scene;

    public $wechat_position;

    public $flow_optimization_enabled;

    public $template_id;
    public $site_set_none;

    public $site_set;

    /**
     * 投放优化
     * @var array
     */
    public $scene_spec = [
        'display_scene' => [],
        'display_scene_none' => 'buxian'
    ];

    public $promotion_card_id;

    public $promotion_title;

    public $promotion_desc;

    public $is_xijing;

    public $page_map;

    public $profile_map;

    public $video_number_map;

    public $finder_object_visibility;

    public $link_name_type;

    public $button_text;

    public $choose_button_switch;

    public $app_gift_pack_switch;

    public $app_gift_pack_code_code;

    public $app_gift_pack_code_tips;

    public $app_gift_pack_code_description;

    /**
     * 参数格式化
     */
    public function format()
    {
    }

    public function paramHook()
    {
        if ($this->site_set_none == 'auto') {
            $this->automatic_site_enabled = true;
            $this->site_set = [];
        }
        if (isset($this->scene_spec['display_scene']) && !$this->scene_spec['display_scene']) {
            $this->scene_spec['display_scene'] = [];
        }
        if ($this->site_set_none == 'auto') {
            $this->automatic_site_enabled = true;
            $this->site_set = [];
        }
        if (!in_array('SITE_SET_MOBILE_UNION', $this->site_set) && !$this->automatic_site_enabled) {
            $this->scene_spec = [];
        }
        if ($this->site_set_none != 'xiaochengxu') {
            $this->wechat_position_status = false;
            $this->wechat_position = [];
        } else {
            if ($this->wechat_position_status) {
                $this->scene_spec['wechat_position'] = $this->wechat_position;
            }
        }
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array|mixed
     */
    public function getPageInfo($account_id)
    {
        if ($page_info = $this->page_map[$account_id] ?? '') {
            return json_decode($page_info);
        }
        return [];
    }

    /**
     * 获取账号朋友圈头像信息
     * @param $account_id
     * @return array|mixed
     */
    public function getProfileInfo($account_id)
    {
        if ($profile_info = $this->profile_map[$account_id] ?? '') {
            return json_decode($profile_info);
        }
        return [];
    }

    /**
     * 获取视频号信息
     * @param $account_id
     * @return array|mixed
     */
    public function getVideoNumberInfo($account_id)
    {
        if ($video_number_info = $this->video_number_map[$account_id] ?? '') {
            return json_decode($video_number_info);
        }
        return [];
    }

    /**
     * 微信单版位
     * @return bool
     */
    public function isWechatSingleSite()
    {
        return in_array(TencentEum::SITE_SET_MOMENTS, $this->site_set) && count($this->site_set) === 1;
    }
}
