<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Tencent\Basics;

use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Param\ADServing\Tencent\ADSettingContentParam as TencentADSettingContentParam;
use App\Utils\Helpers;

class ADSettingContentParam extends TencentADSettingContentParam
{
    public $campaign_type = 'CAMPAIGN_TYPE_NORMAL';
    public $promoted_object_type = '';
    public $campaign_configured_status = '';
    public $ad_configured_status = '';
    public $promoted_object_id = '';
    public $schedule_type = '';
    public $deep_conversion_worth_rate = '';
    public $deep_conversion_behavior_bid = '';
    public $begin_date = '';
    public $end_date = '';
    public $first_day_begin_time_type = 'buxian';
    public $first_day_begin_time = '';
    public $time_series = [];
    public $campaign_daily_budget_none = '';
    public $campaign_daily_budget = '';
    public $speed_mode = '';
    public $ad_daily_budget_none = '';
    public $ad_daily_budget = '';
    public $bid_strategy = '';
    public $billing_event = '';
    public $optimization_goal = '';
    public $bid_amount = [];
    // 出价类型
    public $smart_bid_type = 'SMART_BID_TYPE_CUSTOM';
    public $deep_conversion_spec_none = '';
    public $deep_conversion_spec = [];
    public $idea_type = '';
    public $brand_img = '';
    public $brand_name = '';
    public $title = '';
    public $bottom_text = '';
    public $head_line = '';
    public $label = [];
    public $conversion_target_type = '';
    public $conversion_data_type = '';
    public $excluded_dimension_mode = '';
    public $excluded_dimension = '';
    public $excluded_conversion_behavior_mode = 'none';
    public $excluded_conversion_behavior = '';
    public $is_bid_coefficient = 0;
    public $site_set_bid_coefficient = [];
    public $is_deep_bid_coefficient = 0;
    public $site_set_deep_bid_coefficient = [];
    public $auto_acquisition_enabled = false;
    public $auto_acquisition_budget = 0;
    public $flow_optimization_enabled = true;
    public $shop_img = ''; // 卖点图
    public $bid_adjustment = [];
    public $deep_conversion_worth_advanced_rate = '';
    public $bid_scene = '';

    /**
     * @var bool 是否严格validate
     */
    public $is_strict = true;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        unset($property['is_strict']);
        $this->is_strict = $is_strict;
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === NULL) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if ($this->auto_acquisition_enabled) {
            if (!($this->auto_acquisition_budget >= 200 && $this->auto_acquisition_budget <= 100000)) {
                $error_msg_list[] = [
                    'key' => 'auto_acquisition_budget',
                    'value' => $this->auto_acquisition_budget,
                    'msg' => '一键起量预算只能大于等于200小于等于100000'
                ];
            }
        }

        if ($this->brand_name && strpos($this->brand_name, '333') !== false) {
            $error_msg_list[] = [
                'key' => 'brand_name',
                'value' => $this->brand_name,
                'msg' => '品牌名称(brand_name)包含333，非法命名，请修改参数包!'
            ];
        }

        if (!$this->brand_img) {
            $error_msg_list[] = [
                'key' => 'brand_img',
                'value' => $this->brand_img,
                'msg' => '品牌头像不能为空'
            ];
        }
        if (!$this->brand_name) {
            $error_msg_list[] = [
                'key' => 'brand_name',
                'value' => $this->brand_name,
                'msg' => '品牌名不能为空'
            ];
        } else {
            if (Helpers::ADServingStrLen($this->brand_name) > 24) {
                $error_msg_list[] = [
                    'key' => 'brand_name',
                    'value' => $this->brand_name,
                    'msg' => '品牌名名字不能超12个字'
                ];
            }
        }

        if (!$this->bid_amount) {
            $this->bid_amount = [];
        }

        if (!is_array($this->bid_amount)) {
            $error_msg_list[] = [
                'key' => 'bid_amount',
                'value' => $this->bid_amount,
                'msg' => '请重新填写广告出价'
            ];
        }
        // 手动出价
        if ($this->smart_bid_type != 'SMART_BID_TYPE_SYSTEMATIC') {
            if (count($this->bid_amount) <= 0) {
                $error_msg_list[] = [
                    'key' => 'bid_amount',
                    'value' => $this->bid_amount,
                    'msg' => '广告出价不能为空'
                ];
            } else {
                foreach ($this->bid_amount as $value) {
                    if ($value < 0.1) {
                        $error_msg_list[] = [
                            'key' => 'bid_amount',
                            'value' => $this->bid_amount,
                            'msg' => '广告出价不能低于0.1元'
                        ];
                        break;
                    }
                    if ($value > 500000) {
                        $error_msg_list[] = [
                            'key' => 'bid_amount',
                            'value' => $this->bid_amount,
                            'msg' => '广告出价不能大于500000元'
                        ];
                        break;
                    }
                }
            }
        }

        if ($this->smart_bid_type == 'SMART_BID_TYPE_SYSTEMATIC' && empty($this->ad_daily_budget)) {
            $error_msg_list[] = [
                'key' => 'ad_daily_budget',
                'value' => 0,
                'msg' => "广告自动出价要求广告日预算必填"
            ];
        }

        if (isset($this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount']) &&
            $this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'] > 5000
        ) {
            $error_msg_list[] = [
                'key' => 'deep_conversion_behavior_spec.bid_amount',
                'value' => 0,
                'msg' => "深度优化出价不能超过5000元"
            ];
        }

        if (isset($this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount']) &&
            ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_BEHAVIOR &&
            !is_numeric($this->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'])
        ) {
            $error_msg_list[] = [
                'key' => 'deep_conversion_behavior_spec.bid_amount',
                'value' => 0,
                'msg' => "深度优化出价不是一个数字"
            ];
        }

        if (isset($this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']) &&
            $this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'] > 1000
        ) {
            $error_msg_list[] = [
                'key' => 'deep_conversion_worth_spec.expected_roi',
                'value' => 0,
                'msg' => "深度优化价值效果值最大值1000"
            ];
        }

        if (isset($this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']) &&
            ($setting->deep_conversion_spec['deep_conversion_type'] ?? '') == TencentEum::DEEP_CONVERSION_WORTH &&
            $this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']
        ) {
            $roi_num_array = explode('.', $this->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']);
            if (count($roi_num_array) > 2) {
                $error_msg_list[] = [
                    'key' => 'deep_conversion_worth_spec.expected_roi',
                    'value' => 0,
                    'msg' => "深度优化价值效果值非法."
                ];
            }
            if (!is_numeric($roi_num_array[0])) {
                $error_msg_list[] = [
                    'key' => 'deep_conversion_worth_spec.expected_roi',
                    'value' => 0,
                    'msg' => "深度优化价值效果值非法."
                ];
            }
            if (!is_numeric($roi_num_array[1])) {
                $error_msg_list[] = [
                    'key' => 'deep_conversion_worth_spec.expected_roi',
                    'value' => 0,
                    'msg' => "深度优化价值效果值非法."
                ];
            }
        }

        if ($this->label && count($this->label) > 3) {
            $error_msg_list[] = [
                'key' => 'label',
                'value' => 0,
                'msg' => "标签不能超过3个"
            ];
        }

        if ($this->schedule_type == 'zidingyi') {
            if (!$this->begin_date) {
                $error_msg_list[] = [
                    'key' => 'schedule_type',
                    'value' => 0,
                    'msg' => "结束时间(endtime)与开始时间(starttime)不能为空"
                ];
            }
        }

        return $error_msg_list;
    }

    /**
     * 参数格式化
     */
    public function format()
    {
        if ($this->first_day_begin_time_type == 'buxian') {
            $this->first_day_begin_time = '';
        }
        if (!$this->is_bid_coefficient) {
            $this->site_set_bid_coefficient = [];
        }

        if ($this->deep_conversion_spec_none === 'false') {
            $this->is_deep_bid_coefficient = 0;
            $this->deep_conversion_spec = [];
        }

        if (!$this->is_deep_bid_coefficient) {
            $this->site_set_deep_bid_coefficient = [];
        }

        if ($this->schedule_type == 'zidingyi') {

            if ($this->begin_date) {
                $this->begin_date = mb_strlen(strval($this->begin_date)) > 10 ? $this->begin_date / 1000 : $this->begin_date;
                if (($this->begin_date) < time()) {
                    $this->begin_date = time();
                }
            }

            if ($this->end_date) {
                $this->end_date = mb_strlen(strval($this->end_date)) > 10 ? $this->end_date / 1000 : $this->end_date;
            }

            if ($this->end_date && $this->end_date < $this->begin_date) {
                $this->end_date = strtotime('+1 year');
            }
        } else {
            $this->begin_date = null;
            $this->end_date = null;
        }

        if ($this->begin_date) {
            $this->begin_date = mb_strlen(strval($this->begin_date)) > 10 ? $this->begin_date / 1000 : $this->begin_date;
        } else {
            $this->begin_date = 0;
        }

        if ($this->end_date) {
            $this->end_date = mb_strlen(strval($this->end_date)) > 10 ? $this->end_date / 1000 : $this->end_date;
        } else {
            $this->end_date = 0;
        }
    }

    public function paramHook()
    {
        $this->format();
    }
}
