<?php
/**
 * bilibili  定向包
 * User: Lin
 * Date: 2021/6/3
 * Time: 17:37
 */

namespace App\Param\ADServing\Bilibili;

use App\Constant\BsEnum;
use App\Param\ADServing\AbstractADTargetingContentParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Utils\Helpers;

class ADTargetingContentParam extends AbstractADTargetingContentParam
{
    public $age_type = 'nolimit';
    public $age_list = [];
    public $gender_type = 'nolimit';
    public $gender_list = [];
    public $area_type = 'nolimit';
    public $area_list = [];
    public $profession_interest_crowd_pack_type = 'nolimit';
    public $profession_interest_crowd_pack_id_list = [];
    public $os_target = 'nolimit';
    public $os_version_type = 'nolimit';
    public $os_version_list = [];
    public $device_brand_type = 'nolimit';
    public $device_brand_list = [];
    public $crowd_pack_type = 'nolimit';
    public $include_crowd_pack_id_list = [];
    public $include_crowd_pack_id_map = [];
    public $exclude_crowd_pack_id_list = [];
    public $exclude_crowd_pack_id_map = [];
    public $intelligent_mass = '0';
    public $intelligent_mass_target_type_list = [];
    public $extra_crowd_pack_id_list = [];
    // 关键词

    public function validate()
    {
        $error_msg_list = [];

        return $error_msg_list;
    }


    /**
     * 生成此定向包的md5特殊编码
     * @return string
     */
    public function toMd5()
    {
        $md5_array = $this->toArray();
        ksort($md5_array);
        return md5(json_encode($md5_array, JSON_NUMERIC_CHECK));
    }

    public function format($state)
    {
        if ($this->include_crowd_pack_id_map) {
            $crowd_pack_id_list = array_column(
                $this->include_crowd_pack_id_map,
                'id'
            );
            $this->include_crowd_pack_id_list = $crowd_pack_id_list;
            sort($this->include_crowd_pack_id_list);
        } else {
            if ($this->include_crowd_pack_id_list) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->include_crowd_pack_id_list);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->include_crowd_pack_id_list = [];
                }
            } else {
                $this->include_crowd_pack_id_list = [];
                $this->include_crowd_pack_id_map = [];
            }
        }

        if ($this->exclude_crowd_pack_id_map) {
            $crowd_pack_id_list = array_column(
                $this->exclude_crowd_pack_id_map,
                'id'
            );
            $this->exclude_crowd_pack_id_list = $crowd_pack_id_list;
            sort($this->exclude_crowd_pack_id_list);
        } else {
            if ($this->exclude_crowd_pack_id_list) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->exclude_crowd_pack_id_list);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->exclude_crowd_pack_id_list = [];
                }
            } else {
                $this->exclude_crowd_pack_id_list = [];
                $this->exclude_crowd_pack_id_map = [];
            }
        }
    }

    public function toMQData()
    {
        return $this;
    }

    public function getAllAudienceIdList()
    {
        return array_merge(
            $this->exclude_crowd_pack_id_list ?: [],
            $this->include_crowd_pack_id_list ?: []
        );
    }

    public function getAllFlowIdMd5MapList()
    {
        return [];
    }

    public function resetFlowPackage(array $flow_package_map)
    {
        return [];
    }

    public function getMediaFormatTargetingArray()
    {
        return [];
    }

}
