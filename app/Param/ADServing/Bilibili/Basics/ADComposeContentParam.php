<?php
/**
 * Created by PhpStorm.
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Bilibili\Basics;

use App\Constant\BatchAD;
use App\Constant\PlatId;
use App\Param\ADServing\Bilibili\ADComposeContentParam as BiliBiliADComposeContentParam;

class ADComposeContentParam extends BiliBiliADComposeContentParam
{
    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $this->other_setting;


        /* @var ADSettingContentParam $setting */
        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            if (
                $this->site_config->deep_external_action &&
                $setting->deep_cpa_target &&
                $setting->deep_cpa_target != $this->site_config->deep_external_action
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'deep_cpa_target',
                    'value' => 0,
                    'msg' => "广告配置和参数包的深度转化类型不一致"
                ];
            }

            if (
                $this->site_config->convert_type &&
                $setting->cpa_target &&
                $setting->cpa_target != $this->site_config->convert_type
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'cpa_target',
                    'value' => 0,
                    'msg' => "广告配置和参数包的转化类型不一致"
                ];
            }
        }

        if ($this->site_config->plat_id == PlatId::MINI) {

            if ($other_setting->promotion_content_type != 2) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'promotion_content_type',
                    'value' => 0,
                    'msg' => "小游戏的推广内容类型 必须是线索"
                ];
            }

            foreach ($this->account_list->account_list as $account_info) {
                $mini_game_info = $other_setting->getMiniGame($account_info['account_id']);
                if (!$mini_game_info) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'mini_game_map',
                        'value' => $other_setting->mini_game_map,
                        'msg' => "请选择{$account_info['account_id']}对应的小游戏信息"
                    ];
                }

                $landing_page_info = $other_setting->getLandingPage($account_info['account_id']);
                if (!$landing_page_info) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'landing_page_map',
                        'value' => $other_setting->landing_page_map,
                        'msg' => "请选择{$account_info['account_id']}对应的落地页信息"
                    ];
                }
            }
        }

        foreach ($this->account_list->account_list as $account_info) {
            $ad_space_id_info = $other_setting->getADSpaceId($account_info['account_id']);
            if (!$ad_space_id_info) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'ad_space_id_info',
                    'value' => $other_setting->ad_space_id_map,
                    'msg' => "请选择{$account_info['account_id']}对应的品牌信息"
                ];
            }
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && ($this->compose_config->pic_num_in_ad + $this->compose_config->video_num_in_ad) < 2) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => 0,
                'msg' => "程序化创意时，需要累计2个创意素材以上"
            ];

            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => 0,
                'msg' => "程序化创意时，需要累计2个创意素材以上"
            ];
        }

        if (!$other_setting->description) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'description',
                'value' => 0,
                'msg' => "创意描述必填"
            ];
        }

        return $error_msg_list;
    }
}
