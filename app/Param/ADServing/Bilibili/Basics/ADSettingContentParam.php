<?php
/**
 * bilibili搜索 参数包
 * Date: 2021/6/1
 * Time: 14:38
 */

namespace App\Param\ADServing\Bilibili\Basics;

use App\Param\ADServing\Bilibili\ADSettingContentParam as BiliBiliADComposeContentParam;

class ADSettingContentParam extends BiliBiliADComposeContentParam
{

    public $campaign_budget_limit_type = 'nolimit';
    public $campaign_budget = 0;
    public $launch_begin_date = '';
    public $launch_end_date = '';
    public $launch_time = [];
    public $is_no_bid = 0;
    public $base_target = 11;
    public $cpa_target = '';
    public $deep_cpa_target = '';
    public $cpa_bid_mode = 0;
    public $cpa_bid = [];
    public $deep_cpa_bid_mode = 0;
    public $deep_cpa_bid = [];
    public $dual_bid_two_stage_optimization = 0;
    public $speed_mode = 1;
    public $unit_budget = 0;
    public $channel_id = 10040000;
    public $is_smart_material = 0;
    public $business_category = [];
    public $tag_list = '[]';

    public $installed_user_filter = [];
    public $converted_user_filter = [];


    public function paramHook()
    {
        $this->format();
    }

    public function validate()
    {
        $validate_msg_list = [];
        return $validate_msg_list;
    }

    public function format()
    {
    }

    public function getBusinessCategory()
    {
        $data = [];
        if(isset($this->business_category[0])) { $data['first_category_id'] = $this->business_category[0]; } else { $data['first_category_id'] = 0; }
        if(isset($this->business_category[1])) { $data['second_category_id'] = $this->business_category[1]; } else { $data['second_category_id'] = 0; }
        if(isset($this->business_category[2])) { $data['third_category_id'] = $this->business_category[2]; } else { $data['third_category_id'] = 0; }

        return $data;
    }

    /**
     * 生成目标转化出价
     * @return mixed
     */
    public function getCpaBid()
    {
        if (!$this->cpa_bid || !is_array($this->cpa_bid)) {
            return '';
        }
        if ((int)$this->cpa_bid_mode === 1) {
            return sprintf("%.2f", $this->cpa_bid[0] + mt_rand() / mt_getrandmax() * ($this->cpa_bid[1] - $this->cpa_bid[0]));
        } else {
            return $this->cpa_bid[array_rand($this->cpa_bid, 1)];
        }
    }

    /**
     * 生成深度目标转化出价
     * @return mixed
     */
    public function getDeepCpaBid()
    {
        if (!$this->deep_cpa_bid || !is_array($this->deep_cpa_bid)) {
            return '';
        }
        if ((int)$this->deep_cpa_bid_mode === 1) {
            return sprintf("%.2f", $this->deep_cpa_bid[0] + mt_rand() / mt_getrandmax() * ($this->deep_cpa_bid[1] - $this->deep_cpa_bid[0]));
        } else {
            return $this->deep_cpa_bid[array_rand($this->deep_cpa_bid, 1)];
        }
    }


    /**
     * 生成Roi效果值
     * @return mixed
     */
    public function getRoiRatio()
    {
        if (!$this->deep_cpa_bid || !is_array($this->deep_cpa_bid)) {
            return '';
        }
        if ((int)$this->deep_cpa_bid_mode === 1) {
            return sprintf("%.3f", $this->deep_cpa_bid[0] + mt_rand() / mt_getrandmax() * ($this->deep_cpa_bid[1] - $this->deep_cpa_bid[0]));
        } else {
            return $this->deep_cpa_bid[array_rand($this->deep_cpa_bid, 1)];
        }
    }


}
