<?php
/**
 * Created by PhpStorm.
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Bilibili\Basics;

use App\Param\ADServing\Bilibili\ADOtherSettingContentParam as BiliBiliADComposeContentParam;

class ADOtherSettingContentParam extends BiliBiliADComposeContentParam
{
    public $template_group_id = 4;
    public $promotion_content_type = 6;
    public $is_bili_native = 0;
    public $scene_id_list_type = 'nolimit';
    public $scene_id_list = [];

    public $landing_page_map = [];
    public $mini_game_map = [];
    public $ad_space_id_map = [];

    public $description = '';

    public function getLandingPage($account_id)
    {
        return $this->landing_page_map[$account_id] ?? '';
    }

    public function getMiniGame($account_id)
    {
        return json_decode($this->mini_game_map[$account_id] ?? '[]', true);
    }

    public function getADSpaceId($account_id)
    {
        return $this->ad_space_id_map[$account_id] ?? '';
    }

    public function paramHook()
    {
        $this->landing_page_map = $this->tranObject($this->landing_page_map);
        $this->mini_game_map = $this->tranObject($this->mini_game_map);
        $this->ad_space_id_map = $this->tranObject($this->ad_space_id_map);
        $this->format();
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        return [];
    }

    /**
     * 参数格式化
     */
    public function format()
    {

    }
}
