<?php
/**
 * Created by <PERSON><PERSON>Storm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:46
 */

namespace App\Param\ADServing;

use App\Constant\BatchAD;
use App\Constant\BatchADClassMap;
use App\Container;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Param\AbstractParam;
use App\Param\ADServing\ADComposeContent\ADComposeConfigParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeListParam;
use App\Param\SiteConfigParam;

class ADTaskParam extends AbstractParam
{
    /**
     * @var string
     */
    public $id = '';

    /**
     * @var int
     */
    public $compose_id = 0;

    /**
     * @var int
     */
    public $batch_id = 0;

    /**
     * @var string
     */
    public $company = '';

    /**
     * @var int $origin_type
     */
    public $origin_type = 1;

    /**
     * @var string
     */
    public $platform = '';

    /**
     * @var string
     */
    public $media_type = '';

    /**
     * @var string
     */
    public $account_id = '';

    /**
     * @var string
     */
    public $account_name = '';

    /**
     * @var array $creative_list
     */
    public $creative_list = [];

    /**
     * @var int
     */
    public $creative_mode = BatchAD::CREATIVE_PROGRAM_MODE;

    /**
     * @var array
     */
    public $word_list = [];

    /**
     * @var string
     */
    public $targeting_name = '';

    /**
     * @var string
     */
    public $convert_id = '';

    /**
     * @var int
     */
    public $site_id = 0;

    /**
     * @var int
     */
    public $agent_id = 0;

    /**
     * @var int
     */
    public $ad1_id = 0;

    /**
     * @var int
     */
    public $ad2_id = 0;

    /**
     * @var array
     */
    public $ad3_ids = [];

    /**
     * @var array
     */
    public $ad1_name_text = '';

    /**
     * @var array
     */
    public $ad2_name_text = '';

    /**
     * @var int
     */
    public $state_code = ADTaskModel::WAIT_PUSH_QUEUE;

    /**
     * @var string
     */
    public $error_msg = '';

    /**
     * @var string
     */
    public $create_time = '';

    /**
     * @var string
     */
    public $creator = '';

    public $creator_id = 0;

    /**
     * @var int
     */
    public $state = 1;

    /**
     * @var array $site_config
     */
    public $site_config;

    /**
     * @var SiteConfigParam $site_config_pa
     */
    private $site_config_pa;

    /**
     * 参数扩展
     * @var array
     */
    public $ext = [];

    /**
     * @param $prop_name
     * @param $value
     */
    public function setSiteConfigProp($prop_name, $value)
    {
        $prop_name = trim($prop_name);
        if ($this->site_config_pa && isset($this->site_config_pa->$prop_name)) {
            $this->site_config_pa->$prop_name = $value;
            $this->site_config = $this->site_config_pa->toArray();
        }
    }

    /**
     * 设置拓展参数
     * @param $key
     * @param $value
     */
    public function setExt($key, $value)
    {
        $this->ext->{$key} = $value;
    }

    /**
     * @param SiteConfigParam $param
     */
    public function setSiteConfig(SiteConfigParam $param)
    {
        $this->site_config_pa = $param;
        $this->site_config = $param->toArray();
    }

    /**
     * @return SiteConfigParam
     */
    public function getSiteConfig()
    {
        return $this->site_config_pa;
    }

    /**
     * @var ADComposeConfigParam
     */
    public $compose_config = [];

    /**
     * @var AbstractADOtherSettingContentParam
     */
    public $other_setting = [];

    /**
     * @var string
     */
    public $setting_name = '';

    /* @var AbstractADTargetingContentParam $targeting */
    public $targeting = null;

    /* @var AbstractADSettingContentParam $setting */
    public $setting = null;

    /**
     * 媒体的渠道类型：例如头条：默认和投放管家
     * @var int
     */
    public $media_agent_type = 0;

    /**
     * @var int
     */
    public $is_wait_package = 0;

    /**
     * 绑定规则的规则id和名字的数组
     * @var array $calc_rule_list
     */
    public $calc_rule_list = [];

    /**
     * 是否已经绑定数值规则
     * @var int
     */
    public $calc_rule_state = 0;

    /**
     * @var array
     */
    public $rta_list = [];

    /**
     * 队列所在队列
     * @var int
     */
    public $queue_index = 0;

    /**
     * 任务状态
     * @var int
     */
    public $task_state = ADTaskModel::TASK_STATE_SURE;

    /**
     * 更新时间
     * @var string
     */
    public $update_time = '';

    /**
     * @var int
     */
    public $ad1_check_package_code = 0;

    /**
     * @var int
     */
    public $ad2_check_package_code = 0;

    /**
     * ADTaskParam constructor.
     * @param array $property
     */
    public function __construct(array $property = [])
    {
        unset($property['targeting']);
        unset($property['setting']);

        parent::__construct($property);
    }

    public function paramHook()
    {
        if ($this->media_type) {
            if (!$this->creator) {
                $this->creator = Container::getSession()->name;
            }
            if (!$this->creator_id) {
                $this->creator_id = Container::getSession()->user_id;
            }
        }

        if (!$this->ext) {
            $this->ext = (object)[];
        }
    }

    /**
     * 生成插入数据库的数据
     * @return array
     */
    public function toSqlData()
    {
        return $this->toDbData();
    }

    /**
     * @return array
     */
    private function toDbData()
    {
        $db_data = $this->toArray();
        unset($db_data['id']);
        unset($db_data['update_time']);
        unset($db_data['create_time']);
        $db_data['targeting'] = json_encode($this->targeting, JSON_UNESCAPED_UNICODE);
        $db_data['setting'] = json_encode($this->setting, JSON_UNESCAPED_UNICODE);
        $db_data['other_setting'] = json_encode($this->other_setting, JSON_UNESCAPED_UNICODE);
        if ($this->site_config_pa) {
            $this->site_config = $this->site_config_pa->toArray();
        }
        $db_data['site_config'] = json_encode($this->site_config, JSON_UNESCAPED_UNICODE);
        $db_data['compose_config'] = json_encode($this->compose_config, JSON_UNESCAPED_UNICODE);
        $db_data['creative_list'] = json_encode($this->creative_list, JSON_UNESCAPED_UNICODE);
        $db_data['word_list'] = json_encode($this->word_list, JSON_UNESCAPED_UNICODE);
        $db_data['calc_rule_list'] = json_encode($this->calc_rule_list, JSON_UNESCAPED_UNICODE);
        $db_data['rta_list'] = json_encode($this->rta_list, JSON_UNESCAPED_UNICODE);
        $db_data['ad3_ids'] = json_encode($this->ad3_ids, JSON_UNESCAPED_UNICODE);
        $db_data['ext'] = json_encode($this->ext, JSON_UNESCAPED_UNICODE);

        return $db_data;
    }

    /**
     * @param $data
     * @return $this
     */
    public function initBySqlData($data)
    {
        $data['site_config'] = json_decode($data['site_config'], true);

        $targeting_class_name = (BatchADClassMap::getClassName(
            $data['media_type'],
            $data['media_agent_type'],
            BatchADClassMap::TARGETING_CLASS_NAME
        ));
        $this->targeting = new $targeting_class_name(json_decode($data['targeting'], true));
        unset($data['targeting']);

        $setting_class_name = (BatchADClassMap::getClassName(
            $data['media_type'],
            $data['media_agent_type'],
            BatchADClassMap::SETTING_CLASS_NAME
        ));
        $this->setting = new $setting_class_name(json_decode($data['setting'], true));
        unset($data['setting']);

        $other_setting_class_name = (BatchADClassMap::getClassName(
            $data['media_type'],
            $data['media_agent_type'],
            BatchADClassMap::OTHER_SETTING_CLASS_NAME
        ));
        $this->other_setting = new $other_setting_class_name(json_decode($data['other_setting'], true));
        unset($data['other_setting']);

        $data['compose_config'] = new ADComposeConfigParam(json_decode($data['compose_config'], true));
        $data['word_list'] = json_decode($data['word_list'], true);
        $data['creative_list'] = json_decode($data['creative_list'], true);
        $data['calc_rule_list'] = json_decode($data['calc_rule_list'], true);
        $data['rta_list'] = json_decode($data['rta_list'], true);
        $data['ad3_ids'] = json_decode($data['ad3_ids'], true);
        $data['ext'] = $data['ext'] ?? "{}";
        if ($data['ext'] == 'null') {
            $data['ext'] = "{}";
        }
        $data['ext'] = (object)json_decode($data['ext'], true);
        // 转字符串
        $data['ad1_id'] = (string)$data['ad1_id'];
        $data['ad2_id'] = (string)$data['ad2_id'];

        parent::__construct($data);

        return $this;
    }

    /**
     * @param $data
     * @return $this
     */
    public function initByFontData($data)
    {
        $targeting_class_name = (BatchADClassMap::getClassName(
            $data['media_type'],
            $data['media_agent_type'],
            BatchADClassMap::TARGETING_CLASS_NAME
        ));
        $this->targeting = (new $targeting_class_name($data['targeting']))->toArray();
        unset($data['targeting']);

        $setting_class_name = (BatchADClassMap::getClassName(
            $data['media_type'],
            $data['media_agent_type'],
            BatchADClassMap::SETTING_CLASS_NAME
        ));
        $this->setting = (new $setting_class_name($data['setting']))->toArray();
        unset($data['setting']);

        $other_setting_class_name = (BatchADClassMap::getClassName(
            $data['media_type'],
            $data['media_agent_type'],
            BatchADClassMap::OTHER_SETTING_CLASS_NAME
        ));
        $this->other_setting = new $other_setting_class_name($data['other_setting']);
        unset($data['other_setting']);

        $data['compose_config'] = new ADComposeConfigParam($data['compose_config']);

        parent::__construct($data);

        return $this;
    }

    /**
     * @param string $account_id
     * @param string $account_name
     * @param ADComposeCreativeListParam $creative_list
     * @param array $word_list
     * @param string $targeting_name
     * @param AbstractADTargetingContentParam $targeting
     * @param string $setting_name
     * @param AbstractADSettingContentParam $setting
     * @param array $site_config
     * @param ADComposeConfigParam $compose_config
     * @param AbstractADOtherSettingContentParam $other_setting
     * @param array $calc_rule_list
     * @param array $rta_list
     * @return $this
     */
    public function initByCompose(
        $account_id,
        $account_name,
        ADComposeCreativeListParam $creative_list,
        array $word_list,
        string $targeting_name,
        AbstractADTargetingContentParam $targeting,
        string $setting_name,
        AbstractADSettingContentParam $setting,
        array $site_config,
        ADComposeConfigParam $compose_config,
        AbstractADOtherSettingContentParam $other_setting,
        array $calc_rule_list,
        array $rta_list
    )
    {
        // 初始化账号
        $this->account_id = $account_id;
        $this->account_name = $account_name;

        // 初始化创意
        $this->creative_list = $creative_list->creative_list;

        // 初始化文案
        $this->word_list = $word_list;

        // 初始化定向
        $this->targeting_name = !empty($targeting_name) ? $targeting_name : '填充';
        $this->targeting = $targeting;

        // 初始化广告配置
        $this->site_config = $site_config;

        // 初始化参数
        $this->setting_name = !empty($setting_name) ? $setting_name : '填充';
        $this->setting = $setting;

        // 初始化组合参数
        $this->compose_config = $compose_config->toArray();

        // 初始化补充参数
        $this->other_setting = $other_setting->toArray();

        // 初始化智投规则
        $this->calc_rule_list = $calc_rule_list;

        // 初始RTA
        $this->rta_list = $rta_list;

        return $this;
    }

    /**
     * 传给mq用的数据
     * @return $this
     */
    public function toMQData()
    {
        return $this;
    }

    /**
     * 获取所有创意素材id
     * @return array
     */
    public function getAllCreativeID()
    {
        $creative_id_list = [];
        $creative_media_id_list = [];
        foreach ($this->creative_list as $creative_key => $creative_value) {
            if (isset($creative_value['cover_info'])) {
                if (isset($creative_value['cover_info']['image_id']) && isset($creative_value['video_info']['video_id'])) {
                    $creative_media_id_list[$creative_value['cover_info']['image_id']] = $creative_value['cover_info']['image_id'];
                    $creative_media_id_list[$creative_value['video_info']['video_id']] = $creative_value['video_info']['video_id'];
                } else {
                    $creative_id_list[] = $creative_value['video_info']['id'];
                    if ($creative_value['cover_info']['id'] !== -1) {
                        $creative_id_list[] = $creative_value['cover_info']['id'];
                    }
                }
            } else {
                if (isset($creative_value['image_info']['image_id'])) {
                    $creative_media_id_list[$creative_value['image_info']['image_id']] = $creative_value['image_info']['image_id'];
                } else {
                    if (isset($creative_value['image_info']['image_list']) && $creative_value['image_info']['image_list']) {
                        $creative_id_list = array_merge($creative_id_list, array_column($creative_value['image_info']['image_list'], 'id'));
                    } else {
                        $creative_id_list[] = $creative_value['image_info']['id'];
                    }
                }
            }
        }
        $creative_id_list = array_unique($creative_id_list);
        return [$creative_id_list, $creative_media_id_list];
    }

    /**
     * 获取所有创意素材id
     * @return array
     */
    public function getAllCreativeInfoMapById()
    {
        $creative_id_list = [];
        foreach ($this->creative_list as $creative_key => $creative_value) {
            if (isset($creative_value['cover_info'])) {
                $creative_id_list[$creative_value['cover_info']['id']] = basename($creative_value['cover_info']['url']);
                $creative_id_list[$creative_value['video_info']['id']] = basename($creative_value['video_info']['url']);
            } else {
                if (isset($creative_value['image_info']['image_list']) && $creative_value['image_info']['image_list']) {
                    array_map(function ($ele) use (&$creative_id_list) {
                        $creative_id_list[$ele['id']] = basename($ele['url']);
                    }, $creative_value['image_info']['image_list']);

                } else {
                    $creative_id_list[$creative_value['image_info']['id']] = basename($creative_value['image_info']['url']);
                }
            }
        }
        $creative_id_list = array_unique($creative_id_list);
        return $creative_id_list;
    }


    /**
     * 获取图片素材创意ID
     * @return array
     */
    public function getImageCreativeID()
    {
        $creative_id_list = [];
        foreach ($this->creative_list as $creative_key => $creative_value) {
            if (isset($creative_value['cover_info']['id'])) {
                $creative_id_list[] = $creative_value['cover_info']['id'];
            } else {
                if (isset($creative_value['image_info']['image_list']) && $creative_value['image_info']['image_list']) {
                    $creative_id_list = array_merge($creative_id_list, array_column($creative_value['image_info']['image_list'], 'id'));
                } else if (isset($creative_value['image_info']['id'])) {
                    $creative_id_list[] = $creative_value['image_info']['id'];
                }
            }
        }
        $creative_id_list = array_unique($creative_id_list);
        return $creative_id_list;
    }

    /**
     * 获取视频素材创意ID
     * @return array
     */
    public function getVideoCreativeID()
    {
        $creative_id_list = [];
        foreach ($this->creative_list as $creative_key => $creative_value) {
            if (isset($creative_value['cover_info']['id'])) {
                $creative_id_list[] = $creative_value['video_info']['id'];
            }
        }
        $creative_id_list = array_unique($creative_id_list);
        return $creative_id_list;
    }

    /**
     * 获取文案
     * @return array
     */
    public function getWordList()
    {
        if (!empty($this->word_list)) {
            return $this->word_list;
        } else {
            $word_list = [];
            foreach ($this->creative_list as $item) {
                if ($item['title'] ?? '') {
                    $word_list[] = $item['title'];
                }
                if ($item['title_list'] ?? []) {
                    $word_list = array_merge($word_list, $item['title_list']);
                }
            }
        }
        return array_unique($word_list);
    }

}
