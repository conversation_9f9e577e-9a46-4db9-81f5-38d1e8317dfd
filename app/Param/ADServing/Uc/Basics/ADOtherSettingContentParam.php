<?php
/**
 * Created by <PERSON>pStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Uc\Basics;

use App\Constant\BatchAD;
use App\Constant\UcEnum;
use App\Param\ADServing\Uc\ADOtherSettingContentParam as UcADOtherSettingContentParam;
use App\Service\PlatformAD\PlatformAD;

class ADOtherSettingContentParam extends UcADOtherSettingContentParam

{
    public $app_name;

    /**
     * 1：落地页
     * 2：iOS app
     * 4：安卓 app
     * @var int $objective_type
     */
    public $objective_type = 1;

    public $target_url;
    public $target_url_content;
    public $logo_map = [];
    public $page_map = [];
    public $platform_os = 111;
    public $download_type = '';
    public $jf_down_url_id;
    public $common_source = '';

    public $version_name = '';

    public $developer = '';

    public $function_desc = '';

    public function paramHook()
    {
        $this->logo_map = $this->tranObject($this->logo_map);
        $this->page_map = $this->tranObject($this->page_map);
    }

    public function validate()
    {
        $error_msg_list =[];

        if ($this->objective_type != 1 && !$this->app_name) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'app_name',
                'value' => $this->app_name,
                'msg' => "请填写应用名称"
            ];
        }

        if (empty($this->common_source)) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'common_source',
                'value' => $this->common_source,
                'msg' => '推广来源不能为空'
            ];
        } else {
            if (mb_strlen($this->common_source) > 10) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'common_source',
                    'value' => $this->common_source,
                    'msg' => '推广来源不能名称不能超过10个字符'
                ];
            }
        }

        if (!in_array($this->download_type, [UcEnum::DOWNLOAD_URL_MIN_GAME, UcEnum::DOWNLOAD_URL_JF]) && $this->objective_type == 1 && (empty($this->target_url) || empty($this->target_url_content))) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'target_url_content',
                'value' => $this->target_url_content,
                'msg' => "请选择正确的落地页配置信息"
            ];
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'target_url',
                'value' => $this->target_url,
                'msg' => "请选择正确的落地页配置信息"
            ];
        }

        return $error_msg_list;
    }

    /**
     * 获取账号落地页信息
     * @param $platform
     * @param $agent_id
     * @param $site_id
     * @return string
     */
    public function getUcPageWebUrlMapInfo($platform, $agent_id, $site_id)
    {
        if ($this->objective_type != 1) {
            return '';
        }
        return PlatformAD::create($platform)->getSiteLDYUrl($agent_id, $site_id, $this->target_url) ?? '';
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array
     */
    public function getUcPageExternalUrlMapInfo($account_id)
    {
        return json_decode($this->page_map[$account_id], true) ?? [];
    }

    public function format()
    {
        // TODO: Implement format() method.
    }
}
