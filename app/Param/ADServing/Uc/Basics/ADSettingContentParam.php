<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Uc\Basics;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Exception\AppException;
use App\Param\ADServing\Uc\ADSettingContentParam as UcADSettingContentParam;

class ADSettingContentParam extends UcADSettingContentParam

{
    /**
     * 第2阶段出价 付费
     * @var int
     */
    public $tcpa = 0;
    /**
     * 第1阶段出价 付费
     * @var int
     */
    public $cpc_bid = 0;
    /**
     * Roi系数
     * @var int
     */
    public $roi_target = 0;
    /**
     * Roi 投放方式
     */
    public $delivery_roi = 3;

    /**
     * @var int $convert_filter 0不过滤 1账户层级 2推广组层级 3计划层级 5公司层级
     */
    public $convert_filter = 0;

    /**
     * @var string $beianhao 落地页备案号
     */
    public $beianhao = '';


    public $schedule_type = 'SCHEDULE_FROM_NOW';
    public $start_date = '';
    public $end_date = '';

    public $opt_target = 3;
    public $budget = 0; // 预算出价
    public $budget_type = 0;  // 预算类型  1不限制 0限制
    /**
     * 2优先跑量 3均衡投放 4成本优先
     * @var int $delivery
     */
    public $delivery = 3;
    public $skip_first_stage = 1;
    public $bid = [];
    public $opt_bid = [];
    public $deep_bid = [];
    public $slot_bidding = 2;
    public $deep_convert_strategy = 0;
    public $deep_convert_type = 0;

    public $convert_repeat_type = 1;

    public $schedule_time = [];

    public $paused_adgroup = true; // 一级推广组  true = 暂停

    public $paused_campaign = true; // 二级计划计划开启  true = 暂停
    /**
     * 1CPC  2CPM 新版本 OCPC = 2
     * 推广计划CampaignType中的“计费方式”chargeType字段：
     * - 新增枚举值5，表示橙心投CPA（升级前该枚举值为4）
     * - 原oCPC非橙心投计划会返回枚举值1（CPC），升级后会返回枚举值2（CPM）
     * @var int $charge_type
     */
    public $charge_type = 2;


    public $enable_anxt = false;

    /**
     * 1 优选
     * 2 轮选
     * @var int $show_mode
     */
    public $show_mode = 1;

    /**
     * @var array $label
     */
    public $label = [];

    /**
     * @var array $industry
     */
    public $industry = [];

    public $creative_material_mode = 'custom';

    public $target_url_content = '';

    /**
     * 追踪参数
     * @var array
     */
//    public $track_args = [];


    /**
     * @var bool 是否严格validate
     */
    public $is_strict = true;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        unset($property['is_strict']);
        $this->is_strict = $is_strict;
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === NULL) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }


    public function paramHook()
    {
        /**
         *  原oCPC非橙心投计划会返回枚举值1（CPC），升级后会返回枚举值2（CPM）
         */
//        $this->charge_type = $this->charge_type == 1 ? 2 : $this->charge_type;
        $this->format();
    }


    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if ($this->cpc_bid <= 0) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'cpc_bid',
                'value' => $this->cpc_bid,
                'msg' => "出价不能为0 or 必须保留小数点2位"
            ];
        }

//        if (empty($this->track_args)) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                'key' => 'track_args',
//                'value' => $this->track_args,
//                'msg' => '追踪参数不能为空'
//            ];
//        }

        if (count($this->ucLabStringChanArray($this->label)) > 20) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'label',
                'value' => $this->label,
                'msg' => '创意标签不能超过20个'
            ];
        }

        if (count($this->industry) > 5) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'industry',
                'value' => $this->industry,
                'msg' => '创意-分类-不能超过5个'
            ];
        }

//        if ($this->enable_anxt && $this->deep_convert_type == 1000 && $this->deep_convert_strategy == 2) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                'key' => 'deep_convert_strategy',
//                'value' => $this->deep_convert_strategy,
//                'msg' => '安心投的情况下不能选择深度单目标出价'
//            ];
//        }

        if ($this->budget_type == 0 && !$this->budget) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                'key' => 'budget',
                'value' => $this->budget,
                'msg' => '日预算(budget)不能为空'
            ];
        }

        return $error_msg_list;
    }


    /**
     * 参数格式化
     */
    public function format()
    {
        if (!$this->schedule_time) {
            for ($i = 0; $i < 168; $i++) {
                $this->schedule_time[] = 1;
            }
        }

        if ($this->start_date) {
            $this->start_date = mb_strlen(strval($this->start_date)) > 10 ? $this->start_date / 1000 : $this->start_date;
            if (($this->start_date) < time()) {
                $this->start_date = time();
            }
        }

        if ($this->end_date) {
            $this->end_date = mb_strlen(strval($this->end_date)) > 10 ? $this->end_date / 1000 : $this->end_date;
        }

        if ($this->end_date && $this->end_date < $this->start_date) {
            $this->end_date = strtotime('+1 year');
        }
    }

    /**
     * 获取渠道组
     * @return int
     */
    public function getAgentGroup()
    {
        return AgentGroup::UC;
    }

    /**
     * @return string
     */
    public function getLDYContent()
    {
        if ($this->target_url_content) {
            return $this->target_url_content;
        } else {
            throw new AppException('落地页adid为空');
        }
    }

    /**
     * label 字符串转数组
     * @param $label
     * @return array|string[]
     */
    public function ucLabStringChanArray($label = null)
    {
        if (!is_array($label)) {
            return explode(',', trim(str_replace(['[', ']', '"', ' '], '', $label)));
        }
        return $label;
    }

    /**
     * UC trackArgs 参数参数转化
     * 推广创意，推广计划、推广组 低位至高位
     * 示例：
     * 选中推广创意和推广计划为：
     * “011”
     * 都不选择那么为 “000”
     * @return int
     */
    public function ucTrackArgs()
    {
        if (!empty($this->track_args)) {
            if (count($this->track_args) == 3) {
                return '111';
            } else {
                // 处理追踪权重 111 101 011 110
                $init = [0, 0, 0];
                foreach ($this->track_args as $v) {
                    $init[(int)$v - 1] = 1;
                }
                return implode('', $init);
            }
        }
        return '000';
    }
}