<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Uc\Basics;

use App\Constant\BatchAD;
use App\Constant\UcEnum;
use App\Param\ADServing\Uc\ADComposeContentParam as UcADComposeContentParam;
use App\Utils\Helpers;

class ADComposeContentParam extends UcADComposeContentParam

{
    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];


        foreach ($this->account_list->account_list as $key => $value) {

            if (empty($this->other_setting->logo_map[$value['account_id']])) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'logo_map',
                    'value' => $this->other_setting->logo_map,
                    'msg' => "账号{$value['account_id']}需要选择logo"
                ];
            }

            // 锦帆地页
            if ($this->other_setting->objective_type == 1 && in_array($this->other_setting->download_type, [UcEnum::DOWNLOAD_URL_JF, UcEnum::DOWNLOAD_URL_MIN_GAME])) {
                if (empty($this->other_setting->page_map[$value['account_id']])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'page_map',
                        'value' => $this->other_setting->page_map,
                        'msg' => "请选择账号--{$value['account_id']}--正确的锦帆落地页配置信息"
                    ];
                }
            }
        }
        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
            if ($this->site_config->convert_data_type == 1) {
                // 深度转化问题处理
//                if ($this->site_config->deep_external_action != 1000){
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                        'key' => 'deep_convert_type',
//                        'value' => 0,
//                        'msg' => "深度转化类型错误-参数包选择ROI系数时《广告属性》深度转化必须是付费"
//                    ];
//                }
                // roi 系数处理
                if ($setting->roi_target <= 0 || $setting->roi_target > 1000) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'roi_target',
                        'value' => $setting->roi_target,
                        'msg' => "广告属性-转化统计方式-选择了-回传真实就必须选择ROI-roi系数不能小于0 or 不能大于1000"
                    ];
                }
            } else {
                if ($this->site_config->deep_external_action && $this->site_config->deep_external_action != $setting->deep_convert_type) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'deep_convert_type',
                        'value' => 0,
                        'msg' => "深度转化类型错误-需对应参数包转化类型"
                    ];
                }
            }

            // 选择付费和roi的时候进入
            if ($setting->tcpa <= 0 && $this->site_config->deep_external_action == 1000 && $this->site_config->convert_data_type != 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'cpc_bid',
                    'value' => $setting->cpc_bid,
                    'msg' => "付费-ROI-出价不能为0 or 必须保留小数点2位"
                ];
            }

            // 选择激活和roi的时候进入
            if ($setting->cpc_bid <= 0 && $this->site_config->deep_external_action != 1000 && $this->site_config->convert_data_type == 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'key' => 'cpc_bid',
                    'value' => $setting->cpc_bid,
                    'msg' => "激活-ROI-出价不能为0 or 必须保留小数点2位"
                ];
            }

        }


        foreach ($this->material_list->all_video_list as $video) {
            foreach ($video['cover_list'] as $cover) {
                if ($video['width'] < $video['height']) {
                    if (($video['width'] != 720 && $video['height'] != 1280) || ($cover['width'] != 720 && $cover['height'] != 1280)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$video['url']} -- UC竖版视频 或者 封面必须是720*1280"
                        ];
                    }
                }
//                if ($video['width'] > $video['height']) {
//                    if ($cover['width'] != 640 && $cover['height'] != 360) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "{$video['url']} -- UC横版视频封面必须是640*360"
//                        ];
//                    }
//                } else {
//                    if (($video['width'] != 720 && $video['height'] != 1280) || ($cover['width'] != 720 && $cover['height'] != 1280)) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "{$video['url']} -- UC竖版视频 或者 封面必须是720*1280"
//                        ];
//                    }
//                    if ($video['width'] != $cover['width'] || $video['height'] != $cover['height']){
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                            'value' => 0,
//                            'msg' => "{$video['url']}封面与视频的尺寸不一致"
//                        ];
//                    }
//                }
            }
        }

        foreach ($this->material_list->all_image_list as $image_info) {
            if ($image_info['width'] < $image_info['height']) {
                if (($image_info['width'] != 1080 && $image_info['height'] != 1920)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => "{$image_info['url']} -- UC竖图封面必须是1080*1920且必须小于500KB"
                    ];
                }
            }
//            if ($image_info['width'] > $image_info['height']) {
//                if (($image_info['width'] != 640 && $image_info['height'] != 360)) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                        'value' => 0,
//                        'msg' => "{$image_info['url']} -- UC横图封面必须是640*360且必须小于500KB "
//                    ];
//                }
//            } else {
//                if (($image_info['width'] != 1080 && $image_info['height'] != 1920)) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                        'value' => 0,
//                        'msg' => "{$image_info['url']} -- UC竖图封面必须是1080*1920且必须小于500KB"
//                    ];
//                }
//            }

        }

        foreach ($this->word_list->getWordContent() as $key => $value) {

            if (strpos($value, "%") !== false) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => 'word_list',
                    'value' => $value,
                    'msg' => "{$value},此文案有“%”,UC文案不允许有百分号"
                ];
            }
            if (strpos($value, "【") !== false) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => 'word_list',
                    'value' => $value,
                    'msg' => "{$value},此文案有“【”,UC文案不允许有【"
                ];
            }
            if (strpos($value, "】") !== false) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => 'word_list',
                    'value' => $value,
                    'msg' => "{$value},此文案有“】”,UC文案不允许有】"
                ];
            }
            $len = Helpers::ADServingStrLen($value);
            if ($len > 140) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => 'word_list',
                    'value' => $value,
                    'msg' => "{$value},此文案为{$len}个字符，大于70个字符"
                ];
            }
        }

        return $error_msg_list;
    }

    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD1Name() method.
    }

    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD2Name() method.
    }
}
