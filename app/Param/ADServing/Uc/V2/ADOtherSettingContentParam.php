<?php
/**
 * Created by <PERSON><PERSON>Storm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Uc\V2;

use App\Param\ADServing\Uc\ADOtherSettingContentParam as UcADOtherSettingContentParam;

class ADOtherSettingContentParam extends UcADOtherSettingContentParam
{
    public $logo_map = [];
    public $page_map = [];

    public $convert_map = [];

    public function paramHook()
    {
        $this->logo_map = $this->tranObject($this->logo_map);
        $this->page_map = $this->tranObject($this->page_map);
        $this->convert_map = $this->tranObject($this->convert_map);
    }

    public function validate()
    {
        return parent::validate();
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array
     */
    public function getUcPageExternalUrlMapInfo($account_id)
    {
        return json_decode($this->page_map[$account_id], true) ?? [];
    }

    /**
     * @param $account_id
     * @return array
     */
    public function getConvertId($account_id)
    {
        return json_decode($this->convert_map[$account_id], true) ?? [];
    }
}
