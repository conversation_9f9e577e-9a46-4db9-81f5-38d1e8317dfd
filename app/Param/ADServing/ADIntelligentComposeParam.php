<?php

namespace App\Param\ADServing;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class ADIntelligentComposeParam extends AbstractParam
{
    public $name;

    public $media_type;

    public $compose_id;
    public $data_media_type_list = [];
    public $data_is_relate_root_game_id = 0;
    public $relate_root_game_id_list = [];
    public $data_is_relate_clique_game_id = 0;
    public $relate_clique_game_id_list = [];
    public $material_compose_type;
    public $word_compose_type;
    public $top_material_filter;
    public $potential_material_filter;
    public $new_material_filter;

    public $word_select_type = 0;
    public $custom_word_packet_id = 0;
    public $custom_material_packet_id = 0;
    public $custom_material_packet_list = [];
    public $rule_list;
    public $create_week_day_list = [1, 2, 3, 4, 5, 6, 7];

    public $ad_create_time;
    public $material_num;
    public $word_num;
    public $dispatch_type = 'account';
    public $repeated_creation_times;

    public $creator;

    public $status = 1;

    public function initBySql($object)
    {
        $this->name = $object->name;
        $this->media_type = $object->media_type;
        $this->compose_id = $object->compose_id;
        $this->word_compose_type = $object->word_compose_type;
        $this->data_media_type_list = json_decode($object->data_media_type_list, true);
        $this->data_is_relate_root_game_id = $object->data_is_relate_root_game_id;
        $this->relate_root_game_id_list = json_decode($object->relate_root_game_id_list, true);
        $this->data_is_relate_clique_game_id = $object->data_is_relate_clique_game_id;
        $this->relate_clique_game_id_list = json_decode($object->relate_clique_game_id_list, true);
        $this->material_compose_type = $object->material_compose_type;
        $this->top_material_filter = json_decode($object->top_material_filter, true);
        $this->potential_material_filter = json_decode($object->potential_material_filter, true);
        $this->new_material_filter = json_decode($object->new_material_filter, true);
        $this->word_select_type = $object->word_select_type;
        $this->custom_word_packet_id = $object->custom_word_packet_id;
        $this->custom_material_packet_id = $object->custom_material_packet_id;
        $this->custom_material_packet_list = json_decode($object->custom_material_packet_list, true);
        $this->rule_list = json_decode($object->rule_list, true);
        $this->create_week_day_list = json_decode($object->create_week_day_list, true);
        $this->ad_create_time = $object->ad_create_time;
        $this->material_num = $object->material_num;
        $this->word_num = $object->word_num;
        $this->dispatch_type = $object->dispatch_type;
        $this->repeated_creation_times = $object->repeated_creation_times;
        $this->creator = $object->creator;
        $this->status = $object->status;
        return $this;
    }

    public function validate()
    {
        if (!$this->media_type) {
            throw new AppException('媒体类型为空');
        }

        foreach ($this->top_material_filter['filter_list'] as $filter) {
            if (empty($filter['key']) || empty($filter['condition']) || (empty($filter['value'] ?? '') && !is_numeric($filter['value'] ?? ''))) {
                throw new AppException('优质素材筛选的条件项有空值');
            }
        }
        foreach ($this->top_material_filter['expand_filter'] as $expand_filter) {
            foreach ($expand_filter['filter_list'] as $filter) {
                if (empty($filter['key']) || empty($filter['condition']) || (empty($filter['value'] ?? '') && !is_numeric($filter['value'] ?? ''))) {
                    throw new AppException('优质素材的递进筛选的条件项有空值');
                }
            }

        }

        foreach ($this->potential_material_filter['filter_list'] as $filter) {
            if (empty($filter['key']) || empty($filter['condition']) || (empty($filter['value'] ?? '') && !is_numeric($filter['value'] ?? ''))) {
                throw new AppException('潜力素材筛选的条件项有空值');
            }
        }
        foreach ($this->potential_material_filter['expand_filter'] as $expand_filter) {
            foreach ($expand_filter['filter_list'] as $filter) {
                if (empty($filter['key']) || empty($filter['condition']) || (empty($filter['value'] ?? '') && !is_numeric($filter['value'] ?? ''))) {
                    throw new AppException('潜力素材的递进筛选的条件项有空值');
                }
            }

        }

        foreach ($this->new_material_filter['filter_list'] as $filter) {
            if (empty($filter['key']) || empty($filter['condition']) || (empty($filter['value'] ?? '') && !is_numeric($filter['value'] ?? ''))) {
                throw new AppException('新素材筛选的条件项有空值');
            }
        }
        foreach ($this->new_material_filter['expand_filter'] as $expand_filter) {
            foreach ($expand_filter['filter_list'] as $filter) {
                if (empty($filter['key']) || empty($filter['condition']) || (empty($filter['value'] ?? '') && !is_numeric($filter['value'] ?? ''))) {
                    throw new AppException('新素材的递进筛选的条件项有空值');
                }
            }
        }

        if (!is_array($this->custom_material_packet_list)) {
            throw new AppException('自定义素材包格式为有误');
        }
    }

    public function toData()
    {
        $this->validate();
        $data = parent::toArray();
        if (!$data['creator']) {
            $data['creator'] = Container::getSession()->name;
        }
        $data['data_media_type_list'] = json_encode($data["data_media_type_list"], JSON_UNESCAPED_UNICODE);
        $data['relate_root_game_id_list'] = json_encode($data["relate_root_game_id_list"], JSON_UNESCAPED_UNICODE);
        $data['relate_clique_game_id_list'] = json_encode($data["relate_clique_game_id_list"], JSON_UNESCAPED_UNICODE);
        $data['top_material_filter'] = json_encode($data["top_material_filter"], JSON_UNESCAPED_UNICODE);
        $data['potential_material_filter'] = json_encode($data["potential_material_filter"], JSON_UNESCAPED_UNICODE);
        $data['new_material_filter'] = json_encode($data["new_material_filter"], JSON_UNESCAPED_UNICODE);
        $data['custom_material_packet_list'] = json_encode($data["custom_material_packet_list"], JSON_UNESCAPED_UNICODE);
        $data['rule_list'] = json_encode($data["rule_list"], JSON_UNESCAPED_UNICODE);
        $data['create_week_day_list'] = json_encode($data["create_week_day_list"], JSON_UNESCAPED_UNICODE);
        return $data;
    }

    public function toLogData()
    {
        $data = parent::toArray();
        unset($data['status']);
        return $data;
    }

    public function getMaterialNumConfig($material_num = 0): array
    {
        if (!$material_num) {
            $material_num = $this->material_num;
        }

        if (in_array($this->material_compose_type, ['top_potential', 'top_new', 'potential_new', 'custom_top', 'custom_potential', 'custom_new'])) {
            switch ($this->material_compose_type) {
                case 'top_potential':
                    return ['top' => ceil($material_num / 2), 'potential' => floor($material_num / 2), 'new' => 0, 'custom' => 0];
                case 'top_new':
                    return ['top' => ceil($material_num / 2), 'potential' => 0, 'new' => floor($material_num / 2), 'custom' => 0];
                case 'potential_new':
                    return ['top' => 0, 'potential' => ceil($material_num / 2), 'new' => floor($material_num / 2), 'custom' => 0];
                case 'custom_top':
                    return ['top' => floor($material_num / 2), 'potential' => 0, 'new' => 0, 'custom' => ceil($material_num / 2)];
                case 'custom_potential':
                    return ['top' => 0, 'potential' => floor($material_num / 2), 'new' => 0, 'custom' => ceil($material_num / 2)];
                case 'custom_new':
                    return ['top' => 0, 'potential' => 0, 'new' => floor($material_num / 2), 'custom' => ceil($material_num / 2)];
            }
        } else {
            switch ($this->material_compose_type) {
                case 'top':
                    return ['top' => $material_num, 'potential' => 0, 'new' => 0, 'custom' => 0];
                case 'potential':
                    return ['top' => 0, 'potential' => $material_num, 'new' => 0, 'custom' => 0];
                case 'new':
                    return ['top' => 0, 'potential' => 0, 'new' => $material_num, 'custom' => 0];
                case 'custom':
                    return ['top' => 0, 'potential' => 0, 'new' => 0, 'custom' => $material_num];
                default:
                    return ['top' => 0, 'potential' => 0, 'new' => 0];
            }
        }

        throw new AppException('错误的素材分配规则');
    }

}
