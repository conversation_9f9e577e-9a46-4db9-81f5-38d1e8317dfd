<?php

namespace App\Param\ADServing;

use App\Exception\AppException;
use App\Param\AbstractParam;

class ADIntelligentComposeLogSearchParam extends AbstractParam
{
    public $intelligent_compose_id;

    public $create_time = [];

    public $page = 1;

    public $rows = 50;

    protected function paramHook()
    {
        if(!$this->intelligent_compose_id){
            throw new AppException('智创组合id不能为空');
        }
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 50;
        }
        if ($this->create_time) {
            $this->create_time = array_map(function ($time) {
                return date('Y-m-d H:i:s', substr($time, 0, 10));
            }, $this->create_time);
        }
    }
}
