<?php
/**
 * Created by PhpStorm.
 * User: zzh
 * Date: 2020/03/13
 * Time: 16:10
 */

namespace App\Param\ADServing;

use App\Param\AbstractParam;
use App\Container;
use App\Exception\AppException;

class ADMaterialPacketParam extends AbstractParam
{
    public $id;
    public $name;
    public $video_vertical_list;
    public $video_list;
    public $group_image_list;
    public $group_video_list = [];
    public $small_image_list;
    public $large_image_list;
    public $large_vertical_image_list;
    public $audio_list;
    public $media_type;
    public $platform;
    public $creator;
    public $state;
    public $update_time;
    public $create_time;

    public function paramHook()
    {
        $platform = '';

        if ($this->state == null) {
            $this->state = 1;
        }
        if (!$this->update_time) {
            $this->update_time = time();
        }
        if (!$this->create_time) {
            $this->create_time = time();
        }
        $this->creator = Container::getSession()->name;

        if (!$this->name) {
            throw new AppException('素材包名称不能为空');
        }

        if (!$this->platform) {
            $this->platform = '';
        }

        if (!$this->group_video_list || !is_array($this->group_video_list)) {
            $this->group_video_list = [];
        }
    }

    public function toInsertData()
    {
        $data = $this->toArray();
        $data['group_image_list'] = json_encode($data['group_image_list'], JSON_UNESCAPED_UNICODE);
        $data['group_video_list'] = json_encode($data['group_video_list'], JSON_UNESCAPED_UNICODE);
        $data['small_image_list'] = json_encode($data['small_image_list'], JSON_UNESCAPED_UNICODE);
        $data['large_image_list'] = json_encode($data['large_image_list'], JSON_UNESCAPED_UNICODE);
        $data['large_vertical_image_list'] = json_encode($data['large_vertical_image_list'], JSON_UNESCAPED_UNICODE);
        $data['video_vertical_list'] = json_encode($data['video_vertical_list'], JSON_UNESCAPED_UNICODE);
        $data['video_list'] = json_encode($data['video_list'], JSON_UNESCAPED_UNICODE);
        $data['audio_list'] = $data['audio_list'] ? json_encode($data['audio_list'], JSON_UNESCAPED_UNICODE) : '[]';
        unset($data['id']);
        return $data;
    }
}