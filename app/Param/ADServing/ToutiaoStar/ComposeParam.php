<?php

namespace App\Param\ADServing\ToutiaoStar;


use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;
use App\Utils\Helpers;
use Elasticsearch\Endpoints\Cat\Help;

class ComposeParam extends AbstractParam
{
    public $id = 0;

    public $name = '';

    public $company = '';

    public $platform = '';

    public $account_id = '';

    public $account_name = '';

    public $type = 1;  // 1：短视频 2：直播

    public $site_config = [];

    public $author_setting_list = [];

    public $demand_setting = [];

    public $creator = '';

    public $creator_id = '';

    public $editor = '';

    public $is_del = 0;

    public function paramHook()
    {
        $this->demand_setting['detail_demand'] = $this->demand_setting['detail_demand'] ?: '1';
        $this->demand_setting['product_information'] = $this->demand_setting['product_information'] ?: '1';
        foreach ($this->author_setting_list as &$author_setting) {
            // 自定义开播时间
            if ($author_setting['expiration_live_type'] === 2) {
                if (!empty($author_setting['expiration_live_time_list'])) {
                    $author_setting['expiration_live_time_list'] = array_slice(array_values(array_filter($author_setting['expiration_live_time_list'])), 0, $author_setting['custom_order_count']);
                } else {
                    $author_setting['expiration_live_time_list'] = [];
                }
            } else {
                // 统一开播时间
                $start_time = strtotime($author_setting['unite_expiration_date'][0]);
                $end_time = strtotime($author_setting['unite_expiration_date'][1]);
                $author_setting['expiration_live_time_list'] = [];
                for($i = $start_time; $i <= $end_time; $i = $i + 86400) {
                    $author_setting['expiration_live_time_list'][] = date('Y-m-d', $i) . ' '. $author_setting['unit_expiration_hour'];
                }
                $author_setting['custom_order_count'] = count($author_setting['expiration_live_time_list']);
            }
        }
    }

    public function validate()
    {
        $error_msg_list = [];

//        $product_information_length = Helpers::ADServingStrLen($this->demand_setting['product_information'] ?? '');
//        if ($product_information_length < 1 || $product_information_length > 500) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'product_information',
//                'msg' => '产品介绍要求1-500字',
//            ];
//        }

        $brand_name_length = Helpers::ADServingStrLen($this->demand_setting['brand_name'] ?? '');
        if ($this->type == 1 && ($brand_name_length < 1 || $brand_name_length > 50)) {
            $error_msg_list[] = [
                'type' => 'demand_setting',
                'key' => 'product_information',
                'msg' => '品牌名称要求1-50字',
            ];
        }

        if ($this->type == 1 && !preg_match("/^1\d{10}$/", $this->demand_setting['contact_phone'] ?? '')) {
            $error_msg_list[] = [
                'type' => 'demand_setting',
                'key' => 'contact_phone',
                'msg' => '手机号码不正确',
            ];
        }

        if ($this->type == 1 && !$this->demand_setting['contact_name']) {
            $error_msg_list[] = [
                'type' => 'demand_setting',
                'key' => 'contact_name',
                'msg' => '联系人不能为空',
            ];
        }

        if ($this->type == 1 && (!$this->demand_setting['product_industry'])) {
            $error_msg_list[] = [
                'type' => 'demand_setting',
                'key' => 'product_industry',
                'msg' => '所属行业不能为空',
            ];
        }

        if ($this->type == 1 && ($this->demand_setting['ad_sync_conf']['open'])) {
            if (!$this->demand_setting['ad_sync_conf']['advertiser_id']) {
                $error_msg_list[] = [
                    'type' => 'demand_setting',
                    'key' => 'advertiser_id',
                    'msg' => '广告主ID不能为空',
                ];
            }
        }

        if ($this->type == 2) {

            if (!$this->demand_setting['contact_info']) {
                $error_msg_list[] = [
                    'type' => 'demand_setting',
                    'key' => 'contact_info',
                    'msg' => '联系人不能为空',
                ];
            }
            if (!($this->demand_setting['project_info']['promotion_indicator_type'] ?? '')) {
                $error_msg_list[] = [
                    'type' => 'demand_setting',
                    'key' => 'promotion_indicator_type',
                    'msg' => '行动转化不能为空',
                ];
            }

            if (!($this->demand_setting['brand_info'] ?? [])) {
                $error_msg_list[] = [
                    'type' => 'demand_setting',
                    'key' => 'brand_info',
                    'msg' => '品牌不能为空',
                ];
            }

            if (!($this->demand_setting['project_info']['second_class_category'] ?? '')) {
                $error_msg_list[] = [
                    'type' => 'demand_setting',
                    'key' => 'second_class_category',
                    'msg' => '所属行业不能为空',
                ];
            }
        }

//        if ($this->type == 1 && (!$this->demand_setting['detail_demand'])) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'detail_demand',
//                'msg' => '详细要求不能为空',
//            ];
//        }
//
//        if ($this->type == 2 && (!$this->demand_setting['detail_demand'])) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'detail_demand',
//                'msg' => '直播要求不能为空',
//            ];
//        }
//
//        if ($this->type == 1 && ($this->demand_setting['script_demand'] && Helpers::ADServingStrLen($this->demand_setting['script_demand']) > 20)) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'script_demand',
//                'msg' => '文案要求20字以内',
//            ];
//        }
//
//        if ($this->demand_setting['special_topic'] && Helpers::ADServingStrLen($this->demand_setting['special_topic']) > 40) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'special_topic',
//                'msg' => '指定话题要求40字以内',
//            ];
//        }
//
//        if ($this->type == 1 && ($this->demand_setting['music'] && Helpers::ADServingStrLen($this->demand_setting['music']) > 20)) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'music',
//                'msg' => '指定音乐要求20字以内',
//            ];
//        }
//
//        if ($this->type == 1 && ($this->demand_setting['scene_demand'] && Helpers::ADServingStrLen($this->demand_setting['scene_demand']) > 20)) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'scene_demand',
//                'msg' => '场景要求20字以内',
//            ];
//        }
//
//        if ($this->type == 1 && ($this->demand_setting['prop_demand'] && Helpers::ADServingStrLen($this->demand_setting['prop_demand']) > 20)) {
//            $error_msg_list[] = [
//                'type' => 'demand_setting',
//                'key' => 'prop_demand',
//                'msg' => '道具要求20字以内',
//            ];
//        }

        // 达人信息校验
        if (!$this->author_setting_list) {
            throw new AppException('请添加达人');
        }
        foreach ($this->author_setting_list as $author_setting) {
            if (!$author_setting['aweme_id']) {
                throw new AppException('达人ID不存在');
            } else {
                if ($this->type == 1 && (!($author_setting['industry_component']['industry_anchor_id'] ?? ''))) {
                    $error_msg_list[] = [
                        'type' => 'author_setting',
                        'target' => $author_setting['aweme_id'],
                        'key' => 'industry_anchor_id',
                        'msg' => '达人id' . '下组件必选',
                    ];
                }
                if ($this->type == 2 && (!($author_setting['handle_component']['handle_anchor_id'] ?? ''))) {
                    $error_msg_list[] = [
                        'type' => 'author_setting',
                        'target' => $author_setting['aweme_id'],
                        'key' => 'handle_anchor_id',
                        'msg' => '达人id' . '下组件必选',
                    ];
                }

                if ($this->type == 1 && (!$author_setting['expiration_time'])) {
                    $error_msg_list[] = [
                        'type' => 'author_setting',
                        'target' => $author_setting['aweme_id'],
                        'key' => 'expiration_time',
                        'msg' => '期望发布时间不能为空',
                    ];
                }

                if ($this->type == 1 && (!$author_setting['expiration_time_end'])) {
                    $error_msg_list[] = [
                        'type' => 'author_setting',
                        'target' => $author_setting['aweme_id'],
                        'key' => 'expiration_time_end',
                        'msg' => '期望最迟发布时间不能为空',
                    ];
                }

                if ($this->type == 1 && (!$author_setting['expect_remain_time'] || $author_setting['expect_remain_time'] > 365)) {
                    $error_msg_list[] = [
                        'type' => 'author_setting',
                        'target' => $author_setting['aweme_id'],
                        'key' => 'expect_remain_time',
                        'msg' => '期望保留时间要求1-365天',
                    ];
                }

                if ($this->type == 2) {

                    if (!$author_setting['demand_name']) {
                        $error_msg_list[] = [
                            'type' => 'author_setting',
                            'target' => $author_setting['aweme_id'],
                            'key' => 'demand_name',
                            'msg' => '任务名称不能为空',
                        ];
                    } else {
                        if (mb_strlen($author_setting['demand_name']) > 60) {
                            $error_msg_list[] = [
                                'type' => 'author_setting',
                                'target' => $author_setting['aweme_id'],
                                'key' => 'demand_name',
                                'msg' => '任务名称长度不能超过60',
                            ];
                        }
                    }

//                    $str_live_due_time = strtotime($author_setting['live_due_time'] ?? '');

                    if ($author_setting['video_type'] == 9) {

//                        if (!$author_setting['live_due_time']) {
//                            $error_msg_list[] = [
//                                'type' => 'author_setting',
//                                'target' => $author_setting['aweme_id'],
//                                'key' => 'live_due_time',
//                                'msg' => '直播截止日期不能为空',
//                            ];
//                        } else {
//                            $day_diff = Helpers::dateDiff(date('Ymd'), $author_setting['live_due_time']);
//                            if ($day_diff < 2 || $day_diff > 7) {
//                                $error_msg_list[] = [
//                                    'type' => 'author_setting',
//                                    'target' => $author_setting['aweme_id'],
//                                    'key' => 'live_due_time',
//                                    'msg' => '直播截止日期不正确，请重新选择',
//                                ];
//                            }
//                            if (!$author_setting['accept_expiration_time']) {
//                                $error_msg_list[] = [
//                                    'type' => 'author_setting',
//                                    'target' => $author_setting['aweme_id'],
//                                    'key' => 'accept_expiration_time',
//                                    'msg' => '达人最晚接单日期不能为空',
//                                ];
//                            } else {
//                                $day_diff = Helpers::dateDiff(date('Ymd'), $author_setting['accept_expiration_time']);
//                                if($day_diff < 0) {
//                                    $error_msg_list[] = [
//                                        'type' => 'author_setting',
//                                        'target' => $author_setting['aweme_id'],
//                                        'key' => 'accept_expiration_time',
//                                        'msg' => '接单日期不能早于：'.date('Y-m-d',strtotime('+1 days')),
//                                    ];
//                                } else {
//                                    // 根据直播时长校验接单日期是否正确
//                                    $days = ceil($author_setting['duration'] / 24);
//                                    if (Helpers::dateDiff($author_setting['accept_expiration_time'], $author_setting['live_due_time']) < $days) {
//                                        $error_msg_list[] = [
//                                            'type' => 'author_setting',
//                                            'target' => $author_setting['aweme_id'],
//                                            'key' => 'accept_expiration_time',
//                                            'msg' => '请选择' . date('Y-m-d', strtotime("-{$days} day", $str_live_due_time)) . '内的接单日期',
//                                        ];
//                                    }
//                                }
//                            }
//                        }

                        if ($author_setting['expiration_live_time_list']) {
                            foreach ($author_setting['expiration_live_time_list'] as $item) {
                                $day_diff = Helpers::dateDiff(date('Ymd'), $item);
                                if ($day_diff < 0) {
                                    $error_msg_list[] = [
                                        'type' => 'author_setting',
                                        'target' => $author_setting['aweme_id'],
                                        'key' => 'expiration_live_time_list',
                                        'msg' => '期望开播日期不正确，请重新选择',
                                    ];
                                }
                                if ($day_diff > 10) {
                                    $error_msg_list[] = [
                                        'type' => 'author_setting',
                                        'target' => $author_setting['aweme_id'],
                                        'key' => 'expiration_live_time_list',
                                        'msg' => '服务类型选择按小时，期望开播日期不能超过当前时间10天',
                                    ];
                                }
                            }
                        }
                    }

                    if (!$author_setting['expiration_live_time_list']) {
                        $error_msg_list[] = [
                            'type' => 'author_setting',
                            'target' => $author_setting['aweme_id'],
                            'key' => 'expiration_live_time_list',
                            'msg' => '期望开播日期不能为空',
                        ];
                    } else {
                        if ($author_setting['expiration_live_type'] === 1) {
                            if (!$author_setting['unit_expiration_hour']) {
                                $error_msg_list[] = [
                                    'type' => 'author_setting',
                                    'target' => $author_setting['aweme_id'],
                                    'key' => 'expiration_live_time_list',
                                    'msg' => '期望开播日期不能为空',
                                ];
                            }
                        }
                    }

                    //  校验主播录入情况
                    if (!$author_setting['interface_person']) {
                        $error_msg_list[] = [
                            'type' => 'author_setting',
                            'target' => $author_setting['aweme_id'],
                            'key' => 'interface_person',
                            'msg' => '直播运营人员不能为空',
                        ];
                    }

                    $need_judge_time_lapping = true;

                    foreach ($author_setting['live_anchor_list'] as $anchor_index => $live_anchor_setting) {
                        if (!$live_anchor_setting['live_anchor_id']) {
                            $error_msg_list[] = [
                                'type' => 'author_setting',
                                'target' => $author_setting['aweme_id'],
                                'key' => "live_anchor_list[{$anchor_index}].live_anchor_id",
                                'msg' => '请选择主播',
                            ];
                        } else {
                            if (!$live_anchor_setting['live_prices']) {
                                $error_msg_list[] = [
                                    'type' => 'author_setting',
                                    'target' => $author_setting['aweme_id'],
                                    'key' => "live_anchor_list[{$anchor_index}].live_anchor_id",
                                    'msg' => '主播单价未录入，不可使用',
                                ];
                            }
                            if (!$live_anchor_setting['live_price']) {
                                $error_msg_list[] = [
                                    'type' => 'author_setting',
                                    'target' => $author_setting['aweme_id'],
                                    'key' => "live_anchor_list[{$anchor_index}].live_price_type",
                                    'msg' => '主播单价为0，请确认结算方式对应单价是否录入',
                                ];
                            }
                            if (count($author_setting['live_anchor_list']) > 1 && (!$live_anchor_setting['live_time'] || !(count(array_filter($live_anchor_setting['live_time'])) == 2))) {
                                $need_judge_time_lapping = false;
                                $error_msg_list[] = [
                                    'type' => 'author_setting',
                                    'target' => $author_setting['aweme_id'],
                                    'key' => "live_anchor_list[{$anchor_index}].live_time",
                                    'msg' => '预计直播时间不能为空',
                                ];
                            }
                        }
                    }
                    if (count($author_setting['live_anchor_list']) > 1) {
                        if ($need_judge_time_lapping && Helpers::hasOverlappingIntervals(array_column($author_setting['live_anchor_list'], 'live_time'))) {
                            $error_msg_list[] = [
                                'type' => 'author_setting',
                                'target' => $author_setting['aweme_id'],
                                'key' => "live_anchor_list[0].live_time",
                                'msg' => '多人直播预计直播时间有交叉，请检查',
                            ];
                        }
                        $price_types = array_column($author_setting['live_anchor_list'], 'live_price_type');
                        if (in_array(5, $price_types) && count(array_unique($price_types)) > 1) {
                            $error_msg_list[] = [
                                'type' => 'author_setting',
                                'target' => $author_setting['aweme_id'],
                                'key' => "live_anchor_list[0].live_price_type",
                                'msg' => '多人直播结算方式只能同时为CPS结算方式或同时为非CPS结算方式，请检查',
                            ];
                        }
                    }
                }
            }
        }
        return $error_msg_list;
    }

    public function toInsertData()
    {
        $this->creator = Container::getSession()->get('name');
        $this->creator_id = Container::getSession()->get('user_id');
        $data = $this->toArray();
        $data['site_config'] = $data['site_config'] ? json_encode($this->site_config) : '{}';
        $data['demand_setting'] = $data['demand_setting'] ? json_encode($this->demand_setting) : '{}';
        $data['author_setting_list'] = $data['author_setting_list'] ? json_encode($this->author_setting_list) : '[]';
        unset($data['id']);
        return $data;
    }

    public function toEditData()
    {
        $this->editor = Container::getSession()->get('name');
        $data = $this->toArray();
        $data['site_config'] = $data['site_config'] ? json_encode($this->site_config) : '{}';
        $data['demand_setting'] = $data['demand_setting'] ? json_encode($this->demand_setting) : '{}';
        $data['author_setting_list'] = $data['author_setting_list'] ? json_encode($this->author_setting_list) : '[]';
        unset($data['id']);
        return $data;
    }

}