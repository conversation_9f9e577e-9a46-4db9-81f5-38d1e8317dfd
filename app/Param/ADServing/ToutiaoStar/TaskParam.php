<?php

namespace App\Param\ADServing\ToutiaoStar;


use App\Container;
use App\Param\AbstractParam;

class TaskParam extends AbstractParam
{
    public $id = 0;

    public $compose_id = '';

    public $compose_name = '';

    public $company = '';

    public $platform = '';

    public $account_id = '';

    public $account_name = '';

    public $type = 1; // 1：短视频 2：直播

    public $campaign_id = '0';

    public $star_task_id = '0';

    public $star_task_name = '';

    public $site_id = 0;

    public $site_config = [];

    public $author_setting = [];

    public $demand_setting = [];

    public $order_status = -3;

    public $first_pay_info = [];

    public $error_msg = '';

    public $state_code = 1;

    public $creator = '';

    public $creator_id = '';

    public $create_time = '';

    public $update_time = '';

    public function toInsertData()
    {
        $this->creator_id = Container::getSession()->get('user_id');
        $data = $this->toArray();
        $data['site_config'] = $data['site_config'] ? json_encode($this->site_config) : '{}';
        $data['demand_setting'] = $data['demand_setting'] ? json_encode($this->demand_setting) : '{}';
        $data['first_pay_info'] = $data['first_pay_info'] ? json_encode($this->first_pay_info) : '{}';
        $data['author_setting'] = $data['author_setting'] ? json_encode($this->author_setting) : '[]';
        unset($data['id']);
        unset($data['create_time']);
        unset($data['update_time']);
        return $data;
    }

    public function toEditData()
    {
        $data = $this->toArray();
        $data['site_config'] = $data['site_config'] ? json_encode($this->site_config) : '{}';
        $data['demand_setting'] = $data['demand_setting'] ? json_encode($this->demand_setting) : '{}';
        $data['first_pay_info'] = $data['first_pay_info'] ? json_encode($this->first_pay_info) : '{}';
        $data['author_setting'] = $data['author_setting'] ? json_encode($this->author_setting) : '[]';
        unset($data['id']);
        unset($data['create_time']);
        unset($data['update_time']);
        return $data;
    }

    /**
     * 获取需求名称
     * @return string
     */
    public function getStarTaskName()
    {
        if($this->type == 2) {
            return date('md',strtotime($this->author_setting['expiration_live_time'])) .$this->author_setting['demand_name'] . date('_Y_md_H:i');
        } else {
            return $this->author_setting['industry_component']['anchor_name'] . '-' .
                $this->author_setting['aweme_name'] . '-' .
                date('Y-m-d H:i:s');
        }
    }
}