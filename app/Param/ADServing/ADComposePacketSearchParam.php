<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/3
 * Time: 16:53
 */

namespace App\Param\ADServing;

use App\Param\AbstractParam;

class ADComposePacketSearchParam extends AbstractParam
{
    public $page;
    public $rows;
    public $id;

    public $targeting_compose_type;
    public $setting_compose_type;
    public $creative_mode;
    public $word_list_logic = true;
    public $word_list;
    public $name;
    public $account_id;
    public $media_type;
    public $media_agent_type;
    public $company;
    public $already_task;
    public $platform;
    public $create_time;
    public $creator;
    public $creator_id;

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 10;
        }
        if ($this->create_time) {
            $this->create_time = array_map(function ($time) {
                return date('Y-m-d H:i:s', substr($time, 0, 10));
            }, $this->create_time);
        }
    }
}
