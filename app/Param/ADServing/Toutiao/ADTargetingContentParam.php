<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Toutiao;

use App\Constant\ToutiaoEnum;
use App\Model\HttpModel\Toutiao\AudiencePackage\AudiencePackageModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoUnionFlowPackageLogModel;
use App\Param\ADServing\AbstractADTargetingContentParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\Toutiao\AudiencePackageCreateParam;

class ADTargetingContentParam extends AbstractADTargetingContentParam
{
    public $search_keyword_status = 1;

    public $search_keyword_type = '';

    public $feed_search_keyword_list = [];

    public $search_bid_ratio = 1;

    public $audience_extend = 'ON';

    public $district = '';

    public $city_map = [];
    public $city = [];

    public $location_type = '';

    public $gender = '';

    public $age_none = '';
    public $age = [];

    public $interest_action_mode = '';

    public $ios_osv = '';

    public $android_osv = '';

    public $ac = [];
    public $ac_none = '';

    public $app_behavior_target = '';

    public $app_category_map = [];
    public $app_category = [];

    public $auto_extend_enabled = 0;

    public $auto_extend_targets = [];

    public $carrier = '';
    public $carrier_none = '';

    public $activate_type = [];
    public $activate_type_none = '';

    public $launch_price_none = '';
    public $launch_price = [0, 0];

    public $action_scene = [];

    public $action_days = '';

    public $action_categories_map = [];
    public $action_categories = [];

    public $action_words_map = [];
    public $action_words = [];

    public $interest_categories_map = [];
    public $interest_categories = [];

    public $interest_words_map = [];
    public $interest_words = [];

    public $zidingyirenqun = '';
    public $retargeting_tags_exclude_map = [];
    public $retargeting_tags_exclude = [];

    public $retargeting_tags_include_map = [];
    public $retargeting_tags_include = [];

    public $device_brand_map = [];
    public $device_brand = [];
    public $device_brand_none = '';

    public $device_type = '';

    public $superior_popularity_type = 'NONE';
    public $flow_package_map = [];
    public $flow_package = [];
    public $exclude_flow_package_map = [];
    public $exclude_flow_package = [];

    public $aweme_fan_type = 'NONE';
    public $aweme_fan_categories_map = [];
    public $aweme_fan_categories = [];
    public $aweme_fan_accounts_map = [];
    public $aweme_fan_accounts = [];
    public $aweme_fan_behaviors = [];
    public $aweme_fan_behaviors_days = '15';

    public function __construct($property = [])
    {
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === null) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg = [];
        if ($this->interest_action_mode === AudiencePackageModel::INTEREST_ACTION_MODE_CUSTOM) {
            if (empty($this->action_scene)){
                $error_msg[] = [
                    'key' => 'action_scene',
                    'value' => $this->action_scene,
                    'msg' => '行为场景不能为空'
                ];
            }
        }
        return $error_msg;
    }

    /**
     * 参数格式化
     * @param int $state
     */
    public function format($state)
    {
        if ($this->age_none == ToutiaoEnum::AGE_NONE) {
            $this->age = [];
        }else{
            $this->age = $this->audienceAge();
        }

        if ($this->district && $this->district != 'NONE' && !$this->location_type) {
            $this->location_type = 'ALL';
        }

        if (empty($this->district) || $this->district == AudiencePackageModel::DISTRICT_NONE) {
            $this->city = [];
            $this->city_map = [];
            $this->district = AudiencePackageModel::DISTRICT_NONE;
        }

        if ($this->district && $this->district !== AudiencePackageModel::DISTRICT_NONE) {
            if (!$this->city_map && $state == ADTargetingPacketParam::FINISH_STATE) {
                $this->district = AudiencePackageModel::DISTRICT_NONE;
                $this->location_type = 'ALL';
            }
        }

        //智能放量
        if (!$this->auto_extend_enabled) {
            $this->auto_extend_targets = [];
        }

        // 排序可扩展定向-用于md5
        if (is_array($this->auto_extend_targets) && $this->auto_extend_targets) {
            sort($this->auto_extend_targets);
        } else {
            $this->auto_extend_targets = [];
        }

        // 排序城市-用于md5
        if (is_array($this->city_map) && $this->city_map) {
            $this->city = array_column($this->city_map, 'id');
            sort($this->city);
        } else {
            $this->city && sort($this->city);
        }

        if ($this->ios_osv == '0.0' || !is_numeric($this->ios_osv)) {
            $this->ios_osv = '';
        }

        if ($this->android_osv == '0.0' || !is_numeric($this->android_osv)) {
            $this->android_osv = '';
        }

        if (is_array($this->ac) && $this->ac) {
            sort($this->ac);
        } else {
            $this->ac = [];
            $this->ac_none = 'buxian';
        }

        if (is_array($this->app_category_map) && $this->app_category_map) {
            $this->app_category = array_column($this->app_category_map, 'id');
            sort($this->app_category);
        } else {
            $this->app_category && sort($this->app_category);
        }

        if (is_array($this->carrier) && $this->carrier) {
            sort($this->carrier);
        } else {
            $this->carrier = '';
            $this->carrier_none = 'buxian';
        }

        if (is_array($this->activate_type) && $this->activate_type) {
            sort($this->activate_type);
        } else {
            $this->activate_type = [];
            $this->activate_type_none = 'buxian';
        }

        if (is_array($this->launch_price) && $this->launch_price) {
            sort($this->launch_price);
        } else {
            $this->launch_price = [0, 0];
        }

        if ($this->aweme_fan_type != 'CUSTOM') {
            $this->aweme_fan_categories_map = [];
            $this->aweme_fan_categories = [];
            $this->aweme_fan_accounts_map = [];
            $this->aweme_fan_accounts = [];
            $this->aweme_fan_behaviors = [];
            $this->aweme_fan_behaviors_days = '';
        } else {
            if (is_array($this->aweme_fan_categories_map) && $this->aweme_fan_categories_map) {
                $this->aweme_fan_categories = array_column($this->aweme_fan_categories_map, 'id');
                sort($this->aweme_fan_categories);
            } else {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    $this->aweme_fan_categories && sort($this->aweme_fan_categories);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->aweme_fan_categories = [];
                }
            }

            if (is_array($this->aweme_fan_accounts_map) && $this->aweme_fan_accounts_map) {
                $this->aweme_fan_accounts = array_column($this->aweme_fan_accounts_map, 'id');
                sort($this->aweme_fan_accounts);
            } else {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    $this->aweme_fan_accounts && sort($this->aweme_fan_accounts);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->aweme_fan_accounts = [];
                }
            }
        }

        if ($this->interest_action_mode != AudiencePackageModel::INTEREST_ACTION_MODE_CUSTOM) {
            $this->action_scene = [];
            $this->action_days = '';
            $this->action_categories_map = [];
            $this->action_categories = [];
            $this->action_words_map = [];
            $this->action_words = [];
            $this->interest_categories_map = [];
            $this->interest_categories = [];
            $this->interest_words_map = [];
            $this->interest_words = [];
        } else {
            if (is_array($this->action_scene) && $this->action_scene) {
                $this->action_scene = array_filter($this->action_scene, function ($value) {
                    return $value != 'SEARCH';
                });
                sort($this->action_scene);
            } else {
                $this->action_scene = [];
            }

            if (is_array($this->action_categories_map) && $this->action_categories_map) {
                $this->action_categories = array_column($this->action_categories_map, 'id');
                sort($this->action_categories);
            } else {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    $this->action_categories && sort($this->action_categories);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->action_categories = [];
                }
            }

            if (is_array($this->action_words_map) && $this->action_words_map) {
                $this->action_words = array_column($this->action_words_map, 'id');
                sort($this->action_words);
            } else {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    $this->action_words && sort($this->action_words);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->action_words = [];
                }
            }

            if (is_array($this->interest_categories_map) && $this->interest_categories_map) {
                $this->interest_categories = array_column($this->interest_categories_map, 'id');
                sort($this->interest_categories);
            } else {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    $this->interest_categories && sort($this->interest_categories);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->interest_categories = [];
                }
            }

            if (is_array($this->interest_words_map) && $this->interest_words_map) {
                $this->interest_words = array_column($this->interest_words_map, 'id');
                sort($this->interest_words);
            } else {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    $this->interest_words && sort($this->interest_words);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->interest_words = [];
                }
            }
        }

        if ($this->retargeting_tags_include_map) {
            $this->retargeting_tags_include_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->retargeting_tags_include_map);
            $this->retargeting_tags_include = array_column(
                $this->retargeting_tags_include_map,
                'custom_audience_id'
            );
            sort($this->retargeting_tags_include);
        } else {
            if ($this->retargeting_tags_include) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->retargeting_tags_include);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->retargeting_tags_include = [];
                }
            } else {
                $this->retargeting_tags_include = [];
                $this->retargeting_tags_include_map = [];
            }
        }

        if ($this->retargeting_tags_exclude_map) {
            $this->retargeting_tags_exclude_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->retargeting_tags_exclude_map);
            $this->retargeting_tags_exclude = array_column(
                $this->retargeting_tags_exclude_map,
                'custom_audience_id'
            );
            sort($this->retargeting_tags_exclude);
        } else {
            if ($this->retargeting_tags_exclude) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->retargeting_tags_exclude);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->retargeting_tags_exclude = [];
                }
            } else {
                $this->retargeting_tags_exclude = [];
                $this->retargeting_tags_exclude_map = [];
            }
        }

        if (is_array($this->device_brand_map) && $this->device_brand_map) {
            $this->device_brand = array_column($this->device_brand_map, 'id');
            sort($this->device_brand);
        } else {
            $this->device_brand && sort($this->device_brand);
        }

        if ($this->flow_package_map) {
            $this->flow_package_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->flow_package_map);
            $this->flow_package = array_column(
                $this->flow_package_map,
                'flow_package_id'
            );
            sort($this->flow_package);
        } else {
            if ($this->flow_package) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->flow_package);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->flow_package = [];
                }
            } else {
                $this->flow_package = [];
                $this->flow_package_map = [];
            }
        }

        if ($this->exclude_flow_package_map) {
            $this->exclude_flow_package_map = array_map(function ($value) {
                if (is_string($value)) {
                    return json_decode($value, true);
                } else {
                    return $value;
                }
            }, $this->exclude_flow_package_map);
            $this->exclude_flow_package = array_column(
                $this->exclude_flow_package_map,
                'flow_package_id'
            );
            sort($this->exclude_flow_package);
        } else {
            if ($this->exclude_flow_package) {
                if ($state == ADTargetingPacketParam::UN_FINISH_STATE) {
                    sort($this->exclude_flow_package);
                } elseif ($state == ADTargetingPacketParam::FINISH_STATE) {
                    $this->exclude_flow_package = [];
                }
            } else {
                $this->exclude_flow_package = [];
                $this->exclude_flow_package_map = [];
            }
        }

        if (!$this->device_brand) {
            $this->device_brand_none = 'buxian';
        }

        if ($this->zidingyirenqun == 'buxian') {
            $this->retargeting_tags_include = [];
            $this->retargeting_tags_include_map = [];
            $this->retargeting_tags_exclude = [];
            $this->retargeting_tags_exclude_map = [];
        }

        if (!$this->app_behavior_target) {
            $this->app_behavior_target = 'NONE';
        }

        if ($this->launch_price[0] == 0 && $this->launch_price[1] == 0) {
            $this->launch_price_none = 'buxian';
        }

        if ($this->launch_price_none == 'buxian') {
            unset($this->launch_price);
//            $this->launch_price = [0, 0];
        }

        if ($this->carrier_none == 'buxian') {
            $this->carrier = [];
        }

        if ($this->activate_type_none == 'buxian') {
            $this->activate_type = [];
        }

        if ($this->app_category) {
            $this->app_category = array_map(function ($num) {
                return (int)$num;
            }, $this->app_category);
        }

        if ($this->action_categories) {
            $this->action_categories = array_map(function ($num) {
                return (int)$num;
            }, $this->action_categories);
        }

        if ($this->action_words) {
            $this->action_words = array_map(function ($num) {
                return (int)$num;
            }, $this->action_words);
        }

        if ($this->interest_categories) {
            $this->interest_categories = array_map(function ($num) {
                return (int)$num;
            }, $this->interest_categories);
        }

        if ($this->interest_words) {
            $this->interest_words = array_map(function ($num) {
                return (int)$num;
            }, $this->interest_words);
        }

        if ($this->retargeting_tags_include) {
            $this->retargeting_tags_include = array_map(function ($num) {
                return (int)$num;
            }, $this->retargeting_tags_include);
        }

        if ($this->retargeting_tags_exclude) {
            $this->retargeting_tags_exclude = array_map(function ($num) {
                return (int)$num;
            }, $this->retargeting_tags_exclude);
        }
    }

    /**
     * 生成此定向包的md5特殊编码
     * @return string
     */
    public function toMd5()
    {
        $md5_array = $this->toArray();
        unset($md5_array['zidingyirenqun']);
        unset($md5_array['age_none']);
        unset($md5_array['ac_none']);
        unset($md5_array['carrier_none']);
        unset($md5_array['launch_price_none']);
        unset($md5_array['activate_type_none']);
        unset($md5_array['device_brand_none']);
        unset($md5_array['city_map']);
        unset($md5_array['app_category_map']);
        unset($md5_array['device_brand_map']);
        unset($md5_array['md5']);
        unset($md5_array['action_categories_map']);
        unset($md5_array['action_words_map']);
        unset($md5_array['interest_categories_map']);
        unset($md5_array['interest_words_map']);
        unset($md5_array['retargeting_tags_exclude_map']);
        unset($md5_array['retargeting_tags_include_map']);
        ksort($md5_array);
        return md5(json_encode($md5_array, JSON_NUMERIC_CHECK));
    }

    /**
     * 获取需要推送的人群包id
     * @return array
     */
    public function getAllAudienceIdList()
    {
        return array_merge(
            $this->retargeting_tags_include ?: [],
            $this->retargeting_tags_exclude ?: []
        );
    }

    /**
     * 生成定向给mq的数据
     * @return AbstractADTargetingContentParam
     */
    public function toMQData()
    {
        $this->app_category_map = [];
        $this->device_brand_map = [];
        $this->action_categories_map = [];
        $this->action_words_map = [];
        $this->interest_categories_map = [];
        $this->interest_words_map = [];

        if (!$this->retargeting_tags_exclude_map && $this->retargeting_tags_exclude) {
            $this->retargeting_tags_exclude = [];
        }
        $this->retargeting_tags_exclude_map = [];

        if (!$this->retargeting_tags_include_map && $this->retargeting_tags_include) {
            $this->retargeting_tags_include = [];
        }
        $this->retargeting_tags_include_map = [];

        return $this;
    }

    /**
     * 获取所有流量包id与md5的map
     * @return array
     */
    public function getAllFlowIdMd5MapList()
    {
        $data = [];
        foreach ($this->flow_package_map as $key => $value) {
            $data[$value['rit_md5']]['flow_package_id'] = $value['flow_package_id'];
        }
        foreach ($this->exclude_flow_package_map as $key => $value) {
            $data[$value['rit_md5']]['flow_package_id'] = $value['flow_package_id'];
        }
        $md5_list = array_keys($data);
        if ($md5_list) {
            $all_data = (new OdsToutiaoUnionFlowPackageLogModel())->getListByMd5($md5_list);
            if ($all_data) {
                $all_data = $all_data->keyBy('rit_md5');
                foreach ($all_data as $md5 => $value) {
                    $data[$md5]['rit'] = json_decode($value->rit);
                    $data[$md5]['name'] = $value->name;
                }
            }
        }
        return $data;
    }

    /**
     * 重置流量包
     * @param array $flow_package_map
     */
    public function resetFlowPackage(array $flow_package_map)
    {
        $this->flow_package_map = array_column($this->flow_package_map, null, 'rit_md5');
        $this->exclude_flow_package_map = array_column($this->exclude_flow_package_map, null, 'rit_md5');
        foreach ($flow_package_map as $key => $value) {
            if (isset($this->flow_package_map[$value['md5']])) {
                $this->flow_package_map[$value['md5']] = $value;
            }
            if (isset($this->exclude_flow_package_map[$value['md5']])) {
                $this->exclude_flow_package_map[$value['md5']] = $value;
            }
        }
        $this->flow_package_map = array_values($this->flow_package_map);
        $this->exclude_flow_package_map = array_values($this->exclude_flow_package_map);

        if ($this->flow_package_map) {
            $this->flow_package = array_column(
                $this->flow_package_map,
                'flow_package_id'
            );
            sort($this->flow_package);
        } else {
            $this->flow_package = [];
        }

        if ($this->exclude_flow_package_map) {
            $this->exclude_flow_package = array_column(
                $this->exclude_flow_package_map,
                'flow_package_id'
            );
            sort($this->exclude_flow_package);
        } else {
            $this->exclude_flow_package = [];
        }
    }

    /**
     * 获取媒体接口格式的定向包内容数组
     * @return array
     */
    public function getMediaFormatTargetingArray()
    {
        $media_targeting_param = new AudiencePackageCreateParam($this->toArray());

        return $media_targeting_param->toAudiencePackageInfoBody();
    }


    /**
     * 兼容头条新版年龄拆分问题 人群包数据-年龄-集中处理
     * @return array
     */
    public function audienceAge()
    {
        // 是自定义年龄
        if ($this->age_none === ToutiaoEnum::AGE_CUSTOM) {
            // 是否包含相同值
            if (array_intersect(['AGE_BETWEEN_18_23', 'AGE_BETWEEN_31_40', 'AGE_BETWEEN_41_49', 'AGE_ABOVE_50'], $this->age)) {
                if (in_array('AGE_BETWEEN_18_23', $this->age)) {
                    $this->age = array_merge(['AGE_BETWEEN_18_19', 'AGE_BETWEEN_20_23'], $this->age);
                    unset($this->age[array_search('AGE_BETWEEN_18_23', $this->age)]);
                }
                if (in_array('AGE_BETWEEN_31_40', $this->age)) {
                    $this->age = array_merge(['AGE_BETWEEN_31_35', 'AGE_BETWEEN_36_40'], $this->age);
                    unset($this->age[array_search('AGE_BETWEEN_31_40', $this->age)]);
                }
                if (in_array('AGE_BETWEEN_41_49', $this->age)) {
                    $this->age = array_merge(['AGE_BETWEEN_41_45', 'AGE_BETWEEN_46_50'], $this->age);
                    unset($this->age[array_search('AGE_BETWEEN_41_49', $this->age)]);
                }
                if (in_array('AGE_ABOVE_50', $this->age)) {
                    $this->age = array_merge(['AGE_BETWEEN_51_55', 'AGE_BETWEEN_56_59', 'AGE_ABOVE_60'], $this->age);
                    unset($this->age[array_search('AGE_ABOVE_50', $this->age)]);
                }
            }
            return array_unique(array_values($this->age));
        }
        return $this->age;
    }
}
