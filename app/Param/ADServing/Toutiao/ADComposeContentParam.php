<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Toutiao;

use App\Constant\BatchAD;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\PlatId;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsToutiaoCloudGamePlayableLog;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\ADServing\AbstractADComposeContentParam;
use App\Param\Toutiao\CreativeCreateParam;
use App\Utils\Helpers;
use Common\EnvConfig;

class ADComposeContentParam extends AbstractADComposeContentParam
{
    /**
     * @var ADOtherSettingContentParam
     */
    public $other_setting = [];

    public function paramHook()
    {

    }

    /**
     * 组合参数联合检验
     *
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $this->other_setting;

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            if ($setting->isUnionSplash()) {
                foreach ($this->material_list->all_image_list as $image_info) {
                    if ((int)$image_info['width'] !== 1080 || (int)$image_info['height'] !== 1920) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => $image_info['id'],
                            'msg' => "穿山甲开屏图片素材规格需要符合1080*1920规格：{$image_info['url']}"
                        ];
                        break;
                    }
                }
            }

            if ($this->site_config->deep_external_action != '') {
                if ($setting->deep_bid_type == 'DEEP_BID_DEFAULT') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '广告上报配置为深度转化，参数包中不可以选择无深度优化'
                    ];
                }
                if ($setting->flow_control_mode == 'FLOW_CONTROL_MODE_SMOOTH') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'flow_control_mode',
                        'value' => $setting->flow_control_mode,
                        'msg' => '广告上报配置为深度转化，参数包中不可以选择优先低成本的广告投放速度'
                    ];
                }
                if ((int)$this->site_config->convert_type == 4 &&
                    $this->site_config->deep_external_action == 'AD_CONVERT_TYPE_PURCHASE_ROI' &&
                    (
                        $setting->deep_bid_type != 'ROI_COEFFICIENT' &&
                        $setting->deep_bid_type != 'ROI_PACING' &&
                        $setting->deep_bid_type != 'FIRST_AND_SEVEN_PAY_ROI' &&
                        $setting->deep_bid_type != 'PER_AND_SEVEN_PAY_ROI'
                    )
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '广告上报配置为付费的转化类型，深度转化目标为付费ROI,参数包中只能选择ROI系数深度优化、ROI自动优化、首日+7日付费ROI、每次+7日付费ROI的方式'
                    ];
                }

                if ((int)$this->site_config->convert_type == 4 &&
                    $this->site_config->deep_external_action == 'AD_CONVERT_TYPE_PURCHASE_ROI_7D' &&
                    (
                        $setting->deep_bid_type != 'FIRST_AND_SEVEN_PAY_ROI' &&
                        $setting->deep_bid_type != 'PER_AND_SEVEN_PAY_ROI'
                    )
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '广告上报配置为付费的转化类型，深度转化目标为七日付费ROI,参数包中只能选择首日+7日付费ROI、每次+7日付费ROI的方式'
                    ];
                }
            }

            if ($this->site_config->convert_type == 4 && $this->site_config->convert_data_type == 'EVERY_ONE' && $setting->deep_bid_type != 'BID_PER_ACTION') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => '上报配置的统计方式选择每一次时，深度优化方式(deep_bid_type)必须选择 每次付费出价'
                ];
            }
        }

//        if (
//            $this->site_config->plat_id == PlatId::MINI &&
//            $other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_LINK &&
//            $this->site_config->deep_external_action
//        ) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                'key' => 'promotion_type',
//                'value' => $other_setting->promotion_type,
//                'msg' => '投小游戏的情况下，使用链接的投放内容，不能使用深度转化',
//            ];
//        }

        foreach ($this->material_list->all_video_list as $video) {
            if (!$this->material_list->audio_list) {
                foreach ($video['cover_list'] as $cover) {
                    if ($video['width'] != $cover['width'] || $video['height'] != $cover['height']) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$video['url']}封面与视频的尺寸不一致"
                        ];
                    }
                }
            }
        }

        foreach ($this->word_list->getWordContent() as $key => $word) {
            if (strpos($word, '打金神器') !== false) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'value' => $word,
                    'msg' => "{$word}，此文案不允许使用打金神器字眼"
                ];
            }
            $len = Helpers::ADServingStrLen($word);
            if ($len < 5 || $len > 100) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'value' => $word,
                    'msg' => "{$word}，此文案不符合大于5小于50的规范"
                ];
            }
            if (strpos($word, '{') !== false || strpos($word, '}') !== false) {
                $result = [];
                preg_match_all("/(?<={)[^}]+/", $word, $result);
                if (count($result) > 2) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'value' => $word,
                        'msg' => "文案只能带有2个动态词包,文案:{$word},超出2个动态词包"
                    ];
                }
                foreach ($result[0] as $word_packet_name) {
                    $word_packet_name = trim($word_packet_name, '{}');
                    if (!isset(CreativeCreateParam::WORD_MAP[$word_packet_name])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                            'value' => $word,
                            'msg' => "暂时不支持{{$word_packet_name}}相关的动态词包!"
                        ];
                    }
                }
            }
        }

        if (in_array($this->media_agent_type, [0, 1]) && in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_APP, ToutiaoEnum::PROMOTION_TYPE_HOT])) {
            if ($other_setting->app_name || $other_setting->source) {
                $game_data = (new V2DimGameIdModel())->getDataByGameId($this->platform, $this->site_config->game_id);
                if ($game_data) {
                    if ($other_setting->download_type == 'DOWNLOAD_URL') {
                        if ($other_setting->app_name && $other_setting->app_name != $game_data->app_name) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                'key' => 'app_name',
                                'value' => $other_setting->app_name,
                                'msg' => "app_name与游戏配置本身({$game_data->app_name})不一致"
                            ];
                        }
                    }
                    if ($other_setting->download_type == 'EXTERNAL_URL') {
                        if ($other_setting->source && $other_setting->source != $game_data->app_name) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                'key' => 'source',
                                'value' => $other_setting->source,
                                'msg' => "source与游戏配置本身({$game_data->app_name})不一致"
                            ];
                        }
                    }
                }
            }
        }

        if ($other_setting->is_playable == 2) {
            if ($other_setting->download_type != 'DOWNLOAD_URL') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'download_type',
                    'value' => $other_setting->download_type,
                    'msg' => "云游戏试玩仅支持下载链接的下载方式"
                ];
            }
            if (!$other_setting->playable_id) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'playable_id',
                    'value' => $other_setting->playable_id,
                    'msg' => '云游戏试玩素材不能为空'
                ];
            } else {
                $play_account_list = array_column($this->account_list->account_list, 'account_id');
                $list = (new OdsToutiaoCloudGamePlayableLog())->getDataByIDANDAccountList($other_setting->playable_id, $play_account_list);
                $have_play_account_list = $list->pluck('account_id')->toArray();
                foreach ($play_account_list as $key => $account_id) {
                    if (!in_array($account_id, $have_play_account_list)) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'playable_id',
                            'value' => $other_setting->playable_id,
                            'msg' => "该试玩素材不可被账号({$account_id})使用"
                        ];
                        break;
                    }
                }
            }
        }

        if (in_array($this->media_agent_type, [0, 1])) {
            if ($this->other_setting->promotion_type != 'LINK') {
                if ($this->other_setting->app_name || $this->other_setting->source) {
                    if ($this->other_setting->app_name && strpos($this->other_setting->app_name, '打金神器') !== false) {
                        $error_msg_list[] = [
                            'key' => 'app_name',
                            'value' => $this->other_setting->app_name,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'msg' => "不允许出现打金神器字眼"
                        ];
                    }
                    if ($this->other_setting->source && strpos($this->other_setting->source, '打金神器') !== false) {
                        $error_msg_list[] = [
                            'key' => 'source',
                            'value' => $this->other_setting->source,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'msg' => "不允许出现打金神器字眼"
                        ];
                    }
                }
            }

            if ($this->other_setting->download_type == 'DOWNLOAD_URL') {
                if (!$this->other_setting->app_name) {
                    $error_msg_list[] = [
                        'key' => 'app_name',
                        'value' => $this->other_setting->app_name,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'msg' => '下载类型为安卓时，应用名字(app_name)不能为空'
                    ];
                }
            }

            if ($this->other_setting->app_name && strpos($this->other_setting->app_name, '333') !== false) {
                $error_msg_list[] = [
                    'key' => 'app_name',
                    'value' => $this->other_setting->app_name,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'msg' => '包名(app_name)包含333，非法命名，请修改参数包!'
                ];
            }

            if ($this->other_setting->source && strpos($this->other_setting->source, '333') !== false) {
                $error_msg_list[] = [
                    'key' => 'source',
                    'value' => $this->other_setting->source,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'msg' => '应用名称(source)包含333，非法命名，请修改参数包!'
                ];
            }

            if ($this->other_setting->download_type == 'EXTERNAL_URL') {
                if (!$this->other_setting->source) {
                    $error_msg_list[] = [
                        'key' => 'source',
                        'value' => $this->other_setting->source,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'msg' => '应用下载方式为落地页时，应用名称(source)不能为空'
                    ];
                }
            }


            if ($this->other_setting->promotion_type != 'LINK') {
                $app_name_length = $this->other_setting->download_type == 'DOWNLOAD_URL' ? mb_strlen($this->other_setting->app_name) : mb_strlen($this->other_setting->source);
                if ($app_name_length < 2 || $app_name_length > 20) {
                    $error_msg_list[] = [
                        'key' => 'source',
                        'value' => $this->other_setting->source,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'msg' => "应用名称2-20个字，请正确输入"
                    ];
                    $error_msg_list[] = [
                        'key' => 'app_name',
                        'value' => $this->other_setting->app_name,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'msg' => "应用名称2-20个字，请正确输入"
                    ];
                }
            }
        }

        return $error_msg_list;
    }


    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD1Name() method.
    }

    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
        // TODO: Implement judgeAD2Name() method.
    }

    public function getMaterialFileNormalList(): array
    {
        $normal_list = [
            [
                "_",
                'or',
                [
                    ['file_type' => MaterialFileModel::FILE_TYPE_VIDEO, 'width' => 2560, 'height' => 1440],
                    ['file_type' => MaterialFileModel::FILE_TYPE_VIDEO, 'width' => 1440, 'height' => 2560,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_VIDEO, 'width' => 720, 'height' => 1280,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_VIDEO, 'width' => 1280, 'height' => 720,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_VIDEO, 'width' => 1080, 'height' => 1920,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_VIDEO, 'width' => 1920, 'height' => 1080,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_IMAGE, 'width' => 1080, 'height' => 1920,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_IMAGE, 'width' => 640, 'height' => 2384,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_IMAGE, 'width' => 720, 'height' => 1280,],
                    ['file_type' => MaterialFileModel::FILE_TYPE_IMAGE, 'width' => 1280, 'height' => 720,],
                ]
            ],
        ];
        return $normal_list;
    }
}
