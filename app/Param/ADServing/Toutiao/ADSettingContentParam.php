<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Toutiao;

use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\HttpModel\Toutiao\Creative\CreativeModel;
use App\Param\ADServing\AbstractADSettingContentParam;

class ADSettingContentParam extends AbstractADSettingContentParam
{
    public $delivery_range = '';
    public $union_video_type = '';
    public $smart_inventory = 0;
    public $inventory_mode = '';
    public $inventory_type = [];
    public $schedule_type = '';
    public $smart_bid_type = '';
    public $start_time = '';
    public $end_time = '';
    public $budget = '';
    public $deep_bid_type = '';
    public $roi_goal = [];
    public $deep_cpabid = '';
    public $is_comment_disable = '';
    public $creative_auto_generate_switch = '';
    public $third_industry_id = '';
    public $ad_keywords = [];
    public $is_follow_material = 0;
    public $schedule_time = 0;
    public $web_url = '';
    public $cpa_bid = [];
    public $cpa_bid_mode = ToutiaoEnum::CPA_BID_MODE_NORMAL;
    public $roi_goal_mode = ToutiaoEnum::ROI_GOAL_MODE_NORMAL;


    /**
     * @var bool 是否严格validate
     */
    public $is_strict = true;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        unset($property['is_strict']);
        $this->is_strict = $is_strict;
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === NULL) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg = [];
        if ($this->roi_goal) {
            foreach ($this->roi_goal as $roi) {
                if (!is_numeric($roi)) {
                    $error_msg[] = [
                        'key' => 'roi_goal',
                        'value' => $this->roi_goal,
                        'msg' => "roi系数中有非数字的值"
                    ];
                    break;
                }
            }
        }

        if ($this->cpa_bid) {
            if ((int)$this->cpa_bid_mode === ToutiaoEnum::CPA_BID_MODE_SCOPE) {
                if ($this->cpa_bid[0] >= $this->cpa_bid[1]) {
                    $error_msg[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid_mode,
                        'msg' => '转化出价范围设置有误'
                    ];
                }
            }
        }

        if ($this->deep_cpabid) {
            if (!is_numeric($this->deep_cpabid)) {
                $error_msg[] = [
                    'key' => 'deep_cpabid',
                    'value' => $this->deep_cpabid,
                    'msg' => "深度优化出价中有非数字的值"
                ];
            }
        }

        if ($this->schedule_type == 'SCHEDULE_START_END') {
            if (!$this->start_time && !$this->end_time) {
                $error_msg[] = [
                    'key' => 'schedule_type',
                    'value' => $this->schedule_type,
                    'msg' => "结束时间(end_time)与开始时间(start_time)不能为空"
                ];
            }
        }

        return $error_msg;
    }


    /**
     * 参数格式化
     */
    public function format()
    {
        /*if (!$this->third_industry_id || in_array($this->third_industry_id, [19130301, 19130303])) {
            $this->third_industry_id = 19130304;
        }*/
        if ($this->schedule_type == 'SCHEDULE_START_END') {

            if ($this->start_time) {
                $this->start_time = mb_strlen(strval($this->start_time)) > 10 ? $this->start_time / 1000 : $this->start_time;
                if (($this->start_time) < time()) {
                    $this->start_time = time();
                }
            }

            if ($this->end_time) {
                $this->end_time = mb_strlen(strval($this->end_time)) > 10 ? $this->end_time / 1000 : $this->end_time;
            }

            if ($this->end_time && $this->end_time < $this->start_time) {
                $this->end_time = strtotime('+1 year');
            }
        }

        if ($this->delivery_range == AdModel::DELIVERY_RANGE_UNION) {
            $this->inventory_type = ['INVENTORY_UNION_SLOT'];
        }

        if ($this->start_time) {
            $this->start_time = mb_strlen(strval($this->start_time)) > 10 ? $this->start_time / 1000 : $this->start_time;
        } else {
            $this->start_time = '';
        }

        if ($this->end_time) {
            $this->end_time = mb_strlen(strval($this->end_time)) > 10 ? $this->end_time / 1000 : $this->end_time;
        } else {
            $this->end_time = '';
        }
    }


    /**
     * 生成目标转化出价
     * @return mixed
     */
    public function getCpaBid()
    {
        if (!$this->cpa_bid || !is_array($this->cpa_bid)) {
            return '';
        }
        if ((int)$this->cpa_bid_mode === ToutiaoEnum::CPA_BID_MODE_SCOPE) {
            return sprintf("%.2f",$this->cpa_bid[0] + mt_rand()/mt_getrandmax() * ($this->cpa_bid[1]-$this->cpa_bid[0]));
        } else {
            return $this->cpa_bid[array_rand($this->cpa_bid, 1)];
        }
    }


    /**
     * 生成Roi效果值
     * @return mixed
     */
    public function getRoiGoal()
    {
        if (!$this->roi_goal || !is_array($this->roi_goal)) {
            return '';
        }
        if ((int)$this->roi_goal_mode === ToutiaoEnum::ROI_GOAL_MODE_SCOPE) {
            return sprintf("%.4f",$this->roi_goal[0] + mt_rand()/mt_getrandmax() * ($this->roi_goal[1]-$this->roi_goal[0]));
        } else {
            return $this->roi_goal[array_rand($this->roi_goal, 1)];
        }
    }

    /**
     * 穿山甲开屏场景
     * @return bool
     */
    public function isUnionSplash()
    {
        return in_array(CreativeModel::INVENTORY_UNION_SLOT, $this->inventory_type) && $this->union_video_type == 'SPLASH_VIDEO';
    }

}
