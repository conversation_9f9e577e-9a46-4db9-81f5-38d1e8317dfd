<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Toutiao\Project;

use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Param\ADServing\Toutiao\ADSettingContentParam as ToutiaoADSettingContentParam;

class ADSettingContentParam extends ToutiaoADSettingContentParam
{
    public $ad_target;
    public $bid;
    public $auto_stop = 0;
    public $opt_status = '';
    public $hide_if_converted = 'NO_EXCLUDE';
    public $converted_time_duration = 'THREE_MONTH';
    /**
     * @var bool 是否严格validate
     */
    public $is_strict = true;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        unset($property['is_strict']);
        $this->is_strict = $is_strict;
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === NULL) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = parent::validate();

        if ($this->deep_bid_type == 'ROI_COEFFICIENT') {
            if (!$this->roi_goal || !is_array($this->roi_goal)) {
                $error_msg[] = [
                    'key' => 'roi_goal',
                    'value' => $this->roi_goal,
                    'msg' => 'ROI系数(roi_goal)不能为空'
                ];
            } else if (is_array($this->roi_goal)) {
                foreach ($this->roi_goal as $value) {
                    if ($value < 0) {
                        $error_msg[] = [
                            'key' => 'roi_goal',
                            'value' => $this->roi_goal,
                            'msg' => 'ROI系数(roi_goal)不能小于0'
                        ];
                    }
                    if ($value > 5) {
                        $error_msg[] = [
                            'key' => 'roi_goal',
                            'value' => $this->roi_goal,
                            'msg' => 'ROI系数(roi_goal)不能大于5'
                        ];
                    }
                }
                if ((int)$this->roi_goal_mode === ToutiaoEnum::ROI_GOAL_MODE_SCOPE) {
                    if ($this->roi_goal[0] >= $this->roi_goal[1]) {
                        $error_msg[] = [
                            'key' => 'roi_goal',
                            'value' => $this->roi_goal_mode,
                            'msg' => 'ROI系数范围设置有误'
                        ];
                    }
                }
            }
        }

        if ($this->bid) {
            foreach ($this->bid as $bid_data) {
                if (!is_numeric($bid_data)) {
                    $error_msg[] = [
                        'key' => 'bid',
                        'value' => $this->bid,
                        'msg' => "目标转化出价中有非数字的值"
                    ];
                    break;
                }
            }
        }

        if ($this->delivery_range == AdModel::DELIVERY_RANGE_UNION && !$this->union_video_type) {
            $error_msg_list[] = [
                'key' => 'union_video_type',
                'value' => $this->union_video_type,
                'msg' => "投放形式(union_video_type)不能为空"
            ];
        }

        if ($this->delivery_range == AdModel::DELIVERY_RANGE_UNION && !empty($this->converted_time_duration)) {
            $error_msg_list[] = [
                'key' => 'converted_time_duration',
                'value' => $this->converted_time_duration,
                'msg' => "投放范围是穿山甲不支持转化时间过滤"
            ];
        }

        //不再支持投放场景
        if ($this->inventory_mode == ToutiaoEnum::INVENTORY_MODE_SCENE) {
            $error_msg_list[] = [
                'key' => 'inventory_mode',
                'value' => $this->inventory_mode,
                'msg' => "请重新选择广告位类型,不支持场景指定位置"
            ];
        }


        if ((int)$this->budget < 300) {
            $error_msg_list[] = [
                'key' => 'budget',
                'value' => $this->budget,
                'msg' => '广告预算(budget)不能低于300'
            ];
        }

        if ($this->ad_keywords && is_array($this->ad_keywords)) {
            if (count($this->ad_keywords) > 20) {
                $error_msg_list[] = [
                    'key' => 'ad_keywords',
                    'value' => $this->ad_keywords,
                    'msg' => '标签包词数量(ad_keywords)不能大于20个词'
                ];
            }
            foreach ($this->ad_keywords as $word) {
                if (mb_strlen($word) > 10) {
                    throw new AppException("标签包词(ad_keywords):{$word}已经大于10个字");
                }
                if (strpos($word, '333') !== false) {
                    throw new AppException("标签包词:{$word}包含333字眼，请修改标签包");
                }
            }
        }

        return $error_msg_list;
    }

    public function paramHook()
    {
        $this->format();
    }

    public function format()
    {
        parent::format();
        $this->cpa_bid = $this->bid; // 统一使用cpa_bid来存出价集合
    }

    /**
     * @return string
     */
    public function getLDYContent()
    {
        throw new AppException('头条无落地页adid');
    }
}
