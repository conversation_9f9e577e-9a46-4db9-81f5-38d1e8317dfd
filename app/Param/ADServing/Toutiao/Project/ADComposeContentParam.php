<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Toutiao\Project;

use App\Constant\BatchAD;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\PlatId;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Param\ADServing\Toutiao\ADComposeContentParam as ToutiaoADComposeContentParam;
use App\Param\ADServing\Toutiao\ADTargetingContentParam;

class ADComposeContentParam extends ToutiaoADComposeContentParam
{

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = parent::unionParamValidateJudge();

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        // 投放官家3.0 不能走 转化只能走事件管理
        if ($this->site_config->convert_toolkit === ConvertToolkit::TOUTIAO_CONVERT) {
            throw new AppException('投放管家3.0,媒体不支持转化应用,只能使用事件管理');
        }
        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            /* @var ADSettingContentParam $setting */

            foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {

                /**@var ADTargetingContentParam $targeting */

                if ($setting->delivery_range == AdModel::DELIVERY_RANGE_UNION &&
                    !empty($targeting->activate_type)
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'delivery_range',
                        'value' => $setting->delivery_range,
                        'msg' => '投放范围是穿山甲不支持新用户首次激活时间(activate_type)'
                    ];
                }

                if ($other_setting->download_type == 'EXTERNAL_URL') {
                    if (floatval($targeting->android_osv) >= 7) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                            'source_id' => $targeting_id,
                            'key' => 'android_osv',
                            'value' => $targeting->android_osv,
                            'msg' => '参数包落地页下载类型暂时不支持定向安卓版本7.0以上'
                        ];
                    }
                }
            }

            if (
                $this->site_config->deep_external_action &&
                $this->site_config->deep_external_action == 'AD_CONVERT_TYPE_PURCHASE_ROI_7D' &&
                $setting->deep_bid_type != 'ROI_COEFFICIENT'
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => "深度优化方式需要是7日ROI系数"
                ];
            }

            if ($this->site_config->convert_source_type == ConvertSourceType::H5_API) {
                if ($other_setting->promotion_type != 'LINK') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'promotion_type',
                        'value' => $other_setting->promotion_type,
                        'msg' => "H5_API的转化方式只能是链接"
                    ];
                }

                if (!$other_setting->external_url) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'external_url',
                        'value' => $other_setting->external_url,
                        'msg' => "落地页不能为空"
                    ];
                }

                if ($this->site_config->game_pack) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'promotion_type',
                        'value' => $setting->promotion_type,
                        'msg' => "投放内容是链接时，广告配置只能是不打包的状态"
                    ];
                }
            }

            if ($setting->deep_bid_type == 'BID_PER_ACTION' && ($this->site_config->convert_data_type != 'EVERY_ONE' || $this->site_config->convert_type != 4)) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => '深度优化方式(deep_bid_type)选择每次付费出价，转化目标的统计方式必须选择每一次，且转化目标是激活且付费'
                ];
            }

            if ($this->site_config->convert_type == 4 && $this->site_config->convert_data_type == 'EVERY_ONE' && $setting->deep_bid_type != 'BID_PER_ACTION') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => '上报配置的统计方式选择每一次时，深度优化方式(deep_bid_type)必须选择 每次付费出价'
                ];
            }


//            if ($this->site_config->convert_type == 1 && $setting->delivery_range != 'UNIVERSAL') {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'delivery_range',
//                    'value' => $setting->delivery_range,
//                    'msg' => "转化类型为激活时，只支持通投智选的投放类型"
//                ];
//            }

            if (in_array($setting->ad_target, ['CPA', 'COST', 'PUSH'])) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'ad_target',
                    'value' => 0,
                    'msg' => '请重新选择投放方式'
                ];
            }

            foreach ($this->material_list->all_video_list as $video_key => $video_info) {
                if (count($video_info['cover_list']) > 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => 0,
                        'msg' => "{$video_info['url']}所选封面数量不能大于1，"
                    ];
                }
            }
        }

        if ((int)$this->site_config->plat_id === PlatId::MINI) {
            if ($other_setting->promotion_type !== ToutiaoEnum::LANDING_TYPE_LINK) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'promotion_type',
                    'value' => $other_setting->promotion_type,
                    'msg' => "微信小游戏投放内容只能选择链接"
                ];
            }
            if ($other_setting->download_type !== ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'download_type',
                    'value' => $other_setting->download_type,
                    'msg' => "微信小游戏只能选择落地页链接"
                ];
            }
        }

        if ($this->compose_config->word_num_in_ad < 3) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'word_num_in_ad',
                'value' => $this->compose_config->word_num_in_ad,
                'msg' => "头条管家文案数量必须大于等于3条"
            ];
        }

        if (($this->compose_config->pic_num_in_ad + $this->compose_config->video_num_in_ad) < 3) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => "头条管家视频+图片的数量必须大于等于3条"
            ];
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => "头条管家视频+图片的数量必须大于等于3条"
            ];
        }

        if ($this->compose_config->video_num_in_ad > 50) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => "头条管家创意视频素材最多支持50个"
            ];
        }

        if ($this->compose_config->pic_num_in_ad > 50) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => "头条管家创意图片素材最多支持50个"
            ];
        }

        if ($this->other_setting->promotion_type == 'LIVE' || $this->other_setting->promotion_type == 'GAME') {
            if ($this->creative_mode === BatchAD::CREATIVE_PROGRAM_MODE) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'creative_material_mode',
                    'value' => 0,
                    'msg' => "直播间不支持程序化创意"
                ];
            }
        }

        return $error_msg_list;
    }

}
