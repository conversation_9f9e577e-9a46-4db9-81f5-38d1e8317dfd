<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 12:02
 */

namespace App\Param\ADServing\Toutiao;

use App\Constant\BatchAD;
use App\Model\SqlModel\DataMedia\OdsToutiaoPlayableLogModel;
use App\Param\ADServing\AbstractADOtherSettingContentParam;

class ADOtherSettingContentParam extends AbstractADOtherSettingContentParam
{
    public $promotion_type = 'APP';

    public $os = ['ANDROID', 'IOS'];

    public $download_type = 'DOWNLOAD_URL';

    public $app_name = '';

    public $source = '';

    public $web_url = '';

    public $external_url = '';

    public $external_actions = '';

    /**
     * 试玩类型
     * 0：不使用
     * 1：普通试玩
     * 2：云游戏试玩
     * @var int
     */
    public $is_playable = 0;

    /**
     * 试玩ID
     * @var string
     */
    public $playable_id = '';

    public function paramHook()
    {
        if ($this->external_url) {
            $this->external_url = str_replace('exp/orange', 'page', $this->external_url);
        }
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if ($this->web_url && !preg_match("/http[s]?:\/\/[\w.]+[\w\/]*[\w.]*\??[\w=&+%]*/is", $this->web_url)) {
            $error_msg_list[] = [
                'key' => 'web_url',
                'value' => $this->web_url,
                'msg' => "不是正常的Android应用下载页地址(web_url)"
            ];
        }

        if ($this->is_playable > 0 && !$this->playable_id) {
            $error_msg_list[] = [
                'key' => 'playable_id',
                'value' => $this->playable_id,
                'msg' => '试玩素材不能为空'
            ];
        }

        if ((int)$this->is_playable === 1 && $this->playable_id) {
            $list = (new OdsToutiaoPlayableLogModel())->getPlayableUrlByPlayableId($this->playable_id);
            if ($list->status != 'AUDIT_SUCCESS') {
                $error_msg_list[] = [
                    'key' => 'playable_id',
                    'value' => $this->playable_id,
                    'msg' => '试玩素材审核未通过，不可用'
                ];
            }
        }

        return $error_msg_list;
    }

    /**
     * 参数格式化
     */
    public function format()
    {
    }


}
