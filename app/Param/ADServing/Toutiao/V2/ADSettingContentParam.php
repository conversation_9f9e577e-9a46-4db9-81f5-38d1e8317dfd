<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Toutiao\V2;

use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Param\ADServing\Toutiao\ADSettingContentParam as ToutiaoADSettingContentParam;
use App\Utils\Helpers;

class ADSettingContentParam extends ToutiaoADSettingContentParam
{
    public $delivery_mode = "MANUAL";
    public $ad1_status = ToutiaoEnum::OPERATION_ENABLE_2;
    public $ad2_status = ToutiaoEnum::OPERATION_ENABLE_2;
    public $ad1_budget_mode = ToutiaoEnum::BUDGET_MODE_INFINITE;
    public $ad1_budget = 0;
    public $ad2_budget = 0;
    public $cpa_bid = [];
    public $product_info_image_id = '';
    public $product_info_title = '';
    public $product_info_selling_points = [];
    public $call_to_action_buttons = [];
    public $flow_control_mode = '';
    public $hide_if_converted = 'NO_EXCLUDE';
    public $filter_event = [];
    public $converted_time_duration = 'THREE_MONTH';
    public $hide_if_exists = 'UNLIMITED';
    public $budget_optimize_switch = 'OFF';
    public $first_roi_goal = '';

    /**
     * @var bool 是否严格validate
     */
    public $is_strict = true;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        unset($property['is_strict']);
        $this->is_strict = $is_strict;
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === NULL) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = parent::validate();


        // 手动模式下
        if ($this->delivery_mode === ToutiaoEnum::BUDGET_MODE_MANUAL) {
            if ($this->budget_optimize_switch == ToutiaoEnum::BUDGET_OPTIMIZE_SWITCH_ON && $this->ad1_budget_mode === ToutiaoEnum::BUDGET_MODE_INFINITE) {
                $error_msg_list[] = [
                    'key' => 'ad1_budget_mode',
                    'value' => $this->ad1_budget_mode,
                    'msg' => '项目预算类型为不限的情况下， 预算择优必须是不开启的'
                ];
            }

            if ($this->budget_optimize_switch == ToutiaoEnum::BUDGET_OPTIMIZE_SWITCH_ON && $this->smart_bid_type !== ToutiaoEnum::NO_BID) {
                $error_msg_list[] = [
                    'key' => 'smart_bid_type',
                    'value' => $this->smart_bid_type,
                    'msg' => '开启预算择优的情况下 竞价策略必须是最大转化'
                ];
            }

            if ($this->ad1_budget_mode === ToutiaoEnum::BUDGET_MODE_DAY && $this->smart_bid_type === ToutiaoEnum::NO_BID) {
                if ($this->budget_optimize_switch === ToutiaoEnum::BUDGET_OPTIMIZE_SWITCH_OFF) {
                    $error_msg_list[] = [
                        'key' => 'budget_optimize_switch',
                        'value' => $this->budget_optimize_switch,
                        'msg' => '手动模式下，项目预算类型，必须日预算以及竞价策略必须是最大转化投放
'
                    ];
                }
            }
        }

        if ($this->deep_bid_type == 'ROI_COEFFICIENT' && $this->smart_bid_type != ToutiaoEnum::NO_BID) {
            if (!$this->roi_goal || !is_array($this->roi_goal)) {
                $error_msg_list[] = [
                    'key' => 'roi_goal',
                    'value' => $this->roi_goal,
                    'msg' => 'ROI系数(roi_goal)不能为空'
                ];
            } else if (is_array($this->roi_goal)) {
                foreach ($this->roi_goal as $value) {
                    if ($value < 0) {
                        $error_msg_list[] = [
                            'key' => 'roi_goal',
                            'value' => $this->roi_goal,
                            'msg' => 'ROI系数(roi_goal)不能小于0'
                        ];
                    }
                    if ($value > 5) {
                        $error_msg_list[] = [
                            'key' => 'roi_goal',
                            'value' => $this->roi_goal,
                            'msg' => 'ROI系数(roi_goal)不能大于5'
                        ];
                    }
                }
                if ((int)$this->roi_goal_mode === ToutiaoEnum::ROI_GOAL_MODE_SCOPE) {
                    if ($this->roi_goal[0] >= $this->roi_goal[1]) {
                        $error_msg_list[] = [
                            'key' => 'roi_goal',
                            'value' => $this->roi_goal_mode,
                            'msg' => 'ROI系数范围设置有误'
                        ];
                    }
                }
            }
        }

        if ($this->delivery_range == ToutiaoEnum::DELIVERY_RANGE_DEFAULT && $this->inventory_mode == 'inventory_type') {
            if (!$this->inventory_type) {
                $error_msg_list[] = [
                    'key' => 'inventory_type',
                    'value' => $this->inventory_type,
                    'msg' => "广告投放位置不能为空"
                ];
            }
            if ($this->isUnion()) {
                if (!$this->union_video_type) {
                    $error_msg_list[] = [
                        'key' => 'union_video_type',
                        'value' => $this->union_video_type,
                        'msg' => "投放形式必选"
                    ];
                }
            }
        }

        if ($this->smart_bid_type != ToutiaoEnum::NO_BID && in_array($this->deep_bid_type,[
            ToutiaoEnum:: DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI,
        ])) {
            if($this->first_roi_goal && ($this->first_roi_goal < 0 || $this->first_roi_goal > 5)) {
                $error_msg_list[] = [
                    'key' => 'first_roi_goal',
                    'value' => $this->first_roi_goal,
                    'msg' => '首日ROI(first_roi_goal)取值范围为(0,5]'
                ];
            }
        }

        if ($this->deep_bid_type == ToutiaoEnum::DEEP_BID_TYPE_DEEP_BID_MIN && $this->smart_bid_type != ToutiaoEnum::NO_BID) {
            if (!$this->deep_cpabid) {
                $error_msg_list[] = [
                    'key' => 'deep_cpabid',
                    'value' => $this->deep_cpabid,
                    'msg' => '深度优化出价(deep_cpabid)不能为空'
                ];
            } else {
                if ($this->deep_cpabid > $this->ad2_budget) {
                    $error_msg_list[] = [
                        'key' => 'deep_cpabid',
                        'value' => $this->deep_cpabid,
                        'msg' => '广告预算(budget)不能低于深度优化出价(deep_cpabid)'
                    ];
                }
            }
        }

        if ($this->cpa_bid) {
            foreach ($this->cpa_bid as $bid) {
                if (!is_numeric($bid)) {
                    $error_msg_list[] = [
                        'key' => 'cpa_bid',
                        'value' => $this->cpa_bid,
                        'msg' => "ocpm广告转化出价中有非数字的值"
                    ];
                    break;
                }
            }
        }

        if ($this->ad1_budget_mode === ToutiaoEnum::BUDGET_MODE_DAY) {
            if (!$this->ad1_budget || (int)$this->ad1_budget < 300) {
                $error_msg_list[] = [
                    'key' => 'ad1_budget',
                    'value' => $this->ad1_budget,
                    'msg' => "开启日预算时，项目预算必须大于300"
                ];
            }
        }

        if ($this->delivery_mode != "PROCEDURAL" && $this->smart_bid_type != ToutiaoEnum::NO_BID) {
            if (!$this->ad2_budget || (int)$this->ad2_budget < 300) {
                $error_msg_list[] = [
                    'key' => 'ad2_budget',
                    'value' => $this->ad2_budget,
                    'msg' => "广告预算必须大于300"
                ];
            }
        }

        if ($this->delivery_mode == "PROCEDURAL") {
            if ($this->ad1_budget_mode != ToutiaoEnum::BUDGET_MODE_DAY) {
                $error_msg_list[] = [
                    'key' => 'ad1_budget_mode',
                    'value' => $this->ad1_budget_mode,
                    'msg' => "UBA不可以使用不限预算"
                ];
            }
        }

        if (!$this->call_to_action_buttons || !is_array($this->call_to_action_buttons)) {
            $error_msg_list[] = [
                'key' => 'call_to_action_buttons',
                'value' => $this->call_to_action_buttons,
                'msg' => "请选择行动号召文案"
            ];
        } else if (count($this->call_to_action_buttons) > 10) {
            $error_msg_list[] = [
                'key' => 'call_to_action_buttons',
                'value' => $this->call_to_action_buttons,
                'msg' => "行动号召文案不能大于10个"
            ];
        }

        $product_info_title_length = mb_strlen($this->product_info_title);
        if (!$product_info_title_length) {
            $error_msg_list[] = [
                'key' => 'product_info_title',
                'value' => $this->product_info_title,
                'msg' => "请填写产品名称"
            ];
        } else if (!($product_info_title_length >= 1 && $product_info_title_length <= 20)) {
            $error_msg_list[] = [
                'key' => 'product_info_title',
                'value' => $this->product_info_title,
                'msg' => "产品名称不能大于20个字"
            ];
        }

        if (!$this->product_info_image_id) {
            $error_msg_list[] = [
                'key' => 'product_info_image_id',
                'value' => $this->product_info_image_id,
                'msg' => "请选择产品主图"
            ];
        }

        if (!$this->product_info_selling_points || !is_array($this->product_info_selling_points)) {
            $error_msg_list[] = [
                'key' => 'product_info_selling_points',
                'value' => $this->product_info_selling_points,
                'msg' => "请填写产品卖点"
            ];
        } else {
            if (count($this->product_info_selling_points) > 10) {
                $error_msg_list[] = [
                    'key' => 'product_info_selling_points',
                    'value' => $this->product_info_selling_points,
                    'msg' => "产品卖点不能大于10个"
                ];
            } else {
                foreach ($this->product_info_selling_points as $value) {
                    $value_length = Helpers::ADServingStrLen($value);
                    if (!($value_length > 12 && $value_length <= 18)) {
                        $error_msg_list[] = [
                            'key' => 'product_info_selling_points',
                            'value' => $this->product_info_selling_points,
                            'msg' => "产品卖点:{$value},不符合产品卖点长度需要大于6并且小于等于9（英文字符算半个）的规范"
                        ];
                        break;
                    }
                }
            }
        }


        return $error_msg_list;
    }

    public function paramHook()
    {
        $this->format();
    }

    public function format()
    {
        parent::format();
        if (!in_array($this->deep_bid_type,[
            ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT,
            ToutiaoEnum:: DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI,
            ToutiaoEnum::DEEP_BID_TYPE_PER_AND_SEVEN_PAY_ROI,
        ])) {
            $this->roi_goal = [];
        }

        if (!in_array($this->deep_bid_type,[
            ToutiaoEnum:: DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI,
        ])) {
            $this->first_roi_goal = '';
        }

        if ($this->deep_bid_type != ToutiaoEnum::DEEP_BID_TYPE_DEEP_BID_MIN) {
            $this->deep_cpabid = '';
        }
        $this->cpa_bid = array_values(array_filter($this->cpa_bid));
    }

    /**
     * @return string
     */
    public function getLDYContent()
    {
        throw new AppException('头条无落地页adid');
    }

    /**
     * 仅投放穿山甲
     * @return bool
     */
    public function isUnion()
    {
        return in_array(ToutiaoEnum::INVENTORY_TYPE_UNION, $this->inventory_type) && count($this->inventory_type) === 1;
    }
}
