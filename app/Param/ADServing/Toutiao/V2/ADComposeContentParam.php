<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Toutiao\V2;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\Platform;
use App\Constant\PlatId;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsAwemeAccountNameLogModel;
use App\Param\ADServing\Toutiao\ADComposeContentParam as ToutiaoADComposeContentParam;
use App\Param\ADServing\Toutiao\ADTargetingContentParam;
use App\Service\AwemePriceRangeFilterService;
use App\Service\GamePriceRangeFilterService;

class ADComposeContentParam extends ToutiaoADComposeContentParam
{
    /**
     * @var ADOtherSettingContentParam $other_setting
     */
    public $other_setting;


    /**
     * 获取渠道组
     * @param ADSettingContentParam $setting
     * @param ADOtherSettingContentParam $other_setting
     * @return int
     */
    private function getAgentGroup(ADSettingContentParam $setting, ADOtherSettingContentParam $other_setting): int
    {
        if ($other_setting->marketing_goal == ToutiaoEnum::MARKETING_GOAL_LIVE) {
            return AgentGroup::TOUTIAO_LIVE;
        }

        if ($other_setting->origin_ad_type == 'origin') {
            return AgentGroup::TOUTIAO_ORIGIN;
        }

        if ($other_setting->origin_ad_type == 'hot') {
            return AgentGroup::TOUTIAO_HOT;
        }

        if (in_array(ToutiaoEnum::INVENTORY_TYPE_UNION, $setting->inventory_type)) {
            return AgentGroup::TOUTIAO_UNION;
        }

        return AgentGroup::TOUTIAO;
    }

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = parent::unionParamValidateJudge();

        $other_setting = $this->other_setting;

//        if (
//            in_array($this->site_config->game_id, [14800, 14802]) &&
//            $this->site_config->ext['strategy_type'] == 1 &&
//            !(count(((json_decode($this->site_config->ext['strategy_json'], true))['split_order']) ?? []) > 0)
//        ) {
//            throw new AppException("此游戏({$this->site_config->game_id})必须要配置扣量标签");
//        }


        // 图文创意
        if (in_array($other_setting->origin_ad_type, ['default', 'origin']) && $other_setting->is_carousel) {
            if ($this->material_list->group_image_list) {
                $error_msg_list[] = [
                    'key' => 'all_image_list',
                    'value' => $this->material_list->all_image_list,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'msg' => "当使用图文创意时，不支持视频或组图"
                ];
            }
            if ($other_setting->ad_type == 'ALL') {
                if ($this->compose_config->pic_num_in_ad > 10) {
                    $error_msg_list[] = [
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'msg' => '当使用图文创意时，单元创意图片数量不能超过10'
                    ];
                }
                if ($this->compose_config->pic_num_in_ad < 2) {
                    $error_msg_list[] = [
                        'key' => 'pic_num_in_ad',
                        'value' => $this->compose_config->pic_num_in_ad,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'msg' => '当使用图文创意时，单元创意图片数量不能小于2'
                    ];
                }
                if ($this->compose_config->video_num_in_ad != 1) {
                    $error_msg_list[] = [
                        'key' => 'video_num_in_ad',
                        'value' => $this->compose_config->video_num_in_ad,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'msg' => '当使用图文创意时，单元创意视频/音频数量必须为1'
                    ];
                }
            }

        }

        if (in_array($this->platform, [Platform::TW]) && $other_setting->origin_ad_type == 'origin' && $other_setting->marketing_goal == ToutiaoEnum::MARKETING_GOAL_VIDEO_AND_IMAGE) {
            //把抖音号转化成小写,原因： ods_aweme_account_name_log表入库时，live_user全部是小写，好做统一匹配
            $live_user = [];
            foreach ($other_setting->ies_map as $key => $value) {
                $live_user[$key] = strtolower($value);
            }
            $check_aweme_id = (new OdsAwemeAccountNameLogModel())
                ->getListByGroupLiveUser($this->platform, MediaType::TOUTIAO, 2, array_values($live_user))
                ->toArray();
            if (empty($check_aweme_id)) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'ies_map',
                    'value' => $other_setting->ies_map,
                    'msg' => "所有账号列表下的抖音主播授权未授权或者已失效，请媒体后台核对正确后重新授权，在广告投放-达人对接管理下配置\"可投直投\""
                ];
            } else {
                if (count($live_user) != count($check_aweme_id)) {
                    $select_live_user_data = array_column($check_aweme_id, 'live_user');
                    //获取差异
                    $diff_live_user = array_diff($live_user, $select_live_user_data);
                    // key值转换
                    $aweme_id_switch_account_id = array_flip($live_user);
                    foreach ($diff_live_user as $value) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'ies_map',
                            'value' => $other_setting->ies_map,
                            'msg' => "{$aweme_id_switch_account_id[$value]}还未选择对应的主播或者该主播授权已过期，在广告投放-达人对接管理下配置\"可投直投\""
                        ];
                    }
                }
            }
        }
        foreach ($this->account_list->account_list as $account_info) {
            if ($other_setting->origin_ad_type == 'origin') {
                if (!isset($other_setting->ies_map[$account_info['account_id']]) || !$other_setting->ies_map[$account_info['account_id']]) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'ies_map',
                        'value' => $other_setting->ies_map,
                        'msg' => "{$account_info['account_id']}还未选择对应的主播或者该主播授权已过期，请重新授权"
                    ];
                }
            }
            if ($other_setting->origin_ad_type == 'hot') {
                if (!isset($other_setting->hot_material_map[$account_info['account_id']]) || !$other_setting->hot_material_map[$account_info['account_id']]) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'hot_material_map',
                        'value' => $other_setting->hot_material_map,
                        'msg' => "{$account_info['account_id']}还未选择原生加热素材"
                    ];
                }
            }
            if ($other_setting->origin_ad_type == 'star') {
                if (!isset($other_setting->star_deliver_map[$account_info['account_id']]) || !$other_setting->star_deliver_map[$account_info['account_id']]) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'star_deliver_map',
                        'value' => $other_setting->hot_material_map,
                        'msg' => "{$account_info['account_id']}还未选择联投素材"
                    ];
                }
            }

            if ($other_setting->guide_video_type == 'yes') {
                if (!isset($other_setting->guide_video_id_map[$account_info['account_id']]) || !$other_setting->guide_video_id_map[$account_info['account_id']]) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'guide_video_id_map',
                        'value' => $other_setting->guide_video_id_map,
                        'msg' => "{$account_info['account_id']}还未选择引导视频"
                    ];
                }
            }

            // 手动选择锚点
            if ($other_setting->anchor_related_type == ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                if (!isset($other_setting->anchor_related_map[$account_info['account_id']]) || !$other_setting->anchor_related_map[$account_info['account_id']]) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'anchor_related_map',
                        'value' => $other_setting->anchor_related_map,
                        'msg' => "{$account_info['account_id']}原生锚点选择不能为空"
                    ];
                }
            }
        }

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
            /* @var ADSettingContentParam $setting */

            foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {
                /* @var ADTargetingContentParam $targeting */
                if ($setting->delivery_mode == 'PROCEDURAL' && $targeting->retargeting_tags_include) {
                    $error_msg_list[] = [
                        'key' => 'retargeting_tags_include',
                        'value' => $targeting->retargeting_tags_include,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "UBA不能使用定向包，只能排除人群"
                    ];
                }

//                if ($setting->delivery_mode == 'PROCEDURAL' && in_array($other_setting->origin_ad_type,['default','origin']) && $other_setting->is_carousel) {
//                    $error_msg_list[] = [
//                        'key' => 'delivery_mode',
//                        'value' => $setting->delivery_mode,
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                        'msg' => "图文创意不支持UBA投放"
//                    ];
//                }

                if ($setting->delivery_mode == 'PROCEDURAL' && $targeting->interest_action_mode != 'UNLIMITED') {
                    $error_msg_list[] = [
                        'key' => 'interest_action_mode',
                        'value' => $targeting->interest_action_mode,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "UBA不能使用兴趣行为"
                    ];
                }

                if ($setting->delivery_mode == 'PROCEDURAL' && $targeting->auto_extend_enabled) {
                    $error_msg_list[] = [
                        'key' => 'interest_action_mode',
                        'value' => $targeting->interest_action_mode,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "UBA不能使用智能投放(放量)"
                    ];
                }
            }

//            if (
//                $setting->hide_if_converted != 'NO_EXCLUDE' &&
//                $this->site_config->game_type == 'IOS'
//            ) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'hide_if_converted',
//                    'value' => $setting->hide_if_converted,
//                    'msg' => "iOS应用不支持过滤已安装用户定向"
//                ];
//            }

            if ($setting->smart_bid_type != ToutiaoEnum::NO_BID) {
                if (!$setting->cpa_bid || !is_array($setting->cpa_bid)) {
                    $cpa_bid = 0;
                } else {
                    if ((int)$setting->cpa_bid_mode === ToutiaoEnum::CPA_BID_MODE_SCOPE) {
                        $cpa_bid = $setting->cpa_bid[1];
                    } else {
                        $cpa_bid = max($setting->cpa_bid);
                    }
                }


                if (!$setting->roi_goal || !is_array($setting->roi_goal)) {
                    $roi_goal = '';
                } else {
                    if ((int)$setting->roi_goal_mode === ToutiaoEnum::ROI_GOAL_MODE_SCOPE) {
                        $roi_goal = $setting->roi_goal[0];
                    } else {
                        $roi_goal = min($setting->roi_goal);
                    }
                }

                if ($other_setting->marketing_goal == 'LIVE') {
                    $aweme_error_msg = '';
                    $aweme_is_judge_list = [];
                    foreach ($other_setting->ies_map as $ies_value) {
                        if (!in_array($ies_value, $aweme_is_judge_list)) {
                            $aweme_error = AwemePriceRangeFilterService::isCorrectPrice(
                                MediaType::TOUTIAO,
                                $this->platform,
                                $this->site_config->game_id,
                                $this->site_config->game_type,
                                $this->getAgentGroup($setting, $other_setting),
                                $ies_value,
                                ConvertType::MEDIA[MediaType::TOUTIAO][$this->site_config->convert_type],
                                $setting->deep_bid_type,
                                $cpa_bid,
                                $roi_goal
                            );
                            if ($aweme_error) {
                                if ($cpa_bid && ($aweme_error['bid'] ?? '')) {
                                    $aweme_error_msg .= "{$aweme_error['bid']};";
                                }

                                if ($roi_goal && ($aweme_error['roi_goal'] ?? '')) {
                                    $aweme_error_msg .= "{$aweme_error['bid']};";
                                }
                            }
                            $aweme_is_judge_list[] = $ies_value;
                        }
                    }
                    if ($aweme_error_msg) {
                        $error_msg_list[] = [
                            'key' => 'ies_map',
                            'value' => $other_setting->ies_map,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'msg' => $aweme_error_msg
                        ];
                    }
                }

                $error = GamePriceRangeFilterService::isCorrectPrice(
                    MediaType::TOUTIAO,
                    $this->platform,
                    $this->site_config->game_id,
                    $this->site_config->game_type,
                    $this->getAgentGroup($setting, $other_setting),
                    ConvertType::MEDIA[MediaType::TOUTIAO][$this->site_config->convert_type],
                    $setting->deep_bid_type,
                    $cpa_bid,
                    $roi_goal
                );

                if ($error) {
                    if ($cpa_bid && ($error['bid'] ?? '')) {
                        $error_msg_list[] = [
                            'key' => 'cpa_bid',
                            'value' => $setting->cpa_bid,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'msg' => $error['bid']
                        ];
                    }

                    if ($roi_goal && ($error['roi_goal'] ?? '')) {
                        $error_msg_list[] = [
                            'key' => 'roi_goal',
                            'value' => $setting->roi_goal,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                            'msg' => $error['roi_goal']
                        ];
                    }
                }
            }

            if ($setting->smart_bid_type == 'UPPER_CONTROL' && $this->site_config->deep_external_action) {
                $error_msg_list[] = [
                    'key' => 'smart_bid_type',
                    'value' => $setting->smart_bid_type,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'msg' => "最优成本最优成本下不能使用深度"
                ];
            }

            if ($setting->delivery_range == ToutiaoEnum::DELIVERY_RANGE_DEFAULT) {
                if ($this->other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
                    if ($this->other_setting->is_playable > 0 &&
                        !($this->other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) &&
                        !($setting->isUnion() && $setting->union_video_type === ToutiaoEnum::UNION_VIDEO_TYPE_REWARDED_VIDEO)) {
                        $error_msg_list[] = [
                            'key' => 'is_playable',
                            'value' => $this->other_setting->is_playable,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'msg' => "试玩素材仅支持直接下载类型或者穿山甲激励视频创意"
                        ];
                    }
                }
            }
            if ($setting->delivery_mode == 'PROCEDURAL' &&
                !in_array($this->other_setting->promotion_type, [ToutiaoEnum::LANDING_TYPE_APP, ToutiaoEnum::LANDING_TYPE_MICRO_GAME])) {
                $error_msg_list[] = [
                    'key' => 'promotion_type',
                    'value' => $this->other_setting->promotion_type,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'msg' => "在自动投放的模式下，只能是APP或者小游戏"
                ];
            }
//            if ($setting->delivery_mode == 'PROCEDURAL' &&
//                $this->other_setting->promotion_type == ToutiaoEnum::LANDING_TYPE_APP &&
//                $this->other_setting->app_promotion_type != ToutiaoEnum::APP_PROMOTION_TYPE_DOWNLOAD
//            ) {
//                $error_msg_list[] = [
//                    'key' => 'app_promotion_type',
//                    'value' => $this->other_setting->app_promotion_type,
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                    'msg' => "在自动投放的模式下，只能应用下载"
//                ];
//            }

            if (
                $setting->delivery_mode == 'PROCEDURAL' &&
                $setting->deep_bid_type &&
                $setting->deep_bid_type != ToutiaoEnum::DEEP_BID_TYPE_DEFAULT &&
                !in_array($setting->deep_bid_type, [
                    ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT,
                    ToutiaoEnum::DEEP_BID_TYPE_BID_PER_ACTION,
                    ToutiaoEnum::DEEP_BID_TYPE_ROI_PACING,
                    ToutiaoEnum::DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI,
                    ToutiaoEnum::DEEP_BID_TYPE_PER_AND_SEVEN_PAY_ROI
                ])
            ) {
                $error_msg_list[] = [
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'msg' => "在自动投放的模式下，深度转化类型 只支持每次付费、ROI自动优化、付费ROI、首日+7日付费ROI、每次+7日付费ROI"
                ];
            }

            if (in_array($setting->deep_bid_type, [
                    ToutiaoEnum::DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI,
                    ToutiaoEnum::DEEP_BID_TYPE_PER_AND_SEVEN_PAY_ROI
                ]) && $setting->delivery_mode != 'PROCEDURAL'
            ) {
                $error_msg_list[] = [
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'msg' => "深度出价模式为首日+7日付费ROI、每次+7日付费ROI，必须是UBA和非最大转化投放的情况下使用"
                ];
            }

            if (
                $setting->delivery_mode == 'PROCEDURAL' &&
                $setting->delivery_range != ToutiaoEnum::DELIVERY_RANGE_UNIVERSAL
            ) {
                $error_msg_list[] = [
                    'key' => 'delivery_range',
                    'value' => $setting->delivery_range,
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'msg' => "在自动投放的模式下，只支持通投智选"
                ];
            }

            // 营销类型为直播
            if ($this->other_setting->marketing_goal === ToutiaoEnum::MARKETING_GOAL_LIVE) {
                if ($setting->delivery_range !== ToutiaoEnum::DELIVERY_RANGE_DEFAULT && $setting->delivery_mode != 'PROCEDURAL') {
                    $error_msg_list[] = [
                        'key' => 'delivery_range',
                        'value' => $setting->delivery_range,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => "当营销类型为直播时而且是非UBA，仅支持默认，不支持通投智选"
                    ];
                }
                if ($setting->delivery_mode != 'PROCEDURAL' && ((in_array(ToutiaoEnum::INVENTORY_AWEME_FEED, $setting->inventory_type) && count($setting->inventory_type) > 1) || !in_array(ToutiaoEnum::INVENTORY_AWEME_FEED, $setting->inventory_type))) {
                    $error_msg_list[] = [
                        'key' => 'inventory_type',
                        'value' => $setting->inventory_type,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => "当营销类型为直播时，仅支持抖音信息流"
                    ];
                }
                if ($this->other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP && !in_array($this->other_setting->app_promotion_type, [
                        ToutiaoEnum::APP_PROMOTION_TYPE_DOWNLOAD,
                        ToutiaoEnum::APP_PROMOTION_TYPE_LAUNCH,
                        ToutiaoEnum::APP_PROMOTION_TYPE_RESERVE,
                    ])) {
                    $error_msg_list[] = [
                        'key' => 'app_promotion_type',
                        'value' => $this->other_setting->app_promotion_type,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'msg' => "当营销类型为直播时，仅支持应用下载、应用调起、预约下载"
                    ];
                }
                if ($this->other_setting->ad_type != ToutiaoEnum::AD_TYPE) {
                    $error_msg_list[] = [
                        'key' => 'ad_type',
                        'value' => $this->other_setting->ad_type,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'msg' => "当营销类型为直播时，广告类型仅支持仅支持全部"
                    ];
                }
                if ($this->material_list->all_image_list) {
                    $error_msg_list[] = [
                        'key' => 'all_image_list',
                        'value' => $this->material_list->all_image_list,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'msg' => "当营销类型为直播时，不支持图片素材"
                    ];
                }
            }
        }

        if (
            !in_array($this->platform, ['GR', 'GRBB']) &&
            $this->site_config->game_type == '安卓' &&
            $this->site_config->plat_id == PlatId::MINI &&
            !(
                count($other_setting->os) == 2 ||
                count($other_setting->os) == 0
            )
        ) {
            $error_msg_list[] = [
                'key' => 'os',
                'value' => $other_setting->os,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'msg' => "安卓的小游戏的情况下需要同时选安卓和IOS 或者 都不选"
            ];
        }

        if (
            $this->site_config->game_type == 'IOS' &&
            $this->site_config->plat_id == PlatId::MINI &&
            !(
                count($other_setting->os) == 1 &&
                in_array('IOS', $other_setting->os)
            )
        ) {
            $error_msg_list[] = [
                'key' => 'os',
                'value' => $other_setting->os,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'msg' => "IOS的小游戏的情况下只能选IOS"
            ];
        }

        if ($other_setting->origin_ad_type == 'hot' && count($this->material_list->getAllMaterialList()) > 1) {
            $error_msg_list[] = [
                'key' => 'origin_ad_type',
                'value' => $other_setting->origin_ad_type,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'msg' => "加热的情况下在广告素材处只能选择一个素材"
            ];
        }

        if ($other_setting->origin_ad_type == 'hot' && ($this->compose_config->video_num_in_ad + $this->compose_config->pic_num_in_ad) > 1) {
            $error_msg_list[] = [
                'key' => 'origin_ad_type',
                'value' => $other_setting->origin_ad_type,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'msg' => "加热的情况下在视频数量与大图数量总和只能为1"
            ];
        }

        // 小游戏||销售线索推广
        if (in_array($this->other_setting->promotion_type, [ToutiaoEnum::LANDING_TYPE_MICRO_GAME, ToutiaoEnum::LANDING_TYPE_LINK])) {
            foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
                if ($setting->hide_if_exists != ToutiaoEnum::INTEREST_ACTION_MODE_UNLIMITED) {
                    $error_msg_list[] = [
                        'key' => 'hide_if_exists',
                        'value' => $setting->hide_if_exists,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => "投放小游戏or销售线索推广时,过滤已安装必须是不限"
                    ];
                }
            }
            foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {
                if (!empty($targeting->ios_osv)) {
                    $error_msg_list[] = [
                        'source_id' => $targeting_id,
                        'key' => 'ios_osv',
                        'value' => $targeting->ios_osv,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "投放小游戏时,IOS版本必须是不限"
                    ];
                }
                if ($targeting->android_osv) {
                    $error_msg_list[] = [
                        'source_id' => $targeting_id,
                        'key' => 'android_osv',
                        'value' => $targeting->android_osv,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "投放小游戏是,安卓版本必须是不限"
                    ];
                }
            }
            // 独立落地页
            foreach ($this->account_list->account_list as $account_info) {
                if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE &&
                    $this->other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                    if (empty($this->other_setting->external_url_map[$account_info['account_id']])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'external_url_map',
                            'value' => $this->other_setting->external_url_map,
                            'msg' => '应用下载方式为落地页时，落地页地址(独立)(external_url_map)不能为空'
                        ];
                    }
                }
                // 单账号多选落地页时
                if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE &&
                    $this->other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                    if (empty($this->other_setting->external_url_map_more[$account_info['account_id']])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'external_url_map_more',
                            'value' => $this->other_setting->external_url_map_more,
                            'msg' => '应用下载方式为落地页时，落地页不能为空'
                        ];
                    }
                    foreach ($this->other_setting->external_url_map_more[$account_info['account_id']] as $external_url_map_more_val) {
                        if (empty($external_url_map_more_val)) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                'key' => 'external_url_map_more',
                                'value' => $this->other_setting->external_url_map_more,
                                'msg' => '应用下载方式为落地页时，所有输入框不能为空'
                            ];
                        }
                    }
                }
            }
        }

        if ((!$this->compose_config->ad2_name || count($this->compose_config->ad2_name) <= 0)) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'ad2_name',
                'value' => $this->compose_config->ad2_name,
                'msg' => "广告二级组合名字为空"
            ];
        }

        if ($this->other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
            if ($this->site_config->game_type === '安卓') {
                // 下载链接
                if ($this->other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
                    if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_SHARE
                        && (empty($this->other_setting->web_url_list[0]) && count($this->other_setting->web_url_list) <= 0)) {
                        $error_msg_list[] = [
                            'key' => 'web_url_list',
                            'value' => $this->other_setting->web_url_list,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'msg' => "选择橙子建站(共享)直接下载时，安卓下载地址不能为空"
                        ];
                    }
                    if (in_array($this->other_setting->download_url_type, [ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE, ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE])) {
                        foreach ($this->account_list->account_list as $account_info) {
                            if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE) {
                                if (empty($this->other_setting->web_url_map[$account_info['account_id']]))
                                    $error_msg_list[] = [
                                        'key' => 'web_url_map',
                                        'value' => $this->other_setting->web_url_map,
                                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                        'msg' => "选择橙子建站(独立)直接下载时，安卓下载地址不能为空"
                                    ];
                            }
                            // 单账号多落地页
                            if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE) {
                                if (empty($this->other_setting->web_url_map_more[$account_info['account_id']])) {
                                    $error_msg_list[] = [
                                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                        'key' => 'web_url_map_more',
                                        'value' => $this->other_setting->web_url_map_more,
                                        'msg' => '选择橙子建站(单账号-多选)时，落地页不能为空'
                                    ];
                                }
                                foreach ($this->other_setting->web_url_map_more[$account_info['account_id']] as $web_url_map_more_val) {
                                    if (empty($web_url_map_more_val)) {
                                        $error_msg_list[] = [
                                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                            'key' => 'web_url_map_more',
                                            'value' => $this->other_setting->web_url_map_more,
                                            'msg' => '选择橙子建站(单账号-多选)时，所有输入框不能为空'
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
                // 落地页链接
                if ($this->other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                    if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_SHARE
                        && (empty($this->other_setting->external_url_list[0]) && count($this->other_setting->external_url_list) <= 0)) {
                        $error_msg_list[] = [
                            'key' => 'external_url_list',
                            'value' => $this->other_setting->external_url_list,
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'msg' => "选择落地页(共享)直接下载时，安卓落地页地址不能为空"
                        ];
                    }
                    if (in_array($this->other_setting->download_url_type, [ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE, ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE])) {
                        foreach ($this->account_list->account_list as $account_info) {
                            if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE) {
                                if (empty($this->other_setting->external_url_map[$account_info['account_id']])) {
                                    $error_msg_list[] = [
                                        'key' => 'external_url_map',
                                        'value' => $this->other_setting->external_url_map,
                                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                        'msg' => "选择落地页(独立)直接下载时，安卓落地页地址不能为空"
                                    ];
                                }
                            }
                            // 单账号多落地页
                            if ($this->other_setting->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE) {
                                if (empty($this->other_setting->external_url_map_more[$account_info['account_id']])) {
                                    $error_msg_list[] = [
                                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                        'key' => 'external_url_map_more',
                                        'value' => $this->other_setting->external_url_map_more,
                                        'msg' => '选择落地页(单账号-多选)时，落地页不能为空'
                                    ];
                                }
                                foreach ($this->other_setting->external_url_map_more[$account_info['account_id']] as $external_url_map_more_val) {
                                    if (empty($external_url_map_more_val)) {
                                        $error_msg_list[] = [
                                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                            'key' => 'external_url_map_more',
                                            'value' => $this->other_setting->external_url_map_more,
                                            'msg' => '选择落地页(单账号-多选)时，所有输入框不能为空'
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($this->compose_config->video_num_in_ad > 30) {
            $error_msg_list[] = [
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'msg' => "单元视频数量不能大于30"
            ];
        }

        if ($this->compose_config->pic_num_in_ad > 30) {
            $error_msg_list[] = [
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'msg' => "单元图片数量不能大于30"
            ];
        }

        if (
            (!in_array('site_id', $this->compose_config->ad1_name) &&
                !in_array('time', $this->compose_config->ad1_name) &&
                !in_array('date_time', $this->compose_config->ad1_name) &&
                !in_array('random', $this->compose_config->ad1_name)
            )
            && in_array('site_id', $this->compose_config->ad2_name)
        ) {
            $error_msg_list[] = [
                'key' => 'ad2_name',
                'value' => $this->compose_config->ad2_name,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'msg' => '信息流2.0,当且仅当一级名称包含广告位、随机数或者时间，二级名称才可以使用广告位'
            ];
        }

        $suffix = '';
        $other_setting->marketing_goal == ToutiaoEnum::MARKETING_GOAL_LIVE && $suffix = '直播';
        $other_setting->marketing_goal == ToutiaoEnum::MARKETING_GOAL_VIDEO_AND_IMAGE && $other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_HOT && $suffix = '加热';
        $other_setting->marketing_goal == ToutiaoEnum::MARKETING_GOAL_VIDEO_AND_IMAGE && $other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN && $suffix = '原生';

        if ($suffix && !in_array($suffix, $this->compose_config->ad1_name)) {
            $error_msg_list[] = [
                'key' => 'ad1_name',
                'value' => $this->compose_config->ad1_name,
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'msg' => "选择{$suffix}广告类型时，一级广告名称组成方式需补充  \"{$suffix}\"  字样"
            ];
        }

        return $error_msg_list;
    }

}
