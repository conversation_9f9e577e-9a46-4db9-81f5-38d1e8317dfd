<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Toutiao\V2;

use App\Constant\BatchAD;
use App\Constant\ToutiaoEnum;
use App\Param\ADServing\Toutiao\ADOtherSettingContentParam as ToutiaoADOtherSettingContentParam;

class ADOtherSettingContentParam extends ToutiaoADOtherSettingContentParam
{
    public $hot_material_map = [];
    public $app_promotion_type = 'DOWNLOAD';
    public $marketing_goal = 'VIDEO_AND_IMAGE';
    public $ad_type = 'ALL';
    public $download_mode = 'DEFAULT';
    public $web_url_list = [];
    public $external_url_list = [];
    public $source = '';
    public $micro_promotion_type = 'WECHAT_GAME';

    public $web_url_map = [];
    public $external_url_map = [];
    public $is_feed_and_fav_see = 'OFF';
    public $origin_ad_type = 'default';
    /**
     * 原生锚点
     * @var string
     */
    public $anchor_related_type = 'OFF';
    public $anchor_related_map = [];
    /**
     * 抖音号
     * @var object
     */
    public $ies_map;

    /**
     * 落地页类型
     * @var int
     */
    public $download_url_type = 0;
    /**
     * 预约链接
     * @var string
     */
    public $subscribe_url = '';

    /**
     * 下载链接
     * @var string
     */
    public $web_url = '';

    /**
     * 落地页链接
     * @var string
     */
    public $external_url = '';
    /**
     * 单账号多落地页
     * @var string
     */
    public $web_url_map_more = [];
    public $external_url_map_more = [];

    /**
     * 图文素材
     * @var int
     */
    public $is_carousel = 0;

    /**
     * 素材类型
     * @return void
     */
    public $materials_type = 'PROMOTION_MATERIALS';

    /**
     * 星广联投
     */
    public $star_task_id = '';
    public $star_material_num = 10;
    public $star_deliver_map = [];

    /**
     * 创意组件
     */
    public $component_map = [];

    /**
     * 调起方式
     */
    public $launch_type = 'DIRECT_OPEN';

    public $guide_video_type = '';
    public $guide_video_id_map = [];

    public function paramHook()
    {
        parent::paramHook();
        $this->hot_material_map = $this->tranObject($this->hot_material_map);
        $this->format();
    }

    public function format()
    {
        parent::format();

        $this->ies_map = $this->tranObject($this->ies_map);
        foreach ($this->ies_map as &$ies_info) {
            if ($ies_info && is_string($ies_info)) {
                $ies_info = trim($ies_info);
            }
        }

        $this->external_url_map_more = $this->tranObject($this->external_url_map_more);

        $this->web_url_map_more = $this->tranObject($this->web_url_map_more);

        if ($this->web_url && !$this->web_url_list) {
            $this->web_url_list[] = trim($this->web_url);
        }

        if ($this->external_url && !$this->external_url_list) {
            $this->external_url_list[] = trim($this->external_url);
        }

        $this->web_url_list = array_map('trim', $this->web_url_list);
        $this->external_url_list = array_map('trim', $this->external_url_list);

        $this->anchor_related_map = $this->tranObject($this->anchor_related_map);
        $this->web_url_map = $this->tranObject($this->web_url_map);
        $this->external_url_map = $this->tranObject($this->external_url_map);
    }

    public function validate()
    {
        $error_msg_list = parent::validate();

        if ($this->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_STAR) {
            if ($this->ad_type != ToutiaoEnum::AD_TYPE) {
                $error_msg_list[] = [
                    'key' => 'ad_type',
                    'value' => $this->ad_type,
                    'msg' => "选择星广联投任务时，广告类型仅支持全部"
                ];
            }
            if ($this->marketing_goal != ToutiaoEnum::MARKETING_GOAL_VIDEO_AND_IMAGE) {
                $error_msg_list[] = [
                    'key' => 'ad_type',
                    'value' => $this->ad_type,
                    'msg' => "选择星广联投任务时，营销场景只能选短视频/图片"
                ];
            }
        }

        if ($this->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
            if ($this->app_promotion_type === ToutiaoEnum::APP_PROMOTION_TYPE_RESERVE && !$this->subscribe_url) {
                $error_msg_list[] = [
                    'key' => 'subscribe_url',
                    'value' => $this->subscribe_url,
                    'msg' => "选择应用预约时，预约下载地址必填"
                ];
            }

            if ($this->app_promotion_type === ToutiaoEnum::APP_PROMOTION_TYPE_RESERVE && $this->download_type != ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                $error_msg_list[] = [
                    'key' => 'download_type',
                    'value' => $this->download_type,
                    'msg' => "选择应用预约时，只能选落地页链接"
                ];
            }
        }

        // 营销场景直播
        if ($this->marketing_goal === ToutiaoEnum::MARKETING_GOAL_LIVE) {
            if ($this->origin_ad_type !== ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN) {
                $error_msg_list[] = [
                    'key' => 'origin_ad_type',
                    'value' => $this->origin_ad_type,
                    'msg' => "当营销类型为直播时，原生广告不能是默认"
                ];
            }
            if ($this->anchor_related_type !== ToutiaoEnum::ANCHOR_RELATED_TYPE_OFF) {
                $error_msg_list[] = [
                    'key' => 'anchor_related_type',
                    'value' => $this->anchor_related_type,
                    'msg' => "当营销类型为直播时，直播链路仅支持不启用"
                ];
            }
            if ($this->promotion_type === ToutiaoEnum::LANDING_TYPE_LINK) {
                if ($this->download_type !== ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                    $error_msg_list[] = [
                        'key' => 'download_type',
                        'value' => $this->download_type,
                        'msg' => "当营销类型为直播时，销售线索仅支持橙子建站"
                    ];
                }
            }
        }

        if ($this->promotion_type === ToutiaoEnum::LANDING_TYPE_MICRO_GAME) {
//            if (empty($this->source)) {
//                $error_msg_list[] = [
//                    'key' => 'source',
//                    'value' => $this->source,
//                    'msg' => "推广目的为销售线索推广或者小游戏时,广告来源不能为空!"
//                ];
//            }
            if ($source_len = mb_strlen($this->source)) {
                if ($source_len < 2 || $source_len > 10) {
                    $error_msg_list[] = [
                        'key' => 'source',
                        'value' => $this->source,
                        'msg' => "广告来源长度要求为2-10个中文"
                    ];
                }
            }
            if ($this->download_type != 'EXTERNAL_URL') {
                $error_msg_list[] = [
                    'key' => 'external_url_list',
                    'value' => $this->external_url_list,
                    'msg' => '推广目的为小游戏时--应用下载方式必须为落地页链接'
                ];
            }
        }
        if ($this->download_type == 'EXTERNAL_URL') {
            if ($this->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_SHARE) {
                if (empty($this->external_url_list[0]) || count($this->external_url_list) <= 0) {
                    $error_msg_list[] = [
                        'key' => 'external_url_list',
                        'value' => $this->external_url_list,
                        'msg' => '应用下载方式为落地页时，落地页地址(共享)(external_url_list)不能为空'
                    ];
                }
            }
        }

        return $error_msg_list;
    }


    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array
     */
    public function getV2PageWebUrlMapInfo($account_id)
    {
        if ($this->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE) {
            return isset($this->web_url_map[$account_id]) ? [$this->web_url_map[$account_id]] : [];
        } elseif ($this->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE) {
            return isset($this->web_url_map[$account_id]) ? array_values($this->web_url_map_more[$account_id]) : [];
        }
        return $this->web_url_list ?? [];
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array
     */
    public function getV2PageExternalUrlMapInfo($account_id)
    {
        if ($this->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE) {
            return isset($this->external_url_map[$account_id]) ? [
                preg_replace("/\xE2\x80\x8B/", "", str_replace('exp/orange', 'page', $this->external_url_map[$account_id]))
            ] : [];
        } elseif ($this->download_url_type == ToutiaoEnum::DOWNLOAD_URL_TYPE_ALONE_MORE) {
            if (isset($this->external_url_map_more[$account_id])) {
                foreach ($this->external_url_map_more[$account_id] as &$ex_more) {
                    $ex_more = preg_replace("/\xE2\x80\x8B/", "", str_replace('exp/orange', 'page', $ex_more));
                }
            }
            return isset($this->external_url_map_more[$account_id]) ? array_values($this->external_url_map_more[$account_id]) : [];
        }
        return $this->external_url_list ?? [];
    }

    /**
     * @param $account_id
     * @return mixed|string
     */
    public function getComponentId($account_id)
    {
        if ($component_info = $this->component_map[$account_id] ?? '') {
            $component_info = json_decode($component_info);
            return $component_info->component_id ?? '';
        }
        return '';
    }

}
