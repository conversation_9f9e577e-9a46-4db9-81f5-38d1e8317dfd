<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Toutiao\Basics;

use App\Constant\BatchAD;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\Platform;
use App\Constant\PlatId;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Logic\DSP\ADServingLogic;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\SqlModel\DataMedia\OdsAwemeAccountNameLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameStandardValueDaysIncolumnModel;
use App\Param\ADServing\Toutiao\ADComposeContentParam as ToutiaoADComposeContentParam;
use App\Param\ADServing\Toutiao\ADTargetingContentParam;
use Common\EnvConfig;

class ADComposeContentParam extends ToutiaoADComposeContentParam
{
    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = parent::unionParamValidateJudge();

        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $this->other_setting;

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            /* @var ADSettingContentParam $setting */

            foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {

                /**@var ADTargetingContentParam $targeting */

                if ($other_setting->download_type == 'DOWNLOAD_URL') {

                    if ($this->site_config->game_type != 'IOS') {
                        if (in_array($this->media_agent_type, [0, 1])) {
                            if (!$other_setting->web_url && $other_setting->download_url_type == 0) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                    'key' => 'web_url',
                                    'value' => $other_setting->web_url,
                                    'msg' => '下载类型为安卓时，Android应用下载页(web_url)不能为空'
                                ];
                            }
                            if (!$other_setting->web_url && $other_setting->download_url_type == 1) {
                                foreach ($this->account_list->account_list as $account_info) {
                                    if (!isset($other_setting->web_url_map[$account_info['account_id']]) || !$other_setting->web_url_map[$account_info['account_id']]) {
                                        $error_msg_list[] = [
                                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                            'key' => 'web_url_map',
                                            'value' => $other_setting->web_url_map,
                                            'msg' => "{$account_info['account_id']}橙子建站落地页不能为空"
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }

                if ($setting->promotion_card_mode == 'zidingyi' && !$setting->product_image_id && $other_setting->advanced_creative_type && $other_setting->advanced_creative_type != 'ATTACHED_CREATIVE_LIVE_CARD') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'product_image_id',
                        'value' => $setting->product_image_id,
                        'msg' => "请选择品牌形象图片",
                        'source_id' => $setting_id,
                    ];
                }

                if ($setting->delivery_range == AdModel::DELIVERY_RANGE_UNION &&
                    !empty($targeting->activate_type)
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'delivery_range',
                        'value' => $setting->delivery_range,
                        'msg' => '投放范围是穿山甲不支持新用户首次激活时间(activate_type)'
                    ];
                }

                if ($setting->delivery_range == AdModel::DELIVERY_RANGE_UNION && !empty($setting->converted_time_duration)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'delivery_range',
                        'value' => $setting->delivery_range,
                        'msg' => '投放范围是穿山甲不支持转化时间过滤'
                    ];
                }
            }

            if (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_LIVE, ToutiaoEnum::PROMOTION_TYPE_GAME])) {
                if (!in_array($this->site_config->convert_type, [1, 4])) {
                    throw new AppException('在直播间的情况下，广告配置只能选择激活或者付费的转化类型');
                }

                if ($this->site_config->deep_external_action && !in_array($this->site_config->deep_external_action, ['AD_CONVERT_TYPE_PURCHASE_ROI'])) {
                    throw new AppException('在直播间的情况下，广告配置只能选择付费ROI的深度转化类型');
                }

                if (
                    $setting->deep_bid_type &&
                    !in_array($setting->deep_bid_type, ['DEEP_BID_DEFAULT', 'ROI_PACING', 'ROI_COEFFICIENT', 'BID_PER_ACTION'])
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => "直播间的情况下，深度优化方式下不能选择ROI系数、ROI自动优化或者每次付费以外的类型"
                    ];
                }
            }

            if (
                $setting->smart_bid_type == 'SMART_BID_CONSERVATIVE' &&
                in_array($setting->deep_bid_type, [
                    'ROI_COEFFICIENT', 'ROI_COEFFICIENT_7', 'ROI_PACING'
                ])
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => "深度优化方式下不能选择ROI相关的类型"
                ];
            }

            // 针对广告配置的深度转化与参数包的深度转化是否一致的判断
            if (
                $setting->deep_bid_type &&
                !in_array($setting->deep_bid_type, [ToutiaoEnum::DEEP_BID_TYPE_DEFAULT, ToutiaoEnum::DEEP_BID_TYPE_BID_PER_ACTION])
            ) {
                if (!$this->site_config->deep_external_action) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '广告配置处没有选深度转化类型，参数包不可使用深度转化'
                    ];
                }

                if (
                    in_array(
                        $setting->deep_bid_type,
                        [ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT, ToutiaoEnum::DEEP_BID_TYPE_ROI_PACING]
                    ) && $this->site_config->deep_external_action != 'AD_CONVERT_TYPE_PURCHASE_ROI') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '广告配置处需要选择付费ROI深度转化，参数包才可以使用ROI的深度转化'
                    ];
                }

                if (
                    in_array(
                        $setting->deep_bid_type,
                        [ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT_7]
                    ) && $this->site_config->deep_external_action != 'AD_CONVERT_TYPE_PURCHASE_ROI_7D') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '广告配置处需要选择7日付费ROI深度转化，参数包才可以使用7日ROI的深度转化'
                    ];
                }
            }

            if (
                $setting->deep_bid_type == ToutiaoEnum::DEEP_BID_TYPE_BID_PER_ACTION &&
                ($this->site_config->convert_type != 4 || $this->site_config->convert_data_type != 'EVERY_ONE') &&
                $this->site_config->deep_external_action
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => '广告配置处需要选择付费的转化类型且转化统计方式是每一次，参数包才可以使用每次付费'
                ];
            }

            if ($other_setting->promotion_type == 'LIVE' || $other_setting->promotion_type == 'GAME') {
                if ($this->creative_mode === BatchAD::CREATIVE_PROGRAM_MODE && $other_setting->is_live_creative == 1) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'promotion_type',
                        'value' => 0,
                        'msg' => "投放内容是直播或者小手柄时且创意形式是直播创意不支持程序化创意"
                    ];
                }

                if ($other_setting->is_live_creative == 1 &&
                    $this->compose_config->word_num_in_ad > 1
                ) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                        'key' => 'word_num_in_ad',
                        'value' => $this->compose_config->word_num_in_ad,
                        'msg' => "创意形式：直播创意->单元文案数量不能大于1"
                    ];
                }
                if (!empty($this->material_list->all_video_list)) {
                    foreach ($this->material_list->all_video_list as $video_list_value) {
                        if ($video_list_value['width'] > $video_list_value['height']) {
                            if ($video_list_value['width'] != 1280 || $video_list_value['height'] != 720) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                    'key' => 'video_list',
                                    'value' => $video_list_value['id'],
                                    'msg' => '竖屏高必须是720*1280像素',
                                ];
                                break;
                            }
                        } else {
                            if ($video_list_value['height'] != 1280 || $video_list_value['width'] != 720) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                                    'key' => 'video_vertical_list',
                                    'value' => $video_list_value['id'],
                                    'msg' => '横屏高必须是1280*720像素',
                                ];
                                break;
                            }
                        }
                    }
                }
            }

            if ($this->site_config->plat_id == PlatId::DY_MINI) {
                if ($setting->delivery_range == ToutiaoEnum::DELIVERY_RANGE_UNION) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'delivery_range',
                        'value' => $setting->delivery_range,
                        'msg' => '投放抖音小游戏，投放范围不支持选择穿山甲'
                    ];
                }
            }

            if (in_array($this->platform, [Platform::TW]) && $other_setting->origin_ad_type == 'origin') {
                //把抖音号转化成小写,原因： ods_aweme_account_name_log表入库时，live_user全部是小写，好做统一匹配
                $live_user = [];
                foreach ($other_setting->ies_map as $key => $value){
                    $live_user[$key] = strtolower($value);
                }
                $check_aweme_id = (new OdsAwemeAccountNameLogModel())
                    ->getListByGroupLiveUser($this->platform, MediaType::TOUTIAO, 2, array_values($live_user))
                    ->toArray();
                if (empty($check_aweme_id)){
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'ies_map',
                        'value' => $other_setting->ies_map,
                        'msg' => "所有账号列表下的抖音主播授权未授权或者已失效，请媒体后台核对正确后重新授权，拉取填写"
                    ];
                }else{
                    if (count($live_user) != count($check_aweme_id)){
                        $select_live_user_data = array_column($check_aweme_id, 'live_user');
                        //获取差异
                        $diff_live_user = array_diff($live_user, $select_live_user_data);
                        // key值转换
                        $aweme_id_switch_account_id = array_flip($live_user);
                        foreach ($diff_live_user as $value) {
                            $error_msg_list[] = [
                                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                'key' => 'ies_map',
                                'value' => $other_setting->ies_map,
                                'msg' => "{$aweme_id_switch_account_id[$value]}还未选择对应的主播或者该主播授权已过期，请重新授权"
                            ];
                        }
                    }
                }
            }

            foreach ($this->account_list->account_list as $account_info) {
                if ($other_setting->promotion_type == 'LIVE' || $other_setting->promotion_type == 'GAME' || $other_setting->origin_ad_type == 'origin') {
                    if (!isset($other_setting->ies_map[$account_info['account_id']]) || !$other_setting->ies_map[$account_info['account_id']]) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'ies_map',
                            'value' => $other_setting->ies_map,
                            'msg' => "{$account_info['account_id']}还未选择对应的主播或者该主播授权已过期，请重新授权"
                        ];
                    }
                }

                if ($other_setting->is_union_component == 1) {
                    if (!isset($other_setting->union_component_map[$account_info['account_id']]) || !$other_setting->union_component_map[$account_info['account_id']]) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'union_component_map',
                            'value' => $other_setting->union_component_map,
                            'msg' => "{$account_info['account_id']}还未选择对应的穿山甲轻互动组件"
                        ];
                    }
                }
            }

            if ($setting->promotion_card_mode == 'zidingyi' && $setting->delivery_range !== 'UNIVERSAL' &&
                (
                !in_array('INVENTORY_AWEME_FEED', $setting->inventory_type)
                )
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'inventory_type',
                    'value' => $setting->inventory_type,
                    'msg' => '媒体指定位置无抖音信息流,不可使用推广卡片'
                ];
            }

            if (
                $other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_FORM' ||
                $other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE'
            ) {
                if ($this->site_config->game_type == '安卓') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'advanced_creative_type',
                        'value' => $other_setting->advanced_creative_type,
                        'msg' => '安卓的游戏不允许游戏预约'
                    ];
                }

                if ($this->site_config->deep_external_action != '' || $setting->deep_bid_type != 'DEEP_BID_DEFAULT') {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '游戏预约不允许使用深度转化'
                    ];
                }
            }

            if ($this->site_config->convert_source_type == ConvertSourceType::H5_API) {
                if (!in_array($other_setting->promotion_type, ['LINK', 'LIVE', 'GAME', 'HOT'])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'promotion_type',
                        'value' => $other_setting->promotion_type,
                        'msg' => "H5_API的转化方式只能是链接、直播、小手柄、加热"
                    ];
                }

                if (in_array($this->media_agent_type, [0, 1]) && $other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                    if (!$other_setting->external_url && $other_setting->download_url_type == 0) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'source_id' => $setting_id,
                            'key' => 'external_url',
                            'value' => $other_setting->external_url,
                            'msg' => "落地页不能为空"
                        ];
                    }

                    if ($other_setting->external_url_map && $other_setting->download_url_type == 1) {
                        foreach ($this->account_list->account_list as $account_info) {
                            if (!isset($other_setting->external_url_map[$account_info['account_id']]) || !$other_setting->external_url_map[$account_info['account_id']]) {
                                $error_msg_list[] = [
                                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                                    'key' => 'external_url_map',
                                    'value' => $other_setting->external_url_map,
                                    'msg' => "{$account_info['account_id']}落地页不能为空"
                                ];
                            }
                        }

                    }
                }

                if ($this->site_config->game_pack) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'promotion_type',
                        'value' => $other_setting->promotion_type,
                        'msg' => "投放内容是链接时，广告配置只能是不打包的状态"
                    ];
                }
            }

            if ($this->site_config->game_type === 'IOS' && (int)$setting->hide_if_exists === 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'hide_if_exists',
                    'value' => $setting->hide_if_exists,
                    'msg' => "IOS应用不支持过滤已安装"
                ];
            }

            if ($this->site_config->convert_toolkit !== ConvertToolkit::TOUTIAO_ASSET) {
                if ($setting->deep_bid_type == 'BID_PER_ACTION' && ($this->site_config->convert_data_type != 'EVERY_ONE' || $this->site_config->convert_type != 4)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'deep_bid_type',
                        'value' => $setting->deep_bid_type,
                        'msg' => '深度优化方式(deep_bid_type)选择每次付费出价，转化目标的统计方式必须选择每一次，且转化目标是激活且付费'
                    ];
                }
            }

            if ($this->site_config->convert_type == 4 && $this->site_config->convert_data_type == 'EVERY_ONE' && $setting->deep_bid_type != 'BID_PER_ACTION') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_bid_type',
                    'value' => $setting->deep_bid_type,
                    'msg' => '上报配置的统计方式选择每一次时，深度优化方式(deep_bid_type)必须选择 每次付费出价'
                ];
            }

            //校验roi的情况
            if (!$this->compose_config->ignore_warning && in_array($this->site_config->deep_external_action, [ToutiaoEnum::AD_CONVERT_TYPE_PURCHASE_ROI, ToutiaoEnum::AD_CONVERT_TYPE_PURCHASE_ROI_7D])) {
                $roi_standard_value = (new V2DimGameStandardValueDaysIncolumnModel())->getRoiStandardValue([
                    'platform' => $this->platform,
                    'game_id' => $this->site_config->game_id
                ]);

                //校验首日roi
                if ($this->site_config->deep_external_action == ToutiaoEnum::AD_CONVERT_TYPE_PURCHASE_ROI) {
                    $judge_roi_result = (new ADServingLogic())->judgeRoiValue($setting->roi_goal, $roi_standard_value, 1);
                    $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                        $ele['source_id'] = $setting_id;
                        return $ele;
                    }, $judge_roi_result);
                    $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
                }

                //校验7天roi
                if ($this->site_config->deep_external_action == ToutiaoEnum::AD_CONVERT_TYPE_PURCHASE_ROI_7D) {
                    $judge_roi_result = (new ADServingLogic())->judgeRoiValue($setting->roi_goal, $roi_standard_value, 7);
                    $judge_roi_result = array_map(function ($ele) use ($setting_id) {
                        $ele['source_id'] = $setting_id;
                        return $ele;
                    }, $judge_roi_result);
                    $error_msg_list = array_merge($error_msg_list, $judge_roi_result);
                }
            }

        }

        if (
            //todo 自定义类型的时候 并且  (应用APP + 原生  or  原生加热)
            $this->creative_mode == BatchAD::CREATIVE_CUSTOM_MODE &&
            (
                // 应用APP + 原生  or  原生加热
                ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_APP && $other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN)
                || $other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT
            )
        ) {
            // 手动选择进来
            if ($other_setting->anchor_related_type == ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                foreach ($this->account_list->account_list as $account_info) {
                    if (!isset($other_setting->anchor_related_map[$account_info['account_id']]) || !$other_setting->anchor_related_map[$account_info['account_id']]) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'anchor_related_map',
                            'value' => $other_setting->anchor_related_map,
                            'msg' => "{$account_info['account_id']}原生锚点选择不能为空"
                        ];
                    }
                }
            }
        }

//        if ($this->site_config->convert_toolkit == ConvertToolkit::TOUTIAO_ASSET) {
//            if ($other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
//                if ($other_setting->download_url_type == 0) {
//                    if (!$other_setting->web_url) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                            'key' => 'web_url',
//                            'value' => $other_setting->web_url,
//                            'msg' => '事件管理的情况下，Android应用下载页(web_url)不能为空'
//                        ];
//                    }
//                }
//                if ($other_setting->download_url_type == 1) {
//                    foreach ($this->account_list->account_list as $account_info) {
//                        if (!isset($other_setting->web_url_map[$account_info['account_id']]) || !$other_setting->web_url_map[$account_info['account_id']]) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                                'key' => 'web_url_map',
//                                'value' => $other_setting->web_url_map,
//                                'msg' => "事件管理的情况下，{$account_info['account_id']}的Android应用下载页(web_url)不能为空"
//                            ];
//                        }
//                    }
//                }
//            } elseif ($other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
//                if ($other_setting->download_url_type == 0) {
//                    if (!$other_setting->external_url) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                            'key' => 'external_url',
//                            'value' => $other_setting->external_url,
//                            'msg' => '事件管理的情况下，落地页地址(external_url)不能为空'
//                        ];
//                    }
//                }
//                if ($other_setting->download_url_type == 1) {
//                    foreach ($this->account_list->account_list as $account_info) {
//                        if (!isset($other_setting->external_url_map[$account_info['account_id']]) || !$other_setting->external_url_map[$account_info['account_id']]) {
//                            $error_msg_list[] = [
//                                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                                'key' => 'external_url_map',
//                                'value' => $other_setting->external_url_map,
//                                'msg' => "事件管理的情况下，{$account_info['account_id']}的落地页地址(external_url)不能为空"
//                            ];
//                        }
//                    }
//                }
//            }
//        }

        if ($this->site_config->plat_id == PlatId::DY_MINI) {
            if ($other_setting->promotion_type != ToutiaoEnum::PROMOTION_TYPE_APP) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'promotion_type',
                    'value' => $other_setting->promotion_type,
                    'msg' => "抖音小游戏只能选择应用投放内容"
                ];
            }
        }

        if ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT) {
            $no_material_account_id_list = [];
            foreach ($this->account_list->account_list as $account_info) {
                if (
                    !isset($other_setting->hot_material_map[$account_info['account_id']]) ||
                    !is_array($other_setting->hot_material_map[$account_info['account_id']]) ||
                    count($other_setting->hot_material_map[$account_info['account_id']] ?? []) <= 0
                ) {
                    $no_material_account_id_list[] = $account_info['account_id'];
                }
            }
            if ($no_material_account_id_list) {
                $error_account_id_list = implode(',', $no_material_account_id_list);
                $error_account_id_list = trim($error_account_id_list, ',');
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'hot_material_map',
                    'value' => $other_setting->hot_material_map,
                    'msg' => "需要选择账号{$error_account_id_list}的原生加热素材"
                ];
            }

            if ($this->creative_mode != BatchAD::CREATIVE_CUSTOM_MODE) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'promotion_type',
                    'value' => $other_setting->promotion_type,
                    'msg' => "原生加热素材的情况下只能自定义创意"
                ];
            }

            if ($this->compose_config->video_num_in_ad != 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'video_num_in_ad',
                    'value' => $this->compose_config->video_num_in_ad,
                    'msg' => "原生加热素材只能每广告二级一个视频"
                ];
            }

            if ($this->compose_config->word_num_in_ad != 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => "原生加热素材只能每广告二级一个文案"
                ];
            }

            if ($this->compose_config->pic_num_in_ad != 0) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'pic_num_in_ad',
                    'value' => $this->compose_config->pic_num_in_ad,
                    'msg' => "原生加热素材不能选择广告二级中有图片"
                ];
            }

            if ($this->material_list->getVideoNum() != 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => '',
                    'msg' => "在原生加热素材时不能选择超过1部视频"
                ];
            }

            if ($this->material_list->getImageNum() > 0) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                    'value' => '',
                    'msg' => "在原生加热素材时不能选择图片"
                ];
            }

            if ($this->word_list->getCount() > 1) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'promotion_type',
                    'value' => $other_setting->promotion_type,
                    'msg' => "在原生加热素材时不能选择超过1条文案"
                ];
            }
        }


        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && $this->compose_config->video_num_in_ad > 10) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => "程序化创意视频素材最多支持10组"
            ];
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && $this->compose_config->pic_num_in_ad > 12) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => "程序化创意图片素材最多支持12组"
            ];
        }

        if (
            $this->compose_config->word_material_compose_mode == 0 &&
            $this->creative_mode == BatchAd::CREATIVE_CUSTOM_MODE &&
            (
                $this->compose_config->word_num_in_ad * $this->compose_config->pic_num_in_ad +
                $this->compose_config->word_num_in_ad * $this->compose_config->video_num_in_ad
            ) > 10
        ) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'pic_num_in_ad',
                'value' => $this->compose_config->pic_num_in_ad,
                'msg' => "自定义创意素材最多支持10个，此次叉乘结果大于10个"
            ];
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => "自定义创意素材最多支持10个，此次叉乘结果大于10个"
            ];
        }

        if (
            $this->compose_config->word_material_compose_mode == 1 &&
            $this->creative_mode == BatchAD::CREATIVE_CUSTOM_MODE
        ) {
            if (
                $this->compose_config->word_num_in_ad > $this->compose_config->video_num_in_ad &&
                $this->compose_config->word_num_in_ad > 10
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'word_num_in_ad',
                    'value' => $this->compose_config->word_num_in_ad,
                    'msg' => "自定义创意素材最多支持10个，此次叉乘结果大于10个"
                ];
            }

            if (
                $this->compose_config->word_num_in_ad < $this->compose_config->video_num_in_ad &&
                $this->compose_config->video_num_in_ad > 10
            ) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'video_num_in_ad',
                    'value' => $this->compose_config->video_num_in_ad,
                    'msg' => "自定义创意素材最多支持10个，此次叉乘结果大于10个"
                ];
            }
        }


        return $error_msg_list;
    }

}
