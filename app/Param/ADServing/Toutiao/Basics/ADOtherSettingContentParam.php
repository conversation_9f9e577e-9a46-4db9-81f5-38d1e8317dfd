<?php
/**
 * Created by Php<PERSON>torm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Toutiao\Basics;

use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Tools\SiteModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoPlayableLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoSitePageLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\Toutiao\ADOtherSettingContentParam as ToutiaoADOtherSettingContentParam;
use App\Utils\Helpers;

class ADOtherSettingContentParam extends ToutiaoADOtherSettingContentParam
{
    /**
     * 抖音号
     * @var object
     */
    public $ies_map;

    public $os = ['ANDROID', 'IOS'];
    public $hot_material_map = [];
    public $auto_inherit_switch = 'OFF';
    public $inherit_type = 'INHERIT_FROM_ACCOUNT';
    public $inherited_advertiser_id = [];
    public $is_cpa = false;
    public $ad_pop_zk = 0;

    public $game_package_desc = '';

    public $game_package_batch_id = '';

    public $game_package_thumbnails = ['', ''];

    public $advanced_creative_type = '';

    public $commerce_image_id = '';

    public $is_live_creative = 1;

    public $is_feed_and_fav_see = 1;

    public $commerce_source = '';

    public $commerce_title = '';

    public $marketing_scene = 'GAME_SUBSCRIBE';

    public $app_desc = '';

    public $app_introduction = '';

    public $app_thumbnails = ['', '', ''];

    public $download_url_type = 0;

    public $web_url_map = [];

    public $external_url_map = [];

    public $is_union_component = 0;

    public $union_component_map = [];

    public $origin_ad_type = 'default';

    /**
     * 原生锚点
     * @var string
     */
    public $anchor_related_type = 'OFF';
    public $anchor_related_map = [];

    public function paramHook()
    {
        parent::paramHook();

        $this->ies_map = $this->tranObject($this->ies_map);

        foreach ($this->ies_map as &$ies_info) {
            if ($ies_info && is_string($ies_info)) {
                $ies_info = trim($ies_info);
            }
        }

        $this->hot_material_map = $this->tranObject($this->hot_material_map);

        $this->web_url_map = $this->tranObject($this->web_url_map);

        $this->external_url_map = $this->tranObject($this->external_url_map);

        $this->union_component_map = $this->tranObject($this->union_component_map);

        $this->anchor_related_map = $this->tranObject($this->anchor_related_map);
    }

    public function validate()
    {
        $error_msg_list = parent::validate();

        if (
            $this->auto_inherit_switch == 'ON' &&
            $this->inherit_type == ToutiaoEnum::AUTO_INHERIT_SWITCH_CUSTOMER &&
            !(
                count($this->inherited_advertiser_id) >= 1 &&
                count($this->inherited_advertiser_id) <= 3
            )
        ) {
            $error_msg_list[] = [
                'key' => 'inherited_advertiser_id',
                'value' => $this->inherited_advertiser_id,
                'msg' => "同公司下的其他账户只能1-3个"
            ];
        }

        if ($this->promotion_type == 'LIVE' || $this->promotion_type == 'GAME') {
            if ($this->advanced_creative_type !== 'ATTACHED_CREATIVE_LIVE_CARD') {
                $error_msg_list[] = [
                    'key' => 'advanced_creative_type',
                    'value' => 0,
                    'msg' => "直播间附加创意需要选择直播卡片"
                ];
            }
        }

        if (($this->promotion_type == 'LIVE' || $this->promotion_type == 'GAME') && $this->advanced_creative_type === 'ATTACHED_CREATIVE_LIVE_CARD') {
            if (!$this->commerce_image_id) {
                $error_msg_list[] = [
                    'key' => 'commerce_image_id',
                    'value' => 0,
                    'msg' => "未选择品牌形象参数"
                ];
            }
            if (!$this->commerce_source) {
                $error_msg_list[] = [
                    'key' => 'commerce_source',
                    'value' => 0,
                    'msg' => "未填写产品名称"
                ];
            }
            if (!$this->commerce_title) {
                $error_msg_list[] = [
                    'key' => 'commerce_title',
                    'value' => '',
                    'msg' => "未填写产品描述"
                ];
            }

            if (mb_strlen($this->commerce_title) > 7) {
                $error_msg_list[] = [
                    'key' => 'commerce_title',
                    'value' => '',
                    'msg' => "产品卖点长度不得超过7"
                ];
            }
            if (mb_strlen($this->commerce_source) > 9) {
                $error_msg_list[] = [
                    'key' => 'commerce_source',
                    'value' => 0,
                    'msg' => "产品名称长度不得超过9"
                ];
            }
        }

        if ($this->download_type == 'DOWNLOAD_URL') {
            if (!$this->app_name) {
                $error_msg_list[] = [
                    'key' => 'app_name',
                    'value' => $this->app_name,
                    'msg' => '应用名称不能为空'
                ];
            }
        }

        if ($this->promotion_type == 'LINK') {
            if ($this->is_playable) {
                $error_msg_list[] = [
                    'key' => 'is_playable',
                    'value' => $this->is_playable,
                    'msg' => "链接不能使用试玩"
                ];
            }
            if ($this->advanced_creative_type) {
                $error_msg_list[] = [
                    'key' => 'advanced_creative_type',
                    'value' => $this->advanced_creative_type,
                    'msg' => "链接不能创意附加类型"
                ];
            }
        }

        if (
            $this->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_FORM' ||
            $this->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE'
        ) {

            if ($this->download_type != 'EXTERNAL_URL') {
                $error_msg_list[] = [
                    'key' => 'download_type',
                    'value' => $this->download_type,
                    'msg' => '游戏预约的情景下只能选择落地页链接模式'
                ];
            }

            if (!$this->app_thumbnails) {
                $error_msg_list[] = [
                    'key' => 'app_thumbnails',
                    'value' => $this->app_thumbnails,
                    'msg' => '请选择应用图片(app_thumbnails)'
                ];
            } else {
                if (!$this->app_thumbnails[0]) {
                    $error_msg_list[] = [
                        'key' => 'app_thumbnails',
                        'value' => $this->app_thumbnails,
                        'msg' => '请选择应用图片1(app_thumbnails)'
                    ];
                }
                if (!$this->app_thumbnails[1]) {
                    $error_msg_list[] = [
                        'key' => 'app_thumbnails',
                        'value' => $this->app_thumbnails,
                        'msg' => '请选择应用图片2(app_thumbnails)'
                    ];
                }

                if ($this->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE') {
                    if (!$this->app_thumbnails[2]) {
                        $error_msg_list[] = [
                            'key' => 'app_thumbnails',
                            'value' => $this->app_thumbnails,
                            'msg' => '请选择应用图片3(app_thumbnails)'
                        ];
                    }
                }
            }

            if (!$this->app_introduction) {
                $error_msg_list[] = [
                    'key' => 'app_introduction',
                    'value' => $this->app_introduction,
                    'msg' => '请填写应用介绍(app_introduction)'
                ];
            } else {
                if (Helpers::ADServingStrLen($this->app_introduction) > 40) {
                    $error_msg_list[] = [
                        'key' => 'app_introduction',
                        'value' => $this->app_introduction,
                        'msg' => "应用介绍(app_introduction):{$this->app_introduction}已经大于20个字"
                    ];
                }
            }

            if (!$this->app_desc) {
                $error_msg_list[] = [
                    'key' => 'app_desc',
                    'value' => $this->app_desc,
                    'msg' => '请填写应用描述(app_desc)'
                ];
            } else {
                if (mb_strlen($this->app_desc) > 15) {
                    $error_msg_list[] = [
                        'key' => 'app_desc',
                        'value' => $this->app_desc,
                        'msg' => "应用描述(app_desc):{$this->app_desc}已经大于15字"
                    ];
                }
            }

            if ($this->download_url_type == 1) {
                $error_msg_list[] = [
                    'key' => 'download_url_type',
                    'value' => $this->download_url_type,
                    'msg' => '预约落地页需要使用共享-落地页的形式'
                ];
            }

            if (!$this->external_url) {
                $error_msg_list[] = [
                    'key' => 'external_url',
                    'value' => $this->external_url,
                    'msg' => '请填落地页链接不能为空(external_url)'
                ];
            } else {
                if ($this->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_FORM') {
                    $url = $this->getPageExternalUrlMapInfo();
                    $id_array = explode('/', $url);
                    $id_array = array_values(array_filter($id_array));
                    if ($id_array) {
                        $url_id = array_pop($id_array);
                        $site_page_result = (new OdsToutiaoSitePageLogModel())->getDataById($url_id);
                        if (!$site_page_result) {
                            throw new AppException('找不到游戏预约的落地页的信息');
                        } else {
                            $account_info = (new MediaAccountModel())->getDataByAccountId($site_page_result->account_id);
                            if ($account_info) {
                                $data = (new SiteModel())->getFormInfo(
                                    $account_info->account_id,
                                    $account_info->access_token,
                                    $site_page_result->site_page_id
                                );
                                if (!isset($data['list'][0]['form_id']) || !isset($data['list'][0]['form_index'])) {
                                    throw new AppException('找不到游戏预约的落地页的表单信息');
                                }
                            } else {
                                throw new AppException('找不到游戏预约的落地页的账号信息');
                            }
                        }
                    } else {
                        $error_msg_list[] = [
                            'key' => 'external_url',
                            'value' => $this->external_url,
                            'msg' => '找不到游戏预约的落地页的id'
                        ];
                    }
                }
            }
        }

        if ($this->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_PACKAGE') {
            if (!$this->game_package_desc) {
                $error_msg_list[] = [
                    'key' => 'game_package_desc',
                    'value' => $this->game_package_desc,
                    'msg' => '请填写礼包描述'
                ];
            } else {
                if (mb_strlen($this->game_package_desc) > 15) {
                    $error_msg_list[] = [
                        'key' => 'game_package_desc',
                        'value' => $this->game_package_desc,
                        'msg' => '礼包描述不能大于15字'
                    ];
                }
            }
            if (!$this->game_package_batch_id) {
                $error_msg_list[] = [
                    'key' => 'game_package_batch_id',
                    'value' => $this->game_package_batch_id,
                    'msg' => '请选择游戏礼包码id'
                ];
            }
            if (!$this->game_package_thumbnails[0]) {
                $error_msg_list[] = [
                    'key' => 'game_package_thumbnails',
                    'value' => $this->game_package_thumbnails,
                    'msg' => '请选择游戏礼包码应用图片集1'
                ];
            }
            if (!$this->game_package_thumbnails[1]) {
                $error_msg_list[] = [
                    'key' => 'game_package_thumbnails',
                    'value' => $this->game_package_thumbnails,
                    'msg' => '请选择游戏礼包码应用图片集2'
                ];
            }
        }


        return $error_msg_list;
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array|mixed
     */
    public function getPageWebUrlMapInfo($account_id)
    {
        if ($this->download_url_type == 1) {
            if ($web_url_map = $this->web_url_map[$account_id] ?? '') {
                return trim($web_url_map);
            }
        }
        return trim($this->web_url) ?? '';
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return string
     */
    public function getPageExternalUrlMapInfo($account_id = '')
    {
        if ($this->download_url_type == 1) {
            if ($external_url_map = $this->external_url_map[$account_id] ?? '') {
                $external_url_map = str_replace('exp/orange', 'page', $external_url_map);
                return preg_replace("/\xE2\x80\x8B/", "", trim($external_url_map));
            }
        }
        return preg_replace("/\xE2\x80\x8B/", "", trim($this->external_url)) ?? '';
    }
}
