<?php

namespace App\Param\ADServing;

use App\Container;
use App\Param\AbstractParam;

class ADIntelligentComposeConditionParam extends AbstractParam
{
    public $name;

    public $condition;

    public $creator;

    public $status = 1;

    public function initBySql($object)
    {
        $this->name = $object->name;
        $this->condition = json_decode($object->condition, true);
        $this->creator = $object->creator;
        $this->status = $object->status;
        return $this;
    }

    public function toSqlData(): array
    {
        $data = parent::toArray();
        if (!$data['creator']) {
            $data['creator'] = Container::getSession()->name;
        }
        $data['condition'] = json_encode($data["condition"], JSON_UNESCAPED_UNICODE);
        return $data;
    }
}
