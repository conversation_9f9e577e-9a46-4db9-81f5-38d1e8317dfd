<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/23
 * Time: 16:58
 */

namespace App\Param\ADServing\ADComposeContent;

use App\Exception\AppException;
use App\Param\AbstractParam;

class ADComposeWordListParam extends AbstractParam
{
    /**
     * 文案列表 word_list的结构必定是["文案1","文案2"]
     * @var array
     */
    public $word_list = [];


    public function paramHook()
    {
        if (!$this->word_list) {
            throw new AppException('文案列表不能为空');
        }

        foreach ($this->word_list as &$word) {
            $word_len = mb_strlen($word);
            if ($word_len < 1) {
                throw new AppException('文案列表中的文案长度至少大于1');
            }
            $word = trim($word);
            $word = str_replace(["\n", "\r", "\t", "\v", "\0"], '', $word);
        }
    }

    /**
     * 获取文案数量
     * @return int
     */
    public function getCount()
    {
        return count($this->word_list);
    }

    /**
     * 根据$this->word_list获取一个[文案1,文案2,文案3]的结构
     * @return array
     */
    public function getWordContent()
    {
        return $this->word_list;
    }

    /**
     * 根据下标集合获取$this->word_list一个[文案1,文案2,文案3]的结构
     * @param $index_list
     * @return array
     */
    public function getWordContentByIndexList($index_list)
    {
        return array_map(function ($index) {
            return $this->word_list[$index];
        }, $index_list);
    }

    /**
     * 根据index更新word
     * @param int $index
     * @param string $word
     */
    public function updateWordByIndex(int $index, string $word)
    {
        $word_len = mb_strlen($word);
        if ($word_len < 5) {
            throw new AppException('文案列表(ADComposeWordListParam.word_list)中的文案长度需要5-30个字');
        }
        $this->word_list[$index] = ['content' => $word];
    }
}
