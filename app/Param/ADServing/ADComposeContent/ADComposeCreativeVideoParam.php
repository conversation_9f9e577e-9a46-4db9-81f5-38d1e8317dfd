<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/23
 * Time: 16:58
 */

namespace App\Param\ADServing\ADComposeContent;

use App\Exception\AppException;
use App\Param\AbstractParam;
use function GuzzleHttp\Psr7\str;

class ADComposeCreativeVideoParam extends AbstractParam
{
    /**
     * 视频id
     * @var string
     */
    public $id = '';

    /**
     * 视频url
     * @var string
     */
    public $url = '';

    /**
     * 视频的宽度
     * @var int
     */
    public $width = 0;

    /**
     * 视频的高度
     * @var int
     */
    public $height = 0;

    public function paramHook()
    {
        if (!$this->id) {
            throw new AppException('视频id(ADComposeCreativeVideoParam.id)不能为空');
        }
        if (!$this->url) {
            throw new AppException('视频url(ADComposeCreativeVideoParam.url)不能为空');
        }
        if (strpos($this->url, '.mp3') !== false) {
            return ;
        }
        if (!$this->width) {
            throw new AppException('视频宽度(ADComposeCreativeVideoParam.width)不能为空');
        }
        if (!$this->height) {
            throw new AppException('视频高度(ADComposeCreativeVideoParam.height)不能为空');
        }
    }
}