<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/23
 * Time: 16:58
 */

namespace App\Param\ADServing\ADComposeContent;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\ADTargetingPacketModel;
use App\Param\AbstractParam;
use App\Param\ADServing\ADTargetingPacketParam;
use Illuminate\Support\Collection;

class ADComposeTargetingListParam extends AbstractParam
{
    /**
     * 定向id列表
     * @var array
     */
    public $targeting_id_list = [];

    /**
     * 定向信息列表
     * @var Collection
     */
    public $targeting_info_list = [];

    /**
     * 初始化媒体的对应PrefixParam
     * @param int $media_type
     * @param string $class_name
     * @return string
     */
    public function getMediaPrefixParam(int $media_type, string $class_name)
    {
        $media_name = ucfirst(strtolower(MediaType::CONST_NAME_LIST[$media_type]));
        if ($media_name) {
            return "App\\Param\\ADServing\\{$media_name}\\$class_name";
        } else {
            throw new AppException("找不到{$media_type}的媒体");
        }
    }


    public function paramHook()
    {
//        if (!$this->targeting_list) {
//            throw new AppException('定向列表(ADComposeTargetingListParam.targeting_list)不能为空');
//        }
//
//        $this->targeting_info_list = (new ADTargetingPacketModel())->getListByIds(
////            array_column($this->targeting_list, 'id')
//            [1]
//        );
//
//        if ($this->targeting_info_list->isEmpty()) {
//            throw new AppException('匹配的定向信息列表(ADComposeTargetingListParam.targeting_info_list)为空');
//        }
    }

    /**
     * 新增Targeting
     * @param $id
     */
    public function addTargeting($id)
    {
        $this->targeting_id_list[] = $id;
    }

    /**
     * 新增Targeting_info
     * @param $info
     */
    public function setTargetingInfo(ADTargetingPacketParam $info)
    {
        $this->targeting_info_list = [$info];
        $this->targeting_id_list = [];
    }

    /**
     * 初始化定向信息
     */
    public function initTargetingInfo()
    {
        $targeting_info_list = (new ADTargetingPacketModel())->getListByIds(
            $this->targeting_id_list
        );
        $this->targeting_info_list = [];
        foreach ($targeting_info_list as $index => $targeting_info) {
            $targeting_info->targeting = is_string($targeting_info->targeting) ? json_decode($targeting_info->targeting,true) : $targeting_info->targeting;
            $this->targeting_info_list[$targeting_info->id] = $this->getADTargetingObject($targeting_info);
        }
        if (count($this->targeting_info_list) != count($this->targeting_id_list)) {
            throw new AppException('找不到全部定向包信息，请重新选择');
        }
    }

    /**
     * 获取定向包数量
     * @return int
     */
    public function getCount()
    {
        return count($this->targeting_info_list);
    }

    /**
     * 获取广告定向对象
     * @param $targeting_data
     * @return ADTargetingPacketParam
     */
    public function getADTargetingObject($targeting_data)
    {
        $object = new ADTargetingPacketParam(get_object_vars($targeting_data));
        $object->setTargetingByArray($targeting_data->targeting);
        return $object;
    }

    /**
     * 获取定向包列表
     * @return array
     */
    public function getList()
    {
        return $this->targeting_info_list;
    }


    /**
     * 获取定向包内容
     * @return array
     */
    public function getContentList()
    {
        $targeting_content_list = [];
        foreach ($this->targeting_info_list as $index => $targeting) {
            $targeting_content_list[$index] = $targeting->targeting;
        }
        return $targeting_content_list;
    }
}
