<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/23
 * Time: 16:58
 */

namespace App\Param\ADServing\ADComposeContent;

use App\Exception\AppException;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\AbstractParam;

class ADComposeCreativeImageParam extends AbstractParam
{
    /**
     * 图片id
     * @var string
     */
    public $id = '';

    /**
     * 图片url
     * @var array
     */
    public $url = [];

    /**
     * 视频的宽度
     * @var int
     */
    public $width = 0;

    /**
     * 视频的高度
     * @var int
     */
    public $height = 0;

    /**
     * 组图所需字段-组图下对应的图片
     * @var array
     */
    public $image_list = [];

    public function initImageList()
    {
        $model = new MaterialFileModel();
        $data = $model->getData($this->id);
        if ($data) {
            $list = $model->getListByMaterialId((int)$data->material_id, $data->platform);
            foreach ($list as $image_info) {
                switch ($data->file_type) {
                    case 5:
                        $num = 3;
                        break;
                    case 6:
                        $num = 4;
                        break;
                    case 7:
                        $num = 6;
                        break;
                    case 10:
                        $num = 9;
                        break;
                    default:
                        throw new AppException('只能选择组图类型素材');
                }
                if ($image_info->file_type == 1) {
                    if ($image_info->width == 800 && $image_info->height == 800 && count($this->image_list) < $num) {
                        $this->image_list[] = [
                            'id' => $image_info->id,
                            'width' => $image_info->width,
                            'height' => $image_info->height,
                            'url' => $image_info->url,
                        ];
                    }
                }
            }
        } else {
            throw new AppException('找不到对应的组图素材');
        }

        return $this;
    }

    public function paramHook()
    {
        if (!$this->id) {
            throw new AppException('图片id(ADComposeCreativeImageParam.id)不能为空');
        }
        if (!$this->url) {
            throw new AppException('图片url(ADComposeCreativeImageParam.url)不能为空');
        }
        if (!$this->width) {
            throw new AppException('图片宽度(ADComposeCreativeImageParam.width)不能为空');
        }
        if (!$this->height) {
            throw new AppException('图片高度(ADComposeCreativeImageParam.height)不能为空');
        }
    }
}
