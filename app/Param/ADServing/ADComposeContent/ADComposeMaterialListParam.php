<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/03/23
 * Time: 16:58
 */

namespace App\Param\ADServing\ADComposeContent;

use App\Exception\AppException;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\AbstractParam;

class ADComposeMaterialListParam extends AbstractParam
{
    /**
     * @var array $group_image_list 组图
     */
    public $group_image_list = [];

    /**
     * @var array $group_video_list 组视频
     */
    public $group_video_list = [];

    /**
     * 小图列表
     * @var array
     */
    public $small_image_list = [];

    /**
     * 横版大图列表
     * @var array
     */
    public $large_image_list = [];

    /**
     * 竖版大图列表
     * @var array
     */
    public $large_vertical_image_list = [];

    /**
     * 音频列表
     * @var array
     */
    public $audio_list = [];

    /**
     * 横版视频列表
     * @var array
     */
    public $video_list = [];

    /**
     * 竖版视频列表
     * @var array
     */
    public $video_vertical_list = [];

    public $vertical_cover_list_map = [];

    public $cover_list_map = [];

    /**
     * 所有图片的列表
     * @var array $all_image_list
     */
    public $all_image_list = [];

    /**
     * 所有视频的列表
     * @var array
     */
    public $all_video_list = [];


    public function paramHook()
    {
        $this->group_image_list = $this->getGroupImageInfo($this->group_image_list);
        $this->all_image_list = array_merge($this->all_image_list, $this->small_image_list);
        $this->all_image_list = array_merge($this->all_image_list, $this->large_image_list);
        $this->all_image_list = array_merge($this->all_image_list, $this->large_vertical_image_list);
        $this->all_image_list = array_merge($this->all_image_list, $this->group_image_list);

        foreach ($this->audio_list as &$audio) {
            $audio['cover_list'] = [[
                'id' => -1,
                'url' => -1,
                'width' => -1,
                'height' => -1
            ]];
        }


        $this->video_list = array_map(function ($video_data) {
            $video_data['cover_list'] = array_map(function ($cover_info) {
                return json_decode($cover_info, true);
            }, $video_data['cover_list'] ?: (
            isset($this->cover_list_map[$video_data['id']][0]) ? [$this->cover_list_map[$video_data['id']][0]] : []
            ));
            return $video_data;
        }, $this->video_list);

        $this->video_vertical_list = array_map(function ($video_data) {
            $video_data['cover_list'] = array_map(function ($cover_info) {
                return json_decode($cover_info, true);
            }, $video_data['cover_list'] ?: (
            isset($this->vertical_cover_list_map[$video_data['id']][0]) ? [$this->vertical_cover_list_map[$video_data['id']][0]] : []
            ));
            return $video_data;
        }, $this->video_vertical_list);

        $this->group_video_list = $this->getGroupVideoInfo($this->group_video_list);
        $this->all_video_list = array_merge($this->all_video_list, $this->video_list);
        $this->all_video_list = array_merge($this->all_video_list, $this->video_vertical_list);
        $this->all_video_list = array_merge($this->all_video_list, $this->group_video_list);

        $this->all_video_list = $this->getVideoAllInfo($this->all_video_list);
        $this->all_video_list = array_merge($this->all_video_list, $this->audio_list);

        shuffle($this->all_video_list);
    }

    /**
     * 生成组图结构
     * @param array $group_image_list
     * @return mixed
     */
    public function getGroupImageInfo(array $group_image_list)
    {
        $model = new MaterialFileModel();
        foreach ($group_image_list as $kes => &$group_image_info) {
            $list = $model->getListByMaterialId((int)$group_image_info['material_id'], $group_image_info['platform']);
            foreach ($list as $image_info) {
                if ($image_info->file_type == 1) {
                    $group_image_info['image_list'][] = [
                        'id' => $image_info->id,
                        'width' => $image_info->width,
                        'height' => $image_info->height,
                        'url' => $image_info->url,
                    ];
                }
            }
        }
        return $group_image_list;
    }

    /**
     * 生成组视频结构
     * @param array $group_video_list
     * @return array|mixed
     */
    public function getGroupVideoInfo($group_video_list = [])
    {
        $model = new MaterialFileModel();
        foreach ($group_video_list as $group_video_info) {
            if (is_array($group_video_info)) {
                $list = $model->getListByMaterialId((int)$group_video_info['material_id'], $group_video_info['platform']);
                $group_video_list = [];
                foreach ($list as $video_info) {
                    if ($video_info->file_type != 2) {
                        continue;
                    }
                    $group_video_list[] = (array)$video_info;
                }
            }
        }
        return $group_video_list;
    }

    /**
     * 获取封面以及其他信息
     * @param array $video_list
     * @return array
     */
    private function getVideoAllInfo(array $video_list)
    {
        $need_get_info_name_map = [];
        $need_get_info_name_list = [];

        $video_judge_available_id_list = [];
        foreach ($video_list as $video_key => $video_info) {
            if (isset($video_info['cover_list'])) {
                foreach ($video_info['cover_list'] as $cover_key => $cover_info) {
                    $name_array = explode('/', $cover_info['url']);
                    $name = array_pop($name_array);
                    $need_get_info_name_list[] = $name;
                    $need_get_info_name_map[$video_info['id']][$name] = $name;
                }
            }
            $video_judge_available_id_list[] = $video_info['id'];
            $need_get_info_name_list[] = $video_info['filename'];
        }

        if ($need_get_info_name_list) {
            $material_file_info_list = (new MaterialFileModel())->getListByNameList($need_get_info_name_list)->keyBy('filename');
            foreach ($video_list as $video_key => &$video_info) {
                if (isset($video_info['cover_list'])) {
                    foreach ($video_info['cover_list'] as $cover_key => &$cover_info) {
                        $name_array = explode('/', $cover_info['url']);
                        $name = array_pop($name_array);
                        if (isset($material_file_info_list[$need_get_info_name_map[$video_info['id']][$name]])) {
                            $cover_info['id'] = $material_file_info_list[$need_get_info_name_map[$video_info['id']][$name]]->id;
                            $cover_info['width'] = $material_file_info_list[$need_get_info_name_map[$video_info['id']][$name]]->width;
                            $cover_info['height'] = $material_file_info_list[$need_get_info_name_map[$video_info['id']][$name]]->height;
                        } else {
                            throw new AppException("找不到视频id:{$video_info['id']}的素材封面文件信息");
                        }
                    }
                }
                if (isset($material_file_info_list[$video_info['filename']])) {
                    $video_info['duration'] = $material_file_info_list[$video_info['filename']]->duration;
                    $video_info['bitrate'] = $material_file_info_list[$video_info['filename']]->bitrate;
                    $video_info['size'] = $material_file_info_list[$video_info['filename']]->size;
                } else {
                    throw new AppException("找不到{$video_info['filename']}的素材文件信息");
                }
            }
        }
        return $video_list;
    }

    /**
     *  返回所有视频的视频乘其封面的数量
     * @return int
     */
    public function getAllVideoAndCoverMultiplicationNum()
    {
        $num = 0;
        foreach ($this->video_list as $key => $value) {
            $num += 1 * count($value['cover_list']);
        }
        foreach ($this->video_vertical_list as $key => $value) {
            $num += 1 * count($value['cover_list']);
        }
        return $num;
    }

    /**
     *  返回横版视频的视频乘其封面的数量
     * @return int
     */
    public function getVideoAndCoverMultiplicationNum()
    {
        $num = 0;
        foreach ($this->video_list as $key => $value) {
            $num += 1 * count($value['cover_list']);
        }
        return $num;
    }

    /**
     *  返回横版视频的视频乘其封面的数量
     * @return int
     */
    public function getVerticalVideoAndCoverMultiplicationNum()
    {
        $num = 0;
        foreach ($this->video_vertical_list as $key => $value) {
            $num += 1 * count($value['cover_list']);
        }
        return $num;
    }

    /**
     * 返回图片数量
     * @return int
     */
    public function getImageNum()
    {
        return count($this->all_image_list);
    }

    /**
     * 返回视频数量
     * @return int
     */
    public function getVideoNum()
    {
        return count($this->all_video_list);
    }

    /**
     * 返回所有素材
     */
    public function getAllMaterialList()
    {
        return array_merge($this->all_image_list, $this->video_list, $this->video_vertical_list, $this->group_video_list);
    }
}
