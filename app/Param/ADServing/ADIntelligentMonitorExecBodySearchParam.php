<?php

namespace App\Param\ADServing;

use App\Param\AbstractParam;

class ADIntelligentMonitorExecBodySearchParam extends AbstractParam
{
    public $id;

    public $not_in_id_list;

    public $media_type;

    public $media_agent_type;

    public $name = '';

    public $creator = '';
    public $exec_target_dim = '';

    public $page = 1;

    public $rows = 20;

    public function paramHook()
    {
        if ($this->id && strpos($this->id, ',') !== false) {
            $this->id = explode(',', $this->id);
        }
    }
}
