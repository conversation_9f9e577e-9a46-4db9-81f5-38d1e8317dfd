<?php
/**
 * 广告组合包
 * User: Lin
 * Date: 2021/12/27
 * Time: 10:19
 */

namespace App\Param\ADServing;

use App\Constant\BatchAD;
use App\Constant\BatchADClassMap;
use App\Constant\MediaType;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\PermissionLogic;
use App\Model\SqlModel\DataMedia\OdsGamePackLogModel;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\AbstractParam;
use App\Service\UserService;
use Common\EnvConfig;

class ADComposePacketParam extends AbstractParam
{
    public $id = 0;
    public $name = '';
    public $dispatch_type = 'all';
    public $compose_type = '';
    public $media_type = '';
    public $media_agent_type = '';
    public $targeting_compose_type = '';
    public $setting_compose_type = '';
    public $ad1_create_type = '';
    public $creative_mode = '';
    public $is_wait_package = '';
    public $company = '';
    public $platform = '';
    public $account_list = [];
    public $calc_rule_list = [];
    public $compose_config = [];
    public $group_image_list = [];
    public $small_image_list = [];
    public $large_image_list = [];
    public $large_vertical_image_list = [];
    public $video_list = [];
    public $video_vertical_list = [];
    public $audio_list = [];
    public $cover_list_map = [];
    public $vertical_cover_list_map = [];
    public $rta_list = [];
    public $setting = [];
    public $setting_list = [];
    public $site_config = [];
    /**
     * @var AbstractADTargetingContentParam $targeting
     */
    public $targeting = [];
    public $targeting_list = [];
    public $word_list = [];
    public $other_setting = [];
    public $create_time = '';
    public $update_time = '';
    public $creator = '';
    public $creator_id = 0;
    public $state = 1;

    public function paramHook()
    {
        if (!$this->creator_id) {
            $this->creator_id = Container::getSession()->user_id;
        }
        if (!$this->creator) {
            $this->creator = Container::getSession()->name;
        }
        if (!$this->create_time) {
            $this->create_time = date('Y-m-d H:i:s');
        }

        $this->formatComposeCreativeMode();
        if ($this->media_type > 0) {
            $this->formatTargeting();
        }
    }

    /**
     * 获取所有素材的id映射map内容 （暂时没有组素材）
     * @return array
     */
    private function getAllMaterialFileKeyByID()
    {
        $material_file_map = [];

        foreach ($this->small_image_list as $small_image_file) {
            $material_file_map[(int)$small_image_file['id']] = $small_image_file;
        }
        foreach ($this->large_image_list as $large_image_file) {
            $material_file_map[(int)$large_image_file['id']] = $large_image_file;
        }
        foreach ($this->large_vertical_image_list as $large_vertical_image_file) {
            $material_file_map[(int)$large_vertical_image_file['id']] = $large_vertical_image_file;
        }
        foreach ($this->audio_list as $audio_file) {
            $material_file_map[(int)$audio_file['id']] = $audio_file;
        }
        foreach ($this->video_list as $video_file) {
            $material_file_map[(int)$video_file['id']] = $video_file;
        }
        foreach ($this->video_vertical_list as $video_vertical_file) {
            $material_file_map[(int)$video_vertical_file['id']] = $video_vertical_file;
        }
        foreach ($this->group_image_list as $group_image_file) {
            $material_file_map[(int)$group_image_file['id']] = $group_image_file;
        }

        return $material_file_map;
    }

    public function judgeMaterial($is_return = false): array
    {
        if (UserService::isSuperManager()) {
            return [];
        }
        $material_file_map = $this->getAllMaterialFileKeyByID();

        $material_file_id_list = array_values(array_keys($material_file_map));

        $permission = (new PermissionLogic())->getMaterialPermissionAndUserOptionsByUserId($this->creator_id);
        $material_permission = $permission["material_permission"];
        $user_list = $permission["user_list"]->pluck('name')->toArray();
        $material_file_list = (new OdsMaterialFileLogModel())->getListByIdWithPermission($material_file_id_list, $user_list, $material_permission);
        $legal_ids = $material_file_list->pluck("id")->toArray();

        $error_msg = '';
        $error_msg_list = [];
        if (count($material_file_id_list) != count($legal_ids)) {
            foreach ($material_file_id_list as $material_file_id) {
                if (!in_array($material_file_id, $legal_ids)) {
                    $error_msg .= "无{$material_file_map[$material_file_id]['url']}的权限;";
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                        'value' => $material_file_id,
                        'msg' => "无权限使用，请删除"
                    ];
                }
            }
            if ($error_msg && !$is_return) {
                throw new AppException($error_msg);
            }
        }


        return $error_msg_list;
    }

    public function judgeGame()
    {
        if (UserService::isSuperManager()) {
            return;
        }
        // game权限
        $game_permission = (new PermissionLogic())->getGamePermissionByUserId($this->creator_id);
        $games = (new V2DimGameIdModel())->getListLikeGame($this->site_config['game_id'], [], $game_permission);
        $games_data = $games->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->game_id}-{$item->game_name}",
                'value' => $item->game_id,
                'os' => $item->os,
                'app_id' => $item->app_id,
                'game_id' => $item->game_id,
                'game_name' => $item->game_name,
                'app_name' => $item->app_name,
                'app_package_name' => $item->app_package_name,
                'platform' => $item->platform,
                'plat_id' => $item->plat_id,
            ];
        });

        if ($games_data->where('game_id', '=', $this->site_config['game_id'])->isEmpty()) {
            throw new AppException("无当前游戏权限");
        }
    }

    public function judgeAccount()
    {
        if (UserService::isSuperManager()) {
            return;
        }
        $account_id_list = [];
        foreach ($this->account_list as $account_info) {
            $account_id_list[] = $account_info['account_id'];
        }
        if ($account_id_list) {
            $error_account_id = '';
            $account_info_list = (new MediaAccountModel())->getListByMediaTypeAccountIds($this->media_type, $account_id_list);
            foreach ($account_info_list as $account_info_object) {
                if ($account_info_object->agent_leader != $this->creator) {
                    $error_account_id .= "{$account_info_object->account_id}-{$account_info_object->agent_leader},";
                }
            }
            $error_account_id = trim($error_account_id, ',');
            if ($error_account_id) {
                throw new AppException("帐号id:{$error_account_id} 不是" . $this->creator . "的账号，无法操作");
            }
        }
    }

    public function formatComposeCreativeMode()
    {
        if ($this->media_type === MediaType::TOUTIAO) {
            if ((BatchADClassMap::SERVICE_MAP[$this->media_type][$this->media_agent_type] ?? '') == 'ADLab' ||
                (BatchADClassMap::SERVICE_MAP[$this->media_type][$this->media_agent_type] ?? '') == 'V2') {
                $this->creative_mode = 1;
            }
        }
        if ($this->media_type === MediaType::KUAISHOU) {
            if ((BatchADClassMap::SERVICE_MAP[$this->media_type][$this->media_agent_type] ?? '') == 'ADLab') {
                $this->creative_mode = 1;
            }
            if ((BatchADClassMap::SERVICE_MAP[$this->media_type][$this->media_agent_type] ?? '') == 'Search') {
                $this->creative_mode = 2;
            }
        }
    }

    public function formatTargeting()
    {
        if ($this->targeting_compose_type == 0) {
            $content_class = BatchADClassMap::getClassName(
                $this->media_type,
                0,
                BatchADClassMap::TARGETING_CLASS_NAME
            );
            $this->targeting = new $content_class($this->targeting);
            $this->targeting->format($this->state);
            $this->targeting = $this->targeting->toArray();
        }
    }

    /**
     * @return AbstractADComposeContentParam
     */
    public function makeADComposeContent($script_leader = '', $script_leader_id = 0)
    {
        $content_class = BatchADClassMap::getClassName(
            $this->media_type,
            $this->media_agent_type,
            BatchADClassMap::COMPOSE_CONTENT_CLASS_NAME
        );
        $data = $this->toArray();
        $data['compose_id'] = $this->id;
        if ($script_leader) {
            $data['script_leader'] = $script_leader;
        }
        if ($script_leader_id) {
            $data['script_leader_id'] = $script_leader_id;
        }
        return new $content_class($data);
    }

    public function toData()
    {
        if (!$this->name) {
            throw new AppException('广告组合包名字不能为空');
        }
        if (!$this->create_time) {
            $this->create_time = date('Y-m-d H:i:s');
        }

        $data = $this->toArray();

        unset($data['id']);
        unset($data['update_time']);
        $data['account_list'] = json_encode($data['account_list'], JSON_UNESCAPED_UNICODE);
        $data['compose_config'] = json_encode($data['compose_config'], JSON_UNESCAPED_UNICODE);
        $data['setting'] = json_encode($data['setting'], JSON_UNESCAPED_UNICODE);
        $data['setting_list'] = json_encode($data['setting_list'], JSON_UNESCAPED_UNICODE);
        $data['site_config'] = json_encode($data['site_config'], JSON_UNESCAPED_UNICODE);
        $data['targeting_list'] = json_encode($data['targeting_list'], JSON_UNESCAPED_UNICODE);
        $data['targeting'] = json_encode($data['targeting'], JSON_UNESCAPED_UNICODE);
        $data['word_list'] = json_encode($data['word_list'], JSON_UNESCAPED_UNICODE);
        $data['group_image_list'] = json_encode($data['group_image_list'], JSON_UNESCAPED_UNICODE);
        $data['small_image_list'] = json_encode($data['small_image_list'], JSON_UNESCAPED_UNICODE);
        $data['large_image_list'] = json_encode($data['large_image_list'], JSON_UNESCAPED_UNICODE);
        $data['large_vertical_image_list'] = json_encode($data['large_vertical_image_list'], JSON_UNESCAPED_UNICODE);
        $data['video_list'] = json_encode($data['video_list'], JSON_UNESCAPED_UNICODE);
        $data['video_vertical_list'] = json_encode($data['video_vertical_list'], JSON_UNESCAPED_UNICODE);
        $data['cover_list_map'] = json_encode($data['cover_list_map'], JSON_UNESCAPED_UNICODE);
        $data['audio_list'] = $data['audio_list'] ? json_encode($data['audio_list'], JSON_UNESCAPED_UNICODE) : '[]';
        $data['vertical_cover_list_map'] = json_encode($data['vertical_cover_list_map'], JSON_UNESCAPED_UNICODE);
        $data['other_setting'] = json_encode($data['other_setting'], JSON_UNESCAPED_UNICODE);
        $data['calc_rule_list'] = json_encode($data['calc_rule_list'], JSON_UNESCAPED_UNICODE);
        $data['rta_list'] = json_encode($data['rta_list'], JSON_UNESCAPED_UNICODE);

        return $data;
    }

    public function initBySqlData($data)
    {
        $data['account_list'] = json_decode($data['account_list'], true);
        $data['compose_config'] = json_decode($data['compose_config'], true);
        $data['setting'] = json_decode($data['setting'], true);
        if (!$data['setting']) {
            $data['setting'] = (object)$data['setting'];
        }
        $data['setting_list'] = json_decode($data['setting_list'], true);
        $data['site_config'] = json_decode($data['site_config'], true);
        if (!$data['site_config']) {
            $data['site_config'] = (object)$data['site_config'];
        }
        $data['targeting_list'] = json_decode($data['targeting_list'], true);
        $data['targeting'] = json_decode($data['targeting'], true);
        if (!$data['targeting']) {
            $data['targeting'] = (object)$data['targeting'];
        }
        $data['word_list'] = json_decode($data['word_list'], true);
        $data['group_image_list'] = json_decode($data['group_image_list'], true);
        $data['small_image_list'] = json_decode($data['small_image_list'], true);
        $data['large_image_list'] = json_decode($data['large_image_list'], true);
        $data['large_vertical_image_list'] = json_decode($data['large_vertical_image_list'], true);
        $data['audio_list'] = json_decode($data['audio_list'], true);
        $data['video_list'] = json_decode($data['video_list'], true);
        $data['video_vertical_list'] = json_decode($data['video_vertical_list'], true);
        $data['cover_list_map'] = json_decode($data['cover_list_map'], true) ?: (object)[];
        $data['vertical_cover_list_map'] = json_decode($data['vertical_cover_list_map'], true) ?: (object)[];
        $data['other_setting'] = json_decode($data['other_setting'], true);
        $data['calc_rule_list'] = json_decode($data['calc_rule_list'], true);
        $data['rta_list'] = json_decode($data['rta_list'], true);

        parent::__construct($data);

        return $this;
    }

}
