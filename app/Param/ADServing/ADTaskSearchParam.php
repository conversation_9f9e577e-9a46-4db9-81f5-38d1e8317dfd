<?php
/**
 * User: zzh
 * Date: 2019-12-10
 */

namespace App\Param\ADServing;

use App\Param\AbstractParam;

class ADTaskSearchParam extends AbstractParam
{
    public $page;
    public $rows;
    public $id;
    public $ids = [];
    public $account_id;
    public $compose_id;
    public $game_id;
    public $queue_index;
    public $origin_type;
    public $word_list;
    public $targeting_name_list;
    public $creative_list;
    public $media_type;
    public $media_agent_type;
    public $company;
    public $state_code;
    public $platform;
    public $create_time;
    public $creator;
    public $agent_id;
    public $site_id;
    public $ad1_id;
    public $ad2_id;
    public $is_error;
    public $order_by;
    public $task_state;

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }

        if (!$this->rows) {
            $this->rows = 10;
        }

        if ($this->create_time) {
            $this->create_time = array_map(function ($time) {
                return date('Y-m-d H:i:s',substr($time,0,10));
            }, $this->create_time);
        }

        if ($this->order_by) {
            $this->order_by['order'] = str_replace('ending', '', $this->order_by['order']);
        }

    }

    // todo 一个decode函数
}
