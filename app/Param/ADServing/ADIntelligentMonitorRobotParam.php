<?php

namespace App\Param\ADServing;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class ADIntelligentMonitorRobotParam extends AbstractParam
{
    public $name;

    public $media_type;

    public $media_agent_type;

    public $exec_body_list = [];

    public $weight = 1;

    public $monitoring_frequency = 5;

    public $delay_exec_hour = 0;
    public $loop_number = 1;
    public $robot_notification = 0;
    public $exec_mode = 'once';

    public $status = 1;

    public $creator;


    public function initBySql($object)
    {
        $this->name = $object->name;
        $this->media_type = $object->media_type;
        $this->media_agent_type = $object->media_agent_type;
        $this->exec_body_list = json_decode($object->exec_body_list, true);
        $this->weight = $object->weight;
        $this->monitoring_frequency  = $object->monitoring_frequency ;
        $this->delay_exec_hour = $object->delay_exec_hour;
        $this->loop_number = $object->loop_number;
        $this->robot_notification = $object->robot_notification;
        $this->exec_mode = $object->exec_mode;
        $this->creator = $object->creator;
        $this->status = $object->status;
        return $this;
    }

    public function validate()
    {
        if (!$this->exec_body_list) {
            throw new AppException('执行体必须有值');
        }

        if ((empty($this->delay_exec_hour) && !is_numeric($this->delay_exec_hour))) {
            throw new AppException('执行时间为空');
        }

        if ((empty($this->weight) && !is_numeric($this->weight))) {
            throw new AppException('权重为空');
        }
    }

    public function toData()
    {
        $this->validate();
        $data = parent::toArray();
        if (!$data['creator']) {
            $data['creator'] = Container::getSession()->name;
        }
        $data['exec_body_list'] = json_encode($data["exec_body_list"], JSON_UNESCAPED_UNICODE);
        return $data;
    }

}
