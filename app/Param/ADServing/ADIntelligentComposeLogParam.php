<?php

namespace App\Param\ADServing;

use App\Param\AbstractParam;
use App\Utils\Helpers;

class ADIntelligentComposeLogParam extends AbstractParam
{
    public $intelligent_compose_id = 0;

    public $compose_id;
    public $compose_content;
    public $material_compose_type;
    public $word_compose_type;
    public $top_material_filter;
    public $potential_material_filter;
    public $new_material_filter;
    public $rule_list;

    public $ad_create_time;
    public $material_num;
    public $word_num;

    public $action;
    public $sql_text = '';

    public $data = [];

    public $creator;

    public function setComposeContent($data)
    {
        $this->compose_content = [
            'name' => $data['name'],
            'media_type' => $data['media_type'],
            'company' => $data['company'],
            'platform' => $data['platform'],
            'media_agent_type' => $data['media_agent_type'],
            'site_config' => $data['site_config'],
            'account_list' => $data['account_list'],
            'word_list' => $data['word_list'],
            'targeting' => $data['targeting'],
            'setting' => $data['setting'],
            'other_setting' => $data['other_setting'],
            'rta_list' => $data['rta_list'] ??[],
            'creator_id' => $data['creator_id'],
            'creator' => $data['creator'],
        ];
        return $this;
    }

    public function setProp(array $data)
    {
        $this->action = '';
        $this->sql_text = '';
        $this->data = [];
        unset($data['compose_content']);
        // 设置属性值
        foreach ($data as $property_name => $property_value) {
            if (property_exists($this, $property_name)) {
                $this->$property_name = $property_value;
            }
        }
        return $this;
    }


    public function toSqlData()
    {
        $data = $this->toArray();
        $data['compose_content'] = json_encode($data['compose_content'], JSON_UNESCAPED_UNICODE);
        $data['top_material_filter'] = json_encode($data['top_material_filter'], JSON_UNESCAPED_UNICODE);
        $data['potential_material_filter'] = json_encode($data['potential_material_filter'], JSON_UNESCAPED_UNICODE);
        $data['new_material_filter'] = json_encode($data['new_material_filter'], JSON_UNESCAPED_UNICODE);
        $data['rule_list'] = json_encode($data['rule_list'], JSON_UNESCAPED_UNICODE);
        $data['data'] = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        return $data;
    }
}
