<?php

namespace App\Param\ADServing;

use App\Constant\InitIntelligentMonitor;
use App\Param\AbstractParam;

class ADIntelligentMonitorRobotLogParam extends AbstractParam
{
    public $robot_id;

    public $type = '';

    public $media_type;

    public $media_agent_type;
    public $bind_type;

    public $bind_target_type;
    public $bind_target_value;

    public $target_type;

    public $target_value;
    public $exec_body_id;
    public $condition_body_content = [];
    public $condition_logic_result = [];
    public $condition_result = '';

    public $action = [];
    public $action_result = '';
    public $is_effective = '';

    public $msg = '';

    public $creator;

    public function reset()
    {
        $this->exec_body_id = '';
        $this->condition_body_content = [];
        $this->condition_logic_result = [];
        $this->condition_result = '';
        $this->action = [];
        $this->action_result = '';
        $this->is_effective = '';
        $this->msg = '';
    }

    public function addConditionLogicGroup($exec_body_name, $condition_group_index)
    {
        $this->condition_logic_result[] = [
            'type' => 'condition_group',
            'exec_body_name' => $exec_body_name,
            'num' => $condition_group_index,
        ];
    }

    public function addConditionLogicResult($target_key, $action, $target_value, $result_value, $sql, $result)
    {
        $this->condition_logic_result[] = [
            'type' => 'condition_result',
            'target_key' => $target_key,
            'target_key_name' => InitIntelligentMonitor::CONDITION_TARGET_LABEL_CONFIG[$this->media_type][$this->media_agent_type][$target_key],
            'action' => $action,
            'target_value' => $target_value,
            'result_value' => $result_value,
            'sql' => $sql,
            'result' => $result
        ];
    }

    public function toData(): array
    {
        $data = parent::toArray();
        $data['condition_body_content'] = json_encode($data["condition_body_content"], JSON_UNESCAPED_UNICODE);
        $data['condition_logic_result'] = str_replace('__TARGET_VALUE__', $this->target_value, json_encode($data["condition_logic_result"], JSON_UNESCAPED_UNICODE));
        $data['action'] = json_encode($data["action"], JSON_UNESCAPED_UNICODE);

        return $data;
    }

}
