<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Baidu\Basics;

use App\Constant\BatchAD;
use App\Exception\AppException;
use App\Param\ADServing\Baidu\ADOtherSettingContentParam as BaiduADOtherSettingContentParam;

class ADOtherSettingContentParam extends BaiduADOtherSettingContentParam
{
    public $scene_type = 'DEFAULT';
    public $url_type = '1';
    public $lp_mode = '0';
    public $lp_url = '';
    public $lp_url_content = '';
    public $app_name = '';
    public $app_url = '';
    public $ftypes = [];
    public $ftypes_none = '';
    public $producttypes_none = '';
    public $producttypes = [];
    public $page_map = [];
    public $convert_map = [];
    public $reserve_type= '';
    public $scene_convert_map= [];
    public $subject = '1';
    public $inherit_ascription_type = 0;
    public $inherit_user_ids = [];
    public $bid_position = 2;
    public $ftype_selection = 2;
    public $ftype_selection_list = [];
    public $bid_source = 2;
    public $bid_source_bid = [];
    public $project_switch = 0;
    public $project_map = [];

    /**
     * 参数初始化
     */
    public function paramHook()
    {
        if ($this->lp_url && !preg_match("/http[s]?:\/\/[\w.]+[\w\/]*[\w.]*\??[\w=&\+\%]*/is", $this->lp_url)) {
            throw new AppException('不是正常的落地页地址(lp_url)');
        }
        if (in_array('1', $this->ftypes) || in_array('2', $this->ftypes)) {
            $this->producttypes_none = 'buxian';
            $this->producttypes = [];
        }
        if (!$this->producttypes) {
            $this->producttypes_none = 'buxian';
        }
        $this->page_map = $this->tranObject($this->page_map);
        if ($this->page_map) {
            foreach ($this->page_map as $account_id => $page_item) {
                if ($page_item['page_info'] ?? []) {
                    $this->page_map[$account_id] = array_merge($page_item, json_decode($page_item['page_info'], true));
                }
            }
        }
        $this->convert_map = $this->tranObject($this->convert_map);
        $this->scene_convert_map = $this->tranObject($this->scene_convert_map);
        $this->project_map = $this->tranObject($this->project_map);
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if ($this->inherit_ascription_type == 2 && !$this->inherit_user_ids) {
            $error_msg_list[] = [
                'key' => 'inherit_user_ids',
                'value' => $this->inherit_user_ids,
                'msg' => '必须选择帐号id'
            ];
        }

        if ($this->bid_position == 1 && $this->ftype_selection == 2 && !$this->ftype_selection_list) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'ftype_selection_list',
                'value' => $this->ftype_selection_list,
                'msg' => '流量选择不能为空'
            ];
        }

        if ($this->bid_position == 1 && $this->bid_source == 2 && !$this->bid_source_bid) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                'key' => 'bid_source_bid',
                'value' => $this->bid_source_bid,
                'msg' => '出价选择不能为空'
            ];
        }

        return $error_msg_list;
    }

    /**
     * 参数格式化
     */
    public function format()
    {
    }

    /**
     * 获取转化内容
     * @param $account_id
     * @return array|mixed
     */
    public function getConvertMap($account_id)
    {
        if ($convert_info = $this->convert_map[$account_id] ?? '') {
            $convert_info = json_decode($convert_info);
            return $convert_info;
        }
        return [];
    }

    /**
     * 获取转化内容
     * @param $account_id
     * @return array|mixed
     */
    public function getSceneConvertMap($account_id)
    {
        if ($convert_info = $this->scene_convert_map[$account_id] ?? '') {
            return $convert_info;
        }
        return '';
    }

    /**
     * 获取落地页内容
     * @param $account_id
     * @return array|mixed
     */
    public function getPageMap($account_id)
    {
        if ($page_info = $this->page_map[$account_id] ?? '') {
            $page_info = json_decode($page_info);
            return $page_info;
        }
        return [];
    }

    /**
     * 获取智投广告内容
     * @param $account_id
     * @return array|mixed
     */
    public function getProjectMap($account_id)
    {
        if(!$this->project_switch) return [];
        if ($project_info = $this->project_map[$account_id] ?? '') {
            $project_info = json_decode($project_info);
            return $project_info;
        }
        return [];
    }
}
