<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Baidu\Basics;

use App\Constant\BaiduEnum;
use App\Param\ADServing\Baidu\ADSettingContentParam as BaiduADSettingContentParam;
use App\Utils\Helpers;

class ADSettingContentParam extends BaiduADSettingContentParam
{
    public $bstype = '1';
    public $campaign_pause = true;
    public $ad_pause = true;
    public $apk_name = '';
    public $platform = '';
    public $schedule_type = 'buxian';
    public $starttime = '';
    public $endtime = '';
    public $schedule_map = [];
    public $schedule = [];
    public $bgtctltype = '0';
    public $budget_none = 'buxian';
    public $budget = 0;
    public $bidtype = '3';
    public $pay_mode = '1';
    public $ocpc_bid = [];
    public $auto_opt_none = 'buxian';
    public $auto_opt_strategies = [];
    public $auto_opt_max_bid = '';
    public $optimize_deep_trans = false;
    public $deep_trans_type = '25';
    public $deep_ocpc_bid = '';
    public $roi_ratio = 0;
    public $brand = '';
    public $subtitle = '';
    public $user_portrait = '';
    public $idea_type = '1';
    public $play_num = 0;
    public $exclude_trans = '0';

    public $exclude_trans_filter_time = '1';
    public $binded_reasons_status = 0; // 是否开启推荐理由
    public $binded_reasons = []; // 推荐理由 传空则解绑
    public $campaign_type = BaiduEnum::CAMPAIGN_TYPE_NORMAL;
    public $trans_type_attribute = '1';

    /**
     * @var int $is_manual_bid_for_max_mode 浅度转化出价模式
     */
    public $is_manual_bid_for_max_mode = 0;

    /**
     * @var int $is_manual_deep_bid_for_max_mode 深度转化出价模式
     */
    public $is_manual_deep_bid_for_max_mode = 0;


    /**
     * @var bool 是否严格validate
     */
    public $is_strict = true;

    public function __construct(array $property = [], bool $is_strict = true)
    {
        unset($property['is_strict']);
        $this->is_strict = $is_strict;
        // 判断null的属性值直接删除
        foreach ($property as $property_name => $property_value) {
            if ($property_value === NULL) {
                unset($property[$property_name]);
            }
        }
        parent::__construct($property);
    }

    public function paramHook()
    {
        if (!is_array($this->deep_ocpc_bid)) {
            $this->deep_ocpc_bid = [$this->deep_ocpc_bid];
        }
    }

    /**
     * 校验参数
     */
    public function validate()
    {

        $error_msg_list = [];

        if (Helpers::ADServingStrLen($this->subtitle) > 24) {
            $error_msg_list[] = [
                'key' => 'subtitle',
                'value' => $this->subtitle,
                'msg' => "副标题不允许超过24个字符，中文算两个字符"
            ];
        }

        if ($this->optimize_deep_trans === 'true') {
            if ((int)$this->trans_type_attribute === 2) {
                $error_msg_list[] = [
                    'key' => 'optimize_deep_trans',
                    'value' => $this->optimize_deep_trans,
                    'msg' => "开启付费次数优化，不能打开深度转化"
                ];
            }
        }

        if ($this->deep_trans_type == 27) {
            if (!$this->roi_ratio) {
                $error_msg_list[] = [
                    'key' => 'roi_ratio',
                    'value' => $this->roi_ratio,
                    'msg' => 'ROI系数不能为空或者0'
                ];
            } elseif ($this->roi_ratio > 100) {
                $error_msg_list[] = [
                    'key' => 'roi_ratio',
                    'value' => $this->roi_ratio,
                    'msg' => 'ROI系数不能大于100'
                ];
            }
        }

        if ($this->subtitle && $this->brand && $this->subtitle == $this->brand) {
            $error_msg_list[] = [
                'key' => 'brand',
                'value' => $this->brand,
                'msg' => '副标题与品牌名不能一致'
            ];
        }

        if ($this->brand && strpos($this->brand, '333') !== false) {
            $error_msg_list[] = [
                'key' => 'brand',
                'value' => $this->brand,
                'msg' => '品牌名(brand)包含333，非法命名，请修改参数包!'
            ];
        }

        if (!$this->ocpc_bid || !is_array($this->ocpc_bid) || count($this->ocpc_bid) <= 0) {
            if ($this->campaign_type == BaiduEnum::CAMPAIGN_TYPE_MAX_MODE && $this->is_manual_bid_for_max_mode == 1) {
                $error_msg_list[] = [
                    'key' => 'ocpc_bid',
                    'value' => $this->ocpc_bid,
                    'msg' => '浅度转化出价需要大于0.3元'
                ];
            }
            if ($this->campaign_type != BaiduEnum::CAMPAIGN_TYPE_MAX_MODE) {
                $error_msg_list[] = [
                    'key' => 'ocpc_bid',
                    'value' => $this->ocpc_bid,
                    'msg' => '第二阶段广告出价需要大于0.3元'
                ];
            }
        }

        if (is_array($this->ocpc_bid) && count($this->ocpc_bid) > 0) {
            foreach ($this->ocpc_bid as $key => $value) {
                if ((float)$value <= 0.3) {
                    $error_msg_list[] = [
                        'key' => 'ocpc_bid',
                        'value' => $this->ocpc_bid,
                        'msg' => '第二阶段广告出价需要大于0.3元'
                    ];
                }
            }
        }
        if ($this->campaign_type == BaiduEnum::CAMPAIGN_TYPE_MAX_MODE && $this->is_manual_deep_bid_for_max_mode == 1 && empty($this->deep_ocpc_bid)) {
            $error_msg_list[] = [
                'key' => 'deep_ocpc_bid',
                'value' => $this->deep_ocpc_bid,
                'msg' => '深度转化出价需要大于0元'
            ];
        }

        if (!$this->user_portrait) {
            $error_msg_list[] = [
                'key' => 'user_portrait',
                'value' => $this->user_portrait,
                'msg' => 'icon图标不能为空'
            ];
        }

        if (!$this->brand) {
            $error_msg_list[] = [
                'key' => 'brand',
                'value' => $this->brand,
                'msg' => '品牌名不能为空'
            ];
        }

        if ($this->schedule_type == 'zidingyi') {
            if (!$this->starttime && !$this->endtime) {
                $error_msg_list[] = [
                    'key' => 'schedule_type',
                    'value' => $this->schedule_type,
                    'msg' => '结束时间(endtime)与开始时间(starttime)不能为空'
                ];
            }
        }

        if ($this->campaign_type == BaiduEnum::CAMPAIGN_TYPE_MAX_MODE && empty($this->budget)) {
            $error_msg_list[] = [
                'key' => 'budget',
                'value' => $this->budget,
                'msg' => '放量模式下日预算不能为空'
            ];
        }

        return $error_msg_list;
    }


    /**
     * 参数格式化
     */
    public function format()
    {

        if ($this->schedule_type == 'zidingyi') {

            if ($this->starttime) {
                $this->starttime = mb_strlen(strval($this->starttime)) > 10 ? $this->starttime / 1000 : $this->starttime;
                if (($this->starttime) < time()) {
                    $this->starttime = time();
                }
            }

            if ($this->endtime) {
                $this->endtime = mb_strlen(strval($this->endtime)) > 10 ? $this->endtime / 1000 : $this->endtime;
            }

            if ($this->endtime && $this->endtime < $this->starttime) {
                $this->endtime = strtotime('+1 year');
            }
        } else {
            $this->starttime = null;
            $this->endtime = null;
        }

        if ($this->starttime) {
            $this->starttime = mb_strlen(strval($this->starttime)) > 10 ? $this->starttime / 1000 : $this->starttime;
        } else {
            $this->starttime = 0;
        }

        if ($this->endtime) {
            $this->endtime = mb_strlen(strval($this->endtime)) > 10 ? $this->endtime / 1000 : $this->endtime;
        } else {
            $this->endtime = 0;
        }

        if($this->campaign_type == BaiduEnum::CAMPAIGN_TYPE_NORMAL){
            $this->is_manual_bid_for_max_mode = BaiduEnum::IS_MAX_MODE_FALSE;
            $this->is_manual_deep_bid_for_max_mode = BaiduEnum::IS_MAX_MODE_FALSE;
        }
    }
}
