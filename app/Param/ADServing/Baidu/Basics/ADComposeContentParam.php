<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Baidu\Basics;

use App\Constant\AgentGroup;
use App\Constant\BaiduEnum;
use App\Constant\BatchAD;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsBaiduAudienceGroupModel;
use App\Param\ADServing\ADComposeContent\ADComposeMaterialListParam;
use App\Param\ADServing\Baidu\ADComposeContentParam as BaiduADComposeContentParam;
use App\Param\ADServing\Baidu\ADTargetingContentParam;
use App\Service\GamePriceRangeFilterService;

class ADComposeContentParam extends BaiduADComposeContentParam
{
    const DEPP_BID_TYPE_MAP = [
        '26' => 'DEEP_BID_TYPE_PAY',
        '28' => 'DEEP_BID_TYPE_NEXT_DAY_OPEN',
        '53' => 'DEEP_BID_TYPE_ORDER_SUBMIT',
        '18' => 'DEEP_BID_TYPE_PAGE_CONFIRM_EFFECTIVE_LEADS',
        '10' => 'DEEP_BID_TYPE_SHOPPING',
        '25' => 'DEEP_BID_TYPE_REGISTER',
        '27' => 'DEEP_BID_TYPE_CUSTOM',
        '42' => 'DEEP_BID_TYPE_CREDIT',
        '45' => 'DEEP_BID_TYPE_ECOMMERCE_ORDER',
        '54' => 'DEEP_BID_TYPE_PURCHASE_OF_GOODS',
        '72' => 'DEEP_BID_TYPE_TALK_ABOUT_BUSINESS',
        '73' => 'DEEP_BID_TYPE_DIALBACK_CONNECT',
        '56' => 'DEEP_BID_TYPE_TO_STORE',
        '74' => 'DEEP_BID_TYPE_CLUE_CONFIRM',
        '75' => 'DEEP_BID_TYPE_DIALBACK_INTENTION_FOUND',
        '76' => 'DEEP_BID_TYPE_CLUE_HIGH_INTENTION',
        '77' => 'DEEP_BID_TYPE_DIALBACK_ODERED_CUSTOMER',
        '79' => 'DEEP_BID_TYPE_WECHAT_WECOM_ADD',
    ];

    public function getAgentGroup()
    {
        if ((int)$this->site_config->plat_id === PlatId::MINI) {
            return AgentGroup::BAIDU_MINI_GAME;
        } else {
            return AgentGroup::BAIDU;
        }
    }

    /**
     * 组合参数联合检验
     * @return mixed
     */
    protected function unionParamValidateJudge()
    {
        $error_msg_list = [];

        /** @var ADOtherSettingContentParam $other_setting */
        $other_setting = $this->other_setting;

        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {

            /**@var ADSettingContentParam $setting */

            foreach ($this->targeting_list->getContentList() as $targeting_id => $targeting) {

                /**@var ADTargetingContentParam $targeting */

                if (
                    $this->site_config->game_type == '安卓' &&
                    $this->site_config->plat_id == PlatId::MINI &&
                    $targeting->device_none == 'zidingyi' &&
                    count(array_intersect($targeting->device_map, ["1", "2"])) != 2
                ) {
                    $error_msg_list[] = [
                        'key' => 'os',
                        'source_id' => $targeting_id,
                        'value' => $targeting->device_map,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "安卓的小游戏的情况下需要同时选安卓和IOS的系统版本"
                    ];
                }

                if (
                    $this->site_config->game_type == 'IOS' &&
                    $this->site_config->plat_id == PlatId::MINI &&
                    (
                        in_array('2', $targeting->device_map) ||
                        $targeting->device_none == 'buxian'
                    )
                ) {
                    $error_msg_list[] = [
                        'key' => 'os',
                        'source_id' => $targeting_id,
                        'value' => $targeting->device_map,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                        'msg' => "IOS的小游戏的情况下需要IOS的系统版本，且不能选择安卓系统版本"
                    ];
                }

                if ($other_setting->ftypes_none == 'targeting') {
                    if (!(isset($targeting->is_ftypes) && $targeting->is_ftypes == 'yes')) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_TARGETING,
                            'source_id' => $setting_id,
                            'key' => 'ftypes_none',
                            'value' => $other_setting->ftypes_none,
                            'msg' => "定向包id是{$targeting_id}，没使用版位，无法使用跟随定向包方式组合广告"
                        ];
                        break;
                    }
                }

                $all_audience_group_id = [];
                $audience_expire_error = '';
                if ($targeting->exclude_crowd) {
                    $all_audience_group_id = array_merge($all_audience_group_id, explode(',', $targeting->exclude_crowd));
                }
                if ($targeting->crowd) {
                    $all_audience_group_id = array_merge($all_audience_group_id, explode(',', $targeting->crowd));
                }
                if ($all_audience_group_id) {
                    $list = (new OdsBaiduAudienceGroupModel())->getListInfoById($all_audience_group_id);
                    foreach ($list as $audience) {
                        if (time() > strtotime($audience->expire_time)) {
                            $audience_expire_error .= "人群包id:{$audience->audience_group_id}已经过期,";
                        }
                    }
                }
                if ($audience_expire_error) {
                    $audience_expire_error .= "请重新选择";
                    throw new AppException($audience_expire_error);
                }
            }

            if ($this->site_config->convert_type != '26' && (int)$setting->trans_type_attribute === 2) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'trans_type_attribute',
                    'value' => $setting->trans_type_attribute,
                    'msg' => '转化类型选择付费时，才可以开启付费次数优化'
                ];
            }

            if (!$setting->ocpc_bid || !is_array($setting->ocpc_bid)) {
                $ocpc_bid = 0;
            } else {
                if ((int)$setting->ocpc_bid_mode == 1) {
                    $ocpc_bid = $setting->ocpc_bid[1];
                } else {
                    $ocpc_bid = max($setting->ocpc_bid);
                }
            }

            $error = GamePriceRangeFilterService::isCorrectPrice(
                MediaType::BAIDU,
                $this->platform,
                $this->site_config->game_id,
                $this->site_config->game_type,
                $this->getAgentGroup(),
                $other_setting->scene_type == 'RESERVE' ?
                    (
                    $other_setting->reserve_type == '3' ?
                        'AD_CONVERT_TYPE_NOTIFY_FORM_SUBMIT'
                        :
                        'AD_CONVERT_TYPE_NOTIFY_FORM_CLICK_ACTION'
                    )
                    :
                    ConvertType::MEDIA[MediaType::BAIDU][$this->site_config->convert_type] ?? $this->site_config->convert_type,
                $other_setting->scene_type == 'RESERVE' ? '' : (self::DEPP_BID_TYPE_MAP[$this->site_config->deep_external_action] ?? ''),
                $ocpc_bid,
                $setting->roi_ratio
            );

            if ($error) {
                if ($ocpc_bid && isset($error['bid'])) {
                    $error_msg_list[] = [
                        'key' => 'ocpc_bid',
                        'value' => 0,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => $error['bid']
                    ];
                }

                if ($setting->roi_ratio && isset($error['roi_goal'])) {
                    $error_msg_list[] = [
                        'key' => 'roi_ratio',
                        'value' => 0,
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'msg' => $error['roi_goal']
                    ];
                }
            }

            // 针对广告配置的深度转化与参数包的深度转化是否一致的判断
            if ($setting->deep_trans_type == 25 && $this->site_config->deep_external_action != '25') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_trans_type',
                    'value' => $setting->deep_trans_type,
                    'msg' => '参数包的深度转化类型为注册，广告配置的深度转化类型也需要为注册'
                ];
            }

            if ($setting->deep_trans_type == 26 && $this->site_config->deep_external_action != '26') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_trans_type',
                    'value' => $setting->deep_trans_type,
                    'msg' => '参数包的深度转化类型为付费，广告配置的深度转化类型也需要为付费'
                ];
            }

            if ($setting->deep_trans_type == 27 && $this->site_config->convert_type != '26') {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_trans_type',
                    'value' => $setting->deep_trans_type,
                    'msg' => '参数包的深度转化类型为ROI，广告配置的转化类型也需要为付费'
                ];
            }

            if (!in_array($other_setting->subject, [1, 4])) {
                if (!$setting->subtitle) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'subtitle',
                        'source_id' => $setting_id,
                        'value' => $setting->subtitle,
                        'msg' => "应用下载的类型必须填写副标题"
                    ];
                }
            } else {
                if ($setting->exclude_trans == 5) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'key' => 'exclude_trans',
                        'value' => $setting->exclude_trans,
                        'source_id' => $setting_id,
                        'msg' => "同APP选项仅支持应用下载类型"
                    ];
                }
            }

            if (count($other_setting->ftypes) == 1 && in_array('2', $other_setting->ftypes) &&
                $this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                    'key' => 'idea_type',
                    'value' => $setting->idea_type,
                    'msg' => '单贴吧信息流不支持程序化创意'
                ];
            }

            if ($other_setting->subject == 4) {
                foreach ($this->account_list->account_list as $account_info) {
                    if (empty($other_setting->page_map[$account_info['account_id']])) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                            'key' => 'page_map',
                            'value' => $other_setting->page_map,
                            'msg' => "请选择基木鱼落地页"
                        ];
                        break;
                    }
                }
            }

            foreach ($this->word_list->getWordContent() as $word) {
                if (strlen($word) > 90) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'value' => $word,
                        'msg' => "文案:{$word},大于30个字,不符合规范"
                    ];
                }
                if ($setting->subtitle && strpos($word, $setting->subtitle) !== false) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                        'value' => $word,
                        'msg' => "文案:{$word},包含副标题:{$setting->subtitle},不符合规范"
                    ];
                }
            }

            // 开启播放量进行素材样式验证
            if ($setting->play_num) {
                $material_type_array = $this->getMaterialStyleAddr($this->material_list);
                if ($diff_type = array_diff($material_type_array, [108, 109, 113, 114])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                        'source_id' => $setting_id,
                        'key' => 'play_num',
                        'value' => $setting->play_num,
                        'msg' => '素材样式不支持开启播放量：' . implode(',', array_unique($diff_type))
                    ];
                }
            }

            if ($this->site_config->convert_type == BaiduEnum::CONVERT_TYPE_PAY && $setting->deep_trans_type == BaiduEnum::DEEP_TRANS_TYPES_PAY) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
                    'source_id' => $setting_id,
                    'key' => 'deep_trans_type',
                    'value' => $setting->deep_trans_type,
                    'msg' => '转化类型为付费时,深度转化类型不能选择付费,只能选择深度ROI转化'
                ];
            }

        }

        // 线索广告
        if ((int)$this->site_config->plat_id === PlatId::MINI) {
            // 微信小程序调用类型  需要配置sdk
            if ((int)$other_setting->url_type === 2) {
                if (!$this->site_config->sdk_ext['mini_game_original_id']) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'url_type',
                        'value' => 0,
                        'msg' => "当前游戏未配置sdk，不能使用微信小程序调用类型，请前往sdk管理进行设置"
                    ];
                }
            }
            if (!in_array($other_setting->subject, [1, 4])) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                    'key' => 'subject',
                    'value' => 0,
                    'msg' => "微信小程序只能使用网站链接或基木鱼落地页"
                ];
            }
            if ($other_setting->ftypes_none != 'targeting' && $other_setting->ftypes_none != 'default') {
                if (!in_array(1, $other_setting->ftypes)) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'ftypes_none',
                        'value' => 0,
                        'msg' => "微信小程序需要选择百度信息流投放位置"
                    ];
                }
            }
            foreach ($this->account_list->account_list as $account_info) {
                if (empty($other_setting->convert_map[$account_info['account_id']])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'convert_map',
                        'value' => 0,
                        'msg' => "请选择转化ID,若下拉数据为空需要前往媒体后台自行创建"
                    ];
                    break;
                }
            }
        }

        if ($other_setting->project_switch == 1) {
            foreach ($this->account_list->account_list as $account_info) {
                if (empty($other_setting->project_map[$account_info['account_id']])) {
                    $error_msg_list[] = [
                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
                        'key' => 'project_map',
                        'value' => $other_setting->project_map,
                        'msg' => "请选择智投项目"
                    ];
                    break;
                }
            }
        }

        foreach ($this->material_list->all_video_list as $video) {
            foreach ($video['cover_list'] as $cover) {
                if ($video['width'] != $cover['width'] || $video['height'] != $cover['height']) {
                    if (
                        !(
                            ($video['width'] == 720 && $video['height'] == 1280) &&
                            ($cover['width'] == 1140 && $cover['height'] == 640)
                        )
                    ) {
                        $error_msg_list[] = [
                            'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'key' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
                            'value' => 0,
                            'msg' => "{$video['url']}封面与视频的尺寸不一致 或者 竖版视频需要1140*640的封面"
                        ];
                    }
                }
            }
        }

        if ($this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && $this->compose_config->video_num_in_ad > 12) {
            $error_msg_list[] = [
                'type' => BatchAD::COMPOSE_ERROR_TYPE_COMPOSE_CONFIG,
                'key' => 'video_num_in_ad',
                'value' => $this->compose_config->video_num_in_ad,
                'msg' => '程序化创意视频素材最多支持12组'
            ];
        }

        foreach ($this->word_list->getWordContent() as $key => $word) {
            if (strpos($word, '%') !== false) {
                $error_msg_list[] = [
                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'key' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
                    'value' => $word,
                    'msg' => "{$word}文案不允许有百分号(%)字符"
                ];
            }
        }

        return $error_msg_list;
    }

    /**
     * 返回全部素材的样式集合
     * @param ADComposeMaterialListParam $list
     * @return int[]
     */
    private function getMaterialStyleAddr(ADComposeMaterialListParam $list)
    {
        $list = $list->toFilterArray();

        // 代码冗余了 MediaBaidu类中有一致的逻辑判断 有改动需要同步
        $array = [];

        /** @var ADOtherSettingContentParam $other_setting */
        $other_setting = $this->other_setting;

        foreach ($list as $material_list) {
            foreach ($material_list as $material) {
                if (isset($material['cover_list'])) {
                    if ($material['width'] > $material['height']) {
                        if ($other_setting->subject == 1) {
                            $array[] = 108;
                        } else {
                            $array[] = 109;
                        }
                    } else {
                        if ($other_setting->subject == 1) {
                            $array[] = 111;
                        } else {
                            $array[] = 112;
                        }
                    }
                } else {
                    if ($other_setting->subject == 1) {
                        $array[] = 107;
                    } else {
                        $array[] = 106;
                    }
                }
            }
        }
        return $array;
    }
}
