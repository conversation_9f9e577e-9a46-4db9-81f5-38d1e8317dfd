<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/25
 * Time: 16:42
 */

namespace App\Param\ADServing\Baidu;

use App\Constant\BaiduEnum;
use App\Constant\BatchAD;
use App\Exception\AppException;
use App\Param\ADServing\AbstractADComposeContentParam;
use App\Param\ADServing\ADComposeContent\ADComposeMaterialListParam;


class ADComposeContentParam extends AbstractADComposeContentParam
{

    public function judgeAD1Name(int $num, string $material_name, string $targeting_name)
    {
    }

    public function judgeAD2Name(int $num, string $material_name, string $targeting_name)
    {
    }

    protected function unionParamValidateJudge()
    {

    }

    public function getMaterialFileNormalList(): array
    {
        return [];
    }
}

