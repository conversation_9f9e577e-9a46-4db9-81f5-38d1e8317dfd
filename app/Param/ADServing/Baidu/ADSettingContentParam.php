<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:43
 */

namespace App\Param\ADServing\Baidu;

use App\Constant\AgentGroup;
use App\Constant\BaiduEnum;
use App\Exception\AppException;
use App\Param\ADServing\AbstractADSettingContentParam;
use App\Utils\Helpers;

class ADSettingContentParam extends AbstractADSettingContentParam
{
    public $ocpc_bid = [];
    public $ocpc_bid_mode = 0;

    public $deep_ocpc_bid_mode = 0;

    public function validate()
    {
    }

    public function format()
    {
    }

    /**
     * 生成目标转化出价
     * @return mixed
     */
    public function getOcpcBid()
    {
        if (!$this->ocpc_bid || !is_array($this->ocpc_bid)) {
            return '';
        }
        if ((int)$this->ocpc_bid_mode === 1) {
            return sprintf("%.2f",$this->ocpc_bid[0] + mt_rand()/mt_getrandmax() * ($this->ocpc_bid[1]-$this->ocpc_bid[0]));
        } else {
            return $this->ocpc_bid[array_rand($this->ocpc_bid, 1)];
        }
    }

    /**
     * 生成深度转化出价
     * @return mixed
     */
    public function getDeepOcpcBid()
    {
        if (!$this->deep_ocpc_bid || !is_array($this->deep_ocpc_bid)) {
            return '';
        }
        if ((int)$this->deep_ocpc_bid_mode === 1) {
            return sprintf("%.2f",$this->deep_ocpc_bid[0] + mt_rand()/mt_getrandmax() * ($this->deep_ocpc_bid[1]-$this->deep_ocpc_bid[0]));
        } else {
            return $this->deep_ocpc_bid[array_rand($this->deep_ocpc_bid, 1)];
        }
    }

}
