<?php


namespace App\Param;


use App\Exception\AppException;

class ADAnalysisTencentTransferParam extends AbstractParam
{
    public $operate_type = '';
    public $transfer_info = [];
    public $operator = '';
    const OPERATE_TYPE = ['RECHARGE', 'REFUND'];
    //FUND_TYPE_AD_RECHARGE 广告充值金 FUND_TYPE_COMPENSATE_VIRTUAL 补偿虚拟金余额
    const TRANSFER_TYPE = ['FUND_TYPE_AD_RECHARGE' => 1, 'FUND_TYPE_COMPENSATE_VIRTUAL' => 2, 'FUND_TYPE_SPECIAL_GIFT' => 3, 'FUND_TYPE_MP_GAME_DEVELOPER_WORKING_FUND' => 4, 'FUND_TYPE_MP_GAME_DEVELOPER_GIFT' => 5];
    //循环转账的资金类型顺序
    const TRANSFER_SORT = ['FUND_TYPE_AD_RECHARGE', 'FUND_TYPE_COMPENSATE_VIRTUAL', 'FUND_TYPE_SPECIAL_GIFT', 'FUND_TYPE_MP_GAME_DEVELOPER_WORKING_FUND', 'FUND_TYPE_MP_GAME_DEVELOPER_GIFT'];
    //枚举值=>字段值映射
    const ENUM_TO_FIELD_NAME = [
        'FUND_TYPE_AD_RECHARGE' => 'fund_type_ad_recharge',
        'FUND_TYPE_COMPENSATE_VIRTUAL' => 'compensate_virtual_balance',
        'FUND_TYPE_SPECIAL_GIFT' => 'special_gift_balance',
        'FUND_TYPE_MP_GAME_DEVELOPER_WORKING_FUND' => 'mp_game_developer_working_fund_balance',
        'FUND_TYPE_MP_GAME_DEVELOPER_GIFT' => 'mp_game_developer_gift_balance'
    ];

    public function paramHook()
    {
        if (!in_array($this->operate_type, self::OPERATE_TYPE)) {
            throw new AppException('转账类型错误');
        }

        if (!$this->transfer_info || !is_array($this->transfer_info)) {
            throw new AppException('转账请求信息错误');
        }

        foreach ($this->transfer_info as $one_transfer_info) {
            if (!isset($one_transfer_info['operate_account_id']) || !is_numeric($one_transfer_info['operate_account_id'])) {
                throw new AppException('账号ID格式错误');
            }

            if (!isset($one_transfer_info['account_info']) || !is_array($one_transfer_info['account_info'])) {
                throw new AppException('账号目标信息格式错误');
            }

            foreach ($one_transfer_info['account_info'] as $item) {
                if (!isset($item['account_id']) || !is_numeric($item['account_id'])) {
                    throw new AppException('账号ID格式错误');
                }

                if (!isset($item['transfer_type']) || !in_array($item['transfer_type'], array_keys(self::TRANSFER_TYPE))) {
                    throw new AppException('金额类型错误');
                }

                if (!isset($item['amount']) || !is_numeric($item['amount']) || $item['amount'] <= 0) {
                    throw new AppException('金额错误');
                }
            }
            unset($item);
        }
        unset($one_transfer_info);
    }

}
