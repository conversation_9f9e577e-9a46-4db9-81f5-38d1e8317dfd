<?php
/**
 * 基本报表详情筛选参数
 */

namespace App\Param;


use App\Constant\ADAnalysisSqlMap;
use App\Constant\MediaType;
use App\Utils\SqlParser;
use Common\EnvConfig;

class ADAnalysisDetailFilterParam extends AbstractParam
{
    public $media_type = 0;                  // 所选媒体
    public $dimension = [];                  // 处理后的维度
    public $target = [];                     // 所有指标
    public $filter = [];                     // 所有指标
    public $cost_date;                       // 消耗日期
    public $flow_type;                       //流量类型
    public $statistic_base_on;               //统计方式，1：按子 2：按根
    public $ad2_create_time;                 // 创建日期
    public $game_permission;                 // 游戏权限
    public $agent_permission;                // 渠道权限
    public $hour_data_log_table = [];        //小时消耗表表名
    public $ad_log_table = '';               //二级广告表表名
    public $aggregation_time = '';           //聚合时间类型
    public $reg_log_time_type = '';          //reg_log表时间类型
    public $data_log_time_type = '';         //data_log表时间类型
    public $date_format = '';                //logic层格式化时间戳的格式
    public $time_gap = '';                   //每条数据之间应该有的时间差
    //头条data_log sql map
    public $toutiao_data_log_map = [];
    //媒体类型为头条时外层select字段 sql map
    public $toutiao_main_compute_map = [];
//    public $first_ad_log_table = '';         //一级广告表表名
//    public $third_ad_log_table = '';         //三级广告表表名
//    public $account_log_table = '';          //账户表表名
    //reg_log表数据库及表名
    public $reg_log_table;
    //reg_log表字段对应map
    public $reg_log_map;
    //reg_log表所查字段
    public $reg_log = [];
    //reg_log注册时间
    public $reg_time_field;
    //小时消耗表字段
    public $hour_data_log = [];
    //广告二级表字段
    public $ad_log = [];
    //data_log和ad_log连接字段
    public $join_ad_log = [];
    //消耗小时表group by字段
    public $hour_data_log_group_by = [];
    //reg_log group by字段
    public $reg_group_by = [];
    //with as之间连接字段
    public $main_join = [];
    //筛选的ad2_id的值
//    public $ad2_id = 0;
    //主sql查询的字段
    public $main_select = [];
    //filter筛选字段
    public $data_log_filter_condition = [];
    public $reg_log_filter_condition = [];
//    //头条小时消耗表字段
//    public $toutiao_hour_data_log = [];
//    //腾讯小时消耗表字段
//    public $tencent_hour_data_log = [];

    //需要用计算百分比的字段
    public $all_calculate_percentage = [
        'action_uid_reg_rate', 'first_day_pay_rate', 'click_rate', 'reg_rate', 'active_rate', 'convert_rate',
        'first_day_roi', 'total_roi', 'cost_process', 'pay_rate', 'rate_day_stay_2', 'rate_day_stay_3', 'rate_day_stay_7',
        'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'reg_old_muid_percentage', 'reg_old_clique_muid_percentage',
        'day_three_login_uid_percentage', 'rate_day_roi_7', 'gdt_video_outer_play_time_avg_rate', 'gdt_video_outer_play_rate',
    ];

    //需要相除的字段
    public $all_calculate_division = [
        'cost_per_reg', 'first_day_ltv', 'cost_per_first_day_pay', 'first_day_arppu', 'cpc', 'cpm',
        'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'media_cost_per_reg', 'cost_first_day_pay_times',
        'cost_total_pay_times', 'cost_day_second_login', 'gdt_video_outer_play_cost', 'gdt_video_outer_play_time_count'
    ];
    //需要计算平均值的字段
    public $all_calculate_average = ['cpa_bid', 'deep_cpabid'];
    //需要单纯累加但是要保留两位小数的字段
    public $all_need_keep_two_decimal = ['cost', 'ori_cost', 'budget'];
    //所有需要计算的字段
    public $all_need_calculate = [
        'count', 'count_ad2', 'count_ad2_deliveried', 'count_ad2_delivering', 'count_ad2_undeliveried',
        'count_cost_date', 'count_ad3', 'cost', 'reg_uid_count', 'reg_muid_count', 'action_muid_count', 'first_day_ltv',
        'first_day_pay_count', 'first_day_pay_money', 'first_day_arppu', 'first_day_roi', 'cost_process', 'click',
        'show', 'ori_cost', 'click_rate', 'convert', 'pay_count', 'active_count', 'convert_rate', 'reg_count',
        'media_cost_per_reg', 'pay_rate', 'deep_cpabid', 'cpa_bid', 'budget',
        'action_uid_reg_rate', 'cost_per_reg', 'first_day_pay_rate', 'cost_per_first_day_pay', 'total_roi', 'cpc',
        'cpm', 'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'reg_rate', 'active_rate', 'rate_day_stay_2',
        'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'first_day_pay_times',
        'total_pay_times', 'cost_first_day_pay_times', 'cost_total_pay_times', 'click_count', 'cost_day_second_login',
        'total_pay_count', 'total_pay_money', 'reg_old_muid_count', 'reg_old_clique_muid_count', 'day_three_login_uid_count',
        'reg_old_muid_percentage', 'reg_old_clique_muid_percentage', 'day_three_login_uid_percentage', 'new_pay_day_money',
        'new_pay_day_count', 'rate_day_roi_7',
    ];

    /**
     * 处理不同media_type的select数据
     */
    public function handleDifferentMediaType()
    {
        //如果是按根统计，更换overview_log表
        if (2 === (int)$this->statistic_base_on) {
            $this->reg_log_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dwd_root_game_uid_reg_log';
            $this->reg_log_map = ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_ROOT_GAME_REG_LOG;
            $this->reg_time_field = 'root_game_reg_time';
        } else {
            $this->reg_log_table = EnvConfig::MYSQL['datahub']['database'] . '.v2_dwd_game_uid_reg_log';
            $this->reg_log_map = ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_REG_LOG;
            $this->reg_time_field = 'game_reg_time';
        }
        $this->data_log_time_type = $this->aggregation_time;
        if ('cost_date' === $this->aggregation_time) {
            $this->reg_log_time_type = 'reg_date';
            $this->reg_log[] = ["DATE_FORMAT({$this->reg_time_field},'%Y-%m-%d') AS {$this->reg_log_time_type}"];
            //logic层格式化时间戳的格式
            $this->date_format = 'Y-m-d';
            //每条数据应有的的时间差
            $this->time_gap = 86400;
        }else {
            $this->reg_log_time_type = 'reg_date_hour';
            $this->reg_log[] = ["DATE_FORMAT({$this->reg_time_field},'%Y-%m-%d %H:00:00') AS {$this->reg_log_time_type}"];
            $this->date_format = 'Y-m-d H:i:s';
            $this->time_gap = 3600;
        }
        $this->main_select[] =
            ["IFNULL( reg_log.{$this->reg_log_time_type}, data_log.{$this->data_log_time_type} ) as {$this->data_log_time_type}"];

        $filter = array_pop($this->filter);

        switch ($this->media_type) {
            case MediaType::TOUTIAO:
                if ('rit_flow' === $this->flow_type) {
                    $this->toutiao_main_compute_map = ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_RIT_MAIN_SELECT;
                    $this->toutiao_data_log_map = ADAnalysisSqlMap::TOUTIAO_DETAIL_RIT_DATA_LOG;
                    $this->hour_data_log_table = 'ods_toutiao_rit_day_data_log';
                } else {
                    $this->toutiao_main_compute_map = ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_MAIN_SELECT;
                    $this->toutiao_data_log_map = ADAnalysisSqlMap::TOUTIAO_DETAIL_DATA_LOG;
                    $this->hour_data_log_table = 'ods_toutiao_creative_hour_data_log';
                }

                //获取要select的字段
                foreach ($this->target as $item) {
                    if (isset($this->toutiao_data_log_map[$item])) {
                        $this->hour_data_log[] = $this->toutiao_data_log_map[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TOUTIAO_AD_LOG[$item])) {
                        $this->ad_log[] = ADAnalysisSqlMap::TOUTIAO_AD_LOG[$item];
                    }
                    if (isset($this->reg_log_map[$item])) {
                        $this->reg_log[] = $this->reg_log_map[$item];
                    }
                    if (isset($this->toutiao_main_compute_map[$item])) {
                        $this->main_select[] = $this->toutiao_main_compute_map[$item];
                    }
                }
                unset($item);

//                $this->hour_data_log_table = 'ods_toutiao_inventory_hour_data_log';
                $this->ad_log_table = 'ods_toutiao_ad_log';
//                $this->first_ad_log_table = 'ods_toutiao_campaign_log';
//                $this->third_ad_log_table = 'ods_toutiao_creative_log';
//                $this->account_log_table = 'ods_toutiao_account_log';

                switch (true) {
                    case in_array('site_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.platform', 'data_log.site_id', "data_log.{$this->data_log_time_type}"];
                        $filter['column'] = 'platform-' . $filter['column'];
                        $this->reg_log_filter_condition = SqlParser::get($filter, '', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, '', 'data_log');
                        $this->reg_group_by = ['reg_log.platform', 'reg_log.site_id', $this->reg_log_time_type];
                        break;
                    case in_array('account_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.account_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, '', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, '', 'data_log');
                        $this->reg_group_by = ['reg_log.account_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad1_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.campaign_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'campaign_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'campaign_id', 'data_log');
                        $this->reg_group_by = ['reg_log.campaign_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad2_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.ad_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'adgroup_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'ad_id', 'data_log');
                        $this->reg_group_by = ['reg_log.adgroup_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad3_id', $this->dimension):
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'adcre_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'creative_id', 'data_log');
                        $this->reg_group_by = ['reg_log.adcre_id', $this->reg_log_time_type];
                        $this->hour_data_log_group_by = ['data_log.creative_id', "data_log.{$this->data_log_time_type}"];
                        break;
                }

                $this->join_ad_log = [
                    ['data_log.platform', '=', 'ad_log.platform'],
                    ['data_log.ad_id', '=', 'ad_log.ad_id'],
                ];
                break;
            case MediaType::TENCENT:
                //获取要select的字段
                foreach ($this->target as $item) {
                    if (isset(ADAnalysisSqlMap::TENCENT_DETAIL_DATA_LOG[$item])) {
                        $this->hour_data_log[] = ADAnalysisSqlMap::TENCENT_DETAIL_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::TENCENT_AD_LOG[$item])) {
                        $this->ad_log[] = ADAnalysisSqlMap::TENCENT_AD_LOG[$item];
                    }
                    if (isset($this->reg_log_map[$item])) {
                        $this->reg_log[] = $this->reg_log_map[$item];
                    }
                    if (isset(ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_MAIN_SELECT[$item])) {
                        $this->main_select[] = ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_MAIN_SELECT[$item];
                    }
                }
                unset($item);
//                $this->hour_data_log_table = 'ods_tencent_ad_site_set_day_data_log';
                $this->hour_data_log_table = 'ods_tencent_ad_hour_data_log';
                $this->ad_log_table = 'ods_tencent_adgroup_log';
//                $this->first_ad_log_table = 'ods_tencent_campaign_log';
//                $this->third_ad_log_table = 'ods_tencent_ad_log';
//                $this->account_log_table = 'ods_tencent_account_log';

                if (in_array('site_id', $this->dimension)) {
                    $this->hour_data_log_group_by = ['data_log.platform', 'data_log.site_id', "data_log.{$this->data_log_time_type}"];
                } elseif (in_array('account_id', $this->dimension)) {
                    $this->hour_data_log_group_by = ['data_log.account_id', "data_log.{$this->data_log_time_type}"];
                }elseif (in_array('ad1_id', $this->dimension)) {
                    $this->hour_data_log_group_by = ['data_log.campaign_id', "data_log.{$this->data_log_time_type}"];
                } elseif (in_array('ad2_id', $this->dimension)) {
                    $this->hour_data_log_group_by = ['data_log.adgroup_id', "data_log.{$this->data_log_time_type}"];
                } elseif (in_array('ad3_id', $this->dimension)) {
                    $this->hour_data_log_group_by = ['data_log.ad_id', "data_log.{$this->data_log_time_type}"];
                }

                switch (true) {
                    case in_array('site_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.platform', 'data_log.site_id', "data_log.{$this->data_log_time_type}"];
                        $filter['column'] = 'platform-' . $filter['column'];
                        $this->reg_log_filter_condition = SqlParser::get($filter, '', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, '', 'data_log');
                        $this->reg_group_by = ['reg_log.platform', 'reg_log.site_id', $this->reg_log_time_type];
                        break;
                    case in_array('account_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.account_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, '', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, '', 'data_log');
                        $this->reg_group_by = ['reg_log.account_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad1_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.campaign_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'campaign_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'campaign_id', 'data_log');
                        $this->reg_group_by = ['reg_log.campaign_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad2_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.adgroup_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'adgroup_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'adgroup_id', 'data_log');
                        $this->reg_group_by = ['reg_log.adgroup_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad3_id', $this->dimension):
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'adcre_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'ad_id', 'data_log');
                        $this->reg_group_by = ['reg_log.adcre_id', $this->reg_log_time_type];
                        $this->hour_data_log_group_by = ['data_log.ad_id', "data_log.{$this->data_log_time_type}"];
                        break;
                }

                $this->join_ad_log = [
                    ['data_log.platform', '=', 'ad_log.platform'],
                    ['data_log.adgroup_id', '=', 'ad_log.adgroup_id'],
                ];
                break;
            case MediaType::KUAISHOU:
                $this->hour_data_log_table = 'ods_kuaishou_creative_hour_data_log';

                //获取要select的字段
                foreach ($this->target as $item) {
                    if (isset(ADAnalysisSqlMap::KUAISHOU_DETAIL_DATA_LOG[$item])) {
                        $this->hour_data_log[] = ADAnalysisSqlMap::KUAISHOU_DETAIL_DATA_LOG[$item];
                    }
                    if (isset(ADAnalysisSqlMap::KUAISHOU_AD_LOG[$item])) {
                        $this->ad_log[] = ADAnalysisSqlMap::KUAISHOU_AD_LOG[$item];
                    }
                    if (isset($this->reg_log_map[$item])) {
                        $this->reg_log[] = $this->reg_log_map[$item];
                    }
                    if (isset(ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_MAIN_SELECT[$item])) {
                        $this->main_select[] = ADAnalysisSqlMap::AD_ANALYSIS_DETAIL_MAIN_SELECT[$item];
                    }
                }
                unset($item);

                $this->ad_log_table = 'ods_kuaishou_unit_log';

                switch (true) {
                    case in_array('site_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.platform', 'data_log.site_id', "data_log.{$this->data_log_time_type}"];
                        $filter['column'] = 'platform-' . $filter['column'];
                        $this->reg_log_filter_condition = SqlParser::get($filter, '', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, '', 'data_log');
                        $this->reg_group_by = ['reg_log.platform', 'reg_log.site_id', $this->reg_log_time_type];
                        break;
                    case in_array('account_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.account_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, '', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, '', 'data_log');
                        $this->reg_group_by = ['reg_log.account_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad1_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.campaign_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'campaign_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'campaign_id', 'data_log');
                        $this->reg_group_by = ['reg_log.campaign_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad2_id', $this->dimension):
                        $this->hour_data_log_group_by = ['data_log.ad_id', "data_log.{$this->data_log_time_type}"];
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'adgroup_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'ad_id', 'data_log');
                        $this->reg_group_by = ['reg_log.adgroup_id', $this->reg_log_time_type];
                        break;
                    case in_array('ad3_id', $this->dimension):
                        $this->reg_log_filter_condition = SqlParser::get($filter, 'adcre_id', 'reg_log');
                        $this->data_log_filter_condition = SqlParser::get($filter, 'creative_id', 'data_log');
                        $this->reg_group_by = ['reg_log.adcre_id', $this->reg_log_time_type];
                        $this->hour_data_log_group_by = ['data_log.creative_id', "data_log.{$this->data_log_time_type}"];
                        break;
                }

                $this->join_ad_log = [
                    ['data_log.platform', '=', 'ad_log.platform'],
                    ['data_log.ad_id', '=', 'ad_log.ad_id'],
                ];
                break;
        }
    }

}