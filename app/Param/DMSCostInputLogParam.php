<?php


namespace App\Param;


use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Model\SqlModel\Tanwan\V2DWDDayCostLogModel;
use App\Service\PermissionService;
use App\Utils\DimensionTool;

class DMSCostInputLogParam extends AbstractParam
{
    public $id;                    // id
    public $platform = '';         // 平台
    public $agent_id = 0;          // 渠道id
    public $game_id = 0;           // 游戏id
    public $site_id = 0;           // 广告位id
    public $begin_date;            // 开始日期
    public $end_date;              // 结束日期
    public $money = 0;             // 实际金额
    public $ori_money = 0;         // 原始金额
    public $remark = '';           // 备注
    public $money_type = 0;        // 消耗类型 1消耗  2返货 3调平 4达人消耗
    public $type = 1;              // 1=批量导入 2=手动录入
    public $days = 0;              // 一共有几天
    public $page = 1;              // 页码
    public $rows = 200;            // 分页大小
    public $creator;               // 创建人
    public $editor;                // 修改人
    public $operator;              // 最后操作人
    public $tdate = '';            // 单个日期
    public $old_tdate = '';        // 旧日期
    public $old_money_type = '';   // 旧返货类型
    public $add_type;              // 录入类型 自动=2，3，14，15 其他是手动
    public $aweme_account = '';    // 抖音号
    public $interface_person = ''; // 负责人
    public $cost_input_id = '';    // 消耗录入自己的请求id

    public $game_permission = [];                   // 游戏权限
    public $agent_permission = [];                  // agent权限
    public $dimension_filter = [];                  // 处理后的维度筛选
    public $dimension_filter_raw = [];              // 处理前的维度筛选指标


    public function paramHook()
    {
        $this->dimension_filter_raw = $this->dimension_filter;
        $this->dimension_filter = DimensionTool::handleDimensionFilter($this->dimension_filter);   // 处理维度筛选

        if (!empty($this->begin_date) && !empty($this->end_date)) {
            $this->begin_date = strtotime(date("Y-m-d", strtotime($this->begin_date)));
            $this->end_date = strtotime(date("Y-m-d", strtotime($this->end_date)));
            $this->days = ceil(($this->end_date - $this->begin_date) / 86400) + 1;
        }

        $this->aweme_account = trim($this->aweme_account);
        $this->interface_person = trim($this->interface_person);

//        $this->ori_money = $this->ori_money ?? 0;
//        $this->money = $this->money ?? 0;
//        // 判断录入的金额是否是数字
//        if (!is_numeric($this->money) || !is_numeric($this->ori_money)) {
//            throw new AppException('录入的金额必须是数字');
//        }
    }


    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
        $this->agent_permission = $permission['agent_permission'];
    }

    /**
     * 验证一些必要的参数是否为空
     *
     * @return bool
     */
    public function requireField()
    {
        if ($this->platform == '') {
            return false;
        }
        if (!$this->agent_id > 0) {
            return false;
        }
        if (!$this->game_id > 0) {
            return false;
        }
        if (!$this->site_id > 0) {
            return false;
        }
        if ($this->tdate == '') {
            return false;
        }
        return true;
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->game_permission = $user_permission['game_permission'];
        $this->agent_permission = $user_permission['agent_permission'];
    }
}
