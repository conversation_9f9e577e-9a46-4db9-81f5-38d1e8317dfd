<?php

namespace App\Param\Knowledge;

use App\Param\AbstractParam;

/**
 * 添加向量数据库的知识库所需要的参数
 */
class VikingDBParam extends AbstractParam
{
    /**
     * 文档信息
     *
     * @var array
     */
    public $doc_info;

    /**
     * 文档id
     *
     * @var string
     */
    public $doc_id;

    /**
     * 群名称
     *
     * @var string
     */
    public $chat_name;

    /**
     * 总结内容
     *
     * @var string
     */
    public $summary;

    /**
     * 群id
     *
     * @var string
     */
    public $chat_id;


    /**
     * 开始时间
     *
     * @var string
     */
    public $start_date;

    /**
     * 结束时间
     *
     * @var string
     */
    public $end_date;

    /**
     * 开始时间戳
     *
     * @var int
     */
    public $start_timestamp;

    /**
     * 结束时间戳
     *
     * @var int
     */
    public $end_timestamp;

    /**
     * 文档名称
     *
     * @var string
     */
    public $doc_name;

    /**
     * 类型
     *
     * @var string
     */
    public $type;

    public function setDocName()
    {
        $this->doc_name = $this->chat_name . uniqid('-');
    }

}