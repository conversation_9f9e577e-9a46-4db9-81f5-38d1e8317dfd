<?php
/**
 * Created by PhpStorm.
 * User: hsiao
 * Date: 2023/2/27
 * Time: 10:23
 * Desc:
 */

namespace App\Param\ADMonetization;


use App\Constant\PlatId;
use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Param\AbstractParam;
use Common\EnvConfig;

class ADMonetizationPlanParam extends AbstractParam
{
    public $setting_id;
    public $plan_id;
    public $plan_name;
    public $platform;
    public $game_id;
    public $click_url;
    public $monitor_url;
    public $show_url;
    public $monitor_type;
    public $monitor_game_info;
    public $card_title;
    public $card_describe;
    public $button_name;
    public $card_icon;
    public $package_name;
    public $video;
    public $show_time;
    public $state;
    public $type;
    public $creator;
    public $creator_id;
    public $editor;
    public $editor_id;
    public $create_time;
    public $update_time;


    protected function paramHook()
    {
        if ($this->state == null) {
            $this->state = 1;
        }

        if(!empty($this->card_icon) && is_string($this->card_icon)) {
            $this->card_icon = json_decode($this->card_icon,true);
        }

        if(!empty($this->video) && is_string($this->video)) {
            $this->video = json_decode($this->video,true);
        }

        if(!empty($this->monitor_game_info) && is_string($this->monitor_game_info)) {
            $this->monitor_game_info = json_decode($this->monitor_game_info,true);
        }

        if (!is_null($this->monitor_game_info) && !is_array($this->monitor_game_info)) {
            throw new AppException('游戏信息数据出错');
        }


        $this->monitor_type = $this->monitor_game_info['plat_id'] != PlatId::MINI ? 1 : 2;

        if(!isset($this->monitor_game_info['platform']) || $this->platform != $this->monitor_game_info['platform']) {
            throw new AppException('计划游戏必须跟流量组相同平台');
        }

        if ($this->monitor_game_info['os'] === 'IOS' && $this->monitor_game_info['plat_id'] != PlatId::MINI) {
            if (empty($this->monitor_game_info['app_id'])) {
                throw new AppException(
                    "缺少游戏 {$this->monitor_game_info['label']} 的appid，请{$this->platform}平台补充上传");
            }
            // IOS资源链接赋值苹果商店链接
            $this->monitor_url = "https://itunes.apple.com/cn/app/id{$this->monitor_game_info['app_id']}?mt=8";
        }

        if(empty(trim($this->monitor_url))) {
            throw new AppException('请填写资源链接');
        }

        if (!is_null($this->card_icon) && !is_array($this->card_icon)) {
            throw new AppException('卡片ICON数据出错');
        }

        if (!is_null($this->video) && !is_array($this->video)) {
            throw new AppException('视频数据出错');
        }

        if (!is_null($this->card_icon) && is_array($this->card_icon)) {
            if (!isset($this->card_icon['signature'])) {
                throw new AppException('卡片ICON签名出错');
            }
        }

        if (($len = mb_strlen($this->card_title)) > 18) {
            throw new AppException('卡片标题不能超过18个字，当前长度：'.$len);
        }
        if (($len = mb_strlen($this->card_describe)) > 40) {
            throw new AppException('卡片简介不能超过40个字，当前长度：'.$len);
        }
        if (($len = mb_strlen($this->button_name)) > 4) {
            throw new AppException('卡片按钮文案不能超过4个字，当前长度：'.$len);
        }


        if (!is_null($this->video) && is_array($this->video)) {
            if (isset($this->video['signature'])) {
                $this->video['video_hash'] = $this->video['signature'];
            } else {
                throw new AppException('视频签名出错');
            }

            if (EnvConfig::ENV === 'production') {
                if(isset($this->video['size']) && $this->video['size'] / 1024 / 1024 > 20) {
                    throw new AppException('视频素材大小不得超过 20 MB');
                }
            }
        }

        // 判断流量组和计划是否同个根游戏
        if ((new V2DimGameIdModel())->judgeSameRootGameId($this->platform, $this->game_id,
            $this->monitor_game_info['game_id'] ?? 0)) {
            throw new AppException('流量组配置游戏和计划游戏不能是同个根游戏');
        }

        $user_id = Container::getSession()->user_id;
        $user_name = Container::getSession()->name;
        $this->editor_id = $user_id;
        $this->editor = $user_name;
        $this->update_time = time();
        if (!$this->create_time) {
            $this->creator_id = $user_id;
            $this->creator = $user_name;
            $this->create_time = time();
        } else {
            unset($this->creator_id);
            unset($this->creator);
            unset($this->create_time);
        }
    }


    public function toData()
    {
        $data = $this->toArray();

        $data['card_icon'] = json_encode($data['card_icon'], JSON_UNESCAPED_UNICODE);
        $data['video'] = json_encode($data['video'], JSON_UNESCAPED_UNICODE);
        $data['monitor_game_info'] = json_encode($data['monitor_game_info'], JSON_UNESCAPED_UNICODE);

        return $data;
    }

}
