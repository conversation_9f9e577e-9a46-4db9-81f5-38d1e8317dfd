<?php
/**
 * 广告变现-广告组
 * Created by PhpStorm.
 * User: hsiao
 * Date: 2023/2/24
 * Time: 10:27
 * Desc:
 */

namespace App\Param\ADMonetization;


use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class ADMonetizationGroupParam extends AbstractParam
{
    public $setting_id;
    public $setting_name;
    public $platform;
    public $game_id;
//    public $game_type;
    public $proportion;
    public $state = 1;
    public $creator;
    public $creator_id;
    public $editor;
    public $editor_id;
    public $create_time;
    public $update_time;

    // public $is_edit = 0; // 是否编辑 1=是 0=否


    protected function paramHook()
    {
        if (!is_null($this->proportion) && !is_numeric($this->proportion)) {
            throw new AppException('proportion必须为数字');
        }
        if ($this->proportion < 0 || $this->proportion > 100) {
            throw new AppException('proportion必须在0~100区间');
        }

        $user_id = Container::getSession()->user_id;
        $user_name = Container::getSession()->name;
        $this->editor_id = $user_id;
        $this->editor = $user_name;
        $this->update_time = time();
        if (!$this->create_time) {
            $this->creator_id = $user_id;
            $this->creator = $user_name;
            $this->create_time = time();
        } else {
            unset($this->creator_id);
            unset($this->creator);
            unset($this->create_time);
        }
    }

}
