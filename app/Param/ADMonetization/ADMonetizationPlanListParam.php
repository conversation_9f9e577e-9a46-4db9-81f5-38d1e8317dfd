<?php
/**
 * Created by PhpStorm.
 * User: hsiao
 * Date: 2023/2/27
 * Time: 12:21
 * Desc:
 */

namespace App\Param\ADMonetization;


use App\Param\AbstractParam;

class ADMonetizationPlanListParam extends AbstractParam
{
    public $page;
    public $rows;
    public $create_start_time = 0;
    public $create_end_time = 0;
    public $order;

    public $plan_id;
    public $setting_id;
    public $plan_name;
    public $platform;
    public $game_id;
    public $state;
    public $type;
    public $creator;

    protected function paramHook()
    {
        if (!$this->page) {
            $this->page = 1;
        }
        if (!$this->rows) {
            $this->rows = 50;
        }
    }
}
