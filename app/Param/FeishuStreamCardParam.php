<?php

namespace App\Param;

class FeishuStreamCardParam extends AbstractParam
{
    /**
     * 卡片id,调用创建卡片实体的接口后会返回的
     *
     * @var string
     */
    public $card_id = '';

    /**
     * 序列号，要自己维护自增
     *
     * @var string
     */
    public $sequence = 1;


    /**
     * 卡片内容
     *
     * @var string
     */
     protected $content;

    /**
     * token
     *
     * @var string
     */
    public $tenant_access_token;

    /**
     * 接受者id，要跟下面的类型能对应起来
     *
     * @var string
     */
    public $receive_id;

    /**
     * 接受的id类型
     *
     * @var string
     */
    public $receive_id_type;

    /**
     * 摘要（自动根据 content 生成）
     *
     * @var string
     */
    protected $summary = '深度思考中...';

    /**
     * 设置卡片内容并自动更新摘要
     *
     * @param string $content
     */
    public function setContent(string $content): void
    {
        $this->content = $content;
        $this->updateSummary();
    }

    /**
     * 获取卡片内容
     *
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    public function getSummary(): string
    {
        return $this->summary;
    }



    /**
     * 更新摘要（截取 content 前 20 字符）
     */
    protected function updateSummary(): void
    {
        if (empty($this->content)) {
            $this->summary = '深度思考'; // 默认值
            return;
        }

        // 截取中英文混合内容（最多20字符）
        $this->summary = mb_substr(ltrim($this->content), 0, 20, 'UTF-8');
    }
}