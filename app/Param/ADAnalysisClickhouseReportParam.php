<?php

namespace App\Param;


use App\Constant\ADAnalysisClickhouseSqlMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Utils\ClickhouseDict;
use App\Utils\ClickhouseSqlParser;

class ADAnalysisClickhouseReportParam extends AbstractParam
{
    //数据库类型 1:ADB 2:ClickHouse
    public $database_type = '';
    //所选媒体
    public $media_type = 0;
    //处理后的维度
    public $dimension = [];
    // 所有指标
    public $target = [];
    // 所有筛选
    public $filter = [];
    // 所有数值条件
    public $calc = [];
    // 当前聚合等级
    public $level = 0;
    //数值条件之间关系
    public $calc_condition = '';
    // 所排序条件
    public $order = [];
    // 消耗日期
    public $cost_date;
    //归因方式 1:广告点击归因，2:广告位归因
    public $attribution_type;
    //统计方式，1：按子 2：按根
    public $statistic_base_on = 2;
    // 创建日期
    public $ad2_create_time;
    // 游戏权限
    public $game_permission;
    // 渠道权限
    public $agent_permission;
    //素材效果权限
    public $material_effect_permission;
    //素材可见性权限
    public $material_effect_visible_permission;
    //用户类型（区分市场和设计）
    public $user_type;
    //下属权限
    public $user_list;
    //真实消耗
    public $true_cost;
    //每次获取条数
    public $limit = 50000;
    //要过滤的字段
    private $filter_fields = [];
    //计算数值的字段
    private $calc_fields = [];
    //外层having
    public $main_calc = [];
    //所有字段
    public $all_fields = [];
    //ad_log的字段
    public $ad_log_fields = [];
    //data_log的字段
    public $data_log_fields = [];
    //overview_log的字段
    public $overview_log_fields = [];
    //次外层主sql查询字段
    public $sub_main_fields = [];
    //最外层主sql查询字段
    public $main_fields = [];
    //account_log的字段
    public $account_log_fields = [];
    //first_ad_log的字段
    public $first_ad_log_fields = [];
    //third_ad_log的字段
    public $third_ad_log_fields = [];
    //为了union, ad_log子查询额外查询的字段
    public $ad_log_fields_for_union = [];

    public $ad_log_table = '';
    public $first_ad_log_table = '';
    public $third_ad_log_table = '';
    public $account_log_table = '';
    public $data_log_table = '';
    public $overview_log_table = '';
    public $material_log_table = '';
    public $material_file_log_table = '';
    public $calc_bind_log_table = '';
    public $convert_log_table = '';
    public $agency_change_log_table = '';
    public $site_table = '';
    //根据不同广告层级切换开关所用字段
    public $switch_select = [];
    //根据不同广告层级切换二级广告创建时间字段
    public $ad_log_create_time_field = '';
    public $data_log_create_time_field = '';
    public $overview_log_create_time_field = '';
    //三级筛选
    public $third_ad_log_filter = [];
    //是否以account来控制权限
    public $ctrl_by_account = false;
    //聚合字段
    public $ad_log_group_by = [];
    public $data_log_group_by = [];
    public $overview_log_group_by = [];
    public $sub_main_group_by = [];
    public $main_group_by = [];
    public $sub_data_log_group_by = ['cost_date'];
    public $sub_overview_log_group_by = ['cost_date'];
    public $calc_standard_sub_main_log_group_by = ['cost_date'];
    public $ad_log_filter = [];
    public $data_log_filter = [];
    public $overview_log_filter = [];
    public $game_filter = [];
    public $calc_bind_rule_filter = [];
    public $join_calc_bind = false;
    public $join_calc_bind_on = [];
    public $join_calc_bind_where = [];
//    public $agent_site_filter = [];
    //操作符转换
    private $transfer_operator = ['egt' => '>=', 'elt' => '<=', 'between' => 'BETWEEN'];
    //子查询alias
    private $sub_sql_alias = ['ad_log', 'data_log', 'overview_log'];
    //需要计算百分比的字段
    public $all_calculate_percentage = [];
    //需要相除的字段
    public $all_calculate_division = [];
    //需要计算平均值的字段
    public $all_calculate_average = [];
    //需要单纯累加但是要保留两位小数的字段
    public $all_need_keep_two_decimal = [];
    //所有需要计算的字段
    public $all_need_calculate = [];
    //需要转化成中文的字段
    public $all_translate_to_cn = [];
    public $calc_standard_sub_data_log_fields = [];
    public $calc_standard_sub_overview_log_fields = [];
    public $calc_standard_sub_main_log_fields = [];
    public $calc_standard_main_log_fields = [];
    //广告位归因时overview需要的where条件
    public $where_by_site = [];
    /**
     * @var array
     */
    public $main_order_select = [];
    /**
     * @var array
     */
    public $main_order_by = [];


    /**
     * 处理不同media_type的select group by where数据
     */
    public function paramHook()
    {
        //没选cost_date直接抛异常
        if (empty($this->cost_date)) {
            throw new AppException('请选择消耗时间');
        }

        $this->cost_date[0] = date("Y-m-d", strtotime($this->cost_date[0]));
        $this->cost_date[1] = date("Y-m-d", strtotime($this->cost_date[1]));
//        $this->cost_date[0] = ;
        //过滤转换media_type
        $this->handleMediaType();
        //临时
        if (0 !== (int)$this->media_type) {
            throw new AppException("实时查询仅支持通用媒体类型");
        }
        //获取过滤字段
        if (!empty($this->filter)) {
            foreach ($this->filter as $value) {
                $this->filter_fields[] = $value['column'];
            }
            unset($value);
        }
        //组合having语句、获取数值字段、设置参数绑定
        if (!empty($this->calc)) {
            $this->calc_condition = $this->calc['condition'];
            unset($this->calc['condition']);
            foreach ($this->calc as $v) {
                $this->calc_fields[] = $v['column'];
                $v['operator'] = $this->transfer_operator[$v['operator']] ?? $v['operator'];
                $this->main_calc[] = $v;
            }
            unset($v);
        }

        //将各种name替换成对应的ID，name不需要聚合
        foreach (ADAnalysisClickhouseSqlMap::REAL_GROUP_BY as $select => $real) {
            $index = array_search($select, $this->dimension);
            if (false !== $index) {
                $this->dimension[$index] = $real;
            }
        }
        unset($real);
        $this->dimension = array_unique($this->dimension);

        //合并所有字段
        $this->all_fields = array_merge(
            $this->target,
            $this->dimension,
//            $this->filter_fields,
            $this->calc_fields,
            ADAnalysisClickhouseSqlMap::MUST_HAVE_NO_MATTER_WHAT
        );

        //根据所选指标放入扩展字段
        foreach ($this->all_fields as $item) {
            if (isset(ADAnalysisClickhouseSqlMap::EXTENSION_FIELDS[$item])) {
                $this->all_fields = array_merge(
                    $this->all_fields,
                    ADAnalysisClickhouseSqlMap::EXTENSION_FIELDS[$item]
                );
            }
        }

        if (!array_diff($this->dimension, ADAnalysisClickhouseSqlMap::PERMISSION_CTRL_BY_ACCOUNT_DIMENSION) && array_intersect($this->target, ADAnalysisClickhouseSqlMap::PERMISSION_CTRL_BY_ACCOUNT_TARGET) && !array_diff($this->filter_fields, ADAnalysisClickhouseSqlMap::PERMISSION_CTRL_BY_ACCOUNT_DIMENSION)) {
            $this->ctrl_by_account = true;
        } else {
            $this->all_fields[] = 'cost';
        }

        //去重
        $this->all_fields = array_flip(array_flip($this->all_fields));

        //!!以下方法调用分先后顺序!!
        $this->getTable();
        $this->formatTargetData();
        $this->formatDimensionData();
        $this->formatFilterData();
        $this->handleSpecialConditions();
        $this->commonPartOrderBy();
    }

    /**
     * 公共排序字段
     */
    private function commonPartOrderBy()
    {
        if ($this->order) {
            $business_dimension = ['agent_leader', 'site_id', 'os', 'game_id', 'main_game_id', 'root_game_id', 'clique_id'];
            foreach ($this->order as $order) {
                if (isset($order['target']) && isset($order['dimension']) && isset($order['sort'])) {
                    if (isset(ADAnalysisClickhouseSqlMap::ORDER_SELECT[$order['target']])) {
//                        $field_take_from = 'ad_log';
//                        if (in_array($order['target'], ADAnalysisClickhouseSqlMap::BUSINESS_TARGET)) {
//                            $field_take_from = 'overview_log';
//                        }
                        $partition_by_str = $order['dimension'];

                        if (in_array($order['dimension'], $business_dimension)) {
                            $partition_by_str = $order['dimension'] . ', platform';
                        }

                        $this->main_order_select[] = 'SUM(' . ADAnalysisClickhouseSqlMap::ORDER_SELECT[$order['target']][0] . ') OVER ( PARTITION BY ' . $partition_by_str . ') AS ' . $order['target'] . '_partition_by_' . $order['dimension'];

                        $this->main_order_by[] = $order['target'] . '_partition_by_' . $order['dimension'] . ' ' . $order['sort'];
                    }
                }
            }
        }
    }

    private function handleMediaType()
    {
        if (0 === (int)$this->media_type &&
            isset($this->filter['media_type']) && 1 === count($this->filter['media_type']['value']) &&
            in_array((int)$this->filter['media_type']['value'][0], [MediaType::TOUTIAO, MediaType::TENCENT, MediaType::BAIDU])) {
            $this->media_type = (int)$this->filter['media_type']['value'][0];
        } else {
            $this->media_type = (int)$this->media_type;
        }

        if (!in_array($this->media_type, [0, MediaType::TOUTIAO, MediaType::TENCENT, MediaType::BAIDU])) {
            throw new AppException('media_type参数错误');
        }
    }

    /**
     * 整合要查询的字段
     */
    private function formatTargetData()
    {
        $all_ad_log = array_merge(
            ADAnalysisClickhouseSqlMap::AD_LOG[$this->media_type],
            ADAnalysisClickhouseSqlMap::HAVE_NOT_BUT_NEED_SELECT,
//            ADAnalysisClickhouseSqlMap::HAVE_NOT_BUT_NEED_SELECT['overview_log']
        );
//        $all_data_log = array_merge(
//            ADAnalysisClickhouseSqlMap::DATA_LOG[$this->media_type],
//            ADAnalysisClickhouseSqlMap::HAVE_NOT_BUT_NEED_SELECT['ad_log'],
//            ADAnalysisClickhouseSqlMap::HAVE_NOT_BUT_NEED_SELECT['overview_log']
//        );
//        $all_overview_log = array_merge(
//            ADAnalysisClickhouseSqlMap::OVERVIEW_LOG,
//            ADAnalysisClickhouseSqlMap::HAVE_NOT_BUT_NEED_SELECT['ad_log'],
//            ADAnalysisClickhouseSqlMap::HAVE_NOT_BUT_NEED_SELECT['data_log']
//        );
        foreach ($this->all_fields as $item) {
            if (isset($all_ad_log[$item])) {
                $this->ad_log_fields[$item] = $all_ad_log[$item];
            }
//            if (isset(ADAnalysisClickhouseSqlMap::AD_LOG_SELECT_FOR_UNION[$item])) {
//                $this->ad_log_fields[$item] = ADAnalysisClickhouseSqlMap::AD_LOG_SELECT_FOR_UNION[$item];
//            }
            if (isset(ADAnalysisClickhouseSqlMap::DATA_LOG[$this->media_type][$item])) {
                $this->data_log_fields[$item] = ADAnalysisClickhouseSqlMap::DATA_LOG[$this->media_type][$item];
            }
            if (isset(ADAnalysisClickhouseSqlMap::OVERVIEW_LOG[$item])) {
                $this->overview_log_fields[$item] = ADAnalysisClickhouseSqlMap::OVERVIEW_LOG[$item];
            }
//            if (isset(ADAnalysisClickhouseSqlMap::SUB_MAIN_SELECT[$item])) {
//                $this->sub_main_fields[$item] = ADAnalysisClickhouseSqlMap::SUB_MAIN_SELECT[$item];
//            }
            if (isset(ADAnalysisClickhouseSqlMap::MAIN_SELECT[$item])) {
                $this->main_fields[$item] = ADAnalysisClickhouseSqlMap::MAIN_SELECT[$item];
            }
            if (isset(ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_DATA_LOG[$this->media_type][$item])) {
                $this->calc_standard_sub_data_log_fields[$item] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_DATA_LOG[$this->media_type][$item];
            }
            if (isset(ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_OVERVIEW_LOG[$item])) {
                $this->calc_standard_sub_overview_log_fields[$item] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_OVERVIEW_LOG[$item];
            }
            if (isset(ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_MAIN_SELECT[$item])) {
                $this->calc_standard_sub_main_log_fields[$item] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_MAIN_SELECT[$item];
            }
            if (isset(ADAnalysisClickhouseSqlMap::CALC_STANDARD_MAIN_SELECT[$item])) {
                $this->calc_standard_main_log_fields[$item] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_MAIN_SELECT[$item];
            }
//            if (isset(ADAnalysisClickhouseSqlMap::MAIN_COMPUTE[$this->media_type][$item])) {
//                $this->main_compute[$item] = ADAnalysisClickhouseSqlMap::MAIN_COMPUTE[$this->media_type][$item];
//            }
//            if (isset(ADAnalysisClickhouseSqlMap::ACCOUNT_LOG[$this->media_type][$item])) {
//                $this->account_log_fields[$item] = ADAnalysisClickhouseSqlMap::ACCOUNT_LOG[$this->media_type][$item];
//            }
//            if (isset(ADAnalysisClickhouseSqlMap::FIRST_AD_LOG[$this->media_type][$item])) {
//                $this->first_ad_log_fields[$item] = ADAnalysisClickhouseSqlMap::FIRST_AD_LOG[$this->media_type][$item];
//            }
//            if (isset(ADAnalysisClickhouseSqlMap::THIRD_AD_LOG[$this->media_type][$item])) {
//                $this->third_ad_log_fields[$item] = ADAnalysisClickhouseSqlMap::THIRD_AD_LOG[$this->media_type][$item];
//            }
//            if (isset(ADAnalysisClickhouseSqlMap::JOIN_AD_LOG_SELECT[$this->media_type][$item])) {
//                $this->join_ad_log_select[$item] = ADAnalysisClickhouseSqlMap::JOIN_AD_LOG_SELECT[$this->media_type][$item];
//            }

//            if (isset(ADAnalysisClickhouseSqlMap::MATERIAL_FILE_LOG[$item])) {
//                $this->material_file_log_fields[$item] = ADAnalysisClickhouseSqlMap::MATERIAL_FILE_LOG[$item];
//            }
//            if (isset(ADAnalysisClickhouseSqlMap::MATERIAL_LOG[$item])) {
//                $this->material_log_fields[$item] = ADAnalysisClickhouseSqlMap::MATERIAL_LOG[$item];
//            }
        }
        unset($item);
    }

    /**
     * 整合要聚合的字段
     */
    private function formatDimensionData()
    {
        //TODO::AD_LOG_GROUP_BY做公用的，data log和overview合进去
        $general_group_by = [];
        foreach ($this->dimension as $item) {
            if (isset(ADAnalysisClickhouseSqlMap::GENERAL_GROUP_BY[$this->media_type][$item])) {
                $general_group_by[$item] = ADAnalysisClickhouseSqlMap::GENERAL_GROUP_BY[$this->media_type][$item];
            }
//            if (isset(ADAnalysisClickhouseSqlMap::AD_LOG_GROUP_BY[$this->media_type][$item])) {
//                $this->ad_log_group_by[$item] = ADAnalysisClickhouseSqlMap::AD_LOG_GROUP_BY[$this->media_type][$item];
//            }
            if (isset(ADAnalysisClickhouseSqlMap::DATA_LOG_GROUP_BY[$this->media_type][$item])) {
                $this->data_log_group_by[$item] = ADAnalysisClickhouseSqlMap::DATA_LOG_GROUP_BY[$this->media_type][$item];
            }
//            if (isset(ADAnalysisClickhouseSqlMap::OVERVIEW_LOG_GROUP_BY[$item])) {
//                $this->overview_log_group_by[$item] = ADAnalysisClickhouseSqlMap::OVERVIEW_LOG_GROUP_BY[$item];
//            }
//            if (isset(ADAnalysisClickhouseSqlMap::JOIN_AD_LOG_GROUP_BY[$item])) {
//                $this->join_ad_log_group_by[] = ADAnalysisClickhouseSqlMap::JOIN_AD_LOG_GROUP_BY[$item];
//            }
        }
        unset($item);
        $this->ad_log_group_by = $general_group_by;
        $this->data_log_group_by = array_merge($general_group_by, $this->data_log_group_by);
        $this->overview_log_group_by = $general_group_by;
        $this->sub_main_group_by = $general_group_by;
        $this->main_group_by = $general_group_by;
    }

    /**
     * 整合where查询条件
     */
    private function formatFilterData()
    {
        ClickhouseSqlParser::$dict_column[] = 'game_id';
        ClickhouseSqlParser::$dict_column[] = 'material_file_id';
        ClickhouseSqlParser::$dict_column[] = 'material_file_name';
        ClickhouseSqlParser::$dict_column[] = 'material_id';
        ClickhouseSqlParser::$dict_column[] = 'material_name';
        ClickhouseSqlParser::$dict_column[] = 'material_file_type';
        $ad_log_where = '';
        $data_log_where = '';
        $overview_log_where = '';
        foreach ($this->filter as $filter) {
            if (isset(ADAnalysisClickhouseSqlMap::AD_LOG_FILTER[$this->media_type][$filter['column']])) {

                $ad_log_where = ClickhouseSqlParser::get(
                    $filter,
                    ADAnalysisClickhouseSqlMap::AD_LOG_FILTER[$this->media_type][$filter['column']],
                    'ad_log'
                );
            }

            if (isset(ADAnalysisClickhouseSqlMap::DATA_LOG_FILTER[$this->media_type][$filter['column']])) {
                $data_log_where = ClickhouseSqlParser::get(
                    $filter,
                    ADAnalysisClickhouseSqlMap::DATA_LOG_FILTER[$this->media_type][$filter['column']],
                    'data_log'
                );
            }

            if (isset(ADAnalysisClickhouseSqlMap::OVERVIEW_LOG_FILTER[$this->media_type][$filter['column']])) {
                    $overview_log_where = ClickhouseSqlParser::get(
                        $filter,
                        ADAnalysisClickhouseSqlMap::OVERVIEW_LOG_FILTER[$this->media_type][$filter['column']],
                        'overview_log'
                    );
                }

            //os字段重复， 为了防止在ClickhouseSqlParser中被加入dict-前缀
            if ('ad2_os' === $filter['column']) {
                $ad_log_where = str_replace('dict-', 'ad_log.', $ad_log_where);
                $data_log_where = str_replace('dict-', 'data_log.', $data_log_where);
                $overview_log_where = str_replace('dict-', 'overview_log.', $overview_log_where);
            }

            if ($ad_log_where) {
                $this->ad_log_filter[] = $ad_log_where;
            }
            if ($data_log_where) {
                $this->data_log_filter[] = $data_log_where;
            }
            if ($overview_log_where) {
                $this->overview_log_filter[] = $overview_log_where;
            }

            if (isset(ADAnalysisClickhouseSqlMap::AGENT_SITE_FILTER[$filter['column']])) {

                foreach ($this->sub_sql_alias as $sql_alias) {
                    $agent_where = ClickhouseSqlParser::get(
                        $filter,
                        ADAnalysisClickhouseSqlMap::AGENT_SITE_FILTER[$filter['column']],
                        $sql_alias
                    );
                    $to_as_table = $sql_alias . '_table';
                    $agent_filter_str = ClickhouseDict::dictSelect('agent', $filter['column'], true, $sql_alias, '', $this->$to_as_table);
                    $agent_filter = str_replace('dict-' . $filter['column'], $agent_filter_str, $agent_where);
                    $filter_arr_name = $sql_alias . '_filter';
                    $this->$filter_arr_name[] = $agent_filter;
                }
            }

//            if (isset(ADAnalysisClickhouseSqlMap::SITE_FILTER[$filter['column']])) {
//
//                foreach ($this->sub_sql_alias as $sql_alias) {
//                    $site_where = ClickhouseSqlParser::get(
//                        $filter,
//                        ADAnalysisClickhouseSqlMap::SITE_FILTER[$filter['column']],
//                        $sql_alias
//                    );
//                    $to_as_table = $sql_alias . '_table';
//                    $site_filter_str = ClickhouseDict::dictSelect('site', $filter['column'], true, $sql_alias, '', $this->$to_as_table);
//                    $site_filter = str_replace('dict-' . $filter['column'], $site_filter_str, $site_where);
////                var_dump($ad_log_agent_filter);
//                    $filter_arr_name = $sql_alias . '_filter';
//                    $this->$filter_arr_name[] = $site_filter;
//                }
//            }

            if (isset(ADAnalysisClickhouseSqlMap::GAME_FILTER[$filter['column']])) {
                $where_str = ClickhouseDict::dictSelect('game', $filter['column'], true);

                foreach ($this->sub_sql_alias as $sql_alias) {
                    $game_where = ClickhouseSqlParser::get(
                        $filter,
                        ADAnalysisClickhouseSqlMap::GAME_FILTER[$filter['column']],
                        $sql_alias
                    );
                    $game_filter = str_replace('dict-' . $filter['column'], $where_str, $game_where);
                    $filter_arr_name = $sql_alias . '_filter';
                    $this->$filter_arr_name[] = $game_filter;
                }
            }

            if (isset(ADAnalysisClickhouseSqlMap::MATERIAL_FILTER[$filter['column']])) {
                $filter_column = $filter['column'];
                $filter['column'] = 'material_id' === $filter['column'] ? 'platform-material_id' : $filter['column'];

                foreach ($this->sub_sql_alias as $sql_alias) {
                    $material_where = ClickhouseSqlParser::get(
                        $filter,
                        '',
                        $sql_alias
                    );
                    $material_filter_str = ClickhouseDict::dictSelect('material', ADAnalysisClickhouseSqlMap::MATERIAL_FILTER[$filter_column], true, $sql_alias);
                    $material_filter = str_replace('dict-' . $filter_column, $material_filter_str, $material_where);
                    $filter_arr_name = $sql_alias . '_filter';
                    $this->$filter_arr_name[] = $material_filter;
                }
            }

            if (isset(ADAnalysisClickhouseSqlMap::MATERIAL_SIZE_FILTER[$filter['column']])) {
                $material_size_where = ClickhouseSqlParser::get(
                    $filter,
                    ADAnalysisClickhouseSqlMap::MATERIAL_SIZE_FILTER[$filter['column']],
                );

                $this->ad_log_filter[] = $material_size_where;
                $this->data_log_filter[] = $material_size_where;
                $this->overview_log_filter[] = $material_size_where;
            }

            if (isset(ADAnalysisClickhouseSqlMap::CALC_BIND_RULE_FILTER[$filter['column']])) {
                $where = ClickhouseSqlParser::get($filter, 'calc_bind');
                $this->calc_bind_rule_filter[] = $where;
            }

            if ('calc_rule_bind_status' === $filter['column']) {
                $this->join_calc_bind = true;
                if (0 === (int)$filter['value']) {
                    $this->join_calc_bind_where = ['calc_bind.calc_rule_id', 0];
                } else {
                    $this->join_calc_bind_where = ['calc_bind.status', 1];
                }
            }
        }
        unset($filter);
    }

    /**
     * 处理特殊条件
     */
    private function handleSpecialConditions()
    {
        if (3 === (int)$this->level) {
            $this->switch_select[] = ADAnalysisClickhouseSqlMap::SWITCH[3][$this->media_type];
            $must_have_fields = array_merge(ADAnalysisClickhouseSqlMap::MUST_HAVE_NO_MATTER_WHAT, ['ad3_id', 'ad2_id', 'ad1_id']);
        } elseif (2 === (int)$this->level) {
            $this->switch_select[] = ADAnalysisClickhouseSqlMap::SWITCH[2][$this->media_type];
            $must_have_fields = array_merge(ADAnalysisClickhouseSqlMap::MUST_HAVE_NO_MATTER_WHAT, ['ad2_id', 'ad1_id']);
        } elseif (1 === (int)$this->level) {
            $this->switch_select[] = ADAnalysisClickhouseSqlMap::SWITCH[1][$this->media_type];
            $must_have_fields = array_merge(ADAnalysisClickhouseSqlMap::MUST_HAVE_NO_MATTER_WHAT, ['ad1_id']);
        } else {
            $must_have_fields = ADAnalysisClickhouseSqlMap::MUST_HAVE_NO_MATTER_WHAT;
        }

        if (in_array('switch', $this->target)) {
            $this->ad_log_fields[] = $this->switch_select;
            $this->data_log_fields[] = ["'' as switch"];
            $this->overview_log_fields[] = ["'' as switch"];
            $this->calc_standard_main_log_fields[] = ["'' as switch"];
        }

        //如果必查字段不仅查还需聚合，则使用无"any()"版
        if ($convert_fields = array_intersect($this->dimension, $must_have_fields)) {
            foreach ($convert_fields as $field) {
                $this->ad_log_fields[$field] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_AD_SELECT[$this->media_type][$field];
                $this->data_log_fields[$field] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_AD_SELECT[$this->media_type][$field];
                $this->overview_log_fields[$field] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_AD_SELECT[$this->media_type][$field];
                $this->sub_main_fields[$field] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_SUB_MAIN_SELECT[$field];
                $this->main_fields[$field] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_MAIN_SELECT[$field];
                $this->calc_standard_main_log_fields[$field] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_MAIN_SELECT[$field];
            }
        }

        //广告二级创建时间
        $this->ad_log_create_time_field = ADAnalysisClickhouseSqlMap::AD_CREATE_TIME['ad_log'][$this->media_type];
        $this->data_log_create_time_field = ADAnalysisClickhouseSqlMap::AD_CREATE_TIME['data_log'][$this->media_type];
        $this->overview_log_create_time_field = ADAnalysisClickhouseSqlMap::AD_CREATE_TIME['overview_log'][$this->media_type];

        $this->join_calc_bind_on = ADAnalysisClickhouseSqlMap::JOIN_CALC_ON[$this->media_type];

        //需要计算百分比的字段
        $this->all_calculate_percentage = ADAnalysisClickhouseSqlMap::ALL_CALCULATE_PERCENTAGE;
        //需要相除的字段
        $this->all_calculate_division = ADAnalysisClickhouseSqlMap::ALL_CALCULATE_DIVISION;
        //需要计算平均值的字段
        $this->all_calculate_average = ADAnalysisClickhouseSqlMap::ALL_CALCULATE_AVERAGE;
        //需要单纯累加但是要保留两位小数的字段
        $this->all_need_keep_two_decimal = ADAnalysisClickhouseSqlMap::ALL_NEED_KEEP_TWO_DECIMAL;
        //所有需要计算的字段
        $this->all_need_calculate = ADAnalysisClickhouseSqlMap::ALL_NEED_CALCULATE;
        //需要转化成中文的字段
        $this->all_translate_to_cn = ADAnalysisClickhouseSqlMap::ALL_TRANSLATE_TO_CN;
        //素材with as字段
//        $this->material_log_with_as_fields = ADAnalysisClickhouseSqlMap::MATERIAL_LOG_WITH_AS_FIELDS;

//        $this->third_level_all_filter = array_merge($this->third_ad_log_filter, $this->material_file_log_filter, $this->material_log_filter, $this->material_create_time);
//
//        $this->third_level_all_fields = array_merge($this->third_ad_log_fields, $this->material_file_log_fields, $this->material_log_fields);

//        $this->need_join_third_ad_log_fields = ADAnalysisClickhouseSqlMap::NEED_JOIN_THIRD_AD_LOG_FIELDS;
//
//        $this->overview_need_manual_order_by = ADAnalysisClickhouseSqlMap::OVERVIEW_NEED_MANUAL_ORDER_BY;
        if (0 === (int)$this->media_type) {
            $this->sub_data_log_group_by[] = 'media_type';
            $this->sub_overview_log_group_by[] = 'media_type';
            $this->calc_standard_sub_main_log_group_by[] = 'media_type';

            $this->calc_standard_sub_data_log_fields['media_type'] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_AD_SELECT[0]['media_type'];
            $this->calc_standard_sub_overview_log_fields['media_type'] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_AD_SELECT[0]['media_type'];
            $this->calc_standard_sub_main_log_fields['media_type'] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_SUB_MAIN_SELECT['media_type'];
//            $this->calc_standard_main_log_fields['media_type'] = ADAnalysisClickhouseSqlMap::SPECIAL_GROUP_BY_MAIN_SELECT['media_type'];
        }
        if (!in_array('tt_rit', $this->dimension)) {
            $this->sub_data_log_group_by[] = 'ad3_id';
            $this->sub_overview_log_group_by[] = 'ad3_id';
            $this->calc_standard_sub_main_log_group_by[] = 'ad3_id';
            $this->calc_standard_sub_data_log_fields['ad3_id'] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_DATA_LOG[$this->media_type]['ad3_id'];
            $this->calc_standard_sub_overview_log_fields['ad3_id'] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_OVERVIEW_LOG['ad3_id'];
            $this->calc_standard_sub_main_log_fields['ad3_id'] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_SUB_MAIN_SELECT['ad3_id'];
//            $this->calc_standard_main_log_fields['ad3_id'] = ADAnalysisClickhouseSqlMap::CALC_STANDARD_MAIN_SELECT['ad3_id'];

        }
        if (in_array('tt_inventory_subdivision', $this->dimension)) {
            $this->sub_data_log_group_by[] = 'tt_inventory_subdivision';
            $this->sub_overview_log_group_by[] = 'tt_inventory_subdivision';
            $this->calc_standard_sub_main_log_group_by[] = 'tt_inventory_subdivision';
        }
        if (in_array('gdt_inventory_subdivision', $this->dimension)) {
            $this->sub_data_log_group_by[] = 'gdt_inventory_subdivision';
            $this->sub_overview_log_group_by[] = 'gdt_inventory_subdivision';
            $this->calc_standard_sub_main_log_group_by[] = 'gdt_inventory_subdivision';
        }
        if (in_array('gdt_is_expand_targeting', $this->dimension)) {
            $this->sub_data_log_group_by[] = 'gdt_is_expand_targeting';
            $this->sub_overview_log_group_by[] = 'gdt_is_expand_targeting';
            $this->calc_standard_sub_main_log_group_by[] = 'gdt_is_expand_targeting';
        }
        $this->calc_standard_sub_data_log_fields['cost_date'] = ['data_log.cost_date as cost_date'];
        $this->calc_standard_sub_overview_log_fields['cost_date'] = ['overview_log.log_date as cost_date'];

        if (1 === (int)$this->attribution_type) {
            if (in_array('site_id', $this->dimension)) {
                $this->where_by_site[] = 'overview_log.site_id = overview_log.ad2_site_id';
            }
            if (in_array('account_id', $this->dimension)) {
                $this->where_by_site[] = 'overview_log.account_id = overview_log.ori_account_id';
            }
//            if (in_array('ad1_id', $this->dimension)) {
//                $this->where_by_site[] = 'overview_log.ad1_id = overview_log.ori_ad1_id';
//            }
            if (in_array('ad2_id', $this->dimension)) {
                $this->where_by_site[] = 'overview_log.ad2_id = overview_log.ori_ad2_id';
            }
            if (in_array('ad3_id', $this->dimension)) {
                $this->where_by_site[] = 'overview_log.ad3_id = overview_log.ori_ad3_id';
            }
        }
    }

    /**
     * 获取表名
     */
    private function getTable()
    {
//        if (true === $this->ctrl_by_account) {
//            //以account控制权限时
//            $this->ad_log_table = ADAnalysisClickhouseSqlMap::TABLE['account_log'][$this->media_type];
//        } else
//        if (array_intersect($this->all_fields, ADAnalysisClickhouseSqlMap::NEED_JOIN_THIRD_AD_LOG_FIELDS) || !empty($this->third_ad_log_filter)) {
//            //需要三级维度时
//            $this->ad_log_table = ADAnalysisClickhouseSqlMap::TABLE['ad2_v_ad3'][$this->media_type];
//        } else {
        $this->ad_log_table = ADAnalysisClickhouseSqlMap::TABLE['ad_log'][$this->media_type];
//        }

//        $this->first_ad_log_table = ADAnalysisClickhouseSqlMap::TABLE['ad1_log'][$this->media_type];
//        $this->third_ad_log_table = ADAnalysisClickhouseSqlMap::TABLE['ad3_log'][$this->media_type];
//        $this->account_log_table = ADAnalysisClickhouseSqlMap::TABLE['account_log'][$this->media_type];
        $this->data_log_table = ADAnalysisClickhouseSqlMap::TABLE['hour_data_log'][$this->media_type];
        if (1 === (int)$this->statistic_base_on) {
            $this->overview_log_table = ADAnalysisClickhouseSqlMap::TABLE['overview_log']['game_overview'];
        } else {
            $this->overview_log_table = ADAnalysisClickhouseSqlMap::TABLE['overview_log']['root_game_overview'];
        }
        $this->material_log_table = ADAnalysisClickhouseSqlMap::TABLE['material_log'];
        $this->material_file_log_table = ADAnalysisClickhouseSqlMap::TABLE['material_file_log'];
        $this->calc_bind_log_table = ADAnalysisClickhouseSqlMap::TABLE['calc_bind_log'];
        $this->convert_log_table = ADAnalysisClickhouseSqlMap::TABLE['convert_log'];
        $this->agency_change_log_table = ADAnalysisClickhouseSqlMap::TABLE['agency_change_log'];
        $this->site_table = ADAnalysisClickhouseSqlMap::TABLE['site'];
    }


}