<?php

namespace App\Param;

use App\Constant\TencentOSS;
use App\Exception\AppException;
use App\Utils\ALIOSSTool;
use App\Utils\TencentCOSTool;
use Common\EnvConfig;

/**
 * Class LDYFrameParam
 * @package App\Param
 */
class LDYFrameParam extends AbstractParam
{
    /**
     * @var string $template 落地页框架模板内容
     */
    public $template;

    /**
     * 设置落地页框架模板地址
     * @param string $template_filename
     */
    public function setLDYFrameTemplate(string $template_filename)
    {
        $template_file_src = ROOT_DIR . "/app/Service/LDYResource/iframe/{$template_filename}";
        if (file_exists($template_file_src)) {
            $template_file = fopen($template_file_src, 'r');
            $this->template = fread($template_file, filesize($template_file_src));
            fclose($template_file);
        } else {
            throw new AppException("切广告落地页失败:{$template_file_src}落地页框架模板模板不存在");
        }
    }

    public function setJquery()
    {
        $this->template = str_replace('</head>', '<script src="/static/jquery.min.js" type="text/javascript"></script></head>', $this->template);
    }


    /**
     * 设置落地页的框架属性的值
     * @param string $key
     * @param $value
     */
    public function setProp(string $key, $value)
    {
        $this->template = preg_replace("/\{" . $key . "\}/", $value, $this->template);
    }

    /**
     * 写入落地页框架文件
     * @param string $root_src
     * @param string $html_file_src
     */
    public function put(string $root_src, string $html_file_src)
    {
        $html_file_dir = dirname($root_src . $html_file_src);
        if (!file_exists($html_file_dir)) {
            mkdir($html_file_dir, 0777, true);
        }
        file_put_contents($root_src . $html_file_src, $this->template);

//        TencentCOSTool::multipartUpload($root_src . $html_file_src, $html_file_src, TencentOSS::OSS_LDY_BUCKET, ['PartSize' => 524288000]);
        ALIOSSTool::multiuploadFile($html_file_src, $root_src . $html_file_src);
    }
}
