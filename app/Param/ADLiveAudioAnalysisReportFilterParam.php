<?php
/**
 * 直播话术分析筛选参数
 */

namespace App\Param;


class ADLiveAudioAnalysisReportFilterParam extends AbstractParam
{
    const SELECT_SQL_MAP = [
        'demand_order' => [
            'platform' => 'demand_order.platform',
            'live_author_name' => 'demand_order.author_name as live_author_name',
            'live_author_id' => 'demand_order.author_id as live_author_id',
            'live_account_id' => 'demand_order.account_id as live_account_id',
            'live_demand_id' => 'demand_order.demand_id as live_demand_id',
            'live_order_id' => 'demand_order.order_id as live_order_id',
            'order_create_time' => 'demand_order.create_time as order_create_time',
            'aweme_account' => 'demand_order.aweme_account as aweme_account',
            'aweme_account_id' => 'demand_order.aweme_account as aweme_account_id',
            'avg_duration_watch' => 'demand_order.avg_duration_watch as avg_duration_watch',
            'live_universal_order_status' => 'demand_order.universal_order_status as live_universal_order_status',
            'live_time' => 'sum(demand_order.live_time) as live_time',
            'first_live_time' => 'demand_order.first_live_time as first_live_time',
        ],
        'flow' => [
            'total_play_incr' => 'IFNULL(live.watch_uv, flow.enter_unt) AS total_play_incr',
            'max_play_incr' => 'IFNULL(live.pcu, flow.pcu) AS max_play_incr',
            'avg_play_incr' => 'IFNULL(live.acu, flow.acu) AS avg_play_incr',
            'click' => 'IFNULL(live.component_click_cnt, flow.component_click) AS click',
            'ctr' => 'IFNULL(live.component_click_rate, flow.ctr) AS ctr',
            'show' => 'IFNULL(live.component_show_cnt, flow.component_show) AS show',
            'comment' => 'flow.comment as comment',
            'like' => 'flow.like as like',
            'share' => 'flow.share as share',
            'fans_increase' => 'flow.fans_increase as fans_increase',
            'click_play_rate' => 'IFNULL(CAST((sum( ad_log.click ) / flow.enter_unt) AS DECIMAL ( 10, 4 ) ), 0 ) AS click_play_rate',
        ],
        'basic' => [
            'task_cate' => 'basic.task_cate',
            'live_item_type' => 'basic.item_type as live_item_type',
            'item_release_time' => 'basic.item_release_time',
            'order_release_time' => 'basic.order_release_time',
            'price' => 'basic.price',
            'cost' => 'basic.cost',
            'consumption' => 'basic.consumption',
            'order_status' => 'basic.order_status',
            'live_item_title' => 'basic.item_title as live_item_title',
        ],
        'oud' => [
            'province' => 'oud.province',
            'age' => 'oud.age',
            'gender' => 'oud.gender',
        ],
        'audio' => [
            'up_trend' => 'audio.up_trend',
            'down_trend' => 'audio.down_trend',
            'up_trend_rate' => 'IFNULL(CAST((audio.up_trend / audio.down_trend) AS DECIMAL ( 10, 4 ) ), 0 ) as up_trend_rate',
            'min_live_time' => 'IFNULL(live.live_start_time,audio.min_live_time) as min_live_time',
            'max_live_time' => 'IFNULL(live.live_end_time,audio.max_live_time) as max_live_time',
        ],
        'ad_log' => [
            'ad_click' => 'sum( ad_log.click ) AS ad_click',
            'ad_show' => 'sum( ad_log.`show` ) AS ad_show',
            'ad_cost' => 'sum( ad_log.cost ) AS ad_cost',
            'click_rate' => 'IFNULL(CAST((sum( ad_log.click ) / sum(ad_log.`show`)) AS DECIMAL ( 10, 4 ) ), 0 ) AS click_rate',
            'ies_core_user_id' => 'ad_log.ies_core_user_id',
            'click_play_rate' => 'IFNULL(CAST((sum( ad_log.click ) / flow.enter_unt) AS DECIMAL ( 10, 4 ) ), 0 ) AS click_play_rate',
            'first_day_roi' => 'IFNULL(CAST((sum(overview.first_day_pay_money) / sum( ad_log.cost )) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_roi',
            'total_roi' => 'IFNULL(CAST((sum(overview.sum_total_pay_money) / sum( ad_log.cost )) AS DECIMAL ( 10, 4 ) ), 0 ) AS total_roi',
        ],
        'overview' => [
            'reg_uid_count' => 'sum(overview.reg_uid_count) as reg_uid_count',
            'first_day_pay_money' => 'sum(overview.first_day_pay_money) as first_day_pay_money',
            'first_day_roi' => 'IFNULL(CAST((sum(overview.first_day_pay_money) / sum( ad_log.cost )) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_roi',
            'first_pay_24_hour_pay_money' => 'sum(overview.first_pay_24_hour_pay_money) as first_pay_24_hour_pay_money',
            'first_pay_24_hour_pay_count' => 'sum(overview.first_pay_24_hour_pay_count) as first_pay_24_hour_pay_count',
            'first_pay_24_hour_pay_times' => 'sum(overview.first_pay_24_hour_pay_times) as first_pay_24_hour_pay_times',
            'sum_total_pay_money' => 'sum(overview.sum_total_pay_money) as sum_total_pay_money',
            'total_roi' => 'IFNULL(CAST((sum(overview.sum_total_pay_money) / sum( ad_log.cost )) AS DECIMAL ( 10, 4 ) ), 0 ) AS total_roi'
        ],
        'live' => [
            'watch_cnt' => 'sum(live.watch_cnt) AS watch_cnt',
            'watch_uv' => 'sum(live.watch_uv) AS watch_uv',
            'interact_uv' => 'sum(live.interact_uv) AS interact_uv',
            'interact_rate' => 'IFNULL(CAST((sum(live.interact_uv) / sum(live.watch_uv)) AS DECIMAL ( 10, 4 ) ), 0) as interact_rate',
            'convert_cnt' => 'sum(live.convert_cnt) as convert_cnt',
            'live_id' => 'live.live_id',
            'total_play_incr' => 'IFNULL(live.watch_uv, flow.enter_unt) AS total_play_incr',
            'max_play_incr' => 'IFNULL(live.pcu, flow.pcu) AS max_play_incr',
            'avg_play_incr' => 'IFNULL(live.acu, flow.acu) AS avg_play_incr',
            'click' => 'IFNULL(live.component_click_cnt, flow.component_click) AS click',
            'ctr' => 'IFNULL(live.component_click_rate, flow.ctr) AS ctr',
            'show' => 'IFNULL(live.component_show_cnt, flow.component_show) AS show',
            'min_live_time' => 'IFNULL(live.live_start_time,audio.min_live_time) as min_live_time',
            'max_live_time' => 'IFNULL(live.live_end_time,audio.max_live_time) as max_live_time',
        ],
        'demand_list' => [
            'live_demand_name' => 'demand_list.demand_name as live_demand_name'
        ]
    ];

    const RATE_MAP = [
        'interact_rate' => [
            'interact_uv',
            'watch_uv'
        ],
        'ctr' => [
            'click',
            'show'
        ],
        'up_trend_rate' => [
            'up_trend',
            'down_trend'
        ],
        'click_play_rate' => [
            'ad_click',
            'total_play_incr'
        ],
        'first_day_roi' => [
            'first_day_pay_money',
            'ad_cost'
        ],
        'total_roi' => [
            'sum_total_pay_money',
            'ad_cost'
        ],
        'click_rate' => [
            'ad_click',
            'ad_show'
        ]
    ];

    //数据库类型 1:ADB 2:ClickHouse
    public $database_type = '';
    public $media_type = 0;                  // 所选媒体
    public $dimension = [];                  // 处理后的维度
    public $target = [];                     // 所有指标
    public $filter = [];                     // 所有筛选
    public $filter_fields = [];
    public $calc = [];                       // 所有数值条件
    public $calc_fields = [];
    public $level = 0;                       // 当前聚合等级
    public $calc_condition = '';             //数值条件之间关系
    public $order = [];                      // 所排序条件
    public $create_time;                     // 订单创建时间
    public $live_time;                       // 直播时间
    public $game_permission;                 // 游戏权限
    public $agent_permission;                // 渠道权限
    public $user_type;                       //用户类型（区分市场和设计）
    public $user_list;                       //下属权限
    public $limit = 50000;                   //每次获取条数
//    public $limit = 500;                     //每次获取条数测试用
    public $all_fields;
    public $switch_select = [];
    public $group_by = [];
    public $where = [];
    public $need_join = [];
    public $select = [];

    /**
     * 处理不同media_type的select group by where数据
     */
    public function paramHook()
    {
        if (!in_array('live_author_name', $this->dimension) || !in_array('live_demand_id', $this->dimension)) {
//            throw new AppException("聚合维度必须选上达人名称和任务ID");
        }

//        $this->group_by = [
//            'demand_order.author_name',
//            'demand_order.demand_id'
//        ];

        if ($this->dimension) {
            if (in_array('platform', $this->dimension)) {
                $this->group_by[] = 'demand_order.platform';
            }
            if (in_array('live_account_id', $this->dimension)) {
                $this->group_by[] = 'demand_order.account_id';
            }
            if (in_array('live_order_id', $this->dimension)) {
                $this->group_by[] = 'demand_order.order_id';
            }
            if (in_array('live_id', $this->dimension)) {
                $this->group_by[] = 'live.live_id';
            }
            if (in_array('aweme_account', $this->dimension)) {
                $this->group_by[] = 'demand_order.aweme_account';
            }
            if (in_array('live_demand_id', $this->dimension)) {
                $this->group_by[] = 'demand_order.demand_id';
            }
            if (in_array('live_author_name', $this->dimension)) {
                $this->group_by[] = 'demand_order.author_name';
            }
        }

        if (!empty($this->filter)) {
            foreach ($this->filter as $value) {
                $this->filter_fields[] = $value['column'];
            }
        }

        $this->all_fields = array_merge($this->target, $this->dimension, $this->filter_fields, $this->calc_fields);

        // 重构where条件
        if ($this->filter) {
            foreach ($this->filter as $key => $value) {
                if ($key == 'site_id') {
                    $site_val = [];
                    foreach ($value['value'] as $site_value) {
                        $site_val[] = explode('-', $site_value)[1];
                    }
                    $this->where[$key] = $site_val;
                } else {
                    $this->where[$key] = $value['value'];
                }
            }
        }

        $tmp_all_fields = $this->all_fields;
        foreach ($this->all_fields as $field) {
            if (isset(self::RATE_MAP[$field])) {
                $tmp_all_fields = array_merge($tmp_all_fields, self::RATE_MAP[$field]);
            }
        }

        $this->all_fields = $tmp_all_fields;

        // 找出需要查询的字段和连表
        foreach ($this->all_fields as $field) {
            foreach (self::SELECT_SQL_MAP as $join_table => $select) {
                if (isset($select[$field])) {
                    $this->need_join[] = $join_table;
                    $this->select[] = $select[$field];
                    switch ($join_table) {
                        case 'ad_log':
                            $this->need_join[] = 'live';
                            $this->need_join[] = 'audio';
                            break;
                        case 'overview':
                            $this->need_join[] = 'ad_log';
                    }
                }
            }
        }

        $this->need_join[] = 'live'; // 排序需要 固定连live
        $this->need_join = array_values(array_unique($this->need_join));
        $this->select = array_values(array_unique($this->select));
    }

    private function handleSpecialLogic()
    {
    }

    /**
     * 放入计算回本标准值所需字段
     */
    private function handleStandardCalc()
    {
    }

    private function handleStandardReachedCostCalc()
    {
    }

    /**
     * 公共排序字段
     */
    private function commonPartOrderBy()
    {
    }

    /**
     * 公共select字段筛选
     * @param $select
     */
    private function commonPartSelect($select)
    {
    }

    /**
     * 公共group by字段筛选
     * @param $group_by
     */
    private function commonPartGroupBy($group_by)
    {

    }

    /**
     * 公共where字段筛选
     * @param $filter
     */
    private function commonPartWhere($filter)
    {
    }
}
