<?php

namespace App\Param;


use App\Model\SqlModel\DataMedia\OdsAwemeAccountNameLogModel;

class AwemeAccountInterfaceParam extends AbstractParam
{
    public $platform;
    public $media_type;
    public $type;
    public $live_user;
    public $aweme_account;
    public $aweme_name;
    public $creator;
    public $editor;
    public $interface_person = '';
    public $start_time;
    public $end_time;
    public $page = 1;
    public $rows = 50;

    protected function paramHook()
    {
        // aweme_account 存放用户输入 live_user 存放转小写后的 方便查询与连表
        $this->aweme_name = trim($this->aweme_name);
        $this->aweme_account = trim($this->aweme_account);
        $this->live_user = strtolower($this->aweme_account);
    }

    public function toData()
    {
        $date = date("Y-m-d H:i:s");
        return [
            'platform' => $this->platform,
            'media_type' => $this->media_type,
            'type' => $this->type,
            'live_user' => $this->live_user,
            'aweme_name' => $this->aweme_name,
            'aweme_account' => $this->aweme_account,
            'start_time' => $this->start_time,
            'end_time' => OdsAwemeAccountNameLogModel::END_TIME,
            'creator' => $this->creator,
            'interface_person' => $this->interface_person,
            'insert_time' => $date,
            'update_time' => $date,
        ];
    }

    public function toUpdateData()
    {
        return [
            'aweme_name' => $this->aweme_name,
            'interface_person' => $this->interface_person,
            'editor' => $this->editor,
            'update_time' => date("Y-m-d H:i:s")
        ];
    }
}
