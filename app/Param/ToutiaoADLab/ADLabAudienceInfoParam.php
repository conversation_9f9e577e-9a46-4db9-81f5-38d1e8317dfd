<?php
/**
 * 头条创建投放管家项目
 * User: zhangzhonghao
 * Date: 2020-02-21
 */

namespace App\Param\ToutiaoADLab;

use App\Param\AbstractParam;

/**
 * Class ADLabConvertInfoParam
 * @package App\Param\TouTiaoADLab
 */
class ADLabAudienceInfoParam extends AbstractParam
{
    public $city;
    public $platform;
    public $location_type;
    public $age;
    public $gender;
    public $district;
    public $superior_popularity_type;
    public $flow_package;
    public $exclude_flow_package;
    public $retargeting_tags_include;
    public $retargeting_tags_exclude;
    public $auto_extend_enabled;
    public $auto_extend_targets;
    public $hide_if_converted;
    public $converted_time_duration;

    public function toRequest()
    {
        $data = [
            'district' => $this->district,
            'platform' => $this->platform,
            'gender' => $this->gender,
            'auto_extend_enabled' => $this->auto_extend_enabled,
            'hide_if_converted' => $this->hide_if_converted,
            'converted_time_duration' => $this->converted_time_duration,
        ];

        if (!$data['converted_time_duration'] || $data['converted_time_duration'] == 'NONE') {
            unset($data['converted_time_duration']);
        }

        if (in_array($this->district, ['CITY', 'COUNTY'])) {
            $data['city'] = $this->city;
            $data['location_type'] = $this->location_type;
        }

        if ($this->age) {
            foreach ($this->age as $age_two) {
                $age_info = explode('_', $age_two);
                // 以下代码定向包兼容写死
                if ($age_info[2] == 18 || $age_info[2] == 20){
                    $data['age'][18] = [18, 23];
                }
                if ($age_info[2] == 24){
                    $data['age'][24] = [24, 30];
                }
                if ($age_info[2] == 31 || $age_info[2] == 36){
                    $data['age'][31] = [31, 40];
                }
                if ($age_info[2] == 41 || $age_info[2] == 46){
                    $data['age'][41] = [41, 49];
                }
                // 管家可选值 最大 50 - 100
                if ($age_info[2] == 50 || $age_info[2] > 50) {
                    $data['age'][50] = [50, 100];
                }
//              data['age'][] = [intval($age_info[2]), intval($age_info[3])];
            }
            $data['age'] = array_values($data['age']);
        }

        if ($this->superior_popularity_type && $this->superior_popularity_type != 'zidingyi') {
            $data['superior_popularity_type'] = $this->superior_popularity_type;
        }

        if ($this->flow_package) {
            $data['flow_package'] = $this->flow_package;
        }

        if ($this->exclude_flow_package) {
            $data['exclude_flow_package'] = $this->exclude_flow_package;
        }

        if ($this->retargeting_tags_include) {
            $data['retargeting_tags_include'] = $this->retargeting_tags_include;
        }

        if ($this->retargeting_tags_exclude) {
            $data['retargeting_tags_exclude'] = $this->retargeting_tags_exclude;
        }

        if ($this->auto_extend_enabled == 1) {
            $data['auto_extend_targets'] = array_filter($this->auto_extend_targets, function ($v) {
                return $v != 'INTEREST_ACTION';
            });
            $data['auto_extend_targets'] = array_values($data['auto_extend_targets']);
        }

        return $data;
    }

}
