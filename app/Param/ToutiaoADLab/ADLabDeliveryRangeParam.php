<?php
/**
 * 头条创建投放管家项目
 * User: zhangzhonghao
 * Date: 2020-02-21
 */

namespace App\Param\ToutiaoADLab;

use App\Constant\ToutiaoEnum;
use App\Param\AbstractParam;

/**
 * Class ADLabInventoryInfoParam
 * @package App\Param\TouTiaoADLab
 */
class ADLabDeliveryRangeParam extends AbstractParam
{
    public $inventory_type;
    public $union_video_type;
    public $inventory_catalog;
    public $delivery_range;
    public $smart_inventory;

    public function toRequest()
    {

        $data = [];
        // $this->delivery_range == 默认 和 穿山甲走这里
        if (in_array($this->delivery_range, [ToutiaoEnum::DELIVERY_RANGE_DEFAULT, ToutiaoEnum::DELIVERY_RANGE_UNION])) {
            // 如果是穿山甲
            if ($this->delivery_range === ToutiaoEnum::DELIVERY_RANGE_UNION){
                $data['union_video_type'] = $this->union_video_type;
                $data['inventory_type'] = ['INVENTORY_UNION_SLOT'];
            }else{
                $data['inventory_type'] = $this->inventory_type;
            }
            // 首选媒体
            $data['inventory_catalog'] = ToutiaoEnum::INVENTORY_CATALOG_MANUAL;
        }else{
            // $this->delivery_range == 新版通投智选
            $data['inventory_catalog'] = ToutiaoEnum::INVENTORY_CATALOG_UNIVERSAL;
        }

        return $data;
    }
}
