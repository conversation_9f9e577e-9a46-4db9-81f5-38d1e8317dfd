<?php
/**
 * 头条创建投放管家项目
 * User: zhangzhonghao
 * Date: 2020-02-21
 */

namespace App\Param\ToutiaoADLab;

use App\Param\AbstractParam;

/**
 * Class ADLabCreateParam
 * @package App\Param\TouTiaoADLab
 */
class ADLabAdParam extends AbstractParam
{

    public $ad_target;


    /**
     * @var ADLabAudienceInfoParam $audience_info
     */
    private $audience_info;

    private $track_url_setting;

    /**
     * @var ADLabDeliveryRangeParam $delivery_range
     */
    private $delivery_range;

    /**
     * @param mixed $delivery_range
     */
    public function setDeliveryRange(ADLabDeliveryRangeParam $delivery_range): void
    {
        $this->delivery_range = $delivery_range;
    }

    /**
     * @param mixed $convert_info
     */
    public function setConvertInfo(ADLabConvertInfoParam $convert_info): void
    {
        $this->convert_info = $convert_info;
    }

    /**
     * @param mixed $budget_info
     */
    public function setBudgetInfo(ADLabBudgetInfoParam $budget_info): void
    {
        $this->budget_info = $budget_info;
    }

    /**
     * @param mixed $audience
     */
    public function setAudienceInfo(ADLabAudienceInfoParam $audience_info): void
    {
        $this->audience_info = $audience_info;
    }

    /**
     * @param mixed $track_url_setting
     */
    public function setTrackUrlSetting($track_url_setting): void
    {
        $this->track_url_setting = $track_url_setting;
    }


    public function toBody()
    {
        $data = [
            'ad_target' => $this->ad_target,
            'delivery_range' => $this->delivery_range->toRequest(),
            'audience' => $this->audience_info->toRequest(),
            'track_url_setting' => $this->track_url_setting,
        ];
        return $data;
    }
}
