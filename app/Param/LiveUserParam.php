<?php

namespace App\Param;


use App\Model\SqlModel\DataMedia\OdsLiveUserNameLogModel;

class LiveUserParam extends AbstractParam
{
    public $media_type;
    public $live_user;
    public $live_user_name;
    public $aweme_account;
    public $name_start_time;
    public $name_end_time;
    public $company_start_time;
    public $company_end_time;
    public $type;
    public $company_name;
    public $aweme_account_type = 0;
    public $live_user_type = 0; // 主播类型 目前区分内部/外部
    public $creator;
    public $editor;
    public $page = 1;
    public $rows = 50;

    protected function paramHook()
    {
        // aweme_account 存放用户输入 live_user 存放转小写后的
        $this->aweme_account = $this->aweme_account ?: trim($this->live_user);
        $this->live_user = strtolower(trim($this->live_user));
        $this->live_user_name = trim($this->live_user_name);
        $this->company_name = trim($this->company_name);
    }

    public function toData()
    {
        $date = date("Y-m-d H:i:s");
        return [
            'media_type' => $this->media_type,
            'live_user' => $this->live_user,
            'live_user_name' => $this->live_user_name,
            'aweme_account' => $this->aweme_account,
            'aweme_name' => $this->live_user_name,
            'name_start_time' => $this->name_start_time,
            'name_end_time' => OdsLiveUserNameLogModel::NAME_END_TIME,
            'creator' => $this->creator,
            'type' => $this->type,
            'company_name' => $this->company_name,
            'dsp_update_time' => $date,
            'create_time' => $date,
            'aweme_account_type' => $this->aweme_account_type,
            'live_user_type' => $this->live_user_type,
        ];
    }

    public function toUpdateData()
    {
        return [
            'live_user_name' => $this->live_user_name,
            'aweme_name' => $this->live_user_name,
            'company_name' => $this->company_name,
            'editor' => $this->editor,
            'dsp_update_time' => date("Y-m-d H:i:s"),
            'aweme_account_type' => $this->aweme_account_type,
            'live_user_type' => $this->live_user_type,
        ];
    }
}
