<?php
/**
 * 分摊数据
 */

namespace App\Controller\FA;

use App\Constant\ResponseCode;
use App\Logic\FA\CostLogic;
use App\Logic\FA\SalaryLogic;
use App\Struct\Input;

class ShareController extends Controller
{
    /**
     * @CtrlAnnotation(permissions=['/share/salary'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function salary(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new SalaryLogic())->getShareList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/share/salary'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function salaryDetail(Input $input)
    {
        $input->verify(['department_id', 'tmonth']);

        $data = (new SalaryLogic())->getShareDetailList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/share/cost'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function cost(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new CostLogic())->getShareList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/share/cost'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function costDetail(Input $input)
    {
        $input->verify(['department_id', 'tmonth', 'cost_classify', 'cost_type']);

        $data = (new CostLogic())->getShareDetailList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }
}
