<?php

namespace App\Controller\FA;

use App\Constant\AttendanceData;
use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use App\Logic\FA\AttendanceRecordLogic;
use App\Response\CSV;
use App\Struct\Input;
use App\Utils\UploadTool;

class AttendanceRecordController extends Controller
{
    /**
     * 考勤记录
     * @CtrlAnnotation(permissions=['/attendance-record/list'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function list(Input $input)
    {

        $input->verify(['page', 'rows']);

        $data = (new AttendanceRecordLogic())->record($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * 考勤日志
     * @CtrlAnnotation(permissions=['/attendance-record/detail'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function detail(Input $input)
    {
        $input->verify(['page', 'rows']);

        $data = (new AttendanceRecordLogic())->detail($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * 考勤修正导入
     * @CtrlAnnotation(permissions=['/attendance-record/detail'],log_type='add')
     *
     * @param Input $input
     * @return array|bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importDetail(Input $input)
    {
        // 修改权限判断
        (new AttendanceRecordLogic())->checkEditPermission();

        $data_list = UploadTool::xlsx($this->request->files);

        $data = (new AttendanceRecordLogic())->importDetail($data_list);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    /**
     * 考勤修正导入模板
     * @CtrlAnnotation(permissions=['/attendance-record/detail'],log_type='add')
     *
     * @return CSV
     */
    public function importDetailTemplate()
    {
        return new CSV('人事考勤-考勤情况sample.xlsx', 'sample/fa_attendance_record_fix_template.xlsx');
    }

    /**
     * 考勤修正
     * @CtrlAnnotation(permissions=['/attendance-record/detail'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editDetail(Input $input)
    {
        $input->verify(['work_date', 'staff_number', 'type']);

        // 修改权限判断
        (new AttendanceRecordLogic())->checkEditPermission();

        $data = (new AttendanceRecordLogic())->editDetail($input);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    /**
     * 个人考勤日志
     * @CtrlAnnotation(permissions=['/attendance-record/personal'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function personal(Input $input)
    {
        $input->verify(['work_month']);

        $department_id = Container::getSession()->getUserDetail(self::MODULE)['department_id'];
        if ((int)$department_id === AttendanceData::QUIT_PERMISSION['department_id']) {
            throw new AppException('权限不足，获取数据失败');
        }
        $data = (new AttendanceRecordLogic())->personal($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * 员工考勤签字确认
     * @CtrlAnnotation(permissions=['/attendance-record/personal'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function confirm(Input $input)
    {
        $input->verify(['staff_number', 'work_month', 'confirmed']);

        $data = (new AttendanceRecordLogic())->confirm($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * 人事考勤核对
     * @CtrlAnnotation(permissions=['/attendance-record/personal'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function checked(Input $input)
    {
        $input->verify(['staff_number', 'work_month', 'checked']);

        // 修改权限判断
        (new AttendanceRecordLogic())->checkEditPermission();

        $data = (new AttendanceRecordLogic())->checked($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * 个人历史考勤记录
     * @CtrlAnnotation(permissions=['/attendance-record/personal-history'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function personalHistory(Input $input)
    {
        $input->verify(['page', 'rows']);

        $data = (new AttendanceRecordLogic())->personalHistory($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * 删除考勤日志
     * @CtrlAnnotation(permissions=['/attendance-record/list'],log_type='delete')
     *
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['ids']);
        $data = (new AttendanceRecordLogic())->delete($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/attendance-record/warn'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function warnList(Input $input)
    {
        $input->verify(['page', 'rows', 'work_month']);

        $data = (new AttendanceRecordLogic())->getWarnList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/attendance-record/warn'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function sendNotice(Input $input)
    {
        $input->verify(['media_type', 'type', 'user_id']);

        $data = (new AttendanceRecordLogic())->sendNotice($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '请求发送成功',
            'data' => $data,
        ];
    }

}
