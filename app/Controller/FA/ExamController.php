<?php

namespace App\Controller\FA;

use App\Constant\OuterAuth;
use App\Constant\ResponseCode;
use App\Logic\FA\ExamLogic;
use App\Logic\FA\LogLogic;
use App\Struct\Input;
use App\Utils\OuterAuthTool;

class ExamController extends Controller
{
    protected $pass_method = ['syncRecordByWeb'];

    public function getConfigData(Input $input)
    {
        $data = (new ExamLogic())->getConfigData();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='add')
     *
     * @param Input $input
     * @return array|bool
     */
    public function addQuestion(Input $input)
    {
        $input->verify(['department_id', 'position']);
        $data = (new ExamLogic())->addQuestion($input);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getQuestionData(Input $input)
    {
        $input->verify(['id']);
        $data = (new ExamLogic())->getDataByQuestionGroupId($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='edit')
     *
     * @param Input $input
     * @return array|int
     */
    public function editQuestion(Input $input)
    {
        $input->verify(['id', 'department_id']);
        $data = (new ExamLogic())->editQuestion($input);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editQuestionGroupType(Input $input)
    {
        $input->verify(['id', 'type']);
        $data = (new ExamLogic())->editQuestionGroupType($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editQuestionGroupStatus(Input $input)
    {
        $input->verify(['id', 'status']);
        $data = (new ExamLogic())->editQuestionGroupStatus($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='delete')
     *
     * @param Input $input
     * @return array
     */
    public function deleteQuestion(Input $input)
    {
        $input->verify(['question_id']); //问题id，非分组id
        $data = (new ExamLogic())->deleteQuestion($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function questionLog(Input $input)
    {
        $data = (new LogLogic())->questionLog($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function question(Input $input)
    {
        $data = (new ExamLogic())->getListByQuestion($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getQuestionListByGroupId(Input $input)
    {
        $input->verify(['id']); //分组id

        $data = (new ExamLogic())->getQuestionListByGroupId($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getQuestionGroupList(Input $input)
    {
        $input->verify(['position']);
        $data = (new ExamLogic())->getQuestionGroupList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-question'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getQuestionListByPosition(Input $input)
    {
        $input->verify(['position']);
        $data = (new ExamLogic())->getQuestionListByPosition($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='add')
     *
     * @param Input $input
     * @return array|int
     */
    public function addRecord(Input $input)
    {
        $input->verify(['name', 'position', 'type', 'score', 'content']);
        $data = (new ExamLogic())->addRecord($input);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getRecordData(Input $input)
    {
        $input->verify(['id']);
        $data = (new ExamLogic())->getDataByRecordId($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    public function downloadRecordData(Input $input)
    {
        $input->verify(['id']);
        return (new ExamLogic())->downloadDataByRecordId($input);
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editRecord(Input $input)
    {
        $input->verify(['id', 'content']);
        $data = (new ExamLogic())->editRecord($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editRecordType(Input $input)
    {
        $input->verify(['id', 'type']);
        $data = (new ExamLogic())->editRecordType($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editRecordScore(Input $input)
    {
        $input->verify(['id', 'score']);
        $data = (new ExamLogic())->editRecordScore($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function recordLog(Input $input)
    {
        $data = (new LogLogic())->recordLog($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/manage/exam-record'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function record(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new ExamLogic())->getListByRecord($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    public function syncRecordByWeb(Input $input)
    {
        $input->verify(['name', 'position', 'content', 'question_group_id', 'token', 'timestamp']);
        // 检查用户token
        if (!OuterAuthTool::Auth(OuterAuth::AUTH_TYPE_EXAM_RECORD, $input['token'], $input['timestamp'])) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限添加'
            ];
        }

        $data = (new ExamLogic())->addRecordByWeb($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }
}
