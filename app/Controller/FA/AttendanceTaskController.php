<?php
/**
 * 考勤数据执行流程
 * 1、需要人事提前录入当月的《考勤配置》，导入排班制的《考勤规则》；若《考勤规则》不准确，会导致判断出现个人考勤迟到早退现象，所以考勤规则一定一定要准确。
 *
 * 2、（可选）导入OA数据到fa_attendance_ods_oa_log，此步在OA服务器221.228.76.34上执行。有定时计划任务，若数据丢失，可以重新执行获取一遍
 *    php /data/www/aa.361757.com/oa/approval_data.php 'Y-m-d' '00:00:00' '23:59:59'
 *
 * 3、（可选）导入门禁考勤数据到fa_attendance_ods_door_log，此步在公司内网服务器192.168.100.250上执行。有定时计划任务，若数据丢失，可以重新执行获取一遍
 *    php /data/www/haikang/door_event.php 'Y-m-d 00:00:00' 'Y-m-d 23:59:59'
 *
 * 4、（可选）从企业微信打卡导入数据到fa_attendance_ods_door_log，时间跨度不能大于30天。有定时计划任务，若数据丢失，可以重新执行获取一遍
 *    fa/attendanceTask/run?action=weixin&start_date=Y-m-d&end_date=Y-m-d
 *
 * 5、从fa_attendance_ods_oa_log上导入数据(除漏打卡数据外)到fa_attendance_personal_record表。
 *    fa/attendanceTask/run?action=oa&work_month=Y-m
 *
 * 6、从fa_attendance_ods_door_log导入考勤数据到fa_attendance_personal_record表。
 *    fa/attendanceTask/run?action=door&start_da
 *   7、从fa_attendance_ods_oa_log上导入数据(仅漏打卡数据)到fa_attendance_personal_record表。
 *      fa/attendanceTask/run?action=oaForgetClock&work_month=Y-m
 *
 *   8、修正数据：员工档案库的入职离职员工日期，填充当月缺失的天数考勤，处理相同考勤时间，更新职位，重置申请加班而没打卡数据，更新日期类型。
 *      fa/attendanceTask/run?action=fix?work_month=Y-mte=Y-m-d&end_date=Y-m-d
 *
 *  7、从fa_attendance_ods_oa_log上导入数据(仅漏打卡数据)到fa_attendance_personal_record表。
 *     fa/attendanceTask/run?action=oaForgetClock&work_month=Y-m
 *
 *  8、修正数据：员工档案库的入职离职员工日期，填充当月缺失的天数考勤，处理相同考勤时间，更新职位，重置申请加班而没打卡数据，更新日期类型。
 *     fa/attendanceTask/run?action=fix?work_month=Y-m
 * 7、从fa_attendance_ods_oa_log上导入数据(仅漏打卡数据)到fa_attendance_personal_record表。
 *    fa/attendanceTask/run?action=oaForgetClock&work_month=Y-m
 *
 * 8、修正数据：员工档案库的入职离职员工日期，填充当月缺失的天数考勤，处理相同考勤时间，更新职位，重置申请加班而没打卡数据，更新日期类型。
 *    fa/attendanceTask/run?action=fix?work_month=Y-m
 *
 * 9、判断考勤情况：旷工、迟到、早退
 *    fa/attendanceTask/run?action=check&start_date=Y-m-d&end_date=Y-m-d
 */

namespace App\Controller\FA;

use App\Constant\ResponseCode;
use App\Struct\Input;
use App\Task\AttendanceClockCheckTask;
use App\Task\AttendanceDingTalkCheckInTask;
use App\Task\AttendanceFillRecordTask;
use App\Task\AttendanceFixTask;
use App\Task\AttendanceDoorDataToRecordTask;
use App\Task\AttendanceOADataToRecordTask;
use App\Task\AttendanceWeixinCheckInTask;

class AttendanceTaskController extends Controller
{
    /**
     * @CtrlAnnotation(permissions=['/attendance-task/run'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function run(Input $input)
    {
        $input->verify(['action']);
        switch ($input['action']) {
            case 'oa':
                $data = $this->oa($input);
                break;
            case 'door':
                $data = $this->door($input);
                break;
            case 'oaForgetClock':
                $data = $this->oaForgetClock($input);
                break;
            case 'fix':
                $data = $this->fix($input);
                break;
            case 'check':
                $data = $this->check($input);
                break;
            case 'weixin':
                $data = $this->weixin($input);
                break;
            case 'dingtalk':
                $data = $this->dingtalk($input);
                break;
            case 'dingtalkOa':
                $data = $this->dingtalkOa($input);
                break;
            case 'resetSplit':
                $data = $this->resetSplit($input);
                break;
            case 'resetRecord':
                $data = $this->resetRecord($input);
                break;
            case 'fill':
                $data = $this->fill($input);
                break;
            default:
                $data = [];
                break;
        }

        if (is_array($data) && !empty($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
            'data' => $data
        ];
    }

    private function oa($input)
    {
        $input->verify(['work_month']);
        if (isset($input['work_flow_id']) && !empty($input['work_flow_id'])) {
            $input['work_flow_id'] = explode(',', $input['work_flow_id']);
        } else {
            $input['work_flow_id'] = $input['work_flow_id'] ?? [458, 557, 586, 596, 598];
        }

        $data = (new AttendanceOADataToRecordTask())->toRecord($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function door($input)
    {
        $input->verify(['start_date', 'end_date']);
        $data = (new AttendanceDoorDataToRecordTask())->toRecord($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function oaForgetClock($input)
    {
        $input->verify(['work_month']);
        if (isset($input['work_flow_id']) && !empty($input['work_flow_id'])) {
            $input['work_flow_id'] = explode(',', $input['work_flow_id']);
        } else {
            $input['work_flow_id'] = $input['work_flow_id'] ?? [609];
        }
        $data = (new AttendanceOADataToRecordTask())->toRecord($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function fix($input)
    {
        $input->verify(['work_month']);
        $data = (new AttendanceFixTask())->fix($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function check($input)
    {
        $input->verify(['start_date', 'end_date']);
        $data = (new AttendanceClockCheckTask())->check($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function weixin($input)
    {
        $input->verify(['start_date', 'end_date']);
        $data = (new AttendanceWeixinCheckInTask())->toOdsDoorLog($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
            'data' => $data
        ];
    }

    private function dingtalk($input)
    {
        $input->verify(['start_date', 'end_date']);
        $data = (new AttendanceDingTalkCheckInTask())->toOdsDoorLog($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
            'data' => $data
        ];
    }

    private function dingtalkOa($input)
    {
        $input->verify(['start_date', 'end_date']);
        $data = (new AttendanceDingTalkCheckInTask())->toOdsOaLog($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
            'data' => $data
        ];
    }

    private function resetRecord($input)
    {
        $input->verify(['content']);
        $data = (new AttendanceFixTask())->resetRecord($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function resetSplit($input)
    {
        $input->verify(['content']);
        $data = (new AttendanceFixTask())->resetSplit($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    private function fill($input)
    {
        $input->verify(['work_month']);
        $data = (new AttendanceFillRecordTask())->fill($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }
}
