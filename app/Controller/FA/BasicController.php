<?php
/**
 * 基础数据
 */

namespace App\Controller\FA;

use App\Constant\ResponseCode;
use App\Logic\FA\CostLogic;
use App\Logic\FA\LogLogic;
use App\Logic\FA\SalaryLogic;
use App\Logic\FA\FinanceConfigDataLogic;
use App\Response\CSV;
use App\Utils\UploadTool;
use App\Struct\Input;
use PhpOffice\PhpSpreadsheet\Exception;

class BasicController extends Controller
{
    /**
     * 常用配置
     *
     * @return array
     */
    public function getAllConfig()
    {
        $data = (new FinanceConfigDataLogic())->getAllConfig();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 录入工资
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='add')
     *
     * @param Input $input
     * @return array
     */
    public function addSalary(Input $input)
    {
        $input->verify(['tmonth', 'sbu_id', 'department_id', 'salary', 'total_bonus']);
        $data = (new SalaryLogic())->add($input);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 工资导入
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='add')
     *
     * @return array|bool
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importSalary()
    {
        $data_list = UploadTool::xlsx($this->request->files);
        $data = (new SalaryLogic())->import($data_list);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    /**
     * 工资详情
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getSalaryData(Input $input)
    {
        $input->verify(['id']);
        $data = (new SalaryLogic())->getData($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 工资分摊详情
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getSalaryShareLog(Input $input)
    {
        $input->verify(['id']);
        $data = (new SalaryLogic())->shareLog($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 工资修改
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editSalary(Input $input)
    {
        $input->verify(['id', 'tmonth', 'sbu_id', 'department_id', 'salary', 'total_bonus']);

        (new SalaryLogic())->edit($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 工资批量修改
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editMultiSalary(Input $input)
    {
        $input->verify(['ids', 'salary', 'total_bonus']);

        (new SalaryLogic())->editMulti($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 工资删除
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='delete')
     *
     * @param Input $input
     * @return array
     */
    public function deleteSalary(Input $input)
    {
        $input->verify(['id']);

        (new SalaryLogic())->delete($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 工资批量删除
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='delete')
     *
     * @param Input $input
     * @return array
     */
    public function deleteMultiSalary(Input $input)
    {
        $input->verify(['ids']);

        if (!is_array($input['ids'])) {
            $input['ids'] = \GuzzleHttp\json_decode($input['ids'], true);
        }
        if (!is_array($input['ids']) || empty($input['ids'])) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '',
                'data' => $input['ids'],
            ];
        }
        (new SalaryLogic())->deleteMulti($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 工资报表
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function salary(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new SalaryLogic())->getList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 工资操作日志
     * @CtrlAnnotation(permissions=['/basic/salary'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function salaryLog(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new LogLogic())->salaryLog($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     *费用录入
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='add')
     *
     * @param Input $input
     * @return array|int
     */
    public function addCost(Input $input)
    {
        $input->verify(['tmonth', 'sbu_id', 'cost_type', 'money']);

        $data = (new CostLogic())->add($input);
        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 费用导入
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='add')
     *
     * @return array|bool
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importCost()
    {
        $data_list = UploadTool::xlsx($this->request->files);
        $data = (new CostLogic())->import($data_list);

        if (is_array($data)) {
            return $data;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    /**
     * 费用详情
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getCostData(Input $input)
    {
        $input->verify(['id']);
        $data = (new CostLogic())->getData($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 费用分摊详情
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function getCostShareLog(Input $input)
    {
        $data = (new CostLogic())->shareLog($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 费用编辑
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editCost(Input $input)
    {
        $input->verify(['id', 'tmonth', 'sbu_id', 'cost_type', 'money']);

        (new CostLogic())->edit($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 费用批量编辑
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='edit')
     *
     * @param Input $input
     * @return array
     */
    public function editMultiCost(Input $input)
    {
        $input->verify(['ids', 'money']);

        (new CostLogic())->editMulti($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 费用删除
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='delete')
     *
     * @param Input $input
     * @return array
     */
    public function deleteCost(Input $input)
    {
        $input->verify(['id']);

        (new CostLogic())->delete($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 费用批量删除
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='delete')
     *
     * @param Input $input
     * @return array
     */
    public function deleteMultiCost(Input $input)
    {
        $input->verify(['ids']);

        if (!is_array($input['ids'])) {
            $input['ids'] = \GuzzleHttp\json_decode($input['ids'], true);
        }
        if (!is_array($input['ids']) || empty($input['ids'])) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '',
                'data' => $input['ids'],
            ];
        }
        (new CostLogic())->deleteMulti($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 费用报表
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function cost(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new CostLogic())->getList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 费用操作日志
     * @CtrlAnnotation(permissions=['/basic/cost'],log_type='get')
     *
     * @param Input $input
     * @return array
     */
    public function costLog(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new LogLogic())->costLog($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    public function importSalaryTemplate()
    {
        return new CSV('财务-基础数据-工资导入sample.xlsx', 'sample/fa_salary_template.xlsx');
    }

    public function importCostTemplate()
    {
        return new CSV('财务-基础数据-费用导入sample.xlsx', 'sample/fa_cost_template.xlsx');
    }
}
