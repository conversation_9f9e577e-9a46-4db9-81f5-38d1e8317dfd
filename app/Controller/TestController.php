<?php

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Logic\WechatDataLogic;
use App\Model\HttpModel\DiDi\ApprovalModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\SqlModel\Zeda\ExportFileTaskModel;
use App\Param\FeishuStreamCardParam;
use App\Param\Knowledge\KnowledgeParam;
use App\Service\DataBot\DataBotPy;
use App\Service\DataBot\DataBotService;
use App\Service\DataBot\DataBotSession;
use App\Service\EnterpriseDiDiService;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageFormat;
use App\Struct\RedisCache;
use App\Task\ExportFileTask;
use App\Utils\Helpers;
use Exception;

class TestController extends Controller
{
    protected $pass_method = [
        'exportFileTask',
        'dataBot',
        'knowledge',
        'handlerText',
        'test',
        'didi',
    ];

    public function didi()
    {
        $res = (new ApprovalModel())->getAddr(86, '广州中旭未来科技有限公司');

        dd($res);
        $service = new EnterpriseDiDiService();

        $data = '{"uuid":"73c5c8d4ead1d6d84f8b06e4b7656565","event":{"app_id":"cli_a75258559bf8900b","approval_code":"E83AE510-3A47-4126-B7B0-9C0ACE8DB07D","end_time":1750846659,"i18n_resources":[{"is_default":true,"locale":"zh_cn","texts":{"@i18n@48C02BDA8D205CACE9521A4FE292BE34":"公事外出"}}],"instance_code":"DA03CD15-AC59-4B47-98A2-7C94A9027C29","open_id":"ou_3e2622758081e884c302a06a03d39c6f","out_end_time":"2025-06-26 18:30:00","out_image":"","out_interval":21600,"out_name":"@i18n@48C02BDA8D205CACE9521A4FE292BE34","out_reason":"外出参加网络游戏出版分享会","out_start_time":"2025-06-26 12:30:00","out_unit":"HOUR","start_time":1750843309,"tenant_key":"105189295a17175f","type":"out_approval","user_id":"TW7606"},"token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","ts":"1750846660.054733","type":"event_callback"}';

        $decrypt = json_decode($data, true);
        $service->outApproval($decrypt);
        dd(1);
        //        // 清除缓存
//        $date = '2025-06-23';
//        $employee_no_list = [
//            'TW6513',
//            'TW4573',
//            'TW7102',
//        ];
//        foreach ($employee_no_list as $employee_no) {
//            $service->cleanCache($employee_no, $date);
//        }
////
//        dd(1);

        // 模拟用户打卡
        $data = '{"schema":"2.0","header":{"event_id":"a83c0ddb4cce8c9757fba0318df00d2c","token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","create_time":"1750688537141","event_type":"attendance.user_flow.created_v1","tenant_key":"105189295a17175f","app_id":"cli_a75258559bf8900b"},"event":{"bssid":"14:84:77:84:31:62","check_time":"1750688533","comment":"","employee_id":"TW7102","employee_no":"TW7102","is_field":false,"is_wifi":true,"latitude":0,"location_name":"Tanwan(14:84:77:84:31:62)","longitude":0,"photo_urls":[],"record_id":"7519149999227912194","risk_result":0,"ssid":"Tanwan","type":0}}';


        $decrypt = json_decode($data, true);
        $service->userClockOut($decrypt);
        dd(1);


        // 模拟用户审批通过
        $data = '{"uuid":"00eed76c4d5ec58b5251bf6a1c8353f0","event":{"app_id":"cli_a75258559bf8900b","approval_code":"744AD97C-BF36-414C-A8FF-06804111B5FF","instance_code":"E6140BB1-1062-4ACD-A31F-F31F8B9AD2F1","instance_operate_time":"1750688511343","operate_time":"1750688511343","status":"APPROVED","tenant_key":"105189295a17175f","type":"approval_instance","uuid":"c25bfb5b"},"token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","ts":"1750688512.992143","type":"event_callback"}';
        $decrypt = json_decode($data, true);

        $service->clockOutApproval($decrypt);
        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    /**
     * 测试
     *
     * @return array
     */
    public function test()
    {
        $service = new  EnterpriseDiDiService();

        $dd_user_info = $service->getUserInfoByDidi('TW0985', 2);
        $logic = new WechatDataLogic();
        $start_time = 1752768000 - 86400;
        $user_id = 151;
        $theme = '测试推送';
        $list = $logic->getDataSumPush($start_time, $user_id, $theme);
        return [
            'list'         => $list,
            'dd_user_info' => $dd_user_info,
        ];
    }

    public function exportFileTask()
    {
        $row_data = (new ExportFileTaskModel())->getUnFinishOne();
        $data = (new ExportFileTask())->getSettlementSummarizeList($row_data);

        return [
            'data' => $data,
        ];
    }

    /**
     * 知识问答测试
     *
     * @return array
     * @throws Exception
     */
    public function knowledge()
    {
        //        // 获取消息内容，重新总结
//        $message_list = (new FeiShuMessageModel())->getListByChatIdAndLastTime('oc_7cbabbc3c5ba6f6d2e1c4d2bd35476b9','2025-07-09 00:00:00');
//
//        $message_list = GroupAssistantService::formatToLLMMessageList($message_list);
//        $message_summary = new MessageSummary();
//
//        $summary = $message_summary->generateSummary($message_list, '');
//
//        dd($summary);


        $query = '什么是推理大模型？';
        $union_id = 'on_b8ef743bfe285ce29e8079b882739aa3';
        $chat_id = '';

        // 清空session
        $session_key = GroupAssistantService::INTENT_SESSION_KEY . $union_id;
        $redis = RedisCache::getInstance();
        $redis->del($session_key);
        $session_key = KnowledgeService::QA_SESSION_KEY . $union_id;
        $redis->del($session_key);


        //        // 意图识别
        $result = (new GroupAssistantService())->intent($query, $chat_id, 'p2p', $union_id);
        ////
//        dd($result);
        $param = [
            'query'      => $query,
            'union_id'   => $union_id,
            'chat_id'    => $chat_id,
            'type'       => 'p2p',
            'start_date' => '',
            'end_date'   => ''
        ];
        // 知识问答
        $param = new KnowledgeParam($param);

        // 初始化流式输出的卡片参数
        $message_param = new FeishuStreamCardParam([
            'receive_id'          => $union_id,
            'receive_id_type'     => 'union_id',
            'tenant_access_token' => (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET),
            'content'             => '正在解析消息...',
        ]);

        // 发个消息让用户等待一下
        FeiShuService::stream($message_param);

        (new KnowledgeService())->getKnowledgeAnswer($param, $message_param);

        FeiShuService::updateStreamModel($message_param);

        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    public function dataBot()
    {
        $data_bot_service = new DataBotService('戴焕其', 151, Helpers::getLogger('wechat_data_bot'));

        $user_id = 151;
        //        $user_query = '再加一个注册数指标';
//        $user_query = '前十的呢？';
        $user_query = '查看一下贪玩所有渠道组最近一个月的消耗和注册数，还有流水，按天和渠道组查看';


        $output = $data_bot_service->run($user_query, 1);

        // 是否是数据分析
        $analyse = $output['analyse'] ?? false;

        if ($analyse) {
            // 多维度数据先不考虑，直接拿一条先 TODO
            $table_list = $output['draw_data'][0] ?? [];
            $analyse_respond = DataBotPy::analyse($user_query, 1, $table_list, $user_id, Helpers::getLogger('wechat_data_bot'));

            $output['analyse_respond'] = $analyse_respond;

        } else {
            $draw_data = $output['draw_data'];
            // 循环，返回多张图
            foreach ($draw_data as $data_item) {
                // 生成图片
                $png_filename = DataBotPy::plotting(1, $data_item, $user_id, Helpers::getLogger('wechat_data_bot'));
            }
        }


        $data_bot_session = new DataBotSession(151, 1);
        $route_name = RedisCache::getInstance()->get($data_bot_session->genRouterNameSessionKey(1, 151));
        $output['session_list'] = $data_bot_session->getSessionList($data_bot_session->genDataBotSessionKey($route_name, 1, 151));
        $output['dispatch_router'] = $data_bot_session->getSessionList($data_bot_session->genDispatchRouteSessionKey(1, 151));
        $output['all_session_list'] = DataBotSession::getAllConversationHistory(1, $user_id);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $output
        ];
    }


    public function handlerText()
    {
        $message_id = 'om_x100b4a9397f764240f3fbb96378cd51';

        $file_key = 'file_v3_00ni_29f35640-5433-4ed5-87f9-9e4a1f6a64bg';
        $filename = '202505-江西贪玩-任德权-神器-云账户-cps（抖音、快手）1.xlsx';
        $cut_off = 0;
        MessageFormat::handlerFile($message_id, $file_key, $filename, $cut_off);

        dd(1);
        // 示例用法
        $cut_off = 0;
        $inputText = "https://lx3qcyzne8.feishu.cn/wiki/S9sJwhUiIiTb65kmPNJctldtnde?sheet=50pXYr在线表的都看看啊";
        $outputText = MessageFormat::handlerText($inputText, 'om_46d1c07ea1d2c5c39ee5d149e196b6b0', $cut_off, false);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $outputText
        ];
    }

}