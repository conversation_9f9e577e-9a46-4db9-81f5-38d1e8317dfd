<?php
/**
 * Created by PhpStorm.
 * User: zhenorzz
 * Date: 2019/8/1
 * Time: 14:38
 */

namespace App\Response;

/**
 * Class Html
 * @package App\Response
 * response Html($template, $data)
 *
 * template:
 * {LOOP:$people}
 * {$name}{$surname}
 * {ENDLOOP:$people}
 */
class Html implements Send
{
    /**
     * A template file path with variables placeholders {$variable}.
     * @var string $template_file
     */
    private $template_file;

    private $stack = [];

    /**
     * A key => value store of variable names and values.
     * @var string $template_file
     */
    private $variables;

    public function __construct($template_file, array $variables = [])
    {
        $this->template_file = $template_file;
        $this->variables = $variables;
    }

    public function send(\Swoole\Http\Response $response)
    {
        $response->end($this->replaceVariablesInTemplate());
        return true;
    }


    /**
     * A function to fill the template with variables, returns filled template.
     *
     * @return string
     */

    private function replaceVariablesInTemplate()
    {
        $this->stack = [];
        if (file_exists($this->template_file)) {
            $template = file_get_contents($this->template_file);
        } else {
            $template = $this->template_file;
        }
        $template = str_replace('<', '<?php echo \'<\'; ?>', $template);
        $template = preg_replace('/{\$(\w+)}/', '<?php $this->showVariable(\'$1\'); ?>', $template);
        $template = preg_replace('/{LOOP:\$(\w+)}/', '<?php foreach ($this->variables[\'$1\'] as $ELEMENT): $this->wrap($ELEMENT); ?>', $template);
        $template = preg_replace('/{ENDLOOP:\$(\w+)}/', '<?php $this->unwrap(); endforeach; ?>', $template);
        $template = '?>' . $template;
        return $this->run($template);
    }

    private function showVariable($name)
    {
        if (isset($this->variables[$name])) {
            echo $this->variables[$name];
        } else {
            echo '{' . $name . '}';
        }
    }

    private function wrap($element)
    {
        $this->stack[] = $this->variables;
        foreach ($element as $k => $v) {
            $this->variables[$k] = $v;
        }
    }

    private function unwrap()
    {
        $this->variables = array_pop($this->stack);
    }

    private function run()
    {
        ob_start();
        eval (func_get_arg(0));
        return ob_get_clean();
    }
}