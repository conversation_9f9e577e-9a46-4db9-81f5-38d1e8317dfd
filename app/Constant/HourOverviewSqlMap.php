<?php
/**
 * 分时总览的一些SQL字段映射
 */

namespace App\Constant;

class HourOverviewSqlMap
{
    /**
     * 需要用到click表的指标
     */
    const NEED_CLICK_TABLE = [
        'click_muid_distinct_count_ldy' => 'sum( IF ( t.ad_platform_id = 0, t.hour_click_muid_distinct_count, 0 ) ) AS click_muid_distinct_count_ldy',
        'click_old_muid_count_ldy'      => 'sum( IF ( t.ad_platform_id = 0, t.is_old_root_game_muid_count, 0 ) ) AS click_old_muid_count_ldy',
        'click_ip_distinct_count_ldy'   => 'sum( IF ( t.ad_platform_id = 0, t.hour_click_ip_distinct_count, 0 ) ) AS click_ip_distinct_count_ldy',
        'click_muid_distinct_count'     => 'sum( IF ( t.ad_platform_id != 0, t.hour_click_muid_distinct_count, 0 ) ) AS click_muid_distinct_count',
        'click_old_muid_count'          => 'sum( IF ( t.ad_platform_id != 0, t.is_old_root_game_muid_count, 0 ) ) AS click_old_muid_count',
        'click_ip_distinct_count'       => 'sum( IF ( t.ad_platform_id != 0, t.hour_click_ip_distinct_count, 0 ) ) AS click_ip_distinct_count',
        'click_count_ldy'               => 'sum( IF ( t.ad_platform_id = 0, t.hour_click_count, 0 ) ) AS click_count_ldy',
        'click_muid_count_ldy'          => 'sum( IF ( t.ad_platform_id = 0, t.hour_click_muid_count, 0 ) ) AS click_muid_count_ldy',
        'click_ip_count_ldy'            => 'sum( IF ( t.ad_platform_id = 0, t.hour_click_ip_count, 0 )) AS click_ip_count_ldy',
        'click_count'                   => 'sum( IF ( t.ad_platform_id != 0, t.hour_click_count, 0 ) ) AS click_count',
        'click_muid_count'              => 'sum( IF ( t.ad_platform_id != 0, t.hour_click_muid_count, 0 ) ) AS click_muid_count',
        'click_ip_count'                => 'sum( IF ( t.ad_platform_id != 0, t.hour_click_ip_count, 0 )) AS click_ip_count',
    ];

    /**
     * 需要用到action表的指标
     */
    const NEED_ACTION_TABLE = [
        'action_muid_distinct_count' => 'sum( t.hour_action_muid_distinct_count ) AS action_muid_distinct_count',
        'action_ip_distinct_count'   => 'sum( t.hour_action_ip_distinct_count ) AS action_ip_distinct_count',
        'action_old_muid_count'      => 'sum( t.is_old_root_game_muid_count ) AS action_old_muid_count',
        'action_count'               => 'sum( t.hour_action_count ) AS action_count',
        'action_muid_count'          => 'sum( t.hour_action_muid_count ) AS action_muid_count',
        'action_ip_count'            => 'sum( t.hour_action_ip_count ) AS action_ip_count',
    ];

    /**
     * 需要用到reg表的指标
     */
    const NEED_REG_TABLE = [
        'reg_muid_count'                       => 'sum( t.hour_reg_muid_count ) AS reg_muid_count',
        'reg_ip_count'                         => 'sum( t.hour_reg_ip_count ) AS reg_ip_count',
        'reg_old_muid_count'                   => 'sum( t.is_old_root_game_muid_count ) AS reg_old_muid_count',
        'role_create_count'                    => 'sum( t.is_create_role_count ) AS role_create_count',
        'reg_uid_count'                        => 'sum( t.hour_reg_uid_count ) AS reg_uid_count',
        'reg_muid_distinct_count'              => 'sum( t.hour_reg_muid_distinct_count ) AS reg_muid_distinct_count',
        'reg_ip_distinct_count'                => 'sum( t.hour_reg_ip_distinct_count ) AS reg_ip_distinct_count',
        'second_login_count'                   => 'sum( t.second_login_count ) AS second_login_count',
        'hour_three_login_uid_count'           => 'sum(t.hour_three_login_uid_count) as hour_three_login_uid_count',
        'hour_three_login_muid_distinct_count' => 'sum(t.hour_three_login_muid_distinct_count) as hour_three_login_muid_distinct_count',
    ];

    /**
     * 需要用到pay表的指标（暂时pay表不用判断）
     */
    const NEED_PAY_TABLE = [
        'pay_uid_count'       => 'sum( hour_pay_uid_count ) AS pay_uid_count',
        'first_pay_uid_count' => 'sum( IF ( pay_date_hour = first_pay_date_hour, hour_pay_uid_count, 0 ) ) AS first_pay_uid_count',
        'pay_money'           => 'sum( hour_pay_money ) AS pay_money',
        'pay_times'           => 'sum( hour_pay_times ) AS pay_times',
    ];

    const NEED_MEDIA_TABLE = [
        'media_show_count'         => "sum( t.show) AS media_show_count",
        'media_click_count'        => "sum( t.click) AS media_click_count",
        'media_convert_count'      => "sum( t.convert) AS media_convert_count",
        'media_deep_convert_count' => "sum( t.deep_convert) AS media_deep_convert_count",
    ];

    /**
     * 所有指标
     */
    const ALL_TARGET_MAP = [
        // 媒体转化链
        'click_ip_ldy_same_percent'            => ['click_ip_ldy_same_percent', 'click_ip_distinct_count_ldy', 'click_ip_count_ldy'],
        'click_muid_ldy_same_percent'          => ['click_muid_ldy_same_percent', 'click_muid_distinct_count_ldy', 'click_muid_count_ldy'],
        'click_old_muid_count_ldy_percent'     => ['click_old_muid_count_ldy_percent', 'click_old_muid_count_ldy', 'click_count_ldy'],
        'click_ip_same_percent'                => ['click_ip_same_percent', 'click_ip_distinct_count', 'click_ip_count'],
        'click_muid_same_percent'              => ['click_muid_same_percent', 'click_muid_distinct_count', 'click_muid_count'],
        'click_old_muid_count_percent'         => ['click_old_muid_count_percent', 'click_old_muid_count', 'click_count'],
        'action_ip_same_percent'               => ['action_ip_same_percent', 'action_ip_distinct_count', 'action_ip_count'],
        'action_muid_same_percent'             => ['action_muid_same_percent', 'action_muid_distinct_count', 'action_muid_count'],
        'action_old_muid_count_percent'        => ['action_old_muid_count_percent', 'action_old_muid_count', 'action_count'],
        'reg_ip_same_percent'                  => ['reg_ip_same_percent', 'reg_ip_distinct_count', 'reg_ip_count'],
        'reg_muid_same_percent'                => ['reg_muid_same_percent', 'reg_muid_distinct_count', 'reg_muid_count'],
        'reg_old_muid_count_percent'           => ['reg_old_muid_count_percent', 'reg_old_muid_count', 'reg_muid_count'],
        'role_create_count_percent'            => ['role_create_count_percent', 'role_create_count', 'reg_uid_count'],
        'action_uid_reg_percent'               => ['action_uid_reg_percent', 'action_count', 'reg_uid_count'],
        'action_role_create_percent'           => ['action_role_create_percent', 'action_count', 'role_create_count'],
        'role_create_count'                    => ['role_create_count'],

        // 转化率
        'click_ldy_action_percent'             => ['click_ldy_action_percent', 'action_count', 'click_count_ldy'],
        'click_action_percent'                 => ['click_action_percent', 'action_count', 'click_count'],
        'click_ldy_uid_reg_percent'            => ['click_ldy_uid_reg_percent', 'reg_uid_count', 'click_count_ldy'],
        'click_uid_reg_percent'                => ['click_uid_reg_percent', 'reg_uid_count', 'click_count'],
        'click_ldy_role_create_percent'        => ['click_ldy_role_create_percent', 'role_create_count', 'click_count_ldy'],
        'click_role_create_percent'            => ['click_role_create_percent', 'role_create_count', 'click_count'],
        'true_uid_rate'                        => ['true_uid_rate', 'true_uid_count', 'reg_uid_count'],

        // 24小时
        'cost_per_reg'                         => ['cost_per_reg', 'cost_money', 'reg_uid_count'],
        'cost_per_pay'                         => ['cost_per_pay', 'cost_money', 'first_pay_uid_count'],
        'roi'                                  => ['roi', 'pay_money', 'cost_money'],
        'ltv'                                  => ['ltv', 'reg_total_pay_money', 'reg_uid_count'],
        'second_login_percent'                 => ['second_login_percent', 'second_login_count', 'reg_uid_count'],
        'total_pay_percent'                    => ['total_pay_percent', 'first_pay_uid_count', 'reg_uid_count'],
        'reg_total_pay_money'                  => ['reg_total_pay_money', 'pay_money'],

        // 其他不需要计算的指标
        'click_count_ldy'                      => ['click_count_ldy'],
        'click_ip_count_ldy'                   => ['click_ip_count_ldy'],
        'click_ip_distinct_count_ldy'          => ['click_ip_distinct_count_ldy'],
        'click_muid_count_ldy'                 => ['click_muid_count_ldy'],
        'click_muid_distinct_count_ldy'        => ['click_muid_distinct_count_ldy'],
        'click_old_muid_count_ldy'             => ['click_old_muid_count_ldy'],
        'click_count'                          => ['click_count'],
        'click_ip_count'                       => ['click_ip_count'],
        'click_ip_distinct_count'              => ['click_ip_distinct_count'],
        'click_muid_count'                     => ['click_muid_count'],
        'click_muid_distinct_count'            => ['click_muid_distinct_count'],
        'click_old_muid_count'                 => ['click_old_muid_count'],
        'action_count'                         => ['action_count'],
        'action_ip_count'                      => ['action_ip_count'],
        'action_ip_distinct_count'             => ['action_ip_distinct_count'],
        'action_muid_count'                    => ['action_muid_count'],
        'action_muid_distinct_count'           => ['action_muid_distinct_count'],
        'action_old_muid_count'                => ['action_old_muid_count'],
        'reg_uid_count'                        => ['reg_uid_count'],
        'reg_ip_count'                         => ['reg_ip_count'],
        'reg_ip_distinct_count'                => ['reg_ip_distinct_count'],
        'reg_muid_count'                       => ['reg_muid_count'],
        'reg_muid_distinct_count'              => ['reg_muid_distinct_count'],
        'reg_old_muid_count'                   => ['reg_old_muid_count'],
        'true_uid_count'                       => ['true_uid_count'],
        'hour_three_login_uid_count'           => ['hour_three_login_uid_count'],
        'hour_three_login_muid_distinct_count' => ['hour_three_login_muid_distinct_count'],
        'first_day_pay_times'                  => ['first_day_pay_times'],
        'total_pay_times'                      => ['total_pay_times'],
        'cost_first_day_pay_times'             => ['first_day_pay_times', 'cost_first_day_pay_times', 'cost_money'],
        'cost_total_pay_times'                 => ['total_pay_times', 'cost_money', 'cost_total_pay_times'],

        "media_show_cost"          => ["media_show_cost", "cost_money", "media_show_count"],
        "media_click_rate"         => ["media_click_rate", "media_show_count", "media_click_count"],
        "media_convert_rate"       => ["media_convert_rate", "media_convert_count", "media_click_count"],
        "media_deep_convert_rate"  => ["media_deep_convert_rate", "media_deep_convert_count", "media_click_count"],
        "media_show_count"         => ["media_show_count"],
        "media_click_count"        => ["media_click_count"],
        "media_convert_count"      => ["media_convert_count"],
        "media_deep_convert_count" => ["media_deep_convert_count"],
    ];

}
