<?php


namespace App\Constant;


class TableTimeColumn
{
    const MAP = [
        //TODO::共用，可以根据不同路由区分， [ROUTE_ID => ['table_name' => 'time_field_name']]
        'v2_dws_day_login_log'                         => 'game_reg_date',
        'v2_dwd_day_uid_login_log'                     => 'game_reg_time',
        'v2_dws_hour_game_reg_log'                     => 'game_reg_date_hour',
        'v2_dwd_game_uid_reg_log'                      => 'game_reg_time',
        'v2_dws_day_game_reg_log'                      => 'game_reg_date',
        'v2_dwd_ad_hour_cost_log'                      => 'tdate',
        'v2_dwd_day_cost_log'                          => 'tdate',
        'v2_dwd_hour_cost_log'                         => 'tdate',
        'v2_dws_day_pay_log'                           => 'game_reg_date',
        'v2_dws_hour_pay_log'                          => 'game_reg_date_hour',
        'v2_ods_device_action_log'                     => 'action_time',
        'v2_dws_day_device_action_log'                 => 'action_date',
        'v2_dws_hour_device_action_log'                => 'action_date_hour',
        'v2_ods_sdk_step_log'                          => 'sdk_action_time',
        'v2_dws_day_click_log'                         => 'click_date',
        'v2_dws_hour_click_log'                        => 'click_date_hour',
        'v2_dwd_main_uid_create_log'                   => 'create_time',
        'v2_dwd_day_main_uid_login_log'                => 'create_time',
        'v2_ods_uid_login_log'                         => 'reg_time',
        'v2_ods_pay_order_log'                         => 'reg_time',
        'v2_dws_day_main_pay_log'                      => 'create_date',
        'v2_ads_day_overview_log'                      => 'log_date',
        'v2_ads_day_root_game_overview_log'            => 'log_date',
        'dwd_media_ad3_common_hour_data_log'           => 'cost_date',
        'ods_toutiao_creative_hour_data_log'           => 'cost_date',
        'ods_tencent_ad_hour_data_log'                 => 'cost_date',
        'ods_kuaishou_creative_hour_data_log'          => 'cost_date',
        'v2_ads_day_overview_log_tmp'                  => 'log_date', // 临时使用
        'v2_dwd_game_uid_reg_log_tmp'                  => 'game_reg_time',// 临时使用
        'dwd_game_uid_pay_money_log'                   => 'game_reg_time',
        'v2_ods_uid_reg_log'                           => 'reg_time',
        'v2_dws_day_role_login_log'                    => 'create_date',
        'v2_dws_day_role_create_log'                   => 'create_date',
        'v2_dws_day_role_pay_log'                      => 'create_date',
        'v2_dwd_day_role_login_log'                    => 'create_time',
        'v2_dwd_role_create_log'                       => 'create_time',
        'v2_dws_day_main_create_log'                   => 'create_date',
        'v2_dwd_ad_day_cost_log'                       => 'tdate',
        'v2_dwd_game_role_create_log'                  => 'create_time',
        'v2_dwd_game_uid_reg_source_log'               => 'game_reg_time',
        'v2_dwd_game_uid_reg_statistics_log'           => 'game_reg_time',
        'v2_dwd_root_game_uid_reg_log'                 => 'root_game_reg_time',
        'v2_dws_day_audit_capital_flow_log'            => 'log_date',
        'ods_kuaishou_fund_daily_flow_log'             => 'date',
        'ods_tencent_fund_statements_daily_log'        => 'date',
        'ods_tencent_fund_statements_detailed_log'     => 'date',
        'ods_toutiao_fund_daily_log'                   => 'date',
        'ods_toutiao_fund_transaction_log'             => 'date',
        'ods_wechat_fund_statements_detailed_log'      => 'date',
        'v2_dwd_finance_account_day_cost_log'          => 'cost_date',
        'v2_dws_day_root_game_device_action_log'       => 'action_date',
        'v2_dws_day_root_game_login_log'               => 'game_reg_date',
        'v2_dws_day_root_game_pay_log'                 => 'game_reg_date',
        'v2_dws_day_root_game_reg_log'                 => 'game_reg_date',
        'v2_dwd_root_game_device_action_log'           => 'action_time',
        'v2_dwd_day_root_game_uid_login_log'           => 'root_game_reg_time',
        'v2_dws_hour_root_game_device_action_log'      => 'action_date_hour',
        'v2_dws_hour_root_game_reg_log'                => 'game_reg_date_hour',
        'v2_dws_hour_root_game_pay_log'                => 'game_reg_date_hour',
        'dwd_root_game_uid_pay_money_log'              => 'root_game_reg_time',
        'dwd_root_game_back_uid_pay_money_log'         => 'root_game_reg_time',
        'v2_dim_agent_rebate_id'                       => 'rebate_start_time',
        'v2_dim_agent_group_rebate_id'                 => 'rebate_start_time',
        'v2_dws_hour_root_game_back_reg_log'           => 'game_reg_date_hour',
        'v2_dws_hour_root_game_back_pay_log'           => 'game_reg_date_hour',
        'v2_dws_hour_root_game_back_device_action_log' => 'action_date_hour',
        'v2_dws_day_root_game_back_reg_log'            => 'game_reg_date',
        'v2_dws_day_root_game_back_pay_log'            => 'game_reg_date',
        'v2_dws_day_root_game_back_login_log'          => 'game_reg_date',
        'v2_dws_day_root_game_back_device_action_log'  => 'action_date',
        'v2_dwd_root_game_back_uid_reg_log'            => 'root_game_reg_time',
        'v2_dwd_root_game_back_device_action_log'      => 'action_time',
        'v2_ads_day_root_game_back_overview_log'       => 'log_date',
        'v2_dwd_day_root_game_back_uid_login_log'      => 'root_game_reg_time',
        'wechat_push_data_dms'                         => 'tdate',
        'ods_toutiao_ad_log'                           => 'ad_create_time',
        'v2_dim_ad_account_id'                         => 'ad2_create_time',
        'v2_dws_day_show_log'                          => 'show_date',
        'dwd_media_ad2_common_day_data_log'            => 'cost_date',
        'v2_ods_ad_inspire_cost_log'                   => 'stat_date',
        'v2_dwd_master_date_log'                       => 'start_date',
        'dwd_media_ad4_common_day_data_log'            => 'cost_date',
        'v3_ads_day_root_game_overview_log'            => 'log_date',
        'v3_ads_day_root_game_back_overview_log'       => 'log_date',
        'compress_data_log'                            => 'cost_date',
        'compress_overview_log'                        => 'log_date',
        'ods_tencent_ad_site_set_day_data_log'         => 'cost_date',
        'ods_tencent_ad4_site_set_day_data_log'        => 'cost_date',
        'view_dwd_media_ad3_common_hour_data_log'      => 'cost_date',
    ];


    const GAME_DIMENSION_MAP = [
        'v2_dws_day_root_game_pay_log'      => 'pay_date',
        'v2_dws_day_pay_log'                => 'pay_date',
        'v2_dws_day_root_game_back_pay_log' => 'pay_date',


        'v2_dwd_game_uid_reg_log'           => 'login_date',
        'v2_dwd_root_game_uid_reg_log'      => 'login_date',
        'v2_dwd_root_game_back_uid_reg_log' => 'login_date',
    ];
}
