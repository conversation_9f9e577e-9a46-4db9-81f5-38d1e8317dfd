<?php

namespace App\Constant;
/**
 * 快手枚举
 * Class KuaishouEnum
 * @package App\Constant
 * User: zsp
 * Date: 2022/3/24 0024
 * Time: 11:31
 */
class KuaishouEnum
{
    //计划类型
    const TYPE_APP          = '2';//提升应用安装
    const TYPE_SHOP         = '3';//获取电商下单
    const TYPE_BRAND        = '4';//推广品牌活动
    const TYPE_CLUES        = '5';//收集销售线索
    const TYPE_ACTIVITY     = '7';//提高应用活跃
    const TYPE_GOODS        = '9';//商品库推广
    const TYPE_LIVE         = '16';//粉丝/直播推广
    const TYPE_WECHAT_MINI_GAME = '32';//微信小游戏
    const TYPE_KUAISHOU_MINI_GAME = '19';//快手小游戏

    //优化目标出价类型
    const BID_TYPE_OCPM             = 10;//OCPM
    const BID_TYPE_MAX_CONVERSION   = 12;//最大转化

    //预约广告类型
    const APPOINTED_IOS = 1;
    const APPOINTED_ANDROID = 2;

    //资源位置
    const SCENE_ID_UNION    = 5;//联盟
    const SCENE_ID_SLIDE    = 6;//上下滑大屏广告
    const SCENE_ID_SPLASH   = 27;//开屏

    //url 类型
    const WEB_URI_TYPE_MAGIC_SITE               = 1;
    const WEB_URI_TYPE_DOWNLOAD_PAGE            = 2;
    const WEB_URI_TYPE_PROGRAM_DOWNLOAD_PAGE    = 3;
    const WEB_URI_TYPE_DEEP_LINK_WECHAT    = 4;

    //操作系统版本
    const PLATFORM_OS_NONE      = 0;
    const PLATFORM_OS_ANDROID   = 1;
    const PLATFORM_OS_IOS       = 2;

    const ANDROID_OSV_NONE  = 3;//不限
    const IOS_OSV_NONE      = 6;//不限

    //转化类型
    const CONVERSION_TYPE_LIVE = 6;//直播

    //创意制作方式
    const UNIT_TYPE_CUSTOM          = 4;
    const UNIT_TYPE_PROGRAM         = 5;
    const UNIT_TYPE_PROGRAM2_0      = 7;
    const UNIT_TYPE_PROGRAM3_0      = 8;

    //礼包码发送时机
    const TARGET_ACTION_TYPE_IOS            = 2;//没有从应用商店下载
    const TARGET_ACTION_TYPE_BEGIN_DOWNLOAD = 30;//开始下载后

    //游戏类型
    const GAME_TYPE_ANDROID = '安卓';
    const GAME_TYPE_IOS     = 'IOS';

    //礼包
    const AD_CODE_TYPE_GIFT = 'gift';

    const OCPX_ACTION_TYPE_BEHAVIOR     = 2;//行为数
    const OCPX_ACTION_TYPE_ACTIVE       = 180;//激活数
    const OCPX_ACTION_TYPE_PAY          = 190;//付费
    const OCPX_ACTION_TYPE_ROI_1DAY     = 191;//首日ROI
    const OCPX_ACTION_TYPE_PAY_7DAY     = 739;//7日付费次数
    const OCPX_ACTION_TYPE_ROI_7DAY     = 774;//7日付费次数
    const OCPX_ACTION_TYPE_ENTER_MINI_PROGRAM  = 937;//小程序ROI
    const OCPX_ACTION_TYPE_ENTER_MINI_PROGRAM_EXT  = 943;//937变成943 扩展处理 小程序ROI
    const OCPX_ACTION_TYPE_ROI_7DAY_HISTORY     = 777;//7日付费次数
    const OCPX_ACTION_TYPE_ACTIVE_PAY   = 810;//激活付费

    //深度转化目标
    const DEEP_CONVERSION_TYPE_PAY_ROI  = 92;//付费ROI
    const DEEP_CONVERSION_TYPE_ENTER_MINI_PROGRAM  = 179;//小程序ROI

    //最大转化投放
    const BIG_TYPE_MAX_CONVERSION   = 1;//最大转化
    const BIG_TYPE_NORMAL           = 0;//普通计划

    //投放状态
    const AD_OPTION_DISABLE = 'disable';//暂停
    const PUT_STATUS_STOP   = 2;//暂停
    const PUT_STATUS_MANUAL = '';//投放

    //素材类型
    const CREATIVE_MATERIAL_TYPE_VERTICAL       = 1;//竖屏视频
    const CREATIVE_MATERIAL_TYPE_HORIZONTAL     = 2;//横屏视频
    const CREATIVE_MATERIAL_TYPE_SPLASH_VIDEO   = 11;//开屏视频
    const CREATIVE_MATERIAL_TYPE_SPLASH_PHOTO   = 12;//开屏图片

    //直播类型
    const LIVE_CREATIVE_TYPE_LIVE   = 3;//直播直投
    const LIVE_CREATIVE_TYPE_VIDEO  = 4;//视频引流

    const NONE = [
        'NONE',
        'buxian',
        'none'
    ];

    //APP 行为
    const TARGETING_TAGS_TYPE_APP_INTEREST      = 'APP_INTEREST';
    const TARGETING_TAGS_TYPE_APP_INTEREST_ID   = 'APP_INTEREST_ID';

    //广告计划状态
    const CAMPAIGN_STATUS_UNLIMITED = -2;//不限

    const CAMPAIGN_PUT_STATUS_DELETED   = 3;//已经删除

    //过滤APP
    const FILTER_CONVERTED_LEVEL_APP = 5;


    // 创建搜索快投广告组
    const QUICK_SEARCH = 1;   //	是否开启快投，0：关闭，1：开启
    const QUICK_SEARCH_EXT = 1;   //搜索广告	 是否开启搜索广告，0：关闭，1：开启
    const QUICK_SEARCH_EXT_NO = 0;   //搜索快投	是否开启搜索广告，0：关闭，1：开启
    const EXTEND_SEARCH = 1;   //	智能扩词开启状态，0：关闭，1：开启
    const TERGET_SEARCH = 1;   //	是否开启搜索人群探索，0：关闭，1：开启，开启后会在搜索流量上进行定向突破

    const OUTER_LOOP_NATIVE_OPEN = 1; //是否开启原生  1 开启
    const OUTER_LOOP_NATIVE_CLOSE = 0; //是否开启原生 0 关闭

    const JUXING_TALENT = 0; // 聚星达人
    const BLUE_V_TALENT = 1; // 蓝V 服务号达人
    const COMMON_TALENT = 2; // 普通快手号

    const TALENT_MEDIA = 1; // 媒体素材视频
    const TALENT_SYS = 0; // 系统素材视频

    const SEARCH_CAMPAIGN = 1; // 搜索广告计划
    const INFO_CAMPAIGN = 0; // 信息流广告计划

    const OPEN_NO_WORD = 1; //开启否词
    const NO_WORD_SHORT = 2;
    const NO_WORD_ACCURATE = 1;

    const KUAISHOU_SEARCH = 2;
}

