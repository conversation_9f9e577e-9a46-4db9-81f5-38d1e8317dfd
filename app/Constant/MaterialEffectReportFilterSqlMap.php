<?php

namespace App\Constant;

class MaterialEffectReportFilterSqlMap
{

    const DATAMEDIA_DATABASE = 'tanwan_datamedia';
    const DATAHUB_DATABASE = 'tanwan_datahub';

    const NEED_JOIN_INEFFICIENT_MATERIAL = [
        'is_inefficient', 'tt_is_similar_material', 'tt_is_ad_high_quality', 'tt_is_first_publish_material', 'tt_is_ad_low_quality_material', 'tt_is_ecp_low_quality_material', 'tt_message_ad_low_quality_material', 'tt_message_ecp_low_quality_material'
    ];

    const NEED_TENCENT_SITE_SET = [
        'gdt_inventory_subdivision', 'gdt_xqxs_inventory_subdivision'
    ];

    const NEED_JOIN_MATERIAL_REJECT = [
        'tt_material_reject_reason', 'tt_material_suggestion'
    ];

    const AWEME_ITEM_ID_FIELD = [
        0 => 'item_id',
        MediaType::TOUTIAO => 'aweme_item_id',
        MediaType::TENCENT => 'item_id',
        MediaType::KUAISHOU => 'item_id',
        MediaType::BAIDU => 'item_id',
    ];

    const TABLE = [
        'material' => 'tanwan_datamedia.ods_material_log',
        'material_file' => 'tanwan_datamedia.ods_material_file_log',
        'material_theme' => 'tanwan_datamedia.ods_material_theme',
        'material_label' => 'tanwan_datamedia.ods_material_label',
        'toutiao_inefficient_material_log' => 'ods_toutiao_inefficient_material_log',
        'toutiao_promotion_reject_reason_log' => 'ods_toutiao_promotion_reject_reason_log',

        'ad1_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad1_common_log',
            MediaType::TOUTIAO => 'tanwan_datamedia.ods_toutiao_campaign_log',
            MediaType::TENCENT => 'tanwan_datamedia.ods_tencent_campaign_log',
            MediaType::KUAISHOU => 'tanwan_datamedia.ods_kuaishou_campaign_log',
            MediaType::BAIDU> 'tanwan_datamedia.ods_baidu_campaign_log',
        ],
        // **驱动表** 媒体广告二级公共维度表
        'ad2_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad2_common_log',
            MediaType::TOUTIAO => 'tanwan_datamedia.ods_toutiao_ad_log',
            MediaType::TENCENT => 'tanwan_datamedia.ods_tencent_adgroup_log',
            MediaType::KUAISHOU => 'tanwan_datamedia.ods_kuaishou_unit_log',
            MediaType::BAIDU => 'tanwan_datamedia.ods_baidu_ad_log',
        ],
        // **驱动表** 媒体广告三级公共维度表
        'ad3_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad3_common_log',
            MediaType::TOUTIAO => 'tanwan_datamedia.ods_toutiao_creative_log',
            MediaType::TENCENT => 'tanwan_datamedia.ods_tencent_ad_log',
            MediaType::KUAISHOU => 'tanwan_datamedia.ods_kuaishou_creative_log',
            MediaType::BAIDU => 'tanwan_datamedia.ods_baidu_creative_log',
        ],

        'ad4_common_log' => [
            0 => self::DATAMEDIA_DATABASE . '.dwd_media_ad4_common_log',
            MediaType::TENCENT => self::DATAMEDIA_DATABASE . '.ods_tencent_ad4_log'
        ],
        // 媒体广告三级每小时的数据表(
        // 用于计算每个素材每小时的实际消耗价格[媒体给的折扣价(cost)]，每个素材每小时原消耗价格(ori_cost)，每个素材每小时的展示数,
        // 每个素材每小时的点击数,每个素材每小时的激活数,每个素材每小时的转化数,每个素材每小时的注册数,每个素材每小时的付费人数,
        // 每个素材每小时所属计划的site_id，每个素材每小时所属计划site_id中当即小时的game_id(每个小时的game_id会改变)
        // )
        'hour_data_log' => [
//            0 => 'tanwan_datamedia.dwd_media_ad3_common_hour_data_log',
            0 => self::DATAMEDIA_DATABASE . '.dwd_media_ad4_common_day_data_log',
            MediaType::TOUTIAO => 'tanwan_datamedia.ods_toutiao_creative_hour_data_log',
//            MediaType::TENCENT => 'tanwan_datamedia.ods_tencent_ad_hour_data_log',
            MediaType::TENCENT => self::DATAMEDIA_DATABASE . '.ods_tencent_ad4_day_data_log',
            MediaType::KUAISHOU => 'tanwan_datamedia.ods_kuaishou_creative_hour_data_log',
            MediaType::BAIDU => 'tanwan_datamedia.ods_baidu_creative_hour_data_log',
        ],
        'account_log_table' => [
            0 => 'tanwan_datamedia.dwd_media_account_common_log',
            MediaType::TOUTIAO => 'tanwan_datamedia.ods_toutiao_account_log',
            MediaType::TENCENT => 'tanwan_datamedia.ods_tencent_account_log',
            MediaType::KUAISHOU => 'tanwan_datamedia.ods_kuaishou_account_log',
            MediaType::BAIDU => 'tanwan_datamedia.ods_baidu_account_log',
        ],

        //// 用户注册记录表(用于算出累计充值金额，累计充值人数，累计注册人数)
        //'reg_log' => 'tanwan_datahub.v2_dwd_game_uid_reg_log',
        // 媒体广告三级每日业务数据(用于计算出每日付费金额，每日付费人数，每日激活设备数，每日注册人数，每日注册设备数，创角数)
        'overview_log' => [
            0 => [
                0 => self::DATAHUB_DATABASE . '.v3_ads_day_root_game_overview_log',
                MediaType::TOUTIAO => 'tanwan_datahub.v2_ads_day_root_game_overview_log',
                MediaType::TENCENT => self::DATAHUB_DATABASE . '.v3_ads_day_root_game_overview_log',
                MediaType::KUAISHOU => 'tanwan_datahub.v2_ads_day_root_game_overview_log',
                MediaType::BAIDU => 'tanwan_datahub.v2_ads_day_root_game_overview_log',
            ],
            3 => [
                0 => self::DATAHUB_DATABASE . '.v3_ads_day_root_game_back_overview_log',
                MediaType::TOUTIAO => 'tanwan_datahub.v2_ads_day_root_game_back_overview_log',
                MediaType::TENCENT => self::DATAHUB_DATABASE . '.v3_ads_day_root_game_back_overview_log',
                MediaType::KUAISHOU => 'tanwan_datahub.v2_ads_day_root_game_back_overview_log',
                MediaType::BAIDU => 'tanwan_datahub.v2_ads_day_root_game_back_overview_log',
            ]
        ],
        // 渠道&广告配置关联表(用于与驱动表进行关联，控制驱动表的agent_leader和site_id)
        'dim_agent_site' => 'tanwan_datahub.v2_dim_agent_site_id',
        // 游戏
        'dim_game' => 'tanwan_datahub.v2_dim_game_id',
        // 广告配置&game_id关联表(用于与驱动表和dim_agent_site进行关联，控制驱动表的game_id)
        'dim_site_game' => 'tanwan_datamedia.dim_site_game_id',

        // 头条抖音授权关系表
        'aweme_log' => 'ods_toutiao_aweme_auth_detail_log',
     ];

    //
    const MAIN_COMPUTE = [
        0 => [
            'port_version' => 'third_ad_log.port_version as port_version',
            'media_type' => 'third_ad_log.media_type as media_type',
            'inventory_type' => 'third_ad_log.inventory_type as inventory_type',
            //素材属性数据
            'working_hours' => 'material_log.working_hours as working_hours',
            'material_platform' => 'material_log.material_platform as material_platform',
            'material_name' => 'material_log.name as material_name',
            'material_id' => 'material_log.material_id as material_id',
            'material_filename' => 'material_log.filename as material_filename',
            'material_file_id' => 'material_log.id as material_file_id',
            'signature' => 'third_ad_log.signature as signature',
            'author' => 'material_log.author as author',
            'theme_id' => 'material_log.theme_id as theme_id',
            'theme_pid' => 'material_log.theme_pid as theme_pid',
            'file_type' => 'third_ad_log.file_type as file_type',
            'create_time' => 'material_log.insert_time as create_time',
            'urls' => 'third_ad_log.signature as signature, third_ad_log.file_type as file_type, material_log.zx_play_url as zx_play_url',
            'c_author' => 'material_log.c_author as c_author',
            'a_author' => 'material_log.a_author as a_author',
            'm1_author' => 'material_log.m1_author as m1_author',
            'm2_author' => 'material_log.m2_author as m2_author',
            'm3_author' => 'material_log.m3_author as m3_author',
            'm4_author' => 'material_log.m4_author as m4_author',
            'm5_author' => 'material_log.m5_author as m5_author',
            'actor' => 'material_log.actor as actor',
            'shoot' => 'material_log.shoot as shoot',
            'material_create_date' => 'material_log.insert_time as material_create_date',
            'original' => 'material_log.original as original',
            'size' => "material_log.size as size",
            'effect_grade7' => "material_log.effect_grade7 as effect_grade7",
            'effect_grade30' => "material_log.effect_grade30 as effect_grade30",
            'is_priority' => "material_log.is_priority as is_priority",
            'is_3d' => "material_log.is_3d as is_3d",
            'is_immortal' => "material_log.is_immortal as is_immortal",
            'label' => "material_log.label_id as label",

            //计数项数据
            'count' => "COUNT( DISTINCT third_ad_log.web_creator ) as count",
            'count_ad2' => "COUNT( DISTINCT third_ad_log.count_ad2 ) as count_ad2",
            'count_ad2_deliveried' => "COUNT( DISTINCT data_log.data_log_ad2_id, IF ( data_log.cost > 0, TRUE, NULL ) ) AS count_ad2_deliveried",
            'count_ad2_delivering' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.status = 'AD_STATUS_DELIVERY_OK', TRUE, NULL ) ) AS count_ad2_delivering",
            'count_ad2_undeliveried' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.is_cost = 0, TRUE, NULL ) ) AS count_ad2_undeliveried",
            //'count_cost_date' => [
            //    0 => 'data_log.count_cost_date',
            //    1 => 'SUM(data_log.count_cost_date) as count_cost_date'
            //],
            'count_creative' => "SUM(third_ad_log.count_creative) as count_creative",
            //业务数据
            'platform' => 'third_ad_log.platform as platform',
            'os' => 'third_ad_log.os as os',
            'create_type' => 'third_ad_log.create_type as create_type',
            'account_id' => 'third_ad_log.account_id',
            'account_name' => 'third_ad_log.account_name, third_ad_log.account_id',
            'ad1_id' => 'third_ad_log.ad1_id',
            'ad1_name' => 'third_ad_log.ad1_name, third_ad_log.ad1_id',
            'ad2_id' => 'third_ad_log.ad2_id as ad2_id',
            'ad2_name' => 'third_ad_log.ad2_name, third_ad_log.ad2_id as ad2_id',
            'ad3_id' => 'third_ad_log.ad3_id',
            'ad3_name' => 'third_ad_log.ad3_name, third_ad_log.ad3_id',
            'game_id' => 'third_ad_log.game_id as game_id',
            'game_name' => 'third_ad_log.game_name as game_name',
            'main_game_id' => 'third_ad_log.main_game_id as main_game_id',
            'main_game_name' => 'third_ad_log.main_game_name as main_game_name',
            'root_game_id' => 'third_ad_log.root_game_id as root_game_id',
            'root_game_name' => 'third_ad_log.root_game_name as root_game_name',
            'site_id' => 'third_ad_log.site_id as site_id',
            'agent_leader' => 'third_ad_log.agent_leader as agent_leader',
            'agent_leader_group_name' => 'third_ad_log.agent_leader_group_name as agent_leader_group_name',
            'agent_id' => 'third_ad_log.agent_name as agent_name, third_ad_log.agent_id as agent_id',
            'agent_group_id' => 'third_ad_log.agent_group_name as agent_group_name, third_ad_log.agent_group_id as agent_group_id',
            'playable_url' => 'third_ad_log.playable_url as playable_url',
            'standard_reached_cost' => [
                0 => 'standard_reached_cost',
                1 => 'SUM(standard_reached_cost) as standard_reached_cost'
            ],
            'standard_unreached_cost' => [
                0 => 'standard_unreached_cost',
                1 => 'SUM(standard_unreached_cost) as standard_unreached_cost'
            ],
            'cost_first_date' => 'min(data_cost_first_log.cost_first_date) as cost_first_date',
            'cost_first_30_day' => [
                0 => 'data_cost_first_log.cost_first_30_day',
                1 => 'SUM(data_cost_first_log.cost_first_30_day) as cost_first_30_day'
            ],
            'cost_first_30_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_30_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_30_day_this_month) as cost_first_30_day_this_month'
            ],

            'cost_first_60_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_60_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_60_day_this_month) as cost_first_60_day_this_month'
            ],
            'cost' => [
                0 => 'data_log.cost',
                1 => 'SUM(data_log.cost) as cost'
            ], // 返点后消耗
            'reg_uid_count' => [
                0 => 'overview_log.reg_uid_count',
                1 => 'SUM(overview_log.reg_uid_count) as reg_uid_count'
            ], // 注册数
            'action_uid_reg_rate' => [
                0 => ['overview_log.reg_uid_count',
                      'overview_log.action_muid_count'],
                1 => ['SUM(overview_log.reg_uid_count) as reg_uid_count',
                      'SUM(overview_log.action_muid_count) as action_muid_count']
            ], // 激活注册率
            //实名数
            'true_uid_count' => [
                0 => 'overview_log.true_uid_count',
                1 => 'SUM(overview_log.true_uid_count) as true_uid_count'
            ],
            //实名率
            'true_uid_rate' => [
                0 => [
                    'overview_log.true_uid_count',
                    'overview_log.reg_uid_count',
                    ],
                1 => [
                    'SUM(overview_log.true_uid_count) as true_uid_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count',
                    ]
            ],
            'cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.cost / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                      'data_log.cost',
                      'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.cost) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                      'SUM(data_log.cost) as cost',
                      'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 注册成本
            'reg_muid_count' => [
                0 => 'overview_log.reg_muid_count',
                1 => 'SUM(overview_log.reg_muid_count) as reg_muid_count'
            ], // 注册设备数
            'action_muid_count' => [
                0 => 'overview_log.action_muid_count',
                1 => 'SUM(overview_log.action_muid_count) as action_muid_count'
            ], //激活设备数
            'first_day_ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                      'overview_log.first_day_pay_money',
                      'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                      'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                      'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日ltv
            'ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'overview_log.total_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 累计ltv
            'first_day_pay_count' => [
                0 => 'overview_log.first_day_pay_count',
                1 => 'SUM(overview_log.first_day_pay_count) as first_day_pay_count'
            ], // 首日付费人数
            'first_day_pay_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_count / overview_log.reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                      'overview_log.first_day_pay_count',
                      'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_count) / SUM(overview_log.reg_uid_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                      'SUM(overview_log.first_day_pay_count) as first_day_pay_count',
                      'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日付费率
            'cost_per_first_day_pay' => [
                0 => ['data_log.cost', 'overview_log.first_day_pay_count'],
                1 => ['SUM(data_log.cost) as cost', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日付费成本
            'first_day_pay_money' => [
                0 => 'overview_log.first_day_pay_money',
                1 => 'SUM(overview_log.first_day_pay_money) as first_day_pay_money'
            ], // 首日付费金额
            'seventh_day_pay_money' => [
                0 => 'overview_log.seventh_day_pay_money',
                1 => 'SUM(overview_log.seventh_day_pay_money) as seventh_day_pay_money'
            ], // 首日付费金额
            'fifteenth_day_pay_money' => [
                0 => 'overview_log.fifteenth_day_pay_money',
                1 => 'SUM(overview_log.fifteenth_day_pay_money) as fifteenth_day_pay_money'
            ], // 首日付费金额
            'thirty_day_pay_money' => [
                0 => 'overview_log.thirty_day_pay_money',
                1 => 'SUM(overview_log.thirty_day_pay_money) as thirty_day_pay_money'
            ], // 首日付费金额

            'first_day_arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                      'overview_log.first_day_pay_money',
                      'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                      'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                      'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日arppu
            'arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.total_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'overview_log.total_pay_money',
                    'overview_log.total_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.total_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.total_pay_count) as total_pay_count']
            ], // 累计arppu
            'first_day_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                      'overview_log.first_day_pay_money',
                      'data_log.cost'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                      'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                      'SUM(data_log.cost) as cost']
            ], // 首日roi
            'total_roi' => [
                0 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost']
            ], // 累计roi
            'rate_day_roi_2' => [
                0 => ['overview_log.sum_second_day_pay_money', 'data_log.cost'],
                1 => ['SUM(overview_log.sum_second_day_pay_money) as sum_second_day_pay_money', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'first_day_roi_standard_value' => [
                0 => ['data_log.1_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.1_day_total_standard_value) as 1_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_2_standard_value' => [
                0 => ['data_log.2_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.2_day_total_standard_value) as 2_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3_standard_value' => [
                0 => ['data_log.3_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.3_day_total_standard_value) as 3_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7_standard_value' => [
                0 => ['data_log.7_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.7_day_total_standard_value) as 7_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15_standard_value' => [
                0 => ['data_log.15_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.15_day_total_standard_value) as 15_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30_standard_value' => [
                0 => ['data_log.30_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],


            'rate_login_stay_2' => [
                0 => ['overview_log.sum_day_pay_uid_second_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_second_day_login_count) as sum_day_pay_uid_second_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_3' => [
                0 => ['overview_log.sum_day_pay_uid_third_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_third_day_login_count) as sum_day_pay_uid_third_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_7' => [
                0 => ['overview_log.sum_day_pay_uid_seventh_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_seventh_day_login_count) as sum_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_15' => [
                0 => ['overview_log.sum_day_pay_uid_fifteenth_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_fifteenth_day_login_count) as sum_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_30' => [
                0 => ['overview_log.sum_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_thirty_day_login_count) as sum_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_2' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_30' => [
                0 => ['overview_log.sum_pay_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_pay_day_pay_uid_thirty_day_login_count) as sum_pay_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_first_pay_login_stay_2' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_second_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_second_day_login_count) as sum_day_first_day_pay_uid_second_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_3' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_third_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_third_day_login_count) as sum_day_first_day_pay_uid_third_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_7' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_seventh_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_seventh_day_login_count) as sum_day_first_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_15' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count) as sum_day_first_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_30' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_thirty_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_thirty_day_login_count) as sum_day_first_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            'rate_day_stay_2' => [
                0 => ['overview_log.sum_day_second_login_count', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_day_second_login_count) as sum_day_second_login_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_3' => [
                0 => ['overview_log.sum_is_third_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_third_login) as sum_is_third_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_7' => [
                0 => ['overview_log.sum_is_seventh_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_seventh_login) as sum_is_seventh_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_15' => [
                0 => ['overview_log.sum_is_fifteenth_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_fifteenth_login) as sum_is_fifteenth_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'first_day_pay_times' => [
                0 => 'overview_log.first_day_pay_times',
                1 => 'SUM(overview_log.first_day_pay_times) as first_day_pay_times'
            ],
            'total_pay_times' => [
                0 => 'overview_log.total_pay_times',
                1 => 'SUM(overview_log.total_pay_times) as total_pay_times'
            ],
            'first_day_pay_times_cost' => [
                0 => ['overview_log.first_day_pay_times', 'data_log.cost'],
                1 => ['SUM(overview_log.first_day_pay_times) as first_day_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_times_cost' => [
                0 => ['overview_log.total_pay_times', 'data_log.cost'],
                1 => ['SUM(overview_log.total_pay_times) as total_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'click_count' =>  [
                0 => 'overview_log.click_count',
                1 => 'SUM(overview_log.click_count) as click_count'
            ],
            'rate_day_stay_2_cost' => [
                0 => ['overview_log.sum_day_second_login_count', 'data_log.cost'],
                1 => ['SUM( overview_log.sum_day_second_login_count ) as sum_day_second_login_count', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_count' => [
                0 => 'overview_log.total_pay_count',
                1 => 'SUM(overview_log.total_pay_count) as total_pay_count'
            ],
            'total_pay_money' => [
                0 => 'overview_log.total_pay_money',
                1 => 'SUM(overview_log.total_pay_money) as total_pay_money'
            ],
            //关键等级数
            'key_role_level_count' => [
                0 => 'overview_log.key_role_level_count',
                1 => 'SUM(overview_log.key_role_level_count) as key_role_level_count'
            ],
            //关键等级占比
            'key_role_level_count_rate' => [
                0 => [
                    'overview_log.key_role_level_count',
                    'overview_log.reg_uid_count'
                ],
                1 => [
                    'SUM(overview_log.key_role_level_count) as key_role_level_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count'
                ],
            ],
            //关键等级成本
            'cost_key_role_level_count' => [
                0 => [
                    'data_log.cost',
                    'overview_log.key_role_level_count'
                ],
                1 => [
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.key_role_level_count) as key_role_level_count'
                ],
            ],
            'male_count' => [
                'overview_log.male_count as male_count',
                'SUM(overview_log.male_count) as male_count'
            ],
            'female_count' => [
                'overview_log.female_count as female_count',
                'SUM(overview_log.female_count) as female_count'
            ],
            'age_18_24_count' => [
                'overview_log.age_18_24_count as age_18_24_count',
                'SUM(overview_log.age_18_24_count) as age_18_24_count'
            ],
            'age_25_30_count' => [
                'overview_log.age_25_30_count as age_25_30_count',
                'SUM(overview_log.age_25_30_count) as age_25_30_count'
            ],
            'age_31_40_count' => [
                'overview_log.age_31_40_count as age_31_40_count',
                'SUM(overview_log.age_31_40_count) as age_31_40_count'
            ],
            'age_41_50_count' => [
                'overview_log.age_41_50_count as age_41_50_count',
                'SUM(overview_log.age_41_50_count) as age_41_50_count'
            ],
            'age_over_50_count' => [
                'overview_log.age_over_50_count as age_over_50_count',
                'SUM(overview_log.age_over_50_count) as age_over_50_count'
            ],
            'reg_old_muid_count' => [
                0 => 'overview_log.reg_old_muid_count',
                1 => 'SUM(overview_log.reg_old_muid_count) as reg_old_muid_count'
            ],
            'reg_old_clique_muid_count' => [
                0 => 'overview_log.reg_old_clique_muid_count',
                1 => 'SUM(overview_log.reg_old_clique_muid_count) as reg_old_clique_muid_count'
            ],
            'three_login_uid_count' => [
                0 => 'overview_log.three_login_uid_count',
                1 => 'SUM(overview_log.three_login_uid_count) as three_login_uid_count'
            ],
            'reg_old_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_muid_count ) as reg_old_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'reg_old_clique_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_clique_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_clique_muid_count ) as reg_old_clique_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'three_login_uid_count_per_reg' => [
                0 => ['overview_log.three_login_uid_count', 'overview_log.reg_uid_count'],
                1 => ['SUM( overview_log.three_login_uid_count ) as three_login_uid_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count'],
            ],
            'new_pay_day_money' => [
                0 => 'overview_log.new_pay_day_money',
                1 => 'SUM(overview_log.new_pay_day_money) as new_pay_day_money'
            ],
            'new_pay_day_count' => [
                0 => 'overview_log.new_pay_day_count',
                1 => 'SUM(overview_log.new_pay_day_count) as new_pay_day_count'
            ],
            //媒体通用数据
            'cost_process' => [
                0 => ['IFNULL ( CAST ( ( data_log.sum_ori_cost_for_cost_process / third_ad_log.sum_budget_for_cost_process ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                      'data_log.sum_ori_cost_for_cost_process',
                      'third_ad_log.sum_budget_for_cost_process'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.sum_ori_cost_for_cost_process) / SUM(third_ad_log.sum_budget_for_cost_process) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                      'SUM(data_log.sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
                      'SUM(third_ad_log.sum_budget_for_cost_process) as sum_budget_for_cost_process']
            ], //当天账户币消耗/预算
            'cpc' => [
                0 => ['data_log.ori_cost', 'data_log.click'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click']
            ], // cpc COST / CLICK
            'click' => [
                0 => 'data_log.click',
                1 => 'SUM(data_log.click) as click'
            ], //click
            'cpm' => [
                0 => ['data_log.ori_cost', '`data_log`.`show`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(`data_log`.`show`) as `show`']
            ], // cpm COST/(SHOW/1000)
            'show' => [
                0 => '`data_log`.`show`',
                1 => 'SUM(`data_log`.`show`) as `show`'
            ], // show
            'ori_cost' => [
                0 => 'data_log.ori_cost',
                1 => 'SUM(data_log.ori_cost) as ori_cost'
            ], // 总花费
            'click_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.click / `data_log`.`show` ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                      'data_log.click',
                      '`data_log`.`show`'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.click) / SUM(`data_log`.`show`) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                      'SUM(data_log.click) as click',
                      'SUM(`data_log`.`show`) as `show`']
            ], // 点击率
            'cost_per_convert' => [
                0 => ['data_log.ori_cost', 'data_log.`convert`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`']
            ], // 转化成本
            'convert' => [
                0 => 'data_log.`convert`',
                1 => 'SUM(data_log.`convert`) as `convert`'
            ], // 转化数
            'cost_per_active' => [
                0 => ['data_log.ori_cost', 'data_log.active_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.active_count) as active_count']
            ], // 激活成本
            'cost_per_pay' => [
                0 => ['data_log.ori_cost', 'data_log.pay_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count']
            ], // 付费成本
            'pay_count' => [
                0 => 'data_log.pay_count',
                1 => 'SUM(data_log.pay_count) as pay_count'
            ], // 付费数
            'reg_rate' => [
                0 => ['data_log.reg_count', 'data_log.click'],
                1 => ['SUM(data_log.reg_count) as reg_count', 'SUM(data_log.click) as click']
            ], // 注册率
            'active_rate' => [
                0 => ['data_log.active_count', 'data_log.click'],
                1 => ['SUM(data_log.active_count) as active_count', 'SUM(data_log.click) as click']
            ], // 激活率
            'active_count' => [
                0 => 'data_log.active_count as active_count',
                1 => 'SUM(data_log.active_count) as active_count'
            ], // 激活数
            'convert_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.`convert` / data_log.click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                      'data_log.`convert`',
                      'data_log.click'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.`convert`) / SUM(data_log.click) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                      'SUM(data_log.`convert`) as `convert`',
                      'SUM(data_log.click) as click']
            ], // 转化率
            'reg_count' => [
                0 => 'data_log.reg_count',
                1 => 'SUM(data_log.reg_count) as reg_count'
            ], // 注册数
            'media_cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.ori_cost / data_log.reg_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                      'data_log.reg_count',
                      'data_log.ori_cost'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.ori_cost) / SUM(data_log.reg_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                      'SUM(data_log.reg_count) as reg_count',
                      'SUM(data_log.ori_cost) as ori_cost']
            ],//注册成本(媒体)
            'pay_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.pay_count / data_log.active_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                      'data_log.pay_count',
                      'data_log.active_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.pay_count) / SUM(data_log.active_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                      'SUM(data_log.pay_count) as pay_count',
                      'SUM(data_log.active_count) as active_count']
            ], // 付费率
            //三日付费人数
            'day_third_day_pay_count' => [
                0 => 'overview_log.day_third_day_pay_count',
                1 => 'SUM(overview_log.day_third_day_pay_count) as day_third_day_pay_count'
            ],
            //七日付费人数
            'day_seventh_day_pay_count' => [
                0 => 'overview_log.day_seventh_day_pay_count',
                1 => 'SUM(overview_log.day_seventh_day_pay_count) as day_seventh_day_pay_count'
            ],
            //首充1-6元人数
            'first_day_pay_money_within_6_count' => [
                0 => 'overview_log.first_day_pay_money_within_6_count',
                1 => 'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count'
            ],
            //首充1-6元首日占比
            'first_day_pay_money_within_6_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money_within_6_count / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'overview_log.first_day_pay_money_within_6_count',
                    'overview_log.first_day_pay_count'],
                1 => [
                    'IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money_within_6_count) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            //预估终生付费
            'lifetime_money' => [
                0 => 'overview_log.lifetime_money',
                1 => 'SUM(overview_log.lifetime_money) as lifetime_money'
            ],
            //预估终生回本
            'lifetime_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.lifetime_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'overview_log.lifetime_money',
                    'data_log.cost'],
                1 => [
                    'IFNULL ( CAST ( ( SUM(overview_log.lifetime_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'SUM(overview_log.lifetime_money) as lifetime_money',
                    'SUM(data_log.cost) as cost']
            ],
            'item_id' => 'third_ad_log.item_id as item_id',
            'aweme_title' => "third_ad_log.item_id as item_id, if(third_ad_log.item_id!= 0,ifnull( aweme_log.title, third_ad_log.title ), '') AS aweme_title",
            'aweme_account' => 'third_ad_log.item_id as item_id, aweme_log.aweme_account',
            'aweme_name' => 'third_ad_log.item_id as item_id, aweme_log.aweme_name as aweme_name',
            'interface_person' => 'third_ad_log.item_id as item_id, aweme_log.interface_person as interface_person',
            'interface_person_group_name' => 'third_ad_log.item_id as item_id, aweme_log.interface_person_group_name',
            'intermediary_person' => 'third_ad_log.item_id as item_id, aweme_log.intermediary_person',
            'aweme_item_publish_time' => 'third_ad_log.item_id as item_id, aweme_log.aweme_item_publish_time as aweme_item_publish_time',
            'talent_account' => 'third_ad_log.talent_account as talent_account',
            'talent_name' => 'talent_log.aweme_name as talent_name',
            'talent_interface_person' => 'talent_log.interface_person as talent_interface_person',
            'gdt_inventory_subdivision' => 'third_ad_log.gdt_inventory_subdivision as gdt_inventory_subdivision',
            'gdt_xqxs_inventory_subdivision' => 'third_ad_log.gdt_xqxs_inventory_subdivision as gdt_xqxs_inventory_subdivision',
        ],

        MediaType::TOUTIAO => [
            'port_version' => 'third_ad_log.port_version as port_version',
            'media_type' => MediaType::TOUTIAO.' as media_type',
            'port_version' => 'third_ad_log.port_version as port_version',
            'inventory_type' => 'third_ad_log.inventory_type as inventory_type',
            //头条素材ID
            'tt_material_id' => 'third_ad_log.tt_material_id as tt_material_id',
            'tt_materials_type' => 'third_ad_log.tt_materials_type as tt_materials_type',
            //头条
            'tt_attribution_next_day_open_cnt' => 'SUM(data_log.tt_attribution_next_day_open_cnt) as tt_attribution_next_day_open_cnt',
            'tt_loan_completion' => 'SUM(data_log.tt_loan_completion) as tt_loan_completion',
            'tt_loan_completion_cost' => 'SUM(data_log.tt_loan_completion) as tt_loan_completion, SUM(data_log.ori_cost) as ori_cost',
            'tt_loan_completion_rate' => 'SUM(data_log.tt_loan_completion) as tt_loan_completion, SUM(data_log.reg_count) as reg_count',
            'tt_download_finish' => 'SUM(data_log.tt_download_finish) as tt_download_finish',
            'tt_download_finish_cost' => 'SUM(data_log.tt_download_finish) as tt_download_finish, SUM(data_log.ori_cost) as ori_cost',
            'tt_download_finish_rate' => 'SUM(data_log.tt_download_finish) as tt_download_finish, SUM(data_log.tt_download_start) as tt_download_start',
            'tt_install_finish' => 'SUM(data_log.tt_install_finish) as tt_install_finish',
            'tt_install_finish_rate' => 'SUM(data_log.tt_install_finish) as tt_install_finish, SUM(data_log.tt_download_start) as tt_download_start',
            'tt_install_finish_cost' => 'SUM(data_log.tt_install_finish) as tt_install_finish, SUM(data_log.ori_cost) as ori_cost',
            'tt_download_start' => 'SUM(data_log.tt_download_start) as tt_download_start',
            'tt_download_start_rate' => 'SUM(data_log.tt_download_start) as tt_download_start, SUM(data_log.click) as click',
            'tt_download_start_cost' => 'SUM(data_log.tt_download_start) as tt_download_start, SUM(data_log.ori_cost) as ori_cost',
            'tt_click_install' => 'SUM(data_log.tt_click_install) as tt_click_install',
            'tt_deep_convert_rate' => 'SUM(data_log.tt_deep_convert) as tt_deep_convert, SUM(data_log.tt_convert) as tt_convert',
            'tt_deep_convert' => 'SUM(data_log.tt_deep_convert) as tt_deep_convert',
            'tt_deep_convert_cost' => 'SUM(data_log.tt_deep_convert) as tt_deep_convert, SUM(data_log.ori_cost) as ori_cost',
            'tt_next_day_open_rate' => 'SUM(data_log.tt_next_day_open) as tt_next_day_open, SUM(data_log.active_count) as active_count',
            'tt_next_day_open_cost' => 'SUM(data_log.tt_next_day_open) as tt_next_day_open, SUM(data_log.ori_cost) as ori_cost',
            'tt_next_day_open' => 'SUM(data_log.tt_next_day_open) as tt_next_day_open',
            'tt_game_addiction' => 'SUM(data_log.tt_game_addiction) as tt_game_addiction',
            'tt_game_addiction_rate' => 'SUM(data_log.tt_game_addiction) as tt_game_addiction, SUM(data_log.active_count) as active_count',
            'tt_game_addiction_cost' => 'SUM(data_log.tt_game_addiction) as tt_game_addiction, SUM(data_log.ori_cost) as ori_cost',
            'tt_play_25_feed_break' => 'SUM(data_log.tt_play_25_feed_break) as tt_play_25_feed_break',
            'tt_play_50_feed_break' => 'SUM(data_log.tt_play_50_feed_break) as tt_play_50_feed_break',
            'tt_play_75_feed_break' => 'SUM(data_log.tt_play_75_feed_break) as tt_play_75_feed_break',
            'tt_play_100_feed_break' => 'SUM(data_log.tt_play_100_feed_break) as tt_play_100_feed_break',
            'tt_average_play_time_per_play' => 'SUM(data_log.sum_average_play_time_per_play) as sum_average_play_time_per_play, SUM(data_log.tt_total_play) as tt_total_play',
            'tt_play_over' => 'SUM(data_log.tt_play_over) as tt_play_over',
            'tt_play_duration_sum' => 'SUM(data_log.tt_play_duration_sum) as tt_play_duration_sum',
            'tt_wifi_play' => 'SUM(data_log.tt_wifi_play) as tt_wifi_play',
            //3s播放数
            'tt_play_duration_3s' => 'SUM(data_log.tt_play_duration_3s) as tt_play_duration_3s',
            //3s播放率
            'tt_play_duration_3s_rate' => 'SUM(data_log.tt_play_duration_3s) as tt_play_duration_3s, SUM(data_log.tt_total_play) as tt_total_play',
            'tt_wifi_play_rate' => 'SUM(data_log.tt_wifi_play_num) as tt_wifi_play_num, SUM(data_log.tt_total_play) as tt_total_play',
            'tt_valid_play' => 'SUM(data_log.tt_valid_play) as tt_valid_play',
            'tt_valid_play_cost' => 'SUM(data_log.tt_valid_play) as tt_valid_play, SUM(data_log.ori_cost) as ori_cost',
            'tt_valid_play_rate' => 'SUM(data_log.tt_valid_play) as tt_valid_play, SUM(data_log.tt_total_play) as tt_total_play',
            'tt_play_over_rate' => 'SUM(data_log.tt_play_100_feed_break) as tt_play_100_feed_break, SUM(data_log.tt_total_play) as tt_total_play',
            'tt_total_play' => 'SUM(data_log.tt_total_play) as tt_total_play',
            'tt_location_click' => 'SUM(data_log.tt_location_click) as tt_location_click',
            'tt_comment' => 'SUM(data_log.tt_comment) as tt_comment',
            'tt_share' => 'SUM(data_log.tt_share) as tt_share',
            'tt_follow' => 'SUM(data_log.tt_follow) as tt_follow',
            'tt_home_visited' => 'SUM(data_log.tt_home_visited) as tt_home_visited',
            'tt_like' => 'SUM(data_log.tt_like) as tt_like',
            'tt_dislike_cnt' => 'SUM(data_log.`tt_dislike_cnt`) as tt_dislike_cnt',
            'tt_report_cnt' => 'SUM(data_log.`tt_report_cnt`) as tt_report_cnt',
            'tt_ies_music_click' => 'SUM(data_log.tt_ies_music_click) as tt_ies_music_click',
            'tt_ies_challenge_click' => 'SUM(data_log.tt_ies_challenge_click) as tt_ies_challenge_click',
            //'tt_quality_score' => '',
            //'tt_ctr_score' => '',
            //'tt_web_score' => '',
            //'tt_cvr_score' => '',

            //素材属性数据
            'working_hours' => 'material_log.working_hours as working_hours',
            'material_platform' => 'material_log.material_platform as material_platform',
            'material_name' => 'material_log.name as material_name',
            'material_id' => 'material_log.material_id as material_id',
            'material_filename' => 'material_log.filename as material_filename',
            'material_file_id' => 'material_log.id as material_file_id',
            'signature' => 'third_ad_log.signature as signature',
            'author' => 'material_log.author',
            'theme_id' => 'material_log.theme_id',
            'theme_pid' => 'material_log.theme_pid',
            'file_type' => 'third_ad_log.file_type',
            'create_time' => 'material_log.insert_time as create_time',
            'urls' => 'third_ad_log.signature as signature, third_ad_log.file_type as file_type, material_log.zx_play_url as zx_play_url',
            'c_author' => 'material_log.c_author',
            'a_author' => 'material_log.a_author',
            'm1_author' => 'material_log.m1_author',
            'm2_author' => 'material_log.m2_author',
            'm3_author' => 'material_log.m3_author',
            'm4_author' => 'material_log.m4_author',
            'm5_author' => 'material_log.m5_author',
            'actor' => 'material_log.actor',
            'shoot' => 'material_log.shoot',
            'material_create_date' => 'material_log.insert_time as material_create_date ',
            'original' => 'material_log.original as original',
            'size' => "material_log.size as size",
            'effect_grade7' => "material_log.effect_grade7 as effect_grade7",
            'effect_grade30' => "material_log.effect_grade30 as effect_grade30",
            'is_priority' => "material_log.is_priority as is_priority",
            'is_3d' => "material_log.is_3d as is_3d",
            'is_immortal' => "material_log.is_immortal as is_immortal",
            'label' => "material_log.label_id as label",
            //计数项数据
            'count' => "COUNT( DISTINCT third_ad_log.web_creator ) as count",
            'count_ad2' => "COUNT( DISTINCT third_ad_log.count_ad2 ) as count_ad2",
            'count_ad2_deliveried' => "COUNT( DISTINCT data_log.data_log_ad_id, IF ( data_log.cost > 0, TRUE, NULL ) ) AS count_ad2_deliveried",
            'count_ad2_delivering' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.status = 'AD_STATUS_DELIVERY_OK', TRUE, NULL ) ) AS count_ad2_delivering",
            'count_ad2_undeliveried' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.is_cost = 0, TRUE, NULL ) ) AS count_ad2_undeliveried",
            //'count_cost_date' => [
            //    0 => 'data_log.count_cost_date',
            //    1 => 'SUM(data_log.count_cost_date) as count_cost_date'
            //],
            'count_creative' => "SUM(third_ad_log.count_creative) as count_creative",
            //业务数据
            'platform' => 'third_ad_log.platform as platform',
            'os' => "third_ad_log.os as os",
            'create_type' => 'third_ad_log.create_type as create_type',
            'account_id' => 'third_ad_log.account_id',
            'account_name' => 'third_ad_log.account_name, third_ad_log.account_id',
            'ad1_id' => 'third_ad_log.ad1_id',
            'ad1_name' => 'third_ad_log.ad1_name, third_ad_log.ad1_id',
            'ad2_id' => 'third_ad_log.ad2_id as ad2_id',
            'ad2_name' => 'third_ad_log.ad2_name, third_ad_log.ad2_id as ad2_id',
            'ad3_id' => 'third_ad_log.ad3_id',
            'ad3_name' => 'third_ad_log.ad3_name, third_ad_log.ad3_id',
            'game_id' => 'third_ad_log.game_id as game_id',
            'game_name' => 'third_ad_log.game_name as game_name',
            'main_game_id' => 'third_ad_log.main_game_id as main_game_id',
            'main_game_name' => 'third_ad_log.main_game_name as main_game_name',
            'root_game_id' => 'third_ad_log.root_game_id as root_game_id',
            'root_game_name' => 'third_ad_log.root_game_name as root_game_name',
            'site_id' => 'third_ad_log.site_id as site_id',
            'agent_leader' => 'third_ad_log.agent_leader as agent_leader',
            'agent_leader_group_name' => 'third_ad_log.agent_leader_group_name as agent_leader_group_name',
            'agent_id' => 'third_ad_log.agent_name as agent_name, third_ad_log.agent_id as agent_id',
            'agent_group_id' => 'third_ad_log.agent_group_name as agent_group_name, third_ad_log.agent_group_id as agent_group_id',
            'standard_reached_cost' => [
                0 => 'standard_reached_cost',
                1 => 'SUM(standard_reached_cost) as standard_reached_cost'
            ],
            'standard_unreached_cost' => [
                0 => 'standard_unreached_cost',
                1 => 'SUM(standard_unreached_cost) as standard_unreached_cost'
            ],
            'cost_first_date' => 'min(data_cost_first_log.cost_first_date) as cost_first_date',
            'cost_first_30_day' => [
                0 => 'data_cost_first_log.cost_first_30_day',
                1 => 'SUM(data_cost_first_log.cost_first_30_day) as cost_first_30_day'
            ],
            'cost_first_30_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_30_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_30_day_this_month) as cost_first_30_day_this_month'
            ],
            'cost_first_60_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_60_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_60_day_this_month) as cost_first_60_day_this_month'
            ],
            'cost' => [
                0 => 'data_log.cost',
                1 => 'SUM(data_log.cost) as cost'
            ], // 返点后消耗
            'reg_uid_count' => [
                0 => 'overview_log.reg_uid_count',
                1 => 'SUM(overview_log.reg_uid_count) as reg_uid_count'
            ], // 注册数
            'action_uid_reg_rate' => [
                0 => ['overview_log.reg_uid_count',
                    'overview_log.action_muid_count'],
                1 => ['SUM(overview_log.reg_uid_count) as reg_uid_count',
                    'SUM(overview_log.action_muid_count) as action_muid_count']
            ], // 激活注册率
            //实名数
            'true_uid_count' => [
                0 => 'overview_log.true_uid_count',
                1 => 'SUM(overview_log.true_uid_count) as true_uid_count'
            ],
            //实名率
            'true_uid_rate' => [
                0 => [
                    'overview_log.true_uid_count',
                    'overview_log.reg_uid_count',
                ],
                1 => [
                    'SUM(overview_log.true_uid_count) as true_uid_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count',
                ]
            ],
            'cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.cost / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                    'data_log.cost',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.cost) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 注册成本
            'reg_muid_count' => [
                0 => 'overview_log.reg_muid_count',
                1 => 'SUM(overview_log.reg_muid_count) as reg_muid_count'
            ], // 注册设备数
            'action_muid_count' => [
                0 => 'overview_log.action_muid_count',
                1 => 'SUM(overview_log.action_muid_count) as action_muid_count'
            ], //激活设备数
            'first_day_ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                    'overview_log.first_day_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日ltv
            'ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'overview_log.total_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日ltv
            'first_day_pay_count' => [
                0 => 'overview_log.first_day_pay_count',
                1 => 'SUM(overview_log.first_day_pay_count) as first_day_pay_count'
            ], // 首日付费人数
            'first_day_pay_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_count / overview_log.reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                    'overview_log.first_day_pay_count',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_count) / SUM(overview_log.reg_uid_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日付费率
            'cost_per_first_day_pay' => [
                0 => ['data_log.cost', 'overview_log.first_day_pay_count'],
                1 => ['SUM(data_log.cost) as cost', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日付费成本
            'first_day_pay_money' => [
                0 => 'overview_log.first_day_pay_money',
                1 => 'SUM(overview_log.first_day_pay_money) as first_day_pay_money'
            ], // 首日付费金额
            'seventh_day_pay_money' => [
                0 => 'overview_log.seventh_day_pay_money',
                1 => 'SUM(overview_log.seventh_day_pay_money) as seventh_day_pay_money'
            ], // 首日付费金额
            'fifteenth_day_pay_money' => [
                0 => 'overview_log.fifteenth_day_pay_money',
                1 => 'SUM(overview_log.fifteenth_day_pay_money) as fifteenth_day_pay_money'
            ], // 首日付费金额
            'thirty_day_pay_money' => [
                0 => 'overview_log.thirty_day_pay_money',
                1 => 'SUM(overview_log.thirty_day_pay_money) as thirty_day_pay_money'
            ], // 首日付费金额
            'first_day_arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                    'overview_log.first_day_pay_money',
                    'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日arppu
            'arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.total_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'overview_log.total_pay_money',
                    'overview_log.total_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.total_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.total_pay_count) as total_pay_count']
            ], // 累计arppu
            'first_day_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                    'overview_log.first_day_pay_money',
                    'data_log.cost'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(data_log.cost) as cost']
            ], // 首日roi
            'total_roi' => [
                0 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost']
            ], // 累计roi
            'rate_day_roi_2' => [
                0 => ['overview_log.sum_second_day_pay_money', 'data_log.cost'],
                1 => ['SUM(overview_log.sum_second_day_pay_money) as sum_second_day_pay_money', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'first_day_roi_standard_value' => [
                0 => ['data_log.1_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.1_day_total_standard_value) as 1_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_2_standard_value' => [
                0 => ['data_log.2_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.2_day_total_standard_value) as 2_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3_standard_value' => [
                0 => ['data_log.3_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.3_day_total_standard_value) as 3_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7_standard_value' => [
                0 => ['data_log.7_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.7_day_total_standard_value) as 7_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15_standard_value' => [
                0 => ['data_log.15_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.15_day_total_standard_value) as 15_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30_standard_value' => [
                0 => ['data_log.30_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],


            'rate_login_stay_2' => [
                0 => ['overview_log.sum_day_pay_uid_second_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_second_day_login_count) as sum_day_pay_uid_second_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_3' => [
                0 => ['overview_log.sum_day_pay_uid_third_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_third_day_login_count) as sum_day_pay_uid_third_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_7' => [
                0 => ['overview_log.sum_day_pay_uid_seventh_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_seventh_day_login_count) as sum_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_15' => [
                0 => ['overview_log.sum_day_pay_uid_fifteenth_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_fifteenth_day_login_count) as sum_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_30' => [
                0 => ['overview_log.sum_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_thirty_day_login_count) as sum_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],

            'rate_pay_login_stay_2' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_30' => [
                0 => ['overview_log.sum_pay_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_pay_day_pay_uid_thirty_day_login_count) as sum_pay_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_first_pay_login_stay_2' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_second_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_second_day_login_count) as sum_day_first_day_pay_uid_second_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_3' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_third_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_third_day_login_count) as sum_day_first_day_pay_uid_third_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_7' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_seventh_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_seventh_day_login_count) as sum_day_first_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_15' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count) as sum_day_first_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_30' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_thirty_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_thirty_day_login_count) as sum_day_first_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            'rate_day_stay_2' => [
                0 => ['overview_log.sum_day_second_login_count', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_day_second_login_count) as sum_day_second_login_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_3' => [
                0 => ['overview_log.sum_is_third_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_third_login) as sum_is_third_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_7' => [
                0 => ['overview_log.sum_is_seventh_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_seventh_login) as sum_is_seventh_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_15' => [
                0 => ['overview_log.sum_is_fifteenth_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_fifteenth_login) as sum_is_fifteenth_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'first_day_pay_times' => [
                0 => 'overview_log.first_day_pay_times',
                1 => 'SUM(overview_log.first_day_pay_times) as first_day_pay_times'
            ],
            'total_pay_times' => [
                0 => 'overview_log.total_pay_times',
                1 => 'SUM(overview_log.total_pay_times) as total_pay_times'
            ],
            'first_day_pay_times_cost' => [
                0 => ['overview_log.first_day_pay_times', 'data_log.cost'],
                1 => ['SUM( overview_log.first_day_pay_times ) as first_day_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_times_cost' => [
                0 => ['overview_log.total_pay_times', 'data_log.cost'],
                1 => ['SUM( overview_log.total_pay_times ) as total_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'click_count' =>  [
                0 => 'overview_log.click_count',
                1 => 'SUM(overview_log.click_count) as click_count'
            ],
            'rate_day_stay_2_cost' => [
                0 => ['overview_log.sum_day_second_login_count', 'data_log.cost'],
                1 => ['SUM( overview_log.sum_day_second_login_count ) as sum_day_second_login_count', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_count' => [
                0 => 'overview_log.total_pay_count',
                1 => 'SUM(overview_log.total_pay_count) as total_pay_count'
            ],
            'total_pay_money' => [
                0 => 'overview_log.total_pay_money',
                1 => 'SUM(overview_log.total_pay_money) as total_pay_money'
            ],
            //关键等级数
            'key_role_level_count' => [
                0 => 'overview_log.key_role_level_count',
                1 => 'SUM(overview_log.key_role_level_count) as key_role_level_count'
            ],
            //关键等级占比
            'key_role_level_count_rate' => [
                0 => [
                    'overview_log.key_role_level_count',
                    'overview_log.reg_uid_count'
                ],
                1 => [
                    'SUM(overview_log.key_role_level_count) as key_role_level_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count'
                ],
            ],
            //关键等级成本
            'cost_key_role_level_count' => [
                0 => [
                    'data_log.cost',
                    'overview_log.key_role_level_count'
                ],
                1 => [
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.key_role_level_count) as key_role_level_count'
                ],
            ],
            'male_count' => [
                'overview_log.male_count',
                'SUM(overview_log.male_count) as male_count'
            ],
            'female_count' => [
                'overview_log.female_count',
                'SUM(overview_log.female_count) as female_count'
            ],
            'age_18_24_count' => [
                'overview_log.age_18_24_count as age_18_24_count',
                'SUM(overview_log.age_18_24_count) as age_18_24_count'
            ],
            'age_25_30_count' => [
                'overview_log.age_25_30_count as age_25_30_count',
                'SUM(overview_log.age_25_30_count) as age_25_30_count'
            ],
            'age_31_40_count' => [
                'overview_log.age_31_40_count as age_31_40_count',
                'SUM(overview_log.age_31_40_count) as age_31_40_count'
            ],
            'age_41_50_count' => [
                'overview_log.age_41_50_count as age_41_50_count',
                'SUM(overview_log.age_41_50_count) as age_41_50_count'
            ],
            'age_over_50_count' => [
                'overview_log.age_over_50_count as age_over_50_count',
                'SUM(overview_log.age_over_50_count) as age_over_50_count'
            ],
            'reg_old_muid_count' => [
                0 => 'overview_log.reg_old_muid_count',
                1 => 'SUM(overview_log.reg_old_muid_count) as reg_old_muid_count'
            ],
            'reg_old_clique_muid_count' => [
                0 => 'overview_log.reg_old_clique_muid_count',
                1 => 'SUM(overview_log.reg_old_clique_muid_count) as reg_old_clique_muid_count'
            ],
            'three_login_uid_count' => [
                0 => 'overview_log.three_login_uid_count',
                1 => 'SUM(overview_log.three_login_uid_count) as three_login_uid_count'
            ],
            'reg_old_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_muid_count ) as reg_old_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'reg_old_clique_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_clique_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_clique_muid_count ) as reg_old_clique_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'three_login_uid_count_per_reg' => [
                0 => ['overview_log.three_login_uid_count', 'overview_log.reg_uid_count'],
                1 => ['SUM( overview_log.three_login_uid_count ) as three_login_uid_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count'],
            ],
            'new_pay_day_money' => [
                0 => 'overview_log.new_pay_day_money',
                1 => 'SUM(overview_log.new_pay_day_money) as new_pay_day_money'
            ],
            'new_pay_day_count' => [
                0 => 'overview_log.new_pay_day_count',
                1 => 'SUM(overview_log.new_pay_day_count) as new_pay_day_count'
            ],
            //媒体通用数据
            'cost_process' => [
                0 => ['IFNULL ( CAST ( ( data_log.sum_ori_cost_for_cost_process / third_ad_log.sum_budget_for_cost_process ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                    'data_log.sum_ori_cost_for_cost_process',
                    'third_ad_log.sum_budget_for_cost_process'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.sum_ori_cost_for_cost_process) / SUM(third_ad_log.sum_budget_for_cost_process) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                    'SUM(data_log.sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
                    'SUM(third_ad_log.sum_budget_for_cost_process) as sum_budget_for_cost_process']
            ], //当天账户币消耗/预算
            'cpc' => [
                0 => ['data_log.ori_cost', 'data_log.click'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click']
            ], // cpc COST / CLICK
            'click' => [
                0 => 'data_log.click',
                1 => 'SUM(data_log.click) as click'
            ], //click
            'cpm' => [
                0 => ['data_log.ori_cost', '`data_log`.`show`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(`data_log`.`show`) as `show`']
            ], // cpm COST/(SHOW/1000)
            'show' => [
                0 => '`data_log`.`show`',
                1 => 'SUM(`data_log`.`show`) as `show`'
            ], // show
            'ori_cost' => [
                0 => 'data_log.ori_cost',
                1 => 'SUM(data_log.ori_cost) as ori_cost'
            ], // 总花费
            'click_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.click / `data_log`.`show` ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                    'data_log.click',
                    '`data_log`.`show`'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.click) / SUM(`data_log`.`show`) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                    'SUM(data_log.click) as click',
                    'SUM(`data_log`.`show`) as `show`']
            ], // 点击率
            'cost_per_convert' => [
                0 => ['data_log.ori_cost', 'data_log.`convert`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`']
            ], // 转化成本
            'convert' => [
                0 => 'data_log.`convert`',
                1 => 'SUM(data_log.`convert`) as `convert`'
            ], // 转化数
            'cost_per_active' => [
                0 => ['data_log.ori_cost', 'data_log.active_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.active_count) as active_count']
            ], // 激活成本
            'cost_per_pay' => [
                0 => ['data_log.ori_cost', 'data_log.pay_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count']
            ], // 付费成本
            'pay_count' => [
                0 => 'data_log.pay_count',
                1 => 'SUM(data_log.pay_count) as pay_count'
            ], // 付费数
            'reg_rate' => [
                0 => ['data_log.reg_count', 'data_log.click'],
                1 => ['SUM(data_log.reg_count) as reg_count', 'SUM(data_log.click) as click']
            ], // 注册率
            'active_rate' => [
                0 => ['data_log.active_count', 'data_log.click'],
                1 => ['SUM(data_log.active_count) as active_count', 'SUM(data_log.click) as click']
            ], // 激活率
            'active_count' => [
                0 => 'data_log.active_count as active_count',
                1 => 'SUM(data_log.active_count) as active_count'
            ], // 激活数
            'convert_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.`convert` / data_log.click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                    'data_log.`convert`',
                    'data_log.click'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.`convert`) / SUM(data_log.click) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                    'SUM(data_log.`convert`) as `convert`',
                    'SUM(data_log.click) as click']
            ], // 转化率
            'reg_count' => [
                0 => 'data_log.reg_count',
                1 => 'SUM(data_log.reg_count) as reg_count'
            ], // 注册数
            'media_cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.ori_cost / data_log.reg_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                    'data_log.reg_count',
                    'data_log.ori_cost'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.ori_cost) / SUM(data_log.reg_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                    'SUM(data_log.reg_count) as reg_count',
                    'SUM(data_log.ori_cost) as ori_cost']
            ],//注册成本(媒体)
            'pay_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.pay_count / data_log.active_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                    'data_log.pay_count',
                    'data_log.active_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.pay_count) / SUM(data_log.active_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                    'SUM(data_log.pay_count) as pay_count',
                    'SUM(data_log.active_count) as active_count']
            ], // 付费率

            //三日付费人数
            'day_third_day_pay_count' => [
                0 => 'overview_log.day_third_day_pay_count',
                1 => 'SUM(overview_log.day_third_day_pay_count) as day_third_day_pay_count'
            ],
            //七日付费人数
            'day_seventh_day_pay_count' => [
                0 => 'overview_log.day_seventh_day_pay_count',
                1 => 'SUM(overview_log.day_seventh_day_pay_count) as day_seventh_day_pay_count'
            ],
            //首充1-6元人数
            'first_day_pay_money_within_6_count' => [
                0 => 'overview_log.first_day_pay_money_within_6_count',
                1 => 'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count'
            ],
            //首充1-6元首日占比
            'first_day_pay_money_within_6_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money_within_6_count / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'overview_log.first_day_pay_money_within_6_count',
                    'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money_within_6_count) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            //预估终生付费
            'lifetime_money' => [
                0 => 'overview_log.lifetime_money',
                1 => 'SUM(overview_log.lifetime_money) as lifetime_money'
            ],
            //预估终生回本
            'lifetime_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.lifetime_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'overview_log.lifetime_money',
                    'data_log.cost'],
                1 => [
                    'IFNULL ( CAST ( ( SUM(overview_log.lifetime_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'SUM(overview_log.lifetime_money) as lifetime_money',
                    'SUM(data_log.cost) as cost']
            ],

            'item_id' => 'third_ad_log.item_id as item_id',
            'aweme_title' => "third_ad_log.item_id as item_id, if(third_ad_log.item_id!= 0,ifnull( aweme_log.title, third_ad_log.title ), '') AS aweme_title",
            'aweme_account' => 'third_ad_log.item_id as item_id, aweme_log.aweme_account',
            'aweme_name' => 'third_ad_log.item_id as item_id, aweme_log.aweme_name as aweme_name',
            'interface_person' => 'third_ad_log.item_id as item_id, aweme_log.interface_person as interface_person',
            'interface_person_group_name' => 'third_ad_log.item_id as item_id, aweme_log.interface_person_group_name',
            'intermediary_person' => 'third_ad_log.item_id as item_id, aweme_log.intermediary_person',
            'aweme_item_publish_time' => 'third_ad_log.item_id as item_id, aweme_log.aweme_item_publish_time as aweme_item_publish_time',
            //重点加热素材
            'tt_three_day_cost_five_standard' => 'third_ad_log.item_id as item_id, aweme_log.tt_three_day_cost_five_standard',
            //重点素材首日达标率是否大于50%
            'tt_first_day_roi_standard' => 'third_ad_log.item_id as item_id, aweme_log.tt_first_day_roi_standard',
            'talent_account' => 'third_ad_log.talent_account as talent_account',
            'talent_name' => 'talent_log.aweme_name as talent_name',
            'talent_interface_person' => 'talent_log.interface_person as talent_interface_person',
            //是否低效素材
            'is_inefficient' => 'third_ad_log.is_inefficient',
            //是否同质化挤压严重素材
            'tt_is_similar_material' => 'third_ad_log.tt_is_similar_material',
            //是否AD优质素材
            'tt_is_ad_high_quality' => 'third_ad_log.tt_is_ad_high_quality',
            //是否首发素材
            'tt_is_first_publish_material' => 'third_ad_log.tt_is_first_publish_material',
            //是否AD低质素材
            'tt_is_ad_low_quality_material' => 'third_ad_log.tt_is_ad_low_quality_material',
            //是否千川低质素材
            'tt_is_ecp_low_quality_material' => 'third_ad_log.tt_is_ecp_low_quality_material',
            //AD素材低质原因
            'tt_message_ad_low_quality_material' => 'third_ad_log.tt_message_ad_low_quality_material',
            //千川素材低质原因
            'tt_message_ecp_low_quality_material' => 'third_ad_log.tt_message_ecp_low_quality_material',
            //素材-拒审原因
            'tt_material_reject_reason' => 'third_ad_log.tt_material_reject_reason',
            //素材-审核建议
            'tt_material_suggestion' => 'third_ad_log.tt_material_suggestion',
        ],

        MediaType::TENCENT => [
            'media_type' => MediaType::TENCENT . ' as media_type',
            'port_version' => 'third_ad_log.port_version as port_version',
            'inventory_type' => 'third_ad_log.inventory_type as inventory_type',
            //10%进度播放量
            'gdt_video_outer_play10_count' => 'SUM(data_log.gdt_video_outer_play10_count) as gdt_video_outer_play10_count',
            //25%进度播放量
            'gdt_video_outer_play25_count' => 'SUM(data_log.gdt_video_outer_play25_count) as gdt_video_outer_play25_count',
            //50%进度播放量
            'gdt_video_outer_play50_count' => 'SUM(data_log.gdt_video_outer_play50_count) as gdt_video_outer_play50_count',
            //75%进度播放量
            'gdt_video_outer_play75_count' => 'SUM(data_log.gdt_video_outer_play75_count) as gdt_video_outer_play75_count',
            //95%进度播放量
            'gdt_video_outer_play95_count' => 'SUM(data_log.gdt_video_outer_play95_count) as gdt_video_outer_play95_count',
            //100%进度播放量
            'gdt_video_outer_play100_count' => 'SUM(data_log.gdt_video_outer_play100_count) as gdt_video_outer_play100_count',
            //平均有效播放时长
            'gdt_video_outer_play_time_count' =>
                'SUM(gdt_video_outer_total_play_time_count) as gdt_video_outer_total_play_time_count, SUM(data_log.gdt_video_outer_play_count) as gdt_video_outer_play_count',
            //平均有效播放进度
            'gdt_video_outer_play_time_avg_rate' =>
                'SUM(gdt_video_outer_total_play_time_count) as gdt_video_outer_total_play_time_count, SUM(data_log.gdt_video_total_play_time_count) as gdt_video_total_play_time_count',
            //有效播放率
            'gdt_video_outer_play_rate' =>
                'SUM(data_log.gdt_video_outer_play_count) as gdt_video_outer_play_count, SUM(data_log.`show`) as `show`',
            //有效播放成本
            'gdt_video_outer_play_cost' =>
                'SUM(data_log.ori_cost) as ori_cost, SUM(data_log.gdt_video_outer_play_count) as gdt_video_outer_play_count',
            //3s播放完成量
            'gdt_video_outer_play3s_count' => 'SUM(data_log.gdt_video_outer_play3s_count) as gdt_video_outer_play3s_count',
            //5s播放完成量
            'gdt_video_outer_play5s_count' => 'SUM(data_log.gdt_video_outer_play5s_count) as gdt_video_outer_play5s_count',
            //7s播放完成量
            'gdt_video_outer_play7s_count' => 'SUM(data_log.gdt_video_outer_play7s_count) as gdt_video_outer_play7s_count',

            //素材属性数据
            'working_hours' => 'material_log.working_hours as working_hours',
            'material_platform' => 'material_log.material_platform as material_platform',
            'material_name' => 'material_log.name as material_name',
            'material_id' => 'material_log.material_id as material_id',
            'material_filename' => 'material_log.filename as material_filename',
            'material_file_id' => 'material_log.id as material_file_id',
            'signature' => 'third_ad_log.signature as signature',
            'author' => 'material_log.author as author',
            'theme_id' => 'material_log.theme_id as theme_id',
            'theme_pid' => 'material_log.theme_pid as theme_pid',
            'file_type' => 'third_ad_log.file_type as file_type',
            'create_time' => 'material_log.insert_time as create_time',
            'urls' => 'third_ad_log.signature as signature, third_ad_log.file_type as file_type, material_log.zx_play_url as zx_play_url',
            'c_author' => 'material_log.c_author as c_author',
            'a_author' => 'material_log.a_author as a_author',
            'm1_author' => 'material_log.m1_author as m1_author',
            'm2_author' => 'material_log.m2_author as m2_author',
            'm3_author' => 'material_log.m3_author as m3_author',
            'm4_author' => 'material_log.m4_author as m4_author',
            'm5_author' => 'material_log.m5_author as m5_author',
            'actor' => 'material_log.actor as actor',
            'shoot' => 'material_log.shoot as shoot',
            'material_create_date' => 'material_log.insert_time as material_create_date',
            'original' => 'material_log.original as original',
            'size' => "material_log.size as size",
            'effect_grade7' => "material_log.effect_grade7 as effect_grade7",
            'effect_grade30' => "material_log.effect_grade30 as effect_grade30",
            'is_priority' => "material_log.is_priority as is_priority",
            'is_3d' => "material_log.is_3d as is_3d",
            'is_immortal' => "material_log.is_immortal as is_immortal",
            'label' => "material_log.label_id as label",

            //计数项数据
            'count' => "COUNT( DISTINCT third_ad_log.web_creator ) as count",
            'count_ad2' => "COUNT( DISTINCT third_ad_log.count_ad2 ) as count_ad2",
            'count_ad2_deliveried' => "COUNT( DISTINCT data_log.data_log_ad2_id, IF ( data_log.cost > 0, TRUE, NULL ) ) AS count_ad2_deliveried",
            'count_ad2_delivering' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.system_status='AD_GROUP_STATUS_NORMAL', TRUE, NULL ) ) AS count_ad2_delivering",
            'count_ad2_undeliveried' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.is_cost = 0, TRUE, NULL ) ) AS count_ad2_undeliveried",
            //'count_cost_date' => [
            //    0 => 'data_log.count_cost_date',
            //    1 => 'SUM(data_log.count_cost_date) as count_cost_date'
            //],
            'count_creative' => "SUM(third_ad_log.count_creative) as count_creative",
            //业务数据
            'platform' => 'third_ad_log.platform as platform',
            'os' => 'third_ad_log.os as os',
            'create_type' => 'third_ad_log.create_type as create_type',
            'account_id' => 'third_ad_log.account_id',
            'account_name' => 'third_ad_log.account_name, third_ad_log.account_id',
            'ad1_id' => 'third_ad_log.ad1_id',
            'ad1_name' => 'third_ad_log.ad1_name, third_ad_log.ad1_id',
            'ad2_id' => 'third_ad_log.ad2_id as ad2_id',
            'ad2_name' => 'third_ad_log.ad2_name, third_ad_log.ad2_id as ad2_id',
            'ad3_id' => 'third_ad_log.ad3_id',
            'ad3_name' => 'third_ad_log.ad3_name, third_ad_log.ad3_id',
            'game_id' => 'third_ad_log.game_id as game_id',
            'game_name' => 'third_ad_log.game_name as game_name',
            'main_game_id' => 'third_ad_log.main_game_id as main_game_id',
            'main_game_name' => 'third_ad_log.main_game_name as main_game_name',
            'root_game_id' => 'third_ad_log.root_game_id as root_game_id',
            'root_game_name' => 'third_ad_log.root_game_name as root_game_name',
            'site_id' => 'third_ad_log.site_id as site_id',
            'agent_leader' => 'third_ad_log.agent_leader as agent_leader',
            'agent_leader_group_name' => 'third_ad_log.agent_leader_group_name as agent_leader_group_name',
            'agent_id' => 'third_ad_log.agent_name as agent_name, third_ad_log.agent_id as agent_id',
            'agent_group_id' => 'third_ad_log.agent_group_name as agent_group_name, third_ad_log.agent_group_id as agent_group_id',
            'standard_reached_cost' => [
                0 => 'standard_reached_cost',
                1 => 'SUM(standard_reached_cost) as standard_reached_cost'
            ],
            'standard_unreached_cost' => [
                0 => 'standard_unreached_cost',
                1 => 'SUM(standard_unreached_cost) as standard_unreached_cost'
            ],
            'cost_first_date' => 'min(data_cost_first_log.cost_first_date) as cost_first_date',
            'cost_first_30_day' => [
                0 => 'data_cost_first_log.cost_first_30_day',
                1 => 'SUM(data_cost_first_log.cost_first_30_day) as cost_first_30_day'
            ],
            'cost_first_30_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_30_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_30_day_this_month) as cost_first_30_day_this_month'
            ],

            'cost_first_60_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_60_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_60_day_this_month) as cost_first_60_day_this_month'
            ],
            'cost' => [
                0 => 'data_log.cost',
                1 => 'SUM(data_log.cost) as cost'
            ], // 返点后消耗
            'reg_uid_count' => [
                0 => 'overview_log.reg_uid_count',
                1 => 'SUM(overview_log.reg_uid_count) as reg_uid_count'
            ], // 注册数
            'action_uid_reg_rate' => [
                0 => ['overview_log.reg_uid_count',
                    'overview_log.action_muid_count'],
                1 => ['SUM(overview_log.reg_uid_count) as reg_uid_count',
                    'SUM(overview_log.action_muid_count) as action_muid_count']
            ], // 激活注册率
            //实名数
            'true_uid_count' => [
                0 => 'overview_log.true_uid_count',
                1 => 'SUM(overview_log.true_uid_count) as true_uid_count'
            ],
            //实名率
            'true_uid_rate' => [
                0 => [
                    'overview_log.true_uid_count',
                    'overview_log.reg_uid_count',
                ],
                1 => [
                    'SUM(overview_log.true_uid_count) as true_uid_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count',
                ]
            ],
            'cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.cost / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                    'data_log.cost',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.cost) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 注册成本
            'reg_muid_count' => [
                0 => 'overview_log.reg_muid_count',
                1 => 'SUM(overview_log.reg_muid_count) as reg_muid_count'
            ], // 注册设备数
            'action_muid_count' => [
                0 => 'overview_log.action_muid_count',
                1 => 'SUM(overview_log.action_muid_count) as action_muid_count'
            ], //激活设备数
            'first_day_ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                    'overview_log.first_day_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日ltv
            'ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'overview_log.total_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 累计ltv
            'first_day_pay_count' => [
                0 => 'overview_log.first_day_pay_count',
                1 => 'SUM(overview_log.first_day_pay_count) as first_day_pay_count'
            ], // 首日付费人数
            'first_day_pay_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_count / overview_log.reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                    'overview_log.first_day_pay_count',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_count) / SUM(overview_log.reg_uid_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日付费率
            'cost_per_first_day_pay' => [
                0 => ['data_log.cost', 'overview_log.first_day_pay_count'],
                1 => ['SUM(data_log.cost) as cost', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日付费成本
            'first_day_pay_money' => [
                0 => 'overview_log.first_day_pay_money',
                1 => 'SUM(overview_log.first_day_pay_money) as first_day_pay_money'
            ], // 首日付费金额
            'seventh_day_pay_money' => [
                0 => 'overview_log.seventh_day_pay_money',
                1 => 'SUM(overview_log.seventh_day_pay_money) as seventh_day_pay_money'
            ], // 首日付费金额
            'fifteenth_day_pay_money' => [
                0 => 'overview_log.fifteenth_day_pay_money',
                1 => 'SUM(overview_log.fifteenth_day_pay_money) as fifteenth_day_pay_money'
            ], // 首日付费金额
            'thirty_day_pay_money' => [
                0 => 'overview_log.thirty_day_pay_money',
                1 => 'SUM(overview_log.thirty_day_pay_money) as thirty_day_pay_money'
            ], // 首日付费金额
            'first_day_arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                    'overview_log.first_day_pay_money',
                    'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日arppu
            'arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.total_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'overview_log.total_pay_money',
                    'overview_log.total_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.total_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.total_pay_count) as total_pay_count']
            ], // 累计arppu
            'first_day_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                    'overview_log.first_day_pay_money',
                    'data_log.cost'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(data_log.cost) as cost']
            ], // 首日roi
            'total_roi' => [
                0 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost']
            ], // 累计roi
            'rate_day_roi_2' => [
                0 => ['overview_log.sum_second_day_pay_money', 'data_log.cost'],
                1 => ['SUM(overview_log.sum_second_day_pay_money) as sum_second_day_pay_money', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'first_day_roi_standard_value' => [
                0 => ['data_log.1_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.1_day_total_standard_value) as 1_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_2_standard_value' => [
                0 => ['data_log.2_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.2_day_total_standard_value) as 2_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3_standard_value' => [
                0 => ['data_log.3_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.3_day_total_standard_value) as 3_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7_standard_value' => [
                0 => ['data_log.7_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.7_day_total_standard_value) as 7_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15_standard_value' => [
                0 => ['data_log.15_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.15_day_total_standard_value) as 15_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30_standard_value' => [
                0 => ['data_log.30_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],


            'rate_login_stay_2' => [
                0 => ['overview_log.sum_day_pay_uid_second_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_second_day_login_count) as sum_day_pay_uid_second_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_3' => [
                0 => ['overview_log.sum_day_pay_uid_third_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_third_day_login_count) as sum_day_pay_uid_third_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_7' => [
                0 => ['overview_log.sum_day_pay_uid_seventh_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_seventh_day_login_count) as sum_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_15' => [
                0 => ['overview_log.sum_day_pay_uid_fifteenth_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_fifteenth_day_login_count) as sum_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_30' => [
                0 => ['overview_log.sum_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_thirty_day_login_count) as sum_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_2' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_30' => [
                0 => ['overview_log.sum_pay_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_pay_day_pay_uid_thirty_day_login_count) as sum_pay_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_first_pay_login_stay_2' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_second_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_second_day_login_count) as sum_day_first_day_pay_uid_second_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_3' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_third_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_third_day_login_count) as sum_day_first_day_pay_uid_third_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_7' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_seventh_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_seventh_day_login_count) as sum_day_first_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_15' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count) as sum_day_first_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_30' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_thirty_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_thirty_day_login_count) as sum_day_first_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            'rate_day_stay_2' => [
                0 => ['overview_log.sum_day_second_login_count', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_day_second_login_count) as sum_day_second_login_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_3' => [
                0 => ['overview_log.sum_is_third_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_third_login) as sum_is_third_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_7' => [
                0 => ['overview_log.sum_is_seventh_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_seventh_login) as sum_is_seventh_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_15' => [
                0 => ['overview_log.sum_is_fifteenth_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_fifteenth_login) as sum_is_fifteenth_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'first_day_pay_times' => [
                0 => 'overview_log.first_day_pay_times',
                1 => 'SUM(overview_log.first_day_pay_times) as first_day_pay_times'
            ],
            'total_pay_times' => [
                0 => 'overview_log.total_pay_times',
                1 => 'SUM(overview_log.total_pay_times) as total_pay_times'
            ],
            'first_day_pay_times_cost' => [
                0 => ['overview_log.first_day_pay_times', 'data_log.cost'],
                1 => ['SUM( overview_log.first_day_pay_times ) as first_day_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_times_cost' => [
                0 => ['overview_log.total_pay_times', 'data_log.cost'],
                1 => ['SUM( overview_log.total_pay_times ) as total_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'click_count' =>  [
                0 => 'overview_log.click_count',
                1 => 'SUM(overview_log.click_count) as click_count'
            ],
            'rate_day_stay_2_cost' => [
                0 => ['overview_log.sum_day_second_login_count', 'data_log.cost'],
                1 => ['SUM( overview_log.sum_day_second_login_count ) as sum_day_second_login_count', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_count' => [
                0 => 'overview_log.total_pay_count',
                1 => 'SUM(overview_log.total_pay_count) as total_pay_count'
            ],
            'total_pay_money' => [
                0 => 'overview_log.total_pay_money',
                1 => 'SUM(overview_log.total_pay_money) as total_pay_money'
            ],
            //关键等级数
            'key_role_level_count' => [
                0 => 'overview_log.key_role_level_count',
                1 => 'SUM(overview_log.key_role_level_count) as key_role_level_count'
            ],
            //关键等级占比
            'key_role_level_count_rate' => [
                0 => [
                    'overview_log.key_role_level_count',
                    'overview_log.reg_uid_count'
                ],
                1 => [
                    'SUM(overview_log.key_role_level_count) as key_role_level_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count'
                ],
            ],
            //关键等级成本
            'cost_key_role_level_count' => [
                0 => [
                    'data_log.cost',
                    'overview_log.key_role_level_count'
                ],
                1 => [
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.key_role_level_count) as key_role_level_count'
                ],
            ],
            'male_count' => [
                'overview_log.male_count',
                'SUM(overview_log.male_count) as male_count'
            ],
            'female_count' => [
                'overview_log.female_count',
                'SUM(overview_log.female_count) as female_count'
            ],
            'age_18_24_count' => [
                'overview_log.age_18_24_count as age_18_24_count',
                'SUM(overview_log.age_18_24_count) as age_18_24_count'
            ],
            'age_25_30_count' => [
                'overview_log.age_25_30_count as age_25_30_count',
                'SUM(overview_log.age_25_30_count) as age_25_30_count'
            ],
            'age_31_40_count' => [
                'overview_log.age_31_40_count as age_31_40_count',
                'SUM(overview_log.age_31_40_count) as age_31_40_count'
            ],
            'age_41_50_count' => [
                'overview_log.age_41_50_count as age_41_50_count',
                'SUM(overview_log.age_41_50_count) as age_41_50_count'
            ],
            'age_over_50_count' => [
                'overview_log.age_over_50_count as age_over_50_count',
                'SUM(overview_log.age_over_50_count) as age_over_50_count'
            ],
            'reg_old_muid_count' => [
                0 => 'overview_log.reg_old_muid_count',
                1 => 'SUM(overview_log.reg_old_muid_count) as reg_old_muid_count'
            ],
            'reg_old_clique_muid_count' => [
                0 => 'overview_log.reg_old_clique_muid_count',
                1 => 'SUM(overview_log.reg_old_clique_muid_count) as reg_old_clique_muid_count'
            ],
            'three_login_uid_count' => [
                0 => 'overview_log.three_login_uid_count',
                1 => 'SUM(overview_log.three_login_uid_count) as three_login_uid_count'
            ],
            'reg_old_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_muid_count ) as reg_old_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'reg_old_clique_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_clique_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_clique_muid_count ) as reg_old_clique_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'three_login_uid_count_per_reg' => [
                0 => ['overview_log.three_login_uid_count', 'overview_log.reg_uid_count'],
                1 => ['SUM( overview_log.three_login_uid_count ) as three_login_uid_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count'],
            ],
            'new_pay_day_money' => [
                0 => 'overview_log.new_pay_day_money',
                1 => 'SUM(overview_log.new_pay_day_money) as new_pay_day_money'
            ],
            'new_pay_day_count' => [
                0 => 'overview_log.new_pay_day_count',
                1 => 'SUM(overview_log.new_pay_day_count) as new_pay_day_count'
            ],
            //媒体通用数据
            'cost_process' => [
                0 => ['IFNULL ( CAST ( ( data_log.sum_ori_cost_for_cost_process / third_ad_log.sum_budget_for_cost_process ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                    'data_log.sum_ori_cost_for_cost_process',
                    'third_ad_log.sum_budget_for_cost_process'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.sum_ori_cost_for_cost_process) / SUM(third_ad_log.sum_budget_for_cost_process) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                    'SUM(data_log.sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
                    'SUM(third_ad_log.sum_budget_for_cost_process) as sum_budget_for_cost_process']
            ], //当天账户币消耗/预算
            'cpc' => [
                0 => ['data_log.ori_cost', 'data_log.click'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click']
            ], // cpc COST / CLICK
            'click' => [
                0 => 'data_log.click',
                1 => 'SUM(data_log.click) as click'
            ], //click
            'cpm' => [
                0 => ['data_log.ori_cost', '`data_log`.`show`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(`data_log`.`show`) as `show`']
            ], // cpm COST/(SHOW/1000)
            'show' => [
                0 => '`data_log`.`show`',
                1 => 'SUM(`data_log`.`show`) as `show`'
            ], // show
            'ori_cost' => [
                0 => 'data_log.ori_cost',
                1 => 'SUM(data_log.ori_cost) as ori_cost'
            ], // 总花费
            'click_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.click / `data_log`.`show` ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                    'data_log.click',
                    '`data_log`.`show`'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.click) / SUM(`data_log`.`show`) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                    'SUM(data_log.click) as click',
                    'SUM(`data_log`.`show`) as `show`']
            ], // 点击率
            'cost_per_convert' => [
                0 => ['data_log.ori_cost', 'data_log.`convert`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`']
            ], // 转化成本
            'convert' => [
                0 => 'data_log.`convert`',
                1 => 'SUM(data_log.`convert`) as `convert`'
            ], // 转化数
            'cost_per_active' => [
                0 => ['data_log.ori_cost', 'data_log.active_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.active_count) as active_count']
            ], // 激活成本
            'cost_per_pay' => [
                0 => ['data_log.ori_cost', 'data_log.pay_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count']
            ], // 付费成本
            'pay_count' => [
                0 => 'data_log.pay_count',
                1 => 'SUM(data_log.pay_count) as pay_count'
            ], // 付费数
            'reg_rate' => [
                0 => ['data_log.reg_count', 'data_log.click'],
                1 => ['SUM(data_log.reg_count) as reg_count', 'SUM(data_log.click) as click']
            ], // 注册率
            'active_rate' => [
                0 => ['data_log.active_count', 'data_log.click'],
                1 => ['SUM(data_log.active_count) as active_count', 'SUM(data_log.click) as click']
            ], // 激活率
            'active_count' => [
                0 => 'data_log.active_count as active_count',
                1 => 'SUM(data_log.active_count) as active_count'
            ], // 激活数
            'convert_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.`convert` / data_log.click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                    'data_log.`convert`',
                    'data_log.click'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.`convert`) / SUM(data_log.click) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                    'SUM(data_log.`convert`) as `convert`',
                    'SUM(data_log.click) as click']
            ], // 转化率
            'reg_count' => [
                0 => 'data_log.reg_count',
                1 => 'SUM(data_log.reg_count) as reg_count'
            ], // 注册数
            'media_cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.ori_cost / data_log.reg_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                    'data_log.reg_count',
                    'data_log.ori_cost'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.ori_cost) / SUM(data_log.reg_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                    'SUM(data_log.reg_count) as reg_count',
                    'SUM(data_log.ori_cost) as ori_cost']
            ],//注册成本(媒体)
            'pay_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.pay_count / data_log.active_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                    'data_log.pay_count',
                    'data_log.active_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.pay_count) / SUM(data_log.active_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                    'SUM(data_log.pay_count) as pay_count',
                    'SUM(data_log.active_count) as active_count']
            ], // 付费率

            //三日付费人数
            'day_third_day_pay_count' => [
                0 => 'overview_log.day_third_day_pay_count',
                1 => 'SUM(overview_log.day_third_day_pay_count) as day_third_day_pay_count'
            ],
            //七日付费人数
            'day_seventh_day_pay_count' => [
                0 => 'overview_log.day_seventh_day_pay_count',
                1 => 'SUM(overview_log.day_seventh_day_pay_count) as day_seventh_day_pay_count'
            ],
            //首充1-6元人数
            'first_day_pay_money_within_6_count' => [
                0 => 'overview_log.first_day_pay_money_within_6_count',
                1 => 'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count'
            ],
            //首充1-6元首日占比
            'first_day_pay_money_within_6_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money_within_6_count / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'overview_log.first_day_pay_money_within_6_count',
                    'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money_within_6_count) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            //预估终生付费
            'lifetime_money' => [
                0 => 'overview_log.lifetime_money',
                1 => 'SUM(overview_log.lifetime_money) as lifetime_money'
            ],
            //预估终生回本
            'lifetime_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.lifetime_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'overview_log.lifetime_money',
                    'data_log.cost'],
                1 => [
                    'IFNULL ( CAST ( ( SUM(overview_log.lifetime_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'SUM(overview_log.lifetime_money) as lifetime_money',
                    'SUM(data_log.cost) as cost']
            ],

            'item_id' => 'third_ad_log.item_id as item_id',
            'aweme_title' => "third_ad_log.item_id as item_id, if(third_ad_log.item_id!= 0,ifnull( aweme_log.title, third_ad_log.title ), '') AS aweme_title",
            'aweme_account' => 'third_ad_log.item_id as item_id, aweme_log.aweme_account',
            'aweme_name' => 'third_ad_log.item_id as item_id, aweme_log.aweme_name as aweme_name',
            'interface_person' => 'third_ad_log.item_id as item_id, aweme_log.interface_person as interface_person',
            'interface_person_group_name' => 'third_ad_log.item_id as item_id, aweme_log.interface_person_group_name',
            'intermediary_person' => 'third_ad_log.item_id as item_id, aweme_log.intermediary_person',
            'aweme_item_publish_time' => 'third_ad_log.item_id as item_id, aweme_log.aweme_item_publish_time as aweme_item_publish_time',

            'talent_account' => '"" as talent_account',
            'talent_name' => 'talent_log.aweme_name as talent_name',
            'talent_interface_person' => 'talent_log.interface_person as talent_interface_person',
            'gdt_inventory_subdivision' => 'third_ad_log.gdt_inventory_subdivision as gdt_inventory_subdivision',
            'gdt_xqxs_inventory_subdivision' => 'third_ad_log.gdt_xqxs_inventory_subdivision as gdt_xqxs_inventory_subdivision',
        ],

        MediaType::KUAISHOU => [
            'media_type' => MediaType::KUAISHOU.' as media_type',
            'port_version' => 'third_ad_log.port_version as port_version',
            'inventory_type' => 'third_ad_log.inventory_type as inventory_type',
            //2s播放率
            'ks_ad_photo_played_2s_ratio' => [
                ['data_log.play_2s_num', 'data_log.ks_aclick'],
                ['SUM(data_log.play_2s_num) as play_2s_num', 'SUM(data_log.ks_aclick) as ks_aclick']
            ],
            //3s播放率
            'ks_play_3s_ratio' => [
                ['data_log.play_3s_num', 'data_log.ks_aclick'],
                ['SUM(data_log.play_3s_num) as play_3s_num', 'SUM(data_log.ks_aclick) as ks_aclick']
            ],
            //5s播放率
            'ks_play_5s_ratio' => [
                ['data_log.play_5s_num' ,'data_log.ks_aclick'],
                ['SUM(data_log.play_5s_num) as play_5s_num' ,'SUM(data_log.ks_aclick) as ks_aclick'],
            ],
            //10s播放率
            'ks_ad_photo_played_10s_ratio' => [
                ['data_log.play_10s_num', 'data_log.ks_aclick'],
                ['SUM(data_log.play_10s_num) as play_10s_num', 'SUM(data_log.ks_aclick) as ks_aclick'],
            ],
            //完播率
            'ks_play_end_ratio' => [
                ['data_log.play_end_num', 'data_log.ks_aclick'],
                ['SUM(data_log.play_end_num) as play_end_num', 'SUM(data_log.ks_aclick) as ks_aclick'],
            ],
            //75%进度播放率
            'ks_ad_photo_played_75percent_ratio' => [
                ['data_log.played_75percent_num', 'data_log.ks_aclick'],
                ['SUM(data_log.played_75percent_num) as played_75percent_num', 'SUM(data_log.ks_aclick) as ks_aclick'],
            ],

            //素材属性数据
            'working_hours' => 'material_log.working_hours as working_hours',
            'material_platform' => 'material_log.material_platform as material_platform',
            'material_name' => 'material_log.name as material_name',
            'material_id' => 'material_log.material_id as material_id',
            'material_filename' => 'material_log.filename as material_filename',
            'material_file_id' => 'material_log.id as material_file_id',
            'signature' => 'third_ad_log.signature as signature',
            'author' => 'material_log.author',
            'theme_id' => 'material_log.theme_id',
            'theme_pid' => 'material_log.theme_pid',
            'file_type' => 'third_ad_log.file_type',
            'create_time' => 'material_log.insert_time as create_time',
            'urls' => 'third_ad_log.signature as signature, third_ad_log.file_type as file_type, material_log.zx_play_url as zx_play_url',
            'c_author' => 'material_log.c_author',
            'a_author' => 'material_log.a_author',
            'm1_author' => 'material_log.m1_author',
            'm2_author' => 'material_log.m2_author',
            'm3_author' => 'material_log.m3_author',
            'm4_author' => 'material_log.m4_author',
            'm5_author' => 'material_log.m5_author',
            'actor' => 'material_log.actor',
            'shoot' => 'material_log.shoot',
            'material_create_date' => 'material_log.insert_time as material_create_date ',
            'original' => 'material_log.original as original',
            'size' => "material_log.size as size",
            'effect_grade7' => "material_log.effect_grade7 as effect_grade7",
            'effect_grade30' => "material_log.effect_grade30 as effect_grade30",
            'is_priority' => "material_log.is_priority as is_priority",
            'is_3d' => "material_log.is_3d as is_3d",
            'is_immortal' => "material_log.is_immortal as is_immortal",
            'label' => "material_log.label_id as label",
            //计数项数据
            'count' => "COUNT( DISTINCT third_ad_log.web_creator ) as count",
            'count_ad2' => "COUNT( DISTINCT third_ad_log.count_ad2 ) as count_ad2",
            'count_ad2_deliveried' => "COUNT( DISTINCT data_log.data_log_ad2_id, IF ( data_log.cost > 0, TRUE, NULL ) ) AS count_ad2_deliveried",
            'count_ad2_delivering' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.ad_status = 20 , TRUE, NULL ) ) AS count_ad2_delivering",
            'count_ad2_undeliveried' => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.is_cost = 0, TRUE, NULL ) ) AS count_ad2_undeliveried",
            //'count_cost_date' => [
            //    0 => 'data_log.count_cost_date',
            //    1 => 'SUM(data_log.count_cost_date) as count_cost_date'
            //],
            'count_creative' => "SUM(third_ad_log.count_creative) as count_creative",
            //业务数据
            'platform' => 'third_ad_log.platform as platform',
            'os' => "third_ad_log.os as os",
            'create_type' => 'third_ad_log.create_type as create_type',
            'account_id' => 'third_ad_log.account_id',
            'account_name' => 'third_ad_log.account_name, third_ad_log.account_id',
            'ad1_id' => 'third_ad_log.ad1_id',
            'ad1_name' => 'third_ad_log.ad1_name, third_ad_log.ad1_id',
            'ad2_id' => 'third_ad_log.ad2_id as ad2_id',
            'ad2_name' => 'third_ad_log.ad2_name, third_ad_log.ad2_id as ad2_id',
            'ad3_id' => 'third_ad_log.ad3_id',
            'ad3_name' => 'third_ad_log.ad3_name, third_ad_log.ad3_id',
            'game_id' => 'third_ad_log.game_id as game_id',
            'game_name' => 'third_ad_log.game_name as game_name',
            'main_game_id' => 'third_ad_log.main_game_id as main_game_id',
            'main_game_name' => 'third_ad_log.main_game_name as main_game_name',
            'root_game_id' => 'third_ad_log.root_game_id as root_game_id',
            'root_game_name' => 'third_ad_log.root_game_name as root_game_name',
            'site_id' => 'third_ad_log.site_id as site_id',
            'agent_leader' => 'third_ad_log.agent_leader as agent_leader',
            'agent_leader_group_name' => 'third_ad_log.agent_leader_group_name as agent_leader_group_name',
            'agent_id' => 'third_ad_log.agent_name as agent_name, third_ad_log.agent_id as agent_id',
            'agent_group_id' => 'third_ad_log.agent_group_name as agent_group_name, third_ad_log.agent_group_id as agent_group_id',
            'standard_reached_cost' => [
                0 => 'standard_reached_cost',
                1 => 'SUM(standard_reached_cost) as standard_reached_cost'
            ],
            'standard_unreached_cost' => [
                0 => 'standard_unreached_cost',
                1 => 'SUM(standard_unreached_cost) as standard_unreached_cost'
            ],
            'cost_first_date' => 'min(data_cost_first_log.cost_first_date) as cost_first_date',
            'cost_first_30_day' => [
                0 => 'data_cost_first_log.cost_first_30_day',
                1 => 'SUM(data_cost_first_log.cost_first_30_day) as cost_first_30_day'
            ],
            'cost_first_30_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_30_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_30_day_this_month) as cost_first_30_day_this_month'
            ],
            'cost_first_60_day_this_month' => [
                0 => 'data_cost_first_log.cost_first_60_day_this_month',
                1 => 'SUM(data_cost_first_log.cost_first_60_day_this_month) as cost_first_60_day_this_month'
            ],
            'cost' => [
                0 => 'data_log.cost',
                1 => 'SUM(data_log.cost) as cost'
            ], // 返点后消耗
            'reg_uid_count' => [
                0 => 'overview_log.reg_uid_count',
                1 => 'SUM(overview_log.reg_uid_count) as reg_uid_count'
            ], // 注册数
            'action_uid_reg_rate' => [
                0 => ['overview_log.reg_uid_count',
                    'overview_log.action_muid_count'],
                1 => ['SUM(overview_log.reg_uid_count) as reg_uid_count',
                    'SUM(overview_log.action_muid_count) as action_muid_count']
            ], // 激活注册率
            //实名数
            'true_uid_count' => [
                0 => 'overview_log.true_uid_count',
                1 => 'SUM(overview_log.true_uid_count) as true_uid_count'
            ],
            //实名率
            'true_uid_rate' => [
                0 => [
                    'overview_log.true_uid_count',
                    'overview_log.reg_uid_count',
                ],
                1 => [
                    'SUM(overview_log.true_uid_count) as true_uid_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count',
                ]
            ],
            'cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.cost / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                    'data_log.cost',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.cost) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 注册成本
            'reg_muid_count' => [
                0 => 'overview_log.reg_muid_count',
                1 => 'SUM(overview_log.reg_muid_count) as reg_muid_count'
            ], // 注册设备数
            'action_muid_count' => [
                0 => 'overview_log.action_muid_count',
                1 => 'SUM(overview_log.action_muid_count) as action_muid_count'
            ], //激活设备数
            'first_day_ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                    'overview_log.first_day_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日ltv
            'ltv' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.reg_uid_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'overview_log.total_pay_money',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.reg_uid_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS ltv',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日ltv
            'first_day_pay_count' => [
                0 => 'overview_log.first_day_pay_count',
                1 => 'SUM(overview_log.first_day_pay_count) as first_day_pay_count'
            ], // 首日付费人数
            'first_day_pay_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_count / overview_log.reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                    'overview_log.first_day_pay_count',
                    'overview_log.reg_uid_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_count) / SUM(overview_log.reg_uid_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ], // 首日付费率
            'cost_per_first_day_pay' => [
                0 => ['data_log.cost', 'overview_log.first_day_pay_count'],
                1 => ['SUM(data_log.cost) as cost', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日付费成本
            'first_day_pay_money' => [
                0 => 'overview_log.first_day_pay_money',
                1 => 'SUM(overview_log.first_day_pay_money) as first_day_pay_money'
            ], // 首日付费金额
            'seventh_day_pay_money' => [
                0 => 'overview_log.seventh_day_pay_money',
                1 => 'SUM(overview_log.seventh_day_pay_money) as seventh_day_pay_money'
            ], // 首日付费金额
            'fifteenth_day_pay_money' => [
                0 => 'overview_log.fifteenth_day_pay_money',
                1 => 'SUM(overview_log.fifteenth_day_pay_money) as fifteenth_day_pay_money'
            ], // 首日付费金额
            'thirty_day_pay_money' => [
                0 => 'overview_log.thirty_day_pay_money',
                1 => 'SUM(overview_log.thirty_day_pay_money) as thirty_day_pay_money'
            ], // 首日付费金额
            'first_day_arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                    'overview_log.first_day_pay_money',
                    'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ], // 首日arppu
            'arppu' => [
                0 => ['IFNULL ( CAST ( ( overview_log.total_pay_money / overview_log.total_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'overview_log.total_pay_money',
                    'overview_log.total_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.total_pay_money) / SUM(overview_log.total_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS arppu',
                    'SUM(overview_log.total_pay_money) as total_pay_money',
                    'SUM(overview_log.total_pay_count) as total_pay_count']
            ], // 累计arppu
            'first_day_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                    'overview_log.first_day_pay_money',
                    'data_log.cost'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
                    'SUM(overview_log.first_day_pay_money) as first_day_pay_money',
                    'SUM(data_log.cost) as cost']
            ], // 首日roi
            'total_roi' => [
                0 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.total_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi','SUM(overview_log.total_pay_money) as total_pay_money','SUM(data_log.cost) as cost']
            ], // 累计roi
            'rate_day_roi_2' => [
                0 => ['overview_log.sum_second_day_pay_money', 'data_log.cost'],
                1 => ['SUM(overview_log.sum_second_day_pay_money) as sum_second_day_pay_money', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_third_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3','SUM(overview_log.sum_third_day_pay_money) as sum_third_day_pay_money',' SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_seventh_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7','SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_fifteenth_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15','SUM(overview_log.sum_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_thirty_day_pay_money) / SUM(data_log.cost)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30','SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money','SUM(data_log.cost) as cost']
            ],
            'first_day_roi_standard_value' => [
                0 => ['data_log.1_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.1_day_total_standard_value) as 1_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_2_standard_value' => [
                0 => ['data_log.2_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.2_day_total_standard_value) as 2_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_3_standard_value' => [
                0 => ['data_log.3_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.3_day_total_standard_value) as 3_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_7_standard_value' => [
                0 => ['data_log.7_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.7_day_total_standard_value) as 7_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_15_standard_value' => [
                0 => ['data_log.15_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.15_day_total_standard_value) as 15_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],
            'rate_day_roi_30_standard_value' => [
                0 => ['data_log.30_day_total_standard_value','data_log.cost'],
                1 => ['SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value', 'SUM(data_log.cost) as cost']
            ],


            'rate_login_stay_2' => [
                0 => ['overview_log.sum_day_pay_uid_second_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_second_day_login_count) as sum_day_pay_uid_second_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_3' => [
                0 => ['overview_log.sum_day_pay_uid_third_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_third_day_login_count) as sum_day_pay_uid_third_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_7' => [
                0 => ['overview_log.sum_day_pay_uid_seventh_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_seventh_day_login_count) as sum_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_15' => [
                0 => ['overview_log.sum_day_pay_uid_fifteenth_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_fifteenth_day_login_count) as sum_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_login_stay_30' => [
                0 => ['overview_log.sum_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_day_pay_uid_thirty_day_login_count) as sum_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],

            'rate_pay_login_stay_2' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_2','SUM(overview_log.sum_pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_3' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_3','SUM(overview_log.sum_pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_7' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_7','SUM(overview_log.sum_pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_15' => [
                0 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count'],
                1 => ['IFNULL(CAST((SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) / SUM(overview_log.total_pay_count)) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_pay_login_stay_15','SUM(overview_log.sum_pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count','SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_pay_login_stay_30' => [
                0 => ['overview_log.sum_pay_day_pay_uid_thirty_day_login_count', 'overview_log.total_pay_count'],
                1 => ['SUM(overview_log.sum_pay_day_pay_uid_thirty_day_login_count) as sum_pay_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.total_pay_count) as total_pay_count']
            ],
            'rate_first_pay_login_stay_2' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_second_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_second_day_login_count) as sum_day_first_day_pay_uid_second_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_3' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_third_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_third_day_login_count) as sum_day_first_day_pay_uid_third_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_7' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_seventh_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_seventh_day_login_count) as sum_day_first_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_15' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_fifteenth_day_login_count) as sum_day_first_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],
            'rate_first_pay_login_stay_30' => [
                0 => ['overview_log.sum_day_first_day_pay_uid_thirty_day_login_count', 'overview_log.first_day_pay_count'],
                1 => ['SUM(overview_log.sum_day_first_day_pay_uid_thirty_day_login_count) as sum_day_first_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            'rate_day_stay_2' => [
                0 => ['overview_log.sum_day_second_login_count', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_day_second_login_count) as sum_day_second_login_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_3' => [
                0 => ['overview_log.sum_is_third_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_third_login) as sum_is_third_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_7' => [
                0 => ['overview_log.sum_is_seventh_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_seventh_login) as sum_is_seventh_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'rate_day_stay_15' => [
                0 => ['overview_log.sum_is_fifteenth_login', 'overview_log.reg_uid_count'],
                1 => ['SUM(overview_log.sum_is_fifteenth_login) as sum_is_fifteenth_login', 'SUM(overview_log.reg_uid_count) as reg_uid_count']
            ],
            'first_day_pay_times' => [
                0 => 'overview_log.first_day_pay_times',
                1 => 'SUM(overview_log.first_day_pay_times) as first_day_pay_times'
            ],
            'total_pay_times' => [
                0 => 'overview_log.total_pay_times',
                1 => 'SUM(overview_log.total_pay_times) as total_pay_times'
            ],
            'first_day_pay_times_cost' => [
                0 => ['overview_log.first_day_pay_times', 'data_log.cost'],
                1 => ['SUM( overview_log.first_day_pay_times ) as first_day_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_times_cost' => [
                0 => ['overview_log.total_pay_times', 'data_log.cost'],
                1 => ['SUM( overview_log.total_pay_times ) as total_pay_times', 'SUM(data_log.cost) as cost'],
            ],
            'click_count' =>  [
                0 => 'overview_log.click_count',
                1 => 'SUM(overview_log.click_count) as click_count'
            ],
            'rate_day_stay_2_cost' => [
                0 => ['overview_log.sum_day_second_login_count', 'data_log.cost'],
                1 => ['SUM( overview_log.sum_day_second_login_count ) as sum_day_second_login_count', 'SUM(data_log.cost) as cost'],
            ],
            'total_pay_count' => [
                0 => 'overview_log.total_pay_count',
                1 => 'SUM(overview_log.total_pay_count) as total_pay_count'
            ],
            'total_pay_money' => [
                0 => 'overview_log.total_pay_money',
                1 => 'SUM(overview_log.total_pay_money) as total_pay_money'
            ],
            //关键等级数
            'key_role_level_count' => [
                0 => 'overview_log.key_role_level_count',
                1 => 'SUM(overview_log.key_role_level_count) as key_role_level_count'
            ],
            //关键等级占比
            'key_role_level_count_rate' => [
                0 => [
                    'overview_log.key_role_level_count',
                    'overview_log.reg_uid_count'
                ],
                1 => [
                    'SUM(overview_log.key_role_level_count) as key_role_level_count',
                    'SUM(overview_log.reg_uid_count) as reg_uid_count'
                ],
            ],
            //关键等级成本
            'cost_key_role_level_count' => [
                0 => [
                    'data_log.cost',
                    'overview_log.key_role_level_count'
                ],
                1 => [
                    'SUM(data_log.cost) as cost',
                    'SUM(overview_log.key_role_level_count) as key_role_level_count'
                ],
            ],
            'male_count' => [
                'overview_log.male_count',
                'SUM(overview_log.male_count) as male_count'
            ],
            'female_count' => [
                'overview_log.female_count',
                'SUM(overview_log.female_count) as female_count'
            ],
            'age_18_24_count' => [
                'overview_log.age_18_24_count as age_18_24_count',
                'SUM(overview_log.age_18_24_count) as age_18_24_count'
            ],
            'age_25_30_count' => [
                'overview_log.age_25_30_count as age_25_30_count',
                'SUM(overview_log.age_25_30_count) as age_25_30_count'
            ],
            'age_31_40_count' => [
                'overview_log.age_31_40_count as age_31_40_count',
                'SUM(overview_log.age_31_40_count) as age_31_40_count'
            ],
            'age_41_50_count' => [
                'overview_log.age_41_50_count as age_41_50_count',
                'SUM(overview_log.age_41_50_count) as age_41_50_count'
            ],
            'age_over_50_count' => [
                'overview_log.age_over_50_count as age_over_50_count',
                'SUM(overview_log.age_over_50_count) as age_over_50_count'
            ],
            'reg_old_muid_count' => [
                0 => 'overview_log.reg_old_muid_count',
                1 => 'SUM(overview_log.reg_old_muid_count) as reg_old_muid_count'
            ],
            'reg_old_clique_muid_count' => [
                0 => 'overview_log.reg_old_clique_muid_count',
                1 => 'SUM(overview_log.reg_old_clique_muid_count) as reg_old_clique_muid_count'
            ],
            'three_login_uid_count' => [
                0 => 'overview_log.three_login_uid_count',
                1 => 'SUM(overview_log.three_login_uid_count) as three_login_uid_count'
            ],
            'reg_old_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_muid_count ) as reg_old_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'reg_old_clique_muid_count_per_reg' => [
                0 => ['overview_log.reg_old_clique_muid_count', 'overview_log.sum_day_reg_muid_count'],
                1 => ['SUM( overview_log.reg_old_clique_muid_count ) as reg_old_clique_muid_count', 'SUM(overview_log.sum_day_reg_muid_count) as sum_day_reg_muid_count'],
            ],
            'three_login_uid_count_per_reg' => [
                0 => ['overview_log.three_login_uid_count', 'overview_log.reg_uid_count'],
                1 => ['SUM( overview_log.three_login_uid_count ) as three_login_uid_count', 'SUM(overview_log.reg_uid_count) as reg_uid_count'],
            ],
            'new_pay_day_money' => [
                0 => 'overview_log.new_pay_day_money',
                1 => 'SUM(overview_log.new_pay_day_money) as new_pay_day_money'
            ],
            'new_pay_day_count' => [
                0 => 'overview_log.new_pay_day_count',
                1 => 'SUM(overview_log.new_pay_day_count) as new_pay_day_count'
            ],
            //媒体通用数据
            'cost_process' => [
                0 => ['IFNULL ( CAST ( ( data_log.sum_ori_cost_for_cost_process / third_ad_log.sum_budget_for_cost_process ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                    'data_log.sum_ori_cost_for_cost_process',
                    'third_ad_log.sum_budget_for_cost_process'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.sum_ori_cost_for_cost_process) / SUM(third_ad_log.sum_budget_for_cost_process) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
                    'SUM(data_log.sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
                    'SUM(third_ad_log.sum_budget_for_cost_process) as sum_budget_for_cost_process']
            ], //当天账户币消耗/预算
            'cpc' => [
                0 => ['data_log.ori_cost', 'data_log.click'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click']
            ], // cpc COST / CLICK
            'click' => [
                0 => 'data_log.click',
                1 => 'SUM(data_log.click) as click'
            ], //click
            'cpm' => [
                0 => ['data_log.ori_cost', '`data_log`.`show`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(`data_log`.`show`) as `show`']
            ], // cpm COST/(SHOW/1000)
            'show' => [
                0 => '`data_log`.`show`',
                1 => 'SUM(`data_log`.`show`) as `show`'
            ], // show
            'ori_cost' => [
                0 => 'data_log.ori_cost',
                1 => 'SUM(data_log.ori_cost) as ori_cost'
            ], // 总花费
            'click_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.click / `data_log`.`show` ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                    'data_log.click',
                    '`data_log`.`show`'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.click) / SUM(`data_log`.`show`) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
                    'SUM(data_log.click) as click',
                    'SUM(`data_log`.`show`) as `show`']
            ], // 点击率
            'cost_per_convert' => [
                0 => ['data_log.ori_cost', 'data_log.`convert`'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`']
            ], // 转化成本
            'convert' => [
                0 => 'data_log.`convert`',
                1 => 'SUM(data_log.`convert`) as `convert`'
            ], // 转化数
            'cost_per_active' => [
                0 => ['data_log.ori_cost', 'data_log.active_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.active_count) as active_count']
            ], // 激活成本
            'cost_per_pay' => [
                0 => ['data_log.ori_cost', 'data_log.pay_count'],
                1 => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count']
            ], // 付费成本
            'pay_count' => [
                0 => 'data_log.pay_count',
                1 => 'SUM(data_log.pay_count) as pay_count'
            ], // 付费数
            'reg_rate' => [
                0 => ['data_log.reg_count', 'data_log.click'],
                1 => ['SUM(data_log.reg_count) as reg_count', 'SUM(data_log.click) as click']
            ], // 注册率
            'active_rate' => [
                0 => ['data_log.active_count', 'data_log.click'],
                1 => ['SUM(data_log.active_count) as active_count', 'SUM(data_log.click) as click']
            ], // 激活率
            'active_count' => [
                0 => 'data_log.active_count as active_count',
                1 => 'SUM(data_log.active_count) as active_count'
            ], // 激活数
            'convert_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.`convert` / data_log.click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                    'data_log.`convert`',
                    'data_log.click'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.`convert`) / SUM(data_log.click) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
                    'SUM(data_log.`convert`) as `convert`',
                    'SUM(data_log.click) as click']
            ], // 转化率
            'reg_count' => [
                0 => 'data_log.reg_count',
                1 => 'SUM(data_log.reg_count) as reg_count'
            ], // 注册数
            'media_cost_per_reg' => [
                0 => ['IFNULL ( CAST ( ( data_log.ori_cost / data_log.reg_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                    'data_log.reg_count',
                    'data_log.ori_cost'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.ori_cost) / SUM(data_log.reg_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
                    'SUM(data_log.reg_count) as reg_count',
                    'SUM(data_log.ori_cost) as ori_cost']
            ],//注册成本(媒体)
            'pay_rate' => [
                0 => ['IFNULL ( CAST ( ( data_log.pay_count / data_log.active_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                    'data_log.pay_count',
                    'data_log.active_count'],
                1 => ['IFNULL ( CAST ( ( SUM(data_log.pay_count) / SUM(data_log.active_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
                    'SUM(data_log.pay_count) as pay_count',
                    'SUM(data_log.active_count) as active_count']
            ], // 付费率

            //三日付费人数
            'day_third_day_pay_count' => [
                0 => 'overview_log.day_third_day_pay_count',
                1 => 'SUM(overview_log.day_third_day_pay_count) as day_third_day_pay_count'
            ],
            //七日付费人数
            'day_seventh_day_pay_count' => [
                0 => 'overview_log.day_seventh_day_pay_count',
                1 => 'SUM(overview_log.day_seventh_day_pay_count) as day_seventh_day_pay_count'
            ],
            //首充1-6元人数
            'first_day_pay_money_within_6_count' => [
                0 => 'overview_log.first_day_pay_money_within_6_count',
                1 => 'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count'
            ],
            //首充1-6元首日占比
            'first_day_pay_money_within_6_rate' => [
                0 => ['IFNULL ( CAST ( ( overview_log.first_day_pay_money_within_6_count / overview_log.first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'overview_log.first_day_pay_money_within_6_count',
                    'overview_log.first_day_pay_count'],
                1 => ['IFNULL ( CAST ( ( SUM(overview_log.first_day_pay_money_within_6_count) / SUM(overview_log.first_day_pay_count) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_money_within_6_rate',
                    'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count',
                    'SUM(overview_log.first_day_pay_count) as first_day_pay_count']
            ],

            //预估终生付费
            'lifetime_money' => [
                0 => 'overview_log.lifetime_money',
                1 => 'SUM(overview_log.lifetime_money) as lifetime_money'
            ],
            //预估终生回本
            'lifetime_roi' => [
                0 => ['IFNULL ( CAST ( ( overview_log.lifetime_money / data_log.cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'overview_log.lifetime_money',
                    'data_log.cost'],
                1 => [
                    'IFNULL ( CAST ( ( SUM(overview_log.lifetime_money) / SUM(data_log.cost) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS lifetime_roi',
                    'SUM(overview_log.lifetime_money) as lifetime_money',
                    'SUM(data_log.cost) as cost']
            ],

            'item_id' => 'third_ad_log.item_id as item_id',
            'aweme_title' => "third_ad_log.item_id as item_id, if(third_ad_log.item_id!= 0,ifnull( aweme_log.title, third_ad_log.title ), '') AS aweme_title",
            'aweme_account' => 'third_ad_log.item_id as item_id, aweme_log.aweme_account',
            'aweme_name' => 'third_ad_log.item_id as item_id, aweme_log.aweme_name as aweme_name',
            'interface_person' => 'third_ad_log.item_id as item_id, aweme_log.interface_person as interface_person',
            'interface_person_group_name' => 'third_ad_log.item_id as item_id, aweme_log.interface_person_group_name',
            'intermediary_person' => 'third_ad_log.item_id as item_id, aweme_log.intermediary_person',
            'aweme_item_publish_time' => 'third_ad_log.item_id as item_id, aweme_log.aweme_item_publish_time as aweme_item_publish_time',

            'talent_account' => 'third_ad_log.talent_account as talent_account',
            'talent_name' => 'talent_log.aweme_name as talent_name',
            'talent_interface_person' => 'talent_log.interface_person as talent_interface_person',
        ],
    ];

    const AD_LOG = [
        0 => [
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            'port_version' => ['ad_log.port_version as port_version'],
            'media_type' => ['ad_log.media_type'],
            'platform' => ['ad_log.platform'],
            'os' => ['game.os'],
            'create_type' => ['ad_log.create_type'],
            'account_id' => ['ad_log.account_id'],
            'ad2_id' => ['ad_log.ad2_id'],
            'ad2_name' => ['ad_log.ad2_name'],
            'site_id' => ['ad_log.site_id'],

            //通用属性
            'ad2_create_time' => ['ad_log.ad2_create_time'],
        ],

        MediaType::TOUTIAO => [
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            'port_version' => ['ad_log.port_version as port_version'],
            'media_type' => [MediaType::TOUTIAO.' as media_type'],
            'tt_materials_type' => ['ad_log.materials_type as tt_materials_type'],
            'platform' => ['ad_log.platform'],
            'os' => ['game.os'],
            'create_type' => ['ad_log.create_type'],
            'account_id' => ['ad_log.account_id'],
            'ad2_id' => ['ad_log.ad_id'],
            'ad2_name' => ['ad_log.ad_name'],
            'site_id' => ['ad_log.site_id'],
            //计数项数据
            'count' => ['ad_log.web_creator'],
            'count_ad2' => ['ad_log.ad_id'],
            'count_ad2_delivering' => ['ad_log.ad_id', 'ad_log.opt_status'],
            'count_ad2_undeliveried' => ['ad_log.ad_id', 'ad_log.is_cost'],
            //通用属性
            'ad2_create_time' => ['ad_log.ad2_create_time'],
            //
        ],

        MediaType::TENCENT => [
            'inventory_type' => ['ad_log.site_set as inventory_type'],
            'port_version' => ['ad_log.port_version as port_version'],
            'media_type' => [MediaType::TENCENT.' as media_type'],
            'platform' => ['ad_log.platform'],
            'os' => ['game.os'],
            'create_type' => ['ad_log.create_type'],
            'account_id' => ['ad_log.account_id'],
            'ad2_id' => ['ad_log.adgroup_id'],
            'ad2_name' => ['ad_log.adgroup_name'],
            'site_id' => ['ad_log.site_id'],
            //计数项数据
            'count' => ['ad_log.web_creator'],
            'count_ad2' => ['ad_log.adgroup_id'],
            'count_ad2_delivering' => ['ad_log.adgroup_id', 'ad_log.system_status'],
            'count_ad2_undeliveried' => ['ad_log.adgroup_id', 'ad_log.is_cost'],
            //通用属性
            'ad2_create_time' => ['ad_log.created_time'],
        ],
        MediaType::KUAISHOU => [
            'inventory_type' => ['ad_log.scene_id as inventory_type'],
            'port_version' => ['ad_log.port_version as port_version'],
            'media_type' => [MediaType::KUAISHOU.' as media_type'],
            'platform' => ['ad_log.platform'],
            'os' => ['game.os'],
            'create_type' => ['ad_log.create_type'],
            'account_id' => ['ad_log.account_id'],
            'ad2_id' => ['ad_log.ad_id'],
            'ad2_name' => ['ad_log.ad_name'],
            'site_id' => ['ad_log.site_id'],
            //计数项数据
            'count' => ['ad_log.web_creator'],
            'count_ad2' => ['ad_log.ad_id'],
            'count_ad2_delivering' => ['ad_log.ad_id', 'ad_log.ad_status'],
            'count_ad2_undeliveried' => ['ad_log.ad_id', 'ad_log.is_cost'],
            //通用属性
            'ad2_create_time' => ['ad_log.ad_create_time'],
        ]
    ];

    const DATA_LOG = [
        0 => [
            'gdt_inventory_subdivision' => ['data_log.site_set AS gdt_inventory_subdivision'],
            'gdt_xqxs_inventory_subdivision' => ["CASE 
      WHEN ( data_log.site_set = 'SITE_SET_KANDIAN') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_QQ_MUSIC_GAME') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_VIDEO') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_NEWS') THEN 'SITE_SET_XQXS' 
      ELSE data_log.site_set END AS gdt_xqxs_inventory_subdivision"],
            'count_ad2_deliveried' => ['data_log.ad2_id as data_log_ad2_id', 'SUM(data_log.cost) as cost'],
            //'count_cost_date' => ['COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, TRUE, null) ) as count_cost_date'],

            'cost_date' => ['data_log.cost_date'],
            'cost_month' => ["DATE_FORMAT(data_log.cost_date,'%Y-%m') as cost_month"],
            //'count_cost_date' => ['cost_date', 'SUM(cost) as cost'],
            'cost' => ['SUM(data_log.cost) as cost'],
            'cost_per_reg' => ['SUM(data_log.cost) as cost'],
            'cost_process' => ["SUM(if(cost_date = CURRENT_DATE,data_log.ori_cost,0)) as sum_ori_cost_for_cost_process"],
            'cpc' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click'],
            'click' => ['SUM(data_log.click) as click'],
            'cpm' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`show`) as `show`'],
            'show' => ['SUM(data_log.`show`) as `show`'],
            'ori_cost' => ['SUM(data_log.ori_cost) as ori_cost'],
            'click_rate' => ['SUM(data_log.click) as click', 'SUM(data_log.`show`) as `show`'],
            'cost_per_convert' => ['SUM(data_log.convert) as convert', 'SUM(data_log.ori_cost) as ori_cost'],
            'convert' => ['SUM(data_log.convert) as convert'],
            'cost_per_active' => ['SUM(active) as active_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'cost_per_pay' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count'],
            'pay_count' => ['SUM(data_log.pay_count) as pay_count'],
            'reg_rate' => ['SUM(register) as reg_count', 'SUM(data_log.click) as click'],
            'active_rate' => ['SUM(active) as active_count', 'SUM(data_log.click) as click'],
            'active_count' => ['SUM(active) as active_count'],
            'convert_rate' => ['SUM(data_log.convert) as convert', 'SUM(data_log.click) as click'],
            'reg_count' => ['SUM(register) as reg_count'],
            'media_cost_per_reg' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(register) as reg_count'],
            'pay_rate' => ['SUM(data_log.pay_count) as pay_count', 'SUM(active) as active_count'],

            'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost'],
            'cost_key_role_level_count' => ['SUM(data_log.cost) as cost'],
            'first_day_roi' => ['SUM(data_log.cost) as cost'],
            'total_roi' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_2' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_3' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_7' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_15' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_30' => ['SUM(data_log.cost) as cost'],
            'first_day_roi_standard_value' => [
                "sum(cost*day_1_standard_value) AS 1_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_2_standard_value' => [
                "sum(cost*day_2_standard_value) AS 2_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_3_standard_value' => [
                "sum(cost*day_3_standard_value) AS 3_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_7_standard_value' => [
                "sum(cost*day_7_standard_value) AS 7_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_15_standard_value' => [
                "sum(cost*day_15_standard_value) AS 15_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_30_standard_value' => [
                "sum(cost*day_30_standard_value) AS 30_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
        ],

        MediaType::TOUTIAO => [
            'tt_attribution_next_day_open_cnt' => ['SUM(data_log.attribution_next_day_open_cnt)  as tt_attribution_next_day_open_cnt'],
            'tt_loan_completion' => ['SUM(data_log.loan_completion) as tt_loan_completion'],
            'tt_loan_completion_cost' => ['SUM(data_log.loan_completion) as tt_loan_completion', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_loan_completion_rate' => ['SUM(data_log.loan_completion) as tt_loan_completion', 'SUM(register) as reg_count'],
            'tt_download_finish' => ['SUM(data_log.download_finish) as tt_download_finish'],
            'tt_download_finish_cost' => ['SUM(data_log.download_finish) as tt_download_finish', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_download_finish_rate' => ['SUM(data_log.download_finish) as tt_download_finish',  'SUM(data_log.download_start) as tt_download_start'],
            'tt_install_finish' => ['SUM(data_log.install_finish) as tt_install_finish'],
            'tt_install_finish_rate' => ['SUM(data_log.install_finish) as tt_install_finish', 'SUM(data_log.download_start) as tt_download_start'],
            'tt_install_finish_cost' => ['SUM(data_log.install_finish) as tt_install_finish', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_download_start' => ['SUM(data_log.download_start) as tt_download_start'],
            'tt_download_start_rate' => ['SUM(data_log.download_start) as tt_download_start', 'SUM(data_log.click) as click'],
            'tt_download_start_cost' => ['SUM(data_log.download_start) as tt_download_start', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_click_install' => ['SUM(data_log.click_install) as tt_click_install'],
            'tt_deep_convert_rate' => ['SUM(data_log.deep_convert) as tt_deep_convert', 'SUM(data_log.convert) as tt_convert'],
            'tt_deep_convert' => ['SUM(data_log.deep_convert) as tt_deep_convert'],
            'tt_deep_convert_cost' => ['SUM(data_log.deep_convert) as tt_deep_convert', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_next_day_open_rate' => ['SUM(data_log.next_day_open) as tt_next_day_open', 'SUM(active) as active_count'],
            'tt_next_day_open_cost' => ['SUM(data_log.next_day_open) as tt_next_day_open', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_next_day_open' => ['SUM(data_log.next_day_open) as tt_next_day_open'],
            'tt_game_addiction' => ['SUM(data_log.game_addiction) as tt_game_addiction'],
            'tt_game_addiction_rate' => ['SUM(data_log.game_addiction) as tt_game_addiction', 'SUM(active) as active_count'],
            'tt_game_addiction_cost' => ['SUM(data_log.game_addiction) as tt_game_addiction', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_play_25_feed_break' => ['SUM(data_log.play_25_feed_break) as tt_play_25_feed_break'],
            'tt_play_50_feed_break' => ['SUM(data_log.play_50_feed_break) as tt_play_50_feed_break'],
            'tt_play_75_feed_break' => ['SUM(data_log.play_75_feed_break) as tt_play_75_feed_break'],
            'tt_play_100_feed_break' => ['SUM(data_log.play_100_feed_break) as tt_play_100_feed_break'],
            'tt_average_play_time_per_play' => ['SUM(data_log.total_play * data_log.average_play_time_per_play) as sum_average_play_time_per_play', 'SUM(data_log.total_play) as tt_total_play'],
            'tt_play_over' => ['SUM(data_log.play_over) as tt_play_over'],
            'tt_play_duration_sum' => ['SUM(data_log.play_duration_sum) as tt_play_duration_sum'],
            'tt_wifi_play' => ['SUM(data_log.wifi_play) as tt_wifi_play'],
            //3s播放数
            'tt_play_duration_3s' => ['SUM(data_log.play_duration_3s) as tt_play_duration_3s'],
            //3s播放率
            'tt_play_duration_3s_rate' => ['SUM(data_log.play_duration_3s) as tt_play_duration_3s', 'SUM(data_log.total_play) as tt_total_play'],
            'tt_wifi_play_rate' => ['SUM(data_log.total_play * (data_log.wifi_play_rate / 100)) as tt_wifi_play_num', 'SUM(data_log.total_play) as tt_total_play'],
            'tt_valid_play' => ['SUM(data_log.valid_play) as tt_valid_play'],
            'tt_valid_play_cost' => ['SUM(data_log.valid_play) as tt_valid_play', 'SUM(data_log.ori_cost) as ori_cost'],
            'tt_valid_play_rate' => ['SUM(data_log.valid_play) as tt_valid_play', 'SUM(data_log.total_play) as tt_total_play'],
            'tt_play_over_rate' => ['SUM(data_log.play_100_feed_break) as tt_play_100_feed_break', 'SUM(data_log.total_play) as tt_total_play'],
            'tt_total_play' => ['SUM(data_log.total_play) as tt_total_play'],
            'tt_location_click' => ['SUM(data_log.location_click) as tt_location_click'],
            'tt_comment' => ['SUM(data_log.dy_comment) as tt_comment'],
            'tt_share' => ['SUM(data_log.dy_share) as tt_share'],
            'tt_follow' => ['SUM(data_log.follow) as tt_follow'],
            'tt_home_visited' => ['SUM(data_log.home_visited) as tt_home_visited'],
            'tt_like' => ['SUM(data_log.`dy_like`) as tt_like'],
            'tt_dislike_cnt' => ['SUM(data_log.`dislike_cnt`) as tt_dislike_cnt'],
            'tt_report_cnt' => ['SUM(data_log.`report_cnt`) as tt_report_cnt'],
            'tt_ies_music_click' => ['SUM(data_log.ies_music_click) as tt_ies_music_click'],
            'tt_ies_challenge_click' => ['SUM(data_log.ies_challenge_click) as tt_ies_challenge_click'],

            'count_ad2_deliveried' => ['data_log.ad_id as data_log_ad_id', 'SUM(data_log.cost) as cost'],
            'count_cost_date' => ['COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, TRUE, null) ) as count_cost_date'],

            'cost_date' => ['data_log.cost_date'],
            'cost_month' => ["DATE_FORMAT(data_log.cost_date,'%Y-%m') as cost_month"],
            //'count_cost_date' => ['cost_date', 'SUM(cost) as cost'],
            'cost' => ['SUM(data_log.cost) as cost'],
            'cost_per_reg' => ['SUM(data_log.cost) as cost'],
            'cost_process' => ["SUM(if(cost_date = CURRENT_DATE,data_log.ori_cost,0)) as sum_ori_cost_for_cost_process"],
            'cpc' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click'],
            'click' => ['SUM(data_log.click) as click'],
            'cpm' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`show`) as `show`'],
            'show' => ['SUM(data_log.`show`) as `show`'],
            'ori_cost' => ['SUM(data_log.ori_cost) as ori_cost'],
            'click_rate' => ['SUM(data_log.click) as click', 'SUM(data_log.`show`) as `show`'],
            'cost_per_convert' => ['SUM(data_log.convert) as convert', 'SUM(data_log.ori_cost) as ori_cost'],
            'convert' => ['SUM(data_log.convert) as convert'],
            'cost_per_active' => ['SUM(active) as active_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'cost_per_pay' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count'],
            'pay_count' => ['SUM(data_log.pay_count) as pay_count'],
            'reg_rate' => ['SUM(register) as reg_count', 'SUM(data_log.click) as click'],
            'active_rate' => ['SUM(active) as active_count', 'SUM(data_log.click) as click'],
            'active_count' => ['SUM(active) as active_count'],
            'convert_rate' => ['SUM(data_log.convert) as convert', 'SUM(data_log.click) as click'],
            'reg_count' => ['SUM(register) as reg_count'],
            'media_cost_per_reg' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(register) as reg_count'],
            'pay_rate' => ['SUM(data_log.pay_count) as pay_count', 'SUM(active) as active_count'],

            'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost'],
            'cost_key_role_level_count' => ['SUM(data_log.cost) as cost'],
            'first_day_roi' => ['SUM(data_log.cost) as cost'],
            'total_roi' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_2' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_3' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_7' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_15' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_30' => ['SUM(data_log.cost) as cost'],
            'first_day_roi_standard_value' => [
                "sum(cost*day_1_standard_value) AS 1_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_2_standard_value' => [
                "sum(cost*day_2_standard_value) AS 2_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_3_standard_value' => [
                "sum(cost*day_3_standard_value) AS 3_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_7_standard_value' => [
                "sum(cost*day_7_standard_value) AS 7_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_15_standard_value' => [
                "sum(cost*day_15_standard_value) AS 15_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_30_standard_value' => [
                "sum(cost*day_30_standard_value) AS 30_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
        ],

        MediaType::TENCENT => [
            //10%进度播放量
            'gdt_video_outer_play10_count' => ['SUM(data_log.video_outer_play10_count) as gdt_video_outer_play10_count'],
            //25%进度播放量
            'gdt_video_outer_play25_count' => ['SUM(data_log.video_outer_play25_count) as gdt_video_outer_play25_count'],
            //50%进度播放量
            'gdt_video_outer_play50_count' => ['SUM(data_log.video_outer_play50_count) as gdt_video_outer_play50_count'],
            //75%进度播放量
            'gdt_video_outer_play75_count' => ['SUM(data_log.video_outer_play75_count) as gdt_video_outer_play75_count'],
            //95%进度播放量
            'gdt_video_outer_play95_count' => ['SUM(data_log.video_outer_play95_count) as gdt_video_outer_play95_count'],
            //100%进度播放量
            'gdt_video_outer_play100_count' => ['SUM(data_log.video_outer_play100_count) as gdt_video_outer_play100_count'],
            //平均有效播放时长
            'gdt_video_outer_play_time_count' => [
                'SUM(data_log.video_outer_play_count * data_log.video_outer_play_time_count) as gdt_video_outer_total_play_time_count',
                'SUM(data_log.video_outer_play_count) as gdt_video_outer_play_count'
            ],
            //平均有效播放进度
            'gdt_video_outer_play_time_avg_rate' => [
                'SUM(data_log.video_outer_play_count * data_log.video_outer_play_time_count) as gdt_video_outer_total_play_time_count',
                //视频有效播放量*视频时长=视频播放总时长
                'SUM((data_log.video_outer_play_count * data_log.video_outer_play_time_count ) / video_outer_play_time_avg_rate ) as gdt_video_total_play_time_count'
            ],
            //有效播放率
            'gdt_video_outer_play_rate' => [
                'SUM(data_log.video_outer_play_count) as gdt_video_outer_play_count',
                'SUM(data_log.`view_count`) as `show`'
            ],
            //有效播放成本
            'gdt_video_outer_play_cost' => [
                'SUM(data_log.ori_cost) as ori_cost',
                'SUM(data_log.video_outer_play_count) as gdt_video_outer_play_count'
            ],
            //3s播放完成量
            'gdt_video_outer_play3s_count' => ['SUM(data_log.video_outer_play3s_count) as gdt_video_outer_play3s_count'],
            //5s播放完成量
            'gdt_video_outer_play5s_count' => ['SUM(data_log.video_outer_play5s_count) as gdt_video_outer_play5s_count'],
            //7s播放完成量
            'gdt_video_outer_play7s_count' => ['SUM(data_log.video_outer_play7s_count) as gdt_video_outer_play7s_count'],
            //一阶段花费(微信)
            'gdt_wechat_cost_stage1' => ['SUM(data_log.wechat_cost_stage1) as gdt_wechat_cost_stage1'],
            //二阶段花费(微信)
            'gdt_wechat_cost_stage2' => ['SUM(data_log.wechat_cost_stage2) as gdt_wechat_cost_stage2'],
            //有效消耗
            'standard_reached_cost' => ['SUM(data_log.cost) as cost'],
            //无效消耗
            'standard_unreached_cost' => ['SUM(data_log.cost) as cost'],
            //首日付费次数成本
            'cost_first_day_pay_times' => ['SUM(data_log.cost) as cost'],
            //首日付费次数成本
            'cost_total_pay_times' => ['SUM(data_log.cost) as cost'],
            //次留成本
            'cost_day_second_login' => ['SUM(data_log.cost) as cost'],

            //资源位细分
            'gdt_inventory_subdivision' => ['data_log.site_set as gdt_inventory_subdivision'],
            'gdt_xqxs_inventory_subdivision' => ["CASE WHEN ( data_log.site_set = 'SITE_SET_KANDIAN' ) THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_QQ_MUSIC_GAME' ) THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_VIDEO' ) THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_NEWS' ) THEN 'SITE_SET_XQXS' ELSE data_log.site_set 
        END AS gdt_xqxs_inventory_subdivision"],
            //自动扩量细分
            'gdt_is_expand_targeting' => ['data_log.is_expand_targeting as gdt_is_expand_targeting'],
            'cost_date' => ['data_log.cost_date'],
            'cost_month' => ["DATE_FORMAT(data_log.cost_date,'%Y-%m') as cost_month"],
            'count_ad2_deliveried' => ['data_log.adgroup_id as data_log_ad2_id', 'SUM(data_log.cost) as cost'],
//         'count_cost_date' => ['cost_date',  'SUM(ori_cost) as ori_cost'],
            'count_cost_date' => ['COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, 1, null) ) 
         as count_cost_date'],
            'cost' => ['SUM(data_log.cost) as cost'],
            'cost_per_reg' => ['SUM(data_log.cost) as cost'],
            'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost'],
            'cost_key_role_level_count' => ['SUM(data_log.cost) as cost'],
            'first_day_roi' => [ 'SUM(data_log.cost) as cost'],
            'total_roi' => ['SUM(data_log.cost) as cost'],
            'cost_process' =>
                ["SUM(if(data_log.cost_date = CURRENT_DATE,data_log.ori_cost,0)) as sum_ori_cost_for_cost_process"],
            'cpc' => ['SUM(data_log.valid_click_count) as click', 'SUM(data_log.ori_cost) as ori_cost'],
            'click' => ['SUM(data_log.valid_click_count) as click'],
            'cpm' => ['SUM(data_log.`view_count`) as `show`', 'SUM(data_log.ori_cost) as ori_cost'],
            'show' => ['SUM(data_log.`view_count`) as `show`'],
            'ori_cost' => ['SUM(data_log.ori_cost) as ori_cost'],
            'click_rate' => ['SUM(data_log.valid_click_count) as click', 'SUM(data_log.`view_count`) as `show`'],
            'cost_per_convert' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`conversions_count`) as `convert`'],
            'convert' => ['SUM(data_log.`conversions_count`) as `convert`'],
            'cost_per_active' => ['SUM(data_log.activated_count) as active_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'cost_per_pay' => ['SUM(data_log.purchase_pv) as pay_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'pay_count' => ['SUM(data_log.purchase_pv) as pay_count'],
            'reg_rate' => ['SUM(data_log.app_register_count) as reg_count', 'SUM(data_log.valid_click_count) as click'],
            'active_rate' => ['SUM(data_log.activated_count) as active_count', 'SUM(data_log.valid_click_count) as click'],
            'active_count' => ['SUM(data_log.activated_count) as active_count'],
            'convert_rate' => ['SUM(data_log.`conversions_count`) as `convert`', 'SUM(data_log.valid_click_count) as click'],
            'reg_count' => ['SUM(data_log.app_register_count) as reg_count'],
            'media_cost_per_reg' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.app_register_count) as reg_count'],
            'pay_rate' => ['SUM(data_log.purchase_pv) as pay_count', 'SUM(data_log.activated_count) as active_count'],
            //二回
            'rate_day_roi_2' => ['SUM(data_log.cost) as cost'],
            //三回
            'rate_day_roi_3' => ['SUM(data_log.cost) as cost'],
            //七回
            'rate_day_roi_7' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_15' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_30' => ['SUM(data_log.cost) as cost'],
            'first_day_roi_standard_value' => [
                "SUM(cost*day_1_standard_value) AS 1_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_2_standard_value' => [
                "SUM(cost*day_2_standard_value) AS 2_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_3_standard_value' => [
                "SUM(cost*day_3_standard_value) AS 3_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_7_standard_value' => [
                "SUM(cost*day_7_standard_value) AS 7_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_15_standard_value' => [
                "SUM(cost*day_15_standard_value) AS 15_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_30_standard_value' => [
                "SUM(cost*day_30_standard_value) AS 30_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
        ],

        MediaType::KUAISHOU => [
            //2s播放率
            'ks_ad_photo_played_2s_ratio' => [
                'SUM(data_log.ad_photo_played_2s_ratio * data_log.aclick) as play_2s_num',
                'SUM(data_log.aclick) as ks_aclick'
            ],
            //3s播放率
            'ks_play_3s_ratio' => [
                'SUM(data_log.play_3s_ratio * data_log.aclick) as play_3s_num',
                'SUM(data_log.aclick) as ks_aclick'
            ],
            //5s播放率
            'ks_play_5s_ratio' => [
                'SUM(data_log.play_5s_ratio * data_log.aclick) as play_5s_num',
                'SUM(data_log.aclick) as ks_aclick'
            ],
            //10s播放率
            'ks_ad_photo_played_10s_ratio' => [
                'SUM(data_log.ad_photo_played_10s_ratio * data_log.aclick) as play_10s_num',
                'SUM(data_log.aclick) as ks_aclick'
            ],
            //完播率
            'ks_play_end_ratio' => [
                'SUM(data_log.play_end_ratio * data_log.aclick) as play_end_num',
                'SUM(data_log.aclick) as ks_aclick'
            ],
            //75%进度播放率
            'ks_ad_photo_played_75percent_ratio' => [
                'SUM(data_log.ad_photo_played_75percent_ratio * data_log.aclick) as played_75percent_num',
                'SUM(data_log.aclick) as ks_aclick'
            ],

            //有效消耗
            'standard_reached_cost' => ['SUM(data_log.cost) as cost'],
            //无效消耗
            'standard_unreached_cost' => ['SUM(data_log.cost) as cost'],
            //首日付费次数成本
            'cost_first_day_pay_times' => ['SUM(data_log.cost) as cost'],
            //首日付费次数成本
            'cost_total_pay_times' => ['SUM(data_log.cost) as cost'],
            //次留成本
            'cost_day_second_login' => ['SUM(data_log.cost) as cost'],

            'cost_date' => ['data_log.cost_date'],
            'cost_month' => ["DATE_FORMAT(data_log.cost_date,'%Y-%m') as cost_month"],
            'count_ad2_deliveried' => ['data_log.ad_id as data_log_ad2_id', 'SUM(data_log.cost) as cost'],
//         'count_cost_date' => ['cost_date',  'SUM(ori_cost) as ori_cost'],
            'count_cost_date' => ['COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, 1, null) ) 
         as count_cost_date'],
            'cost' => ['SUM(data_log.cost) as cost'],
            'cost_per_reg' => ['SUM(data_log.cost) as cost'],
            'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost'],
            'cost_key_role_level_count' => ['SUM(data_log.cost) as cost'],
            'first_day_roi' => [ 'SUM(data_log.cost) as cost'],
            'total_roi' => ['SUM(data_log.cost) as cost'],
            'cost_process' =>
                ["SUM(if(data_log.cost_date = CURRENT_DATE,data_log.ori_cost,0)) as sum_ori_cost_for_cost_process"],
            'cpc' => ['SUM(data_log.photo_click) as click', 'SUM(data_log.ori_cost) as ori_cost'],
            'click' => ['SUM(data_log.photo_click) as click'],
            'cpm' => ['SUM(data_log.`show`) as `show`', 'SUM(data_log.ori_cost) as ori_cost'],
            'show' => ['SUM(data_log.`show`) as `show`'],
            'ori_cost' => ['SUM(data_log.ori_cost) as ori_cost'],
            'click_rate' => ['SUM(data_log.photo_click) as click', 'SUM(data_log.`show`) as `show`'],
            'cost_per_convert' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`bclick`) as `convert`'],
            'convert' => ['SUM(data_log.`bclick`) as `convert`'],
            'cost_per_active' => ['SUM(data_log.activation) as active_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'cost_per_pay' => ['SUM(data_log.event_order_paid) as pay_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'pay_count' => ['SUM(data_log.event_order_paid) as pay_count'],
            'reg_rate' => ['SUM(data_log.event_register) as reg_count', 'SUM(data_log.photo_click) as click'],
            'active_rate' => ['SUM(data_log.activation) as active_count', 'SUM(data_log.photo_click) as click'],
            'active_count' => ['SUM(data_log.activation) as active_count'],
            'convert_rate' => ['SUM(data_log.`bclick`) as `convert`', 'SUM(data_log.photo_click) as click'],
            'reg_count' => ['SUM(data_log.event_register) as reg_count'],
            'media_cost_per_reg' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.event_register) as reg_count'],
            'pay_rate' => ['SUM(data_log.event_order_paid) as pay_count', 'SUM(data_log.activation) as active_count'],
            //二回
            'rate_day_roi_2' => ['SUM(data_log.cost) as cost'],
            //三回
            'rate_day_roi_3' => ['SUM(data_log.cost) as cost'],
            //七回
            'rate_day_roi_7' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_15' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_30' => ['SUM(data_log.cost) as cost'],
            'first_day_roi_standard_value' => [
                "SUM(cost*day_1_standard_value) AS 1_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_2_standard_value' => [
                "SUM(cost*day_2_standard_value) AS 2_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_3_standard_value' => [
                "SUM(cost*day_3_standard_value) AS 3_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_7_standard_value' => [
                "SUM(cost*day_7_standard_value) AS 7_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_15_standard_value' => [
                "SUM(cost*day_15_standard_value) AS 15_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_30_standard_value' => [
                "SUM(cost*day_30_standard_value) AS 30_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
        ]
    ];

    const ACCOUNT_LOG = [
        0 => [
            'account_name' => ['account_name'],
        ],
        MediaType::TOUTIAO => [
            'account_name' => ['account_name'],
        ],
        MediaType::TENCENT => [
            'account_name' => ['account_name'],
        ],
        MediaType::KUAISHOU => [
            'account_name' => ['account_name'],
        ],
        MediaType::BAIDU => [
            'account_name' => ['account_name'],
        ],
    ];

    const FIRST_AD_LOG = [
        0 => [
            'ad1_id' => ['ad1_id as ad1_id'],
            'ad1_name' => ['ad1_name as ad1_name'],
        ],
        MediaType::TOUTIAO => [
            'ad1_id' => ['campaign_id as ad1_id'],
            'ad1_name' => ['campaign_name as ad1_name'],
        ],
        MediaType::TENCENT => [
            'ad1_id' => ['campaign_id as ad1_id'],
            'ad1_name' => ['campaign_name as ad1_name'],
        ],
        MediaType::KUAISHOU => [
            'ad1_id' => ['campaign_id as ad1_id'],
            'ad1_name' => ['campaign_name as ad1_name'],
        ],
        MediaType::BAIDU => [
            'ad1_id' => ['campaign_id as ad1_id'],
            'ad1_name' => ['campaign_name as ad1_name'],
        ],
    ];

    const THIRD_AD_LOG = [
        0 => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            'media_type' => ['third_ad_log.media_type'],
            'platform' => ['third_ad_log.platform'],
            'account_id' => ['third_ad_log.account_id', 'account_log.account_name'],
            'account_name' => ['account_log.account_name', 'third_ad_log.account_id'],
            'ad1_id' => ['first_ad_log.ad1_id as ad1_id'],
            'ad1_name' => ['first_ad_log.ad1_name as ad1_name'],
            'ad2_id' => ['third_ad_log.ad2_id'],
            'ad2_name' => ['ad_log.ad2_name'],

            'ad3_id' => ['third_ad_log.ad3_id as ad3_id'],
            'ad3_name' => ['third_ad_log.title'],
            //计数项数据
            'count' => ['ad_log.web_creator', 'third_ad_log.ad3_id as ad3_id'],
            'count_ad2' => ['ad_log.ad2_id as count_ad2', 'third_ad_log.ad3_id as ad3_id'],
            'count_ad2_delivering' => ["ad_log.ad2_id as count_ad2", "ad_log.status", 'third_ad_log.ad3_id as ad3_id'],
            'count_ad2_undeliveried' => ["ad_log.ad2_id as count_ad2",  "ad_log.is_cost", 'third_ad_log.ad3_id as ad3_id'],
            //'count_creative' => ['third_ad_log.ad3_id'],
            'count_creative' => ['COUNT( DISTINCT third_ad_log.ad3_id ) as count_creative'],
            'count_ad2_deliveried' => ['third_ad_log.ad3_id as ad3_id'],
            'urls' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature", 'forth_ad_log.media_material_type as file_type'],
            'file_type' => ['forth_ad_log.media_material_type as file_type'],
            'playable_url' => ['third_ad_log.playable_url'],

            'os' => ["game.os as os"],
            'create_type' => ['ad_log.create_type as create_type'],
            'game_id' => ['game.game_id as game_id'],
            'game_name' => ['game.game_name as game_name'],
            'main_game_id' => ['game.main_game_id as main_game_id'],
            'main_game_name' => ['game.main_game_name as main_game_name'],
            'root_game_id' => ['game.root_game_id as root_game_id'],
            'root_game_name' => ['game.root_game_name as root_game_name'],
            'site_id' => ['ad_log.site_id as site_id'],
            'agent_leader' => ['agent_site.agent_leader as agent_leader'],
            'agent_leader_group_name' => ["IFNULL( alg.agent_leader_group_name, '未分组' ) AS agent_leader_group_name"],
            'agent_id' => ['agent_site.agent_id as agent_id', 'agent_site.agent_name as agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id as agent_group_id', 'agent_site.agent_group_name as agent_group_name'],

            'cost_process' => ["SUM(if(ad_log.opt_status = 'AD_STATUS_ENABLE',ad_log.budget,0)) as sum_budget_for_cost_process"],
            'item_id' => ['third_ad_log.item_id'],
            'aweme_title' => ['third_ad_log.item_id', 'third_ad_log.title as title'],
            'aweme_account' => ['third_ad_log.item_id'],
            'aweme_name' => ['third_ad_log.item_id'],
            'interface_person' => ['third_ad_log.item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id'],
            'intermediary_person' => ['third_ad_log.item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id'],
            'talent_account' => ['third_ad_log.talent_account'],
            'talent_interface_person' => ['third_ad_log.talent_account'],
        ],

        MediaType::TOUTIAO => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            'media_type' => [MediaType::TOUTIAO. ' as media_type'],
            //头条素材ID
            'tt_material_id' => ['third_ad_log.material_id as tt_material_id'],
            'tt_materials_type' => ['ad_log.materials_type as tt_materials_type'],
            'platform' => ['third_ad_log.platform'],
            'account_id' => ['third_ad_log.account_id', 'account_log.account_name'],
            'account_name' => ['account_log.account_name', 'third_ad_log.account_id'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as campaign_name'],
            'ad2_id' => ['ad_log.ad_id as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name'],

            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.ad3_name as ad3_name'],
            //计数项数据
            'count' => ['ad_log.web_creator', 'third_ad_log.creative_id as ad3_id'],
            'count_ad2' => ['ad_log.ad_id as count_ad2', 'third_ad_log.creative_id as ad3_id'],
            'count_ad2_delivering' => ["ad_log.ad_id as count_ad2", "ad_log.status", 'third_ad_log.creative_id as ad3_id'],
            'count_ad2_undeliveried' => ["ad_log.ad_id as count_ad2", "ad_log.is_cost", 'third_ad_log.creative_id as ad3_id'],
            'count_creative' => ['COUNT( DISTINCT third_ad_log.creative_id ) as count_creative'],
            'count_ad2_deliveried' => ['third_ad_log.creative_id as ad3_id'],
            'urls' => ["if(ad_log.signature != '',ad_log.signature,third_ad_log.signature ) AS signature", 'third_ad_log.file_type'],
            'file_type' => ['third_ad_log.file_type'],

            'os' => ["game.os as os"],
            'create_type' => ['ad_log.create_type as create_type'],
            'game_id' => ['game.game_id as game_id'],
            'game_name' => ['game.game_name as game_name'],
            'main_game_id' => ['game.main_game_id as main_game_id'],
            'main_game_name' => ['game.main_game_name as main_game_name'],
            'root_game_id' => ['game.root_game_id as root_game_id'],
            'root_game_name' => ['game.root_game_name as root_game_name'],
            'site_id' => ['ad_log.site_id as site_id'],
            'agent_leader' => ['agent_site.agent_leader as agent_leader'],
            'agent_leader_group_name' => ["IFNULL( alg.agent_leader_group_name, '未分组' ) AS agent_leader_group_name"],
            'agent_id' => ['agent_site.agent_id as agent_id', 'agent_site.agent_name as agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id as agent_group_id', 'agent_site.agent_group_name as agent_group_name'],

            'cost_process' => ["SUM(if(ad_log.opt_status = 'AD_STATUS_ENABLE',ad_log.budget,0)) as sum_budget_for_cost_process"],
            'item_id' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_title' => ['third_ad_log.aweme_item_id as item_id', 'third_ad_log.title as title'],
            'aweme_account' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_name' => ['third_ad_log.aweme_item_id as item_id'],
            'interface_person' => ['third_ad_log.aweme_item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.aweme_item_id as item_id'],
            'intermediary_person' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.aweme_item_id as item_id'],
            //重点加热素材
            'tt_three_day_cost_five_standard' => ['third_ad_log.aweme_item_id as item_id'],
            //重点素材首日达标率是否大于50%
            'tt_first_day_roi_standard' => ['third_ad_log.aweme_item_id as item_id'],
            'talent_account' => ['third_ad_log.ies_core_user_id as talent_account'],
            'talent_interface_person' => ['third_ad_log.ies_core_user_id as talent_account'],
            //是否低效素材
            'is_inefficient' => ['toutiao_inefficient_material_log.is_inefficient'],
            //是否同质化挤压严重素材
            'tt_is_similar_material' => ['toutiao_inefficient_material_log.is_similar_material as tt_is_similar_material'],
            //是否AD优质素材
            'tt_is_ad_high_quality' => ['toutiao_inefficient_material_log.is_ad_high_quality as tt_is_ad_high_quality'],
            //是否首发素材
            'tt_is_first_publish_material' => ['toutiao_inefficient_material_log.is_first_publish_material as tt_is_first_publish_material'],
            //是否AD低质素材
            'tt_is_ad_low_quality_material' => ['toutiao_inefficient_material_log.is_ad_low_quality_material as tt_is_ad_low_quality_material'],
            //是否千川低质素材
            'tt_is_ecp_low_quality_material' => ['toutiao_inefficient_material_log.is_ecp_low_quality_material as tt_is_ecp_low_quality_material'],
            //AD素材低质原因
            'tt_message_ad_low_quality_material' => ['toutiao_inefficient_material_log.message_ad_low_quality_material as tt_message_ad_low_quality_material'],
            //千川素材低质原因
            'tt_message_ecp_low_quality_material' => ['toutiao_inefficient_material_log.message_ecp_low_quality_material as tt_message_ecp_low_quality_material'],
            //头条2.0广告审核记录
            'tt_material_reject_reason' => ['toutiao_promotion_reject_reason_log.material_reject_reason as tt_material_reject_reason'],
            'tt_material_suggestion' => ['toutiao_promotion_reject_reason_log.material_suggestion as tt_material_suggestion'],
        ],

        MediaType::TENCENT => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.site_set as inventory_type'],
            'media_type' => [MediaType::TENCENT . ' as media_type'],
            'platform' => ['third_ad_log.platform'],
            'account_id' => ['third_ad_log.account_id', 'account_log.account_name'],
            'account_name' => ['account_log.account_name', 'third_ad_log.account_id'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as campaign_name'],
            'ad2_id' => ['ad_log.adgroup_id as ad2_id'],
            'ad2_name' => ['ad_log.adgroup_name as ad2_name'],

            'ad3_id' => ['third_ad_log.ad_id as ad3_id'],
            'ad3_name' => ['third_ad_log.ad_name as ad3_name'],
            'gdt_inventory_subdivision' => ['data_log.site_set AS gdt_inventory_subdivision'],
            'gdt_xqxs_inventory_subdivision' => ["CASE 
      WHEN ( data_log.site_set = 'SITE_SET_KANDIAN') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_QQ_MUSIC_GAME') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_VIDEO') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_NEWS') THEN 'SITE_SET_XQXS' 
      ELSE data_log.site_set END AS gdt_xqxs_inventory_subdivision"],
            //计数项数据
            'count' => ['ad_log.web_creator', 'third_ad_log.ad_id as ad3_id'],
            'count_ad2' => ['ad_log.adgroup_id as count_ad2', 'third_ad_log.ad_id as ad3_id'],
            'count_ad2_delivering' => ["ad_log.adgroup_id as count_ad2", "ad_log.system_status", 'third_ad_log.ad_id as ad3_id'],
            'count_ad2_undeliveried' => ["ad_log.adgroup_id as count_ad2", "ad_log.is_cost", 'third_ad_log.ad_id as ad3_id'],
            'count_creative' => ['COUNT( DISTINCT third_ad_log.ad_id ) as count_creative'],
            'count_ad2_deliveried' => ['third_ad_log.ad_id as ad3_id'],
            'urls' => ["forth_ad_log.signature AS signature", 'third_ad_log.file_type'],
            'file_type' => ['third_ad_log.file_type'],

            'os' => ["game.os as os"],
            'create_type' => ['ad_log.create_type as create_type'],
            'game_id' => ['game.game_id as game_id'],
            'game_name' => ['game.game_name as game_name'],
            'main_game_id' => ['game.main_game_id as main_game_id'],
            'main_game_name' => ['game.main_game_name as main_game_name'],
            'root_game_id' => ['game.root_game_id as root_game_id'],
            'root_game_name' => ['game.root_game_name as root_game_name'],
            'site_id' => ['ad_log.site_id as site_id'],
            'agent_leader' => ['agent_site.agent_leader as agent_leader'],
            'agent_leader_group_name' => ["IFNULL( alg.agent_leader_group_name, '未分组' ) AS agent_leader_group_name"],
            'agent_id' => ['agent_site.agent_id as agent_id', 'agent_site.agent_name as agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id as agent_group_id', 'agent_site.agent_group_name as agent_group_name'],

            'cost_process' => ["SUM(if(ad_log.opt_status = 'AD_STATUS_ENABLE',ad_log.budget,0)) as sum_budget_for_cost_process"],
            'item_id' => ['third_ad_log.item_id as item_id'],
            'aweme_title' => ['third_ad_log.item_id as item_id', 'third_ad_log.title as title'],
            'aweme_account' => ['third_ad_log.item_id as item_id'],
            'aweme_name' => ['third_ad_log.item_id as item_id'],
            'interface_person' => ['third_ad_log.item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id as item_id'],
            'intermediary_person' => ['third_ad_log.item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id as item_id'],
            'talent_account' => ['"" as talent_account'],
            'talent_interface_person' => ['"" as talent_account'],
        ],

        MediaType::KUAISHOU => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.scene_id as inventory_type'],
            'media_type' => [MediaType::KUAISHOU . ' as media_type'],
            'platform' => ['third_ad_log.platform'],
            'account_id' => ['third_ad_log.account_id', 'account_log.user_name as account_name'],
            'account_name' => ['account_log.user_name as account_name', 'third_ad_log.account_id'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as campaign_name'],
            'ad2_id' => ['ad_log.ad_id as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name'],

            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.sticker_title as ad3_name'],
            //计数项数据
            'count' => ['ad_log.web_creator', 'third_ad_log.creative_id as ad3_id'],
            'count_ad2' => ['ad_log.ad_id as count_ad2', 'third_ad_log.creative_id as ad3_id'],
            'count_ad2_delivering' => ["ad_log.ad_id as count_ad2", "ad_log.ad_status", 'third_ad_log.creative_id as ad3_id'],
            'count_ad2_undeliveried' => ["ad_log.ad_id as count_ad2", "ad_log.is_cost", 'third_ad_log.creative_id as ad3_id'],
            'count_creative' => ['COUNT( DISTINCT third_ad_log.creative_id ) as count_creative'],
            'count_ad2_deliveried' => ['third_ad_log.creative_id as ad3_id'],
            'urls' => ["third_ad_log.signature AS signature", 'third_ad_log.file_type'],
            'file_type' => ['third_ad_log.file_type'],

            'os' => ["game.os as os"],
            'create_type' => ['ad_log.create_type as create_type'],
            'game_id' => ['game.game_id as game_id'],
            'game_name' => ['game.game_name as game_name'],
            'main_game_id' => ['game.main_game_id as main_game_id'],
            'main_game_name' => ['game.main_game_name as main_game_name'],
            'root_game_id' => ['game.root_game_id as root_game_id'],
            'root_game_name' => ['game.root_game_name as root_game_name'],
            'site_id' => ['ad_log.site_id as site_id'],
            'agent_leader' => ['agent_site.agent_leader as agent_leader'],
            'agent_leader_group_name' => ["IFNULL( alg.agent_leader_group_name, '未分组' ) AS agent_leader_group_name"],
            'agent_id' => ['agent_site.agent_id as agent_id', 'agent_site.agent_name as agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id as agent_group_id', 'agent_site.agent_group_name as agent_group_name'],

            'cost_process' => ["SUM(if(ad_log.ad_put_status = 1',ad_log.ad_day_budget,0)) as sum_budget_for_cost_process"],
            'item_id' => ['third_ad_log.item_id as item_id'],
            'aweme_title' => ['third_ad_log.item_id as item_id', 'third_ad_log.sticker_title as title'],
            'aweme_account' => ['third_ad_log.item_id as item_id'],
            'aweme_name' => ['third_ad_log.item_id as item_id'],
            'interface_person' => ['third_ad_log.item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id as item_id'],
            'intermediary_person' => ['third_ad_log.item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id as item_id'],
            'talent_account' => ['"" as talent_account'],
            'talent_interface_person' => ['"" as talent_account'],
        ],

        MediaType::BAIDU => [
            'port_version' => ['ad_log.port_version as port_version'],
            'media_type' => [MediaType::BAIDU . ' as media_type'],
            'inventory_type' => ['ad_log.flow_types as inventory_type'],
            'platform' => ['third_ad_log.platform'],
            'account_id' => ['third_ad_log.account_id', 'account_log.account_name'],
            'account_name' => ['account_log.account_name', 'third_ad_log.account_id'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as campaign_name'],
            'ad2_id' => ['ad_log.ad_id as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name'],

            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.title as ad3_name'],
            //计数项数据
            'count' => ['ad_log.web_creator', 'third_ad_log.ad_id as ad3_id'],
            'count_ad2' => ['ad_log.ad_id as count_ad2', 'third_ad_log.ad_id as ad3_id'],
            'count_ad2_delivering' => ["ad_log.ad_id as count_ad2", "ad_log.ad_status", 'third_ad_log.creative_id as ad3_id'],
            'count_ad2_undeliveried' => ["ad_log.ad_id as count_ad2", "ad_log.is_cost", 'third_ad_log.creative_id as ad3_id'],
            'count_creative' => ['COUNT( DISTINCT third_ad_log.creative_id ) as count_creative'],
            'count_ad2_deliveried' => ['third_ad_log.creative_id as ad3_id'],
            'urls' => ["third_ad_log.signature AS signature", 'third_ad_log.file_type'],
            'file_type' => ['third_ad_log.file_type'],

            'os' => ["game.os as os"],
            'create_type' => ['ad_log.create_type as create_type'],
            'game_id' => ['game.game_id as game_id'],
            'game_name' => ['game.game_name as game_name'],
            'main_game_id' => ['game.main_game_id as main_game_id'],
            'main_game_name' => ['game.main_game_name as main_game_name'],
            'root_game_id' => ['game.root_game_id as root_game_id'],
            'root_game_name' => ['game.root_game_name as root_game_name'],
            'site_id' => ['ad_log.site_id as site_id'],
            'agent_leader' => ['agent_site.agent_leader as agent_leader'],
            'agent_leader_group_name' => ["IFNULL( alg.agent_leader_group_name, '未分组' ) AS agent_leader_group_name"],
            'agent_id' => ['agent_site.agent_id as agent_id', 'agent_site.agent_name as agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id as agent_group_id', 'agent_site.agent_group_name as agent_group_name'],

            'cost_process' => ["SUM(if(first_ad_log.campaign_pause = 0, first_ad_log.campaign_budget,0)) as sum_budget_for_cost_process"],
            'item_id' => ['third_ad_log.item_id as item_id'],
            'aweme_title' => ['third_ad_log.item_id as item_id', 'third_ad_log.title as title'],
            'aweme_account' => ['third_ad_log.item_id as item_id'],
            'aweme_name' => ['third_ad_log.item_id as item_id'],
            'interface_person' => ['third_ad_log.item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id as item_id'],
            'intermediary_person' => ['third_ad_log.item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id as item_id'],
            'talent_account' => ['"" as talent_account'],
            'talent_interface_person' => ['"" as talent_account'],
        ]
    ];

    const OVERVIEW_LOG =  [
        // 消耗日期
        'cost_date' => ['overview_log.log_date as cost_date'],
        // 消耗日期
        'cost_month' => ["DATE_FORMAT(overview_log.log_date,'%Y-%m') as cost_month"],
        //实名率
        'true_uid_rate' => [
            'SUM(overview_log.true_uid_count) as true_uid_count',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //实名数
        'true_uid_count' => ['SUM(overview_log.true_uid_count) as true_uid_count'],
        'action_uid_reg_rate' => ['IFNULL(SUM(overview_log.day_action_muid_distinct_count), 0) as action_muid_count',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'reg_muid_count' => ['IFNULL(SUM(overview_log.day_reg_muid_distinct_count), 0 ) as reg_muid_count'],
        'action_muid_count' => ['IFNULL(SUM(overview_log.day_action_muid_distinct_count), 0) as action_muid_count'],
        'first_day_ltv' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'ltv' => ['SUM(overview_log.day_total_pay_money) as total_pay_money', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'first_day_pay_count' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'first_day_pay_rate' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'cost_per_first_day_pay' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'first_day_pay_money' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money'],
        'seventh_day_pay_money' => ['SUM(overview_log.day_seventh_day_pay_money) as seventh_day_pay_money'],
        'fifteenth_day_pay_money' => ['SUM(overview_log.day_fifteenth_day_pay_money) as fifteenth_day_pay_money'],
        'thirty_day_pay_money' => ['SUM(overview_log.day_thirty_day_pay_money) as thirty_day_pay_money'],
        'first_day_arppu' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money', 'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'arppu' => ['SUM(overview_log.day_total_pay_money) as total_pay_money', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'first_day_roi' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money'],

        'reg_uid_count' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'cost_per_reg' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'total_roi' => ['SUM(overview_log.day_total_pay_money) as total_pay_money'],
        'rate_day_roi_2' => ['SUM(overview_log.day_second_day_pay_money) as sum_second_day_pay_money'],
        'rate_day_roi_3' => ['SUM(overview_log.day_third_day_pay_money) as sum_third_day_pay_money'],
        'rate_day_roi_7' => ['SUM(overview_log.day_seventh_day_pay_money) as sum_seventh_day_pay_money'],
        'rate_day_roi_15' => ['SUM(overview_log.day_fifteenth_day_pay_money) as sum_fifteenth_day_pay_money'],
        'rate_day_roi_30' => ['SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'],
        'rate_day_stay_2' => ['SUM(overview_log.day_second_login_count) as sum_day_second_login_count', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'rate_day_stay_3' => ['SUM(overview_log.day_third_login_count) as sum_is_third_login', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'rate_day_stay_7' => ['SUM(overview_log.day_seventh_login_count) as sum_is_seventh_login', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'rate_day_stay_15' => ['SUM(overview_log.day_fifteenth_login_count) as sum_is_fifteenth_login', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],

        'rate_login_stay_2' => ['SUM(overview_log.day_pay_uid_second_day_login_count) as sum_day_pay_uid_second_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_login_stay_3' => ['SUM(overview_log.day_pay_uid_third_day_login_count) as sum_day_pay_uid_third_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_login_stay_7' => ['SUM(overview_log.day_pay_uid_seventh_day_login_count) as sum_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_login_stay_15' => ['SUM(overview_log.day_pay_uid_fifteenth_day_login_count) as sum_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_login_stay_30' => ['SUM(overview_log.day_pay_uid_thirty_day_login_count) as sum_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_pay_login_stay_2' => ['SUM(overview_log.pay_day_pay_uid_second_day_login_count) as sum_pay_day_pay_uid_second_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_pay_login_stay_3' => ['SUM(overview_log.pay_day_pay_uid_third_day_login_count) as sum_pay_day_pay_uid_third_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_pay_login_stay_7' => ['SUM(overview_log.pay_day_pay_uid_seventh_day_login_count) as sum_pay_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_pay_login_stay_15' => ['SUM(overview_log.pay_day_pay_uid_fifteenth_day_login_count) as sum_pay_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_pay_login_stay_30' => ['SUM(overview_log.pay_day_pay_uid_thirty_day_login_count) as sum_pay_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'rate_first_pay_login_stay_2' => ['SUM(overview_log.day_first_day_pay_uid_second_day_login_count) as sum_day_first_day_pay_uid_second_day_login_count', 'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'rate_first_pay_login_stay_3' => ['SUM(overview_log.day_first_day_pay_uid_third_day_login_count) as sum_day_first_day_pay_uid_third_day_login_count', 'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'rate_first_pay_login_stay_7' => ['SUM(overview_log.day_first_day_pay_uid_seventh_day_login_count) as sum_day_first_day_pay_uid_seventh_day_login_count', 'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'rate_first_pay_login_stay_15' => ['SUM(overview_log.day_first_day_pay_uid_fifteenth_day_login_count) as sum_day_first_day_pay_uid_fifteenth_day_login_count', 'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'rate_first_pay_login_stay_30' => ['SUM(overview_log.day_first_day_pay_uid_thirty_day_login_count) as sum_day_first_day_pay_uid_thirty_day_login_count', 'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],

        'first_day_pay_times' => ['SUM(overview_log.day_first_day_pay_times) as first_day_pay_times'],
        'total_pay_times' => ['SUM(overview_log.total_pay_times) as total_pay_times'],
        'first_day_pay_times_cost' => ['SUM(overview_log.day_first_day_pay_times) as first_day_pay_times'],
        'total_pay_times_cost' => ['SUM(overview_log.total_pay_times) as total_pay_times'],
        'click_count' => ['sum(overview_log.click_count) as click_count'],
        'rate_day_stay_2_cost' => ['SUM(overview_log.day_second_login_count) as sum_day_second_login_count'],
        'total_pay_count' => ['SUM(overview_log.day_total_pay_count) as total_pay_count'],
        'total_pay_money' => ['SUM(overview_log.day_total_pay_money) as total_pay_money'],
        'male_count' => ['SUM(overview_log.male_count) as male_count'],
        'female_count' => ['SUM(overview_log.female_count) as female_count'],
        'age_18_24_count' => ['SUM(overview_log.age_18_24_count) as age_18_24_count'],
        'age_25_30_count' => ['SUM(overview_log.age_25_30_count) as age_25_30_count'],
        'age_31_40_count' => ['SUM(overview_log.age_31_40_count) as age_31_40_count'],
        'age_41_50_count' => ['SUM(overview_log.age_41_50_count) as age_41_50_count'],
        'age_over_50_count' => ['SUM(overview_log.age_over_50_count) as age_over_50_count'],
        //关键等级数
        'key_role_level_count' => ['SUM(overview_log.key_role_level_count) as key_role_level_count'],
        //关键等级占比
        'key_role_level_count_rate' => [
            'SUM(overview_log.key_role_level_count) as key_role_level_count',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //关键等级成本
        'cost_key_role_level_count' => ['SUM(overview_log.key_role_level_count) as key_role_level_count'],
        'reg_old_muid_count' => ['SUM(if(overview_log.is_old_root_game_muid  = 1,overview_log.day_reg_uid_count,0)) as reg_old_muid_count'],
        'reg_old_clique_muid_count' => ['SUM(if(overview_log.is_old_clique_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_clique_muid_count'],
        'three_login_uid_count' => ['SUM(overview_log.day_three_login_uid_count) as three_login_uid_count'],
        'reg_old_muid_count_per_reg' => ['SUM(if(overview_log.is_old_root_game_muid  = 1,overview_log.day_reg_uid_count,0)) as reg_old_muid_count', 'SUM(overview_log.day_reg_muid_count) as sum_day_reg_muid_count'],
        'reg_old_clique_muid_count_per_reg' => ['SUM(if(overview_log.is_old_clique_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_clique_muid_count', 'SUM(overview_log.day_reg_muid_count) as sum_day_reg_muid_count'],
        'three_login_uid_count_per_reg' => ['SUM(overview_log.day_three_login_uid_count) as three_login_uid_count', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'new_pay_day_money' => ['SUM( overview_log.day_new_pay_day_money ) as new_pay_day_money'],
        'new_pay_day_count' => ['SUM( overview_log.day_new_pay_day_count ) as new_pay_day_count'],
        //三日付费人数
        'day_third_day_pay_count' => ['SUM(overview_log.day_third_day_pay_count) as day_third_day_pay_count'],
        //七日付费人数
        'day_seventh_day_pay_count' => ['SUM(overview_log.day_seventh_day_pay_count) as day_seventh_day_pay_count'],
        //首充1-6元人数
        'first_day_pay_money_within_6_count' => ['SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count'],

        //首充1-6元首日占比
        'first_day_pay_money_within_6_rate' => [
            'SUM(overview_log.first_day_pay_money_within_6_count) as first_day_pay_money_within_6_count',
            'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'
        ],

        //预估终生付费
        'lifetime_money' => [
            'SUM(overview_log.lifetime_money) as lifetime_money'
        ],
        //预估终生回本
        'lifetime_roi' => [
            'SUM(overview_log.lifetime_money) as lifetime_money'
        ],
        'gdt_inventory_subdivision' => ["CASE WHEN ( overview_log.csite = 100 OR overview_log.csite = 'SITE_SET_KANDIAN' ) THEN 'SITE_SET_KANDIAN' 
      WHEN ( overview_log.csite = 101 OR overview_log.csite = 'SITE_SET_QQ_MUSIC_GAME' ) THEN 'SITE_SET_QQ_MUSIC_GAME' 
      WHEN ( overview_log.csite = 28 OR overview_log.csite = 'SITE_SET_TENCENT_VIDEO' ) THEN 'SITE_SET_TENCENT_VIDEO' 
      WHEN ( overview_log.csite = 25 OR overview_log.csite = 'SITE_SET_MOBILE_INNER' ) THEN 'SITE_SET_MOBILE_INNER' 
      WHEN ( overview_log.csite = 27 OR overview_log.csite = 'SITE_SET_TENCENT_NEWS' ) THEN 'SITE_SET_TENCENT_NEWS' 
      WHEN ( overview_log.csite = 102 OR overview_log.csite = 'SITE_SET_MOMENTS' ) THEN 'SITE_SET_MOMENTS' 
      WHEN ( overview_log.csite = 15 OR overview_log.csite = 'SITE_SET_MOBILE_UNION' ) THEN 'SITE_SET_MOBILE_UNION' 
      WHEN ( overview_log.csite = 21 OR overview_log.csite = 'SITE_SET_WECHAT' ) THEN 'SITE_SET_WECHAT' ELSE overview_log.csite 
    END AS  gdt_inventory_subdivision"],
        'gdt_xqxs_inventory_subdivision' => ["CASE WHEN ( overview_log.csite = 100 OR overview_log.csite = 'SITE_SET_KANDIAN' ) THEN 'SITE_SET_XQXS' 
      WHEN ( overview_log.csite = 101 OR overview_log.csite = 'SITE_SET_QQ_MUSIC_GAME' ) THEN 'SITE_SET_XQXS' 
      WHEN ( overview_log.csite = 28 OR overview_log.csite = 'SITE_SET_TENCENT_VIDEO' ) THEN 'SITE_SET_XQXS' 
      WHEN ( overview_log.csite = 27 OR overview_log.csite = 'SITE_SET_TENCENT_NEWS' ) THEN 'SITE_SET_XQXS' ELSE overview_log.csite 
    END AS gdt_xqxs_inventory_subdivision"],
    ];

    const MATERIAL_LOG = [
        'working_hours' => ['material.working_hours'],
        'urls' => ["REPLACE(url,'.mp4','.wm') as zx_play_url"],
        'material_platform' => ['material.platform as material_platform'],
        'material_name' => ['material.name'],
        'material_id' => ['material_file.material_id'],
        'author' => ['material.author'],
        'theme_id' => ['material.theme_id'],
        'theme_pid' => ['material_theme.theme_pid'],
        'create_time' => ['material.insert_time'],
        'c_author' => ['material.c_author'],
        'a_author' => ['material.a_author'],
        'm1_author' => ['material.m1_author'],
        'm2_author' => ['material.m2_author'],
        'm3_author' => ['material.m3_author'],
        'm4_author' => ['material.m4_author'],
        'm5_author' => ['material.m5_author'],
        'actor' => ['material.actor'],
        'shoot' => ['material.shoot'],
        'material_create_date' => ['material.insert_time'],
        'original' => ['material.original'],
        'effect_grade7' => ['material.effect_grade7'],
        'effect_grade30' => ['material.effect_grade30'],
        'is_priority' => ['material.is_priority'],
        'is_3d' => ['material.is_3d'],
        'is_immortal' => ['material.is_immortal'],
        'label' => ['material_label1.label_id'],

        'material_filename' => ['material_file.filename'],
        'material_file_id' => ['material_file.id'],
        'size' => ["CONCAT(material_file.width, '*', material_file.height) as size"],
    ];
//    const SITE_GAME = [
//        'game_id' => ['game_id']
//    ];
//    const AGENT_SITE = [
//        'agent_leader' => ['agent_leader']
//    ];
//    const GAME = [
//        'game_name' => ['game_name']
//    ];
//    const MATERIAL = [
//        'material_name' => 'name',
//        'material_id' => 'material_id',
//        'author' => 'author',
//        'theme_pid' => 'theme_id',
//        'theme_id' => 'theme_id',
//        'create_time' => 'insert_time',
//        'urls' => 'urls',
//        'c_author' => 'c_author',
//        'a_author' => 'a_author',
//        'm1_author' => 'm1_author',
//        'm2_author' => 'm2_author',
//        'm3_author' => 'm3_author',
//        'm4_author' => 'm4_author',
//        'm5_author' => 'm5_author',
//        'actor' => 'actor',
//        'shoot' => 'shoot',
//        'material_create_date' => 'insert_time'
//    ];
//    const MATERIAL_FILE = [
//        'material_filename' => 'filename',
//        'material_file_id' => 'id',
//        'signature' => 'signature'
//    ];
    const AWEME_LOG = [
        'item_id' => ['ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'aweme_title' => ['aweme_list.title as title', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'aweme_account' => ['ifnull( aweme_list.aweme_id , agent_site.aweme_account) AS aweme_account', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'aweme_name' => ['aweme_list.aweme_name as aweme_name', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'interface_person' => ['agent_site.interface_person as interface_person', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'interface_person_group_name' => ["IFNULL(person_group.interface_person_group_name,'未分组') AS interface_person_group_name", 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'intermediary_person' => ['agent_site.intermediary_person as intermediary_person', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        'aweme_item_publish_time' => ['agent_site.aweme_item_publish_time AS aweme_item_publish_time', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        //重点加热素材
        'tt_three_day_cost_five_standard' => ['toutiao_item_cost.three_day_cost_five_standard as tt_three_day_cost_five_standard', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id'],
        //重点素材首日达标率是否大于50%
        'tt_first_day_roi_standard' => ['toutiao_item_cost.first_day_roi_standard as tt_first_day_roi_standard', 'ifnull(aweme_list.item_id,agent_site.item_id) AS item_id']
        ];

    //
    const AD_LOG_FILTER = [
        0 => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.inventory_type',
            'platform' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'ad2_id' => 'ad_log.ad2_id',
            'ad2_name' => 'ad_log.ad2_name',
            'where操作的字段名' => '数据表真实字段名',
        ],
        MediaType::TOUTIAO => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.inventory_type',
            'tt_materials_type' => 'ad_log.materials_type',
            'platform' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'ad2_id' => 'ad_log.ad_id',
            'ad2_name' => 'ad_log.ad_name',
            'where操作的字段名' => '数据表真实字段名',
        ],
        MediaType::TENCENT => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.site_set',
            'platform' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'ad2_id' => 'ad_log.adgroup_id',
            'ad2_name' => 'ad_log.adgroup_name',
            'where操作的字段名' => '数据表真实字段名',
        ],
        MediaType::KUAISHOU => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.scene_id',
            'platform' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'ad2_id' => 'ad_log.ad_id',
            'ad2_name' => 'ad_log.ad_name',
            'where操作的字段名' => '数据表真实字段名',
        ],
        MediaType::BAIDU => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.flow_types',
            'platform' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'ad2_id' => 'ad_log.ad_id',
            'ad2_name' => 'ad_log.ad_name',
            'where操作的字段名' => '数据表真实字段名',
        ]
    ];

    const DATA_LOG_FILTER = [
        0 => [
            'platform' => 'data_log.platform',
        ],
        MediaType::TOUTIAO => [
            'platform' => 'data_log.platform',
        ],
        MediaType::TENCENT => [
            'platform' => 'data_log.platform',
            'gdt_inventory_subdivision' => 'data_log.site_set',
            'gdt_xqxs_inventory_subdivision' => "data_log.ifnull(CASE WHEN ( data_log.site_set = 'SITE_SET_KANDIAN' ) THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_QQ_MUSIC_GAME' ) THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_VIDEO' ) THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_NEWS' ) THEN 'SITE_SET_XQXS' ELSE data_log.site_set 
        END,0)",
        ],
        MediaType::KUAISHOU => [
            'platform' => 'data_log.platform',
        ],
        MediaType::BAIDU => [
            'platform' => 'data_log.platform',
        ]
    ];
    const FIRST_AD_LOG_FILTER = [
        0 => [
            'ad1_id' => 'first_ad_log.ad1_id',
            'ad1_name' => 'first_ad_log.ad1_name',
            ],
        MediaType::TOUTIAO => [
            'ad1_id' => 'first_ad_log.campaign_id',
            'ad1_name' => 'first_ad_log.campaign_name',
            ],
        MediaType::TENCENT => [
            'ad1_id' => 'first_ad_log.campaign_id',
            'ad1_name' => 'first_ad_log.campaign_name',
            ],
        MediaType::KUAISHOU => [
            'ad1_id' => 'first_ad_log.campaign_id',
            'ad1_name' => 'first_ad_log.campaign_name',
            ],
        MediaType::BAIDU => [
            'ad1_id' => 'first_ad_log.campaign_id',
            'ad1_name' => 'first_ad_log.campaign_name',
            ],
    ];
    const THIRD_AD_LOG_FILTER = [
        0 => [
            'ad3_id' => 'third_ad_log.ad3_id',
            'platform' => 'third_ad_log.platform',
//            'platform-site_id' => 'ad_log.site_id',
            'signature' => 'third_ad_log.signature',
            'file_type' => 'forth_ad_log.media_material_type',
            'talent_account' => 'third_ad_log.talent_account',
            ],
        MediaType::TOUTIAO => [
            'tt_material_id' => 'third_ad_log.material_id',
            'tt_image_mode' => 'image_mode',
            'ad3_id' => 'third_ad_log.creative_id',
            'platform' => 'third_ad_log.platform',
//            'platform-site_id' => 'ad_log.site_id',
            'signature' => 'third_ad_log.signature',
            'file_type' => 'third_ad_log.file_type',
            'talent_account' => 'third_ad_log.ies_core_user_id',
            ],
        MediaType::TENCENT => [
            'ad3_id' => 'third_ad_log.ad_id',
            'platform' => 'third_ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'signature' => 'forth_ad_log.signature',
            'file_type' => 'third_ad_log.media_material_type',
            'gdt_inventory_subdivision' => "data_log.site_set",
            'gdt_xqxs_inventory_subdivision' => "third_ad_log.ifnull(CASE 
      WHEN ( data_log.site_set = 'SITE_SET_KANDIAN') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_QQ_MUSIC_GAME') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_VIDEO') THEN 'SITE_SET_XQXS' 
      WHEN ( data_log.site_set = 'SITE_SET_TENCENT_NEWS') THEN 'SITE_SET_XQXS' 
      ELSE data_log.site_set END,0)",
        ],
        MediaType::KUAISHOU => [
            'ad3_id' => 'third_ad_log.creative_id',
            'platform' => 'third_ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'signature' => 'third_ad_log.signature',
            'file_type' => 'third_ad_log.file_type',
        ],
        MediaType::BAIDU => [
            'ad3_id' => 'third_ad_log.creative_id',
            'platform' => 'third_ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'signature' => 'third_ad_log.signature',
            'file_type' => 'third_ad_log.file_type',
        ]
    ];
    const MAIN_FILTER = [
        0 => [
//            'platform' => 'ad_log.platform',
//            'os' => 'os',
        ],
        MediaType::TOUTIAO => [
//            'platform' => 'ad_log.platform',
//            'platform-site_id' => 'platform-site_id',
//            'os' => 'os',
        ]
    ];
    //
    const OVERVIEW_LOG_FILTER = [
        'platform' => 'overview_log.platform',
        'gdt_inventory_subdivision' => "overview_log.ifnull(CASE WHEN ( overview_log.csite = 100 OR overview_log.csite = 'SITE_SET_KANDIAN' ) THEN 'SITE_SET_KANDIAN' 
      WHEN ( overview_log.csite = 101 OR overview_log.csite = 'SITE_SET_QQ_MUSIC_GAME' ) THEN 'SITE_SET_QQ_MUSIC_GAME' 
      WHEN ( overview_log.csite = 28 OR overview_log.csite = 'SITE_SET_TENCENT_VIDEO' ) THEN 'SITE_SET_TENCENT_VIDEO' 
      WHEN ( overview_log.csite = 25 OR overview_log.csite = 'SITE_SET_MOBILE_INNER' ) THEN 'SITE_SET_MOBILE_INNER' 
      WHEN ( overview_log.csite = 27 OR overview_log.csite = 'SITE_SET_TENCENT_NEWS' ) THEN 'SITE_SET_TENCENT_NEWS' 
      WHEN ( overview_log.csite = 102 OR overview_log.csite = 'SITE_SET_MOMENTS' ) THEN 'SITE_SET_MOMENTS' 
      WHEN ( overview_log.csite = 15 OR overview_log.csite = 'SITE_SET_MOBILE_UNION' ) THEN 'SITE_SET_MOBILE_UNION' 
      WHEN ( overview_log.csite = 21 OR overview_log.csite = 'SITE_SET_WECHAT' ) THEN 'SITE_SET_WECHAT' ELSE overview_log.csite 
    END,0)",
        'gdt_xqxs_inventory_subdivision' => "overview_log.ifnull(CASE WHEN ( overview_log.csite = 100 OR overview_log.csite = 'SITE_SET_KANDIAN' ) THEN 'SITE_SET_XQXS' 
      WHEN ( overview_log.csite = 101 OR overview_log.csite = 'SITE_SET_QQ_MUSIC_GAME' ) THEN 'SITE_SET_XQXS' 
      WHEN ( overview_log.csite = 28 OR overview_log.csite = 'SITE_SET_TENCENT_VIDEO' ) THEN 'SITE_SET_XQXS' 
      WHEN ( overview_log.csite = 27 OR overview_log.csite = 'SITE_SET_TENCENT_NEWS' ) THEN 'SITE_SET_XQXS' ELSE overview_log.csite 
    END,0)",
    ];
//    const REG_LOG_FILTER = [
//        'platform' => 'reg_log.platform',
//    ];
    const SITE_GAME_FILTER = [];
    const AGENT_SITE_FILTER = [
        'agent_leader' => 'agent_site.agent_leader',
        'platform-agent_id' => 'agent_site.agent_id',
        'platform-agent_group_id' => 'agent_site.agent_group_id'];
    const AGENT_LEADER_GROUP_FILTER = ['agent_leader_group_name' => 'alg.agent_leader_group_name'];
    const GAME_FILTER = [
        'os' => 'game.os',
        'platform-game_id' => 'game_id',
        'platform-main_game_id' => 'main_game_id',
        'platform-root_game_id' => 'root_game_id',
    ];
    const ACCOUNT_LOG_FILTER = [
        'account_id' => 'ad_log.account_id',
        'account_name' => 'account_log.account_name'
    ];
    const MATERIAL_FILTER = [
        'working_hours' => 'material.working_hours',
        'material_name' => 'material.name',
        'platform-material_id' => 'material.material_id',
        'platform-theme_id' => 'material.theme_id',
        'platform-theme_pid' => 'material.theme_id',
        'author' => 'material.author',
        'c_author' => 'material.c_author',
        'a_author' => 'material.a_author',
        'm1_author' => 'material.m1_author',
        'm2_author' => 'material.m2_author',
        'm3_author' => 'material.m3_author',
        'm4_author' => 'material.m4_author',
        'm5_author' => 'material.m5_author',
        'actor' => 'material.actor',
        'shoot' => 'material.shoot',
        'material_create_date' => 'material.insert_time',
        //'file_type' => 'material.file_type',
        'original' => 'material.original',
        'effect_grade7' => 'material.effect_grade7',
        'effect_grade30' => 'material.effect_grade30',
        'is_priority' => 'material.is_priority',
        'is_3d' => 'material.is_3d',
        'is_immortal' => 'material.is_immortal',
    ];
    const MATERIAL_FILE_FILTER = [
        'material_platform' => 'material_file.platform',
        'material_filename' => 'material_file.filename',
        'material_file_id' => 'material_file.id',
        //'signature' => 'material_file.signature',
        //'file_type' => 'material_file.file_type',
        'size' => "CONCAT(material_file.width, '*', material_file.height)"
    ];
    const MATERIAL_LABEL_FILTER = [
        'label' => 'material_label.label_id',
        'label_pid' => 'material_label.label_pid'
    ];
    const AWEME_FILTER = [
        'item_id' => 'aweme_list.item_id',
        'aweme_title' => 'aweme_list.title',
        'aweme_account' => 'aweme_list.aweme_id',
        'aweme_name' => 'aweme_list.aweme_name',
        'interface_person' => 'agent_site.interface_person',
        'interface_person_group_name' => 'person_group.interface_person_group_name',
        'intermediary_person' => 'agent_site.intermediary_person',
        'tt_three_day_cost_five_standard' => 'toutiao_item_cost.three_day_cost_five_standard',
        //重点素材首日达标率是否大于50%
        'tt_first_day_roi_standard' => 'toutiao_item_cost.first_day_roi_standard',
    ];


    const AD_CREATE_TIME = [
        0 => 'ad_log.ad2_create_time',
        MediaType::TOUTIAO => 'ad_log.ad_create_time',
        MediaType::TENCENT => 'ad_log.created_time',
        MediaType::KUAISHOU => 'ad_log.ad_create_time',
        MediaType::BAIDU => 'ad_log.ad_create_time',
    ];
    //废弃
    const SECOND_TABLE_ID = [
        0 => ['ad_log.ad2_id'],
        MediaType::TOUTIAO => ['ad_log.ad_id'],
        MediaType::TENCENT => [],
        MediaType::KUAISHOU => [],
        MediaType::BAIDU => [],
    ];
    const ALL_JOIN_FIELDS = [
        0 => ['ad_log.ad2_id', 'ad_log.platform', 'ad_log.account_id', 'ad_log.ad1_id', 'ad_log.media_type'],
        MediaType::TOUTIAO => ['ad_log.ad_id', 'ad_log.platform', 'ad_log.account_id', 'ad_log.campaign_id'],
        MediaType::TENCENT => [],
        MediaType::KUAISHOU => [],
        MediaType::BAIDU => [],
    ];
    //不同广告的连接字段
    const DATA_LOG_JOIN_FIELDS = [
        0 => ['platform', 'ad2_id', 'media_type'],
        MediaType::TOUTIAO => ['platform', 'ad_id', 'cost', 'click', '`show`', 'ori_cost', 'convert', 'active', 'pay_count', 'register'],
        MediaType::TENCENT => [],
        MediaType::KUAISHOU => [],
        MediaType::BAIDU => [],
    ];
    const AD_LOG_JOIN_FIELDS = [
        0 => ['platform', 'ad2_id', 'media_type'],
        MediaType::TOUTIAO => ['platform', 'ad_id'],
        MediaType::TENCENT => [],
        MediaType::KUAISHOU => [],
        MediaType::BAIDU => [],
    ];

    //不同广告ad_log和data_log连接条件
    const JOIN_DATA_ON = [
        0 => [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.ad3_id', '=', 'third_ad_log.ad3_id'],
            ['data_log.media_type', '=', 'third_ad_log.media_type']
        ],
        MediaType::TOUTIAO => [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.creative_id', '=', 'third_ad_log.creative_id']
        ],
        MediaType::TENCENT => [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.dynamic_creative_id', '=', 'third_ad_log.ad_id']
        ],
        MediaType::KUAISHOU => [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.creative_id', '=', 'third_ad_log.creative_id']
        ],
        MediaType::BAIDU => [
            ['data_log.platform', '=', 'third_ad_log.platform'],
            ['data_log.creative_id', '=', 'third_ad_log.creative_id']
        ]
    ];
    //
    const JOIN_REG_ON = [
        0 => [
            ['third_ad_log.platform', '=', 'reg_log.platform'],
            ['third_ad_log.ad3_id', '=', 'reg_log.adcre_id'],
            ['agent_site.media_type_id', '=', 'third_ad_log.media_type']
        ],
        MediaType::TOUTIAO => [
            ['third_ad_log.platform', '=', 'reg_log.platform'],
            ['third_ad_log.creative_id', '=', 'reg_log.adcre_id'],
            //['reg_log.ad_platform_id', '=', MediaType::TOUTIAO]
        ],
        MediaType::TENCENT => [
            ['third_ad_log.platform', '=', 'reg_log.platform'],
            ['third_ad_log.ad_id', '=', 'reg_log.adcre_id'],
            //['reg_log.ad_platform_id', '=', MediaType::TOUTIAO]
        ],
        MediaType::KUAISHOU => [
            ['third_ad_log.platform', '=', 'reg_log.platform'],
            ['third_ad_log.creative_id', '=', 'reg_log.adcre_id'],
            //['reg_log.ad_platform_id', '=', MediaType::TOUTIAO]
        ],
        MediaType::BAIDU => [
            ['third_ad_log.platform', '=', 'reg_log.platform'],
            ['third_ad_log.creative_id', '=', 'reg_log.adcre_id'],
            //['reg_log.ad_platform_id', '=', MediaType::TOUTIAO]
        ],
    ];
    //
    const JOIN_OVERVIEW_ON = [
        0 => [
            ['third_ad_log.platform', '=', 'overview_log.platform'],
            ['third_ad_log.ad3_id', '=', 'overview_log.ad3_id'],
            ['agent_site.media_type_id', '=', 'third_ad_log.media_type']
        ],
        MediaType::TOUTIAO => [
            ['third_ad_log.platform', '=', 'overview_log.platform'],
            ['third_ad_log.creative_id', '=', 'overview_log.ad3_id']
        ],
        MediaType::TENCENT => [
            ['third_ad_log.platform', '=', 'overview_log.platform'],
            ['third_ad_log.ad_id', '=', 'overview_log.ad3_id'],
        ],
        MediaType::KUAISHOU => [
            ['third_ad_log.platform', '=', 'overview_log.platform'],
            ['third_ad_log.creative_id', '=', 'overview_log.ad3_id'],
        ],
        MediaType::BAIDU => [
            ['third_ad_log.platform', '=', 'overview_log.platform'],
            ['third_ad_log.creative_id', '=', 'overview_log.ad3_id'],
        ],
    ];
    //
    const ACCOUNT_JOIN_FIELDS = [
        0 => ['platform', 'account_id'],
        MediaType::TOUTIAO => ['platform', 'account_id'],
        MediaType::TENCENT => [],
        MediaType::KUAISHOU => [],
        MediaType::BAIDU => [],
    ];
    //不同广告ad_log和account_log连接条件
    const JOIN_ACCOUNT_ON = [
        0 => [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ],
        MediaType::TOUTIAO => [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ],
        MediaType::TENCENT => [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ],
        MediaType::KUAISHOU => [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ],
        MediaType::BAIDU => [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ],
    ];
    //
    const FIRST_AD_LOG_JOIN_FIELDS = [
        0 => ['platform', 'ad1_id'],
        MediaType::TOUTIAO => ['platform', 'campaign_id'],
        MediaType::TENCENT => [],
        MediaType::KUAISHOU => [],
        MediaType::BAIDU => [],
    ];
    //
    const THIRD_AD_LOG_JOIN_FIELDS = [
        0 => ['third_ad_log.platform', 'third_ad_log.ad2_id', "if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature", 'third_ad_log.media_type'],
        MediaType::TOUTIAO => ['third_ad_log.platform', 'third_ad_log.ad_id', "if(ad_log.signature != '',ad_log.signature,third_ad_log.signature ) AS signature"],
        MediaType::TENCENT => ['third_ad_log.platform', 'third_ad_log.adgroup_id', "forth_ad_log.signature AS signature"],
        MediaType::KUAISHOU => ['third_ad_log.platform', 'third_ad_log.ad_id', "third_ad_log.signature AS signature"],
        MediaType::BAIDU => ['third_ad_log.platform', 'third_ad_log.ad_id', "third_ad_log.signature AS signature"]
    ];
    //不同广告ad_log和first_ad_log连接条件
    const FIRST_AD_LOG_ON = [
        0 =>[
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.ad1_id', '=', 'ad_log.ad1_id'],
        ],
        MediaType::TOUTIAO =>[
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        MediaType::TENCENT =>[
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        MediaType::KUAISHOU =>[
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
        MediaType::BAIDU =>[
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.campaign_id', '=', 'ad_log.campaign_id']
        ],
    ];

    const FORTH_AD_LOG_ON = [
        0 =>[
            ['third_ad_log.platform', '=', 'forth_ad_log.platform'],
            ['third_ad_log.ad3_id', '=', 'forth_ad_log.ad3_id'],
        ],
        MediaType::TENCENT =>[
            ['third_ad_log.platform', '=', 'forth_ad_log.platform'],
            ['third_ad_log.ad_id', '=', 'forth_ad_log.ad_id'],
        ],
    ];

    const FOURTH_JOIN_DATA_LOG_ON = [
        ['data_log.platform', '=', 'forth_ad_log.platform'],
        ['data_log.ad4_id', '=', 'forth_ad_log.ad4_id'],
        ['data_log.account_id', '=', 'forth_ad_log.account_id']
    ];

    const FOURTH_JOIN_OVERVIEW_LOG_ON = [
        ['overview_log.platform', '=', 'forth_ad_log.platform'],
        ['overview_log.ad4_id', '=', 'forth_ad_log.ad4_id'],
        ['overview_log.account_id', '=', 'forth_ad_log.account_id']
    ];

    //不同广告ad_log和third_ad_log连接条件
    const AD_LOG_ON = [
        0 => [
            ['ad_log.platform', '=', 'third_ad_log.platform'],
            ['ad_log.ad2_id', '=', 'third_ad_log.ad2_id'],
            ['ad_log.media_type', '=', 'third_ad_log.media_type']
        ],
        MediaType::TOUTIAO => [
            ['ad_log.platform', '=', 'third_ad_log.platform'],
            ['ad_log.ad_id', '=', 'third_ad_log.ad_id']
        ],
        MediaType::TENCENT => [
            ['ad_log.platform', '=', 'third_ad_log.platform'],
            ['ad_log.adgroup_id', '=', 'third_ad_log.adgroup_id']
        ],
        MediaType::KUAISHOU => [
            ['ad_log.platform', '=', 'third_ad_log.platform'],
            ['ad_log.ad_id', '=', 'third_ad_log.ad_id']
        ],
        MediaType::BAIDU => [
            ['ad_log.platform', '=', 'third_ad_log.platform'],
            ['ad_log.ad_id', '=', 'third_ad_log.ad_id']
        ],
    ];
//    const MATERIAL_JOIN_FIELDS = ['platform', 'material_id'];
//    const MATERIAL_FILE_JOIN_FIELDS = ['platform', 'material_id', 'signature'];
//    const GAME_JOIN_FIELDS = ['platform', 'game_id'];
//    const SITE_GAME_JOIN_FIELDS = ['platform', 'site_id', 'game_id'];
//    const AGENT_SITE_JOIN_FIELDS = ['platform', 'site_id'];    const AGENT_SITE_JOIN_FIELDS = ['platform', 'site_id'];
//    const REG_LOG_JOIN_FIELDS = ['platform', 'ad_platform_id', 'adgroup_id', 'uid', 'total_pay_money'];
//    const OVERVIEW_LOG_JOIN_FIELDS = ['platform', 'ad2_id', 'media_type',
//        'day_reg_muid_distinct_count', 'day_action_muid_distinct_count', 'day_reg_muid_count', 'day_action_muid_count',
//        'day_first_day_pay_money', 'day_reg_uid_count', 'day_first_day_pay_count'];
//    const JOIN_SITE_GAME_ON = [
//        ['site_game.platform', '=', 'ad_log.platform'],
//        ['site_game.site_id', '=', 'ad_log.site_id']
//    ];
//    const JOIN_GAME_ON = [
//        ['game.platform', '=', 'site_game.platform'],
//        ['game.game_id', '=', 'site_game.game_id']
//    ];
//    const JOIN_AGENT_SITE_ON = [
//        ['agent_site.platform', '=', 'ad_log.platform'],
//        ['agent_site.site_id', '=', 'ad_log.site_id']
//    ];
    const JOIN_MATERIAL_FILE_ON = [
        ['material_file.platform', '=', 'third_ad_log.platform'],
        ['material_file.signature', '=', 'third_ad_log.signature'],
    ];
    const JOIN_MATERIAL_ON = [
        ['material.platform', '=', 'material_file.platform'],
        ['material.material_id', '=', 'material_file.material_id']
    ];
    const JOIN_MATERIAL_LABEL_ON = [
        ['material_label.platform', '=', 'material.platform'],
        ['material_label.material_id', '=', 'material.material_id']
    ];

    const AD_LOG_GROUP_BY = [
        0 => [
            'port_version' => ['ad_log.port_version'],
            'inventory_type' => ['ad_log.inventory_type'],
            'signature' => ["if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)"],
            'material_id' => ["if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)"],
            'author' => ["if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)"],
            'theme_pid' => ["if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)"],
            'theme_id' => ["if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)"],

            'platform' => ['third_ad_log.platform'],
            'ad3_id' => ['third_ad_log.ad3_id'],
            'ad2_id' => ['third_ad_log.ad2_id'],

            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id'],
            'agent_group_id' => ['agent_site.agent_group_id'],
            'create_type' => ['ad_log.create_type'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'media_type' => ['third_ad_log.media_type'],


            'item_id' => ['third_ad_log.item_id'],
            'aweme_title' => ['third_ad_log.item_id'],
            'aweme_account' => ['third_ad_log.item_id'],
            'aweme_name' =>  ['third_ad_log.item_id'],
            'interface_person' => ['third_ad_log.item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id'],
            'intermediary_person' => ['third_ad_log.item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id'],
            'talent_account' => ['third_ad_log.talent_account'],
            'talent_interface_person' => ['third_ad_log.talent_account'],
            'gdt_xqxs_inventory_subdivision' => ['gdt_xqxs_inventory_subdivision'],
            'gdt_inventory_subdivision' => ['gdt_inventory_subdivision'],
        ],
        MediaType::TOUTIAO => [
            'port_version' => ['ad_log.port_version'],
            'inventory_type' => ['ad_log.inventory_type'],
            'signature' => ["if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)"],
            'material_id' => ["if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)"],
            'author' => ["if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)"],
            'theme_pid' => ["if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)"],
            'theme_id' => ["if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)"],
            'platform' => ['third_ad_log.platform'],
            'ad3_id' => ['third_ad_log.creative_id'],
            'ad2_id' => ['third_ad_log.ad_id'],

            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id'],
            'agent_group_id' => ['agent_site.agent_group_id'],
            'create_type' => ['ad_log.create_type'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],

            'item_id' => ['third_ad_log.aweme_item_id'],
            'aweme_title' => ['third_ad_log.aweme_item_id'],
            'aweme_account' => ['third_ad_log.aweme_item_id'],
            'aweme_name' =>  ['third_ad_log.aweme_item_id'],
            'interface_person' => ['third_ad_log.aweme_item_id'],
            'interface_person_group_name' => ['third_ad_log.aweme_item_id'],
            'intermediary_person' => ['third_ad_log.aweme_item_id'],
            'aweme_item_publish_time' => ['third_ad_log.aweme_item_id'],
            'talent_account' => ['third_ad_log.ies_core_user_id'],
            'talent_interface_person' => ['third_ad_log.ies_core_user_id'],
        ],
        MediaType::TENCENT => [
            'port_version' => ['ad_log.port_version'],
            'inventory_type' => ['ad_log.site_set'],
            'signature' => ["forth_ad_log.signature"],
            'material_id' => ["forth_ad_log.signature"],
            'author' => ["forth_ad_log.signature"],

            'platform' => ['third_ad_log.platform'],
            'ad3_id' => ['third_ad_log.ad_id'],
            'ad2_id' => ['third_ad_log.adgroup_id'],

            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id'],
            'agent_group_id' => ['agent_site.agent_group_id'],
            'create_type' => ['ad_log.create_type'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],

            'item_id' => ['third_ad_log.item_id'],
            'aweme_title' => ['third_ad_log.item_id'],
            'aweme_account' => ['third_ad_log.item_id'],
            'aweme_name' =>  ['third_ad_log.item_id'],
            'interface_person' => ['third_ad_log.item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id'],
            'intermediary_person' => ['third_ad_log.item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id'],
            'talent_account' => ['talent_account'],
            'talent_interface_person' => ['talent_account'],
            'gdt_xqxs_inventory_subdivision' => ['gdt_xqxs_inventory_subdivision'],
            'gdt_inventory_subdivision' => ['gdt_inventory_subdivision'],
        ],

        MediaType::KUAISHOU => [
            'port_version' => ['ad_log.port_version'],
            'inventory_type' => ['ad_log.scene_id'],
            'signature' => ["third_ad_log.signature"],
            'material_id' => ["third_ad_log.signature"],
            'author' => ["third_ad_log.signature"],

            'platform' => ['third_ad_log.platform'],
            'ad3_id' => ['third_ad_log.creative_id'],
            'ad2_id' => ['third_ad_log.ad_id'],

            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id'],
            'agent_group_id' => ['agent_site.agent_group_id'],
            'create_type' => ['ad_log.create_type'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],

            'item_id' => ['third_ad_log.item_id'],
            'aweme_title' => ['third_ad_log.item_id'],
            'aweme_account' => ['third_ad_log.item_id'],
            'aweme_name' =>  ['third_ad_log.item_id'],
            'interface_person' => ['third_ad_log.item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id'],
            'intermediary_person' => ['third_ad_log.item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id'],
            'talent_account' => ['talent_account'],
            'talent_interface_person' => ['talent_account'],
        ],

        MediaType::BAIDU => [
            'port_version' => ['ad_log.port_version'],
            'inventory_type' => ['ad_log.flow_types'],
            'signature' => ["third_ad_log.signature"],
            'material_id' => ["third_ad_log.signature"],
            'author' => ["third_ad_log.signature"],

            'platform' => ['third_ad_log.platform'],
            'ad3_id' => ['third_ad_log.creative_id'],
            'ad2_id' => ['third_ad_log.ad_id'],

            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id'],
            'agent_group_id' => ['agent_site.agent_group_id'],
            'create_type' => ['ad_log.create_type'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],

            'item_id' => ['third_ad_log.item_id'],
            'aweme_title' => ['third_ad_log.item_id'],
            'aweme_account' => ['third_ad_log.item_id'],
            'aweme_name' =>  ['third_ad_log.item_id'],
            'interface_person' => ['third_ad_log.item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id'],
            'intermediary_person' => ['third_ad_log.item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id'],
            'talent_account' => ['talent_account'],
            'talent_interface_person' => ['talent_account'],
        ],
    ];

    const MATERIAL_FILE_GROUP_BY = [
        'signature' => 'signature',
        'platform' => 'platform'
    ];
    const MATERIAL_GROUP_BY = [
        'material_id' => 'material_id',
        'platform' => 'platform'
    ];
    const AWEME_GROUP_BY = [
        'item_id' => 'item_id',
        'aweme_title' => 'item_id',
        'aweme_account' => 'item_id',
        'aweme_name' =>  'item_id',
        'interface_person' => 'item_id',
        'interface_person_group_name' => 'item_id',
        'intermediary_person' => 'item_id',
        'aweme_item_publish_time' => 'item_id'
    ];

    const JOIN_AD_LOG_SELECT = [
        0 => [
            'media_type' => ['ad_log.media_type as media_type'],
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            'platform' => ['third_ad_log.platform'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id', 'agent_site.agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id', 'agent_site.agent_group_name'],
            'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['third_ad_log.account_id'],
            'account_name' => ['account_log.account_name as account_name'],
            'ad1_id' => ['first_ad_log.ad1_id as ad1_id'],
            'ad1_name' => ['first_ad_log.ad1_name as ad1_name'],
            'ad2_id' => ['ad_log.ad2_id'],
            'ad2_name' => ['ad_log.ad2_name'],
            'ad3_id' => ['third_ad_log.ad3_id as ad3_id'],
            'ad3_name' => ['third_ad_log.ad3_name as ad3_name'],
            'ad2_create_time' => ['ad_log.ad2_create_time'],
            'ad2_status' => ['ad_log.status as ad2_status'],

            'signature' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'material_id' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'theme_pid' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'theme_id' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'count' => ['third_ad_log.ad3_id as ad3_id'],
            'count_ad2' => ['third_ad_log.ad3_id as ad3_id'],
            'count_ad2_delivering' => ['third_ad_log.ad3_id as ad3_id'],
            'count_ad2_undeliveried' => ['third_ad_log.ad3_id as ad3_id'],
            'count_creative' => ['third_ad_log.ad3_id as ad3_id'],
            'count_ad2_deliveried' => ['third_ad_log.ad3_id as ad3_id'],

            'item_id' => ['third_ad_log.item_id'],
            'aweme_title' => ['third_ad_log.item_id'],
            'aweme_account' => ['third_ad_log.item_id'],
            'aweme_name' => ['third_ad_log.item_id'],
            'interface_person' => ['third_ad_log.item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id'],
            'intermediary_person' => ['third_ad_log.item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id'],
            'talent_account' => ['third_ad_log.talent_account'],
            'talent_interface_person' => ['third_ad_log.talent_account'],
        ],
        MediaType::TOUTIAO => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            'port_version' => ['ad_log.port_version as port_version'],
            //头条素材ID
            'tt_material_id' => ['third_ad_log.material_id as tt_material_id'],
            'tt_materials_type' => ['ad_log.materials_type as tt_materials_type'],
            'platform' => ['third_ad_log.platform'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id', 'agent_site.agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id', 'agent_site.agent_group_name'],
            'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['third_ad_log.account_id'],
            'account_name' => ['account_log.account_name'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as ad1_name'],
            'ad2_id' => ['ad_log.ad_id as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name'],
            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.title as ad3_name'],
            'ad2_create_time' => ['ad_log.ad_create_time as ad2_create_time'],
            'ad2_status' => ['ad_log.status as ad2_status'],
            'signature' => ["if(ad_log.signature != '',ad_log.signature,third_ad_log.signature ) AS signature"],
            'material_id' => ["if(ad_log.signature != '',ad_log.signature,third_ad_log.signature ) AS signature"],
            'theme_pid' => ["if(ad_log.signature != '',ad_log.signature,third_ad_log.signature ) AS signature"],
            'theme_id' => ["if(ad_log.signature != '',ad_log.signature,third_ad_log.signature ) AS signature"],
            'count' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_delivering' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_undeliveried' => ['third_ad_log.creative_id as ad3_id'],
            'count_creative' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_deliveried' => ['third_ad_log.creative_id as ad3_id'],

            'item_id' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_title' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_account' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_name' => ['third_ad_log.aweme_item_id as item_id'],
            'interface_person' => ['third_ad_log.aweme_item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.aweme_item_id as item_id'],
            'intermediary_person' => ['third_ad_log.aweme_item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.aweme_item_id as item_id'],
            'talent_account' => ['third_ad_log.ies_core_user_id as talent_account'],
            'talent_interface_person' => ['third_ad_log.ies_core_user_id as talent_account'],
        ],
        MediaType::TENCENT => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.site_set as inventory_type'],
            'platform' => ['third_ad_log.platform'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id', 'agent_site.agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id', 'agent_site.agent_group_name'],
            'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['third_ad_log.account_id'],
            'account_name' => ['account_log.account_name'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as ad1_name'],
            'ad2_id' => ['ad_log.adgroup_id as ad2_id'],
            'ad2_name' => ['ad_log.adgroup_name as ad2_name'],
            'ad3_id' => ['third_ad_log.ad_id as ad3_id'],
            'ad3_name' => ['third_ad_log.ad_name as ad3_name'],
            'ad2_create_time' => ['ad_log.created_time as ad2_create_time'],
            'ad2_status' => ['ad_log.system_status as ad2_status'],
            'signature' => ["forth_ad_log.signature AS signature"],
            'material_id' => ["forth_ad_log.signature AS signature"],

            'count' => ['third_ad_log.ad_id as ad3_id'],
            'count_ad2' => ['third_ad_log.ad_id as ad3_id'],
            'count_ad2_delivering' => ['third_ad_log.ad_id as ad3_id'],
            'count_ad2_undeliveried' => ['third_ad_log.ad_id as ad3_id'],
            'count_creative' => ['third_ad_log.ad_id as ad3_id'],
            'count_ad2_deliveried' => ['third_ad_log.ad_id as ad3_id'],

            'item_id' => ['third_ad_log.item_id as item_id'],
            'aweme_title' => ['third_ad_log.item_id as item_id'],
            'aweme_account' => ['third_ad_log.item_id as item_id'],
            'aweme_name' => ['third_ad_log.item_id as item_id'],
            'interface_person' => ['third_ad_log.item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id as item_id'],
            'intermediary_person' => ['third_ad_log.item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id as item_id'],
            'talent_account' => ['"" as talent_account'],
            'talent_interface_person' => ['"" as talent_account'],
        ],

        MediaType::KUAISHOU => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.scene_id as inventory_type'],
            'platform' => ['third_ad_log.platform'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id', 'agent_site.agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id', 'agent_site.agent_group_name'],
            'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['third_ad_log.account_id'],
            'account_name' => ['account_log.user_name as account_name'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as ad1_name'],
            'ad2_id' => ['ad_log.ad_id as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name'],
            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.sticker_title as ad3_name'],
            'ad2_create_time' => ['ad_log.ad_create_time as ad2_create_time'],
            'ad2_status' => ['ad_log.ad_status as ad2_status'],
            'signature' => ["third_ad_log.signature AS signature"],
            'material_id' => ["third_ad_log.signature AS signature"],

            'count' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_delivering' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_undeliveried' => ['third_ad_log.creative_id as ad3_id'],
            'count_creative' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_deliveried' => ['third_ad_log.creative_id as ad3_id'],

            'item_id' => ['third_ad_log.item_id as item_id'],
            'aweme_title' => ['third_ad_log.item_id as item_id'],
            'aweme_account' => ['third_ad_log.item_id as item_id'],
            'aweme_name' => ['third_ad_log.item_id as item_id'],
            'interface_person' => ['third_ad_log.item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id as item_id'],
            'intermediary_person' => ['third_ad_log.item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id as item_id'],
            'talent_account' => ['"" as talent_account'],
            'talent_interface_person' => ['"" as talent_account'],
        ],

        MediaType::BAIDU => [
            'port_version' => ['ad_log.port_version as port_version'],
            'inventory_type' => ['ad_log.flow_types as inventory_type'],
            'platform' => ['third_ad_log.platform'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'agent_leader_group_name' => ['alg.agent_leader_group_name'],
            'agent_id' => ['agent_site.agent_id', 'agent_site.agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id', 'agent_site.agent_group_name'],
            'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['third_ad_log.account_id'],
            'account_name' => ['account_log.account_name'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as ad1_name'],
            'ad2_id' => ['ad_log.ad_id as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name'],
            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.title as ad3_name'],
            'ad2_create_time' => ['ad_log.ad_create_time as ad2_create_time'],
            'ad2_status' => ['ad_log.ad_status as ad2_status'],
            'signature' => ["third_ad_log.signature AS signature"],
            'material_id' => ["third_ad_log.signature AS signature"],

            'count' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_delivering' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_undeliveried' => ['third_ad_log.creative_id as ad3_id'],
            'count_creative' => ['third_ad_log.creative_id as ad3_id'],
            'count_ad2_deliveried' => ['third_ad_log.creative_id as ad3_id'],

            'item_id' => ['third_ad_log.item_id as item_id'],
            'aweme_title' => ['third_ad_log.item_id as item_id'],
            'aweme_account' => ['third_ad_log.item_id as item_id'],
            'aweme_name' => ['third_ad_log.item_id as item_id'],
            'interface_person' => ['third_ad_log.item_id as item_id'],
            'interface_person_group_name' => ['third_ad_log.item_id as item_id'],
            'intermediary_person' => ['third_ad_log.item_id as item_id'],
            'aweme_item_publish_time' => ['third_ad_log.item_id as item_id'],
            'talent_account' => ['"" as talent_account'],
            'talent_interface_person' => ['"" as talent_account'],
        ],
    ];

    const JOIN_AD_LOG_GROUP_BY = [
        0 => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.inventory_type',
            'signature' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
            'material_id' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
            'author' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
            'theme_pid' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
            'theme_id' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
            'platform' => 'ad_log.platform',
            'os' => 'game.os',
            'agent_leader' => 'agent_site.agent_leader',
            'agent_leader_group_name' => 'alg.agent_leader_group_name',
            'agent_id' => 'agent_site.agent_id',
            'agent_group_id' => 'agent_site.agent_group_id',
            'create_type' => 'ad_log.create_type',
            'game_id' => 'game.game_id',
            'main_game_id' => 'game.main_game_id',
            'root_game_id' => 'game.root_game_id',
            'media_type' => 'third_ad_log.media_type',
            'ad3_id' => 'third_ad_log.ad3_id',
            'ad2_id' => 'third_ad_log.ad2_id',

            'item_id' => 'third_ad_log.item_id',
            'aweme_title' => 'third_ad_log.item_id',
            'aweme_account' => 'third_ad_log.item_id',
            'aweme_name' =>  'third_ad_log.item_id',
            'interface_person' => 'third_ad_log.item_id',
            'interface_person_group_name' => 'third_ad_log.item_id',
            'intermediary_person' => 'third_ad_log.item_id',
            'aweme_item_publish_time' => 'third_ad_log.item_id',
            'talent_account' => 'third_ad_log.talent_account',
            'talent_interface_person' => 'third_ad_log.talent_account',
            'gdt_inventory_subdivision' => 'gdt_inventory_subdivision',
            'gdt_xqxs_inventory_subdivision' => 'gdt_xqxs_inventory_subdivision',
            ],
        MediaType::TOUTIAO => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.inventory_type',
            //头条素材ID
            'tt_material_id' => 'tt_material_id',
            'tt_materials_type' => 'tt_materials_type',
            'signature' => "if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)",
            'material_id' => "if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)",
            'author' => "if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)",
            'theme_pid' => "if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)",
            'theme_id' => "if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)",
            'platform' => 'ad_log.platform',
            'os' => 'game.os',
            'agent_leader' => 'agent_site.agent_leader',
            'agent_leader_group_name' => 'alg.agent_leader_group_name',
            'agent_id' => 'agent_site.agent_id',
            'agent_group_id' => 'agent_site.agent_group_id',
            'create_type' => 'ad_log.create_type',
            'game_id' => 'game.game_id',
            'main_game_id' => 'game.main_game_id',
            'root_game_id' => 'game.root_game_id',
            'media_type' => 'third_ad_log.media_type',
            'ad3_id' => 'third_ad_log.creative_id',
            'ad2_id' => 'third_ad_log.ad_id',

            'item_id' => 'third_ad_log.aweme_item_id',
            'aweme_title' => 'third_ad_log.aweme_item_id',
            'aweme_account' => 'third_ad_log.aweme_item_id',
            'aweme_name' =>  'third_ad_log.aweme_item_id',
            'interface_person' => 'third_ad_log.aweme_item_id',
            'interface_person_group_name' => 'third_ad_log.aweme_item_id',
            'intermediary_person' => 'third_ad_log.aweme_item_id',
            'aweme_item_publish_time' => 'third_ad_log.aweme_item_id',
            'talent_account' => 'third_ad_log.ies_core_user_id',
            'talent_interface_person' => 'third_ad_log.ies_core_user_id',
        ],
        MediaType::TENCENT => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.site_set',
            'signature' => "forth_ad_log.signature",
            'material_id' => "forth_ad_log.signature",
            'author' => "forth_ad_log.signature",

            'platform' => 'third_ad_log.platform',
            'ad3_id' => 'third_ad_log.ad_id',
            'ad2_id' => 'third_ad_log.adgroup_id',

            'os' => 'game.os',
            'agent_leader' => 'agent_site.agent_leader',
            'agent_leader_group_name' => 'alg.agent_leader_group_name',
            'agent_id' => 'agent_site.agent_id',
            'agent_group_id' => 'agent_site.agent_group_id',
            'create_type' => 'ad_log.create_type',
            'game_id' => 'game.game_id',
            'main_game_id' => 'game.main_game_id',
            'root_game_id' => 'game.root_game_id',

            'media_type' => 'third_ad_log.media_type',
            'item_id' => 'third_ad_log.item_id',
            'aweme_title' => 'third_ad_log.item_id',
            'aweme_account' => 'third_ad_log.item_id',
            'aweme_name' =>  'third_ad_log.item_id',
            'interface_person' => 'third_ad_log.item_id',
            'interface_person_group_name' => 'third_ad_log.item_id',
            'intermediary_person' => 'third_ad_log.item_id',
            'aweme_item_publish_time' => 'third_ad_log.item_id',
            'talent_account' => 'talent_account',
            'talent_interface_person' => 'talent_account',
            'gdt_inventory_subdivision' => 'gdt_inventory_subdivision',
            'gdt_xqxs_inventory_subdivision' => 'gdt_xqxs_inventory_subdivision',
        ],

        MediaType::KUAISHOU => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.scene_id',
            'signature' => "third_ad_log.signature",
            'material_id' => "third_ad_log.signature",
            'author' => "third_ad_log.signature",

            'platform' => 'ad_log.platform',
            'os' => 'game.os',
            'agent_leader' => 'agent_site.agent_leader',
            'agent_leader_group_name' => 'alg.agent_leader_group_name',
            'agent_id' => 'agent_site.agent_id',
            'agent_group_id' => 'agent_site.agent_group_id',
            'create_type' => 'ad_log.create_type',
            'game_id' => 'game.game_id',
            'main_game_id' => 'game.main_game_id',
            'root_game_id' => 'game.root_game_id',
            'media_type' => 'third_ad_log.media_type',
            'ad3_id' => 'third_ad_log.creative_id',
            'ad2_id' => 'third_ad_log.ad_id',

            'item_id' => 'third_ad_log.item_id',
            'aweme_title' => 'third_ad_log.item_id',
            'aweme_account' => 'third_ad_log.item_id',
            'aweme_name' =>  'third_ad_log.item_id',
            'interface_person' => 'third_ad_log.item_id',
            'interface_person_group_name' => 'third_ad_log.item_id',
            'intermediary_person' => 'third_ad_log.item_id',
            'aweme_item_publish_time' => 'third_ad_log.item_id',
            'talent_account' => 'talent_account',
            'talent_interface_person' => 'talent_account',
        ],

        MediaType::BAIDU => [
            'port_version' => 'ad_log.port_version',
            'inventory_type' => 'ad_log.flow_types',
            'signature' => "third_ad_log.signature",
            'material_id' => "third_ad_log.signature",
            'author' => "third_ad_log.signature",

            'platform' => 'ad_log.platform',
            'os' => 'game.os',
            'agent_leader' => 'agent_site.agent_leader',
            'agent_leader_group_name' => 'alg.agent_leader_group_name',
            'agent_id' => 'agent_site.agent_id',
            'agent_group_id' => 'agent_site.agent_group_id',
            'create_type' => 'ad_log.create_type',
            'game_id' => 'game.game_id',
            'main_game_id' => 'game.main_game_id',
            'root_game_id' => 'game.root_game_id',
            'media_type' => 'third_ad_log.media_type',
            'ad3_id' => 'third_ad_log.creative_id',
            'ad2_id' => 'third_ad_log.ad_id',

            'item_id' => 'third_ad_log.item_id',
            'aweme_title' => 'third_ad_log.item_id',
            'aweme_account' => 'third_ad_log.item_id',
            'aweme_name' =>  'third_ad_log.item_id',
            'interface_person' => 'third_ad_log.item_id',
            'interface_person_group_name' => 'third_ad_log.item_id',
            'intermediary_person' => 'third_ad_log.item_id',
            'aweme_item_publish_time' => 'third_ad_log.item_id',
            'talent_account' => 'talent_account',
            'talent_interface_person' => 'talent_account',
        ],
    ];

    const AD_LOG_SPECIAL_GROUP_BY = [
        0 => ["if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)"],
        MediaType::TOUTIAO => ["if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)"],
        MediaType::TENCENT => ["forth_ad_log.signature"],
        MediaType::KUAISHOU => ["third_ad_log.signature"],
        MediaType::BAIDU => ["third_ad_log.signature"],
    ];

    const JOIN_AD_LOG_SPECIAL_GROUP_BY = [
        0 => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
        MediaType::TOUTIAO => "if(ad_log.signature != '', ad_log.signature, third_ad_log.signature)",
        MediaType::TENCENT => "forth_ad_log.signature",
        MediaType::KUAISHOU => "third_ad_log.signature",
        MediaType::BAIDU => "third_ad_log.signature",
    ];

    const STANDARD_COST_SUB_OVERVIEW_LOG_AD3_ID_FIELD = [
        0 => ["IF( ad_log.signature != '', overview_log.ad2_id , overview_log.ad3_id  ) AS ad3_id"],
        MediaType::TOUTIAO => ["IF( ad_log.signature != '', overview_log.ad2_id , overview_log.ad3_id ) AS ad3_id"],
        MediaType::TENCENT => ['overview_log.ad3_id AS ad3_id'],
        MediaType::KUAISHOU => ['overview_log.ad3_id AS ad3_id'],
        MediaType::BAIDU => ['overview_log.ad3_id AS ad3_id']
    ];

    const STANDARD_COST_SUB_DATA_LOG_AD3_ID_FIELD = [
        0 => ["IF( ad_log.signature != '', data_log.ad2_id , data_log.ad3_id  ) AS ad3_id"],
        MediaType::TOUTIAO => ["IF( ad_log.signature != '', data_log.ad_id , data_log.creative_id ) AS ad3_id"],
        MediaType::TENCENT => ['data_log.ad_id AS ad3_id'],
        MediaType::KUAISHOU => ['data_log.ad_id AS ad3_id'],
        MediaType::BAIDU => ['data_log.ad_id AS ad3_id'],
    ];

    const SUB_DATE_LOG_GROUP_BY = [
         0 => ['ad3_id', 'cost_date', 'media_type'],
         MediaType::TOUTIAO => ['ad3_id', 'cost_date', 'media_type'],
         MediaType::TENCENT => ['ad3_id', 'cost_date'],
         MediaType::KUAISHOU => ['ad3_id', 'cost_date'],
         MediaType::BAIDU => ['ad3_id', 'cost_date'],
    ];

    const SUB_OVERVIEW_LOG_GROUP_BY = [
        0 => ['ad3_id', 'cost_date', 'media_type'],
        MediaType::TOUTIAO => ['ad3_id', 'cost_date', 'media_type'],
        MediaType::TENCENT => ['ad3_id', 'cost_date'],
        MediaType::KUAISHOU => ['ad3_id', 'cost_date'],
        MediaType::BAIDU => ['ad3_id', 'cost_date'],
    ];

    const SUB_DATA_LOG_JOIN_SUB_OVERVIEW_ON = [
        0 => [
            ['sub_overview_log.ad3_id', '=', 'sub_data_log.ad3_id'],
            ['sub_overview_log.cost_date', '=', 'sub_data_log.cost_date'],
            ['sub_overview_log.media_type', '=', 'sub_data_log.media_type']
        ],
        MediaType::TOUTIAO => [
            ['sub_overview_log.ad3_id', '=', 'sub_data_log.ad3_id'],
            ['sub_overview_log.cost_date', '=', 'sub_data_log.cost_date']
        ],
        MediaType::TENCENT => [
            ['sub_overview_log.ad3_id', '=', 'sub_data_log.ad3_id'],
            ['sub_overview_log.cost_date', '=', 'sub_data_log.cost_date']
        ],
        MediaType::KUAISHOU => [
            ['sub_overview_log.ad3_id', '=', 'sub_data_log.ad3_id'],
            ['sub_overview_log.cost_date', '=', 'sub_data_log.cost_date']
        ],
        MediaType::BAIDU => [
            ['sub_overview_log.ad3_id', '=', 'sub_data_log.ad3_id'],
            ['sub_overview_log.cost_date', '=', 'sub_data_log.cost_date']
        ],
    ];

    const TOUTIAO_INEFFICIENT_FILTER = [
        //是否低效素材
        'is_inefficient' => 'toutiao_inefficient_material_log.is_inefficient',
        //是否同质化挤压严重素材
        'tt_is_similar_material' => 'toutiao_inefficient_material_log.is_similar_material',
        //是否AD优质素材
        'tt_is_ad_high_quality' => 'toutiao_inefficient_material_log.is_ad_high_quality',
        //是否首发素材
        'tt_is_first_publish_material' => 'toutiao_inefficient_material_log.is_first_publish_material',
        //是否AD低质素材
        'tt_is_ad_low_quality_material' => 'toutiao_inefficient_material_log.is_ad_low_quality_material',
        //是否千川低质素材
        'tt_is_ecp_low_quality_material' => 'toutiao_inefficient_material_log.is_ecp_low_quality_material',
        //AD素材低质原因
        'tt_message_ad_low_quality_material' => 'toutiao_inefficient_material_log.message_ad_low_quality_material',
        //千川素材低质原因
        'tt_message_ecp_low_quality_material' => 'toutiao_inefficient_material_log.message_ecp_low_quality_material'
    ];

    const TOUTIAO_REJECT_FILTER = [
        // 是否有拒审原因
        'tt_material_reject_reason' => [
            // 无
            0 => "(toutiao_promotion_reject_reason_log.material_reject_reason  = '[]' OR 
                  toutiao_promotion_reject_reason_log.material_reject_reason IS NULL)",
            // 有
            1 => "(toutiao_promotion_reject_reason_log.material_reject_reason  <> '[]' AND 
                  toutiao_promotion_reject_reason_log.material_reject_reason IS NOT NULL)",
        ],
        // 是否有审核建议
        'tt_material_suggestion' => [
            // 无
            0 => "(toutiao_promotion_reject_reason_log.material_suggestion  = '[]' OR 
                  toutiao_promotion_reject_reason_log.material_suggestion IS NULL)",
            // 有
            1 => "(toutiao_promotion_reject_reason_log.material_suggestion  <> '[]' AND 
                  toutiao_promotion_reject_reason_log.material_suggestion IS NOT NULL)",
        ]
    ];
}
