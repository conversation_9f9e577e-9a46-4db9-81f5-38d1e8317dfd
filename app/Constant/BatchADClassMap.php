<?php

/**
 * auto generate file.
 * Created at: 2020-04-13 15:06:32
 * auto generate
 */

namespace App\Constant;

use App\Exception\AppException;
use App\Utils\Helpers;

class BatchADClassMap
{
    const COMPOSE_CONTENT_CLASS_NAME = 'ADComposeContentParam';
    const TARGETING_CLASS_NAME = 'ADTargetingContentParam';
    const SETTING_CLASS_NAME = 'ADSettingContentParam';
    const OTHER_SETTING_CLASS_NAME = 'ADOtherSettingContentParam';

    /**
     * 获取用处理媒体类名格式
     * @param int $media_type
     * @param int $media_agent_type
     * @param string $class_name
     * @return string
     */
    public static function getClassName($media_type, $media_agent_type, $class_name)
    {
        $media_name = Helpers::pascal(ucfirst(strtolower(MediaType::CONST_NAME_LIST[$media_type])));

        if ($media_name) {
            $src = "App\\Param\\ADServing\\{$media_name}";
        } else {
            throw new AppException('找不到对应content类的路径');
        }

        if (in_array($class_name, [self::COMPOSE_CONTENT_CLASS_NAME, self::SETTING_CLASS_NAME, self::OTHER_SETTING_CLASS_NAME])) {
            $src_prefix = BatchADClassMap::PARAM_SRC_PREFIX_MAP[$media_type][$media_agent_type];
            if (!$src_prefix) {
                throw new AppException('参数包类型错误');
            }
            $class_name = "{$src_prefix}\\{$class_name}";
        }

        return "{$src}\\{$class_name}";
    }

    const PARAM_SRC_PREFIX_MAP = [
        MediaType::TOUTIAO => [
            0 => 'Basics',
            1 => 'Project',
            2 => 'V2',
        ],
        MediaType::TENCENT => [
            0 => 'Basics',
            1 => 'Ada',
            2 => 'V3',
        ],
        MediaType::KUAISHOU => [
            0 => 'Basics',
            1 => 'Project',
            2 => 'Search'
        ],
        MediaType::BAIDU => [
            0 => 'Basics',
        ],
        MediaType::BAIDU_SEARCH => [
            0 => 'Basics',
        ],
        MediaType::UC => [
            0 => 'Basics',
            1 => 'V2',
        ],
        MediaType::BILIBILI =>[
            0 => 'Basics',
        ]
    ];

    const SERVICE_MAP = [
        MediaType::TOUTIAO => [
            0 => '',
            1 => 'ADLab',
            2 => 'V2'
        ],
        MediaType::TENCENT => [
            0 => '',
            1 => 'ADA',
            2 => 'V3',
        ],
        MediaType::MP => [
            0 => ''
        ],
        MediaType::KUAISHOU => [
            0 => '',
            1 => 'ADLab',
            2 => 'Search'
        ],
        MediaType::BAIDU => [
            0 => '',
        ],
        MediaType::BAIDU_SEARCH => [
            0 => '',
        ],
        MediaType::UC => [
            0 => '',
            1 => 'V2',
        ],
        MediaType::YOUTUBE => [
            0 => '',
        ],
        MediaType::BILIBILI => [
            0 => '',
        ],
    ];
}

