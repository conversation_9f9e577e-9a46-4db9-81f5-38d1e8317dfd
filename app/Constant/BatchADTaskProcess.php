<?php
/**
 * Created by Ph<PERSON><PERSON>torm.
 * User: Administrator
 * Date: 2020/08/25
 * Time: 20:16
 */

namespace App\Constant;

use App\Exception\AppException;

class BatchADTaskProcess
{
    public static $map = [
        'TW' => [
            MediaType::TOUTIAO => 40,
            MediaType::TENCENT => 20,
            MediaType::KUAISHOU => 20,
            MediaType::BAIDU => 10,
            MediaType::BAIDU_SEARCH => 1,
            MediaType::UC => 1,
            MediaType::BILIBILI => 10,
        ],
        'ZW' => [
            MediaType::TOUTIAO => 10,
            MediaType::TENCENT => 10,
            MediaType::KUAISHOU => 10,
            MediaType::BAIDU => 5,
            MediaType::BAIDU_SEARCH => 1,
            MediaType::UC => 1,
            MediaType::BILIBILI => 5,
        ],
        'GR' => [
            MediaType::TOUTIAO => 10,
            MediaType::TENCENT => 10,
            MediaType::KUAISHOU => 10,
            MediaType::BAIDU => 5,
            MediaType::BAIDU_SEARCH => 1,
            MediaType::UC => 1,
            MediaType::BILIBILI => 5,
        ],
        'GRBB' => [
            MediaType::TOUTIAO => 20,
            MediaType::TENCENT => 20,
            MediaType::KUAISHOU => 15,
            MediaType::BAIDU => 10,
            MediaType::BAIDU_SEARCH => 1,
            MediaType::UC => 1,
            MediaType::BILIBILI => 10,
        ]
    ];

    /**
     * 获取进程号对应的队列号与媒体类型
     * @return array
     */
    public static function batchADTaskProcessMap()
    {
        $process_id_map = [];
        $process_index = 1;
        foreach (self::$map as $platform => $org_media_config) {
            foreach ($org_media_config as $media_type => $process_num) {
                for ($i = 0; $i < $process_num; $i++) {
                    $process_id_map[$process_index++] = [
                        'platform' => $platform,
                        'media_type' => $media_type,
                        'queue_index' => $i + 1,
                    ];
                }
            }
        }
        return $process_id_map;
    }

    /**
     * @param int $account
     * @param $media_type
     * @param $platform
     * @return int
     */
    static public function getQueueIndexForAccount(int $account, $platform, $media_type)
    {
        $process_num = self::$map[$platform][$media_type];
        if (!$process_num) {
            throw new AppException("没有配置{$media_type}-{$platform}的广告消化队列进程数!");
        }
        return ($account % (int)$process_num) + 1;
    }

    /**
     * 获取组织-媒体队列初始化的数量
     * @param $media_type
     * @param $platform
     * @return array
     */
    public static function getPlatformMediaQueueNumInitMap($platform, $media_type)
    {
        $init_message_num_map = [];
        $process_num = self::$map[$platform][$media_type];
        if (!$process_num) {
            throw new AppException("没有配置{$media_type}-{$platform}的广告消化队列进程数!");
        }
        for ($i = 1; $i <= $process_num; $i++) {
            $init_message_num_map["queue_{$i}"] = 0;
        }
        return $init_message_num_map;
    }
}
