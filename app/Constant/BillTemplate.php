<?php
/**
 * 对账单模板
 * User: Melody
 * Date: 2020/6/23
 * Time: 14:27
 */

namespace App\Constant;


class BillTemplate
{

    const PLATFORM_MAP = [
        'TW'        => [
            'invoice'              => '江西贪玩信息技术有限公司',
            'agent_leader_address' => '广东省广州市天河区黄埔大道中656号CFC汇金中心62层',
            'bank_name'            => '中国建设银行股份有限公司广信支行',
            'bank_card_number'     => '36050183115000000162',
        ],
        'GZTW'      => [
            'invoice'              => '广州贪玩信息技术有限公司',
            'agent_leader_address' => '广东省广州市天河区黄埔大道中656号CFC汇金中心62层',
            'bank_name'            => '中国建设银行股份有限公司广信支行',
            'bank_card_number'     => '36050183115000000162',
        ],
        'xinxin'    => [
            'invoice'              => '上饶市新新信息技术有限公司',
            'agent_leader_address' => '广东省广州市天河区黄埔大道中656号CFC汇金中心62层',
            'bank_name'            => '上饶银行股份有限公司鄱阳滨洲支行',
            'bank_card_number'     => '210003090000000874',
        ],
        'wanzi'     => [
            'agent_leader_name'    => '余光火',
            'mobile'               => '***********',
            'invoice'              => '鄱阳县圣宏网络科技有限公司',
            'agent_leader_address' => '江西省上饶市鄱阳县鄱阳镇东湖世纪豪园1幢1层112号',
            'bank_name'            => '中国工商银行鄱阳县支行',
            'bank_card_number'     => '1512218009000290110',
        ],
        '915'       => [
            'invoice'              => '江西九一五信息技术有限公司',
            'agent_leader_address' => '江西省上饶市鄱阳县洪迈大道在水一方10号楼二层',
            'bank_name'            => '工商银行鄱阳县支行',
            'bank_card_number'     => '1512218009000269033',
        ],
        // 短信渠道组特殊处理
        'DX'        => [
            'invoice'              => '鄱阳县圣宏网络科技有限公司',
            'agent_leader_address' => '江西省上饶市鄱阳县鄱阳镇洪迈大道在水一方10号楼二层',
            'bank_name'            => '中国工商银行鄱阳县支行',
            'bank_card_number'     => '1512218009000290110',
        ],
        // 特殊处理新新的CPA
        'xinxinCPA' => [
            'agent_leader_name'    => '刘希金',
            'mobile'               => '***********',
            'invoice'              => '上饶市新新信息技术有限公司',
            'agent_leader_address' => '江西省上饶市高铁经济试验区茶圣东路文创中心12号楼2楼201室',
            'bank_name'            => '中国工商银行股份有限公司上饶商贸城支行',
            'bank_card_number'     => '1512031619100003652',
        ],
    ];

    const GZTW_AGENT_ID_LIST = [
        432315,
        609403
    ];
    const MOBILE_MAP = [
        '黎洁雯' => '***********',
        '钟亿霞' => '***********',
        '董文娟' => '***********',
        '邓琴'   => '***********',
        '刘希金' => '***********',
        '余光火' => '***********',
    ];


    const COMPANY_MAP = [
        '广丰贪玩' => [
            'invoice' => '上饶市广丰区贪玩网络科技有限公司',
            'agent_leader_address' => '江西省上饶市广丰区芦林街道精元电脑西路梦想小镇503号',
            'bank_name'            => '招商银行股份有限公司上饶广丰支行',
            'bank_card_number'     => '***************',
        ],
        '鄱阳贪玩' => [
            'invoice' => '鄱阳县贪玩网络科技有限公司',
            'agent_leader_address' => '江西省上饶市鄱阳县鄱阳镇洪迈大道在水一方10号楼二层',
            'bank_name'            => '中国工商银行鄱阳县支行',
            'bank_card_number'     => '1512218009000269157',
        ],
        '广州贪玩' => [
            'invoice' => '广州贪玩信息技术有限公司',
            'agent_leader_address' => '广州市天河区黄埔大道中656号6401室-6409室',
            'bank_name'            => '兴业银行股份有限公司广州东风支行',
            'bank_card_number'     => '391120100100186007',
        ],
        '中旭数科' => [
            'invoice' => '广州中旭数科信息科技有限公司',
            'agent_leader_address' => '广州市天河区黄埔大道中656号6204室（仅限办公）',
            'bank_name'            => '中国建设银行股份有限公司广州天河高新区支行',
            'bank_card_number'     => '44050158050700002073',
        ],
        '中旭未来' => [
            'invoice' => '广州中旭未来科技有限公司',
            'agent_leader_address' => '广州市天河区黄埔大道中656号6501室-6505室',
            'bank_name'            => '招商银行股份有限公司广州科技园支行',
            'bank_card_number'     => '***************',
        ],
        '兰州旭兰' => [
            'invoice' => '兰州旭兰信息技术有限公司',
            'agent_leader_address' => '甘肃省兰州市城关区高新街道高新雁南路1258号科技孵化大厦16层1608-17室',
            'bank_name'            => '招商银行股份有限公司兰州高新技术开发区支行',
            'bank_card_number'     => '***************',
        ],
        '广州游点' => [
            'invoice' => '广州游点文化传媒有限公司',
            'agent_leader_address' => '广州市天河区奥体南路9号A1栋402-03(仅限办公)',
            'bank_name'            => '招商银行广州分行黄埔大道支行',
            'bank_card_number'     => '***************',
        ],
        '上海贪玩' => [
            'invoice' => '上海贪玩传奇信息技术有限公司',
            'agent_leader_address' => '上海市嘉定区真南路4268号2幢JT15882室',
            'bank_name'            => '招商银行股份有限公司上海南翔支行',
            'bank_card_number'     => '***************',
        ],
        '江西贪玩' => [
            'invoice' => '江西贪玩信息技术有限公司',
            'agent_leader_address' => '江西省上饶市高铁经济试验区天佑东大道6号6楼',
            'bank_name'            => '中国建设银行股份有限公司广信支行',
            'bank_card_number'     => '36050183115000000163',
        ],
        '海南贪玩' => [
            'invoice' => '海南贪玩信息技术有限公司',
            'agent_leader_address' => '海南省澄迈县老城镇老城开发区南一环路600米处南侧海南生态软件园C地块一期C06栋一层103号',
            'bank_name'            => '中国银行海口龙华支行',
            'bank_card_number'     => '************',
        ],
    ];


    const CPS = '<!DOCTYPE html>
            <html xmlns:o="urn:schemas-microsoft-com:office:office"
                  xmlns:x="urn:schemas-microsoft-com:office:excel"
                  xmlns="http://www.w3.org/TR/REC-html40">
            <head>
                <meta charset="UTF-8">
                <title>结算确认函</title>
                <!--[if gte mso 9]>
                <xml>
                    <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                            <x:ExcelWorksheet>
                                <x:Name></x:Name>
                                <x:WorksheetOptions>
                                    <x:DisplayGridlines/>
                                </x:WorksheetOptions>
                            </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                    </x:ExcelWorkbook>
                </xml><![endif]-->
            </head>
            <body>
            <table class="table" border="1" style="border: 2px solid #000; font-size: 14px;">
            
                <tr style="height: 25px; font-size: 18px; border: 0">
                    <th colspan="8">{$title}CPS结算确认函({$all_time})</th>
                </tr>
                <tr style="height: 25px">
                    <th width="80">计费周期</th>
                    <th>渠道</th>
                    <th>位置</th>
                    <th width="100">总流水金额</th>
                    <th width="80">渠道费率</th>
                    <th width="80">渠道费</th>
                    <th width="60">税费</th>
                    <th width="80">分成比例</th>
                    <th width="100">结算金额</th>
                </tr>
            
                {$site_date_list}
                                                            
                <tr style="height: 25px">
                    <th colspan="3">合计</th>
                    <td align="center">{$total_money}</td>
                    <td></td>
                    <td align="center">{$total_channel_fee}</td>
                    <td></td>
                    <td></td>
                    <td align="center">{$total_settle_money}</td>
                </tr>
                <tr style="height: 25px">
                    <th colspan="3"> 结算金额</th>
                    <th colspan="6">{$total_settle_money}</th>
                </tr>
                <tr style="height: 25px">
                    <th colspan="3"> 结算金额（大写）：</th>
                    <th colspan="6">{$chinese_num}</th>
                </tr>
                <tr style="height: 25px">
                    <th colspan="3">不含税结算金额</th>
                    <th colspan="6">{$net_settlement_amount}</th>
                </tr>
                <tr style="height: 25px">
                    <th colspan="3">不含税结算金额（大写）</th>
                    <th colspan="6">{$net_settlement_amount_chinese}</th>
                </tr>
              
            
                <tr style="height: 50px">
                    <td colspan="3" align="center"> 备注</td>
                    <td colspan="6" style="height: 50px;">
                        1、本结算函经双方盖章有效；<br/>
                        <span style="color: red">2、贵司需正确填写"贵方信息"，银行账户须与合同一致，如因填写有误造成损失，我方概不负责；</span><br/>
                        3、确认数据正确无误后，请将结算函竖版打印2份加盖公章，连同正规合法发票一同邮寄至我司（如贵公司需要对账单回寄，请打印3份）；<br/>
                        4、请严格按照要求开具发票，否则不予结算。
                    </td>
                </tr>
            
                <tr style="height: 25px; border: 0">
                    <td style="font-weight: bold">我方信息</td>
                    <td colspan="3"></td>
                    <td style="font-weight: bold;color: red">贵方信息</td>
                    <td colspan="4"></td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td>发票抬头：</td>
                    <td colspan="3">{$our_invoice}</td>
                    <td>账户名称：</td>
                    <td colspan="4">{$account_name}</td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td>发票内容：</td>
                    <td colspan="3">信息服务费</td>
                    <td>开户银行：</td>
                    <td colspan="4">{$account_bank}</td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td>发票类型：</td>
                    <td colspan="3">增值税专用发票</td>
                    <td>银行账号：</td>
                    <td colspan="4">&nbsp;{$account_number}</td>
                </tr>
                <tr style="height: 50px; border: 0">
                    <td>收件人：</td>
                    <td colspan="3">{$agent_leader}</td>
                    <td>纳税人识别号：</td>
                    <td colspan="4"></td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td>电话：</td>
                    <td colspan="3">&nbsp;{$agent_leader_mobile}</td>
                    <td>收件人：</td>
                    <td colspan="4"></td>
                </tr>
                <tr style="height: 50px; border: 0">
                    <td>邮寄地址：</td>
                    <td colspan="3">{$agent_leader_address}</td>
                    <td>电话：</td>
                    <td colspan="4"></td>
                </tr>
                <tr style="height: 50px; border: 0">
                    <td></td>
                    <td colspan="3"></td>
                    <td>邮寄地址：</td>
                    <td colspan="4"></td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td colspan="4"></td>
                    <td colspan="5"></td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td colspan="4"></td>
                    <td colspan="5"></td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td colspan="4" style="font-weight: bold">我方：{$our_company}</td>
                    <td colspan="5" style="font-weight: bold">贵方：{$other_company}</td>
                </tr>
                <tr style="height: 25px; border: 0">
                    <td colspan="4" style="font-weight: bold">公司签章：</td>
                    <td colspan="5" style="font-weight: bold">公司签章：</td>
                </tr>
            
            </table>
            </body>
            </html>';


    const CPA = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>结算确认函</title>
</head>
<body>
<table class="table" border="1" style="border: 2px solid #000; font-size: 14px;">

    <tr style="height: 25px; font-size: 20px; border: 0;color: #fff">
        <th colspan="7" style="background-color: blue">{$agent_name}-{$agent_id}&{$platform}CPA结算确认函({$all_time})</th>
    </tr>

    <tr style="height: 30px; border: 0;">
        <td colspan="7">结算时间：{$all_time}</td>
    </tr>
    <tr style="height: 30px; border: 0;">
        <td colspan="7">对账日期：{$now_date}</td>
    </tr>

    <tr>
        <th style="background-color: #f7cb8a;" width="100">计费周期</th>
        <th style="background-color: #f7cb8a;" width="100">渠道</th>
        <th style="background-color: #f7cb8a;" >位置</th>
        <th style="background-color: #f7cb8a;" width="120">新增注册设备数</th>
        <th style="background-color: #f7cb8a;" width="80">单价</th>
        <th style="background-color: #f7cb8a;" width="80">税率</th>
        <th style="background-color: #f7cb8a;" width="100">结算金额</th>
    </tr>
    {$site_date_list}
    <tr>
        <th align="center" colspan="3">合计</th>
        <td align="center">{$total_log_value}</td>
        <td></td>
        <td></td>
        <td align="center">￥{$total_settle_money}</td>
    </tr>

    <tr style="color: red;">
        <th align="center" colspan="3" style="background-color: yellow"> 结算金额合计</th>
        <th align="center" colspan="4" style="background-color: yellow">{$total_settle_money}</th>
    </tr>
    <tr style="color: red;">
        <th align="center" colspan="3" style="background-color: yellow"> 不含税结算金额合计</th>
        <th align="center" colspan="4" style="background-color: yellow">{$total_net_settlement_amount}</th>
    </tr>
    <tr style="border: 0">
        <td colspan="7">
            结算公式=新增注册设备数*单价*（1-税率）
        </td>
    </tr>
    <tr style="border: 0">
        <td colspan="7">
            备注：
        </td>
    </tr>
    <tr style="border: 0">
        <td colspan="7">
            请您与收到结算单5日内完成核对，如无问题请将盖章后的结算单连同发票快递给我们，
            我们将在收到发票后10日内安排付款。发票类型如果不是“增值税专用发票”，<span style="color: red">扣6.72个税点</span>。
        </td>
    </tr>

    <tr style="border: 0">
        <td colspan="7"></td>
    </tr>
    <tr style="border: 0">
        <td colspan="7"></td>
    </tr>

    <tr style="border: 0">
        <td style="font-weight: bold" colspan="3">甲方信息:</td>
        <td style="font-weight: bold" colspan="4">乙方信息:</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3">收件人：{$agent_leader}</td>
        <td colspan="4">收件人：</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3">电话：{$agent_leader_mobile}</td>
        <td colspan="4">电话：{$other_mobile}</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3">地址：{$agent_leader_address}</td>
        <td colspan="4">地址：{$other_address}</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3">开户名：{$our_company}</td>
        <td colspan="4">开户名：{$other_company}</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3">开户银行：{$our_bank}</td>
        <td colspan="4">开户银行：{$other_bank}</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3">银行账户：{$our_bank_number}</td>
        <td colspan="4">银行账户：{$other_bank_number}</td>
    </tr>
    <tr style="border: 0">
        <td colspan="7"></td>
    </tr>
    <tr style="border: 0">
        <td colspan="3" style="font-weight: bold">甲方：{$our_company}</td>
        <td colspan="4" style="font-weight: bold">乙方：{$other_company}</td>
    </tr>
    <tr style="border: 0">
        <td colspan="3" style="font-weight: bold">公司签章：</td>
        <td colspan="4" style="font-weight: bold">公司签章：</td>
    </tr>

</table>
</body>
</html>';
}
