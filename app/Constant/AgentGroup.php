<?php

/**
 * auto generate file.
 * Created at: 2020-04-13 15:06:32
 * auto generate
 */

namespace App\Constant;

class AgentGroup
{
    const TENCENT = 1;
    const WX_MOMENTS = 2;
    const UC = 3;
    const BAIDU = 4;
    const TOUTIAO = 5;
    const XULI = 6;
    const OW = 7;
    const OTHER = 8;
    const IQIYI = 9;
    const SEARCH = 10;
    const MOMO = 11;
    const NETEASE = 12;
    const BAIDU_SEARCH = 13;
    const SOUGOU_SEARCH = 14;
    const SHENMA_SEARCH = 15;
    const MEDIA_SCHEDULE = 16;
    const KUAISHOU = 17;
    const TOUTIAO_SEARCH = 21;
    const KUAISHOU_STAR = 23;
    const DOUYIN_STAR = 24;
    const QIHU_SEARCH = 25;
    const BAIDU_OCPC = 26;
    const BAIDU_SCHEDULE = 41;
    const TOUTIAO_UNION = 44;
    const TOUTIAO_SCHEDULE = 46;
    const WX_MINI_APP = 65;
    const WEIBO = 82;
    const PINDUODUO = 78;
    const JUZHANG_ASO = 90;
    const TOUTIAO_PINPAI = 105;
    const WX_MINI_GAME = 160;
    const BILIBILI = 161;
    const DOUYIN_LIVE = 162;
    const KUAISHOU_PINPAI = 165;
    const CPA = 175;
    const CPS = 176;
    const HUAWEI = 185;
    const TOUTIAO_PINPAI_LIVE = 186;
    const KUAISHOU_UNION = 189;
    const ASA = 192;
    const TOUTIAO_LIVE = 194;
    const TOUTIAO_APP = 196;
    const ADQ_WX_MINI_GAME = 197;
    const HUAWEI_OCPD = 207;
    const TOUTIAO_UOP = 214;
    const CHENZHONG_LIVE = 210;
    const KUAISHOU_LIVE = 211;
    const DOUYIN_UOP = 212;
    const TAPTAP = 216;
    const QIHU_JINGJIA = 236;
    const TOUTIAO_HOT = 237;
    const KUAISHOU_MINI_GAME = 243;
    const BAIDU_MINI_GAME = 249;
    const HUYA_MINI_GAME = 252;
    const UC_MINI_GAME = 253;
    const UC_LIVE_DIVERSION = 254;
    const DOUYIN_HOT = 256;
    const HUAWEI_LY = 257;
    const DOUYIN_MINI_GAME = 261;
    const DOUYIN_WINDMILL_FANS = 266;
    const DOUYIN_UOP_FANS = 267;
    const TOUTIAO_ORIGIN = 268;
    const BAIDU_SEARCH_MINI_GAME = 270;
    const KUAISHOU_HOT = 271;
    const KUAISHOU_ORIGIN = 272;
    const TENCENT_CHANNELS = 275;

    const TENCENT_SOUGOU_SEARCH = 287;

    const KUAISHOU_SEARCH = 296;
    const KUAISHOU_NATIVE = 302;
    const DOUYIN = 312;
    const QIANCHUAN = 313;
    const HONOR = 317;
    const HUAWEI_JINGHONG = 320;
    const ALIPAY = 321;
    const XINGTU = 353;
    const DOUYIN_ENTERPRISE = 357;
    const BILIBILI_LY = 365;
    const XIAOMI = 399;
    const OPPO = 409;
    const WX_CHANNEL_HOT = 415;
    const VIVO_LY = 420;
    const OPPO_LY = 421;
    const XINGTU_SPREAD_STAR = 432;
    const DOUYIN_PINXUAN = 438;
    const HUAWEI_PUSH = 440;
    const TOUTIAO_XINGTU_LIANTOU = 456;
    const SISANJIUJIU_YEYOU = 474;
    const SISANJIUJIU_LY = 475;
    const XIAOMI_QUICK_APP = 476;
    const LEIDIAN_LY = 477;
    const MUMU_LY = 478;
    const NUBIA_LY = 479;
    const MEIZU_LY = 480;
    const SAMSUANG_LY = 481;
    const ERSANSAN_LEYUAN_LY = 482;
    const HUAWEI_QUICK_GAME = 483;
    const HUAWEI_SHOUMENG = 484;
    const HUAWEI_HARMONY = 485;
    const OPPO_QUICK_GAME = 486;
    const VIVO_QUICK_GAME = 487;
    const ALIPAY_MINI_GAME_LY = 488;
    const MEITUAN_MINI_GAME_LY = 489;
    const BAIDU_MINI_GAME_LY = 490;
    const BILIBILI_LY_CPS = 494;
    const KUAIFA_LY = 496;
    const HUAWEI_MONETIZATION = 499;
    const ZB_FANS = 501;
    const VIDEO_HOT = 516;

    const TYPE_MAP = [
        self::TENCENT => '广点通',
        self::WX_MOMENTS => '朋友圈',
        self::UC => 'UC头条',
        self::BAIDU => '百度信息流',
        self::TOUTIAO => '今日头条',
        self::XULI => '旭力',
        self::OW => '官网',
        self::OTHER => '其他',
        self::IQIYI => '爱奇艺',
        self::SEARCH => '搜索',
        self::MOMO => '陌陌',
        self::NETEASE => '网易',
        self::BAIDU_SEARCH => '百度搜索',
        self::SOUGOU_SEARCH => '搜狗搜索',
        self::SHENMA_SEARCH => '神马搜索',
        self::MEDIA_SCHEDULE => '排期媒体',
        self::KUAISHOU => '快手',
        self::TOUTIAO_SEARCH => '头条搜索',
        self::KUAISHOU_STAR => '快手红人',
        self::DOUYIN_STAR => '抖音红人',
        self::QIHU_SEARCH => '360搜索',
        self::BAIDU_OCPC => '百度OCPC',
        self::BAIDU_SCHEDULE => '百度排期',
        self::TOUTIAO_UNION => '今日头条-穿山甲',
        self::TOUTIAO_SCHEDULE => '头条排期',
        self::WX_MINI_APP => '微信小程序',
        self::WEIBO => '微博',
        self::PINDUODUO => '拼多多',
        self::JUZHANG_ASO => '巨掌aso',
        self::TOUTIAO_PINPAI => '头条品牌',
        self::WX_MINI_GAME => '微信小游戏',
        self::BILIBILI => '哔哩哔哩',
        self::DOUYIN_LIVE => '抖音直播',
        self::KUAISHOU_PINPAI => '快手品牌',
        self::CPA => 'CPA',
        self::CPS => 'CPS',
        self::HUAWEI => '华为',
        self::TOUTIAO_PINPAI_LIVE => '头条品牌直播',
        self::KUAISHOU_UNION => '快手联盟组',
        self::TOUTIAO_LIVE => '头条直播导流',
        self::TOUTIAO_APP => '头条应用管理中心',
        self::ADQ_WX_MINI_GAME => 'adq微信小游戏',
        self::HUAWEI_OCPD => '华为ocpd',
        self::TOUTIAO_UOP => '头条小手柄导流',
        self::CHENZHONG_LIVE => '快手直播',
        self::KUAISHOU_LIVE => '快手直播导流',
        self::DOUYIN_UOP => '抖音小手柄',
        self::TAPTAP => 'taptap',
        self::QIHU_JINGJIA => '360竞价',
        self::TOUTIAO_HOT => '头条原生加热',
        self::KUAISHOU_MINI_GAME => '快手小游戏',
        self::BAIDU_MINI_GAME => '百度小游戏',
        self::HUYA_MINI_GAME => '虎牙直播-小程序',
        self::UC_MINI_GAME => 'UC小游戏',
        self::UC_LIVE_DIVERSION => 'UC直播导流',
        self::DOUYIN_HOT => '抖音加热',
        self::HUAWEI_LY => '华为联运',
        self::DOUYIN_MINI_GAME => '抖音小游戏',
        self::DOUYIN_WINDMILL_FANS => '风车粉丝群',
        self::DOUYIN_UOP_FANS => '手柄粉丝群',
        self::TOUTIAO_ORIGIN => '头条原生直投',
        self::BAIDU_SEARCH_MINI_GAME => '百度搜索小游戏',
        self::KUAISHOU_HOT => '快手原生加热',
        self::KUAISHOU_ORIGIN => '快手原生直投',
        self::TENCENT_CHANNELS => 'adq微信视频号',
        self::KUAISHOU_SEARCH => '快手搜索',
        self::KUAISHOU_NATIVE => '快手原生',
        self::DOUYIN => '抖音',
        self::QIANCHUAN => '千川',
        self::HONOR => '荣耀',
        self::HUAWEI_JINGHONG => '华为鲸鸿动能',
        self::ALIPAY => '支付宝',
        self::XINGTU => '星广联投',
        self::BILIBILI_LY => '哔哩哔哩-联运',
        self::XIAOMI => '小米',
        self::OPPO => 'oppo',
        self::WX_CHANNEL_HOT => '微信视频号加热',
        self::VIVO_LY => 'vivo联运',
        self::OPPO_LY => 'oppo联运',
        self::DOUYIN_ENTERPRISE => '抖音企业号',
        self::DOUYIN_PINXUAN => '品宣抖音号',
        self::XINGTU_SPREAD_STAR => '星图传播达人',
        self::HUAWEI_PUSH => '华为推送',
        self::TOUTIAO_XINGTU_LIANTOU => '头条-星广联投',
        self::SISANJIUJIU_YEYOU => '4399页游',
        self::SISANJIUJIU_LY => '4399联运',
        self::XIAOMI_QUICK_APP => '小米快应用',
        self::LEIDIAN_LY => '雷电联运',
        self::MUMU_LY => 'mumu联运',
        self::NUBIA_LY => '努比亚联运',
        self::MEIZU_LY => '魅族联运',
        self::SAMSUANG_LY => '三星联运',
        self::ERSANSAN_LEYUAN_LY => '233乐园联运',
        self::HUAWEI_QUICK_GAME => '华为快游戏',
        self::HUAWEI_SHOUMENG => '手盟-华为',
        self::HUAWEI_HARMONY => '华为鸿蒙',
        self::OPPO_QUICK_GAME => 'oppo快游戏',
        self::VIVO_QUICK_GAME => 'vivo快游戏',
        self::ALIPAY_MINI_GAME_LY => '支付宝小游戏联运',
        self::MEITUAN_MINI_GAME_LY => '美团小游戏联运',
        self::BAIDU_MINI_GAME_LY => '百度小游戏联运',
        self::BILIBILI_LY_CPS => '哔哩哔哩发行CPS',
        self::KUAIFA_LY => '快发-联运',
        self::HUAWEI_MONETIZATION => '华为流量变现',
        self::ZB_FANS => 'ZB粉丝群',
        self::VIDEO_HOT => '视频号加热',
        self::TENCENT_SOUGOU_SEARCH => '腾讯搜狗搜索'
    ];

    const MEDIA_MAP = [
        MediaType::TOUTIAO => self::TOUTIAO,
        MediaType::TENCENT => self::TENCENT,
        MediaType::KUAISHOU => self::KUAISHOU,
        MediaType::BAIDU => self::BAIDU,
        MediaType::MP => self::WX_MOMENTS,
        MediaType::UC => self::UC,
        MediaType::IQIYI => self::IQIYI,
        MediaType::SHENMA_SEARCH => self::SHENMA_SEARCH,
        MediaType::BAIDU_SEARCH => self::BAIDU_SEARCH,
        MediaType::QIHU_SEARCH => self::QIHU_SEARCH,
        MediaType::TOUTIAO_PINPAI_LIVE => self::TOUTIAO_PINPAI_LIVE,
        MediaType::ASA => self::ASA,
        MediaType::QIHU_JINGJIA => self::QIHU_JINGJIA,
        MediaType::BILIBILI => self::BILIBILI,
        MediaType::WEIBO => self::WEIBO,
        MediaType::DOUYIN => self::DOUYIN,
        MediaType::QIANCHUAN => self::QIANCHUAN,
        MediaType::ALIPAY => self::ALIPAY,
        MediaType::XINGTU => self::XINGTU,
        MediaType::HUAWEI => self::HUAWEI,
        MediaType::TAPTAP => self::TAPTAP,
        MediaType::OPPO => self::OPPO,
        MediaType::HUAWEI_JINGHONG => self::HUAWEI_JINGHONG,
        MediaType::VIVO => self::VIVO_LY,
        MediaType::HONOR => self::HONOR,
        MediaType::HUAWEI_MONETIZATION => self::HUAWEI_MONETIZATION,
    ];
}

