<?php

namespace App\Constant;

use App\Container;
use Common\EnvConfig;

class Environment
{
    const PROD = 'production';
    const CRON = 'cron';
    const LOCAL = 'local';
    const DEV = 'development';
    const TEST = 'test';

    // 体制内域名
    const INTERNAL_DOMAIN = [
        'dms.zx.com',
        'data.zx.com',
    ];

    // 体制内域名dev
    const INTERNAL_DOMAIN_DEV = [
        'dms-dev.zx.com', // dev的体制内
        'localhost:8099', // 本地
    ];

    /**
     * 判断当前访问的域名是否是体制外的
     *
     * @return int
     */
    static public function isExternalDomain()
    {
        if (EnvConfig::ENV == self::LOCAL) {
            return 0;
        } else {
            $internal_domain = EnvConfig::ENV === self::PROD ? self::INTERNAL_DOMAIN : self::INTERNAL_DOMAIN_DEV;
        }

        if (Container::getServer()) {
            if (!in_array(Container::getRequest()->header['host'], $internal_domain, true)) {
                return 1;
            }
        }

        return 0;
    }
}

