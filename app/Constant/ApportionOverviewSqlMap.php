<?php
/**
 * 分摊规则的总览SQL字段映射
 */

namespace App\Constant;


class ApportionOverviewSqlMap
{
    /**
     * sum类型的分摊字符串
     *
     * @var string
     */
    public $apportion_sum_str = '';

    /**
     * count类型的分摊字符串
     *
     * @var string
     */
    public $apportion_count_str = '';

    /**
     * @param int $apportion_type 是否分摊
     * @param int $preregister_apportion 是否预约分摊
     * @return void
     */
    public function setApportion(int $apportion_type, int $preregister_apportion = 0)
    {
        if ($apportion_type == 0) {
            $this->apportion_sum_str = '';
            $this->apportion_count_str = '';
        } else {
            // 选了预约分摊并且是自然量分摊的 sql 的情况下 自然量分摊用after_book_apportion_rate
            if ($preregister_apportion == 1 && $apportion_type === 1) {
                $this->apportion_sum_str = '* oda.after_book_apportion_rate';
                $this->apportion_count_str = ' * avg(oda.after_book_apportion_rate)';
            } else {
                $this->apportion_sum_str = '* oda.apportion_rate';
                $this->apportion_count_str = ' * avg(oda.apportion_rate)';
            }
        }
    }

    /**
     * 按根的login表
     *
     */
    public function getRootGameLoginTable()
    {
        return [
            'first_pay_count'      => "sum( IF ( DATEDIFF( t2.login_date, t1.root_game_first_pay_time ) + 1 = 1, 1, 0 )  $this->apportion_sum_str ) AS first_pay_count",
            'is_pay_second_login'  => "sum( IF ( DATEDIFF( t2.login_date, t1.root_game_first_pay_time ) + 1 = 2, 1, 0 )  $this->apportion_sum_str ) AS is_pay_second_login",
            'is_pay_third_login'   => "sum( IF ( DATEDIFF( t2.login_date, t1.root_game_first_pay_time ) + 1 = 3, 1, 0 )  $this->apportion_sum_str ) AS is_pay_third_login",
            'is_pay_seventh_login' => "sum( IF ( DATEDIFF( t2.login_date, t1.root_game_first_pay_time ) + 1 = 7, 1, 0 )  $this->apportion_sum_str ) AS is_pay_seventh_login",
            'is_pay_thirty_login'  => "sum( IF ( DATEDIFF( t2.login_date, t1.root_game_first_pay_time ) + 1 = 30, 1, 0 )  $this->apportion_sum_str ) AS is_pay_thirty_login",
        ];
    }

    /**
     * 按子的login表
     *
     */
    public function getLoginTable()
    {
        return [
            'first_pay_count'      => "sum( IF ( DATEDIFF( t2.login_date, t1.first_pay_time ) + 1 = 1, 1, 0 )  $this->apportion_sum_str ) AS first_pay_count",
            'is_pay_second_login'  => "sum( IF ( DATEDIFF( t2.login_date, t1.first_pay_time ) + 1 = 2, 1, 0 )  $this->apportion_sum_str ) AS is_pay_second_login",
            'is_pay_third_login'   => "sum( IF ( DATEDIFF( t2.login_date, t1.first_pay_time ) + 1 = 3, 1, 0 )  $this->apportion_sum_str ) AS is_pay_third_login",
            'is_pay_seventh_login' => "sum( IF ( DATEDIFF( t2.login_date, t1.first_pay_time ) + 1 = 7, 1, 0 )  $this->apportion_sum_str ) AS is_pay_seventh_login",
            'is_pay_thirty_login'  => "sum( IF ( DATEDIFF( t2.login_date, t1.first_pay_time ) + 1 = 30, 1, 0 )  $this->apportion_sum_str ) AS is_pay_thirty_login",
        ];
    }

    /**
     * 需要用到click表的指标
     */
    public function getClickTable()
    {
        return [
            'click_muid_distinct_count_ldy'      => "sum( IF (( t.ad_platform_id = 0 and t.type in (2,5)), t.day_click_muid_distinct_count, 0 ) $this->apportion_sum_str ) AS click_muid_distinct_count_ldy",
            'click_old_muid_count_ldy'           => "sum( IF ( (t.ad_platform_id = 0 and t.type in (2,5) AND is_old_root_game_muid = 1), t.day_click_count, 0 ) $this->apportion_sum_str) AS click_old_muid_count_ldy",
            'click_old_clique_muid_count_ldy'    => "sum( IF ( ((t.ad_platform_id = 0 and t.type in (2,5)) AND ( is_old_clique_game_muid = 1 OR is_old_root_game_muid = 1 )), day_click_count, 0 )  $this->apportion_sum_str) AS click_old_clique_muid_count_ldy",
            'click_ip_distinct_count_ldy'        => "sum( IF( (t.ad_platform_id = 0 and t.type in (2,5)), t.day_click_ip_distinct_count, 0 )  $this->apportion_sum_str) AS click_ip_distinct_count_ldy",
            'click_muid_distinct_count'          => "sum( IF ( t.type=1 or (t.type=4 and t.ad_platform_id !=0), t.day_click_muid_distinct_count, 0 )  $this->apportion_sum_str) AS click_muid_distinct_count",
            'click_old_muid_count'               => "sum( IF ( t.type=1 or (t.type=4 and t.ad_platform_id !=0) AND is_old_root_game_muid = 1, t.day_click_count, 0 )  $this->apportion_sum_str) AS click_old_muid_count",
            'click_old_clique_muid_count'        => "sum( IF (  t.type=1 or (t.type=4 and t.ad_platform_id !=0) AND ( is_old_clique_game_muid = 1 OR is_old_root_game_muid = 1 ), day_click_count, 0 )  $this->apportion_sum_str) AS click_old_clique_muid_count",
            'click_ip_distinct_count'            => "sum( IF (  t.type=1 or (t.type=4 and t.ad_platform_id !=0), t.day_click_ip_distinct_count, 0 )  $this->apportion_sum_str) AS click_ip_distinct_count",
            'click_count_ldy'                    => "sum( IF ( (t.ad_platform_id = 0 and t.type in (2,5)), t.day_click_count, 0 ) $this->apportion_sum_str) AS click_count_ldy",
            'click_muid_count_ldy'               => "sum( IF (t.ad_platform_id = 0 and t.type in (2,5), t.day_click_muid_count, 0 ) $this->apportion_sum_str) AS click_muid_count_ldy",
            'click_ip_count_ldy'                 => "sum(IF( (t.ad_platform_id = 0 and t.type in (2,5)), t.day_click_ip_count, 0 )$this->apportion_sum_str ) AS click_ip_count_ldy",
            'click_count'                        => "sum( IF ( t.type =1 or (t.type=4 and t.ad_platform_id !=0), t.day_click_count, 0 ) $this->apportion_sum_str) AS click_count",
            'click_muid_count'                   => "sum( IF (  t.type=1 or (t.type=4 and t.ad_platform_id !=0) , t.day_click_muid_count, 0 ) $this->apportion_sum_str) AS click_muid_count",
            'click_ip_count'                     => "sum(IF( t.type=1 or (t.type=4 and t.ad_platform_id !=0), t.day_click_ip_count, 0 ) $this->apportion_sum_str) AS click_ip_count",

            //         这里的指标说是不需要了  先留着防一手
            //        'click_count_yeyou_show' => "sum( IF ( t.type=1, t.day_click_count, 0 ) $this->apportion_sum_str ) AS click_count_yeyou_show",
            //        'click_ip_distinct_count_yeyou_show' => "sum( IF  ( t.type=1, t.day_click_ip_distinct_count, 0 ) $this->apportion_sum_str) AS click_ip_distinct_count_yeyou_show",
            //        'click_ip_count_yeyou_show' => "sum(IF(  t.type=1, t.day_click_ip_count, 0 ) $this->apportion_sum_str) AS click_ip_count_yeyou_show",
            //        'click_muid_distinct_count_yeyou_show' => "sum( IF (  t.type=1, t.day_click_muid_distinct_count, 0 ) $this->apportion_sum_str ) AS click_muid_distinct_count_yeyou_show",
            //        'click_old_muid_count_yeyou_show' => "sum( IF ( t.type=1 AND is_old_root_game_muid = 1, t.day_click_count, 0  ) $this->apportion_sum_str ) AS click_old_muid_count_yeyou_show",
            //        'click_old_clique_muid_count_yeyou_show' => "sum( IF ( t.type=1 AND ( is_old_clique_game_muid = 1 OR is_old_root_game_muid = 1 ), day_click_count, 0 ) $this->apportion_sum_str) AS click_old_clique_muid_count_yeyou_show",
            //        'click_muid_count_yeyou_show' => "sum( IF ( t.type=1, t.day_click_muid_count, 0 ) $this->apportion_sum_str) AS click_muid_count_yeyou_show",
            'click_count_ldy_show'               => "sum( IF ( ( t.ad_platform_id = 0 AND t.type IN ( 3, 4 ) ), t.day_click_count, 0 ) $this->apportion_sum_str ) AS click_count_ldy_show",
            'click_ip_count_ldy_show'            => "sum( IF ( ( t.ad_platform_id = 0 AND t.type IN ( 3, 4 ) ), t.day_click_ip_count, 0 ) $this->apportion_sum_str ) AS click_ip_count_ldy_show",
            'click_ip_distinct_count_ldy_show'   => "sum( IF ( ( t.ad_platform_id = 0 AND t.type IN ( 3, 4 ) ), t.day_click_ip_distinct_count, 0 ) $this->apportion_sum_str ) AS click_ip_distinct_count_ldy_show",
            'click_muid_count_ldy_show'          => "sum( IF ( (t.ad_platform_id = 0 AND t.type IN ( 3, 4 )), t.day_click_muid_count, 0) $this->apportion_sum_str) AS click_muid_count_ldy_show",
            'click_muid_distinct_count_ldy_show' => "sum( IF ( ( t.ad_platform_id = 0 AND t.type IN ( 3, 4 ) ), t.day_click_muid_distinct_count, 0 ) $this->apportion_sum_str ) AS click_muid_distinct_count_ldy_show",
        ];
    }

    public function getMediaShowTable()
    {
        return [
            "show_count"               => "sum( t.day_show_count $this->apportion_sum_str) AS show_count",
            "show_ip_distinct_count"   => "sum( t.day_show_ip_distinct_count $this->apportion_sum_str) AS show_ip_distinct_count",
            "show_ip_count"            => "sum( t.day_show_ip_count $this->apportion_sum_str) AS show_ip_count",
            "show_muid_distinct_count" => "sum( t.day_show_muid_distinct_count $this->apportion_sum_str) AS show_muid_distinct_count",
            "show_muid_count"          => "sum( t.day_show_muid_count $this->apportion_sum_str) AS show_muid_count",
        ];
    }

    /**
     * 需要用到action表的指标
     */
    public function getActionTable()
    {
        return [
            'action_muid_distinct_count'   => "sum( t.day_action_muid_distinct_count  $this->apportion_sum_str) AS action_muid_distinct_count",
            'action_ip_distinct_count'     => "sum( t.day_action_ip_distinct_count $this->apportion_sum_str ) AS action_ip_distinct_count",
            'action_old_muid_count'        => "sum( if(t.is_old_root_game_muid=1,t.day_action_muid_count,0) $this->apportion_sum_str ) AS action_old_muid_count",
            'action_old_clique_muid_count' => "sum( if(t.is_old_clique_game_muid=1 OR t.is_old_root_game_muid=1,t.day_action_count,0) $this->apportion_sum_str) AS action_old_clique_muid_count",
            'action_count'                 => "sum( t.day_action_count $this->apportion_sum_str) AS action_count",
            'action_muid_count'            => "sum( t.day_action_muid_count $this->apportion_sum_str) AS action_muid_count",
            'action_ip_count'              => "sum( t.day_action_ip_count $this->apportion_sum_str) AS action_ip_count",
        ];
    }

    /**
     * 需要用到reg表的指标
     * 按子的跟按根的如果相同，只配置这里就行，
     * 如果不同，则按根的需要再配置一下字段
     */
    public function getRegTable()
    {
        $plat_id_sql = PlatId::getMiniPlatIDSql();
        return [
            'reg_muid_count'                      => "sum( day_reg_muid_count $this->apportion_sum_str ) AS reg_muid_count",
            'reg_ip_count'                        => "sum( day_reg_ip_count $this->apportion_sum_str ) AS reg_ip_count",
            'reg_old_muid_count'                  => "sum( if(is_old_root_game_muid=1,day_reg_muid_count,0) $this->apportion_sum_str ) AS reg_old_muid_count",
            'reg_old_clique_muid_count'           => "sum( if(is_old_clique_game_muid=1 OR is_old_root_game_muid=1,day_reg_uid_count,0) $this->apportion_sum_str ) AS reg_old_clique_muid_count",
            'role_create_count'                   => "sum( is_create_role_count $this->apportion_sum_str ) AS role_create_count",
            'first_day_role_login_count'          => "sum( is_first_day_role_login_count $this->apportion_sum_str ) AS first_day_role_login_count",
            'reg_uid_count'                       => "sum( day_reg_uid_count $this->apportion_sum_str ) AS reg_uid_count",
            'reg_muid_distinct_count'             => "sum( day_reg_muid_distinct_count $this->apportion_sum_str ) AS reg_muid_distinct_count",
            'reg_ip_distinct_count'               => "sum( day_reg_ip_distinct_count $this->apportion_sum_str ) AS reg_ip_distinct_count",
            'second_login_count'                  => "sum( day_second_login_count $this->apportion_sum_str ) AS second_login_count",
            'first_day_pay_count'                 => "sum( day_first_day_pay_count $this->apportion_sum_str ) AS first_day_pay_count",
            'first_day_pay_money'                 => "sum( day_first_day_pay_money $this->apportion_sum_str ) AS first_day_pay_money",
            'reg_total_pay_count'                 => "sum( IF ( t.total_pay_money > 0, 1, 0 ) $this->apportion_sum_str ) AS reg_total_pay_count",
            'reg_total_pay_money'                 => "sum( t.total_pay_money $this->apportion_sum_str ) AS reg_total_pay_money",
            'lifetime_money'                      => "sum( IF( site.settlement_type = 'cps', 0, t.lifetime_money ) $this->apportion_sum_str ) AS lifetime_money",
            'pre_pay_way_ip_money'                => "sum( t.lifetime_money * t2.pay_way_ip_rate $this->apportion_sum_str ) AS pre_pay_way_ip_money",
            'pre_channel_pay'                     => "sum( t.lifetime_money * t2.channel_rate $this->apportion_sum_str)  AS pre_channel_pay",
            'pre_ios_pay'                         => "sum( t.lifetime_money * t2.ios_rate $this->apportion_sum_str)  AS pre_ios_pay",
            'pre_divide_pay_money'                => "sum( t.lifetime_money * t2.cp_percent $this->apportion_sum_str)AS pre_divide_pay_money",
            'pre_dehan_divide_money'              => "sum( t.lifetime_money * t2.dehan_rate $this->apportion_sum_str)AS pre_dehan_divide_money",
            'pre_server_money'                    => "sum( t.lifetime_money * t2.server_rate $this->apportion_sum_str)AS pre_server_money",
            'pre_applet_divide_money'             => "sum( t.lifetime_money * t2.applet_rate $this->apportion_sum_str)AS pre_applet_divide_money",
            'other_cost'                          => "sum( t.lifetime_money * t2.other_cost_rate $this->apportion_sum_str)AS other_cost",
            'day_main_uid_create_count'           => "sum( day_main_uid_create_count $this->apportion_sum_str ) AS day_main_uid_create_count",
            'create_role_uid_count'               => "count( DISTINCT t.platform, t.game_id, t.uid) $this->apportion_count_str AS create_role_uid_count",
            'day_three_login_uid_count'           => "sum( t.day_three_login_uid_count $this->apportion_sum_str ) AS day_three_login_uid_count",
            'day_three_login_muid_distinct_count' => "sum( t.day_three_login_muid_distinct_count $this->apportion_sum_str ) AS day_three_login_muid_distinct_count",
            'first_day_pay_times'                 => "sum( IF(date(game_reg_time)=date(first_pay_time),first_day_pay_times,0) $this->apportion_sum_str ) AS first_day_pay_times",
            'total_pay_times'                     => "sum(total_pay_times $this->apportion_sum_str) AS total_pay_times",
            'true_uid_count'                      => "sum( IF ( true_name != '', 1, 0 )  $this->apportion_sum_str) AS true_uid_count",
            'day_24_hour_pay_count'               => "sum(day_24_hour_pay_count $this->apportion_sum_str) AS day_24_hour_pay_count",
            'day_24_hour_pay_money'               => "sum(day_24_hour_pay_money $this->apportion_sum_str) AS day_24_hour_pay_money",
            'max_first_day_pay_money'             => "max( first_day_pay_money $this->apportion_sum_str ) AS max_first_day_pay_money",
            'xcx_role_create_count'               => "sum( IF ( game.plat_id IN $plat_id_sql, is_create_role_count, 0 ) ) $this->apportion_sum_str  AS xcx_role_create_count",
            "is_third_login"                      => "sum(is_third_login $this->apportion_sum_str) as is_third_login",
            "is_seventh_login"                    => "sum(is_seventh_login $this->apportion_sum_str) as is_seventh_login",
            "is_fifteenth_login"                  => "sum(is_fifteenth_login $this->apportion_sum_str) as is_fifteenth_login",
            "is_thirty_login"                     => "sum(is_thirty_login $this->apportion_sum_str) as is_thirty_login",
            "pre_game_360_pay"                    => "sum(day_first_day_pay_money * ifnull(multiple, 0) $this->apportion_sum_str) as pre_game_360_pay",
            "first_day_pay_money_within_6"        => "COUNT( first_day_pay_money BETWEEN 1 AND 6 OR NULL) $this->apportion_count_str as first_day_pay_money_within_6",
            "first_day_pay_money_within_18"       => "COUNT( first_day_pay_money BETWEEN 1 AND 18 OR NULL) $this->apportion_count_str as first_day_pay_money_within_18",
            "second_day_pay_money"                => "sum(second_day_pay_money$this->apportion_sum_str)  as second_day_pay_money",
            "third_day_pay_money"                 => "sum(third_day_pay_money$this->apportion_sum_str) as third_day_pay_money",
            "seventh_day_pay_money"               => "sum(seventh_day_pay_money$this->apportion_sum_str) as seventh_day_pay_money",
            "fifteenth_day_pay_money"             => "sum(fifteenth_day_pay_money$this->apportion_sum_str) as fifteenth_day_pay_money",
            "thirty_day_pay_money"                => "sum(thirty_day_pay_money$this->apportion_sum_str) as thirty_day_pay_money",
            "first_pay_24_hour_pay_count"         => "sum( IF ( first_pay_24_hour_pay_money > 0, 1, 0 ) $this->apportion_sum_str) AS first_pay_24_hour_pay_count",
            "first_pay_24_hour_pay_money"         => " sum( first_pay_24_hour_pay_money $this->apportion_sum_str) AS first_pay_24_hour_pay_money",
            "first_pay_24_hour_pay_times"         => " sum( first_pay_24_hour_pay_times $this->apportion_sum_str) AS first_pay_24_hour_pay_times",
            "first_day_pay_once_within_30"        => "COUNT(first_day_pay_times  = 1 AND first_day_pay_money BETWEEN 1 AND 30 OR NULL) AS first_day_pay_once_within_30",
            "is_second_muid_login"                => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_second_login = 1 OR NULL ) AS is_second_muid_login",
            "is_third_muid_login"                 => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_third_login = 1 OR NULL ) AS is_third_muid_login",
            "is_seventh_muid_login"               => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_seventh_login = 1 OR NULL ) AS is_seventh_muid_login",
            "is_fifteenth_muid_login"             => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_fifteenth_login = 1 OR NULL ) AS is_fifteenth_muid_login",
            "is_thirty_muid_login"                => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_thirty_login = 1 OR NULL ) AS is_thirty_muid_login",
            "profit_day_360_pay"                  => "profit_day_360_pay",
            "month_total_pay_money"               => "month_total_pay_money",
            "month_money"                         => "month_money",
            "profit_month_360_pay"                => "profit_month_360_pay",
            "key_role_level_count"                => "sum( if( t.max_role_level >= IFNULL( pbd.key_role_level, 0 ), 1, 0) $this->apportion_sum_str) AS key_role_level_count",
            "first_day_pay_once_key_money_count"  => "COUNT( root_game_first_day_pay_times = 1 AND root_game_first_day_pay_money BETWEEN 0.01 AND IFNULL( pbd.key_pay_money, 0 ) OR NULL ) $this->apportion_count_str AS first_day_pay_once_key_money_count"
        ];
    }

    /**
     * 需要用到reg表的指标 按根
     */
    public function getRootGameRegTable()
    {
        return [
            'reg_total_pay_count'           => "sum( IF ( t.root_game_total_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS reg_total_pay_count",
            'reg_total_pay_money'           => "sum( t.root_game_total_pay_money $this->apportion_sum_str ) AS reg_total_pay_money",
            'first_day_pay_times'           => "sum( IF(date(root_game_reg_time)=date(root_game_first_pay_time),root_game_first_day_pay_times,0)  $this->apportion_sum_str) AS first_day_pay_times",
            'total_pay_times'               => "sum(root_game_total_pay_times $this->apportion_sum_str) as total_pay_times",
            'max_first_day_pay_money'       => "max( root_game_first_day_pay_money $this->apportion_sum_str) AS max_first_day_pay_money",
            "second_day_pay_money"          => "sum(root_game_second_day_pay_money $this->apportion_sum_str)  as second_day_pay_money",
            "third_day_pay_money"           => "sum(root_game_third_day_pay_money $this->apportion_sum_str) as third_day_pay_money",
            "seventh_day_pay_money"         => "sum(root_game_seventh_day_pay_money $this->apportion_sum_str) as seventh_day_pay_money",
            "fifteenth_day_pay_money"       => "sum(root_game_fifteenth_day_pay_money $this->apportion_sum_str) as fifteenth_day_pay_money",
            "thirty_day_pay_money"          => "sum(root_game_thirty_day_pay_money $this->apportion_sum_str) as thirty_day_pay_money",
            "first_day_pay_money_within_6"  => "COUNT( root_game_first_day_pay_money BETWEEN 1 AND 6 OR NULL) $this->apportion_count_str as first_day_pay_money_within_6",
            "first_day_pay_money_within_18" => "COUNT( root_game_first_day_pay_money BETWEEN 1 AND 18 OR NULL) $this->apportion_count_str as first_day_pay_money_within_18",
            "first_day_pay_once_within_30"  => "COUNT( root_game_first_day_pay_times = 1 AND root_game_first_day_pay_money BETWEEN 1 AND 30 OR NULL) AS first_day_pay_once_within_30",
            "is_second_muid_login"          => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_second_login = 1 OR NULL ) AS is_second_muid_login",
            "is_third_muid_login"           => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_third_login = 1 OR NULL ) AS is_third_muid_login",
            "is_seventh_muid_login"         => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_seventh_login = 1 OR NULL ) AS is_seventh_muid_login",
            "is_fifteenth_muid_login"       => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_fifteenth_login = 1 OR NULL ) AS is_fifteenth_muid_login",
            "is_thirty_muid_login"          => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_thirty_login = 1 OR NULL ) AS is_thirty_muid_login",
            // 海外指标，按回流统计时(dimension_type=3)，字段内容包含有t.is_root_reg，所以用以下字段名（字段+dimension_type）来区分
            "is_second_muid_login_3"        => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_second_login = 1 OR NULL ) AS is_second_muid_login",
            "is_third_muid_login_3"         => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_third_login = 1 OR NULL ) AS is_third_muid_login",
            "is_seventh_muid_login_3"       => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_seventh_login = 1 OR NULL ) AS is_seventh_muid_login",
            "is_fifteenth_muid_login_3"     => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_fifteenth_login = 1 OR NULL ) AS is_fifteenth_muid_login",
            "is_thirty_muid_login_3"        => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv !='', TRUE, NULL ),t.is_thirty_login = 1 OR NULL ) AS is_thirty_muid_login",

        ];
    }

    /**
     * 需要用到old表的指标
     */
    public function getOldTable()
    {
        return [
            'uid_count'                 => "count(DISTINCT t.platform,t.game_id,t.uid) $this->apportion_count_str AS uid_count",
            'action_same_day_reg_count' => "count( DISTINCT t.platform, t.game_id, t.uid ,(t.login_date = DATE(t.game_reg_time) AND t.login_date = DATE(t.action_time) ) OR NULL)  $this->apportion_count_str AS action_same_day_reg_count",
            'reg_uid_new_pay_count'     => "count(DISTINCT t.platform,t.game_id,t.uid)  $this->apportion_count_str AS reg_uid_new_pay_count",
            'old_uid_count'             => "count(DISTINCT t.platform,t.game_id,t.uid) $this->apportion_count_str AS old_uid_count",
            'old_uid_pay_count'         => "count(DISTINCT t.platform,t.game_id,t.uid)  $this->apportion_count_str AS old_uid_pay_count",
            'muid_count'                => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL )) $this->apportion_count_str AS muid_count",
            'reg_muid_new_pay_count'    => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS reg_muid_new_pay_count",
            'old_muid_count'            => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS old_muid_count",
            'old_muid_pay_count'        => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS old_muid_pay_count",
        ];
    }

    /**
     * 需要用到old表的按根指标
     */
    public function getRootGameOldTable()
    {
        return [
            'uid_count'                 => "count(DISTINCT t.platform,t.root_game_id,t.uid) $this->apportion_count_str AS uid_count",
            'uid_count_3'               => "count(DISTINCT t.platform,t.root_game_id,t.uid,t.root_game_reg_time) $this->apportion_count_str AS uid_count",
            'action_same_day_reg_count' => "count( DISTINCT t.platform, t.root_game_id, t.uid ,(t.login_date = DATE(t.root_game_reg_time) AND t.login_date = DATE(t.action_time) ) OR NULL)  $this->apportion_count_str AS action_same_day_reg_count",
            'reg_uid_new_pay_count'     => "count(DISTINCT t.platform,t.root_game_id,t.uid) $this->apportion_count_str AS reg_uid_new_pay_count",
            'reg_uid_new_pay_count_3'   => "count(DISTINCT t.platform,t.root_game_id,t.uid,t.root_game_reg_time) $this->apportion_count_str AS reg_uid_new_pay_count",
            'old_uid_count'             => "count(DISTINCT t.platform,t.root_game_id,t.uid) $this->apportion_count_str AS old_uid_count",
            'old_uid_pay_count'         => "count(DISTINCT t.platform,t.root_game_id,t.uid) $this->apportion_count_str AS old_uid_pay_count",
            'muid_count'                => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL )) $this->apportion_count_str AS muid_count",
            'reg_muid_new_pay_count'    => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS reg_muid_new_pay_count",
            'old_muid_count'            => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS old_muid_count",
            'old_muid_pay_count'        => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS old_muid_pay_count",
            // 海外指标，按回流统计时(dimension_type=3)，字段内容包含有t.is_root_reg，所以用以下字段名（字段+dimension_type）来区分
            'muid_count_3'              => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL )) $this->apportion_count_str AS muid_count",
            'reg_muid_new_pay_count_3'  => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS reg_muid_new_pay_count",
            'old_muid_count_3'          => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS old_muid_count",
            'old_muid_pay_count_3'      => "count( DISTINCT t.platform,t.game_id,t.agent_id,t.site_id,t.account_id,t.account_name,t.ad_platform_id,t.campaign_id,t.adgroup_id,t.adcre_id,t.csite,t.ctype,t.is_old_root_game_muid,t.is_old_root_pay_muid,t.is_old_clique_game_muid,t.is_old_clique_pay_muid,t.device_model,t.system_version,DATE(t.root_game_reg_time),t.is_simulator,t.is_root_reg, t.muid, t.moaid, t.idfv, IF ( t.muid != '' OR t.moaid != '' OR t.idfv != '', TRUE, NULL ) ) $this->apportion_count_str AS old_muid_pay_count",

        ];
    }

    /**
     * 需要用到pay表的指标
     */
    public function getPayTable()
    {
        // old_uid_pay_money  这个要特殊处理，不在这配置
        return [
            'total_pay_money'              => "sum(day_pay_money $this->apportion_sum_str) AS total_pay_money",
            'new_pay_day_pay_money'        => "sum(IF ( pay_date =first_pay_date, day_pay_money, 0 )  $this->apportion_sum_str) AS new_pay_day_pay_money",
            'new_pay_day_pay_uid_count'    => "sum(IF ( pay_date =first_pay_date, day_pay_uid_count, 0 )  $this->apportion_sum_str) AS new_pay_day_pay_uid_count",
            'all_total_pay_times'          => "sum(day_pay_times $this->apportion_sum_str) AS all_total_pay_times",
            'callback_total_day_pay_money' => "sum( callback_day_pay_money $this->apportion_sum_str) AS callback_total_day_pay_money",
            'callback_day_pay_count'       => "sum( callback_day_pay_count $this->apportion_sum_str) as callback_day_pay_count",
            'callback_total_day_pay_times' => "sum(callback_day_pay_times $this->apportion_sum_str) as callback_total_day_pay_times",
            "lifetime_money_cps"           => "sum( IF( site.settlement_type = 'cps',  t.day_pay_money , 0) $this->apportion_sum_str) AS lifetime_money_cps",
            "pre_channel_pay_cps"          => "sum( t.day_pay_money * t2.channel_rate $this->apportion_sum_str) AS pre_channel_pay_cps",
            "pre_ios_pay_cps"              => "sum( t.day_pay_money * t2.ios_rate $this->apportion_sum_str) AS pre_ios_pay_cps",
            "pre_divide_pay_money_cps"     => "sum( t.day_pay_money * t2.cp_percent $this->apportion_sum_str) AS pre_divide_pay_money_cps",
            "pre_dehan_divide_money_cps"   => "sum( t.day_pay_money * t2.dehan_rate $this->apportion_sum_str) AS pre_dehan_divide_money_cps",
            "pre_server_money_cps"         => "sum( t.day_pay_money * t2.server_rate $this->apportion_sum_str) AS pre_server_money_cps",
            "pre_applet_divide_money_cps"  => "sum( t.day_pay_money * t2.applet_rate $this->apportion_sum_str) AS pre_applet_divide_money_cps",
            "pre_pay_way_ip_money_cps"     => "sum( t.day_pay_money * t2.pay_way_ip_rate $this->apportion_sum_str) AS pre_pay_way_ip_money_cps",
            "other_cost_cps"               => "sum( t.day_pay_money * t2.other_cost_rate $this->apportion_sum_str) AS other_cost_cps",
            "old_uid_pay_money"            => "sum(IF(game_reg_date<YEAR_MONTH(pay_date),day_pay_money,0)  $this->apportion_sum_str) AS old_uid_pay_money",
        ];
    }

    // 用户分群的按根reg指标
    public function getUserGroupRootGameRegTable()
    {
        return [
            'first_day_pay_times'                 => "sum( IF ( date( root_game_reg_time ) = date( root_game_first_pay_time ), root_game_first_day_pay_times, 0 )  $this->apportion_sum_str) AS first_day_pay_times",
            'total_pay_times'                     => "sum( root_game_total_pay_times  $this->apportion_sum_str) AS total_pay_times",
            'reg_total_pay_count'                 => "sum( IF ( t.root_game_total_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS reg_total_pay_count",
            'reg_total_pay_money'                 => "sum( t.root_game_total_pay_money  $this->apportion_sum_str) AS reg_total_pay_money",
            'lifetime_money'                      => "sum( t.lifetime_money  $this->apportion_sum_str) AS lifetime_money",
            'pre_pay_way_ip_money'                => "sum( t.lifetime_money * t2.pay_way_ip_rate $this->apportion_sum_str) AS pre_pay_way_ip_money",
            'true_uid_count'                      => "sum( IF ( t.true_name != '', 1, 0 )  $this->apportion_sum_str) AS true_uid_count",
            'day_24_hour_pay_count'               => "sum( IF ( t.root_game_reg_24_hour_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS day_24_hour_pay_count",
            'day_24_hour_pay_money'               => "sum( t.root_game_reg_24_hour_pay_money  $this->apportion_sum_str) AS day_24_hour_pay_money",
            'reg_uid_count'                       => "count(1) $this->apportion_count_str AS reg_uid_count",
            'reg_old_muid_count'                  => "sum( IF ( t.is_old_root_game_muid = 1, 1, 0 )  $this->apportion_sum_str) AS reg_old_muid_count",
            'reg_old_clique_muid_count'           => "SUM( IF ( t.is_old_clique_game_muid = 1 OR t.is_old_root_game_muid = 1, 1, 0 )  $this->apportion_sum_str) AS reg_old_clique_muid_count",
            'reg_muid_count'                      => "sum( IF ( muid != '' OR moaid != '' OR idfv != '', 1, 0 )  $this->apportion_sum_str) AS reg_muid_count",
            'reg_muid_distinct_count'             => "count( DISTINCT muid, moaid, idfv, IF ( muid != '' OR moaid != '' OR idfv != '', TRUE, NULL )) $this->apportion_count_str AS reg_muid_distinct_count",
            'reg_ip_count'                        => "sum( IF ( ip != '', 1, 0 )  $this->apportion_sum_str) AS reg_ip_count",
            'reg_ip_distinct_count'               => "count( DISTINCT ip, IF ( ip != '', TRUE, NULL )) $this->apportion_count_str AS reg_ip_distinct_count",
            'second_login_count'                  => "sum( is_second_login  $this->apportion_sum_str) AS second_login_count",
            'first_day_pay_count'                 => "sum( IF ( root_game_first_day_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS first_day_pay_count",
            'first_day_pay_money'                 => "sum( root_game_first_day_pay_money  $this->apportion_sum_str) AS first_day_pay_money",
            'day_three_login_uid_count'           => "sum( IF ( root_game_reg_24_hour_login_times >= 3, 1, 0 )  $this->apportion_sum_str) AS day_three_login_uid_count",
            'day_three_login_muid_distinct_count' => "count(DISTINCT muid,moaid,idfv,IF( root_game_reg_24_hour_login_times >= 3 AND ( muid != '' OR moaid != '' OR idfv != '' ), TRUE, NULL )) $this->apportion_count_str AS day_three_login_muid_distinct_count",
            'role_create_count'                   => "sum( is_create_role  $this->apportion_sum_str) AS role_create_count",
            'first_day_role_login_count'          => "sum( is_first_day_role_login  $this->apportion_sum_str) AS first_day_role_login_count",
            "is_third_login"                      => "sum(is_third_login $this->apportion_sum_str) as is_third_login",
            "is_seventh_login"                    => "sum(is_seventh_login $this->apportion_sum_str) as is_seventh_login",
            "is_fifteenth_login"                  => "sum(is_fifteenth_login $this->apportion_sum_str) as is_fifteenth_login",
            "is_thirty_login"                     => "sum(is_thirty_login $this->apportion_sum_str) as is_thirty_login",
            "first_day_pay_money_within_6"        => "COUNT( root_game_first_day_pay_money BETWEEN 1 AND 6 OR NULL) $this->apportion_count_str as first_day_pay_money_within_6",
            "first_day_pay_money_within_18"       => "COUNT( root_game_first_day_pay_money BETWEEN 1 AND 18 OR NULL) $this->apportion_count_str as first_day_pay_money_within_18",
            "first_day_pay_once_within_30"        => "COUNT( root_game_first_day_pay_times = 1 AND root_game_first_day_pay_money BETWEEN 1 AND 30 OR NULL) $this->apportion_count_str AS first_day_pay_once_within_30",
        ];
    }


    /**
     *  消耗表的指标
     */
    public function getCostTable()
    {
        return [
            'cost_money'             => "sum( IF ( IFNULL( standard.standard_days, 0 ) IN ( 1, 0 ), money, 0 ) $this->apportion_sum_str ) AS cost_money",
            'ori_money'              => "sum( IF ( IFNULL( standard.standard_days, 0 ) IN ( 1, 0 ), ori_money, 0 ) $this->apportion_sum_str ) AS ori_money",
            'total_standard_value'   => "sum( t.money * IF ( standard.standard_days = 1, standard.standard_value, 0 ) $this->apportion_sum_str ) AS total_standard_value",
            'seventh_standard_value' => "sum( t.money * IF ( standard.standard_days = 7, standard.standard_value, 0 ) $this->apportion_sum_str ) AS seventh_standard_value",
            'thirty_standard_value'  => "sum( t.money * IF ( standard.standard_days = 30, standard.standard_value, 0 ) $this->apportion_sum_str ) AS thirty_standard_value",
            'anchor_cost'            => "sum( IF ( IFNULL( standard.standard_days, 0 ) IN ( 1, 0 ) AND add_type = 18, money, 0 ) $this->apportion_sum_str ) AS anchor_cost",
        ];
    }

    /**
     * 用户分群用到pay表的按子的指标
     */
    public function getUserGroupPayTable()
    {

        return [
            'old_uid_pay_money'            => "sum(IF(game_reg_date<YEAR_MONTH(pay_date),day_pay_money,0) $this->apportion_sum_str) AS old_uid_pay_money",
            'total_pay_money'              => "sum( IF ( t.order_status_id = 1, pay_money, 0 )  $this->apportion_sum_str) AS total_pay_money",
            'new_pay_day_pay_money'        => "sum(IF( Date( t.pay_time ) = Date( t2.first_pay_time ) AND t.order_status_id = 1, t.pay_money, 0 ) $this->apportion_sum_str) AS new_pay_day_pay_money",
            'new_pay_day_pay_uid_count'    => "COUNT( DISTINCT IF ( DATE( t.pay_time ) = DATE( t2.first_pay_time ), t.uid, NULL )) $this->apportion_count_str AS new_pay_day_pay_uid_count",
            'all_total_pay_times'          => "COUNT( IF ( t.order_status_id = 1, 1, NULL )) $this->apportion_count_str AS all_total_pay_times",
            'callback_total_day_pay_money' => "sum( callback_day_pay_money $this->apportion_sum_str) AS callback_total_day_pay_money",
            'callback_day_pay_count'       => "sum( callback_day_pay_count $this->apportion_sum_str) as callback_day_pay_count",
            'callback_total_day_pay_times' => "sum(callback_day_pay_times $this->apportion_sum_str) as callback_total_day_pay_times",
        ];
    }

    /**
     * 用户分群用到pay表的按根的指标
     */
    public function getUserGroupRootGamePayTable()
    {
        return [
            'old_uid_pay_money'            => "sum(IF(game_reg_date<YEAR_MONTH(pay_date),day_pay_money,0) $this->apportion_sum_str) AS old_uid_pay_money",
            'total_pay_money'              => "sum( IF ( t.order_status_id = 1, pay_money, 0 )  $this->apportion_sum_str) AS total_pay_money",
            'new_pay_day_pay_money'        => "sum(IF( Date( t.pay_time ) = Date( t2.root_game_first_pay_time ) AND t.order_status_id = 1, t.pay_money, 0 ) $this->apportion_sum_str) AS new_pay_day_pay_money",
            'new_pay_day_pay_uid_count'    => "COUNT( DISTINCT IF ( DATE( t.pay_time ) = DATE( t2.root_game_first_pay_time ), t.uid, NULL ) ) $this->apportion_count_str AS new_pay_day_pay_uid_count",
            'all_total_pay_times'          => "COUNT( IF ( t.order_status_id = 1, 1, NULL )) $this->apportion_count_str AS all_total_pay_times",
            'callback_total_day_pay_money' => "sum( callback_day_pay_money $this->apportion_sum_str) AS callback_total_day_pay_money",
            'callback_day_pay_count'       => "sum( callback_day_pay_count $this->apportion_sum_str) as callback_day_pay_count",
            'callback_total_day_pay_times' => "sum(callback_day_pay_times $this->apportion_sum_str) as callback_total_day_pay_times",
        ];
    }

    /**
     * 用户分群的按子reg指标
     */
    public function getUserGroupRegTable()
    {

        return [
            'first_day_pay_times'                 => "sum( IF ( date( game_reg_time ) = date( first_pay_time ), first_day_pay_times, 0 )  $this->apportion_sum_str) AS first_day_pay_times",
            'total_pay_times'                     => "sum( total_pay_times  $this->apportion_sum_str) AS total_pay_times",
            'reg_total_pay_count'                 => "sum( IF ( t.total_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS reg_total_pay_count",
            'reg_total_pay_money'                 => "sum( t.total_pay_money  $this->apportion_sum_str) AS reg_total_pay_money",
            'lifetime_money'                      => "sum( t.lifetime_money  $this->apportion_sum_str) AS lifetime_money",
            'pre_pay_way_ip_money'                => "sum( t.lifetime_money * t2.pay_way_ip_rate $this->apportion_sum_str) AS pre_pay_way_ip_money",
            'true_uid_count'                      => "sum( IF ( t.true_name != '', 1, 0 )  $this->apportion_sum_str) AS true_uid_count",
            'day_24_hour_pay_count'               => "sum( IF ( t.game_reg_24_hour_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS day_24_hour_pay_count",
            'day_24_hour_pay_money'               => "sum( t.game_reg_24_hour_pay_money  $this->apportion_sum_str) AS day_24_hour_pay_money",
            'reg_uid_count'                       => "count( 1 ) $this->apportion_count_str AS reg_uid_count",
            'reg_old_muid_count'                  => "sum( IF ( t.is_old_root_game_muid = 1, 1, 0 )  $this->apportion_sum_str) AS reg_old_muid_count",
            'reg_old_clique_muid_count'           => "SUM( IF ( t.is_old_clique_game_muid = 1 OR t.is_old_root_game_muid = 1, 1, 0 )  $this->apportion_sum_str) AS reg_old_clique_muid_count",
            'reg_muid_count'                      => "sum( IF ( muid != '' OR moaid != '' OR idfv != '', 1, 0 )  $this->apportion_sum_str) AS reg_muid_count",
            'reg_muid_distinct_count'             => "count( DISTINCT muid, moaid, idfv, IF ( muid != '' OR moaid != '' OR idfv != '', TRUE, NULL )) $this->apportion_count_str AS reg_muid_distinct_count",
            'reg_ip_count'                        => "sum( IF ( ip != '', 1, 0 )  $this->apportion_sum_str) AS reg_ip_count",
            'reg_ip_distinct_count'               => "count( DISTINCT ip, IF ( ip != '', TRUE, NULL )) $this->apportion_count_str AS reg_ip_distinct_count",
            'second_login_count'                  => "sum( is_second_login  $this->apportion_sum_str) AS second_login_count",
            'first_day_pay_count'                 => "sum( IF ( first_day_pay_money > 0, 1, 0 )  $this->apportion_sum_str) AS first_day_pay_count",
            'first_day_pay_money'                 => "sum( first_day_pay_money  $this->apportion_sum_str) AS first_day_pay_money",
            'day_three_login_uid_count'           => "sum( IF ( game_reg_24_hour_login_times >= 3, 1, 0 )  $this->apportion_sum_str) AS day_three_login_uid_count",
            'day_three_login_muid_distinct_count' => "count(DISTINCT muid,moaid,idfv,IF( game_reg_24_hour_login_times >= 3 AND ( muid != '' OR moaid != '' OR idfv != '' ), TRUE, NULL )) $this->apportion_count_str AS day_three_login_muid_distinct_count",
            'role_create_count'                   => "sum( is_create_role  $this->apportion_sum_str) AS role_create_count",
            'first_day_role_login_count'          => "sum( is_first_day_role_login  $this->apportion_sum_str) AS first_day_role_login_count",
            "is_third_login"                      => "sum(is_third_login $this->apportion_sum_str) as is_third_login",
            "is_seventh_login"                    => "sum(is_seventh_login $this->apportion_sum_str) as is_seventh_login",
            "is_fifteenth_login"                  => "sum(is_fifteenth_login $this->apportion_sum_str) as is_fifteenth_login",
            "is_thirty_login"                     => "sum(is_thirty_login $this->apportion_sum_str) as is_thirty_login",
            "first_day_pay_money_within_6"        => "COUNT( first_day_pay_money BETWEEN 1 AND 6 OR NULL) $this->apportion_count_str as first_day_pay_money_within_6",
            "first_day_pay_money_within_18"       => "COUNT( first_day_pay_money BETWEEN 1 AND 18 OR NULL) $this->apportion_count_str as first_day_pay_money_within_18",
            "first_day_pay_once_within_30"        => "COUNT( first_day_pay_times = 1 AND first_day_pay_money BETWEEN 1 AND 30 OR NULL) $this->apportion_count_str AS first_day_pay_once_within_30",
        ];
    }

    /**
     * 需要用到official_pay表的指标
     */
    public function getOfficialPayTable()
    {
        return [
            'official_pay_count' => "count(DISTINCT (if( t.is_official = 1,( t.platform, t.game_id, t.uid),NULL) ))  $this->apportion_count_str AS official_pay_count",
            'official_pay_money' => "sum(IF( t.is_official = 1, t.pay_money, 0 )  $this->apportion_sum_str) AS official_pay_money",
            'pay_money'          => "sum( IF ( t.order_status_id = 1, t.pay_money, t.pay_money *- 1 ) $this->apportion_sum_str) AS pay_money",
        ];
    }


    /**
     * Profit表的指标
     */
    public function getProfitTable()
    {
        return [
            'game_pay_money'   => "sum(IF( order_status_id = 1, pay_money, pay_money *- 1 )) AS game_pay_money",
            "divide_pay_money" => "sum( IF ( order_status_id = 1, pay_money, pay_money *- 1 ) * ( ifnull( t4.divide_percent, 0 ) ) ) AS divide_pay_money",
            "apple_pay_money"  => "cast((sum(IF(( t1.platform IN ( 'TW' ) AND t1.pay_way_id = 69 )   OR ( t1.platform IN ( '' )
                                AND t1.pay_way_id IN ( 3, 14, 33, 35 ) ),pay_money,0 ) ) ) * IFNULL( t3.ios_divide, 0 ) AS DECIMAL ( 12, 4 ) ) AS apple_pay_money",
            "yyb_pay_money"    => "cast((sum(IF(( t1.platform IN ( 'TW' ) AND t1.pay_way_id = 63 AND t1.channel_id = 56 ) 
		                     OR ( t1.platform IN ( '' ) AND t1.pay_way_id = 99 ),pay_money,0)) ) * IFNULL( t3.yyb_divide, 0 ) AS DECIMAL ( 12, 4 ) ) AS yyb_pay_money",

            "pay_way_pay_money" => "cast(sum(IF ((t1.platform IN ('TW') AND t1.pay_way_id=69) OR (t1.platform IN ('') 
                                AND t1.pay_way_id IN (3,14,33,35)) 
                                AND order_status_id=1,pay_money,0))*IFNULL(t3.ios_divide,0)+sum(IF ((t1.platform IN ('TW') 
                                AND t1.pay_way_id=63 AND t1.channel_id=56) OR (t1.platform IN ('') AND t1.pay_way_id=99) 
                                AND order_status_id=1,pay_money,0))*IFNULL(t3.yyb_divide,0)+sum(IF ((t1.platform IN ('TW') 
                                AND (t1.pay_way_id !=69 AND (t1.pay_way_id !=63 OR t1.channel_id !=56))) OR (t1.platform IN ('') 
                                AND (t1.pay_way_id NOT IN (3,14,33,35) AND (t1.pay_way_id !=99))) 
                                AND order_status_id=1,pay_money*pay_way_proportion/10000,0)) AS DECIMAL (12,4)) 
                                AS pay_way_pay_money",
        ];

    }


    /**
     * 需要用到INSPIRE表的指标
     */
    public function getInspireTable()
    {
        return [
            'inspire_show'  => "SUM( t2.show $this->apportion_sum_str) AS inspire_show",
            'inspire_click' => "SUM( t2.click $this->apportion_sum_str) AS inspire_click",
            'inspire_cost'  => "SUM( t2.cost $this->apportion_sum_str) AS inspire_cost",
        ];
    }


    /**
     * 需要用到的预估付费
     */
    public function getPreDictPayTable()
    {
        return [
            'tpay_360' => "SUM( t.day_360_total_pay_money * ifnull( his.final_360_rate, 1 ) $this->apportion_sum_str ) AS tpay_360",
        ];
    }

    public function getTeamConfigDataTable()
    {
        return [
            "is_third_login"   => "sum(is_third_login $this->apportion_sum_str) AS is_third_login",
            "is_seventh_login" => "sum(is_seventh_login $this->apportion_sum_str) AS is_seventh_login",
        ];
    }

    public function getRegSourceTable()
    {
        return [
            'xcx_ori_reg_count' => "count( 1 ) $this->apportion_count_str AS xcx_ori_reg_count",
        ];
    }

    public function getMediaAD3Table()
    {
        return [
            'media_show_count'         => "sum( t.show $this->apportion_sum_str) AS media_show_count",
            'media_click_count'        => "sum( t.click $this->apportion_sum_str) AS media_click_count",
            'media_convert_count'      => "sum( t.convert $this->apportion_sum_str) AS media_convert_count",
            'media_deep_convert_count' => "sum( t.deep_convert $this->apportion_sum_str) AS media_deep_convert_count",
        ];
    }


    /**
     * 所有指标
     */
    const ALL_TARGET_MAP = [
        'click_ip_ldy_same_percent'               => ['click_ip_ldy_same_percent', 'click_ip_distinct_count_ldy', 'click_ip_count_ldy'],
        'click_muid_ldy_same_percent'             => ['click_muid_ldy_same_percent', 'click_muid_distinct_count_ldy', 'click_muid_count_ldy'],
        'click_old_muid_count_ldy_percent'        => ['click_old_muid_count_ldy_percent', 'click_old_muid_count_ldy', 'click_count_ldy'],
        'click_ip_same_percent'                   => ['click_ip_same_percent', 'click_ip_distinct_count', 'click_ip_count'],
        'click_muid_same_percent'                 => ['click_muid_same_percent', 'click_muid_distinct_count', 'click_muid_count'],
        'click_old_muid_count_percent'            => ['click_old_muid_count_percent', 'click_old_muid_count', 'click_count'],
        'action_ip_same_percent'                  => ['action_ip_same_percent', 'action_ip_distinct_count', 'action_ip_count'],
        'action_muid_same_percent'                => ['action_muid_same_percent', 'action_muid_distinct_count', 'action_muid_count'],
        'action_old_muid_count_percent'           => ['action_old_muid_count_percent', 'action_old_muid_count', 'action_count'],
        'reg_ip_same_percent'                     => ['reg_ip_same_percent', 'reg_ip_distinct_count', 'reg_ip_count'],
        'reg_muid_same_percent'                   => ['reg_muid_same_percent', 'reg_muid_distinct_count', 'reg_muid_count'],
        'reg_old_muid_count_percent'              => ['reg_old_muid_count_percent', 'reg_old_muid_count', 'reg_muid_count'],
        'role_create_count_percent'               => ['role_create_count_percent', 'role_create_count', 'reg_uid_count'],
        'first_day_role_login_count_percent'      => ['first_day_role_login_count_percent', 'first_day_role_login_count', 'reg_uid_count'],
        'cost_per_reg'                            => ['cost_per_reg', 'cost_money', 'reg_uid_count'],
        'cost_per_pay'                            => ['cost_per_pay', 'cost_money', 'reg_total_pay_count'],
        'cost_per_second_login'                   => ['cost_per_second_login', 'cost_money', 'second_login_count'],
        'cost_per_first_day_pay'                  => ['cost_per_first_day_pay', 'cost_money', 'first_day_pay_count'],
        'first_day_roi'                           => ['first_day_roi', 'first_day_pay_money', 'cost_money'],
        'roi'                                     => ['roi', 'reg_total_pay_money', 'cost_money'],
        'first_day_pay_percent'                   => ['first_day_pay_percent', 'first_day_pay_count', 'reg_uid_count'],
        'first_day_arppu'                         => ['first_day_arppu', 'first_day_pay_money', 'first_day_pay_count'],
        'first_day_ltv'                           => ['first_day_ltv', 'first_day_pay_money', 'reg_uid_count'],
        'cost_per_day_24_hour_pay'                => ['cost_per_day_24_hour_pay', 'cost_money', 'day_24_hour_pay_count'],
        'day_24_hour_roi'                         => ['day_24_hour_roi', 'day_24_hour_pay_money', 'cost_money'],
        'day_24_hour_pay_percent'                 => ['day_24_hour_pay_percent', 'day_24_hour_pay_count', 'reg_uid_count'],
        'day_24_hour_arppu'                       => ['day_24_hour_arppu', 'day_24_hour_pay_money', 'day_24_hour_pay_count'],
        'day_24_hour_ltv'                         => ['day_24_hour_ltv', 'day_24_hour_pay_money', 'reg_uid_count'],
        'second_login_percent'                    => ['second_login_percent', 'second_login_count', 'reg_uid_count'],
        'arppu'                                   => ['arppu', 'reg_total_pay_money', 'reg_total_pay_count'],
        'ltv'                                     => ['ltv', 'reg_total_pay_money', 'reg_uid_count'],
        'total_pay_percent'                       => ['total_pay_percent', 'reg_total_pay_count', 'reg_uid_count'],
        'range_new_arpu'                          => ['range_new_arpu', 'total_pay_money', 'old_uid_pay_money', 'reg_uid_count'],
        'range_new_arppu'                         => ['range_new_arppu', 'total_pay_money', 'old_uid_pay_money', 'reg_uid_new_pay_count'],
        'reg_uid_new_pay_percent'                 => ['reg_uid_new_pay_percent', 'reg_uid_new_pay_count', 'reg_uid_count'],
        'range_old_arpu'                          => ['range_old_arpu', 'old_uid_pay_money', 'old_uid_count'],
        'range_old_arppu'                         => ['range_old_arppu', 'old_uid_pay_money', 'old_uid_pay_count'],
        'old_uid_pay_percent'                     => ['old_uid_pay_percent', 'old_uid_pay_count', 'old_uid_count'],
        'range_arpu'                              => ['range_arpu', 'total_pay_money', 'uid_count'],
        'range_arppu'                             => ['range_arppu', 'total_pay_money', 'old_uid_pay_count', 'reg_uid_new_pay_count'],
        'range_pay_count'                         => ['range_pay_count', 'old_uid_pay_count', 'reg_uid_new_pay_count'],
        'reg_uid_new_pay_money'                   => ['reg_uid_new_pay_money', 'total_pay_money', 'old_uid_pay_money'],
        'range_uid_pay_percent'                   => ['range_uid_pay_percent', 'reg_uid_new_pay_count', 'old_uid_pay_count', 'uid_count'],
        'click_ldy_action_percent'                => ['click_ldy_action_percent', 'action_count', 'click_count_ldy'],
        'click_action_percent'                    => ['click_action_percent', 'action_count', 'click_count'],
        'click_ldy_uid_reg_percent'               => ['click_ldy_uid_reg_percent', 'reg_uid_count', 'click_count_ldy'],
        'click_uid_reg_percent'                   => ['click_uid_reg_percent', 'reg_uid_count', 'click_count'],
        'click_ldy_role_create_percent'           => ['click_ldy_role_create_percent', 'role_create_count', 'click_count_ldy'],
        'click_role_create_percent'               => ['click_role_create_percent', 'role_create_count', 'click_count'],
        'action_uid_reg_percent'                  => ['action_uid_reg_percent', 'reg_uid_count', 'action_count'],
        'action_role_create_percent'              => ['action_role_create_percent', 'role_create_count', 'action_count'],
        'click_old_clique_muid_count_percent'     => ['click_old_clique_muid_count_percent', 'click_old_clique_muid_count', 'click_count'],
        'action_old_clique_muid_count_percent'    => ['action_old_clique_muid_count_percent', 'action_old_clique_muid_count', 'action_muid_count'],
        'click_old_clique_muid_count_ldy_percent' => ['click_old_clique_muid_count_ldy_percent', 'click_old_clique_muid_count_ldy', 'click_count_ldy'],
        'reg_old_clique_muid_count_percent'       => ['reg_old_clique_muid_count_percent', 'reg_old_clique_muid_count', 'reg_muid_count'],
        'per_role_create_cost'                    => ['per_role_create_cost', 'role_create_count', 'cost_money'],
        'action_reg_rate'                         => ['reg_uid_count', 'action_count', 'action_reg_rate'],
        'click_count_ldy'                         => ['click_count_ldy'],
        'role_create_count'                       => ['role_create_count'],
        'click_ip_count_ldy'                      => ['click_ip_count_ldy'],
        'click_ip_distinct_count_ldy'             => ['click_ip_distinct_count_ldy'],
        'click_muid_count_ldy'                    => ['click_muid_count_ldy'],
        'click_muid_distinct_count_ldy'           => ['click_muid_distinct_count_ldy'],
        'click_old_muid_count_ldy'                => ['click_old_muid_count_ldy'],
        'click_count'                             => ['click_count'],
        'click_ip_count'                          => ['click_ip_count'],
        'click_ip_distinct_count'                 => ['click_ip_distinct_count'],
        'click_muid_count'                        => ['click_muid_count'],
        'click_muid_distinct_count'               => ['click_muid_distinct_count'],
        'click_old_muid_count'                    => ['click_old_muid_count'],
        'action_count'                            => ['action_count'],
        'action_ip_count'                         => ['action_ip_count'],
        'action_ip_distinct_count'                => ['action_ip_distinct_count'],
        'action_muid_count'                       => ['action_muid_count'],
        'action_muid_distinct_count'              => ['action_muid_distinct_count'],
        'action_old_muid_count'                   => ['action_old_muid_count'],
        'reg_uid_count'                           => ['reg_uid_count'],
        'reg_ip_count'                            => ['reg_ip_count'],
        'reg_ip_distinct_count'                   => ['reg_ip_distinct_count'],
        'reg_muid_count'                          => ['reg_muid_count'],
        'reg_muid_distinct_count'                 => ['reg_muid_distinct_count'],
        'reg_old_muid_count'                      => ['reg_old_muid_count'],
        'first_day_pay_count'                     => ['first_day_pay_count'],
        'first_day_pay_money'                     => ['first_day_pay_money'],
        'day_24_hour_pay_count'                   => ['day_24_hour_pay_count'],
        'day_24_hour_pay_money'                   => ['day_24_hour_pay_money'],
        'second_login_count'                      => ['second_login_count'],
        'reg_total_pay_count'                     => ['reg_total_pay_count'],
        'reg_total_pay_money'                     => ['reg_total_pay_money'],
        'pre_pay_way_ip_money'                    => ['pre_pay_way_ip_money'],
        'reg_uid_new_pay_count'                   => ['reg_uid_new_pay_count'],
        'old_uid_count'                           => ['old_uid_count'],
        'old_uid_pay_count'                       => ['old_uid_pay_count'],
        'old_uid_pay_money'                       => ['old_uid_pay_money'],
        'uid_count'                               => ['uid_count'],
        'total_pay_money'                         => ['total_pay_money'],
        'reg_old_clique_muid_count'               => ['reg_old_clique_muid_count'],
        'click_old_clique_muid_count_ldy'         => ['click_old_clique_muid_count_ldy'],
        'click_old_clique_muid_count'             => ['click_old_clique_muid_count'],
        'action_old_clique_muid_count'            => ['action_old_clique_muid_count'],
        'day_three_login_uid_count'               => ['day_three_login_uid_count'],
        'day_three_login_muid_distinct_count'     => ['day_three_login_muid_distinct_count'],
        //        'click_count_yeyou_show' => ['click_count_yeyou_show'],
        //        'click_ip_distinct_count_yeyou_show' => ['click_ip_distinct_count_yeyou_show'],
        //        'click_ip_count_yeyou_show' => ['click_ip_count_yeyou_show'],
        //        'click_muid_distinct_count_yeyou_show' => ['click_muid_distinct_count_yeyou_show'],
        //        'click_old_muid_count_yeyou_show' => ['click_old_muid_count_yeyou_show'],
        //        'click_old_clique_muid_count_yeyou_show' => ['click_old_clique_muid_count_yeyou_show'],
        //        'click_muid_count_yeyou_show' => ['click_muid_count_yeyou_show'],
        'all_total_pay_times'                     => ['all_total_pay_times'],
        'pay_money'                               => ['pay_money'],
        'official_pay_count'                      => ['official_pay_count'],
        'official_pay_money'                      => ['official_pay_money'],
        'official_pay_rate'                       => ['pay_money', 'official_pay_money', 'official_pay_rate'],
        //        'show_old_muid_count_percent' => ['show_old_muid_count_percent', 'click_count_yeyou_show', 'click_old_muid_count_yeyou_show'],
        //        'show_old_clique_muid_count_percent' => ['show_old_clique_muid_count_percent', 'click_count_yeyou_show', 'click_old_clique_muid_count_yeyou_show'],
        'action_reg_muid_rate'                    => ['reg_muid_distinct_count', 'action_muid_distinct_count', 'action_reg_muid_rate'],
        'cost_create_rate'                        => ['cost_create_rate', 'cost_money', 'total_pay_money'],
        'new_pay_day_pay_money'                   => ['new_pay_day_pay_money'],
        'new_pay_day_pay_uid_count'               => ['new_pay_day_pay_uid_count'],
        'first_day_pay_times'                     => ['first_day_pay_times'],
        'total_pay_times'                         => ['total_pay_times'],
        'true_uid_count'                          => ['true_uid_count'],
        'true_uid_rate'                           => ['true_uid_rate', 'true_uid_count', 'reg_uid_count'],
        'cost_first_day_pay_times'                => ['first_day_pay_times', 'cost_first_day_pay_times', 'cost_money'],
        'cost_total_pay_times'                    => ['total_pay_times', 'cost_money', 'cost_total_pay_times'],
        'action_null_muid_rate'                   => ['action_muid_count', 'action_count', 'action_null_muid_rate'],
        'max_first_day_pay_money'                 => ['max_first_day_pay_money'],
        'cost_money'                              => ['cost_money'],
        'ori_money'                               => ['ori_money'],
        "is_third_login_rate"                     => ["is_third_login_rate", "is_third_login", "reg_uid_count"],
        "is_seventh_login_rate"                   => ["is_seventh_login_rate", "is_seventh_login", "reg_uid_count"],
        "is_fifteenth_login_rate"                 => ["is_fifteenth_login_rate", "is_fifteenth_login", "reg_uid_count"],
        "is_thirty_login_rate"                    => ["is_thirty_login_rate", "is_thirty_login", "reg_uid_count"],
        "is_pay_second_login"                     => ["is_pay_second_login"],
        "is_pay_third_login"                      => ["is_pay_third_login"],
        "is_pay_seventh_login"                    => ["is_pay_seventh_login"],
        "is_pay_thirty_login"                     => ["is_pay_thirty_login"],
        "is_pay_second_login_rate"                => ["is_pay_second_login_rate", "is_pay_second_login", "first_pay_count"],
        "is_pay_third_login_rate"                 => ["is_pay_third_login_rate", "is_pay_third_login", "first_pay_count"],
        "is_pay_seventh_login_rate"               => ["is_pay_seventh_login_rate", "is_pay_seventh_login", "first_pay_count"],
        "is_pay_thirty_login_rate"                => ["is_pay_thirty_login_rate", "is_pay_thirty_login", "first_pay_count"],
        "roi_2"                                   => ["roi_2", "second_day_pay_money", "cost_money"],
        "roi_3"                                   => ["roi_3", "third_day_pay_money", "cost_money"],
        "roi_7"                                   => ["roi_7", "seventh_day_pay_money", "cost_money"],
        "roi_15"                                  => ["roi_15", "fifteenth_day_pay_money", "cost_money"],
        "roi_30"                                  => ["roi_30", "thirty_day_pay_money", "cost_money"],
        "ltv_2"                                   => ["ltv_2", "second_day_pay_money", "reg_uid_count"],
        "ltv_3"                                   => ["ltv_3", "third_day_pay_money", "reg_uid_count"],
        "ltv_7"                                   => ["ltv_7", "seventh_day_pay_money", "reg_uid_count"],
        "ltv_15"                                  => ["ltv_15", "fifteenth_day_pay_money", "reg_uid_count"],
        "ltv_30"                                  => ["ltv_30", "thirty_day_pay_money", "reg_uid_count"],
        "is_third_login"                          => ["is_third_login"],
        "is_seventh_login"                        => ["is_seventh_login"],
        "is_fifteenth_login"                      => ["is_fifteenth_login"],
        "is_thirty_login"                         => ["is_thirty_login"],
        "second_day_pay_money"                    => ["second_day_pay_money"],
        "third_day_pay_money"                     => ["third_day_pay_money"],
        "seventh_day_pay_money"                   => ["seventh_day_pay_money"],
        "fifteenth_day_pay_money"                 => ["fifteenth_day_pay_money"],
        "thirty_day_pay_money"                    => ["thirty_day_pay_money"],
        "game_pay_money"                          => ["game_pay_money"],
        "divide_pay_money"                        => ["divide_pay_money"],
        "apple_pay_money"                         => ["apple_pay_money"],
        "yyb_pay_money"                           => ["yyb_pay_money"],
        "pay_way_pay_money"                       => ["pay_way_pay_money"],
        "operation_profit"                        => ["operation_profit", "game_pay_money", "divide_pay_money", "pay_way_pay_money", "cost_money"],
        "deduct_total_pay_money"                  => ["game_pay_money", "divide_pay_money", "deduct_total_pay_money"],
        "click_muid_distinct_count_ldy_show"      => ["click_muid_distinct_count_ldy_show"],
        "click_muid_count_ldy_show"               => ["click_muid_count_ldy_show"],
        "click_ip_distinct_count_ldy_show"        => ["click_ip_distinct_count_ldy_show"],
        "click_ip_count_ldy_show"                 => ["click_ip_count_ldy_show"],
        "click_count_ldy_show"                    => ["click_count_ldy_show"],
        "show_muid_count"                         => ["show_muid_count"],
        "show_muid_distinct_count"                => ["show_muid_distinct_count"],
        "show_ip_count"                           => ["show_ip_count"],
        "show_ip_distinct_count"                  => ["show_ip_distinct_count"],
        "show_count"                              => ["show_count"],
        "ldy_show_ip_same_percent"                => ["ldy_show_ip_same_percent", "click_ip_distinct_count_ldy_show", "click_ip_count_ldy_show"],
        "ldy_show_ip_muid_same_percent"           => ["ldy_show_ip_muid_same_percent", "click_muid_distinct_count_ldy_show", "click_muid_count_ldy_show"],
        "show_ip_same_percent"                    => ["show_ip_same_percent", "show_ip_distinct_count", "show_ip_count"],
        "show_ip_muid_same_percent"               => ["show_ip_muid_same_percent", "show_muid_distinct_count", "show_muid_count"],
        // 团队数据
        "predic_roi"                              => ["predic_roi", "tpay_360", "cost_money"],
        "tpay_360"                                => ["tpay_360"],
        "is_third_login_percent"                  => ["is_third_login_percent", "is_third_login", "reg_uid_count"],
        "is_seventh_login_percent"                => ["is_seventh_login_percent", "is_seventh_login", "reg_uid_count"],
        "first_day_standard_rate"                 => ["first_day_standard_rate", "first_day_pay_money", "total_standard_value"],
        "seventh_day_standard_rate"               => ["seventh_day_standard_rate", "seventh_day_pay_money", "seventh_standard_value"],
        "thirty_day_standard_rate"                => ["thirty_day_standard_rate", "thirty_day_pay_money", "thirty_standard_value"],

        // 预估的指标
        'lifetime_money'                          => ['lifetime_money', 'lifetime_money_cps'],
        'lifetime_roi'                            => ['lifetime_roi', 'lifetime_money', 'lifetime_money_cps', 'cost_money'],
        "pre_channel_pay"                         => ["pre_channel_pay", "pre_channel_pay_cps"],
        "pre_ios_pay"                             => ["pre_ios_pay", "pre_ios_pay_cps"],
        "pre_divide_pay_money"                    => ["pre_divide_pay_money", "pre_divide_pay_money_cps"],
        "pre_dehan_divide_money"                  => ["pre_dehan_divide_money", "pre_dehan_divide_money_cps"],
        "pre_server_money"                        => ["pre_server_money", "pre_server_money_cps"],
        "pre_applet_divide_money"                 => ["pre_applet_divide_money", "pre_applet_divide_money_cps"],
        "other_cost"                              => ["other_cost", "other_cost_cps"],
        "lifetime_profit"                         => ["lifetime_money", "lifetime_money_cps", "pre_divide_pay_money", "pre_divide_pay_money_cps", "pre_ios_pay", "pre_ios_pay_cps", "pre_channel_pay", "pre_channel_pay_cps", "cost_money", "pre_channel_pay", "pre_channel_pay_cps", "pre_dehan_divide_money", "pre_dehan_divide_money_cps", "pre_server_money", "pre_server_money_cps", "pre_applet_divide_money", "pre_applet_divide_money_cps", 'inspire_cost', 'pre_pay_way_ip_money', "pre_pay_way_ip_money_cps"],
        "lifetime_profit_include_other_cost"      => ["lifetime_money", "lifetime_money_cps", "pre_divide_pay_money", "pre_divide_pay_money_cps", "pre_ios_pay", "pre_ios_pay_cps", "pre_channel_pay", "pre_channel_pay_cps", "cost_money", "pre_channel_pay", "pre_channel_pay_cps", "pre_dehan_divide_money", "pre_dehan_divide_money_cps", "pre_server_money", "pre_server_money_cps", "pre_applet_divide_money", "pre_applet_divide_money_cps", "other_cost", "other_cost_cps", "inspire_cost", "pre_pay_way_ip_money_cps", 'pre_pay_way_ip_money'],
        "pre_game_360_profit"                     => ["pre_game_360_pay", "pre_channel_pay", "pre_channel_pay_cps", "pre_ios_pay", "pre_ios_pay_cps", "pre_divide_pay_money", "pre_divide_pay_money_cps", "cost_money"],
        "pre_game_360_pay"                        => ["pre_game_360_pay"],
        // 从pay表拿的
        "lifetime_money_cps"                      => ["lifetime_money_cps"],
        "pre_channel_pay_cps"                     => ["pre_channel_pay_cps"],
        "pre_ios_pay_cps"                         => ["pre_ios_pay_cps"],
        "pre_divide_pay_money_cps"                => ["pre_divide_pay_money_cps"],
        "pre_dehan_divide_money_cps"              => ["pre_dehan_divide_money_cps"],
        "pre_server_money_cps"                    => ["pre_server_money_cps"],
        "pre_applet_divide_money_cps"             => ["pre_applet_divide_money_cps"],
        "pre_pay_way_ip_money_cps"                => ["pre_pay_way_ip_money_cps"],
        "other_cost_cps"                          => ["other_cost_cps"],

        // 小游戏注册创角
        "xcx_ori_reg_count"                       => ["xcx_ori_reg_count"],
        "xcx_role_create_rate"                    => ["xcx_role_create_rate", "xcx_ori_reg_count", "xcx_role_create_count"],
        'callback_first_day_pay_money'            => ['callback_first_day_pay_money'],
        'callback_first_day_pay_count'            => ['callback_first_day_pay_count'],
        'predict_overall_score'                   => ['predict_overall_score'],
        'official_apportion_proportion'           => ['official_apportion_proportion', 'first_day_pay_money'],
        'first_day_pay_money_within_6'            => ['first_day_pay_money_within_6'],
        'first_day_pay_money_within_18'           => ['first_day_pay_money_within_18'],
        'first_day_pay_once_within_30'            => ['first_day_pay_once_within_30'],
        'callback_total_day_pay_money'            => ["callback_total_day_pay_money"],
        'callback_day_pay_count'                  => ["callback_day_pay_count"],
        'callback_total_day_pay_times'            => ["callback_total_day_pay_times"],
        // 激励广告位相关指标
        "inspire_show"                            => ["inspire_show"],
        "inspire_click"                           => ["inspire_click"],
        "inspire_cost"                            => ["inspire_cost"],

        // 首次付费24小时指标
        "first_pay_24_hour_pay_money"             => ["first_pay_24_hour_pay_money"],
        "first_pay_24_hour_pay_count"             => ["first_pay_24_hour_pay_count"],
        'first_pay_24_hour_pay_rate'              => ['first_pay_24_hour_pay_rate', 'first_pay_24_hour_pay_count', 'reg_uid_count'],
        'first_pay_24_hour_ltv'                   => ['first_pay_24_hour_ltv', 'first_pay_24_hour_pay_money', 'reg_uid_count'],
        'first_pay_24_hour_arpu'                  => ['first_pay_24_hour_arpu', 'first_pay_24_hour_pay_money', 'first_pay_24_hour_pay_count'],
        'first_pay_24_hour_pay_cost'              => ['first_pay_24_hour_pay_cost', 'first_pay_24_hour_pay_count', 'cost_money'],
        'first_pay_24_hour_roi'                   => ['first_pay_24_hour_roi', 'cost_money', 'first_pay_24_hour_pay_money'],
        'first_pay_24_hour_pay_times'             => ['first_pay_24_hour_pay_times'],
        'first_pay_24_hour_pay_times_cost'        => ['first_pay_24_hour_pay_times_cost', 'first_pay_24_hour_pay_times', 'cost_money'],
        "action_same_day_reg_count"               => ["action_same_day_reg_count"],
        "action_same_day_reg_rate"                => ["action_same_day_reg_count", "action_same_day_reg_rate", "action_count"],

        // 海外指标
        "muid_count"                              => ["muid_count"],
        "reg_muid_new_pay_count"                  => ["reg_muid_new_pay_count"],
        "old_muid_count"                          => ["old_muid_count"],
        "old_muid_pay_count"                      => ["old_muid_pay_count"],
        "is_second_muid_login"                    => ["is_second_muid_login"],
        "is_third_muid_login"                     => ["is_third_muid_login"],
        "is_seventh_muid_login"                   => ["is_seventh_muid_login"],
        "is_fifteenth_muid_login"                 => ["is_fifteenth_muid_login"],
        "is_thirty_muid_login"                    => ["is_thirty_muid_login"],
        // 海外计算类型指标
        "muid_total_pay_count"                    => ["muid_total_pay_count", "reg_muid_new_pay_count", "old_muid_pay_count"],
        "reg_muid_arpu"                           => ["reg_muid_arpu", "reg_uid_new_pay_money", "reg_muid_distinct_count"],
        "reg_muid_pay_arpu"                       => ["reg_muid_pay_arpu", "reg_uid_new_pay_money", "reg_muid_new_pay_count"],
        "reg_muid_new_pay_percent"                => ["reg_muid_new_pay_percent", "reg_muid_new_pay_count", "reg_muid_distinct_count"],
        "cost_per_reg_muid"                       => ["cost_per_reg_muid", "cost_money", "reg_muid_new_pay_count"],
        "old_muid_arpu"                           => ["old_muid_arpu", "old_uid_pay_money", "old_muid_count"],
        "old_muid_pay_arpu"                       => ["old_muid_pay_arpu", "old_uid_pay_money", "old_muid_pay_count"],
        "old_muid_pay_percent"                    => ["old_muid_pay_percent", "old_muid_pay_count", "old_muid_count"],
        "muid_total_arpu"                         => ["muid_total_arpu", "total_pay_money", "muid_count"],
        "muid_total_pay_arpu"                     => ["muid_total_pay_arpu", "total_pay_money", "reg_muid_new_pay_count", "old_muid_pay_count"],
        "muid_total_pay_percent"                  => ["muid_total_pay_percent", "reg_muid_new_pay_count", "old_muid_pay_count", "muid_count"],
        "is_second_muid_login_rate"               => ["is_second_muid_login_rate", "is_second_muid_login", "reg_muid_distinct_count"],
        "is_third_muid_login_rate"                => ["is_third_muid_login_rate", "is_third_muid_login", "reg_muid_distinct_count"],
        "is_seventh_muid_login_rate"              => ["is_seventh_muid_login_rate", "is_seventh_muid_login", "reg_muid_distinct_count"],
        "is_fifteenth_muid_login_rate"            => ["is_fifteenth_muid_login_rate", "is_fifteenth_muid_login", "reg_muid_distinct_count"],
        "is_thirty_muid_login_rate"               => ["is_thirty_muid_login_rate", "is_thirty_muid_login", "reg_muid_distinct_count"],
        "is_second_muid_login_cost"               => ["is_second_muid_login_cost", "cost_money", "is_second_muid_login"],
        "is_third_muid_login_cost"                => ["is_third_muid_login_cost", "cost_money", "is_third_muid_login"],
        "is_seventh_muid_login_cost"              => ["is_seventh_muid_login_cost", "cost_money", "is_seventh_muid_login"],
        "profit_day_360_pay"                      => ["profit_day_360_pay"],
        "month_total_pay_money"                   => ["month_total_pay_money"],
        "month_money"                             => ["month_money"],
        "profit_month_360_pay"                    => ["profit_month_360_pay"],
        // 等级指标
        "key_role_level_count"                    => ["key_role_level_count"],
        "key_role_level_percent"                  => ["key_role_level_percent", "key_role_level_count", "reg_uid_count"],
        "key_role_level_cost"                     => ["key_role_level_cost", "cost_money", "key_role_level_count"],
        'first_day_pay_rate_within_6'             => ['first_day_pay_rate_within_6', 'first_day_pay_money_within_6', 'first_day_pay_count'],
        'first_day_pay_rate_within_18'            => ['first_day_pay_rate_within_18', 'first_day_pay_money_within_18', 'first_day_pay_count'],
        'daily_cost_money'                        => ['daily_cost_money', 'cost_money', 'cost_days'],

        // 媒体
        "media_show_count"                        => ["media_show_count"],
        "media_click_count"                       => ["media_click_count"],
        "media_convert_count"                     => ["media_convert_count"],
        "media_deep_convert_count"                => ["media_deep_convert_count"],

        "media_show_cost"         => ["media_show_cost", "cost_money", "media_show_count"],
        "media_click_rate"        => ["media_click_rate", "media_show_count", "media_click_count"],
        "media_convert_rate"      => ["media_convert_rate", "media_convert_count", "media_click_count"],
        "media_deep_convert_rate" => ["media_deep_convert_rate", "media_deep_convert_count", "media_click_count"],

        "anchor_cost_rate" => ['anchor_cost_rate', 'anchor_cost', 'cost_money'],

        "first_day_pay_once_key_money_count" => ["first_day_pay_once_key_money_count"],
        "first_day_pay_once_key_money_rate"  => ["first_day_pay_once_key_money_rate", "first_day_pay_once_key_money_count", "first_day_pay_count"],
    ];

    const FORMAT_COLUMN = [
        'first_day_pay_money',
        'seventh_day_pay_money',
        'thirty_day_pay_money',
        'reg_total_pay_money',
        'lifetime_money',
        'pre_pay_way_ip_money',
        'old_uid_pay_money',
        'total_pay_money',
        'roll_total_pay_money',
        'official_pay_money',
        'pay_money',
        'reg_uid_new_pay_money',
        'operation_profit',
        'deduct_total_pay_money',
        'inspire_cost',
        'pre_channel_pay',
        'pre_ios_pay',
        'pre_divide_pay_money',
        'pre_dehan_divide_money',
        'pre_server_money',
        'pre_applet_divide_money',
        'other_cost',
        'lifetime_profit',
        'lifetime_profit_include_other_cost',
        "profit_day_360_pay",
        "month_total_pay_money",
        "month_money",
        "profit_month_360_pay",
    ];


    const MAP_TYPE_SELECT = [
        // 默认-地域
        0 => [
            'inner' => [
                'ip_country ( reg.ip ) AS country',
                'ip_province ( reg.ip ) AS province',
                'ip_city ( reg.ip ) AS city',
                'count( 1 ) AS reg_uid_count',
                'sum(IF ( total_pay_money > 0, 1, 0 )) AS pay_uid_count',
                'sum( total_pay_money ) AS total_pay_money',
            ],
            'outer' => []
        ],
        // 性别
        2 => [
            'inner' => [
                'reg.uid',
                'reg.total_pay_money',
                "if(ifnull(info.sex,'未知')='','未知',ifnull(info.sex,'未知')) as sex"
            ],
            'outer' => [
                'sex',
                'count( 1 ) AS reg_uid_count',
                'sum(IF( total_pay_money > 0, 1, 0 )) AS pay_uid_count',
                'sum( total_pay_money ) AS total_pay_money',
            ],
        ],
        // 年龄
        3 => [
            'inner' => [
                'reg.uid',
                'reg.total_pay_money',
                "ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) AS age",
                "CASE
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) > 0 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) < 10 THEN '10岁以下' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 10 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 19 THEN '10-19岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 20 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 29 THEN '20-29岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 30 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 39 THEN '30-39岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 40 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 49 THEN '40-49岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 50 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 59 THEN '50-59岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 60 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 69 THEN '60-69岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 70 THEN'70岁以上' 
                        ELSE '其他' 
                END AS 'age_type'",
            ],
            'outer' => [
                'count( 1 ) AS reg_uid_count',
                'sum(IF( total_pay_money > 0, 1, 0 )) AS pay_uid_count',
                'sum( total_pay_money ) AS total_pay_money',
                "age_type"
            ],
        ],
    ];

    const MAP_TYPE_GROUP = [
        0 => [
            'country',
            'province',
            'city',
        ],
        2 => [
            'sex'
        ],
        3 => [
            "age_type"
        ],
    ];

    const MAP_TYPE_SELECT_ROOT = [
        // 默认-地域
        0 => [
            'inner' => [
                "substring_index(reg.ip, '-',-1) as reg_ip",
                'reg.uid',
                'reg.root_game_total_pay_money as total_pay_money',
            ],
            'outer' => [
                'ip_info.country as country',
                'ip_info.region as province',
                'ip_info.city as city',
                'count( 1 ) AS reg_uid_count',
                'sum(IF ( root_game_total_pay_money > 0, 1, 0 )) AS pay_uid_count',
                'sum( root_game_total_pay_money ) AS total_pay_money',
            ]
        ],
        // 性别
        2 => [
            'inner' => [
                'reg.uid',
                'reg.root_game_total_pay_money as total_pay_money',
                "if(ifnull(info.sex,'未知')='','未知',ifnull(info.sex,'未知')) as sex"
            ],
            'outer' => [
                'sex',
                'count( 1 ) AS reg_uid_count',
                'sum(IF( total_pay_money > 0, 1, 0 )) AS pay_uid_count',
                'sum( total_pay_money ) AS total_pay_money',
            ],
        ],
        // 年龄
        3 => [
            'inner' => [
                'reg.uid',
                'reg.root_game_total_pay_money as total_pay_money',
                "ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) AS age",
                "CASE
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) > 0 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) < 10 THEN '10岁以下' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 10 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 19 THEN '10-19岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 20 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 29 THEN '20-29岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 30 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 39 THEN '30-39岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 40 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 49 THEN '40-49岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 50 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 59 THEN '50-59岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 60 
                        AND ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) <= 69 THEN '60-69岁' 
                        WHEN ifnull( TIMESTAMPDIFF( YEAR, birth_date, CURDATE()), '' ) >= 70 THEN'70岁以上' 
                        ELSE '其他' 
                END AS 'age_type'",
            ],
            'outer' => [
                'count( 1 ) AS reg_uid_count',
                'sum(IF( total_pay_money > 0, 1, 0 )) AS pay_uid_count',
                'sum( total_pay_money ) AS total_pay_money',
                "age_type"
            ],
        ],
    ];

}
