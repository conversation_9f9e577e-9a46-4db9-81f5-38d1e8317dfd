<?php

namespace App\Constant;

class ADLiveCostSqlMap
{

    const QUERY_TARGET = [
        // 订单承担手续费
        'order_star_order_fee' => ['sum(main.order_star_order_fee) as order_star_order_fee'],
        // 系统
        'os' => ['main.os as os'],
        // 主播工资
        'anchor_cost' => ['sum(main.anchor_cost) as anchor_cost'],
        // 主播在星图提现手续费
        'anchor_order_extra' => ["sum(main.anchor_order_extra) as anchor_order_extra"],
        // 主播在星图提现金额
        'anchor_order_fee' => ["sum(main.anchor_order_fee) as anchor_order_fee"],
        // 结算主体
        'payway_company' => ["main.payway_company"],
        // 游点下发工资
        'yd_company_fee' => ["sum(main.yd_company_fee) as yd_company_fee"],
        // 游点提现手续费
        'yd_out_tax' => ['sum(main.yd_out_tax) as yd_out_tax'],
        // 应返款
        'should_return_money' => ['sum(main.should_return_money) as should_return_money'],
        // 税前应补款
        'pre_should_pay_money' => ['sum(main.pre_should_pay_money) as pre_should_pay_money'],
        // 应补款
        'should_pay_money' => ['sum(main.should_pay_money) as should_pay_money'],
        // 贪玩付游点
        'tw_pay_yd' => ['sum(main.tw_pay_yd) as tw_pay_yd'],
        // 最终成本
        'last_cost' => ["sum(main.last_cost) as last_cost"],
        // 游点返款
        'yd_return_money' => ['sum(main.yd_return_money) as yd_return_money'],
        // 云账户手续费率
        'cloud_account_tax' => ["main.cloud_account_tax"],
        // 收款方式
        'pay_way' => ['main.pay_way'],
        // 需求ID
        'campaign_id' => ['main.campaign_id'],
        // 订单类型
        'dsp_order_type' => ['main.dsp_order_type'],
        // 订单类型
        'star_order_type' => ['main.order_type_label as star_order_type'],
        // 负责人分组
        'agent_leader_group_name' => ['main.agent_leader_group_name'],
        // 媒体类型
        'media_type' => ['main.live_media_type as media_type'],
        // MCN_ID
        'mcn_id' => ['main.mcn_id'],
        // 主播实名
        'live_true_name' => ['main.live_true_name'],
        // 主播ID
        'anchor_log_id' => ['main.anchor_log_id'],
        // 主播名
        'live_anchor_name' => ['main.live_anchor_name'],
        // 商务
        'star_business' => ['main.star_business'],
        // 平台
        'platform' => ['main.platform'],
        // 账户名
        'live_account_name' => ['main.live_account_name'],
        // 账户id
        'live_account_id' => ['main.live_account_id'],
        // 任务ID
        'live_demand_id' => ['main.live_order_id as live_demand_id'],
        // 任务名
        'live_demand_name' => ['main.live_demand_name as live_demand_name'],
        // 订单ID
        'live_order_id' => ['main.live_order_id'],
        // 机构名
        'anchor_company' => ['main.anchor_company'],
        // 订单抖音号
        'star_aweme_account' => ['main.star_aweme_account'],
        // 广告位抖音号
        'site_aweme_account' => ['main.site_aweme_account'],
        // 开播时间
        'first_live_time' => ['main.first_live_time'],
        // 直播时长（秒）
        'live_time' => ['sum(main.live_time) as live_time'],
        // 违规扣款
        'deduction_amount' => ['sum(main.deduction_amount) as deduction_amount'],
        // 扣款原因
        'deduction_desc' => ['main.deduction_desc'],
        // 加班费
        'extra_payment' => ['main.extra_payment'],
        // 加班明细
        'extra_desc' => ['main.extra_desc'],
        // 是否开区
        'is_server_open' => ['main.is_server_open'],
        // 渠道id
        'agent_id' => ['main.agent_id'],
        // 创建时间
        'create_time' => ['main.create_time'],
        // 达人id
        'live_author_id' => ['main.live_author_id'],
        // 达人名
        'live_author_name' => ['main.live_author_name'],
        // 订单状态
        'universal_order_status' => ['main.universal_order_status'],
        // 直播运营
        'star_interface_person' => ['main.star_interface_person'],
        // 广告位直播运营
        'site_int_person' => ['main.site_int_person'],
        // 安卓广告位
        'android_site_id' => ['main.android_site_id'],
        // 苹果广告位
        'ios_site_id' => ['main.ios_site_id'],
        // 下单人
        'live_order_creator' => ['main.live_order_creator'],
        // 渠道负责人
        'agent_leader' => ['main.agent_leader'],
        // 游戏id
        'game_id' => ['main.game_id'],
        // 游戏名
        'game_name' => ['main.game_name'],
        // 根游戏名
        'root_game_name' => ['main.root_game_name'],
        // 游戏app_name
        'app_name' => ['main.app_name'],
        // 扣款时间
        'opter_time' => ["if(main.opter_time = '1970-01-01 08:00:00','',main.opter_time) as opter_time"],
        // 财务操作类型
        'opter_type' => ['main.opter_type'],
        // 财务扣款金额
        'amount' => ['sum(main.amount) as amount'],
        // 价格类型
        'price_type' => ['main.price_type'],
        // 任务金额
        'price' => ['sum(main.price) as price'],
        // 价格
        'live_price' => ['sum(main.live_price) as live_price'],
        // 最低应播时长
        'live_hour' => ['sum(main.live_hour) as live_hour'],
        // 订单扣款金额
        'cost' => ['sum(main.cost) as cost'],
        // 达人机构
        'author_mcn_enum' => ["main.author_mcn_enum"],
        // 达人机构
        'author_mcn' => ["main.author_mcn"],
        // 达人类型
        'author_type' => ['main.author_type'],
        // 是否承担星图提现手续费
        'is_extra' => ['main.is_extra'],
        // 承担的云账户打款税点
        'cloud_account_tax_author_cover_rate' => ['main.cloud_account_tax_author_cover_rate'],
        // 代理商全称
        'agency_full_name' => ['main.agency_full_name'],
        // 开户结算主体
        'settle_company' => ['main.settle_company'],
        // 代理商id
        'agency_id' => ['main.agency_id'],
        // 对方开票税率
        'company_tax' => ['main.company_tax'],
        // 是否承担开票税
        'com_is_extra' => ['main.com_is_extra'],
        // 直播间形式
        'live_mode' => ['main.live_mode'],
        // 是否承担星图下单手续费
        'order_is_extra' => ['main.order_is_extra'],
        // 用于计算工资直播时长
        'live_time_cost' => ["sum(main.live_time_cost) as live_time_cost"],
        // 星图下单手续费
        'star_order_fee' => ["sum(main.star_order_fee) as star_order_fee"],
        // 主播星图提现手续费率
        'anchor_order_tax' => ["main.anchor_order_tax"],
        // 游点提现金额手续费率
        'yd_cash_tax' => ["main.yd_cash_tax"],
        // 抵扣后税后应补/应返
        'after_tax_deduction_amount' => ['sum(main.after_tax_deduction_amount) as after_tax_deduction_amount'],
        // 返款支付宝ID
        'alipay_order_no' => ['main.alipay_order_no'],
        // 返款时间
        'alipay_trans_dt' => ["if(main.alipay_trans_dt = '1970-01-01 08:00:00','',main.alipay_trans_dt) as alipay_trans_dt"],
        // 合同游戏名
        'contract_game_name' => ['main.contract_game_name'],
        // 合同游戏id
        'contract_game_id' => ['main.contract_game_id'],
        // 对账时间
        'confirm_time' => ["if(main.confirm_time = '1970-01-01 08:00:00','',main.confirm_time) as confirm_time"],
        // 开票时间
        'invoice_time' => ["if(main.invoice_time = '1970-01-01 08:00:00','',main.invoice_time) as invoice_time"],
        // 收付款方
        'payee' => ['main.payee'],
        // 收付款方开户行
        'payee_bank' => ['main.payee_bank'],
        // 收付款方地址
        'payee_address' => ['main.payee_address'],
        // 收付款方银行卡号
        'payee_bank_card_no' => ['main.payee_bank_card_no'],
        // 结算标识
        'trade_no' => ['main.trade_no'],
        // 是否结算
        'is_settled' => ['main.is_settled'],
        // cps结算量
        'log_value' => ['round(sum(main.log_value),4) as log_value'],
        // cps真实量
        'log_true_value' => ['round(sum(main.log_true_value),4) as log_true_value'],
        // 直播渠道
        'live_media_type' => ['main.live_media_type'],
        // 税率
        'cps_tax_rate' => ['main.cps_tax_rate'],
        // 渠道费率
        'cps_channel_fee_rate' => ['main.cps_channel_fee_rate'],
        // 扣量比例
        'cps_deduction_value' => ['main.cps_deduction_value'],
        // cps分成比例/注册单价
        'cps_settlement_base_value' => ['main.cps_settlement_base_value'],
        // cps开户人
        'cps_bank_holder' => ['main.cps_bank_holder'],
        // cps银行地址
        'cps_bank_area' => ['main.cps_bank_area'],
        // cps银行名称
        'cps_bank_name' => ['main.cps_bank_name'],
        // cps银行账户
        'cps_bank_card_number' => ['main.cps_bank_card_number'],
        // cps结算金额
        'settlement_money' => ['sum(main.settlement_money) as settlement_money'],
    ];

    // 需要合计的字段
    const ALL_NEED_AMOUNT = [
        'live_time', 'price', 'cost', 'consumption', 'author_settle_amount', 'pay_back', 'amount', 'deduction', 'star_order_fee',
        'anchor_cost', 'anchor_order_extra', 'anchor_order_fee', 'tw_pay_yd', 'should_return_money', 'should_pay_money','pre_should_pay_money',
        'yd_out_tax', 'extra_payment', 'deduction_amount', 'yd_return_money','live_time_cost',
        'last_cost', 'after_tax_deduction_amount', 'yd_company_fee', 'log_value', 'log_true_value','settlement_money'
    ];

    // 需要计算百分比的字段
    const ALL_CALCULATE_PERCENTAGE = [
        'anchor_order_tax', 'yd_cash_tax', 'cloud_account_tax_author_cover_rate','cps_channel_fee_rate','cps_tax_rate','cps_deduction_value'
    ];

}
