<?php

namespace App\Constant;

class InitIntelligentMonitor
{
    const DEFAULT_CONSUMER_NUM = 20;

    const ACTION_VALUE_TYPE_INPUT = 'input';
    const ACTION_VALUE_TYPE_SELECT = 'select';
    const ACTION_VALUE_TYPE_DATE = 'date';

    const LEVEL_CONFIG = [
        MediaType::TOUTIAO => [
            BatchADMediaAgentType::TOUTIAO_V2 => [
                'account' => 1,
                'ad1' => 2,
                'ad2' => 3,
                'ad3' => 4,
            ]
        ],
        MediaType::TENCENT => [
            BatchADMediaAgentType::TENCENT_V3 => [
                'account' => 1,
                'ad1' => 2,
                'ad3' => 3
            ]
        ],
        MediaType::KUAISHOU => [
            BatchADMediaAgentType::KUAISHOU => [
                'account' => 1,
                'ad2' => 2,
                'ad3' => 3,
            ]
        ]
    ];

    const TARGET_LABEL_CONFIG = [
        MediaType::TOUTIAO => [
            BatchADMediaAgentType::TOUTIAO_V2 => [
                'account' => '账户',
                'ad1' => '项目',
                'ad2' => '广告',
                'ad3' => '创意素材',
            ]
        ],
        MediaType::TENCENT => [
            BatchADMediaAgentType::TENCENT_V3 => [
                'account' => '账户',
                'ad1' => '广告',
                'ad3' => '创意'
            ]
        ],
        MediaType::KUAISHOU => [
            BatchADMediaAgentType::KUAISHOU => [
                'account' => '账户',
                'ad2' => '广告组',
                'ad3' => '广告创意',
            ]
        ]
    ];

    const CONDITION_TARGET_LABEL_CONFIG = [
        MediaType::TOUTIAO => [
            BatchADMediaAgentType::TOUTIAO_V2 => [
                'account_balance' => '账号余额',
                'account_ori_cost' => '账号消耗',
                'account_cost_per_reg' => '账号注册成本',
                'account_cost_per_first_day_pay' => '账号首日付费成本',
                'account_first_day_roi' => '账号首日回本',
                'project_ori_cost' => '项目消耗',
                'project_register' => '项目注册数',
                'project_cpm' => '项目cpm',
                'project_first_day_roi' => '项目首日回本',
                'project_total_roi' => '项目累计回本',
                'project_cost_per_reg' => '项目注册成本',
                'project_cost_per_first_day_pay' => '项目首日付费成本',
                'project_total_pay_money' => '项目累计付费',
                'project_create_time' => '项目新建时间(精准)',
                'project_create_time_relative' => '项目新建时间(相对)',
                'promotion_ori_cost' => '广告消耗',
                'promotion_register' => '广告注册数',
                'promotion_cpm' => '广告cpm',
                'promotion_first_day_roi' => '广告首日回本',
                'promotion_total_roi' => '广告累计回本',
                'promotion_cost_per_reg' => '广告注册成本',
                'promotion_cost_per_first_day_pay' => '广告首日付费成本',
                'promotion_total_pay_money' => '广告累计付费',
                'promotion_suggestion' => '广告诊断状态',
                'promotion_create_time' => '广告新建时间(精准)',
                'promotion_create_time_relative' => '广告新建时间(相对)',
                'creative_material_ori_cost' => '创意素材消耗',
                'creative_material_register' => '创意素材注册数',
                'creative_material_first_day_roi' => '创意素材首日回本',
                'creative_material_total_roi' => '创意素材累计回本',
                'creative_material_cost_per_reg' => '创意素材注册成本',
                'creative_material_cost_per_first_day_pay' => '创意素材首日付费成本',
                'creative_material_total_pay_money' => '创意素材累计付费',
            ]
        ],
        MediaType::TENCENT => [
            BatchADMediaAgentType::TENCENT_V3 => [
                'account_total_balance' => '账号余额',
                'adgroup_ori_cost' => '广告消耗',
                'adgroup_register' => '广告注册数',
                'adgroup_first_day_roi' => '广告首日回本',
                'adgroup_seventh_day_roi' => '广告七日回本',
                'adgroup_fifteenth_day_roi' => '广告十五日回本',
                'adgroup_thirty_day_roi' => '广告三十日回本',
                'adgroup_total_roi' => '广告累计回本',
                'adgroup_cost_per_reg' => '广告注册成本',
                'adgroup_cost_per_first_day_pay' => '广告首日付费成本',
                'adgroup_total_pay_money' => '广告累计付费',
                'adgroup_create_time' => '广告新建时间(精准)',
                'adgroup_create_time_relative' => '广告新建时间(相对)',
                'ad_ori_cost' => '创意消耗',
                'ad_register' => '创意注册数',
                'ad_first_day_roi' => '创意首日回本',
                'ad_seventh_day_roi' => '创意七日回本',
                'ad_fifteenth_day_roi' => '创意十五日回本',
                'ad_thirty_day_roi' => '创意三十日回本',
                'ad_total_roi' => '创意累计回本',
                'ad_cost_per_reg' => '创意注册成本',
                'ad_cost_per_first_day_pay' => '创意首日付费成本',
                'ad_total_pay_money' => '创意累计付费',
                'ad_create_time' => '创意新建时间(精准)',
                'ad_create_time_relative' => '创意新建时间(相对)',
            ]
        ],
        MediaType::KUAISHOU => [
            BatchADMediaAgentType::KUAISHOU => [
                'account_balance' => '账号余额',
                'unit_ori_cost' => '单元消耗',
                'unit_register' => '单元注册数',
                'unit_first_day_roi' => '单元首日回本',
                'unit_seventh_day_roi' => '单元七日回本',
                'unit_total_roi' => '单元累计回本',
                'unit_cost_per_reg' => '单元注册成本',
                'unit_cost_per_first_day_pay' => '单元首日付费成本',
                'unit_total_pay_money' => '单元累计付费',
                'unit_create_time' => '单元新建时间(精准)',
                'unit_create_time_relative' => '单元新建时间(相对)',
                'creative_ori_cost' => '创意消耗',
                'creative_register' => '创意注册数',
                'creative_first_day_roi' => '创意首日回本',
                'creative_seventh_day_roi' => '创意七日回本',
                'creative_total_roi' => '创意累计回本',
                'creative_cost_per_reg' => '创意注册成本',
                'creative_cost_per_first_day_pay' => '创意首日付费成本',
                'creative_total_pay_money' => '创意累计付费',
                'creative_create_time' => '创意新建时间(精准)',
                'creative_create_time_relative' => '创意新建时间(相对)',
            ]
        ]
    ];

    const ACTION_TARGET_LABEL_CONFIG = [
        MediaType::TOUTIAO => [
            BatchADMediaAgentType::TOUTIAO_V2 => [
                'account' => [
                    'budget' => '账户日预算'
                ],
                'ad1' => [
                    'opt_status' => '项目状态',
                    'delete' => '删除项目',
                    'budget' => '项目日预算',
                ],
                'ad2' => [
                    'opt_status' => '广告状态',
                    'delete' => '删除广告',
                ],
                'ad3' => [
                    'opt_status' => '创意素材状态',
                ]
            ]
        ],
        MediaType::TENCENT => [
            BatchADMediaAgentType::TENCENT_V3 => [
                'account' => [
                    'daily_budget' => '账户日预算',
                ],
                'ad1' => [
                    'configured_status' => '广告状态',
                    'auto_acquisition_budget' => '一键起量开启且设置起量预算',
                    'auto_acquisition_enabled' => '一键起量关闭',
                    'begin_date' => '开始投放日期延迟',
                    'delete' => '删除广告',
                    'daily_budget' => '广告日预算',
                ],
                'ad3' => [
                    'configured_status' => '创意状态',
                    'delete' => '删除创意',
                ]
            ]
        ],
        MediaType::KUAISHOU => [
            BatchADMediaAgentType::KUAISHOU => [
                'ad2' => [
                    'put_status' => '广告组状态',
                ],
                'ad3' => [
                    'put_status' => '广告创意状态',
                ]
            ]
        ],
    ];

    const CONFIG = [
        'target' => [ // 1维:media_type 2维:media_agent_type 3维:各种
            MediaType::TOUTIAO => [
                BatchADMediaAgentType::TOUTIAO_V2 => [
                    'options' => [
                        [
                            'value' => 'account',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account'],
                            'level' => self::LEVEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account']
                        ],
                        [
                            'value' => 'ad1',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['ad1'],
                            'level' => self::LEVEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['ad1']
                        ],
                        [
                            'value' => 'ad2',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['ad2'],
                            'level' => self::LEVEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['ad2'],
                        ],
                        [
                            'value' => 'ad3',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['ad3'],
                            'level' => self::LEVEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['ad3'],
                        ],
                    ]
                ]
            ],
            MediaType::TENCENT => [
                BatchADMediaAgentType::TENCENT_V3 => [
                    'options' => [
                        [
                            'value' => 'account',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['account'],
                            'level' => self::LEVEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['account']
                        ],
                        [
                            'value' => 'ad1',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad1'],
                            'level' => self::LEVEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad1']
                        ],
                        [
                            'value' => 'ad3',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad3'],
                            'level' => self::LEVEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad3']
                        ],
                    ]
                ]
            ],
            MediaType::KUAISHOU => [
                BatchADMediaAgentType::KUAISHOU => [
                    'options' => [
                        [
                            'value' => 'account',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['account'],
                            'level' => self::LEVEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['account']
                        ],
                        [
                            'value' => 'ad2',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['ad2'],
                            'level' => self::LEVEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['ad2']
                        ],
                        [
                            'value' => 'ad3',
                            'label' => self::TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['ad3'],
                            'level' => self::LEVEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['ad3']
                        ],
                    ]
                ]
            ]
        ],
        'condition_target_config' => [ // 1维:media_type 2维:media_agent_type 3维:target 4维:各种
            MediaType::TOUTIAO => [
                BatchADMediaAgentType::TOUTIAO_V2 => [
                    'account' => [
                        'options' => [
                            ['value' => 'account_balance', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account_balance'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'account_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account_ori_cost'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'account_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account_cost_per_reg'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'account_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account_cost_per_first_day_pay'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'account_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['account_first_day_roi'], 'type' => 'custom', 'unit' => ''],
                        ],
                        'action' => [
                            'account_balance' => ['>', '>=', '=', '<=', '<'],
                            'account_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'account_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'account_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'account_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                        ]
                    ],
                    'ad1' => [
                        'options' => [
                            ['value' => 'project_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_ori_cost'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_register'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_cpm', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_cpm'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_first_day_roi'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_total_roi'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_cost_per_reg'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_cost_per_first_day_pay'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_total_pay_money'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'project_create_time', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_create_time'], 'type' => 'datetime', 'unit' => ''],
                            [
                                'value' => 'project_create_time_relative',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['project_create_time_relative'],
                                'type' => 'unit',
                                'unit' => '小时',
                                'explain' => [
                                    '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                                    '早于前1个小时 : 新中国成立 到 2025-05-12 13:30:29',
                                    '最近1个小时 : 2025-05-12 13:30:29 到 2025-05-12 14:30:29'
                                ]
                            ],
                        ],
                        'action' => [
                            'project_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'project_register' => ['>', '>=', '=', '<=', '<'],
                            'project_cpm' => ['>', '>=', '=', '<=', '<'],
                            'project_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'project_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'project_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'project_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'project_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                            'project_create_time' => ['>=', '=', '<=',],
                            'project_create_time_relative' => ['早于前', '最近'],
                        ]
                    ],
                    'ad2' => [
                        'options' => [
                            ['value' => 'promotion_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_ori_cost'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_register'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_cpm', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_cpm'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_first_day_roi'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_total_roi'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_cost_per_reg'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_cost_per_first_day_pay'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'promotion_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_total_pay_money'], 'type' => 'custom', 'unit' => ''],
                            [
                                'value' => 'promotion_suggestion',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_suggestion'],
                                'type' => 'select',
                                'unit' => '',
                                'options' => [
                                    ['label' => '低效素材无法跑量', 'value' => '低效素材无法跑量'],
                                    ['label' => '活跃度低广告', 'value' => '活跃度低广告'],
                                    ['label' => '相似广告挤压严重', 'value' => '相似广告挤压严重'],
                                    ['label' => '预估空耗', 'value' => '预估空耗']
                                ]
                            ],
                            ['value' => 'promotion_create_time', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_create_time'], 'type' => 'datetime', 'unit' => ''],
                            [
                                'value' => 'promotion_create_time_relative',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['promotion_create_time_relative'],
                                'type' => 'unit',
                                'unit' => '小时',
                                'explain' => [
                                    '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                                    '早于前1个小时 : 新中国成立 到 2025-05-12 13:30:29',
                                    '最近1个小时 : 2025-05-12 13:30:29 到 2025-05-12 14:30:29'
                                ]
                            ],
                        ],
                        'action' => [
                            'promotion_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'promotion_register' => ['>', '>=', '=', '<=', '<'],
                            'promotion_cpm' => ['>', '>=', '=', '<=', '<'],
                            'promotion_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'promotion_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'promotion_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'promotion_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'promotion_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                            'promotion_suggestion' => ['='],
                            'promotion_create_time' => ['>=', '=', '<=',],
                            'promotion_create_time_relative' => ['早于前', '最近'],
                        ]
                    ],
                    'ad3' => [
                        'options' => [
                            ['value' => 'creative_material_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_ori_cost'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'creative_material_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_register'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'creative_material_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_first_day_roi'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'creative_material_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_total_roi'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'creative_material_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_cost_per_reg'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'creative_material_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_cost_per_first_day_pay'], 'type' => 'custom', 'unit' => ''],
                            ['value' => 'creative_material_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TOUTIAO][BatchADMediaAgentType::TOUTIAO_V2]['creative_material_total_pay_money'], 'type' => 'custom', 'unit' => ''],
                        ],
                        'action' => [
                            'creative_material_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'creative_material_register' => ['>', '>=', '=', '<=', '<'],
                            'creative_material_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'creative_material_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'creative_material_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'creative_material_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'creative_material_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                        ]
                    ]
                ]
            ],
            MediaType::TENCENT => [
                BatchADMediaAgentType::TENCENT_V3 => [
                    'account' => [
                        'options' => [
                            ['value' => 'account_total_balance', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['account_total_balance'], 'type' => 'custom'],
                        ],
                        'action' => [
                            'account_total_balance' => ['>', '>=', '=', '<=', '<']
                        ]
                    ],
                    'ad1' => [
                        'options' => [
                            ['value' => 'adgroup_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_ori_cost'], 'type' => 'custom'],
                            ['value' => 'adgroup_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_register'], 'type' => 'custom'],
                            ['value' => 'adgroup_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_first_day_roi'], 'type' => 'custom'],
                            ['value' => 'adgroup_seventh_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_seventh_day_roi'], 'type' => 'custom'],
                            ['value' => 'adgroup_fifteenth_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_fifteenth_day_roi'], 'type' => 'custom'],
                            ['value' => 'adgroup_thirty_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_thirty_day_roi'], 'type' => 'custom'],
                            ['value' => 'adgroup_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_total_roi'], 'type' => 'custom'],
                            ['value' => 'adgroup_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_cost_per_reg'], 'type' => 'custom'],
                            ['value' => 'adgroup_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_cost_per_first_day_pay'], 'type' => 'custom'],
                            ['value' => 'adgroup_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_total_pay_money'], 'type' => 'custom'],
                            ['value' => 'adgroup_create_time', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_create_time'], 'type' => 'datetime'],
                            [
                                'value' => 'adgroup_create_time_relative',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['adgroup_create_time_relative'],
                                'type' => 'unit',
                                'unit' => '小时',
                                'explain' => [
                                    '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                                    '早于前1个小时 : 新中国成立 到 2025-05-12 13:30:29',
                                    '最近1个小时 : 2025-05-12 13:30:29 到 2025-05-12 14:30:29'
                                ]
                            ],
                        ],
                        'action' => [
                            'adgroup_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_register' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_seventh_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_fifteenth_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_thirty_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                            'adgroup_create_time' => ['>=', '=', '<=',],
                            'adgroup_create_time_relative' => ['早于前', '最近'],
                        ]
                    ],
                    'ad3' => [
                        'options' => [
                            ['value' => 'ad_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_ori_cost'], 'type' => 'custom'],
                            ['value' => 'ad_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_register'], 'type' => 'custom'],
                            ['value' => 'ad_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_first_day_roi'], 'type' => 'custom'],
                            ['value' => 'ad_seventh_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_seventh_day_roi'], 'type' => 'custom'],
                            ['value' => 'ad_fifteenth_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_fifteenth_day_roi'], 'type' => 'custom'],
                            ['value' => 'ad_thirty_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_thirty_day_roi'], 'type' => 'custom'],
                            ['value' => 'ad_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_total_roi'], 'type' => 'custom'],
                            ['value' => 'ad_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_cost_per_reg'], 'type' => 'custom'],
                            ['value' => 'ad_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_cost_per_first_day_pay'], 'type' => 'custom'],
                            ['value' => 'ad_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_total_pay_money'], 'type' => 'custom'],
                            ['value' => 'ad_create_time', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_create_time'], 'type' => 'datetime'],
                            [
                                'value' => 'ad_create_time_relative',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::TENCENT][BatchADMediaAgentType::TENCENT_V3]['ad_create_time_relative'],
                                'type' => 'unit',
                                'unit' => '小时',
                                'explain' => [
                                    '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                                    '早于前1个小时 : 新中国成立 到 2025-05-12 13:30:29',
                                    '最近1个小时 : 2025-05-12 13:30:29 到 2025-05-12 14:30:29'
                                ]
                            ],
                        ],
                        'action' => [
                            'ad_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'ad_register' => ['>', '>=', '=', '<=', '<'],
                            'ad_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'ad_seventh_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'ad_fifteenth_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'ad_thirty_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'ad_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'ad_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'ad_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'ad_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                            'ad_create_time' => ['>=', '=', '<=',],
                            'ad_create_time_relative' => ['早于前', '最近'],
                        ]
                    ]
                ]
            ],
            MediaType::KUAISHOU => [
                BatchADMediaAgentType::KUAISHOU => [
                    'account' => [
                        'options' => [
                            ['value' => 'account_balance', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['account_balance'], 'type' => 'custom'],
                        ],
                        'action' => [
                            'account_balance' => ['>', '>=', '=', '<=', '<'],
                        ]
                    ],
                    'ad2' => [
                        'options' => [
                            ['value' => 'unit_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_ori_cost'], 'type' => 'custom'],
                            ['value' => 'unit_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_register'], 'type' => 'custom'],
                            ['value' => 'unit_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_first_day_roi'], 'type' => 'custom'],
                            ['value' => 'unit_seventh_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_seventh_day_roi'], 'type' => 'custom'],
                            ['value' => 'unit_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_total_roi'], 'type' => 'custom'],
                            ['value' => 'unit_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_cost_per_reg'], 'type' => 'custom'],
                            ['value' => 'unit_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_cost_per_first_day_pay'], 'type' => 'custom'],
                            ['value' => 'unit_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_total_pay_money'], 'type' => 'custom'],
                            ['value' => 'unit_create_time', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_create_time'], 'type' => 'datetime'],
                            [
                                'value' => 'unit_create_time_relative',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['unit_create_time_relative'],
                                'type' => 'unit',
                                'unit' => '小时',
                                'explain' => [
                                    '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                                    '早于前1个小时 : 新中国成立 到 2025-05-12 13:30:29',
                                    '最近1个小时 : 2025-05-12 13:30:29 到 2025-05-12 14:30:29'
                                ]
                            ],
                        ],
                        'action' => [
                            'unit_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'unit_register' => ['>', '>=', '=', '<=', '<'],
                            'unit_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'unit_seventh_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'unit_fifteenth_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'unit_thirty_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'unit_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'unit_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'unit_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'unit_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                            'unit_create_time' => ['>=', '=', '<=',],
                            'unit_create_time_relative' => ['早于前', '最近'],
                        ]
                    ],
                    'ad3' => [
                        'options' => [
                            ['value' => 'creative_ori_cost', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_ori_cost'], 'type' => 'custom'],
                            ['value' => 'creative_register', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_register'], 'type' => 'custom'],
                            ['value' => 'creative_first_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_first_day_roi'], 'type' => 'custom'],
                            ['value' => 'creative_seventh_day_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_seventh_day_roi'], 'type' => 'custom'],
                            ['value' => 'creative_total_roi', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_total_roi'], 'type' => 'custom'],
                            ['value' => 'creative_cost_per_reg', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_cost_per_reg'], 'type' => 'custom'],
                            ['value' => 'creative_cost_per_first_day_pay', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_cost_per_first_day_pay'], 'type' => 'custom'],
                            ['value' => 'creative_total_pay_money', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_total_pay_money'], 'type' => 'custom'],
                            ['value' => 'creative_create_time', 'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_create_time'], 'type' => 'datetime'],
                            [
                                'value' => 'creative_create_time_relative',
                                'label' => self::CONDITION_TARGET_LABEL_CONFIG[MediaType::KUAISHOU][BatchADMediaAgentType::KUAISHOU]['creative_create_time_relative'],
                                'type' => 'unit',
                                'unit' => '小时',
                                'explain' => [
                                    '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                                    '早于前1个小时 : 新中国成立 到 2025-05-12 13:30:29',
                                    '最近1个小时 : 2025-05-12 13:30:29 到 2025-05-12 14:30:29'
                                ]
                            ],
                        ],
                        'action' => [
                            'creative_ori_cost' => ['>', '>=', '=', '<=', '<'],
                            'creative_register' => ['>', '>=', '=', '<=', '<'],
                            'creative_first_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'creative_seventh_day_roi' => ['>', '>=', '=', '<=', '<'],
                            'creative_total_roi' => ['>', '>=', '=', '<=', '<'],
                            'creative_cost_per_reg' => ['>', '>=', '=', '<=', '<'],
                            'creative_cost_per_first_day_pay' => ['>', '>=', '=', '<=', '<'],
                            'creative_total_pay_money' => ['>', '>=', '=', '<=', '<'],
                            'creative_create_time' => ['>=', '=', '<=',],
                            'creative_create_time_relative' => ['早于前', '最近'],
                        ]
                    ],
                ]
            ]
        ],
        'action_target_config' => [ // 1维:media_type 2维:media_agent_type 3维:target 4维:各种
            MediaType::TOUTIAO => [
                BatchADMediaAgentType::TOUTIAO_V2 => [
                    'account' => [
                        'options' => [
                            ['value' => 'budget', 'label' => '账户日预算'],
                        ],
                        'action' => [
                            'budget' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_INPUT
                            ],
                        ]
                    ],
                    'ad1' => [
                        'options' => [
                            ['value' => 'opt_status', 'label' => '项目状态'],
                            ['value' => 'delete', 'label' => '删除项目'],
                            ['value' => 'budget', 'label' => '项目日预算'],
                        ],
                        'action' => [
                            'opt_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用项目', 'value' => 'ENABLE'],
                                    ['label' => '暂停项目', 'value' => 'DISABLE'],
                                ]
                            ],
                            'delete' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '删除', 'value' => 'delete'],
                                ]
                            ],
                            'budget' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_INPUT
                            ],
                        ]
                    ],
                    'ad2' => [
                        'options' => [
                            ['value' => 'opt_status', 'label' => '广告状态'],
                            ['value' => 'delete', 'label' => '删除广告'],
                        ],
                        'action' => [
                            'opt_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用广告', 'value' => 'ENABLE'],
                                    ['label' => '暂停广告', 'value' => 'DISABLE'],
                                ]
                            ],
                            'delete' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '删除', 'value' => 'delete'],
                                ]
                            ],
                        ]
                    ],
                    'ad3' => [
                        'options' => [
                            ['value' => 'opt_status', 'label' => '创意素材状态'],
                        ],
                        'action' => [
                            'opt_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用创意素材', 'value' => 'ENABLE'],
                                    ['label' => '暂停创意素材', 'value' => 'DISABLE'],
                                ]
                            ]
                        ]
                    ]
                ],
            ],
            MediaType::TENCENT => [
                BatchADMediaAgentType::TENCENT_V3 => [
                    'account' => [
                        'options' => [
                            ['value' => 'daily_budget', 'label' => '账户日预算'],
                        ],
                        'action' => [
                            'daily_budget' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_INPUT
                            ],
                        ]
                    ],
                    'ad1' => [
                        'options' => [
                            ['value' => 'configured_status', 'label' => '广告状态'],
                            ['value' => 'auto_acquisition_budget', 'label' => '一键起量开启且设置起量预算'],
                            ['value' => 'auto_acquisition_enabled', 'label' => '一键起量关闭'],
                            ['value' => 'begin_date', 'label' => '开始投放日期延迟'],
                            ['value' => 'delete', 'label' => '删除广告'],
                            ['value' => 'daily_budget', 'label' => '广告日预算'],
                        ],
                        'action' => [
                            'daily_budget' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_INPUT
                            ],
                            'configured_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用广告', 'value' => 'NORMAL'],
                                    ['label' => '暂停广告', 'value' => 'SUSPEND'],
                                ]
                            ],
                            'begin_date' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '延后一天', 'value' => '1'],
                                    ['label' => '延后两天', 'value' => '2'],
                                    ['label' => '延后三天', 'value' => '3'],
                                    ['label' => '延后四天', 'value' => '4'],
                                    ['label' => '延后五天', 'value' => '5'],
                                    ['label' => '延后六天', 'value' => '6'],
                                    ['label' => '延后七天', 'value' => '7'],
                                ]
                            ],
                            'auto_acquisition_enabled' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '关', 'value' => 'close'],
                                ]
                            ],
                            'auto_acquisition_budget' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_INPUT
                            ],
                            'delete' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '删除', 'value' => 'delete'],
                                ]
                            ],
                        ]
                    ],
                    'ad3' => [
                        'options' => [
                            ['value' => 'configured_status', 'label' => '创意状态'],
                            ['value' => 'delete', 'label' => '删除创意'],
                        ],
                        'action' => [
                            'configured_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用创意', 'value' => 'NORMAL'],
                                    ['label' => '暂停创意', 'value' => 'SUSPEND'],
                                ]
                            ],
                            'delete' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '删除', 'value' => 'delete'],
                                ]
                            ],
                        ],
                    ]
                ],
            ],
            MediaType::KUAISHOU => [
                BatchADMediaAgentType::KUAISHOU => [
                    'ad2' => [
                        'options' => [
                            ['value' => 'put_status', 'label' => '广告组状态'],
                        ],
                        'action' => [
                            'put_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用广告组', 'value' => '1'],
                                    ['label' => '暂停广告组', 'value' => '2'],
                                    ['label' => '删除广告组', 'value' => '3'],
                                ]
                            ],
                        ]
                    ],
                    'ad3' => [
                        'options' => [
                            ['value' => 'put_status', 'label' => '广告创意状态'],
                        ],
                        'action' => [
                            'put_status' => [
                                'action_value_type' => self::ACTION_VALUE_TYPE_SELECT,
                                'action_value_options' => [
                                    ['label' => '启用广告创意', 'value' => '1'],
                                    ['label' => '暂停广告创意', 'value' => '2'],
                                    ['label' => '删除广告创意', 'value' => '3'],
                                ]
                            ],
                        ],
                    ]
                ]
            ]
        ],
        'time_range_type' => [
            ['label' => '最近', 'value' => 'recently'],
            ['label' => '前', 'value' => 'ago']
        ],
        'time_range_unit' => [
            ['label' => '天', 'value' => 'day'],
            ['label' => '小时', 'value' => 'hour'],
        ],
        'time_range_explain' => array(
            'day' => [
                '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                '前7天 : 2025-05-05 00:00:00 到 2025-05-11 23:59:59',
                '最近7天 : 2025-05-05 00:00:00 到 2025-05-12 14:30:29'
            ],
            'hour' => [
                '假设以2025-05-12 14:30:29作为监控执行的日期时间',
                '前7小时 : 2025-05-12 07:30:29 到 2025-05-12 13:30:29',
                '最近7小时 : 2025-05-12 07:30:29 到 2025-05-12 14:30:29'
            ]
        )
    ];
}
