<?php

namespace App\Constant;

use Common\EnvConfig;

class ADComposeFilterSqlMap
{
    const DATA_MEDIA_KEY = EnvConfig::MYSQL['data_media']['database'];

    const MEDIA_TABLE = [
        'tt_hour_data_log' => self::DATA_MEDIA_KEY . '.ods_toutiao_creative_hour_data_log',
        'tt_ad_log' => self::DATA_MEDIA_KEY . '.ods_toutiao_ad_log',
        'tt_ad3_log' => self::DATA_MEDIA_KEY . '.ods_toutiao_creative_log',
        'tt_inefficient_material_log' => self::DATA_MEDIA_KEY . '.ods_toutiao_inefficient_material_log',
        'tx_component_day_data_log' => self::DATA_MEDIA_KEY . '.ods_tencent_component_day_data_log',
    ];

    const TABLE = [
        // **驱动表** 媒体广告二级公共维度表
        'ad2_common_log' => self::DATA_MEDIA_KEY . '.dwd_media_ad2_common_log',
        // **驱动表** 媒体广告三级公共维度表
        'ad3_common_log' => self::DATA_MEDIA_KEY . '.dwd_media_ad4_common_log',
        // 媒体广告三级每小时的数据表(
        // 用于计算每个素材每小时的实际消耗价格[媒体给的折扣价(cost)]，每个素材每小时原消耗价格(ori_cost)，每个素材每小时的展示数,
        // 每个素材每小时的点击数,每个素材每小时的激活数,每个素材每小时的转化数,每个素材每小时的注册数,每个素材每小时的付费人数,
        // 每个素材每小时所属计划的site_id，每个素材每小时所属计划site_id中当即小时的game_id(每个小时的game_id会改变)
        // )
        'hour_data_log' => self::DATA_MEDIA_KEY . '.dwd_media_ad4_common_day_data_log',
        // 用户注册记录表(用于算出累计充值金额，累计充值人数，累计注册人数)
//        'reg_log' => 'tanwan_datahub.v2_dwd_game_uid_reg_log',
        'reg_log' => 'tanwan_datahub.v2_dwd_game_uid_reg_log',
        // 媒体广告三级每日业务数据(用于计算出每日付费金额，每日付费人数，每日激活设备数，每日注册人数，每日注册设备数，创角数)
//        'overview_log' => 'tanwan_datahub.v2_ads_day_overview_log',
        'overview_log' => 'tanwan_datahub.v3_ads_day_root_game_back_overview_log',
        // 素材表(记录同一个作者的同一批素材，一行记录包括多条素材文件)
        'material_log' => self::DATA_MEDIA_KEY . '.ods_material_log',
        // **驱动表** 素材文件表
        'material_file_log' => self::DATA_MEDIA_KEY . '.ods_material_file_log',
        // **驱动表** 所用账号表
        'account_common_log' => self::DATA_MEDIA_KEY . '.dwd_media_account_common_log',
        // 渠道&广告配置关联表(用于与驱动表进行关联，控制驱动表的agent_leader和site_id)
        'dim_agent_site' => 'tanwan_datahub.v2_dim_agent_site_id',
        // 广告配置&game_id关联表(用于与驱动表和dim_agent_site进行关联，控制驱动表的game_id)
        'dim_site_game' => self::DATA_MEDIA_KEY . '.dim_site_game_id',
        // 游戏id-只为控制os
        'dim_game_id' => self::DATA_MEDIA_KEY . '.v2_dim_game_id',
    ];

    const TT_FORMULA = [
        'tt_hour_data_log' => [
            'tt_sum_attribution_next_day_open_cnt' => 'SUM ( attribution_next_day_open_cnt ) AS tt_sum_attribution_next_day_open_cnt',
            'tt_sum_loan_completion' => 'SUM ( loan_completion ) AS tt_sum_loan_completion',
            'tt_sum_ori_cost' => 'SUM ( ' . self::MEDIA_TABLE['tt_hour_data_log'] . '.ori_cost ) AS tt_sum_ori_cost',
            'tt_sum_register' => 'SUM ( ' . self::MEDIA_TABLE['tt_hour_data_log'] . '.register ) AS tt_sum_register',
            'tt_sum_download_finish' => 'SUM ( download_finish ) AS tt_sum_download_finish',
            'tt_sum_install_finish' => 'SUM ( install_finish ) AS tt_sum_install_finish',
            'tt_sum_click' => 'SUM ( ' . self::MEDIA_TABLE['tt_hour_data_log'] . '.click ) AS tt_sum_click',
            'tt_sum_download_start' => 'SUM ( download_start ) AS tt_sum_download_start',
            'tt_sum_click_install' => 'SUM ( click_install ) AS tt_sum_click_install',
            'tt_sum_attribution_deep_convert_cost' => 'SUM ( attribution_deep_convert_cost ) AS tt_sum_attribution_deep_convert_cost',
            'tt_sum_attribution_convert' => 'SUM ( attribution_convert ) AS tt_sum_attribution_convert',
            'tt_sum_deep_convert' => 'SUM ( deep_convert ) as tt_sum_deep_convert',
            'tt_sum_convert' => 'SUM ( ' . self::MEDIA_TABLE['tt_hour_data_log'] . '.convert ) as tt_sum_convert',
            'tt_sum_next_day_open' => 'SUM ( next_day_open ) AS tt_sum_next_day_open',
            'tt_sum_active' => 'SUM ( ' . self::MEDIA_TABLE['tt_hour_data_log'] . '.active ) AS tt_sum_active',
            'tt_sum_game_addiction' => ' SUM ( game_addiction ) AS tt_sum_game_addiction',
            'tt_sum_play_75_feed_break' => 'SUM ( play_75_feed_break ) AS tt_sum_play_75_feed_break',
            'tt_sum_play_100_feed_break' => 'SUM ( play_100_feed_break ) AS tt_sum_play_100_feed_break',
            'tt_sum_play_duration_sum' => 'SUM ( play_duration_sum ) AS tt_sum_play_duration_sum',
            'tt_sum_valid_play' => 'SUM ( valid_play ) AS tt_sum_valid_play',
            'tt_sum_play_25_feed_break' => 'SUM ( play_25_feed_break ) AS tt_sum_play_25_feed_break',
            'tt_sum_total_play' => 'SUM ( total_play ) AS tt_sum_total_play',
            'tt_sum_wifi_play' => 'SUM ( wifi_play ) AS tt_sum_wifi_play',
            'tt_sum_play_50_feed_break' => 'SUM ( play_50_feed_break ) AS tt_sum_play_50_feed_break',
            'tt_sum_play_over' => 'SUM ( play_over ) AS tt_sum_play_over',
            'tt_sum_location_click' => 'SUM ( location_click ) AS tt_sum_location_click',
            'tt_sum_comment' => 'SUM ( comment ) AS tt_sum_comment',
            'tt_sum_share' => 'SUM ( share ) AS tt_sum_share',
            'tt_sum_follow' => 'SUM ( follow ) AS tt_sum_follow',
            'tt_sum_home_visited' => 'SUM ( home_visited ) AS tt_sum_home_visited',
            'tt_sum_like' => 'SUM ( `like` ) AS tt_sum_like',
            'tt_sum_ies_music_click' => 'SUM ( ies_music_click ) AS tt_sum_ies_music_click',
            'tt_sum_ies_challenge_click' => 'SUM ( ies_challenge_click ) AS tt_sum_ies_challenge_click',
            'tt_average_play_time_per_play' => 'CAST ( SUM ( average_play_time_per_play ) / SUM ( total_play ) AS DECIMAL ( 12, 2 ) ) AS tt_average_play_time_per_play',
        ],
        'main' => [
            'tt_loan_completion_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_loan_completion AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_loan_completion_cost',
            'tt_loan_completion_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_loan_completion / tt_hour_data_log.tt_sum_register AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_loan_completion_rate',
            'tt_download_finish_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_download_finish AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_download_finish_cost',
            'tt_download_start_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_download_start AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_download_start_cost',
            'tt_install_finish_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_install_finish AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_install_finish_cost',
            'tt_download_start_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_download_start / tt_hour_data_log.tt_sum_click AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_download_start_rate',
            'tt_install_finish_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_install_finish / tt_hour_data_log.tt_sum_download_start AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_install_finish_rate',
            'tt_deep_convert_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_deep_convert / tt_hour_data_log.tt_sum_convert AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_deep_convert_rate',
            'tt_deep_convert_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_deep_convert AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_deep_convert_cost',
            'tt_download_finish_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_download_finish / tt_hour_data_log.tt_sum_download_start AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_download_finish_rate',
            'tt_next_day_open_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_next_day_open / tt_hour_data_log.tt_sum_active AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_next_day_open_rate',
            'tt_next_day_open_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_next_day_open AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_next_day_open_cost',
            'tt_active_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_active / tt_hour_data_log.tt_sum_click AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_active_rate',
            'tt_game_addiction_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_game_addiction / tt_hour_data_log.tt_sum_active AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_game_addiction_rate',
            'tt_game_addiction_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_game_addiction AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_game_addiction_cost',
            'tt_wifi_play_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_wifi_play / tt_hour_data_log.tt_sum_total_play AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_wifi_play_rate',
            'tt_play_over_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_play_over / tt_hour_data_log.tt_sum_total_play AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_play_over_rate',
            'tt_valid_play_cost' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_ori_cost / tt_hour_data_log.tt_sum_valid_play AS DECIMAL ( 12, 2 ) ) , 0 ) AS tt_valid_play_cost',
            'tt_valid_play_rate' => 'IFNULL ( CAST ( tt_hour_data_log.tt_sum_valid_play / tt_hour_data_log.tt_sum_total_play AS DECIMAL ( 12, 4 ) ) , 0 ) AS tt_valid_play_rate',
        ]
    ];

    const TX_FORMULA = [
        'tx_component_day_data_log' => [
            'tx_activated_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_activated_cost',
            'tx_click_activated_rate' => 'CAST ( SUM ( activated_count ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_click_activated_rate',
            'tx_reg_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( reg_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_reg_cost',
            'tx_reg_clk_rate' => 'CAST ( SUM ( reg_pv ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_reg_clk_rate',
            'tx_activate_register_rate' => 'CAST ( SUM ( reg_pv ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_activate_register_rate',
            'tx_mini_game_register_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( mini_game_register_users ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_register_cost',
            'tx_mini_game_register_rate' => 'CAST ( SUM ( mini_game_register_users ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_register_rate',
            'tx_first_pay_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( first_pay_count ) AS DECIMAL ( 12, 2 ) ) AS tx_first_pay_cost',
            'tx_first_pay_rate' => 'CAST ( SUM ( first_pay_count ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_first_pay_rate',
            'tx_key_behavior_conversions_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( key_behavior_conversions_count ) AS DECIMAL ( 12, 2 ) ) AS tx_key_behavior_conversions_cost',
            'tx_key_behavior_conversions_rate' => 'CAST ( SUM ( key_behavior_conversions_count ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_key_behavior_conversions_rate',
            'tx_mini_game_create_role_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( mini_game_create_role_users ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_create_role_cost',
            'tx_retention_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( retention_count ) AS DECIMAL ( 12, 2 ) ) AS tx_retention_cost',
            'tx_retention_rate' => 'CAST ( SUM ( retention_count ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_retention_rate',
            'tx_app_retention_d3_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( app_retention_d3_uv ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d3_cost',
            'tx_app_retention_d3_rate' => 'CAST ( SUM ( app_retention_d3_uv ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d3_rate',
            'tx_app_retention_d5_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( app_retention_d5_uv ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d5_cost',
            'tx_app_retention_d5_rate' => 'CAST ( SUM ( app_retention_d5_uv ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d5_rate',
            'tx_app_retention_d7_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( app_retention_d7_uv ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d7_cost',
            'tx_app_retention_d7_rate' => 'CAST ( SUM ( app_retention_d7_uv ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d7_rate',
            'tx_app_retention_lt7_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( app_retention_lt7 ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_lt7_cost',
            'tx_mini_game_retention_d1_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( mini_game_retention_d1 ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_retention_d1_cost',
            'tx_mini_game_retention_d1_rate' => 'CAST ( SUM ( mini_game_retention_d1 ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_retention_d1_rate',
            'tx_purchase_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( purchase_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_cost',
            'tx_purchase_clk_rate' => 'CAST ( SUM ( purchase_pv ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_clk_rate',
            'tx_purchase_act_rate' => 'CAST ( SUM ( purchase_pv ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_act_rate',
            'tx_purchase_roi' => 'CAST ( SUM ( purchase_amount ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_roi',
            'tx_purchase_act_arpu' => 'CAST ( SUM ( purchase_pv ) / SUM ( activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_act_arpu',
            'tx_purchase_reg_arpu' => 'CAST ( SUM ( purchase_amount ) / SUM ( reg_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_reg_arpu',
            'tx_purchase_reg_arppu' => 'CAST ( SUM ( purchase_amount ) / SUM ( purchase_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_reg_arppu',
            'tx_cheout_1d_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( cheout_pv_1d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_1d_cost',
            'tx_cheout_1d_rate' => 'CAST ( SUM ( cheout_pv_1d ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_1d_rate',
            'tx_cheout_fd_reward' => 'CAST ( SUM ( cheout_fd ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_fd_reward',
            'tx_cheout_3d_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( cheout_pv_3d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_3d_cost',
            'tx_cheout_3d_rate' => 'CAST ( SUM ( cheout_pv_3d ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_3d_rate',
            'tx_cheout_td_reward' => 'CAST ( SUM ( cheout_td ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_td_reward',
            'tx_cheout_5d_rate' => 'CAST ( SUM ( cheout_pv_5d ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_5d_rate',
            'tx_cheout_5d_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( cheout_pv_5d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_5d_cost',
            'tx_cheout_7d_rate' => 'CAST ( SUM ( cheout_pv_7d ) / SUM ( valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_7d_rate',
            'tx_cheout_7d_cost' => 'CAST ( SUM ( ori_cost ) / SUM ( cheout_pv_7d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_7d_cost',
            'tx_cheout_ow_reward' => 'CAST ( SUM ( cheout_ow ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_ow_reward',
            'tx_cheout_tw_reward' => 'CAST ( SUM ( cheout_tw ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_tw_reward',
            'tx_cheout_15d_reward' => 'CAST ( SUM ( cheout_15d ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_15d_reward',
            'tx_cheout_om_reward' => 'CAST ( SUM ( cheout_om ) / SUM ( ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_om_reward',
            'tx_mini_game_paying_arpu' => 'CAST ( SUM ( purchase_amount ) / SUM ( mini_game_register_users ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_paying_arpu',
            'tx_ori_cost' => 'SUM ( ods_tencent_component_day_data_log.ori_cost ) AS tx_sum_ori_cost',
            'tx_first_pay_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.first_pay_count ) AS tx_sum_first_pay_count',
            'tx_mini_game_create_role_users' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_create_role_users ) AS tx_sum_mini_game_create_role_users',
            'tx_app_retention_lt7' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_lt7 ) AS tx_sum_app_retention_lt7',
            'tx_retention_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.retention_count ) AS tx_sum_retention_count',
            'tx_click' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.valid_click_count ) AS tx_sum_click',
            'tx_show' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.view_count ) AS tx_sum_show',
            'tx_cost' => 'SUM ( ods_tencent_component_day_data_log.cost ) AS tx_sum_cost',
            'tx_click_rate' => 'IFNULL ( CAST ( ( SUM( valid_click_count ) / SUM( view_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_click_rate',
            'tx_convert' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.conversions_count ) AS tx_sum_convert',
            'tx_active_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.activated_count ) AS tx_sum_active_count',
            'tx_pay_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_pv  ) AS tx_sum_pay_count',
            'tx_reg_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.reg_pv  ) AS tx_sum_reg_count',
            'tx_convert_rate' => 'IFNULL ( CAST ( ( SUM( conversions_count ) / SUM( valid_click_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_convert_rate',
            'tx_pay_rate' => 'IFNULL ( CAST ( ( SUM( purchase_pv  ) / SUM( activated_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_pay_rate',
            'tx_reg_rate' => 'IFNULL ( CAST ( ( SUM( reg_pv  ) / SUM( activated_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_reg_rate',
            'tx_active_rate' => 'IFNULL ( CAST ( ( SUM( activated_count ) / SUM( valid_click_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_active_rate',
            'tx_active_cost' => 'IFNULL ( CAST ( ( SUM( activated_count ) / SUM( cost ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_active_cost',
            'tx_convert_cost' => 'IFNULL ( CAST ( ( SUM( conversions_count ) / SUM( cost ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_convert_cost',
            'tx_cpc' => 'IFNULL ( CAST ( ( SUM( ori_cost ) / SUM( valid_click_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_cpc',
            'tx_cpm' => 'IFNULL ( CAST ( ( SUM( ori_cost ) / SUM( view_count ) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS tx_cpm',
            'tx_activated_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.activated_count ) AS tx_sum_activated_count',
            'tx_valid_click_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.valid_click_count ) AS tx_sum_valid_click_count',
            'tx_reg_pv' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.reg_pv ) AS tx_sum_reg_pv',
            'tx_mini_game_register_users' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_register_users ) AS tx_sum_mini_game_register_users',
            'tx_key_behavior_conversions_count' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.key_behavior_conversions_count ) AS tx_sum_key_behavior_conversions_count',
            'tx_app_retention_d3_uv' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d3_uv ) AS tx_sum_app_retention_d3_uv',
            'tx_app_retention_d5_uv' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d5_uv ) AS tx_sum_app_retention_d5_uv',
            'tx_app_retention_d7_uv' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d7_uv ) AS tx_sum_app_retention_d7_uv',
            'tx_mini_game_retention_d1' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_retention_d1 ) AS tx_sum_mini_game_retention_d1',
            'tx_purchase_pv' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_pv ) AS tx_sum_purchase_pv',
            'tx_purchase_amount' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_amount ) AS tx_sum_purchase_amount',
            'tx_cheout_pv_1d' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_1d ) AS tx_sum_cheout_pv_1d',
            'tx_cheout_fd' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_fd ) AS tx_sum_cheout_fd',
            'tx_cheout_pv_3d' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_3d ) AS tx_sum_cheout_pv_3d',
            'tx_cheout_td' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_td ) AS tx_sum_cheout_td',
            'tx_cheout_pv_5d' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_5d ) AS tx_sum_cheout_pv_5d',
            'tx_cheout_pv_7d' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_7d ) AS tx_sum_cheout_pv_7d',
            'tx_cheout_ow' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_ow ) AS tx_sum_cheout_ow',
            'tx_cheout_tw' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_tw ) AS tx_sum_cheout_tw',
            'tx_cheout_om' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_om ) AS tx_sum_cheout_om',
            'tx_cheout_15d' => 'SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_15d ) AS tx_sum_cheout_15d',

        ],
        'main' => [
            'tx_activated_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_activated_cost',
            'tx_click_activated_rate' => 'CAST ( ( tx_sum_activated_count  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_click_activated_rate',
            'tx_reg_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_reg_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_reg_cost',
            'tx_reg_clk_rate' => 'CAST ( ( tx_sum_reg_pv  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_reg_clk_rate',
            'tx_activate_register_rate' => 'CAST ( ( tx_sum_reg_pv  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_activate_register_rate',
            'tx_mini_game_register_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_mini_game_register_users ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_register_cost',
            'tx_mini_game_register_rate' => 'CAST ( ( tx_sum_mini_game_register_users  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_register_rate',
            'tx_first_pay_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_first_pay_count ) AS DECIMAL ( 12, 2 ) ) AS tx_first_pay_cost',
            'tx_first_pay_rate' => 'tx_component_day_data_log.tx_first_pay_rate',
            'tx_key_behavior_conversions_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_key_behavior_conversions_count ) AS DECIMAL ( 12, 2 ) ) AS tx_key_behavior_conversions_cost',
            'tx_key_behavior_conversions_rate' => 'CAST ( ( tx_sum_key_behavior_conversions_count  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_key_behavior_conversions_rate',
            'tx_mini_game_create_role_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_mini_game_create_role_users ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_create_role_cost',
            'tx_retention_cost' => 'tx_component_day_data_log.tx_retention_cost',
            'tx_retention_rate' => 'CAST ( ( tx_sum_retention_count  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_retention_rate',
            'tx_app_retention_d3_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_app_retention_d3_uv ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d3_cost',
            'tx_app_retention_d3_rate' => 'CAST ( ( tx_sum_app_retention_d3_uv  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d3_rate',
            'tx_app_retention_d5_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_app_retention_d5_uv ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d5_cost',
            'tx_app_retention_d5_rate' => 'CAST ( ( tx_sum_app_retention_d5_uv  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d5_rate',
            'tx_app_retention_d7_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_app_retention_d7_uv ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_d7_cost',
            'tx_app_retention_d7_rate' => 'tx_component_day_data_log.tx_app_retention_d7_rate',
            'tx_app_retention_lt7_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_app_retention_lt7 ) AS DECIMAL ( 12, 2 ) ) AS tx_app_retention_lt7_cost',
            'tx_mini_game_retention_d1_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_mini_game_retention_d1 ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_retention_d1_cost',
            'tx_mini_game_retention_d1_rate' => 'CAST ( ( tx_sum_mini_game_retention_d1  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_mini_game_retention_d1_rate',
            'tx_purchase_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_purchase_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_cost',
            'tx_purchase_clk_rate' => 'CAST ( ( tx_sum_purchase_pv  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_clk_rate',
            'tx_purchase_act_rate' => 'CAST ( ( tx_sum_purchase_pv  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_act_rate',
            'tx_purchase_roi' => 'CAST ( ( tx_sum_purchase_amount  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_roi',
            'tx_purchase_act_arpu' => 'CAST ( ( tx_sum_purchase_pv  / tx_sum_activated_count ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_act_arpu',
            'tx_purchase_reg_arpu' => 'CAST ( ( tx_sum_purchase_amount  / tx_sum_reg_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_reg_arpu',
            'tx_purchase_reg_arppu' => 'CAST ( ( tx_sum_purchase_amount  / tx_sum_purchase_pv ) AS DECIMAL ( 12, 2 ) ) AS tx_purchase_reg_arppu',
            'tx_cheout_1d_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_cheout_pv_1d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_1d_cost',
            'tx_cheout_1d_rate' => 'CAST ( ( tx_sum_cheout_pv_1d  /  tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_1d_rate',
            'tx_cheout_fd_reward' => 'CAST ( ( tx_sum_cheout_fd  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_fd_reward',
            'tx_cheout_3d_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_cheout_pv_3d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_3d_cost',
            'tx_cheout_3d_rate' => 'CAST ( ( tx_sum_cheout_pv_3d  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_3d_rate',
            'tx_cheout_td_reward' => 'CAST ( ( tx_sum_cheout_td  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_td_reward',
            'tx_cheout_5d_rate' => 'CAST ( ( tx_sum_cheout_pv_5d  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_5d_rate',
            'tx_cheout_5d_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_cheout_pv_5d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_5d_cost',
            'tx_cheout_7d_rate' => 'CAST ( ( tx_sum_cheout_pv_7d  / tx_sum_valid_click_count ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_7d_rate',
            'tx_cheout_7d_cost' => 'CAST ( ( tx_sum_ori_cost  / tx_sum_cheout_pv_7d ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_7d_cost',
            'tx_cheout_ow_reward' => 'CAST ( ( tx_sum_cheout_ow  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_ow_reward',
            'tx_cheout_tw_reward' => 'CAST ( ( tx_sum_cheout_tw  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_tw_reward',
            'tx_cheout_15d_reward' => 'CAST ( ( tx_sum_cheout_15d  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_15d_reward',
            'tx_cheout_om_reward' => 'CAST ( ( tx_sum_cheout_om  / tx_sum_ori_cost ) AS DECIMAL ( 12, 2 ) ) AS tx_cheout_om_reward',
            'tx_mini_game_paying_arpu' => 'tx_component_day_data_log.tx_mini_game_paying_arpu',
        ]
    ];

    const FORMULA = [
        'ad2_common_log' => [
            'count_ad2' => "COUNT ( DISTINCT " . self::TABLE['ad2_common_log'] . ".ad2_id ) AS count_ad2",
            'count_ad2_delivering' => "COUNT ( DISTINCT " . self::TABLE['ad2_common_log'] . ".ad2_id , if ( " . self::TABLE['ad2_common_log'] . ".status = 'AD_STATUS_DELIVERY_OK' , TRUE , null ) ) AS count_ad2_delivering",
            'count_ad2_undeliveried' => "COUNT ( DISTINCT " . self::TABLE['ad2_common_log'] . ".ad2_id , if ( " . self::TABLE['ad2_common_log'] . ".is_cost = '0' , TRUE , null ) ) AS count_ad2_undeliveried",
            'sum_budget_for_cost_process' => "SUM ( if ( " . self::TABLE['ad2_common_log'] . ".opt_status = 'AD_STATUS_ENABLE' , budget , 0 ) ) as sum_budget_for_cost_process"
        ],
        'hour_data_log' => [
            'count' => "COUNT ( DISTINCT agent_site.agent_leader , if ( " . self::TABLE['hour_data_log'] . ".cost > 0 , TRUE , null ) ) AS count",
            'count_ad2_deliveried' => "COUNT ( DISTINCT " . self::TABLE['hour_data_log'] . ".ad2_id , if ( " . self::TABLE['hour_data_log'] . ".cost > 0 , TRUE , null ) ) AS count_ad2_deliveried",
            'count_cost_date' => "COUNT ( distinct cost_date , if ( " . self::TABLE['hour_data_log'] . ".cost > 0 , TRUE , null ) ) AS count_cost_date",
            'sum_cost' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.COST ) AS sum_cost',
            'sum_register' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.REGISTER ) AS sum_register',
            'sum_ori_cost' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.ORI_COST ) AS sum_ori_cost',
            'sum_click' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.CLICK ) AS sum_click',
            'sum_show' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.SHOW ) AS sum_show',
            'sum_convert' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.CONVERT ) AS sum_convert',
            'sum_active' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.ACTIVE ) AS sum_active',
            'sum_pay_count' => 'SUM ( ' . self::TABLE['hour_data_log'] . '.PAY_COUNT ) AS sum_pay_count',
            'count_ad3' => 'COUNT ( distinct ' . self::TABLE['hour_data_log'] . '.AD3_ID ) AS count_ad3',
            'sum_ori_cost_for_cost_process' => 'SUM ( if ( cost_date = CURRENT_DATE , ori_cost , 0 ) ) as sum_ori_cost_for_cost_process'
        ],
        'overview_log' => [
            'sum_first_day_pay_money' => "SUM ( " . self::TABLE['overview_log'] . ".day_first_day_pay_money ) AS sum_first_day_pay_money",

            'sum_first_day_pay_count' => "SUM ( " . self::TABLE['overview_log'] . ".day_first_day_pay_count ) AS sum_first_day_pay_count",

            'sum_day_first_day_pay_times' => "SUM ( day_first_day_pay_times ) AS sum_day_first_day_pay_times",
            'sum_total_pay_times' => "SUM ( total_pay_times ) AS sum_total_pay_times",
            'sum_click_count' => "SUM ( click_count ) AS sum_click_count",

            'sum_day_reg_count' => "SUM ( " . self::TABLE['overview_log'] . ".day_reg_uid_count ) AS sum_day_reg_count",
            'sum_day_reg_muid_count' => "SUM ( day_reg_muid_count ) AS sum_day_reg_muid_count",
            'sum_day_reg_muid_distinct_count' => "SUM ( day_reg_muid_distinct_count ) AS sum_day_reg_muid_distinct_count",
            'sum_day_action_muid_distinct_count' => "SUM ( day_action_muid_distinct_count ) AS sum_day_action_muid_distinct_count",
            'sum_day_action_muid_count' => "SUM ( day_action_muid_count ) AS sum_day_action_muid_count",
            'sum_day_reg_uid_count' => "SUM ( " . self::TABLE['overview_log'] . ".day_reg_uid_count ) AS sum_day_reg_uid_count",

            'sum_reg_uid_count' => "SUM ( " . self::TABLE['overview_log'] . ".day_reg_uid_count ) AS sum_reg_uid_count",

            'sum_total_pay_money' => "SUM ( day_total_pay_money ) AS sum_total_pay_money",
            'sum_total_pay_count' => "SUM ( IF ( day_total_pay_count > 0, 1, 0 ) ) AS sum_total_pay_count",
            'sum_day_second_login_count' => "SUM ( day_second_login_count ) AS sum_day_second_login_count",
            'sum_day_third_login_count' => "SUM ( day_third_login_count ) AS sum_day_third_login_count",
            'sum_day_seventh_login_count' => "SUM ( day_seventh_login_count ) AS sum_day_seventh_login_count",
            'sum_day_fifteenth_login_count' => "SUM ( day_fifteenth_login_count ) AS sum_day_fifteenth_login_count",

            'sum_day_three_login_uid_count' => "SUM ( day_three_login_uid_count ) AS sum_day_three_login_uid_count",

            'sum_second_day_pay_money' => "SUM ( day_second_day_pay_money ) AS sum_second_day_pay_money",
            'sum_third_day_pay_money' => "SUM ( day_third_day_pay_money ) AS sum_third_day_pay_money",
            'sum_seventh_day_pay_money' => "SUM ( day_seventh_day_pay_money ) AS sum_seventh_day_pay_money",
            'sum_fifteenth_day_pay_money' => "SUM ( day_fifteenth_day_pay_money ) AS sum_fifteenth_day_pay_money",
            'sum_thirty_day_pay_money' => "SUM ( day_thirty_day_pay_money ) AS sum_thirty_day_pay_money",

            'sum_day_old_root_game_reg_uid_count' => 'SUM ( IF ( is_old_root_game_muid = 1 , ' . self::TABLE['overview_log'] . '.day_reg_uid_count , 0 ) ) AS sum_day_old_root_game_reg_uid_count',
            'sum_day_old_clique_game_reg_uid_count' => 'SUM ( IF ( is_old_clique_game_muid = 1 , ' . self::TABLE['overview_log'] . '.day_reg_uid_count , 0 ) ) AS sum_day_old_clique_game_reg_uid_count',
        ],
        'reg_log' => [
            'reg_uid_count' => "COUNT ( uid ) AS reg_uid_count",
        ],
        'main' => [
            'total_pay_money' => 'IFNULL ( overview_log.sum_total_pay_money, 0 ) AS total_pay_money',
            'first_day_pay_money' => 'IFNULL ( overview_log.sum_first_day_pay_money, 0 ) AS first_day_pay_money',
            'seventh_day_pay_money' => 'IFNULL ( overview_log.sum_seventh_day_pay_money, 0 ) AS seventh_day_pay_money',
            'fifteenth_day_pay_money' => 'IFNULL ( overview_log.sum_fifteenth_day_pay_money, 0 ) AS fifteenth_day_pay_money',
            'thirty_day_pay_money' => 'IFNULL ( overview_log.sum_thirty_day_pay_money, 0 ) AS thirty_day_pay_money',
            'first_day_pay_count' => 'IFNULL ( overview_log.sum_first_day_pay_count, 0 ) AS first_day_pay_count',
            'day_first_day_pay_times' => 'IFNULL ( overview_log.sum_day_first_day_pay_times, 0 ) AS day_first_day_pay_times',
            'total_pay_times' => 'IFNULL ( overview_log.sum_total_pay_times, 0 ) AS total_pay_times',
            'click_count' => 'IFNULL ( overview_log.sum_click_count, 0 ) AS click_count',

            'first_day_ltv' => 'IFNULL ( CAST ( ( overview_log.sum_first_day_pay_money / overview_log.sum_day_reg_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_ltv',
            'total_ltv' => 'IFNULL ( CAST ( ( overview_log.sum_total_pay_money / overview_log.sum_day_reg_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_ltv',
            'first_day_pay_rate' => 'IFNULL ( CAST ( overview_log.sum_first_day_pay_count / overview_log.sum_day_reg_count AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate',
            'cost_per_first_day_pay' => 'IFNULL ( CAST ( hour_data_log.sum_cost / overview_log.sum_first_day_pay_count AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_first_day_pay',
            'first_day_roi' => 'IFNULL ( CAST ( ( overview_log.sum_first_day_pay_money / hour_data_log.sum_cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_roi',
            'total_roi' => 'IFNULL ( CAST ( ( overview_log.sum_total_pay_money / hour_data_log.sum_cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_roi',
            'first_day_arppu' => 'IFNULL ( CAST ( ( overview_log.sum_first_day_pay_money / overview_log.sum_first_day_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_arppu',
            'total_arppu' => 'IFNULL ( CAST ( ( overview_log.sum_total_pay_money / overview_log.sum_total_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS total_arppu',
            'action_uid_reg_rate' => 'IFNULL ( CAST ( ( overview_log.sum_day_reg_muid_distinct_count / overview_log.sum_day_action_muid_distinct_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS action_uid_reg_rate',
            'click_rate' => 'IFNULL ( CAST ( ( hour_data_log.sum_click / hour_data_log.sum_show ) AS DECIMAL ( 12, 4 ) ), 0 ) AS click_rate',
            'cost_per_convert' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / hour_data_log.sum_convert ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_convert',
            'cost_per_reg' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / overview_log.sum_reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_reg',
            'cost_per_active' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / hour_data_log.sum_active ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_active',
            'cost_per_pay' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / hour_data_log.sum_pay_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_per_pay',
            'cpm' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / ( hour_data_log.sum_show / 1000 ) ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cpm',
            'cpc' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / hour_data_log.sum_click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cpc',
            'convert_rate' => 'IFNULL ( CAST ( ( hour_data_log.sum_convert / hour_data_log.sum_click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS convert_rate',
            'reg_rate' => 'IFNULL ( CAST ( ( hour_data_log.sum_register / hour_data_log.sum_click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS reg_rate',
            'active_rate' => 'IFNULL ( CAST ( ( hour_data_log.sum_active / hour_data_log.sum_click ) AS DECIMAL ( 12, 4 ) ), 0 ) AS active_rate',
            'pay_rate' => 'IFNULL ( CAST ( ( hour_data_log.sum_pay_count / hour_data_log.sum_register ) AS DECIMAL ( 12, 4 ) ), 0 ) AS pay_rate',
            'media_cost_per_reg' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / hour_data_log.sum_register ) AS DECIMAL ( 12, 4 ) ), 0 ) AS media_cost_per_reg',
            'rate_day_stay_2' => 'IFNULL ( CAST ( ( overview_log.sum_day_second_login_count / overview_log.sum_day_reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_stay_2',
            'rate_day_stay_3' => 'IFNULL ( CAST ( ( overview_log.sum_day_third_login_count /  overview_log.sum_reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_stay_3',
            'rate_day_stay_7' => 'IFNULL ( CAST ( ( overview_log.sum_day_seventh_login_count /  overview_log.sum_reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_stay_7',
            'rate_day_stay_15' => 'IFNULL ( CAST ( ( overview_log.sum_day_fifteenth_login_count /  overview_log.sum_reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_stay_15',
            'cost_process' => 'IFNULL ( CAST ( ( hour_data_log.sum_ori_cost_for_cost_process / ad2_common_log.sum_budget_for_cost_process ) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_process',
            'rate_day_roi_2' => 'IFNULL ( CAST ( ( overview_log.sum_second_day_pay_money / hour_data_log.sum_cost ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_2',
            'rate_day_roi_3' => 'IFNULL ( CAST ( ( overview_log.sum_third_day_pay_money / hour_data_log.sum_cost) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_3',
            'rate_day_roi_7' => 'IFNULL ( CAST ( ( overview_log.sum_seventh_day_pay_money / hour_data_log.sum_cost) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_7',
            'rate_day_roi_15' => 'IFNULL ( CAST ( ( overview_log.sum_fifteenth_day_pay_money / hour_data_log.sum_cost) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_15',
            'rate_day_roi_30' => 'IFNULL ( CAST ( ( overview_log.sum_thirty_day_pay_money / hour_data_log.sum_cost) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_roi_30',

            'day_three_login_uid_count' => 'IFNULL ( overview_log.sum_day_three_login_uid_count, 0 ) AS day_three_login_uid_count',

            'cost_total_pay_times' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / overview_log.sum_total_pay_times) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_total_pay_times',
            'cost_day_first_day_pay_times' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / overview_log.sum_day_first_day_pay_times) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_day_first_day_pay_times',
            'cost_day_second_login_count' => 'IFNULL ( CAST ( ( hour_data_log.sum_cost / overview_log.sum_day_second_login_count) AS DECIMAL ( 12, 4 ) ), 0 ) AS cost_day_second_login_count',

            'day_old_root_game_reg_uid_count' => 'IFNULL ( CAST ( overview_log.sum_day_old_root_game_reg_uid_count AS DECIMAL ( 12, 4 ) ), 0 ) AS day_old_root_game_reg_uid_count',
            'day_old_clique_game_reg_uid_count' => 'IFNULL ( CAST ( overview_log.sum_day_old_clique_game_reg_uid_count AS DECIMAL ( 12, 4 ) ), 0 ) AS day_old_clique_game_reg_uid_count',

            'rate_day_old_root_game_reg_uid' => 'IFNULL ( CAST ( ( overview_log.sum_day_old_root_game_reg_uid_count / overview_log.sum_day_reg_muid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_old_root_game_reg_uid',
            'rate_day_old_clique_game_reg_uid' => 'IFNULL ( CAST ( ( overview_log.sum_day_old_clique_game_reg_uid_count / overview_log.sum_day_reg_muid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_old_clique_game_reg_uid',

            'rate_day_three_login_uid_count' => 'IFNULL ( CAST ( ( overview_log.sum_day_three_login_uid_count /  overview_log.sum_reg_uid_count ) AS DECIMAL ( 12, 4 ) ), 0 ) AS rate_day_three_login_uid_count',
        ]
    ];

    const TT_TARGET_CONFIG = [
        'tt_attribution_next_day_open_cnt' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_attribution_next_day_open_cnt']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_attribution_next_day_open_cnt , 0 ) AS tt_attribution_next_day_open_cnt'
            ]
        ],
        'tt_loan_completion' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_loan_completion']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_loan_completion , 0 ) AS tt_loan_completion'
            ]
        ],
        'tt_loan_completion_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_finish']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_loan_completion_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_download_finish'
            ]
        ],
        'tt_loan_completion_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_loan_completion'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_register']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_loan_completion_rate'],
                'tt_hour_data_log.tt_sum_loan_completion',
                'tt_hour_data_log.tt_sum_register'
            ]
        ],
        'tt_download_finish' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_finish']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_download_finish , 0 ) AS tt_download_finish',
            ]
        ],
        'tt_install_finish' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_install_finish']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_install_finish , 0 ) AS tt_install_finish'
            ]
        ],
        'tt_download_finish_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_finish']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_download_finish_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_download_finish'
            ]
        ],
        'tt_install_finish_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_install_finish']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_install_finish_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_install_finish'
            ]
        ],
        'tt_download_start_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_start'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_click']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_download_start_rate'],
                'tt_hour_data_log.tt_sum_download_start',
                'tt_hour_data_log.tt_sum_click'
            ]
        ],
        'tt_install_finish_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_install_finish'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_start']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_install_finish_rate'],
                'tt_hour_data_log.tt_sum_install_finish',
                'tt_hour_data_log.tt_sum_download_start'
            ]
        ],
        'tt_download_start' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_start']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_download_start , 0 ) AS tt_download_start',
            ]
        ],
        'tt_download_start_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_start'],
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_download_start_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_download_start'
            ]
        ],
        'tt_click_install' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_click_install']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_click_install , 0 ) AS tt_click_install',
            ]
        ],
        // 深度转化成本
        'tt_attribution_deep_convert_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_attribution_deep_convert_cost']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_attribution_deep_convert_cost , 0 ) AS tt_attribution_deep_convert_cost',
            ]
        ],
        // 深度转化数
        'tt_attribution_convert' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_attribution_convert']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_attribution_convert , 0 ) AS tt_attribution_convert',
            ]
        ],
        // 深度转化率
        'tt_deep_convert_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_deep_convert'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_convert']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_deep_convert_rate'],
                'tt_hour_data_log.tt_sum_deep_convert',
                'tt_hour_data_log.tt_sum_convert'
            ]
        ],// 深度转化数
        'tt_deep_convert' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_deep_convert'],
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_deep_convert , 0 ) AS tt_deep_convert',
            ]
        ],// 深度转化成本
        'tt_deep_convert_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_deep_convert'],
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_deep_convert_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_deep_convert'
            ]
        ],
        'tt_download_finish_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_finish'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_download_start']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_download_finish_rate'],
                'tt_hour_data_log.tt_sum_download_finish',
                'tt_hour_data_log.tt_sum_download_start'
            ]
        ],
        'tt_next_day_open_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_next_day_open'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_active']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_next_day_open_rate'],
                'tt_hour_data_log.tt_sum_next_day_open',
                'tt_hour_data_log.tt_sum_active'
            ]
        ],
        'tt_next_day_open_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_next_day_open']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_next_day_open_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_next_day_open'
            ]
        ],
        'tt_next_day_open' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_next_day_open']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_next_day_open , 0 ) AS tt_next_day_open',
            ]
        ],
        'tt_active_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_active'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_click']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_active_rate'],
                'tt_hour_data_log.tt_sum_active',
                'tt_hour_data_log.tt_sum_click'
            ]
        ],
        'tt_active' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_active']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_active , 0 ) AS tt_active',
            ]
        ],
        'tt_game_addiction' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_game_addiction']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_game_addiction , 0 ) AS tt_game_addiction',
            ]
        ],
        'tt_game_addiction_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_game_addiction'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_active']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_game_addiction_rate'],
                'tt_hour_data_log.tt_sum_game_addiction',
                'tt_hour_data_log.tt_sum_active'
            ]
        ],
        'tt_game_addiction_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_game_addiction']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_game_addiction_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_game_addiction'
            ]
        ],
        'tt_play_75_feed_break' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_75_feed_break']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_play_75_feed_break , 0 ) AS tt_play_75_feed_break',
            ]
        ],
        'tt_play_100_feed_break' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_100_feed_break']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_play_100_feed_break , 0 ) AS tt_play_100_feed_break',
            ]
        ],
        'tt_average_play_time_per_play' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_average_play_time_per_play']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_average_play_time_per_play , 0 ) AS tt_average_play_time_per_play',
            ]
        ],
        'tt_play_duration_sum' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_duration_sum']],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_play_duration_sum , 0 ) AS tt_play_duration_sum',
            ]
        ],
        'tt_wifi_play_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_wifi_play'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_total_play']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_wifi_play_rate'],
                'tt_hour_data_log.tt_sum_wifi_play',
                'tt_hour_data_log.tt_sum_total_play'
            ]
        ],
        'tt_valid_play' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_valid_play']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_valid_play , 0 ) AS tt_valid_play'
            ]
        ],
        'tt_play_25_feed_break' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_25_feed_break']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_play_25_feed_break , 0 ) AS tt_play_25_feed_break',
            ]
        ],
        'tt_play_over_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_over'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_total_play']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_play_over_rate'],
                'tt_hour_data_log.tt_sum_play_over',
                'tt_hour_data_log.tt_sum_total_play'
            ]
        ],
        'tt_total_play' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_total_play']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_total_play , 0 ) AS tt_total_play',
            ]
        ],
        'tt_valid_play_cost' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ori_cost'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_valid_play']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_valid_play_cost'],
                'tt_hour_data_log.tt_sum_ori_cost',
                'tt_hour_data_log.tt_sum_valid_play'
            ]
        ],
        'tt_valid_play_rate' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_valid_play'],
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_total_play']
            ],
            'main' => [
                self::TT_FORMULA['main']['tt_valid_play_rate'],
                'tt_hour_data_log.tt_sum_valid_play',
                'tt_hour_data_log.tt_sum_total_play'
            ]
        ],
        'tt_wifi_play' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_wifi_play']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_wifi_play , 0 ) AS tt_wifi_play',
            ]
        ],
        'tt_play_50_feed_break' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_50_feed_break']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_play_50_feed_break , 0 ) AS tt_play_50_feed_break',
            ]
        ],
        'tt_play_over' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_play_over']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_play_over , 0 ) AS tt_play_over',
            ]
        ],
        'tt_location_click' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_location_click']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_location_click , 0 ) AS tt_location_click',
            ]
        ],
        'tt_comment' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_comment']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_comment , 0 ) AS tt_comment',
            ]
        ],
        'tt_share' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_share']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_share , 0 ) AS tt_share',
            ]
        ],
        'tt_follow' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_follow']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_follow , 0 ) AS tt_follow',
            ]
        ],
        'tt_home_visited' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_home_visited']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_home_visited , 0 ) AS tt_home_visited',
            ]
        ],
        'tt_like' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_like']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_like , 0 ) AS `tt_like`',
            ]
        ],
        'tt_ies_music_click' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ies_music_click']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_ies_music_click , 0 ) AS tt_ies_music_click',
            ]
        ],
        'tt_ies_challenge_click' => [
            'tt_hour_data_log' => [
                self::TT_FORMULA['tt_hour_data_log']['tt_sum_ies_challenge_click']
            ],
            'main' => [
                'IFNULL ( tt_hour_data_log.tt_sum_ies_challenge_click , 0 ) AS tt_ies_challenge_click',
            ]
        ],
    ];

    const TARGET_CONFIG = [
        'tx_ori_cost' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost']],
            'main' => ['tx_component_day_data_log.tx_sum_ori_cost as tx_ori_cost']
        ],
        'tx_click' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_click']],
            'main' => ['tx_component_day_data_log.tx_sum_click as tx_click']
        ],
        'tx_show' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_show']],
            'main' => ['tx_component_day_data_log.tx_sum_show as tx_show']
        ],
        'tx_cost' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_cost']],
            'main' => ['tx_component_day_data_log.tx_sum_cost as tx_cost']
        ],
        'tx_click_rate' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_click_rate']],
            'main' => ['tx_component_day_data_log.tx_click_rate']
        ],
        'tx_convert' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_convert']],
            'main' => ['tx_component_day_data_log.tx_sum_convert as tx_convert']
        ],
        'tx_active_count' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_active_count']],
            'main' => ['tx_component_day_data_log.tx_sum_active_count as tx_active_count']
        ],
        'tx_pay_count' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_pay_count']],
            'main' => ['tx_component_day_data_log.tx_sum_pay_count as tx_pay_count']
        ],
        'tx_reg_count' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_reg_count']],
            'main' => ['tx_component_day_data_log.tx_sum_reg_count as tx_reg_count']
        ],
        'tx_convert_rate' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_convert_rate']],
            'main' => ['tx_component_day_data_log.tx_convert_rate']
        ],
        'tx_pay_rate' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_pay_rate']],
            'main' => ['tx_component_day_data_log.tx_pay_rate']
        ],
        'tx_reg_rate' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_reg_rate']],
            'main' => ['tx_component_day_data_log.tx_reg_rate']
        ],
        'tx_active_rate' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_active_rate']],
            'main' => ['tx_component_day_data_log.tx_active_rate']
        ],
        'tx_active_cost' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_active_cost']],
            'main' => ['tx_component_day_data_log.tx_active_cost']
        ],
        'tx_convert_cost' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_convert_cost']],
            'main' => ['tx_component_day_data_log.tx_convert_cost']
        ],
        'tx_cpc' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_cpc']],
            'main' => ['tx_component_day_data_log.tx_cpc']
        ],
        'tx_cpm' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_cpm']],
            'main' => ['tx_component_day_data_log.tx_cpm']
        ],
        'tx_activated_count' => [
            'tx_component_day_data_log' => [self::MEDIA_TABLE['tx_component_day_data_log'] . '.activated_count as tx_activated_count'],
            'main' => ['tx_component_day_data_log.tx_activated_count']
        ],
        'tx_activated_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_activated_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_activated_count',
            ]
        ],
        'tx_click_activated_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_click_activated_rate'],
                'tx_component_day_data_log.tx_sum_activated_count',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_reg_pv' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_reg_pv'],],
            'main' => ['tx_component_day_data_log.tx_sum_reg_pv as tx_reg_pv']
        ],
        'tx_register_by_display_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.register_by_display_count ) AS tx_sum_register_by_display_count',],
            'main' => ['tx_component_day_data_log.tx_sum_register_by_display_count as tx_register_by_display_count']
        ],
        'tx_register_by_click_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.register_by_click_count ) AS tx_sum_register_by_click_count',],
            'main' => ['tx_component_day_data_log.tx_sum_register_by_click_count as tx_register_by_click_count']
        ],
        'tx_reg_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_reg_pv']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_reg_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_reg_pv',
            ]
        ],
        'tx_reg_clk_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_reg_pv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_reg_clk_rate'],
                'tx_component_day_data_log.tx_sum_valid_click_count',
                'tx_component_day_data_log.tx_sum_reg_pv',
            ]
        ],
        'tx_activate_register_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_reg_pv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_activate_register_rate'],
                'tx_component_day_data_log.tx_sum_activated_count',
                'tx_component_day_data_log.tx_sum_reg_pv',
            ]
        ],
        'tx_reg_pla_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.reg_pla_pv ) AS tx_sum_reg_pla_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_reg_pla_pv as tx_reg_pla_pv'],
        ],
        'tx_web_register_uv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.web_register_uv ) AS tx_sum_web_register_uv',],
            'main' => ['tx_component_day_data_log.tx_sum_web_register_uv as tx_web_register_uv'],
        ],
        'tx_mini_game_register_users' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_register_users ) AS tx_sum_mini_game_register_users',],
            'main' => ['tx_component_day_data_log.tx_sum_mini_game_register_users as tx_mini_game_register_users'],
        ],
        'tx_mini_game_register_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_mini_game_register_users']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_mini_game_register_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_mini_game_register_users',
            ]
        ],
        'tx_mini_game_register_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_mini_game_register_users'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_mini_game_register_rate'],
                'tx_component_day_data_log.tx_sum_mini_game_register_users',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_first_pay_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.first_pay_count ) AS tx_sum_first_pay_count',],
            'main' => ['tx_component_day_data_log.tx_sum_first_pay_count as tx_first_pay_count'],
        ],
        'tx_first_pay_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_first_pay_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_first_pay_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_first_pay_count',
            ]
        ],
        'tx_first_pay_rate' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_first_pay_rate']],
            'main' => ['tx_component_day_data_log.tx_first_pay_rate']
        ],
        'tx_leads_purchase_uv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.leads_purchase_uv ) AS tx_sum_leads_purchase_uv',],
            'main' => ['tx_component_day_data_log.tx_sum_leads_purchase_uv as tx_leads_purchase_uv'],
        ],
        'tx_mini_game_first_paying_users' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_first_paying_users ) AS tx_sum_mini_game_first_paying_users',],
            'main' => ['tx_component_day_data_log.tx_sum_mini_game_first_paying_users as tx_mini_game_first_paying_users'],
        ],
        'tx_key_behavior_conversions_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.key_behavior_conversions_count ) AS tx_sum_key_behavior_conversions_count',],
            'main' => ['tx_component_day_data_log.tx_sum_key_behavior_conversions_count as tx_key_behavior_conversions_count'],
        ],
        'tx_key_behavior_conversions_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_key_behavior_conversions_count'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_key_behavior_conversions_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_key_behavior_conversions_count',
            ]
        ],
        'tx_key_behavior_conversions_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_key_behavior_conversions_count'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_key_behavior_conversions_rate'],
                'tx_component_day_data_log.tx_sum_key_behavior_conversions_count',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_game_authorize_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.game_authorize_count ) AS tx_sum_game_authorize_count',],
            'main' => ['tx_component_day_data_log.tx_sum_game_authorize_count as tx_game_authorize_count'],
        ],
        'tx_game_create_role_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.game_create_role_count ) AS tx_sum_game_create_role_count',],
            'main' => ['tx_component_day_data_log.tx_sum_game_create_role_count as tx_game_create_role_count'],
        ],
        'tx_mini_game_create_role_users' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_create_role_users ) AS tx_sum_mini_game_create_role_users',],
            'main' => ['tx_component_day_data_log.tx_sum_mini_game_create_role_users as tx_mini_game_create_role_users'],
        ],
        'tx_mini_game_create_role_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_mini_game_create_role_users'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_mini_game_create_role_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_mini_game_create_role_users',
            ]
        ],
        'tx_retention_count' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.retention_count ) AS tx_sum_retention_count',],
            'main' => ['tx_component_day_data_log.tx_sum_retention_count as tx_retention_count'],
        ],
        'tx_retention_cost' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_retention_cost']],
            'main' => ['tx_component_day_data_log.tx_retention_cost']
        ],
        'tx_retention_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_retention_count'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_retention_rate'],
                'tx_component_day_data_log.tx_sum_retention_count',
                'tx_component_day_data_log.tx_sum_activated_count',
            ]
        ],
        'tx_app_retention_d2_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d2_pv ) AS tx_sum_app_retention_d2_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d2_pv as tx_app_retention_d2_pv'],
        ],
        'tx_app_retention_d3_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d3_pv ) AS tx_sum_app_retention_d3_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d3_pv as tx_app_retention_d3_pv'],
        ],
        'tx_app_retention_d3_uv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d3_uv ) AS tx_sum_app_retention_d3_uv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d3_uv as tx_app_retention_d3_uv'],
        ],
        'tx_app_retention_d3_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_app_retention_d3_uv'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_app_retention_d3_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_app_retention_d3_uv',
            ]
        ],
        'tx_app_retention_d3_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_app_retention_d3_uv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_app_retention_d3_rate'],
                'tx_component_day_data_log.tx_sum_app_retention_d3_uv',
                'tx_component_day_data_log.tx_sum_activated_count',
            ]
        ],
        'tx_app_retention_d4_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d4_pv ) AS tx_sum_app_retention_d4_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d4_pv as tx_app_retention_d4_pv'],
        ],
        'tx_app_retention_d5_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d5_pv ) AS tx_sum_app_retention_d5_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d5_pv as tx_app_retention_d5_pv'],
        ],
        'tx_app_retention_d5_uv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d5_uv ) AS tx_sum_app_retention_d5_uv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d5_uv as tx_app_retention_d5_uv'],
        ],
        'tx_app_retention_d5_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_app_retention_d5_uv'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_app_retention_d5_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_app_retention_d5_uv',
            ]
        ],
        'tx_app_retention_d5_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_app_retention_d5_uv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_app_retention_d5_rate']
            ]
        ],
        'tx_app_retention_d6_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d6_pv ) AS tx_sum_app_retention_d6_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d6_pv as tx_app_retention_d6_pv'],
        ],
        'tx_app_retention_d7_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d7_pv ) AS tx_sum_app_retention_d7_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d7_pv as tx_app_retention_d7_pv'],
        ],
        'tx_app_retention_d7_uv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_d7_uv ) AS tx_sum_app_retention_d7_uv',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_d7_uv as tx_app_retention_d7_uv'],
        ],
        'tx_app_retention_d7_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_app_retention_d7_uv']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_app_retention_d7_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_app_retention_d7_uv',
            ]
        ],
        'tx_app_retention_lt7' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.app_retention_lt7 ) AS tx_sum_app_retention_lt7',],
            'main' => ['tx_component_day_data_log.tx_sum_app_retention_lt7 as tx_app_retention_lt7'],
        ],
        'tx_app_retention_lt7_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_app_retention_lt7'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_app_retention_lt7_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_app_retention_lt7',
            ]
        ],
        'tx_mini_game_retention_d1' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.mini_game_retention_d1 ) AS tx_sum_mini_game_retention_d1',],
            'main' => ['tx_component_day_data_log.tx_sum_mini_game_retention_d1 as tx_mini_game_retention_d1'],
        ],
        'tx_mini_game_retention_d1_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_mini_game_retention_d1']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_mini_game_retention_d1_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_mini_game_retention_d1',
            ]
        ],
        'tx_mini_game_retention_d1_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_mini_game_retention_d1'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_mini_game_retention_d1_rate'],
                'tx_component_day_data_log.tx_sum_mini_game_retention_d1',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_purchase_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_pv ) AS tx_sum_purchase_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_purchase_pv as tx_purchase_pv'],
        ],
        'tx_purchase_imp_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_imp_pv ) AS tx_sum_purchase_imp_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_purchase_imp_pv as tx_purchase_imp_pv'],
        ],
        'tx_purchase_clk_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_clk_pv ) AS tx_sum_purchase_clk_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_purchase_clk_pv as tx_purchase_clk_pv'],
        ],
        'tx_purchase_amount' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_amount ) AS tx_sum_purchase_amount',],
            'main' => ['tx_component_day_data_log.tx_sum_purchase_amount as tx_purchase_amount'],
        ],
        'tx_purchase_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_pv'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_purchase_pv',
            ]
        ],
        'tx_purchase_clk_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_pv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_clk_rate'],
                'tx_component_day_data_log.tx_sum_purchase_pv',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_purchase_act_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_pv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_act_rate'],
                'tx_component_day_data_log.tx_sum_purchase_pv',
                'tx_component_day_data_log.tx_sum_activated_count',
            ]
        ],
        'tx_purchase_roi' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_amount'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_roi'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_purchase_amount',
            ]
        ],
        'tx_purchase_act_arpu' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_pv'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_activated_count'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_act_arpu'],
                'tx_component_day_data_log.tx_sum_purchase_pv',
                'tx_component_day_data_log.tx_sum_activated_count',
            ]
        ],
        'tx_purchase_reg_arpu' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_amount'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_reg_pv'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_reg_arpu'],
                'tx_component_day_data_log.tx_sum_purchase_amount',
                'tx_component_day_data_log.tx_sum_reg_pv',
            ]
        ],
        'tx_purchase_reg_arppu' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_amount'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_purchase_pv'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_purchase_reg_arppu'],
                'tx_component_day_data_log.tx_sum_purchase_pv',
                'tx_component_day_data_log.tx_sum_purchase_amount',
            ]
        ],
        'tx_cheout_pv_1d' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_1d ) AS tx_sum_cheout_pv_1d',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_pv_1d as tx_cheout_pv_1d'],
        ],
        'tx_cheout_fd' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_fd ) AS tx_sum_cheout_fd',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_fd as tx_cheout_fd'],
        ],
        'tx_cheout_1d_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_1d'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_1d_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_pv_1d',
            ]
        ],
        'tx_cheout_1d_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_1d'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_1d_rate'],
                'tx_component_day_data_log.tx_sum_cheout_pv_1d',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_cheout_fd_reward' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_fd'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_fd_reward'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_fd',
            ]
        ],
        'tx_cheout_pv_3d' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_3d ) AS tx_sum_cheout_pv_3d',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_pv_3d as tx_cheout_pv_3d'],
        ],
        'tx_cheout_td' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_td ) AS tx_sum_cheout_td',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_td as tx_cheout_td'],
        ],
        'tx_cheout_3d_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_3d'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_3d_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_pv_3d',
            ]
        ],
        'tx_cheout_3d_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_3d'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_3d_rate'],
                'tx_component_day_data_log.tx_sum_cheout_pv_3d',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_cheout_td_reward' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_td'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_td_reward'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_td',
            ]
        ],
        'tx_cheout_pv_5d' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_5d ) AS tx_sum_cheout_pv_5d',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_pv_5d as tx_cheout_pv_5d'],
        ],
        'tx_cheout_5d_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_5d'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_5d_rate'],
                'tx_component_day_data_log.tx_sum_cheout_pv_5d',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_cheout_5d_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_5d'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_5d_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_pv_5d',
            ]
        ],
        'tx_cheout_pv_7d' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_pv_7d ) AS tx_sum_cheout_pv_7d',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_pv_7d as tx_cheout_pv_7d'],
        ],
        'tx_cheout_ow' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_ow ) AS tx_sum_cheout_ow',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_ow as tx_cheout_ow'],
        ],
        'tx_cheout_7d_cost' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_7d'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_7d_cost'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_pv_7d',
            ]
        ],
        'tx_cheout_7d_rate' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_pv_7d'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_valid_click_count']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_7d_rate'],
                'tx_component_day_data_log.tx_sum_cheout_pv_7d',
                'tx_component_day_data_log.tx_sum_valid_click_count',
            ]
        ],
        'tx_cheout_ow_reward' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_ow'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_ow_reward'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_ow',
            ]
        ],
        'tx_cheout_tw' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_tw ) AS tx_sum_cheout_tw',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_tw as tx_cheout_tw'],
        ],
        'tx_cheout_tw_reward' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_tw'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost'],
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_tw_reward'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_tw',
            ]
        ],
        'tx_purchase_clk_15d_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_clk_15d_pv ) AS tx_sum_purchase_clk_15d_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_purchase_clk_15d_pv as tx_purchase_clk_15d_pv'],
        ],
        'tx_cheout_15d' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_15d ) AS tx_sum_cheout_15d',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_15d as tx_cheout_15d'],
        ],
        'tx_cheout_15d_reward' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_15d'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_15d_reward'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_15d',
            ]
        ],
        'tx_purchase_clk_30d_pv' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.purchase_clk_30d_pv ) AS tx_sum_purchase_clk_30d_pv',],
            'main' => ['tx_component_day_data_log.tx_sum_purchase_clk_30d_pv as tx_purchase_clk_30d_pv'],
        ],
        'tx_cheout_om' => [
            'tx_component_day_data_log' => ['SUM ( ' . self::MEDIA_TABLE['tx_component_day_data_log'] . '.cheout_om ) AS tx_sum_cheout_om',],
            'main' => ['tx_component_day_data_log.tx_sum_cheout_om as tx_cheout_om'],
        ],
        'tx_cheout_om_reward' => [
            'tx_component_day_data_log' => [
                self::TX_FORMULA['tx_component_day_data_log']['tx_cheout_om'],
                self::TX_FORMULA['tx_component_day_data_log']['tx_ori_cost']
            ],
            'main' => [
                self::TX_FORMULA['main']['tx_cheout_om_reward'],
                'tx_component_day_data_log.tx_sum_ori_cost',
                'tx_component_day_data_log.tx_sum_cheout_om',
            ]
        ],
        'tx_mini_game_paying_arpu' => [
            'tx_component_day_data_log' => [self::TX_FORMULA['tx_component_day_data_log']['tx_mini_game_paying_arpu']],
            'main' => ['tx_component_day_data_log.tx_mini_game_paying_arpu']
        ],
        'tt_is_inefficient' => [
            'tt_inefficient_material_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_inefficient'],
            'material_file_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_inefficient'],
            'main' => ['driver.is_inefficient']
        ],
        'tt_is_similar_queue_material' => [
            'tt_inefficient_material_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_queue_material'],
            'material_file_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_queue_material'],
            'main' => ['driver.is_similar_queue_material']
        ],
        'tt_is_similar_expected_queue_material' => [
            'tt_inefficient_material_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_expected_queue_material'],
            'material_file_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_expected_queue_material'],
            'main' => ['driver.is_similar_expected_queue_material']
        ],
        'tt_is_similar_material' => [
            'tt_inefficient_material_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_material'],
            'material_file_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_material'],
            'main' => ['driver.is_similar_material']
        ],
        'tt_is_ad_low_quality_material' => [
            'tt_inefficient_material_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_ad_low_quality_material'],
            'material_file_log' => [self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_ad_low_quality_material'],
            'main' => ['driver.is_ad_low_quality_material']
        ],
        'agent_group_id' => [
            'ad2_common_log' => ['agent_site.agent_group_id'],
            'main' => ['driver.agent_group_id as agent_group_id']
        ],
        'agent_group_name' => [
            'ad2_common_log' => ['group_concat( distinct agent_site.agent_group_name ) as agent_group_name'],
            'main' => ['ad2_common_log.agent_group_name as agent_group_name']
        ],
        // 操作状态
        'advertising_opt_status' => [
            'ad2_common_log' => [self::TABLE['ad2_common_log'] . '.opt_status'],
            'main' => ['ad2_common_log.opt_status as advertising_opt_status']
        ],
        // 账号id
        'account_id' => [
            'ad2_common_log' => [self::TABLE['ad2_common_log'] . '.account_id'],
            'main' => ['ad2_common_log.account_id']
        ],
        // 平台
        'platform' => [
            'ad2_common_log' => [self::TABLE['ad2_common_log'] . '.platform'],
            'main' => ['driver.platform'],
        ],
        // 测试人数
        'count' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['count']],
            'main' => ['IFNULL ( hour_data_log.count , 0 ) AS count']
        ],
        // 计划数
        'count_ad2' => [
            'ad2_common_log' => [self::FORMULA['ad2_common_log']['count_ad2']],
            'main' => ['IFNULL ( ad2_common_log.count_ad2 , 0 ) AS count_ad2']
        ],
        // 消耗计划数
        'count_ad2_deliveried' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['count_ad2_deliveried']],
            'main' => ['IFNULL ( hour_data_log.count_ad2_deliveried , 0 ) AS count_ad2_deliveried']
        ],
        // 在投计划数
        'count_ad2_delivering' => [
            'ad2_common_log' => [self::FORMULA['ad2_common_log']['count_ad2_delivering']],
            'main' => ['IFNULL ( ad2_common_log.count_ad2_delivering , 0 ) AS count_ad2_delivering']
        ],
        // 未测试计划数
        'count_ad2_undeliveried' => [
            'ad2_common_log' => [self::FORMULA['ad2_common_log']['count_ad2_undeliveried']],
            'main' => ['IFNULL ( ad2_common_log.count_ad2_undeliveried , 0 ) AS count_ad2_undeliveried']
        ],
        // 创意数
        'count_ad3' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['count_ad3']],
            'main' => ['IFNULL ( hour_data_log.count_ad3 , 0 ) AS count_ad3']
        ],
        // 消耗天数
        'count_cost_date' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['count_cost_date']],
            'main' => ['IFNULL ( hour_data_log.count_cost_date , 0 ) AS count_cost_date']
        ],
        // 累计付费金额
        'total_pay_money' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_total_pay_money']],
            'main' => [self::FORMULA['main']['total_pay_money']]
        ],
        // 首日付费金额
        'first_day_pay_money' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_first_day_pay_money']],
            'main' => [self::FORMULA['main']['first_day_pay_money']]
        ],
        // 7日付费金额
        'seventh_day_pay_money' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_seventh_day_pay_money']],
            'main' => [self::FORMULA['main']['seventh_day_pay_money']]
        ],
        // 15日付费金额
        'fifteenth_day_pay_money' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_fifteenth_day_pay_money']],
            'main' => [self::FORMULA['main']['fifteenth_day_pay_money']]
        ],
        // 30日付费金额
        'thirty_day_pay_money' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_thirty_day_pay_money']],
            'main' => [self::FORMULA['main']['thirty_day_pay_money']]
        ],
        // 首日付费次数
        'day_first_day_pay_times' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_day_first_day_pay_times']],
            'main' => [self::FORMULA['main']['day_first_day_pay_times']]
        ],
        // 累计付费次数
        'total_pay_times' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_total_pay_times']],
            'main' => [self::FORMULA['main']['total_pay_times']]
        ],
        // 首日付费次数成本
        'cost_total_pay_times' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'overview_log' => [self::FORMULA['overview_log']['sum_day_first_day_pay_times']],
            'main' => [
                self::FORMULA['main']['cost_day_first_day_pay_times'],
                'sum_cost',
                'sum_day_first_day_pay_times'
            ]
        ],
        // 累计付费次数成本
        'cost_day_first_day_pay_times' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'overview_log' => [self::FORMULA['overview_log']['sum_total_pay_times']],
            'main' => [
                self::FORMULA['main']['cost_total_pay_times'],
                'sum_cost',
                'sum_total_pay_times'
            ]
        ],
        'click_count' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_click_count']],
            'main' => [
                self::FORMULA['main']['click_count'],
            ]
        ],
        // 首日ltv
        'first_day_ltv' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_first_day_pay_money'],
                self::FORMULA['overview_log']['sum_day_reg_count']
            ],
            'main' => [
                self::FORMULA['main']['first_day_ltv'],
                'sum_first_day_pay_money',
                'sum_day_reg_count'
            ]
        ],
        // 首日付费人数
        'first_day_pay_count' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_first_day_pay_count']],
            'main' => [self::FORMULA['main']['first_day_pay_count']]
        ],
        // 首日付费率
        'first_day_pay_rate' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_first_day_pay_count'],
                self::FORMULA['overview_log']['sum_day_reg_count']
            ],
            'main' => [
                self::FORMULA['main']['first_day_pay_rate'],
                'sum_first_day_pay_count',
                'sum_day_reg_count'
            ]
        ],
        // 首日付费成本
        'cost_per_first_day_pay' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'overview_log' => [self::FORMULA['overview_log']['sum_first_day_pay_count']],
            'main' => [
                self::FORMULA['main']['cost_per_first_day_pay'],
                'sum_cost',
                'sum_first_day_pay_count'
            ]
        ],
        // 累计ltv
        'total_ltv' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_day_reg_count'], self::FORMULA['overview_log']['sum_total_pay_money']],
            'main' => [self::FORMULA['main']['total_ltv'], 'sum_total_pay_money', 'sum_day_reg_count']
        ],
        // 首日roi
        'first_day_roi' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'overview_log' => [self::FORMULA['overview_log']['sum_first_day_pay_money']],
            'main' => [self::FORMULA['main']['first_day_roi'], 'sum_cost', 'sum_first_day_pay_money']
        ],
        // 累计roi
        'total_roi' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'overview_log' => [self::FORMULA['overview_log']['sum_total_pay_money']],
            'main' => [
                self::FORMULA['main']['total_roi'],
                'sum_cost',
                'sum_total_pay_money'
            ]
        ],
        // 首日arppu
        'first_day_arppu' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_first_day_pay_money'],
                self::FORMULA['overview_log']['sum_first_day_pay_count']
            ],
            'main' => [
                self::FORMULA['main']['first_day_arppu'],
                'sum_first_day_pay_money',
                'sum_first_day_pay_count'
            ]
        ],
        // 累计arppu
        'total_arppu' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_total_pay_money'],
                self::FORMULA['overview_log']['sum_total_pay_count']
            ],
            'main' => [
                self::FORMULA['main']['total_arppu'],
                'sum_total_pay_money',
                'sum_total_pay_count'
            ]
        ],
        // 返点后消耗
        "cost" => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'main' => ['IFNULL ( hour_data_log.sum_cost , 0 ) AS cost']
        ],
        // 注册数
        'reg_uid_count' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_reg_uid_count']],
            'main' => ['sum_reg_uid_count as reg_uid_count']
        ],
        // 激活注册率
        'action_uid_reg_rate' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_reg_muid_distinct_count'],
                self::FORMULA['overview_log']['sum_day_action_muid_distinct_count']
            ],
            'main' => [
                self::FORMULA['main']['action_uid_reg_rate'],
                'sum_day_reg_muid_distinct_count',
                'sum_day_action_muid_distinct_count'
            ]
        ],
        // 注册成本
        'cost_per_reg' => [
            'hour_data_log' => [
                self::FORMULA['hour_data_log']['sum_cost'],
            ],
            'overview_log' => [
                self::FORMULA['overview_log']['sum_reg_uid_count']
            ],
            'main' => [self::FORMULA['main']['cost_per_reg'], 'sum_cost', 'sum_reg_uid_count']
        ],
        // 注册设备数
        'reg_muid_count' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_day_reg_muid_count']],
            'main' => ['IFNULL ( overview_log.sum_day_reg_muid_count , 0 ) AS reg_muid_count']
        ],
        // 激活设备数
        'action_muid_count' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_day_action_muid_count']],
            'main' => ['IFNULL ( overview_log.sum_day_action_muid_count , 0 ) AS action_muid_count']
        ],
        // cpc COST / CLICK
        'cpc' => [
            'hour_data_log' => [
                self::FORMULA['hour_data_log']['sum_cost'],
                self::FORMULA['hour_data_log']['sum_click']
            ],
            'main' => [self::FORMULA['main']['cpc'], 'sum_cost', 'sum_click']
        ],
        // click
        'click' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_click']],
            'main' => ['IFNULL ( hour_data_log.sum_click , 0 ) AS click']
        ],
        // cpm COST/(SHOW/1000)
        'cpm' => [
            'hour_data_log' => [
                self::FORMULA['hour_data_log']['sum_cost'],
                self::FORMULA['hour_data_log']['sum_show']
            ],
            'main' => [self::FORMULA['main']['cpm'], 'sum_cost', 'sum_show']
        ],
        // show
        'show' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_show']],
            'main' => ['IFNULL ( hour_data_log.sum_show , 0 ) AS `show`']
        ],
        // 总花费
        'ori_cost' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_ori_cost']],
            'main' => ['IFNULL ( hour_data_log.sum_ori_cost , 0 ) AS ori_cost']
        ],
        // 点击率
        'click_rate' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_click'], self::FORMULA['hour_data_log']['sum_show']],
            'main' => [self::FORMULA['main']['click_rate'], 'sum_click', 'sum_show']
        ],
        // 转化成本
        'cost_per_convert' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost'], self::FORMULA['hour_data_log']['sum_convert']],
            'main' => [self::FORMULA['main']['cost_per_convert'], 'sum_cost', 'sum_convert']
        ],
        // 转化数
        'convert' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_convert']],
            'main' => ['IFNULL ( hour_data_log.sum_convert , 0 ) AS `convert`']
        ],
        // 激活成本
        'cost_per_active' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost'], self::FORMULA['hour_data_log']['sum_active']],
            'main' => [self::FORMULA['main']['cost_per_active'], 'sum_cost', 'sum_active']
        ],
        // 付费成本
        'cost_per_pay' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost'], self::FORMULA['hour_data_log']['sum_pay_count']],
            'main' => [self::FORMULA['main']['cost_per_pay'], 'sum_cost', 'sum_pay_count']
        ],
        // 付费数
        'pay_count' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_pay_count']],
            'main' => ['IFNULL ( hour_data_log.sum_pay_count , 0 ) AS pay_count']
        ],
        // 注册率
        'reg_rate' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_register'], self::FORMULA['hour_data_log']['sum_click']],
            'main' => [self::FORMULA['main']['reg_rate'], 'sum_register', 'sum_click']
        ],
        // 激活率
        'active_rate' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_active'], self::FORMULA['hour_data_log']['sum_click']],
            'main' => [self::FORMULA['main']['active_rate'], 'sum_active', 'sum_click']
        ],
        // 激活数
        'active_count' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_active']],
            'main' => ['IFNULL ( hour_data_log.sum_active , 0 ) AS active_count']
        ],
        // 转化率
        'convert_rate' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_convert'], self::FORMULA['hour_data_log']['sum_click']],
            'main' => [self::FORMULA['main']['convert_rate'], 'sum_convert', 'sum_click']
        ],
        // 注册数
        'reg_count' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_register']],
            'main' => ['IFNULL ( hour_data_log.sum_register , 0 ) AS reg_count']
        ],
        // 付费率
        'pay_rate' => [
            'hour_data_log' => [
                self::FORMULA['hour_data_log']['sum_pay_count'],
                self::FORMULA['hour_data_log']['sum_register']
            ],
            'main' => [self::FORMULA['main']['pay_rate'], 'sum_pay_count', 'sum_register']
        ],
        'media_cost_per_reg' => [
            'hour_data_log' => [
                self::FORMULA['hour_data_log']['sum_cost'],
                self::FORMULA['hour_data_log']['sum_register']
            ],
            'main' => [
                self::FORMULA['main']['media_cost_per_reg'],
                'sum_cost',
                'sum_register'
            ]
        ],
        // 次留成本
        'cost_day_second_login_count' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_second_login_count'],
            ],
            'hour_data_log' => [
                self::FORMULA['hour_data_log']['sum_cost'],
            ],
            'main' => [
                self::FORMULA['main']['cost_day_second_login_count'],
                'sum_cost',
                'sum_day_second_login_count',
            ]
        ],
        // 次留
        'rate_day_stay_2' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_second_login_count'],
                self::FORMULA['overview_log']['sum_day_reg_uid_count']
            ],
            'main' => [
                self::FORMULA['main']['rate_day_stay_2'],
                'sum_day_second_login_count',
                'sum_day_reg_uid_count'
            ]
        ],
        // 三留
        'rate_day_stay_3' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_third_login_count'],
                self::FORMULA['overview_log']['sum_reg_uid_count'],
            ],
            'main' => [self::FORMULA['main']['rate_day_stay_3'], 'sum_day_third_login_count', 'sum_reg_uid_count']
        ],
        // 七留
        'rate_day_stay_7' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_seventh_login_count'],
                self::FORMULA['overview_log']['sum_reg_uid_count']
            ],
            'main' => [self::FORMULA['main']['rate_day_stay_7'], 'sum_day_seventh_login_count', 'sum_reg_uid_count']
        ],
        // 十五留
        'rate_day_stay_15' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_fifteenth_login_count'],
                self::FORMULA['overview_log']['sum_reg_uid_count']
            ],
            'main' => [self::FORMULA['main']['rate_day_stay_15'], 'sum_day_fifteenth_login_count', 'sum_reg_uid_count']
        ],
        'cost_process' => [
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_ori_cost_for_cost_process']],
            'ad2_common_log' => [self::FORMULA['ad2_common_log']['sum_budget_for_cost_process']],
            'main' => [self::FORMULA['main']['cost_process'], 'sum_ori_cost_for_cost_process', 'sum_budget_for_cost_process']
        ],
        'day_three_login_uid_count' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_day_three_login_uid_count']],
            'main' => [
                self::FORMULA['main']['day_three_login_uid_count'],
                'sum_day_three_login_uid_count'
            ]
        ],
        'rate_day_three_login_uid_count' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_three_login_uid_count'],
                self::FORMULA['overview_log']['sum_reg_uid_count']
            ],
            'main' => [
                self::FORMULA['main']['rate_day_three_login_uid_count'],
                'sum_day_three_login_uid_count',
                'sum_reg_uid_count',
            ]
        ],
        'day_old_root_game_reg_uid_count' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_old_root_game_reg_uid_count'],
            ],
            'main' => [
                'sum_day_old_root_game_reg_uid_count',
                self::FORMULA['main']['day_old_root_game_reg_uid_count'],
            ]
        ],
        'day_old_clique_game_reg_uid_count' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_old_clique_game_reg_uid_count'],
            ],
            'main' => [
                self::FORMULA['main']['day_old_clique_game_reg_uid_count'],
                'sum_day_old_clique_game_reg_uid_count',
            ]
        ],
        'rate_day_old_root_game_reg_uid' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_old_root_game_reg_uid_count'],
                self::FORMULA['overview_log']['sum_day_reg_muid_count'],
            ],
            'main' => [
                self::FORMULA['main']['rate_day_old_root_game_reg_uid'],
                'sum_day_old_root_game_reg_uid_count',
                'sum_day_reg_muid_count',
            ]
        ],
        'rate_day_old_clique_game_reg_uid' => [
            'overview_log' => [
                self::FORMULA['overview_log']['sum_day_old_clique_game_reg_uid_count'],
                self::FORMULA['overview_log']['sum_day_reg_muid_count'],
            ],
            'main' => [
                self::FORMULA['main']['rate_day_old_clique_game_reg_uid'],
                'sum_day_old_clique_game_reg_uid_count',
                'sum_day_reg_muid_count',
            ]
        ],
        // 2回
        'rate_day_roi_2' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_second_day_pay_money']],
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'main' => [self::FORMULA['main']['rate_day_roi_2'], 'sum_second_day_pay_money', 'sum_cost']
        ],
        // 3回
        'rate_day_roi_3' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_third_day_pay_money']],
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'main' => [self::FORMULA['main']['rate_day_roi_3'], 'sum_third_day_pay_money', 'sum_cost']
        ],
        // 7回
        'rate_day_roi_7' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_seventh_day_pay_money']],
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'main' => [self::FORMULA['main']['rate_day_roi_7'], 'sum_seventh_day_pay_money', 'sum_cost']
        ],
        // 15回
        'rate_day_roi_15' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_fifteenth_day_pay_money']],
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'main' => [self::FORMULA['main']['rate_day_roi_15'], 'sum_fifteenth_day_pay_money', 'sum_cost']
        ], // 15回
        'rate_day_roi_30' => [
            'overview_log' => [self::FORMULA['overview_log']['sum_thirty_day_pay_money']],
            'hour_data_log' => [self::FORMULA['hour_data_log']['sum_cost']],
            'main' => [self::FORMULA['main']['rate_day_roi_30'], 'sum_thirty_day_pay_money', 'sum_cost']
        ],
        'material_name' => [
            'material_log' => [self::TABLE['material_log'] . '.name AS material_name'],
            'main' => ['driver.material_name'],
        ],
        'material_id' => [
            'material_log' => [self::TABLE['material_log'] . '.material_id AS material_id'],
            'main' => ['driver.material_id'],
        ],
        'author' => [
            'material_log' => [self::TABLE['material_log'] . '.author AS author'],
            'main' => ['driver.author'],
        ],
        'c_author' => [
            'material_log' => [self::TABLE['material_log'] . '.c_author AS c_author'],
            'main' => ['driver.c_author'],
        ],
        'a_author' => [
            'material_log' => [self::TABLE['material_log'] . '.a_author AS a_author'],
            'main' => ['driver.a_author'],
        ],
        'm1_author' => [
            'material_log' => [self::TABLE['material_log'] . '.m1_author AS m1_author'],
            'main' => ['driver.m1_author'],
        ],
        'm2_author' => [
            'material_log' => [self::TABLE['material_log'] . '.m2_author AS m2_author'],
            'main' => ['driver.m2_author'],
        ],
        'm3_author' => [
            'material_log' => [self::TABLE['material_log'] . '.m3_author AS m3_author'],
            'main' => ['driver.m3_author'],
        ],
        'm4_author' => [
            'material_log' => [self::TABLE['material_log'] . '.m4_author AS m4_author'],
            'main' => ['driver.m4_author'],
        ],
        'actor' => [
            'material_log' => [self::TABLE['material_log'] . '.actor AS actor'],
            'main' => ['driver.actor'],
        ],
        'shoot' => [
            'material_log' => [self::TABLE['material_log'] . '.shoot AS shoot'],
            'main' => ['driver.shoot'],
        ],
        'theme_pid' => [
            'material_log' => [self::TABLE['material_log'] . '.theme_id AS theme_id'],
            'main' => ['driver.theme_id'],
        ],
        'theme_id' => [
            'material_log' => [self::TABLE['material_log'] . '.theme_id AS theme_id'],
            'main' => ['driver.theme_id'],
        ],
        'original' => [
            'material_log' => [self::TABLE['material_log'] . '.original AS original'],
            'main' => ['driver.original'],
        ],
        'effect_grade7' => [
            'material_log' => [self::TABLE['material_log'] . '.effect_grade7 AS effect_grade7'],
            'main' => ['driver.effect_grade7'],
        ],
        'effect_grade30' => [
            'material_log' => [self::TABLE['material_log'] . '.effect_grade30 AS effect_grade30'],
            'main' => ['driver.effect_grade30'],
        ],
        'approved_rate' => [
            'material_log' => [self::TABLE['material_log'] . '.approved_rate AS approved_rate'],
            'main' => ['driver.approved_rate'],
        ],
        'tencent_approved_rate' => [
            'material_log' => [self::TABLE['material_log'] . '.tencent_approved_rate AS tencent_approved_rate'],
            'main' => ['driver.tencent_approved_rate'],
        ],
        'tencent_7day_approved' => [
            'material_log' => [self::TABLE['material_log'] . '.tencent_7day_approved AS tencent_7day_approved'],
            'main' => ['driver.tencent_7day_approved'],
        ],
        'is_priority' => [
            'material_log' => [self::TABLE['material_log'] . '.is_priority AS is_priority'],
            'main' => ['driver.is_priority'],
        ],
//        'bitrate' => [
//            'material_file_log' => [self::TABLE['material_file_log'] . '.bitrate'],
//            'main' => ['driver.bitrate'],
//        ],
//        'duration' => [
//            'material_file_log' => [self::TABLE['material_file_log'] . '.duration'],
//            'main' => ['driver.duration'],
//        ],
//        'size' => [
//            'material_file_log' => [self::TABLE['material_file_log'] . '.size'],
//            'main' => ['driver.size'],
//        ],
        'signature' => [
            'main' => ['driver.signature'],
        ],
    ];

    const FILTER_CONFIG = [
        'tt_is_inefficient' => [
            'material_file_log' => self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_inefficient',
        ],
        'tt_is_similar_queue_material' => [
            'material_file_log' => self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_queue_material',
        ],
        'tt_is_similar_expected_queue_material' => [
            'material_file_log' => self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_expected_queue_material',
        ],
        'tt_is_similar_material' => [
            'material_file_log' => self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_similar_material',
        ],
        'tt_is_ad_low_quality_material' => [
            'material_file_log' => self::MEDIA_TABLE['tt_inefficient_material_log'] . '.is_ad_low_quality_material',
        ],
        'game_id' => [
            'account_common_log' => 'site_game.game_id',
            'material_file_log' => 'site_game.game_id',
            'ad2_common_log' => 'site_game.game_id',
            'hour_data_log' => self::TABLE['hour_data_log'] . '.game_id',
            'overview_log' => self::TABLE['overview_log'] . '.game_id',
            'reg_log' => self::TABLE['reg_log'] . '.game_id',
            'tt_hour_data_log' => self::MEDIA_TABLE['tt_hour_data_log'] . '.game_id',
            'tx_component_day_data_log' => 'game.game_id',
        ],
        'root_game_id' => [
            'account_common_log' => 'game.root_game_id',
            'material_file_log' => 'game.root_game_id',
            'ad2_common_log' => 'game.root_game_id',
            'hour_data_log' => 'game.root_game_id',
            'overview_log' => 'game.root_game_id',
            'reg_log' => 'game.root_game_id',
            'tt_hour_data_log' => 'game.game_id',
            'tx_component_day_data_log' => 'game.game_id',
        ],
        'main_game_id' => [
            'account_common_log' => 'game.main_game_id',
            'material_file_log' => 'game.main_game_id',
            'ad2_common_log' => 'game.main_game_id',
            'hour_data_log' => 'game.main_game_id',
            'tt_hour_data_log' => 'game.main_game_id',
            'overview_log' => 'game.main_game_id',
            'reg_log' => 'game.main_game_id',
            'tx_component_day_data_log' => 'game.main_game_id',
        ],
        'agent_group_id' => [
            'account_common_log' => 'agent_site.agent_group_id',
//            'material_file_log' => 'agent_site.agent_group_id',
            'ad2_common_log' => 'agent_site.agent_group_id',
            'ad3_common_log' => 'agent_site.agent_group_id',
            'hour_data_log' => 'agent_site.agent_group_id',
            'overview_log' => 'agent_site.agent_group_id',
            'reg_log' => 'agent_site.agent_group_id',
            'tt_hour_data_log' => 'agent_site.agent_group_id',
            'tx_component_day_data_log' => 'agent_site.agent_group_id',
        ],
        'agent_leader' => [
            'account_common_log' => self::TABLE['account_common_log'] . '.account_leader',
            'ad2_common_log' => 'agent_site.agent_leader',
            'ad3_common_log' => 'agent_site.agent_leader',
            'hour_data_log' => 'agent_site.agent_leader',
            'overview_log' => 'agent_site.agent_leader',
            'reg_log' => 'agent_site.agent_leader',
            'tt_hour_data_log' => 'agent_site.agent_leader',
            'tx_component_day_data_log' => 'agent_site.agent_leader',
        ],
//        'site_id' => [
//            // 此处因为权限注入要写死site_game
//            'account_common_log' => 'site_game.site_id',
//            'material_file_log' => 'site_game.site_id',
//            'ad2_common_log' => 'site_game.site_id',
//            'ad3_common_log' => 'site_game.site_id',
//            'hour_data_log' => self::TABLE['hour_data_log'] . '.site_id',
//            'overview_log' => self::TABLE['overview_log'] . '.site_id',
//            'reg_log' => self::TABLE['reg_log'] . '.site_id',
//            'tt_hour_data_log' => self::MEDIA_TABLE['tt_hour_data_log'] . '.site_id',
//        ],
        'ad2_id' => [
            'account_common_log' => self::TABLE['ad2_common_log'] . '.ad2_id',
            'material_file_log' => self::TABLE['ad2_common_log'] . '.ad2_id',
            'ad2_common_log' => self::TABLE['ad2_common_log'] . '.ad2_id',
            'ad3_common_log' => self::TABLE['ad3_common_log'] . '.ad2_id',
            'hour_data_log' => self::TABLE['hour_data_log'] . '.ad2_id',
            'overview_log' => self::TABLE['overview_log'] . '.ad2_id',
            'reg_log' => self::TABLE['reg_log'] . '.adgroup_id',
            'tt_hour_data_log' => self::MEDIA_TABLE['tt_hour_data_log'] . '.ad_id',
            'tx_component_day_data_log' => self::TABLE['ad2_common_log'] . '.ad2_id',
        ],
        'advertising_opt_status' => [
            'account_common_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'material_file_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'ad2_common_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'ad3_common_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'hour_data_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'overview_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'reg_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'tt_hour_data_log' => self::TABLE['ad2_common_log'] . '.opt_status',
            'tx_component_day_data_log' => self::TABLE['ad2_common_log'] . '.opt_status',
        ],
        'advertising_status' => [
            'account_common_log' => self::TABLE['ad2_common_log'] . '.status',
            'material_file_log' => self::TABLE['ad2_common_log'] . '.status',
            'ad2_common_log' => self::TABLE['ad2_common_log'] . '.status',
            'ad3_common_log' => self::TABLE['ad2_common_log'] . '.status',
            'hour_data_log' => self::TABLE['ad2_common_log'] . '.status',
            'overview_log' => self::TABLE['ad2_common_log'] . '.status',
            'reg_log' => self::TABLE['ad2_common_log'] . '.status',
            'tt_hour_data_log' => self::TABLE['ad2_common_log'] . '.status',
            'tx_component_day_data_log' => self::TABLE['ad2_common_log'] . '.status',
        ],
        'ad2_os' => [
            'account_common_log' => self::TABLE['ad2_common_log'] . '.os',
            'material_file_log' => self::TABLE['ad2_common_log'] . '.os',
            'ad2_common_log' => self::TABLE['ad2_common_log'] . '.os',
            'ad3_common_log' => self::TABLE['ad2_common_log'] . '.os',
            'hour_data_log' => self::TABLE['ad2_common_log'] . '.os',
            'overview_log' => self::TABLE['ad2_common_log'] . '.os',
            'reg_log' => self::TABLE['ad2_common_log'] . '.os',
            'tt_hour_data_log' => self::TABLE['ad2_common_log'] . '.os',
            'tx_component_day_data_log' => self::TABLE['ad2_common_log'] . '.os',
        ],
        'os' => [
            'account_common_log' => 'game.os',
            'material_file_log' => 'game.os',
            'ad2_common_log' => 'game.os',
            'ad3_common_log' => 'game.os',
            'hour_data_log' => 'game.os',
            'overview_log' => 'game.os',
            'reg_log' => 'game.os',
            'tt_hour_data_log' => 'game.os',
            'tx_component_day_data_log' => 'game.os',
        ],
//        'web_creator' => [
//            'ad2_common_log' => 'web_creator',
//            'ad3_common_log' => 'web_creator',
//            'material_file_log' => 'web_creator',
//        ],
        'inventory_type' => [
            'account_common_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'hour_data_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'overview_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'reg_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'tt_hour_data_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'ad2_common_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'ad3_common_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'material_file_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
            'tx_component_day_data_log' => self::TABLE['ad2_common_log'] . '.inventory_type',
        ],
        'account_id' => [
            'account_common_log' => self::TABLE['account_common_log'] . '.account_id',
            'material_file_log' => self::TABLE['ad3_common_log'] . '.account_id',
            'ad2_common_log' => self::TABLE['ad2_common_log'] . '.account_id',
            'ad3_common_log' => self::TABLE['ad3_common_log'] . '.account_id',
            'hour_data_log' => self::TABLE['hour_data_log'] . '.account_id',
            'overview_log' => self::TABLE['ad2_common_log'] . '.account_id',
            'reg_log' => self::TABLE['ad2_common_log'] . '.account_id',
            'tt_hour_data_log' => self::TABLE['ad3_common_log'] . '.account_id',
            'tx_component_day_data_log' => self::TABLE['ad3_common_log'] . '.account_id',
        ],
        'title' => [
            'account_common_log' => self::TABLE['ad3_common_log'] . '.title',
            'material_file_log' => self::TABLE['ad3_common_log'] . '.title',
            'ad2_common_log' => self::TABLE['ad3_common_log'] . '.title',
            'ad3_common_log' => self::TABLE['ad3_common_log'] . '.title',
            'hour_data_log' => self::TABLE['ad3_common_log'] . '.title',
            'overview_log' => self::TABLE['ad3_common_log'] . '.title',
            'reg_log' => self::TABLE['ad3_common_log'] . '.title',
            'tt_hour_data_log' => self::TABLE['ad3_common_log'] . '.title',
            'tx_component_day_data_log' => self::TABLE['ad3_common_log'] . '.title',
        ],
        'material_file_id' => [
            'account_common_log' => self::TABLE['material_file_log'] . '.id',
            'material_file_log' => self::TABLE['material_file_log'] . '.id',
            'ad2_common_log' => self::TABLE['material_file_log'] . '.id',
            'ad3_common_log' => self::TABLE['material_file_log'] . '.id',
            'hour_data_log' => self::TABLE['material_file_log'] . '.id',
            'overview_log' => self::TABLE['material_file_log'] . '.id',
            'reg_log' => self::TABLE['material_file_log'] . '.id',
            'tt_hour_data_log' => self::TABLE['material_file_log'] . '.id',
            'tx_component_day_data_log' => self::TABLE['material_file_log'] . '.id',
        ],
        'material_filename' => [
            'account_common_log' => self::TABLE['material_file_log'] . '.filename',
            'material_file_log' => self::TABLE['material_file_log'] . '.filename',
            'ad2_common_log' => self::TABLE['material_file_log'] . '.filename',
            'ad3_common_log' => self::TABLE['material_file_log'] . '.filename',
            'hour_data_log' => self::TABLE['material_file_log'] . '.filename',
            'overview_log' => self::TABLE['material_file_log'] . '.filename',
            'reg_log' => self::TABLE['material_file_log'] . '.filename',
            'tt_hour_data_log' => self::TABLE['material_file_log'] . '.filename',
            'tx_component_day_data_log' => self::TABLE['material_file_log'] . '.filename',
        ],
        'material_id' => [
            'account_common_log' => self::TABLE['material_file_log'] . '.material_id',
            'material_file_log' => self::TABLE['material_file_log'] . '.material_id',
            'ad2_common_log' => self::TABLE['material_file_log'] . '.material_id',
            'ad3_common_log' => self::TABLE['material_file_log'] . '.material_id',
            'hour_data_log' => self::TABLE['material_file_log'] . '.material_id',
            'overview_log' => self::TABLE['material_file_log'] . '.material_id',
            'reg_log' => self::TABLE['material_file_log'] . '.material_id',
            'tt_hour_data_log' => self::TABLE['material_file_log'] . '.material_id',
            'tx_component_day_data_log' => self::TABLE['material_file_log'] . '.material_id',
        ],
        'material_name' => [
            'account_common_log' => self::TABLE['material_log'] . '.name',
            'material_file_log' => self::TABLE['material_log'] . '.name',
            'ad2_common_log' => self::TABLE['material_log'] . '.name',
            'ad3_common_log' => self::TABLE['material_log'] . '.name',
            'hour_data_log' => self::TABLE['material_log'] . '.name',
            'overview_log' => self::TABLE['material_log'] . '.name',
            'reg_log' => self::TABLE['material_log'] . '.name',
            'tt_hour_data_log' => self::TABLE['material_log'] . '.name',
            'tx_component_day_data_log' => self::TABLE['material_log'] . '.name',
        ],
        'theme_pid' => [
            'material_file_log' => self::TABLE['material_log'] . '.theme_id',
        ],
        'signature' => [
            'material_file_log' => self::TABLE['material_file_log'] . '.signature',
        ],
        'theme_id' => [
            'material_file_log' => self::TABLE['material_log'] . '.theme_id',
        ],
        'original' => [
            'material_file_log' => self::TABLE['material_log'] . '.original',
        ],
        'effect_grade30' => [
            'material_file_log' => self::TABLE['material_log'] . '.effect_grade30',
        ],
        'effect_grade7' => [
            'material_file_log' => self::TABLE['material_log'] . '.effect_grade7',
        ],
        'is_priority' => [
            'material_file_log' => self::TABLE['material_log'] . '.is_priority',
        ],
        'bitrate' => [
            'material_file_log' => self::TABLE['material_file_log'] . '.bitrate',
        ],
        'duration' => [
            'material_file_log' => self::TABLE['material_file_log'] . '.duration',
        ],
        'size' => [
            'material_file_log' => self::TABLE['material_file_log'] . '.size',
        ]
    ];

    const CALC_CONFIG = [
        'count' => [
            'main' => 'count'
        ],
        'count_ad2' => [
            'main' => 'count_ad2'
        ],
        'count_ad2_deliveried' => [
            'main' => 'count_ad2_deliveried'
        ],
        'count_ad2_delivering' => [
            'main' => 'count_ad2_delivering'
        ],
        'count_ad2_undeliveried' => [
            'main' => 'count_ad2_undeliveried'
        ],
        'count_ad3' => [
            'main' => 'count_ad3'
        ],
        'cost' => [
            'main' => 'cost'
        ],
        'first_day_ltv' => [
            'main' => 'first_day_ltv'
        ],
        'first_day_arppu' => [
            'main' => 'first_day_arppu'
        ],
        'first_day_roi' => [
            'main' => 'first_day_roi'
        ],
        'first_day_pay_rate' => [
            'main' => 'first_day_pay_rate'
        ],
        'total_roi' => [
            'main' => 'total_roi'
        ],
        'total_ltv' => [
            'main' => 'total_ltv'
        ],
        'total_arppu' => [
            'main' => 'total_arppu'
        ],
        'click_rate' => [
            'main' => 'click_rate'
        ],
        'convert_rate' => [
            'main' => 'convert_rate'
        ],
        'pay_rate' => [
            'main' => 'pay_rate'
        ],
        'cost_per_reg' => [
            'main' => 'cost_per_reg'
        ],
        'count_cost_date' => [
            'main' => 'count_cost_date'
        ],
        'duration' => [
            'main' => 'duration'
        ],
        'size' => [
            'main' => 'size / (1024*1024) '
        ],
        'bitrate' => [
            'main' => 'bitrate'
        ],
        'cost_per_pay' => [
            'main' => 'cost_per_pay'
        ],
    ];
}
