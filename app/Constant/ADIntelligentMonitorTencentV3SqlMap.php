<?php

namespace App\Constant;

class ADIntelligentMonitorTencentV3SqlMap
{
    const TENCENTV3_FORMULA = [
        'account_total_balance' => 'ods_tencent_account_log.total_balance',
        'adgroup_id' => 'ifnull(ods_tencent_campaign_log.campaign_id,0)',
        'adgroup_ori_cost' => 'ifnull(ods_tencent_ad_hour_data_log.ori_cost,0)',
        'adgroup_register' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'adgroup_first_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'adgroup_seventh_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_seventh_day_pay_money,0))',
        'adgroup_fifteenth_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_fifteenth_day_pay_money,0))',
        'adgroup_thirty_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_thirty_day_pay_money,0))',
        'adgroup_total_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'adgroup_fist_day_pay_count' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'adgroup_create_time' => "ifnull(ods_tencent_campaign_log.created_time,'')",
        'ad_id' => 'ifnull(ods_tencent_ad_log.ad_id,0)',
        'ad_ori_cost' => 'ifnull(ods_tencent_ad_hour_data_log.ori_cost,0)',
        'ad_register' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'ad_first_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'ad_seventh_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_seventh_day_pay_money,0))',
        'ad_fifteenth_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_fifteenth_day_pay_money,0))',
        'ad_thirty_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_thirty_day_pay_money,0))',
        'ad_total_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'ad_fist_day_pay_count' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'ad_create_time' => "ifnull(ods_tencent_ad_log.adcreative_created_time,'')",
    ];

    const TENCENTV3_FORMULA_ALIAS = [
        'account_total_balance' => self::TENCENTV3_FORMULA['account_total_balance'] . ' as account_total_balance',
        'adgroup_id' => self::TENCENTV3_FORMULA['adgroup_id'] . ' as campaign_id',
        'adgroup_ori_cost' => self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ' as adgroup_ori_cost',
        'adgroup_register' => self::TENCENTV3_FORMULA['adgroup_register'] . ' as adgroup_register',
        'adgroup_first_day_pay_money' => self::TENCENTV3_FORMULA['adgroup_first_day_pay_money'] . ' as adgroup_first_day_pay_money',
        'adgroup_seventh_day_pay_money' => self::TENCENTV3_FORMULA['adgroup_seventh_day_pay_money'] . ' as adgroup_seventh_day_pay_money',
        'adgroup_fifteenth_day_pay_money' => self::TENCENTV3_FORMULA['adgroup_fifteenth_day_pay_money'] . ' as adgroup_fifteenth_day_pay_money',
        'adgroup_thirty_day_pay_money' => self::TENCENTV3_FORMULA['adgroup_thirty_day_pay_money'] . ' as adgroup_thirty_day_pay_money',
        'adgroup_total_pay_money' => self::TENCENTV3_FORMULA['adgroup_total_pay_money'] . ' as adgroup_total_pay_money',
        'adgroup_fist_day_pay_count' => self::TENCENTV3_FORMULA['adgroup_fist_day_pay_count'] . ' as adgroup_fist_day_pay_count',
        'adgroup_create_time' => self::TENCENTV3_FORMULA['adgroup_create_time'] . ' as adgroup_create_time',
        'adgroup_create_time_relative' => self::TENCENTV3_FORMULA['adgroup_create_time'] . ' as adgroup_create_time_relative',
        'ad_id' => self::TENCENTV3_FORMULA['ad_id'] . ' as ad_id',
        'ad_ori_cost' => self::TENCENTV3_FORMULA['ad_ori_cost'] . ' as ad_ori_cost',
        'ad_register' => self::TENCENTV3_FORMULA['ad_register'] . ' as ad_register',
        'ad_first_day_pay_money' => self::TENCENTV3_FORMULA['ad_first_day_pay_money'] . ' as ad_first_day_pay_money',
        'ad_seventh_day_pay_money' => self::TENCENTV3_FORMULA['ad_seventh_day_pay_money'] . ' as ad_seventh_day_pay_money',
        'ad_fifteenth_day_pay_money' => self::TENCENTV3_FORMULA['ad_fifteenth_day_pay_money'] . ' as ad_fifteenth_day_pay_money',
        'ad_thirty_day_pay_money' => self::TENCENTV3_FORMULA['ad_thirty_day_pay_money'] . ' as ad_thirty_day_pay_money',
        'ad_total_pay_money' => self::TENCENTV3_FORMULA['ad_total_pay_money'] . ' as ad_total_pay_money',
        'ad_fist_day_pay_count' => self::TENCENTV3_FORMULA['ad_fist_day_pay_count'] . ' as ad_fist_day_pay_count',
        'ad_create_time' => self::TENCENTV3_FORMULA['ad_create_time'] . ' as ad_create_time',
        'ad_create_time_relative' => self::TENCENTV3_FORMULA['ad_create_time'] . ' as ad_create_time_relative',
    ];

    const TENCENTV3_TARGET = [
        // account
        'account_total_balance' => [
            'ods_tencent_account_log' => [
                self::TENCENTV3_FORMULA_ALIAS['account_total_balance']
            ]
        ],
        // adgroup
        'adgroup_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ') AS adgroup_ori_cost'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'ods_tencent_ad_hour_data_log' => []
        ],
        'adgroup_register' => [
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_register'],
            ]
        ],
        'adgroup_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_first_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS adgroup_first_day_roi'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_first_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ]
        ],
        'adgroup_seventh_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_seventh_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS adgroup_seventh_day_roi'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_seventh_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ]
        ],
        'adgroup_fifteenth_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_fifteenth_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS adgroup_fifteenth_day_roi'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_fifteenth_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ]
        ],
        'adgroup_thirty_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_thirty_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS adgroup_thirty_day_roi'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_thirty_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ]
        ],
        'adgroup_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_total_pay_money'] . '/' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS adgroup_total_roi'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_total_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ]
        ],
        'adgroup_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ' /' . self::TENCENTV3_FORMULA['adgroup_register'] . ') AS DECIMAL(12, 4)), 0) as adgroup_cost_per_reg'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_register'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ],
        ],
        'adgroup_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['adgroup_ori_cost'] . ' /' . self::TENCENTV3_FORMULA['adgroup_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS adgroup_cost_per_first_day_pay'
            ],
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_fist_day_pay_count'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['adgroup_ori_cost'].' AS adgroup_ori_cost',
            ],
        ],
        'adgroup_total_pay_money' => [
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_total_pay_money'],
            ]
        ],
        'adgroup_create_time' => [
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
                self::TENCENTV3_FORMULA_ALIAS['adgroup_create_time']
            ]
        ],
        'adgroup_create_time_relative' => [
            'ods_tencent_campaign_log' => [
                self::TENCENTV3_FORMULA_ALIAS['adgroup_id'],
                self::TENCENTV3_FORMULA_ALIAS['adgroup_create_time_relative']
            ]
        ],
        // ad
        'ad_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ') AS ad_ori_cost'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'ods_tencent_ad_hour_data_log' => []
        ],
        'ad_register' => [
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_register'],
            ]
        ],
        'ad_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_first_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS ad_first_day_roi'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_first_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ]
        ],
        'ad_seventh_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_seventh_day_pay_money'] . '/' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS ad_seventh_day_roi'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_seventh_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ]
        ],
        'ad_fifteenth_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_fifteenth_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS ad_fifteenth_day_roi'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_fifteenth_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ]
        ],
        'ad_thirty_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_thirty_day_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS ad_thirty_day_roi'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_thirty_day_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ]
        ],
        'ad_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_total_pay_money'] . '/ ' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS ad_total_roi'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_total_pay_money'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ]
        ],
        'ad_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_ori_cost'] . ' /' . self::TENCENTV3_FORMULA['ad_register'] . ') AS DECIMAL(12, 4)), 0) as ad_cost_per_reg'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_register'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ],
        ],
        'ad_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TENCENTV3_FORMULA['ad_ori_cost'] . '/' . self::TENCENTV3_FORMULA['ad_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS ad_cost_per_first_day_pay'
            ],
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_fist_day_pay_count'],
            ],
            'group-by-ods_tencent_ad_hour_data_log' => [
                self::TENCENTV3_FORMULA['ad_ori_cost'].' AS ad_ori_cost',
            ],
        ],
        'ad_total_pay_money' => [
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_total_pay_money'],
            ]
        ],
        'ad_create_time' => [
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
                self::TENCENTV3_FORMULA_ALIAS['ad_create_time']
            ]
        ],
        'ad_create_time_relative' => [
            'ods_tencent_ad_log' => [
                self::TENCENTV3_FORMULA_ALIAS['ad_id'],
                self::TENCENTV3_FORMULA_ALIAS['ad_create_time_relative']
            ]
        ],
    ];

    const TENCENTV3_JOIN = [
        // adgroup
        'adgroup_ori_cost' => [
            'ods_tencent_campaign_log' . '-' . 'ods_tencent_ad_hour_data_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'ods_tencent_ad_hour_data_log.campaign_id'],
                    ['ods_tencent_campaign_log.account_id', 'ods_tencent_ad_hour_data_log.account_id'],
                ],
                'where' => [
                    ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                ]
            ]
        ],
        'adgroup_register' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ]
        ],
        'adgroup_first_day_roi' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_seventh_day_roi' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_fifteenth_day_roi' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_thirty_day_roi' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_total_roi' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_cost_per_reg' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_cost_per_first_day_pay' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'adgroup_total_pay_money' => [
            'ods_tencent_campaign_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_campaign_log.campaign_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ]
        ],
        // ad
        'ad_ori_cost' => [
            'ods_tencent_ad_log' . '-' . 'ods_tencent_ad_hour_data_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'ods_tencent_ad_hour_data_log.ad_id'],
                    ['ods_tencent_ad_log.account_id', 'ods_tencent_ad_hour_data_log.account_id'],
                ],
                'where' => [
                    ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                ]
            ]
        ],
        'ad_register' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ]
        ],
        'ad_first_day_roi' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_seventh_day_roi' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_fifteenth_day_roi' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_thirty_day_roi' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_total_roi' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_cost_per_reg' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_cost_per_first_day_pay' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_tencent_ad_hour_data_log' => [
                'group-by' => [
                    'ods_tencent_ad_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_tencent_ad_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_tencent_ad_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'ad_total_pay_money' => [
            'ods_tencent_ad_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_tencent_ad_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TENCENT]
                ]
            ]
        ],
    ];

    const TENCENTV3_GROUP = [
        'account_total_balance' => 'ods_tencent_account_log.account_id',
        'adgroup_ori_cost' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_register' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_first_day_roi' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_seventh_day_roi' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_fifteenth_day_roi' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_thirty_day_roi' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_total_roi' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_cost_per_reg' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_cost_per_first_day_pay' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_total_pay_money' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_create_time' => 'ods_tencent_campaign_log.campaign_id',
        'adgroup_create_time_relative' => 'ods_tencent_campaign_log.campaign_id',
        'ad_ori_cost' => 'ods_tencent_ad_log.ad_id',
        'ad_register' => 'ods_tencent_ad_log.ad_id',
        'ad_first_day_roi' => 'ods_tencent_ad_log.ad_id',
        'ad_seventh_day_roi' => 'ods_tencent_ad_log.ad_id',
        'ad_fifteenth_day_roi' => 'ods_tencent_ad_log.ad_id',
        'ad_thirty_day_roi' => 'ods_tencent_ad_log.ad_id',
        'ad_total_roi' => 'ods_tencent_ad_log.ad_id',
        'ad_cost_per_reg' => 'ods_tencent_ad_log.ad_id',
        'ad_cost_per_first_day_pay' => 'ods_tencent_ad_log.ad_id',
        'ad_total_pay_money' => 'ods_tencent_ad_log.ad_id',
        'ad_create_time' => 'ods_tencent_ad_log.ad_id',
        'ad_create_time_relative' => 'ods_tencent_ad_log.ad_id',
    ];

    const TENCENTV3_WHERE = [
        'account_total_balance' => [
            'account' => 'ods_tencent_account_log.account_id',
        ],
        // adgroup
        'adgroup_ori_cost' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_register' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_first_day_roi' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_seventh_day_roi' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_fifteenth_day_roi' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_thirty_day_roi' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_total_roi' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_cost_per_reg' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_cost_per_first_day_pay' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_total_pay_money' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_create_time' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        'adgroup_create_time_relative' => [
            'account' => 'ods_tencent_campaign_log.account_id',
            'ad1' => 'ods_tencent_campaign_log.campaign_id',
        ],
        // ad
        'ad_ori_cost' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_register' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_first_day_roi' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_seventh_day_roi' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_fifteenth_day_roi' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_thirty_day_roi' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_total_roi' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_cost_per_reg' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_cost_per_first_day_pay' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_total_pay_money' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_create_time' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
        'ad_create_time_relative' => [
            'account' => 'ods_tencent_ad_log.account_id',
            'ad1' => 'ods_tencent_ad_log.campaign_id',
            'ad3' => 'ods_tencent_ad_log.ad_id',
        ],
    ];
}
