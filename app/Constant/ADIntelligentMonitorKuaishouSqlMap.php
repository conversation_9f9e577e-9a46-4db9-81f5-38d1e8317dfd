<?php

namespace App\Constant;

class ADIntelligentMonitorKuaishouSqlMap
{
    const KUAISHOU_FORMULA = [
        'account_balance' => 'ods_kuaishou_account_log.balance',
        'unit_id' => 'ifnull(ods_kuaishou_unit_log.ad_id,0)',
        'unit_ori_cost' => 'ifnull(ods_kuaishou_creative_hour_data_log.ori_cost,0)',
        'unit_register' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'unit_first_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'unit_seventh_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_seventh_day_pay_money,0))',
        'unit_fifteenth_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_fifteenth_day_pay_money,0))',
        'unit_thirty_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_thirty_day_pay_money,0))',
        'unit_total_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'unit_fist_day_pay_count' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'unit_create_time' => "ifnull(ods_kuaishou_unit_log.ad_create_time,'')",
        'creative_id' => 'ifnull(ods_kuaishou_creative_log.creative_id,0)',
        'creative_ori_cost' => 'ifnull(ods_kuaishou_creative_hour_data_log.ori_cost,0)',
        'creative_register' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'creative_first_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'creative_seventh_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_seventh_day_pay_money,0))',
        'creative_fifteenth_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_fifteenth_day_pay_money,0))',
        'creative_thirty_day_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_thirty_day_pay_money,0))',
        'creative_total_pay_money' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'creative_fist_day_pay_count' => 'sum(ifnull(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'creative_create_time' => "ifnull(ods_kuaishou_creative_log.creative_create_time,'')",
    ];

    const KUAISHOU_FORMULA_ALIAS = [
        'account_balance' => self::KUAISHOU_FORMULA['account_balance'] . ' as account_balance',
        'unit_id' => self::KUAISHOU_FORMULA['unit_id'] . ' as ad_id',
        'unit_ori_cost' => self::KUAISHOU_FORMULA['unit_ori_cost'] . ' as unit_ori_cost',
        'unit_register' => self::KUAISHOU_FORMULA['unit_register'] . ' as unit_register',
        'unit_first_day_pay_money' => self::KUAISHOU_FORMULA['unit_first_day_pay_money'] . ' as unit_first_day_pay_money',
        'unit_seventh_day_pay_money' => self::KUAISHOU_FORMULA['unit_seventh_day_pay_money'] . ' as unit_seventh_day_pay_money',
        'unit_fifteenth_day_pay_money' => self::KUAISHOU_FORMULA['unit_fifteenth_day_pay_money'] . ' as unit_fifteenth_day_pay_money',
        'unit_thirty_day_pay_money' => self::KUAISHOU_FORMULA['unit_thirty_day_pay_money'] . ' as unit_thirty_day_pay_money',
        'unit_total_pay_money' => self::KUAISHOU_FORMULA['unit_total_pay_money'] . ' as unit_total_pay_money',
        'unit_fist_day_pay_count' => self::KUAISHOU_FORMULA['unit_fist_day_pay_count'] . ' as unit_fist_day_pay_count',
        'unit_create_time' => self::KUAISHOU_FORMULA['unit_create_time'] . ' as unit_create_time',
        'unit_create_time_relative' => self::KUAISHOU_FORMULA['unit_create_time'] . ' as unit_create_time_relative',
        'creative_id' => self::KUAISHOU_FORMULA['creative_id'] . ' as creative_id',
        'creative_ori_cost' => self::KUAISHOU_FORMULA['creative_ori_cost'] . ' as creative_ori_cost',
        'creative_register' => self::KUAISHOU_FORMULA['creative_register'] . ' as creative_register',
        'creative_first_day_pay_money' => self::KUAISHOU_FORMULA['creative_first_day_pay_money'] . ' as creative_first_day_pay_money',
        'creative_seventh_day_pay_money' => self::KUAISHOU_FORMULA['creative_seventh_day_pay_money'] . ' as creative_seventh_day_pay_money',
        'creative_fifteenth_day_pay_money' => self::KUAISHOU_FORMULA['creative_fifteenth_day_pay_money'] . ' as creative_fifteenth_day_pay_money',
        'creative_thirty_day_pay_money' => self::KUAISHOU_FORMULA['creative_thirty_day_pay_money'] . ' as creative_thirty_day_pay_money',
        'creative_total_pay_money' => self::KUAISHOU_FORMULA['creative_total_pay_money'] . ' as creative_total_pay_money',
        'creative_fist_day_pay_count' => self::KUAISHOU_FORMULA['creative_fist_day_pay_count'] . ' as creative_fist_day_pay_count',
        'creative_create_time' => self::KUAISHOU_FORMULA['creative_create_time'] . ' as creative_create_time',
        'creative_create_time_relative' => self::KUAISHOU_FORMULA['creative_create_time'] . ' as creative_create_time_relative',
    ];

    const KUAISHOU_TARGET = [
        // account
        'account_balance' => [
            'ods_kuaishou_account_log' => [
                self::KUAISHOU_FORMULA_ALIAS['account_balance']
            ]
        ],
        // unit
        'unit_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ') AS unit_ori_cost'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'ods_kuaishou_creative_hour_data_log' => []
        ],
        'unit_register' => [
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_register'],
            ]
        ],
        'unit_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_first_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS unit_first_day_roi'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_first_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ]
        ],
        'unit_seventh_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_seventh_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS unit_seventh_day_roi'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_seventh_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ]
        ],
        'unit_fifteenth_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_fifteenth_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS unit_fifteenth_day_roi'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_fifteenth_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ]
        ],
        'unit_thirty_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_thirty_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS unit_thirty_day_roi'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_thirty_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ]
        ],
        'unit_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_total_pay_money'] . '/' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS unit_total_roi'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_total_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ]
        ],
        'unit_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ' /' . self::KUAISHOU_FORMULA['unit_register'] . ') AS DECIMAL(12, 4)), 0) as unit_cost_per_reg'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_register'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ],
        ],
        'unit_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['unit_ori_cost'] . ' /' . self::KUAISHOU_FORMULA['unit_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS unit_cost_per_first_day_pay'
            ],
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_fist_day_pay_count'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['unit_ori_cost'].' AS unit_ori_cost',
            ],
        ],
        'unit_total_pay_money' => [
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_total_pay_money'],
            ]
        ],
        'unit_create_time' => [
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
                self::KUAISHOU_FORMULA_ALIAS['unit_create_time']
            ]
        ],
        'unit_create_time_relative' => [
            'ods_kuaishou_unit_log' => [
                self::KUAISHOU_FORMULA_ALIAS['unit_id'],
                self::KUAISHOU_FORMULA_ALIAS['unit_create_time_relative']
            ]
        ],
        // creative
        'creative_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ') AS creative_ori_cost'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'ods_kuaishou_creative_hour_data_log' => []
        ],
        'creative_register' => [
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_register'],
            ]
        ],
        'creative_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_first_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_first_day_roi'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_first_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ]
        ],
        'creative_seventh_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_seventh_day_pay_money'] . '/' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_seventh_day_roi'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_seventh_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ]
        ],
        'creative_fifteenth_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_fifteenth_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_fifteenth_day_roi'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_fifteenth_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ]
        ],
        'creative_thirty_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_thirty_day_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_thirty_day_roi'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_thirty_day_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ]
        ],
        'creative_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_total_pay_money'] . '/ ' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_total_roi'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_total_pay_money'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ]
        ],
        'creative_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_ori_cost'] . ' /' . self::KUAISHOU_FORMULA['creative_register'] . ') AS DECIMAL(12, 4)), 0) as creative_cost_per_reg'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_register'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ],
        ],
        'creative_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::KUAISHOU_FORMULA['creative_ori_cost'] . '/' . self::KUAISHOU_FORMULA['creative_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS creative_cost_per_first_day_pay'
            ],
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_fist_day_pay_count'],
            ],
            'group-by-ods_kuaishou_creative_hour_data_log' => [
                self::KUAISHOU_FORMULA['creative_ori_cost'].' AS creative_ori_cost',
            ],
        ],
        'creative_total_pay_money' => [
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_total_pay_money'],
            ]
        ],
        'creative_create_time' => [
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
                self::KUAISHOU_FORMULA_ALIAS['creative_create_time']
            ]
        ],
        'creative_create_time_relative' => [
            'ods_kuaishou_creative_log' => [
                self::KUAISHOU_FORMULA_ALIAS['creative_id'],
                self::KUAISHOU_FORMULA_ALIAS['creative_create_time_relative']
            ]
        ],
    ];

    const KUAISHOU_JOIN = [
        // unit
        'unit_ori_cost' => [
            'ods_kuaishou_unit_log' . '-' . 'ods_kuaishou_creative_hour_data_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'ods_kuaishou_creative_hour_data_log.ad_id'],
                    ['ods_kuaishou_unit_log.account_id', 'ods_kuaishou_creative_hour_data_log.account_id'],
                ],
                'where' => [
                    ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                ]
            ]
        ],
        'unit_register' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ]
        ],
        'unit_first_day_roi' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_seventh_day_roi' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_fifteenth_day_roi' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_thirty_day_roi' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_total_roi' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_cost_per_reg' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_cost_per_first_day_pay' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [],
            ]
        ],
        'unit_total_pay_money' => [
            'ods_kuaishou_unit_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_unit_log.ad_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ]
        ],
        // creative
        'creative_ori_cost' => [
            'ods_kuaishou_creative_log' . '-' . 'ods_kuaishou_creative_hour_data_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'ods_kuaishou_creative_hour_data_log.creative_id'],
                    ['ods_kuaishou_creative_log.account_id', 'ods_kuaishou_creative_hour_data_log.account_id'],
                ],
                'where' => [
                    ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                ]
            ]
        ],
        'creative_register' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ]
        ],
        'creative_first_day_roi' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_seventh_day_roi' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_fifteenth_day_roi' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_thirty_day_roi' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_total_roi' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_cost_per_reg' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_cost_per_first_day_pay' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ],
            'tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_kuaishou_creative_hour_data_log' => [
                'group-by' => [
                    'ods_kuaishou_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_kuaishou_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_kuaishou_creative_hour_data_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [],
            ]
        ],
        'creative_total_pay_money' => [
            'ods_kuaishou_creative_log' . '-' . 'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'on' => [
                    ['ods_kuaishou_creative_log.creative_id', 'tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
                'where' => [
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                    ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::KUAISHOU]
                ]
            ]
        ],
    ];

    const KUAISHOU_GROUP = [
        'account_balance' => 'ods_kuaishou_account_log.account_id',
        'unit_ori_cost' => 'ods_kuaishou_unit_log.ad_id',
        'unit_register' => 'ods_kuaishou_unit_log.ad_id',
        'unit_first_day_roi' => 'ods_kuaishou_unit_log.ad_id',
        'unit_seventh_day_roi' => 'ods_kuaishou_unit_log.ad_id',
        'unit_fifteenth_day_roi' => 'ods_kuaishou_unit_log.ad_id',
        'unit_thirty_day_roi' => 'ods_kuaishou_unit_log.ad_id',
        'unit_total_roi' => 'ods_kuaishou_unit_log.ad_id',
        'unit_cost_per_reg' => 'ods_kuaishou_unit_log.ad_id',
        'unit_cost_per_first_day_pay' => 'ods_kuaishou_unit_log.ad_id',
        'unit_total_pay_money' => 'ods_kuaishou_unit_log.ad_id',
        'unit_create_time' => 'ods_kuaishou_unit_log.ad_id',
        'unit_create_time_relative' => 'ods_kuaishou_unit_log.ad_id',
        'creative_ori_cost' => 'ods_kuaishou_creative_log.creative_id',
        'creative_register' => 'ods_kuaishou_creative_log.creative_id',
        'creative_first_day_roi' => 'ods_kuaishou_creative_log.creative_id',
        'creative_seventh_day_roi' => 'ods_kuaishou_creative_log.creative_id',
        'creative_fifteenth_day_roi' => 'ods_kuaishou_creative_log.creative_id',
        'creative_thirty_day_roi' => 'ods_kuaishou_creative_log.creative_id',
        'creative_total_roi' => 'ods_kuaishou_creative_log.creative_id',
        'creative_cost_per_reg' => 'ods_kuaishou_creative_log.creative_id',
        'creative_cost_per_first_day_pay' => 'ods_kuaishou_creative_log.creative_id',
        'creative_total_pay_money' => 'ods_kuaishou_creative_log.creative_id',
        'creative_create_time' => 'ods_kuaishou_creative_log.creative_id',
        'creative_create_time_relative' => 'ods_kuaishou_creative_log.creative_id',
    ];

    const KUAISHOU_WHERE = [
        'account_balance' => [
            'account' => 'ods_kuaishou_account_log.account_id',
        ],
        // unit
        'unit_ori_cost' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_register' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_first_day_roi' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_seventh_day_roi' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_fifteenth_day_roi' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_thirty_day_roi' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_total_roi' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_cost_per_reg' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_cost_per_first_day_pay' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_total_pay_money' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_create_time' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        'unit_create_time_relative' => [
            'account' => 'ods_kuaishou_unit_log.account_id',
            'ad2' => 'ods_kuaishou_unit_log.ad_id',
        ],
        // creative
        'creative_ori_cost' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_register' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_first_day_roi' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_seventh_day_roi' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_fifteenth_day_roi' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_thirty_day_roi' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_total_roi' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_cost_per_reg' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_cost_per_first_day_pay' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_total_pay_money' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_create_time' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
        'creative_create_time_relative' => [
            'account' => 'ods_kuaishou_creative_log.account_id',
            'ad2' => 'ods_kuaishou_creative_log.ad_id',
            'ad3' => 'ods_kuaishou_creative_log.creative_id',
        ],
    ];
}
