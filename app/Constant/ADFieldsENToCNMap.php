<?php

namespace App\Constant;

/**
 * 广告报表数据字段英文-中文转换
 * Class ADFieldsENToCNMap
 * @package App\Constant
 */
class ADFieldsENToCNMap
{
    const EN_TO_CN = [
        'GDT_IS_PLAYABLE-0' => '否',
        'GDT_IS_PLAYABLE-1' => '是',
        //头条2.0一级广告状态
        'PROJECT_STATUS_ENABLE' => '启用',
        'PROJECT_STATUS_DISABLE' => '暂停',
        'PROJECT_STATUS_DELETE' => '删除',
        'PROJECT_STATUS_ALL' => '所有包含已删除',
        'PROJECT_STATUS_NOT_DELETE' => '所有不包含已删除',
        'PROJECT_STATUS_BUDGET_EXCEED' => '项目超出预算',
        'PROJECT_STATUS_BUDGET_PRE_OFFLINE_BUDGET' => '项目接近预算',
        'PROJECT_STATUS_NOT_START' => '未达投放时间',
        'PROJECT_STATUS_DONE' => '已完成',
        'PROJECT_STATUS_NO_SCHEDULE' => '不在投放时段',
        'TT_THREE_DAY_COST_FIVE_STANDARD-1' => '是',
        'TT_THREE_DAY_COST_FIVE_STANDARD-0' => '否',
        'IS_MIX_OS-0' => '否',
        'IS_MIX_OS-1' => '是',
        'DISABLERAISE' => '不能开启起量',
        'ENABLERAISE' => '可以开启起量',
        'RAISING' => '起量中',
        'PORT_VERSION-'=> '默认',
        'PORT_VERSION-2.0' => '头条2.0/腾讯ADA',
        'PORT_VERSION-99' => '头条管家',
        'PORT_VERSION-3.0' => '3.0',
        'CREATE_TYPE-0' => '未知',
        'CREATE_TYPE-1' => '集团后台新建',
        'CREATE_TYPE-2' => '非集团后台新建',
        'CREATE_TYPE-3' => '智能投放',
//        'media_type-' . MediaType::TOUTIAO => MediaType::MEDIA_TYPE_MAP[MediaType::TOUTIAO],
//        'media_type-' . MediaType::TENCENT => MediaType::MEDIA_TYPE_MAP[MediaType::TENCENT],
//        'media_type-' . MediaType::MP => MediaType::MEDIA_TYPE_MAP[MediaType::MP],
//        'media_type-' . MediaType::KUAISHOU => MediaType::MEDIA_TYPE_MAP[MediaType::KUAISHOU],
        'SMART_BID_CUSTOM' => '手动出价',
        'SMART_BID_CONSERVATIVE' => '自动出价',
        'SMART_BID_NO_BID' => 'Nobid出价',
        'EXTERNAL_URL' => '落地页下载链接',
        'DOWNLOAD_URL' => '应用下载链接',
        'REGION' => '地域',
        'GENDER' => '性别',
        'AGE' => '年龄',
        'CUSTOM_AUDIENCE' => '自定人群-定向',
        'INTEREST_ACTION' => '行为兴趣',
        'GENDER_MALE' => '男',
        'GENDER_FEMALE' => '女',
        'NONE' => '不限',
        'AD_STATUS_NORMAL' => '有效',
//        'AD_STATUS_DELIVERY_OK' => '投放中',
        'AD_STATUS_DELIVERY_OK' => '有效',
        'AD_STATUS_DISABLE' => '计划暂停',
        'AD_STATUS_AUDIT' => '新建审核中',
        'AD_STATUS_REAUDIT' => '修改审核中',
        'AD_STATUS_DONE' => '已完成（投放达到结束时间）',
        'AD_STATUS_CREATE' => '计划新建',
        'AD_STATUS_AUDIT_DENY' => '审核不通过',
        'AD_STATUS_BALANCE_EXCEED' => '账户余额不足',
        'AD_STATUS_BUDGET_EXCEED' => '超出预算',
        'AD_STATUS_NOT_START' => '未到达投放时间',
        'AD_STATUS_NO_SCHEDULE' => '不在投放时段',
        'AD_STATUS_CAMPAIGN_DISABLE' => '已被一级广告暂停',
        'AD_STATUS_CAMPAIGN_EXCEED' => '广告组超出预算',
        'AD_STATUS_DELETE' => '已删除',
        'AD_STATUS_FROZEN' => '已冻结',
        'AD_STATUS_ALL' => '所有包含已删除',
        'AD_STATUS_NOT_DELETE' => '所有不包含已删除',
        'AD_STATUS_ADVERTISER_BUDGET_EXCEED' => '超出账户日预算',
        'AD_STATUS_FINISHED' => '已结束',
        /**
         * 腾讯3.0 二级
         */
        "ADGROUP_STATUS_FROZEN" => "已冻结",
        "ADGROUP_STATUS_SUSPEND" => "暂停中",
        "ADGROUP_STATUS_NOT_IN_DELIVERY_TIME" => "广告未到投放时间",
        "ADGROUP_STATUS_ACTIVE" => "投放中",
        "ADGROUP_STATUS_DELETED" => "已删除",
        "ADGROUP_STATUS_ACCOUNT_BALANCE_NOT_ENOUGH" => "账户余额不足",
        "ADGROUP_STATUS_DAILY_BUDGET_REACHED" => "广告达到日预算上限",
        /**
         * 腾讯3.0 三级
         */
        "DYNAMIC_CREATIVE_STATUS_PENDING" => "审核中",
        "DYNAMIC_CREATIVE_STATUS_DENIED" => "审核不通过",
        "DYNAMIC_CREATIVE_STATUS_ACTIVE" => "投放中",
        "DYNAMIC_CREATIVE_STATUS_SUSPEND" => "暂停",
        "DYNAMIC_CREATIVE_STATUS_PREPARE_FAILED" => "创意准备失败",
        "DYNAMIC_CREATIVE_STATUS_DELETED" => "已删除",
        "DYNAMIC_CREATIVE_STATUS_CREATING" => "创意准备中",
        /*头条新加*/
        'AD_STATUS_LIVE_CANNOT_LANUCH' => '关联直播间不可投放',
        'AD_STATUS_CAMPAIGN_PRE_OFFLINE_BUDGET' => '广告组接近预算',
        'AD_STATUS_PRE_OFFLINE_BUDGET' => '广告接近预算',
        'AD_STATUS_PRE_ONLINE' => '预上线',
        'AD_STATUS_DSP_AD_DISABLE' => '已被广告计划暂停',
        'AD_STATUS_AUDIT_STATUS_ERROR' => '异常，请联系审核人员',
        'AD_STATUS_DRAFT' => '草稿',
        'AD_STATUS_ADVERTISER_PRE_OFFLINE_BUDGET' => '账户接近预算',
        'AD_STATUS_CANNOT_EDIT' => '不可编辑',
        'AD_STATUS_EXTERNAL_URL_DISABLE' => '落地页暂不可用',
        'AD_STATUS_LIVE_ROOM_OFF' => '关联直播间未开播',
        'AD_STATUS_AWEME_ACCOUNT_PUNISHED' => '关联抖音账号封禁不可投放',
        'AD_STATUS_AWEME_ACCOUNT_DISABLED' => '关联抖音号不可投',
        'AD_STATUS_PRODUCT_OFFLINE' => '关联商品不可投',
        'AD_STATUS_LIVE_CANNOT_LAUNCH' => '直播无法发起',
        /*头条新加*/
        'STATUS_DISABLE' => '已禁用',
        'STATUS_PENDING_CONFIRM' => '审核中',
        'STATUS_PENDING_VERIFIED' => '待验证',
        'STATUS_CONFIRM_FAIL' => '审核失败/可再次申请',
        'STATUS_ENABLE' => '已审核',
        'STATUS_CONFIRM_FAIL_END' => '审核失败/最终状态',
        'STATUS_PENDING_CONFIRM_MODIFY' => '修改审核中',
        'STATUS_CONFIRM_MODIFY_FAIL' => '修改审核失败',
        'STATUS_PUNISH' => '惩罚',
        'STATUS_WAIT_FOR_BPM_AUDIT' => '等待CRM审核',
        'STATUS_SELF_SERVICE_UNAUDITED' => '待验证资质',
        'STATUS_ENABLE_AND_AVATAR_AUDITING' => 'SMB客户待合同归档',
        'STATUS_WAIT_FOR_BPM_FILE_CONTACT' => 'SMB客户待合同归档',
        'STATUS_WAIT_FOR_ACCOUNT_FEE' => 'SMB广告主待缴纳开户费',
        'STATUS_WAIT_FOR_PUBLIC_AUTH' => '待对公验证',
        'CAMPAIGN_STATUS_ENABLE' => '启用',
        'CAMPAIGN_STATUS_IS_DELETED' => '已删除',
        'CAMPAIGN_STATUS_IS_NOT_DELETED' => '未删除',
        'CAMPAIGN_STATUS_DISABLE' => '暂停',
        'CAMPAIGN_STATUS_DELETE' => '删除',
        'CAMPAIGN_STATUS_ALL' => '所有包含已删除',
        'CAMPAIGN_STATUS_NOT_DELETE' => '所有不包含已删除',
        'CAMPAIGN_STATUS_ADVERTISER_BUDGET_EXCEED' => '超出广告主日预算',
        'AD_STATUS_ENABLE' => '计划启用',
        'CREATIVE_STATUS_DELIVERY_OK' => '投放中',
        'CREATIVE_STATUS_NOT_START' => '未到达投放时间',
        'CREATIVE_STATUS_NO_SCHEDULE' => '不在投放时段',
        'CREATIVE_STATUS_DISABLE' => '创意暂停',
        'CREATIVE_STATUS_CAMPAIGN_DISABLE' => '已被广告组暂停',
        'CREATIVE_STATUS_CAMPAIGN_EXCEED' => '广告组超出预算',
        'CREATIVE_STATUS_AUDIT' => '新建审核中',
        'CREATIVE_STATUS_REAUDIT' => '修改审核中',
        'CREATIVE_STATUS_DELETE' => '创意已删除',
        'CREATIVE_STATUS_DONE' => '已完成（投放达到结束时间）',
        'CREATIVE_STATUS_AD_DISABLE' => '广告计划暂停',
        'CREATIVE_STATUS_AUDIT_DENY' => '审核不通过',
        'CREATIVE_STATUS_BALANCE_EXCEED' => '账户余额不足',
        'CREATIVE_STATUS_BUDGET_EXCEED' => '超出预算',
        'CREATIVE_STATUS_DATA_ERROR' => '数据错误',
        'CREATIVE_STATUS_PRE_ONLINE' => '预上线',
        'CREATIVE_STATUS_AD_AUDIT' => '广告计划新建审核中',
        'CREATIVE_STATUS_AD_REAUDIT' => '广告计划修改审核中',
        'CREATIVE_STATUS_AD_AUDIT_DENY' => '广告计划审核不通过',
        'CREATIVE_STATUS_ALL' => '所有包含已删除',
        'CREATIVE_STATUS_NOT_DELETE' => '所有不包含已删除',
        'CREATIVE_STATUS_ADVERTISER_BUDGET_EXCEED' => '超出账户日预算',
        'CREATIVE_STATUS_ENABLE' => '创意启用',
        'DEFAULT' => '默认，不在学习期中',
        'LEARN_FAILED' => '学习失败',
        'LEARNING' => '学习中',
        'LEARNED' => '学习成功',
        'SCHEDULE_FROM_NOW' => '从现在开始一直投放',
        'SCHEDULE_START_END	' => '选择起始时间',
//        'FLOW_CONTROL_MODE_FAST' => '优先跑量（对应CPC的加速投放）',
        'FLOW_CONTROL_MODE_FAST' => '优先跑量',
//        'FLOW_CONTROL_MODE_SMOOTH' => '优先低成本（对应CPC的标准投放）',
        'FLOW_CONTROL_MODE_SMOOTH' => '优先低成本',
        'FLOW_CONTROL_MODE_BALANCE' => '均衡投放',
        'ORIGINAL_VIDEO' => '原生视频',
        'REWARDED_VIDEO' => '激励视频',
        'SPLASH_VIDEO' => '穿山甲开屏',
        'STATIC_ASSEMBLE' => '程序化创意',
        //计费类型
        'PRICING_CPC' => 'CPC',
        'PRICING_CPM' => 'CPM',
        'PRICING_OCPC' => 'OCPC',
        'PRICING_OCPM' => 'OCPM',
        'PRICING_CPV' => 'CPV',
        'PRICING_CPA' => 'CPA',
        //预算类型
        'BUDGET_MODE_INFINITE' => '不限',
        'BUDGET_MODE_DAY' => '日预算',
        'BUDGET_MODE_TOTAL' => '总预算',
        //深度优化方式
        'DEEP_BID_DEFAULT' => '不启用，无深度优化',
        'DEEP_BID_PACING' => '自动优化(手动出价方式下)',
        'DEEP_BID_MIN' => '自定义双出价(手动出价方式下)',
        'ROI_COEFFICIENT' => 'ROI系数',
        'ROI_PACING' => 'ROI系数-自动优化',
        'MIN_SECOND_STAGE' => '两阶段优化',
        'PACING_SECOND_STAGE' => '动态两阶段',
        'SMARTBID' => '自动优化(自动出价方式下)',
        'AUTO_MIN_SECOND_STAGE' => '自定义双出价(自动出价方式下)',
        'BID_PER_ACTION' => '每次付费出价',
        'FIRST_AND_SEVEN_PAY_ROI' => '首日ROI+7日ROI',
        'PER_AND_SEVEN_PAY_ROI' =>  '每次付费+7日ROI',
        //开启智能放量
        'TT_AUTO_EXTEND_ENABLES-0' => '否',
        'TT_AUTO_EXTEND_ENABLES-1' => '是',
        /**
         * 腾讯
         */
        //腾讯二级广告客户设置状态
        'AD_STATUS_SUSPEND' => '暂停',
        //腾讯二级广告系统状态
        'AD_GROUP_STATUS_NORMAL' => '有效',
        'AD_GROUP_STATUS_PENDING' => '待审核',
        'CREATIVE_STATUS_PENDING' => '待审核',
        'AD_STATUS_PENDING' => '待审核',
        "AD_STATUS_DENIED" => "审核不通过",
        "AD_STATUS_INVALID" => "异常（当投放视频广告时，该状态代表视频转码失败）",
        'AD_GROUP_STATUS_DENIED' => '审核不通过',
        'CREATIVE_STATUS_DENY' => '审核不通过',
        'AD_GROUP_STATUS_FROZEN' => '封停',
        'CREATIVE_STATUS_FROZEN' => '封停',
        'AD_GROUP_STATUS_PARTIALLY_PENDING' => '部分审核中',
        'CREATIVE_STATUS_PARTIALLY_PENDING' => '部分审核中',
        'AD_STATUS_PARTIALLY_PENDING' => '部分审核中',
        'AD_GROUP_STATUS_PARTIALLY_NORMAL' => '部分有效',
        'CREATIVE_STATUS_PARTIALLY_NORMAL' => '部分有效',
        'AD_STATUS_PARTIALLY_NORMAL' => '部分有效',
        'AD_GROUP_STATUS_PREPARE' => '准备中（渠道包在审核中）',
        'AD_STATUS_PREPARE' => '准备中（渠道包在审核中）',
        'CREATIVE_STATUS_PREPARE' => '准备中（渠道包在审核中）',
        'AD_GROUP_STATUS_DELETED' => '已删除',
        'AD_STATUS_DELETED' => '已删除',
        'AD_GROUP_STATUS_INVALID' => '失效（渠道包审核不通过，请更新渠道包）',
        //腾讯广告状态
        'STATUS_UNKNOWN' => '未知状态',
        'STATUS_PENDING' => '审核中',
        'STATUS_DENIED' => '审核不通过',
        'STATUS_FROZEN' => '冻结',
        'STATUS_SUSPEND' => '暂停中',
        'STATUS_READY' => '未到投放时间',
        'STATUS_ACTIVE' => '投放中',
        'STATUS_STOP' => '投放结束',
        'STATUS_PREPARE' => '准备中',
        'STATUS_DELETED' => '已删除',
        'STATUS_ACTIVE_ACCOUNT_FROZEN' => '广告被暂停（账户资金被冻结）',
        'STATUS_ACTIVE_ACCOUNT_EMPTY' => '广告被暂停（账户余额不足）',
        'STATUS_ACTIVE_ACCOUNT_LIMIT' => '广告被暂停（账户达日限额）',
        'STATUS_ACTIVE_CAMPAIGN_LIMIT' => '广告被暂停（推广计划达日限额）',
        'STATUS_ACTIVE_CAMPAIGN_SUSPEND' => '广告被暂停（推广计划暂停）',
        'STATUS_ACTIVE_AD_LIMIT' => '广告被暂停（广告达日限额）',
        'STATUS_PART_READY' => '部分待投放',
        'STATUS_PART_ACTIVE' => '部分投放中',
        //oCPA深度优化方式-广点通
        'DEEP_OPTIMIZATION_ACTION_TYPE_DOUBLE_GOAL_BID' => '双目标出价',
        'DEEP_OPTIMIZATION_ACTION_TYPE_TWO_STAGE_BID' => '两阶段出价',
        //深度优化ROI目标
        'GOAL_7DAY_PURCHASE_ROAS' => '七日付费 ROI',
        'GOAL_30DAY_ORDER_ROAS' => '下单 ROI',
        'GOAL_1DAY_PURCHASE_ROAS' => '首日付费 ROI',
        'GOAL_1DAY_MONETIZATION_ROAS' => '首日变现 ROI',
        'GOAL_7DAY_MONETIZATION_ROAS' => '七日变现 ROI',
        //账户状态
        'CUSTOMER_STATUS_NORMAL' => '有效',
        'CUSTOMER_STATUS_PENDING' => '待审核',
        'CUSTOMER_STATUS_DENIED' => '审核不通过',
        'CUSTOMER_STATUS_FROZEN' => '封停',
        'CUSTOMER_STATUS_TOBE_ACCEPTED' => '待接受',
        'CUSTOMER_STATUS_TOBE_ACTIVATED' => '待激活',
        'CUSTOMER_STATUS_SUSPEND' => '暂停',
        'CUSTOMER_STATUS_MATERIAL_PREPARED' => '广告主资料准备',
        'CUSTOMER_STATUS_DELETED' => '删除',
        'CUSTOMER_STATUS_FROZEN_TEMPORARILY' => '临时冻结',
        'CUSTOMER_STATUS_UNREGISTERED' => '未注册',
        //推广计划类型
        'CAMPAIGN_TYPE_SEARCH' => '搜索广告，仅支持读',
        'CAMPAIGN_TYPE_NORMAL' => '普通展示广告，可投放除微信朋友圈外的所有流量',
        'CAMPAIGN_TYPE_WECHAT_OFFICIAL_ACCOUNTS' => '微信公众号广告，仅可投放微信非朋友圈流量（公众号、小程序等）的广告',
        'CAMPAIGN_TYPE_WECHAT_MOMENTS' => '微信朋友圈广告，仅可投放微信朋友圈流量的广告',
        //广告投放速度类型
        'BID_STRATEGY_AVERAGE_COST' => '稳定拿量',
        'BID_STRATEGY_MAX_COST' => '优先最大转化',
        'BID_STRATEGY_TARGET_COST' => '优先拿量',
        'BID_STRATEGY_PRIORITY_LOW_COST' => '优先低成本',
        'BID_STRATEGY_UNSUPPORTED' => '不支持的出价策略',
        //计费类型
        'BILLINGEVENT_CLICK' => '按点击扣费',
        'BILLINGEVENT_APP_DOWNLOAD' => '按照应用下载扣费',
        'BILLINGEVENT_IMPRESSION' => '按曝光扣费',
        'BILLINGEVENT_APP_INSTALL' => '按照应用下载扣费',
        //自动扩量
        'GDT_EXPAND_ENABLED-0' => '否',
        'GDT_EXPAND_ENABLED-1' => '是',
        //优化目标类型/优化转化行为目标
        'OPTIMIZATIONGOAL_NONE' => 'none',
        'OPTIMIZATIONGOAL_BRAND_CONVERSION' => '品牌转化',
        'OPTIMIZATIONGOAL_FOLLOW' => '关注',
        'OPTIMIZATIONGOAL_CLICK' => '点击',
        'OPTIMIZATIONGOAL_IMPRESSION' => '曝光',
        'OPTIMIZATIONGOAL_APP_DOWNLOAD' => '下载',
        'OPTIMIZATIONGOAL_APP_ACTIVATE' => '激活',
        'OPTIMIZATIONGOAL_APP_REGISTER' => 'App 注册',
        'OPTIMIZATIONGOAL_ONE_DAY_RETENTION' => '次日留存',
        'OPTIMIZATIONGOAL_APP_PURCHASE' => 'App 付费次数',
        'OPTIMIZATIONGOAL_ECOMMERCE_ORDER' => '下单',
        'OPTIMIZATIONGOAL_ECOMMERCE_CHECKOUT' => 'H5 付费次数',
        'OPTIMIZATIONGOAL_LEADS' => '表单预约',
        'OPTIMIZATIONGOAL_ECOMMERCE_CART' => '加入购物车',
        'OPTIMIZATIONGOAL_PROMOTION_CLICK_KEY_PAGE' => 'H5 注册',
        'OPTIMIZATIONGOAL_VIEW_COMMODITY_PAGE' => '商品详情页浏览',
        'OPTIMIZATIONGOAL_ONLINE_CONSULTATION' => '在线咨询',
        'OPTIMIZATIONGOAL_TELEPHONE_CONSULTATION' => '电话拨打',
        'OPTIMIZATIONGOAL_PAGE_RESERVATION' => '表单预约',
        'OPTIMIZATIONGOAL_DELIVERY' => '发货',
        'OPTIMIZATIONGOAL_MESSAGE_AFTER_FOLLOW' => '公众号内发消息',
        'OPTIMIZATIONGOAL_CLICK_MENU_AFTER_FOLLOW' => '公众号内点击菜单栏',
        'OPTIMIZATIONGOAL_PAGE_EFFECTIVE_ONLINE_CONSULT' => '有效在线咨询',
        'OPTIMIZATIONGOAL_PAGE_EFFECTIVE_PHONE_CALL' => '有效电话拨打',
        'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_CONSULT' => '有效在线咨询',
        'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_PHONE' => '有效电话拨打',
        'OPTIMIZATIONGOAL_LEADS_COLLECT' => '综合线索收集',
        'OPTIMIZATIONGOAL_FIRST_PURCHASE' => '首次付费',
        'OPTIMIZATIONGOAL_APPLY' => '进件',
        'OPTIMIZATIONGOAL_PRE_CREDIT' => '预授信',
        'OPTIMIZATIONGOAL_CREDIT' => '授信',
        'OPTIMIZATIONGOAL_WITHDRAW_DEPOSITS' => '提现',
        'OPTIMIZATIONGOAL_PROMOTION_VIEW_KEY_PAGE' => '关键页面访问',
        'OPTIMIZATIONGOAL_MOBILE_APP_CREATE_ROLE' => '小游戏创角',
        'OPTIMIZATIONGOAL_CANVAS_CLICK' => '跳转按钮点击',
        'OPTIMIZATIONGOAL_PROMOTION_CLAIM_OFFER' => '领券',
        'OPTIMIZATIONGOAL_ECOMMERCE_ADD_TO_WISHLIST' => '商品收藏',
        'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_RESERVATION' => '有效表单预约',
        'OPTIMIZATIONGOAL_PAGE_RECEIPT' => '签收',
        'OPTIMIZATIONGOAL_PAGE_SCAN_CODE' => '加企业微信客服',
        'OPTIMIZATIONGOAL_SELECT_COURSE' => '选课',
        'OPTIMIZATIONGOAL_CONFIRM_POTENTIAL_CUSTOMER_PHONE' => '电话潜在客户',
        'OPTIMIZATIONGOAL_MOBILE_APP_AD_INCOME' => '广告变现',
        'OPTIMIZATIONGOAL_MOBILE_APP_ACCREDIT' => '小游戏授权',
        'OPTIMIZATIONGOAL_PURCHASE_MEMBER_CARD' => '首次会员购买',
        'OPTIMIZATIONGOAL_PAGE_CONFIRM_EFFECTIVE_LEADS' => '有效综合线索',
        //oCPA 深度优化价值配置
        'DEEP_CONVERSION_WORTH' => '优化ROI',
        'DEEP_CONVERSION_BEHAVIOR' => '优化转化行为',
        'DEEP_CONVERSION_WORTH_ADVANCED' => '强化ROI',
        //是否开启自动扩量
        'IS_EXPAND_TARGETING-0' => '否',
        'IS_EXPAND_TARGETING-1' => '是',
        //推广目标类型
        'PROMOTED_OBJECT_TYPE_APP_ANDROID' => 'Android 应用',
        'PROMOTED_OBJECT_TYPE_APP_IOS' => 'IOS 应用',
        'PROMOTED_OBJECT_TYPE_ECOMMERCE' => '电商推广',
        'PROMOTED_OBJECT_TYPE_LINK_WECHAT' => '微信品牌页',
        'PROMOTED_OBJECT_TYPE_APP_ANDROID_MYAPP' => '应用宝推广',
        'PROMOTED_OBJECT_TYPE_APP_ANDROID_UNION' => 'Android 应用推广（广告包）',
        'PROMOTED_OBJECT_TYPE_LOCAL_ADS_WECHAT' => '本地广告（微信推广）',
        'PROMOTED_OBJECT_TYPE_QQ_BROWSER_MINI_PROGRAM' => 'QQ 浏览器小程序',
        'PROMOTED_OBJECT_TYPE_LINK' => '网页',
        'PROMOTED_OBJECT_TYPE_QQ_MESSAGE' => 'QQ 消息',
        'PROMOTED_OBJECT_TYPE_QZONE_VIDEO_PAGE' => '认证空间-视频说说，仅可读',
        'PROMOTED_OBJECT_TYPE_LOCAL_ADS' => '本地广告，仅可读',
        'PROMOTED_OBJECT_TYPE_ARTICLE' => '好文广告，仅可读',
        'PROMOTED_OBJECT_TYPE_LEAD_AD' => '线索收集',
        'PROMOTED_OBJECT_TYPE_TENCENT_KE' => '腾讯课堂，仅可读',
        "PROMOTED_OBJECT_TYPE_EXCHANGE_APP_ANDROID_MYAPP" => '换量应用，仅可读',
        'PROMOTED_OBJECT_TYPE_QZONE_PAGE_ARTICLE' => 'QQ 空间日志页，仅可读',
        'PROMOTED_OBJECT_TYPE_QZONE_PAGE_IFRAMED' => 'QQ 空间嵌入页，仅可读',
        'PROMOTED_OBJECT_TYPE_QZONE_PAGE' => 'QQ 空间首页，仅可读',
        'PROMOTED_OBJECT_TYPE_APP_PC' => 'PC 应用，仅可读',
        'PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT' => '微信小游戏',
        'PROMOTED_OBJECT_TYPE_MINI_GAME_QQ' => 'QQ 小游戏',
        //成本保障状态
        'COST_GUARANTEE_STATUS_NONE' => '无成本保障状态',
        'COST_GUARANTEE_STATUS_EFFECTIVE' => '成本保障生效中',
        'COST_GUARANTEE_STATUS_FAILED' => '成本保障已失效',
        'COST_GUARANTEE_STATUS_FINISHED' => '成本保障已结束',
        'COST_GUARANTEE_STATUS_CONFIRMING' => '成本保障确认中',
        'COST_GUARANTEE_STATUS_SUCCEEDED' => '超成本赔付已完成',
        //地点类型
        'RECENTLY_IN' => '近期',
        'VISITED_IN' => '去过',
        'LIVE_IN' => '常住',
        'TRAVEL_IN' => '旅行',
        //消费类型
        'PAID_GOODS_VIRTUAL' => '虚拟商品',
        'PAID_GOODS_REAL' => '实物商品',
        //付费用户
        'APP_PAID' => 'APP 付费用户',
        'ECOMMERCE_PAID' => '已有电商付费用户',
        'LATENT_VIR_PAY' => '潜在虚拟付费用户，仅可读',
        //移动联盟场景定向
        'MOBILE_UNION_IN_WECHAT' => '优量汇第三方流量在微信内打开的场景',
        'MOBILE_UNION_FEEDS' => '信息流',
        'MOBILE_UNION_REWARDED_VIDEO' => '激励视频',
        'MOBILE_UNION_QA_COMMUNITY' => '问答社区',
        'MOBILE_UNION_SHORT_VIDEO' => '短视频',
        'MOBILE_UNION_REWARDED_MEDIA' => '激励媒体',
        'MOBILE_UNION_SMB_CUSTOMIZATION' => '中小广告主定制',
        'MOBILE_UNION_GAME_ADVERTISER_PECULIAR' => '轻度游戏广告主定制',
        'MOBILE_UNION_READ_ADVERTISER' => '阅读行业广告主定制',
        //广告展示场景
        'DISPLAY_SCENE_BANNER' => 'Banner',
        'DISPLAY_SCENE_INLINE' => '插屏',
        'DISPLAY_SCENE_SPLASH' => '开屏',
        'DISPLAY_SCENE_NATIVE' => '原生',
        'DISPLAY_SCENE_REWARDED_VIDEO' => '激励视频',
        //是否是激励视频广告
        //是否是激励视频广告
        'IS_REWARDED_VIDEO_AD-0' => '否',
        'IS_REWARDED_VIDEO_AD-1' => '是',
        //是否开启自动版位
        'AUTOMATIC_SITE_ENABLED-0' => '否',
        'AUTOMATIC_SITE_ENABLED-1' => '是',
        //app 行为对象的类型
        'APP_CLASS' => '类目',
        //app 行为列表
        'ACTIVE' => '活跃',
        'PAID' => '付费',
        //落地页类型
        'PAGE_TYPE_DEFAULT' => '默认落地页类型',
        'PAGE_TYPE_TSA_APP' => '通过 TSA 落地页制作工具生成的自定义 APP 介绍页',
        'PAGE_TYPE_TSA_WEB_NONE_ECOMMERCE' => '通过 TSA 落地页制作工具生成的非电商类网页',
        'PAGE_TYPE_FENGYE_ECOMMERCE' => '通过枫叶落地页制作工具生成的电商类网页',
        'PAGE_TYPE_CANVAS' => 'Canvas 原生推广页',
        'PAGE_TYPE_MINI_PROGRAM' => '小程序落地页',
        'PAGE_TYPE_CANVAS_WECHAT' => '微信原生推广页，通过微信创建的落地页类型',
        'PAGE_TYPE_MINI_PROGRAM_WECHAT' => '微信小程序落地页',
        'PAGE_TYPE_UNSUPPORTED' => '尚不支持的落地页类型',
        'PAGE_TYPE_MINI_GAME_WECHAT' => '微信小游戏落地页',
        'PAGE_TYPE_MINI_PROGRAM_QQ' => 'QQ 小程序落地页',
        'PAGE_TYPE_MINI_GAME_QQ' => 'QQ 小程序落地页',
        'PAGE_TYPE_MINI_PROGRAM_CANVAS_WECHAT' => '微信小程序简版原生页',
        'PAGE_TYPE_MOMENTS_SIMPLE_NATIVE_WECHAT' => '微信简版原生页',
        'PAGE_TYPE_FULL_SCREEN_WECHAT' => '微信素材放大(待废弃,仅支持微信小游戏下使用)',
        'PAGE_TYPE_YUEBAO_QUICKAPP' => '阅宝快应用',
        'PAGE_TYPE_YUEBAO_OFFICIAL_ACCOUNT_ARTICLE' => '阅宝公众号(H5 页, 点击唤起应用)',
        //腾讯一级投放速度模式
        'SPEED_MODE_FAST' => '加速投放，广告会以较快的速度获得曝光，选择加速投放可能会导致您的预算较快地耗尽',
        'SPEED_MODE_STANDARD' => '标准投放，系统会优化您的广告的投放，让您的预算在设定的投放时段内较为平稳地消耗，默认为标准投放',
        /**
         * 快手
         */
        'FLOW_CONTROL_MODE_UNKNOWN' => '未知',
        'FLOW_CONTROL_MODE_PRIORITY_LOW_COST' => '低优先成本',
        'KUAISHOU_FLOW_CONTROL_MODE-0' => '未知',
        'KUAISHOU_FLOW_CONTROL_MODE-1' => '正常投放',
        'KUAISHOU_FLOW_CONTROL_MODE-2' => '平滑投放',
        'KUAISHOU_FLOW_CONTROL_MODE-3' => '低优先成本',

        //二级状态
        'KUAISHOU_UNIT_STATUS-1' => '计划已暂停',
        'KUAISHOU_UNIT_STATUS-3' => '计划超预算',
        'KUAISHOU_UNIT_STATUS-6' => '余额不足',
        'KUAISHOU_UNIT_STATUS-10' => '已删除',
        'KUAISHOU_UNIT_STATUS-11' => '审核中',
        'KUAISHOU_UNIT_STATUS-12' => '审核不通过',
        'KUAISHOU_UNIT_STATUS-14' => '已结束',
        'KUAISHOU_UNIT_STATUS-15' => '已暂停',
        'KUAISHOU_UNIT_STATUS-17' => '组超预算',
        'KUAISHOU_UNIT_STATUS-19' => '未达投放时间',
        'KUAISHOU_UNIT_STATUS-20' => '有效',
        //计划类型
        'KUAISHOU_AD_TYPE-0' => '信息流',
        'KUAISHOU_AD_TYPE-1' => '搜索',
        //是否自动出价
        'KUAISHOU_SMART_BID-0' => '手动出价',
        'KUAISHOU_SMART_BID-1' => '自动出价',
        //优化目标
        'KUAISHOU_OCPX_ACTION_TYPE-0' => '未知',
        'KUAISHOU_OCPX_ACTION_TYPE-2' => '点击转化链接',
        'KUAISHOU_OCPX_ACTION_TYPE-10' => '曝光',
        'KUAISHOU_OCPX_ACTION_TYPE-11' => '点击',
        'KUAISHOU_OCPX_ACTION_TYPE-31' => '下载完成',
        'KUAISHOU_OCPX_ACTION_TYPE-53' => '提交线索',
        'KUAISHOU_OCPX_ACTION_TYPE-180' => '激活',
        'KUAISHOU_OCPX_ACTION_TYPE-348' => '有效线索',
        'KUAISHOU_OCPX_ACTION_TYPE-109' => '电话卡激活',
        'KUAISHOU_OCPX_ACTION_TYPE-137' => '量房',
        'KUAISHOU_OCPX_ACTION_TYPE-190' => ' 付费',
        'KUAISHOU_OCPX_ACTION_TYPE-191' => '首日ROI',
        'KUAISHOU_OCPX_ACTION_TYPE-324' => '唤起应用',
        'KUAISHOU_OCPX_ACTION_TYPE-383' => ' 授信',
        'KUAISHOU_OCPX_ACTION_TYPE-384' => '完件',
        'KUAISHOU_OCPX_ACTION_TYPE-394' => '订单提交',
        'KUAISHOU_OCPX_ACTION_TYPE-396' => '注册',
        'KUAISHOU_OCPX_ACTION_TYPE-715' => '微信复制优化目标',
        'KUAISHOU_OCPX_ACTION_TYPE-716' => '多转化事件',
        'KUAISHOU_OCPX_ACTION_TYPE-717' => '广告观看次数',
        'KUAISHOU_OCPX_ACTION_TYPE-731' => '广告观看5次',
        'KUAISHOU_OCPX_ACTION_TYPE-732' => '广告观看10次',
        'KUAISHOU_OCPX_ACTION_TYPE-733' => '广告观看20次',
        'KUAISHOU_OCPX_ACTION_TYPE-773' => '关键行为',
        'KUAISHOU_OCPX_ACTION_TYPE-774' => '7日ROI',
        'KUAISHOU_OCPX_ACTION_TYPE-72' => '小店通商品和主页推广',
        'KUAISHOU_OCPX_ACTION_TYPE-739' => '7日付费次数',
        'KUAISHOU_OCPX_ACTION_TYPE-392' => '小店通商品推广',
        'KUAISHOU_OCPX_ACTION_TYPE-395' => '小店通商品和主页推广',
        'KUAISHOU_OCPX_ACTION_TYPE-62' => '直播观看数',
        'KUAISHOU_OCPX_ACTION_TYPE-192' => '直播推广ROI',
        'KUAISHOU_OCPX_ACTION_TYPE-634' => '预约表单',
        'KUAISHOU_OCPX_ACTION_TYPE-635' => '预约点击跳转',
        'KUAISHOU_OCPX_ACTION_TYPE-810' => '激活付费',
        'KUAISHOU_OCPX_ACTION_TYPE-346' => '自然日次日留存',
        //深度转化目标
        'KUAISHOU_DEEP_CONVERSION_TYPE-0' => '无',
        'KUAISHOU_DEEP_CONVERSION_TYPE-3' => '付费',
        'KUAISHOU_DEEP_CONVERSION_TYPE-7' => '次日留存',
        'KUAISHOU_DEEP_CONVERSION_TYPE-10' => '完件',
        'KUAISHOU_DEEP_CONVERSION_TYPE-11' => '授信',
        'KUAISHOU_DEEP_CONVERSION_TYPE-13' => '添加购物车',
        'KUAISHOU_DEEP_CONVERSION_TYPE-14' => '提交订单',
        'KUAISHOU_DEEP_CONVERSION_TYPE-15' => '购买',
        'KUAISHOU_DEEP_CONVERSION_TYPE-44' => '有效线索',
        'KUAISHOU_DEEP_CONVERSION_TYPE-92' => '付费ROI',
        'KUAISHOU_DEEP_CONVERSION_TYPE-181' => '激活后24H次日留存',
        //创意展现方式
        'KUAISHOU_SHOW_MODE-0' => '默认',
        'KUAISHOU_SHOW_MODE-1' => '轮播',
        'KUAISHOU_SHOW_MODE-2' => '优选',
        //创意制作方式
        'KUAISHOU_UNIT_TYPE-3' => 'DPA 自定义创意',
        'KUAISHOU_UNIT_TYPE-4' => '自定义',
        'KUAISHOU_UNIT_TYPE-5' => '程序化创意',
        'KUAISHOU_UNIT_TYPE-7' => '程序化创意2.0',
        //素材类型
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-0' => '未知',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-1' => '竖版',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-2' => '横版',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-3' => '后贴片单图图片创意',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-5' => '竖版图片(优选/联盟)',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-6' => '横版图片(优选/联盟/信息流/快看点)',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-9' => '小图(优选/信息流/快看点)',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-10' => '组图(优选/信息流/快看点)',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-11' => '开屏视频',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-12' => '开屏图片',
        'KUAISHOU_CREATIVE_MATERIAL_TYPE-14' => 'DPA模版',
        //是否僵尸创意
        'KUAISHOU_CREATIVE_STATUS_TYPE-0' => '非僵尸',
        'KUAISHOU_CREATIVE_STATUS_TYPE-1' => '僵尸',
        /**
         * 百度
         */
        'CAMPAIGN_STATUS_DELIVERY_OK' => '有效',
        'CAMPAIGN_STATUS_NO_SCHEDULE' => '处于暂停时段',
        'CAMPAIGN_STATUS_BUDGET_EXCEED' => '推广计划预算不足',
        'CAMPAIGN_STATUS_ACCOUNT_UNACTIVATED' => '账户待激活',
        'CAMPAIGN_STATUS_ACCOUNT_BUDGET_EXCEED' => '账户预算不足',
        'CAMPAIGN_STATUS_BALANCE_EXCEED' => '账户余额为零',
        'CAMPAIGN_STATUS_FROZEN' => '被禁推',
        'CAMPAIGN_STATUS_APP_DOWN' => 'APP已下线',
        'CREATIVE_STATUS_INVALID' => '无效',
        'CREATIVE_STATUS_PARTIALLY_DELIVERY_OK' => '部分有效',

        'BAIDU_NOT_SKIP_STAGE_ONE' => '不跳过第一阶段',
        'BAIDU_SKIP_STAGE_ONE' => '跳过第一阶段',
        'BAIDU_FLOW_CONTROL_MODE-0' => '不跳过第一阶段',
        'BAIDU_FLOW_CONTROL_MODE-1' => '跳过第一阶段',
        //百度优化目标 1:点击（CPC）2:曝光（CPM）3:转化（oCPC/oCPM）5:eCPC'
        'BAIDU_AD_BIDTYPE-1' => '点击（CPC）',
        'BAIDU_AD_BIDTYPE-2' => '曝光（CPM）',
        'BAIDU_AD_BIDTYPE-3' => '转化（oCPC/oCPM）',
        'BAIDU_AD_BIDTYPE-5' => 'eCPC',
        //百度广告二级状态
        'BAIDU_ADVERTISING_OPT_STATUS-0' => '推广中',
        'BAIDU_ADVERTISING_OPT_STATUS-1' => '暂停推广',

    ];

    const INVENTORY_TO_CN = [
        //头条
        'INVENTORY_ESSAY_FEED' => '头条文章信息流',
        'INVENTORY_FEED' => '头条信息流',
        'INVENTORY_TEXT_LINK' => '头条文章详情页',
        'INVENTORY_VIDEO_FEED' => '西瓜信息流',
        'INVENTORY_HOTSOON_FEED' => '火山信息流',
        'INVENTORY_AWEME_FEED' => '抖音信息流',
        'INVENTORY_UNION_SLOT' => '穿山甲',
        'UNION_BOUTIQUE_GAME' => '穿山甲精选休闲游戏',
        'INVENTORY_UNION_SPLASH_SLOT' => '穿山甲开屏广告',
        'INVENTORY_AWEME_SEARCH' => '搜索广告—抖音位',
        'INVENTORY_SEARCH' => '搜索广告—头条位',
        'INVENTORY_UNIVERSAL' => '通投智选',
        'INVENTORY_BEAUTY' => '轻颜相机',
        'INVENTORY_PIPIXIA' => '皮皮虾',
        'INVENTORY_AUTOMOBILE' => '懂车帝',
        'INVENTORY_STUDY' => '好好学习',
        'INVENTORY_FACE_U' => 'faceu',
        'INVENTORY_TOMATO_NOVEL' => '番茄小说',

        //腾讯
        'SITE_SET_QZONE' => 'QQ空间PC站点',
        'SITE_SET_QQCLIENT' => 'QQ客户端',
        'SITE_SET_MUSIC' => 'QQ音乐PC站点',
        'SITE_SET_MOBILE_UNION' => '优量汇',
        'SITE_SET_KUAISHOU' => '快手',
        'SITE_SET_QQCOM' => '腾讯网-PC站点',
        'SITE_SET_WECHAT' => '微信-移动站点',
        'SITE_SET_MOBILE_MYAPP' => '应用宝移动-移动站点',
        'SITE_SET_MOBILE_INNER' => '移动内部站点-移动站点',
        'SITE_SET_TENCENT_NEWS' => '腾讯新闻',
        'SITE_SET_TENCENT_VIDEO' => '腾讯视频-移动站点',
        'SITE_SET_TENCENT_KUAIBAO' => '天天快报-移动站点',
        'SITE_SET_PCQQ' => 'QQ、QQ空间、腾讯音乐、腾讯网等PC媒体',
        'SITE_SET_KANDIAN' => '腾讯看点',
        'SITE_SET_QQ_MUSIC_GAME' => 'QQ、腾讯音乐及游戏',
        'SITE_SET_MOMENTS' => '微信朋友圈',
        'SITE_SET_MINI_GAME_WECHAT' => '微信小游戏',
        'SITE_SET_MINI_GAME_QQ' => 'QQ 小游戏',
        'SITE_SET_MOBILE_GAME' => 'App 游戏',
        'SITE_SET_QQSHOPPING' => 'QQ 购物',
        'SITE_SET_CHANNELS' => '微信视频号',
        'SITE_SET_WECHAT_PLUGIN' => '微信新闻插件',
        'SITE_SET_WECHAT_SEARCH' => '微信搜一搜',
        'SITE_SET_SEARCH_SCENE' => '搜索场景',
        'SITE_SET_QBSEARCH' => 'QQ 浏览器等（搜索广告）',
        'SITE_SET_XQXS' => '腾讯平台与内容媒体',
        //快手
        'KUAISHOU_SCENE_OPTIMIZATION' => '优选广告位',
        'KUAISHOU_SCENE_OBSOLETE_FEED' => '按场景选择广告位-信息流广告（旧广告位，包含上下滑大屏广告）',
        'KUAISHOU_SCENE_STICKY' => '视频播放页广告-便利贴广告',
        'KUAISHOU_SCENE_UPDOWN_VIDEO' => '上下滑大屏广告',
        'KUAISHOU_SCENE_FEED' => '信息流广告',
        'KUAISHOU-1' => '优选广告位',
        'KUAISHOU-2' => '按场景选择广告位-信息流广告（旧广告位，包含上下滑大屏广告）',
        'KUAISHOU-3' => '视频播放页广告-便利贴广告',
        'KUAISHOU-6' => '上下滑大屏广告',
        'KUAISHOU-7' => '信息流广告',
        //百度
        'BAIDU_FEED' => '百度信息流',
        'BAIDU_TIEBA' => '贴吧信息流',
        'BAIDU_UNOIN' => '百青藤移动',
        'BAIDU_HAOKAN' => '好看视频',
        'BAIDU_ABANDONED_FEED' => '百度信息流(弃用)',

        //1:百度信息流 2:贴吧信息4:百青藤 8:好看视频（好看视频流量目前在实验阶段，仅限已开通该流量的账户使用） 32: 爱奇艺（爱奇艺流量目前在实验阶段，仅限已开通该流量的账户使用） 65536: 信息流 131072:插屏和横幅 262144:app开屏 524288:激励视频'
        'BAIDU_INVENTORY-1' => '百度信息流',
        'BAIDU_INVENTORY-2' => '贴吧信息流',
        'BAIDU_INVENTORY-4' => '百青藤',
        'BAIDU_INVENTORY-8' => '好看视频',
        'BAIDU_INVENTORY-32' => '爱奇艺',
        'BAIDU_INVENTORY-64' => '百度小说',
        'BAIDU_INVENTORY-65536' => '百度信息流',
        'BAIDU_INVENTORY-131072' => '插屏和横幅',
        'BAIDU_INVENTORY-262144' => 'app开屏',
        'BAIDU_INVENTORY-524288' => '激励视频',
    ];

    //行为兴趣
    const BEHAVIOR_OR_INTEREST = [
        'interest' => '兴趣',
        'targeting_tags' => '标签列表',
        'category_id_list' => '标签列表ID',
        'keyword_list' => '关键词列表',
        'behavior' => '行为',
        'scene' => '场景',
        'time_window' => '时间窗',
        'intensity' => '强度',
        'intention' => '意向',
        //场景
        'BEHAVIOR_INTEREST_SCENE_ALL' => '全部场景',
        'BEHAVIOR_INTEREST_SCENE_APP' => 'App',
        'BEHAVIOR_INTEREST_SCENE_ECOMMERCE' => '电商',
        'BEHAVIOR_INTEREST_SCENE_INFORMATION' => '资讯',
        //时间窗
        'BEHAVIOR_INTEREST_TIME_WINDOW_SEVEN_DAY' => '7天',
        'BEHAVIOR_INTEREST_TIME_WINDOW_FIFTEEN_DAY' => '15天',
        'BEHAVIOR_INTEREST_TIME_WINDOW_THIRTY_DAY' => '30天',
        'BEHAVIOR_INTEREST_TIME_WINDOW_THREE_MONTH' => '3个月',
        'BEHAVIOR_INTEREST_TIME_WINDOW_SIX_MONTH' => '6个月',
        'BEHAVIOR_INTEREST_TIME_WINDOW_ONE_YEAR' => '1年',
        //行为兴趣意向定向的行为部分的强度
        'BEHAVIOR_INTEREST_INTENSITY_ALL' => '全部强度',
        'BEHAVIOR_INTEREST_INTENSITY_HIGH' => '高强度'
    ];

    //创意规格ID
    const ADCREATIVE_TEMPLATE_ID = [
        711 => '横版大图16:9',
        712 => '竖版大图9:16',
        713 => '通栏大图7:2',
        714 => '横版小图3:2',
        715 => '横版小图4:3',
        716 => '横版小图16:9',
        717 => '方形小图1:1',
        718 => '横版三小图3:2',
        719 => '横版三小图4:3',
        720 => '横版视频16:9',
        721 => '竖版视频9:16',
        722 => '贴片视频16:9',
        723 => '􏲮􏰑􏲱􏱱􏵎􏴋长形大图轮播16:9',
        724 => '方形大图轮播1:1',
        725 => '大图随心互动',
        726 => '全景广告',
        727 => '卡券广告',
        876 => 'QQ购物-基础',
        877 => 'QQ购物-视频',
        878 => 'QQ购物-橱窗',
        879 => 'QQ购物-标签',
        117 => '465x230单图(文)公众号、新闻插件底部',
        133 => '582x166单图(文)公众号、新闻插件底部',
        134 => '114x114单图(文)公众号、新闻插件底部',
        484 => '960x540单图(文)公众号文中广告',
        567 => '960x334单图(文)公众号文章底部广告',
        608 => '960x334单图(文)小程序',
        616 => '960x540单图视频播放前图片广告',
        677 => '640x360单图(文)公众号底部电商标签按钮样式',
        998 => '960x540单图公众号优雅大图',
        841 => '960x540单图(文)文中图文新样式',
        538 => '960x540视频公众号底部优雅视频',
        559 => '960x540视频小程序激励式视频',
        560 => '750x1334视频小程序激励式视频',
        1515 => '960x540视频视频播放前图片广告',
        925 => '960x334单图banner图片20:7',
        927 => '960x540单图(文)横版大图16:9',
        929 => '960x540视频 横版视频16:9',
        311 => '常规大图1:1 800x800',
        263 => '常规大图5:4 800x640',
        310 => '常规大图4:5 640x800',
        450 => '基础卡片 横版大图 16:9 800x450',
        588 => '标签卡片 横版大图 16:9 800x450',
        641 => '3图 800x800',
        644 => '3图 640x960',
        647 => '3图 640x1136',
        642 => '4图 800x800',
        645 => '4图 640x960',
        648 => '4图 640x1136',
        643 => '6图 800x800',
        646 => '6图 640x960',
        649 => '6图 640x1136',
        452 => '基础卡片 横版视频16:9',
        589 => '标签卡片 横版视频16:9',
        618 => '常规视频4:3',
        1064 => '常规视频750x1536',
        1465 => '常规视频16:9',
        1480 => '常规视频9:16',
        29 => '14字文字链',
        87 => '文字链',
        345 => '240x180单图(文)',
        443 => '324x204单图(文)',
        445 => '312x312单图(文)',
        468 => '640x360视频',
        1030 => '多宫格1:1',
        1708 => '朋友圈 卡片 广告横版视频16:9',
    ];

    //腾讯渠道包审核状态
    const TENCENT_CHANNEL_PACKAGE_STATUS = [
        'CHANNEL_PACKAGE_STATUS_PASSED' => '审核通过',
        'CHANNEL_PACKAGE_STATUS_REVIEWING' => '审核中',
        'CHANNEL_PACKAGE_STATUS_DENIED' => '审核拒绝',
        'CHANNEL_PACKAGE_STATUS_DENIED_AGAIN' => '审核拒绝，使用更新前的数据',
        'CHANNEL_PACKAGE_STATUS_REVIEWING_AGAIN' => '再次审核中',
        'CHANNEL_PACKAGE_STATUS_ON_OFFLINE' => '下线中',
        'CHANNEL_PACKAGE_STATUS_OFFLINE' => '下线',
        'CHANNEL_PACKAGE_STATUS_DRAFT' => '草稿'
    ];

    //百度渠道包审核状态
    const BAIDU_CHANNEL_PACKAGE_STATUS = [
        '1' => '审核中',
        '2' => '审核通过',
        '3' => '审核未通过'
    ];

    //头条转化类型
    const TOUTIAO_CONVERT_TYPE = [
        'AD_CONVERT_TYPE_MULTI_NATIVE_ACTION' => '互动',
        'AD_CONVERT_TYPE_EFFECTIVE_PLAY' => '有效播放',
        'AD_CONVERT_TYPE_CLICK_NUM' => '点击量',
        'AD_CONVERT_TYPE_SHOW_OFF_NUM' => '展示量',
        'AD_CONVERT_TYPE_PHONE' => '电话拨打',
        'AD_CONVERT_TYPE_FORM' => '表单提交',
        'AD_CONVERT_TYPE_MAP_SEARCH' => '地图搜索',
        'AD_CONVERT_TYPE_DOWNLOAD_FINISH' => '下载完成',
        'AD_CONVERT_TYPE_BUTTON' => '按钮跳转',
        'AD_CONVERT_TYPE_XPATH' => 'xpath类型转换',
        'AD_CONVERT_TYPE_VIEW' => '关键页面浏览',
        'AD_CONVERT_TYPE_ACTIVE' => '激活',
        'AD_CONVERT_TYPE_DOWNLOAD_START' => '下载按钮download_start',
        'AD_CONVERT_TYPE_QQ' => 'qq咨询',
        'AD_CONVERT_TYPE_LOTTERY' => '抽奖',
        'AD_CONVERT_TYPE_VOTE' => '投票',
        'AD_CONVERT_TYPE_ACTIVE_REGISTER' => '激活且注册',
        'AD_CONVERT_TYPE_PAY' => '激活且付费',
        'AD_CONVERT_TYPE_INSTALL_FINISH' => '安装完成',
        'AD_CONVERT_TYPE_PHONE_CONFIRM' => '智能电话-确认拨打',
        'AD_CONVERT_TYPE_PHONE_CONNECT' => '智能电话-确认接通',
        'AD_CONVERT_TYPE_PHONE_EFFECTIVE' => '智能电话-有效接通',
        'AD_CONVERT_TYPE_CONSULT_EFFECTIVE' => '有效咨询',
        'AD_CONVERT_TYPE_APP_ORDER' => 'app内下单(电商)',
        'AD_CONVERT_TYPE_APP_UV' => 'app内访问',
        'AD_CONVERT_TYPE_APP_CART' => 'app内添加购物车(电商)',
        'AD_CONVERT_TYPE_APP_PAY' => 'app内付费',
        'AD_CONVERT_TYPE_SALES_LEAD' => '销售线索',
        'AD_CONVERT_TYPE_GAME_ADDICTION' => '关键行为(原深度转化)',
        'AD_CONVERT_TYPE_CUSTOMER_EFFECTIVE' => '有效获客',
        'AD_CONVERT_TYPE_EFFECTIVE_COPY' => '关键页面到达&有效内容复制',
        'AD_CONVERT_TYPE_COUPON' => '卡券领取',
        'AD_CONVERT_TYPE_APP_DETAIL_UV' => 'app内详情页到站uv',
        'AD_CONVERT_TYPE_RSS' => '账号关注',
        'AD_CONVERT_TYPE_FORM_CONNECT' => '表单提交-已接通',
        'AD_CONVERT_TYPE_FORM_ANSWER' => '有效沟通',
        'AD_CONVERT_TYPE_DIALBACK' => '提交回呼电话',
        'AD_CONVERT_TYPE_DIALBACK_CONFIRM' => '回呼电话-确认拨打',
        'AD_CONVERT_TYPE_DIALBACK_CONNECT' => '回呼电话-确认接通',
        'AD_CONVERT_TYPE_FORM_DEEP' => '分层表单',
        'AD_CONVERT_TYPE_UPDATE_LEVEL' => '激活且升级',
        'AD_CONVERT_TYPE_CREATE_GAMEROLE' => '激活且创建角色',
        'AD_CONVERT_TYPE_NEXT_DAY_OPEN' => '激活且次留',
        'AD_CONVERT_TYPE_INVALID_CLUE' => '无效线索',
        'AD_CONVERT_TYPE_INTENTION_CLUE' => '有意向客户',
        'AD_CONVERT_TYPE_HIGH_VALUE_CLUE' => '高价值客户',
        'AD_CONVERT_TYPE_PAID_CLUE' => '已成单',
        'AD_CONVERT_TYPE_NATIVE_ACTION' => '原生互动',
        'AD_CONVERT_TYPE_LIKE_ACTION' => '视频点赞',
        'AD_CONVERT_TYPE_FOLLOW_ACTION' => '账户关注',
        'AD_CONVERT_TYPE_COMMENT_ACTION' => '视频评论',
        'AD_CONVERT_TYPE_LOCATION_ACTION' => 'POI点击',
        'AD_CONVERT_TYPE_SHOPPING_ACTION' => '购物车点击',
        'AD_CONVERT_TYPE_REDIRECT_TO_SHOP' => '调起店铺',
        'AD_CONVERT_TYPE_LINK_ACTION' => 'link点击',
        'AD_CONVERT_TYPE_DEEP_PURCHASE' => '多次付费',
        'AD_CONVERT_TYPE_SUCCESSORDER_ACTION' => '小店转化',
        'AD_CONVERT_TYPE_POI_COLLECT' => 'poi地址点击',
        'AD_CONVERT_TYPE_POI_ADDRESS_CLICK' => 'poi收藏',
        'AD_CONVERT_TYPE_RESERVATION' => 'poi预定',
        'AD_CONVERT_TYPE_MESSAGE_ACTION' => '私信消息',
        'AD_CONVERT_TYPE_SHARE_ACTION' => '分享',
        'AD_CONVERT_TYPE_CLICK_LANDING_PAGE' => '访问推广详情页',
        'AD_CONVERT_TYPE_CLICK_SHOPWINDOW' => '访问主页商品橱窗',
        'AD_CONVERT_TYPE_CLICK_DOWNLOAD' => '访问主页下载应用',
        'AD_CONVERT_TYPE_CLICK_CALL_DY' => '点击主页内电话拨打',
        'AD_CONVERT_TYPE_CLICK_WEBSITE' => '访问主页官网',
        'AD_CONVERT_TYPE_CONVERT_PAGE_VIEW' => '访问目标页面',
        'AD_CONVERT_TYPE_MESSAGE' => '短信',
        'AD_CONVERT_TYPE_REDIRECT' => '页面跳转',
        'AD_CONVERT_TYPE_SHOPPING' => '商品购买',
        'AD_CONVERT_TYPE_CONSULT' => '在线咨询',
        'AD_CONVERT_TYPE_WECHAT' => '微信',
        'AD_CONVERT_TYPE_OTHER' => '其他',
        'AD_CONVERT_TYPE_MULTIPLE' => '多转化事件',
        'AD_CONVERT_TYPE_POI_MULTIPLE' => 'POI门店多转化目标',
        'AD_CONVERT_TYPE_INTERACTION' => '互动',
        'AD_CONVERT_TYPE_LOAN_COMPLETION' => '互联网金融-完件',
        'AD_CONVERT_TYPE_PRE_LOAN_CREDIT' => '互联网金融-预授信',
        'AD_CONVERT_TYPE_LOAN_CREDIT' => '互联网金融-授信',
        'AD_CONVERT_TYPE_IDCARD_INFORMATION' => '身份证信息填写完成',
        'AD_CONVERT_TYPE_BANKCARD_INFORMATION' => '银行卡信息填写完成',
        'AD_CONVERT_TYPE_PERSONAL_INFORMATION' => '补充个人信息填写完成',
        'AD_CONVERT_TYPE_CERTIFICATION_INFORMATION' => '用户活体认证信息上传完成',
        'AD_CONVERT_TYPE_LT_ROI' => '广告变现ROI',
        'AD_CONVERT_TYPE_LIVE_HOMEPAGE' => '直播导流',
        'AD_CONVERT_TYPE_REDIRECT_TO_STORE' => '店铺导流',
        'AD_CONVERT_TYPE_FEED_LIVE_HOMEPAGE' => '火山feed进入直播页',
        'AD_CONVERT_TYPE_AUTHORIZATION' => '授权(电商)',
        'AD_CONVERT_TYPE_COMMODITY_CLICK' => '快上电商推广目的',
        'AD_CONVERT_TYPE_CONSULT_CLUE' => '留咨咨询',
        'AD_CONVERT_TYPE_BOOST' => '自然助推',
        'AD_CONVERT_TYPE_STAY_TIME' => '店铺停留',
        'AD_CONVERT_TYPE_PURCHASE_OF_GOODS' => '商品签收',
        'AD_CONVERT_TYPE_PURCHASE_ROI' => '付费ROI',
        'AD_CONVERT_TYPE_LIVE_NATIVE_ACITON' => '直播间原生互动',
        'AD_CONVERT_TYPE_LIVE_FOLLOW_ACITON' => '直播间关注',
        'AD_CONVERT_TYPE_LIVE_COMMENT_ACTION' => '直播间评论',
        'AD_CONVERT_TYPE_LIVE_GIFT_ACTION' => '直播间内打赏',
        'AD_CONVERT_TYPE_LIVE_SLIDECART_CLICK_ACTION' => '直播间查看购物车',
        'AD_CONVERT_TYPE_LIVE_CLICK_PRODUCT_ACTION' => '直播间查看商品',
        'AD_CONVERT_TYPE_LIVE_ENTER_ACTION' => '直播间观看',
        'AD_CONVERT_TYPE_LIVE_SUCCESSORDER_ACTION' => '直播间成单',
        'AD_CONVERT_TYPE_NOTIFY_DOWNLOAD' => '预约下载',
        'AD_CONVERT_TYPE_PREMIUM_PAYMENT' => '保险支付',
        'AD_CONVERT_TYPE_MESSAGE_CLICK' => '私信点击',
        'AD_CONVERT_TYPE_UG_ROI' => '内广roi',
        'AD_CONVERT_TYPE_ENTER_HOMEPAGE' => '进入个人主页',
        'AD_CONVERT_TYPE_SHOPPING_CART' => '商品购物车点击',
        'AD_CONVERT_TYPE_WECHAT_REGISTER' => '微信内注册',
        'AD_CONVERT_TYPE_WECHAT_PAY' => '微信内付费',
        'AD_CONVERT_TYPE_MESSAGE_INTERACTION' => '沟通互动',
        'AD_CONVERT_TYPE_LIVE_STAY_TIME' => '直播间停留',
        'AD_CONVERT_TYPE_NEW_FOLLOW_ACTION' => '粉丝增长',
        'AD_CONVERT_TYPE_APPLET_CLICK' => '小程序互动',
        'AD_CONVERT_TYPE_MESSAGE_SERVICE' => '私信服务',
        'AD_CONVERT_TYPE_MESSAGE_CLUE' => '私信留资',
        'AD_CONVERT_TYPE_LIVE_FANS_ACTION' => '直播间加入粉丝团',
        'AD_CONVERT_TYPE_CLUE_CONFIRM' => '回访_信息确认',
        'AD_CONVERT_TYPE_CLUE_INTERFLOW' => '回访_加为好友',
        'AD_CONVERT_TYPE_CLUE_HIGH_INTENTION' => '回访_高潜成交',
        'AD_CONVERT_TYPE_SUBMIT_CERTIFICATION' => '提交认证',
        'AD_CONVERT_TYPE_FIRST_RENTAL_ORDER' => '首次发单',
        'AD_CONVERT_TYPE_LIVE_COMPONENT_CLICK' => '组件点击',
        'AD_CONVERT_TYPE_LIVE_BUSINESS_FITTING' => '直播间组件点击',
        'AD_CONVERT_TYPE_CLUE_PAY_SUCCEED' => '支付_存在意向',
        'AD_CONVERT_TYPE_RETENTION_7D' => '7日留存',
        'AD_CONVERT_TYPE_OTO_STAY_TIME' => '团单浏览',
        'AD_CONVERT_TYPE_OTO_PAY' => '团购支付',
        'AD_CONVERT_TYPE_PREMIUM_ROI' => '保费ROI',
        'AD_CONVERT_TYPE_MESSAGE_ORDER_SUCCESS' => '私信成单',
        'AD_CONVERT_TYPE_MESSAGE_JOIN_GROUP' => '私信用户入群',
        'AD_CONVERT_TYPE_LIVE_JOIN_GROUP' => '粉丝入群',
        'AD_CONVERT_TYPE_LIVE_APPOINTMENT' => '预约直播',
        'AD_CONVERT_TYPE_FOLLOW_LIVE_ENTER' => '粉丝访问直播',
        'AD_CONVERT_TYPE_FOLLOW_CLICK_PRODUCT' => '关注并加购',
        'AD_CONVERT_TYPE_FOLLOW_VIDEO_PLAY_FINISH' => '粉丝观看',
        'AD_CONVERT_TYPE_GAMESTATION_DOWNLOAD_DOUPLUS' => '游戏站下载 - DOU +',
        'AD_CONVERT_TYPE_QC_FOLLOW_ACTION' => '提升粉丝',
        'AD_CONVERT_TYPE_QC_MUST_BUY' => '内容种草',
        'AD_CONVERT_TYPE_NOTIFY_FORM' => '预约表单',
        'AD_CONVERT_TYPE_FIRST_CLASS' => '到课',
        'AD_CONVERT_TYPE_CONVERSION_CLASS' => '正价课购买',
        'AD_CONVERT_TYPE_IPU_QUALIFY' => '激活首日广告展示达标',
        'AD_CONVERT_TYPE_ANCHOR_CLICK' => '直播支付ROI',
        'AD_CONVERT_TYPE_INAPP_NEXT_DAY_OPEN' => '拉活次留',
        'AD_CONVERT_TYPE_PURCHASE_ROI_2D' => '付费roi-2日',
        'AD_CONVERT_TYPE_PURCHASE_ROI_7D' => '付费roi-7日',
        'AD_CONVERT_TYPE_LIVE_OTO_PAY' => '直播间团购支付',
        'AD_CONVERT_TYPE_LIVE_OTO_PAY_CLICK' => '直播间团购发起支付',
        'AD_CONVERT_TYPE_LIVE_OTO_CLICK' => '直播间团购点击',
        'AD_CONVERT_TYPE_WECHAT_WECOM_ADD' => '添加企业微信',
        'AD_CONVERT_TYPE_WECHAT_FIRST_MSG' => '微信_用户首次消息',
        'AD_CONVERT_TYPE_RETENTION_DAYS' => '留存天数',
        'AD_CONVERT_TYPE_INTELLIGENT_SERVICE_OPEN' => '智能客服_用户开口',
        'AD_CONVERT_PAGE_VIEW' => '访问目标页面',
    ];

    const DIFF_MEDIA_STATUS_MAP = [
        MediaType::KUAISHOU => [
            1 => [
                1 => '投放',
                2 => '暂停',
                3 => '删除',
            ],
            2 => [
                1 => '投放',
                2 => '暂停',
                3 => '删除',
            ],
            3 => [
                1 => '投放',
                2 => '暂停',
                3 => '删除',
            ],
        ]
    ];

    const DIFF_MEDIA_AD1_TYPE_MAP = [
        MediaType::KUAISHOU => [
            2 => '提升应用安装',
            3 => '获取电商下单',
            4 => '推广品牌活动',
            5 => '收集销售线索',
            7 => '提高应用活跃',
            9 => '商品库推广'
        ],
        MediaType::BAIDU => [
            1 => '网站链接',
            2 => '应用下载（iOS）',
            3 => '应用下载（Android）',
        ]
    ];

    const DIFF_MEDIA_OPT_STATUS_MAP = [
        MediaType::KUAISHOU => [
            1 => [
                -1 => '不限',
                1 => '已暂停',
                2 => '不限',
                3 => '计划超预算',
                4 => '有效',
                5 => '已删除',
                6 => '余额不足'
            ],
            2 => [
                -1 => '不限',
                1 => '计划已暂停',
                3 => '计划超预算',
                5 => '广告计划已删除',
                6 => '余额不足',
                11 => '审核中',
                12 => '审核未通过',
                14 => '已结束',
                15 => '已暂停',
                17 => '组超预算',
                19 => '未达投放时间',
                20 => '有效',
                -2 => '所有包含已删除',
                10 => '只包含已删除'
            ],
            3 => [
                -1 => '不限',
                1 => '计划已暂停',
                3 => '计划超预算',
                6 => '余额不足',
                11 => '组审核中',
                12 => '组审核未通过',
                14 => '已结束',
                15 => '组已暂停',
                17 => '组超预算',
                19 => '未达投放时间',
                40 => '已删除',
                41 => '审核中',
                42 => '审核未通过',
                46 => '已暂停',
                52 => '投放中',
                53 => '作品异常',
                54 => '视频审核通过可投放滑滑场景',
                55 => '部分素材审核失败'
            ]
        ],
        MediaType::BAIDU => [
            1 => [
                0 => '有效',
                1 => '处于暂停时段',
                2 => '暂停推广',
                3 => '推广计划预算不足',
                4 => '账户待激活',
                11 => '账户预算不足',
                20 => '账户余额为零',
                23 => '被禁推',
                24 => 'app已下线'
            ],
            2 => [
                0 => '有效',
                1 => '暂停推广',
                2 => '推广计划暂停推广',
                -100 => '已删除'
            ],
            3 => [
                0 => '有效',
                1 => '暂停推广',
                2 => '审核中',
                3 => '审核未通过',
                4 => '无效',
                5 => '部分有效'
            ]
        ]
    ];

    const DIFF_MEDIA_OS_MAP = [
        MediaType::KUAISHOU => [
            1 => 'Android',
            2 => 'iOS',
            0 => '不限',
        ],
        MediaType::BAIDU => [
            0 => '全部',
            1 => 'iOS',
            2 => 'Android',
            4 => '计算机',
            3 => '其他',
        ]
    ];

    const DIFF_MEDIA_PRICING_MAP = [
        MediaType::BAIDU => [
            1 => 'oCPC',
            2 => 'oCPM'
        ]
    ];

    //头条深度优化方式
    const TT_DEEP_BID_TYPE = [
        'DEEP_BID_DEFAULT' => '不启用，无深度优化',
        'DEEP_BID_PACING' => '自动优化(手动出价方式下)',
        'DEEP_BID_MIN' => '自定义双出价(手动出价方式下)',
        'ROI_COEFFICIENT' => 'ROI系数',
        'ROI_PACING' => 'ROI系数-自动优化',
        'MIN_SECOND_STAGE' => '两阶段优化',
        'PACING_SECOND_STAGE' => '动态两阶段',
        'SMARTBID' => '自动优化(自动出价方式下)',
        'AUTO_MIN_SECOND_STAGE' => '自定义双出价(自动出价方式下)',
        'BID_PER_ACTION' => '每次付费出价',
        'FIRST_AND_SEVEN_PAY_ROI' => '首日ROI+7日ROI',
        'PER_AND_SEVEN_PAY_ROI' =>  '每次付费+7日ROI',
    ];

    const TOUTIAO_ACCOUNT_STATUS = [
        'STATUS_ENABLE' => '已审核',
        'STATUS_DISABLE' => '已禁用',
        'STATUS_PENDING_CONFIRM' => '审核中',
        'STATUS_PENDING_VERIFIED' => '待验证',
        'STATUS_CONFIRM_FAIL' => '审核失败/可再次申请',
        'STATUS_CONFIRM_FAIL_END' => '审核失败/最终状态',
        'STATUS_PENDING_CONFIRM_MODIFY' => '修改审核中',
        'STATUS_CONFIRM_MODIFY_FAIL' => '修改审核失败',
        'STATUS_PUNISH' => '惩罚',
        'STATUS_WAIT_FOR_BPM_AUDIT' => '等待CRM审核',
        'STATUS_SELF_SERVICE_UNAUDITED' => '待验证资质',
        'STATUS_ENABLE_AND_AVATAR_AUDITING' => 'SMB客户待合同归档',
        'STATUS_WAIT_FOR_BPM_FILE_CONTACT' => 'SMB客户待合同归档',
        'STATUS_WAIT_FOR_ACCOUNT_FEE' => 'SMB广告主待缴纳开户费',
        'STATUS_WAIT_FOR_PUBLIC_AUTH' => '待对公验证',
    ];

    const TENCENT_ACCOUNT_STATUS = [
        'CUSTOMER_STATUS_NORMAL' => '有效',
        'CUSTOMER_STATUS_PENDING' => '待审核',
        'CUSTOMER_STATUS_DENIED' => '审核不通过',
        'CUSTOMER_STATUS_FROZEN' => '封停',
        'CUSTOMER_STATUS_TOBE_ACCEPTED' => '待接受',
        'CUSTOMER_STATUS_TOBE_ACTIVATED' => '待激活',
        'CUSTOMER_STATUS_SUSPEND' => '暂停',
        'CUSTOMER_STATUS_MATERIAL_PREPARED' => '广告主资料准备',
        'CUSTOMER_STATUS_DELETED' => '删除',
        'CUSTOMER_STATUS_FROZEN_TEMPORARILY' => '临时冻结',
        'CUSTOMER_STATUS_UNREGISTERED' => '未注册',
    ];

    const TT_AD1_BUDGET_OPTIMIZATION = [
        'ON' => '是',
        'OFF' => '否'
    ];

    const TT_CAMPAIGN_DELIVERY_MODE = [
        'PROCEDURAL' => 'UBA',
        'MANUAL' => '手动'
    ];

    //是否低效素材
    const IS_INEFFICIENT = [
        '否',
        '是'
    ];

    //是否有素材审核内容
    const IS_MATERIAL_REJECT = [
        '无',
        '有'
    ];

    //头条素材类型
    const TT_MATERIALS_TYPE = [
        'LIVE_MATERIALS' => '直播素材',
        'PROMOTION_MATERIALS' => '广告素材',
//        'CREATIVE_IMAGE_MODE_SMALL' => '小图',
//        'CREATIVE_IMAGE_MODE_LARGE' => '大图',
//        'CREATIVE_IMAGE_MODE_GROUP' => '组图',
//        'CREATIVE_IMAGE_MODE_VIDEO' => '横版视频',
//        'CREATIVE_IMAGE_MODE_GIF' => 'GIF图',
//        'CREATIVE_IMAGE_MODE_LARGE_VERTICAL' => '大图竖图',
//        'CREATIVE_IMAGE_MODE_VIDEO_VERTICAL' => '竖版视频',
//        'TOUTIAO_SEARCH_AD_IMAGE' => '搜索大图',
//        'SEARCH_AD_SMALL_IMAGE' => '搜索小图',
//        'CREATIVE_IMAGE_MODE_UNION_SPLASH' => '穿山甲开屏图片',
//        'CREATIVE_IMAGE_MODE_UNION_SPLASH_VIDEO' => '穿山甲开屏视频',
//        'CREATIVE_IMAGE_MODE_DISPLAY_WINDOW' => '搜索橱窗',
//        'MATERIAL_IMAGE_MODE_TITLE' => '标题类型',
//        'CREATIVE_IMAGE_MODE_AWEME_LIVE' => '直播画面',
//        'CREATIVE_IMAGE_MODE_PLAYABLE_HORIZONTAL' => '横版试玩素材',
//        'CREATIVE_IMAGE_MODE_PLAYABLE_VERTICAL' => '竖版试玩素材',
//        'CREATIVE_IMAGE_MODE_DIRECT_PLAYABLE' => '直出互动素材',
//        'CREATIVE_IMAGE_MODE_DECORATION_COUPON' => '家装卡券素材',
    ];

    //直播订单类型
    const LIVE_VIDEO_TYPE = [
        '0' => '直播',
        '1' => '短视频',
    ];

    //直播订单状态
    const LIVE_ORDER_STATUS_TYPE = [
        'ALL' => '不限',
        'WAIT_PAYMENT' => '待付款',
        'RECEIVEING' => '待接收',
        'ONGOING' => '进行中',
        'FINISHED' => '已完成',
        'WAIT_EVALUATE' => '待评价',
        'CANCELED' => '已取消',
    ];

    // 转化类型 （头条/快手/腾讯/百度）
    const CONVERT_TYPE = [
        'AD_CONVERT_TYPE_EFFECTIVE_PLAY' => '有效播放',
        'AD_CONVERT_TYPE_CLICK_NUM' => '点击量',
        'AD_CONVERT_TYPE_SHOW_OFF_NUM' => '展示量',
        'AD_CONVERT_TYPE_PHONE' => '电话拨打',
        'AD_CONVERT_TYPE_FORM' => '表单提交',
        'AD_CONVERT_TYPE_MAP_SEARCH' => '地图搜索',
        'AD_CONVERT_TYPE_DOWNLOAD_FINISH' => '下载完成',
        'AD_CONVERT_TYPE_BUTTON' => '按钮跳转',
        'AD_CONVERT_TYPE_XPATH' => 'xpath类型转换',
        'AD_CONVERT_TYPE_VIEW' => '关键页面浏览',
        'AD_CONVERT_TYPE_ACTIVE' => '激活',
        'AD_CONVERT_TYPE_DOWNLOAD_START' => '下载按钮download_start',
        'AD_CONVERT_TYPE_QQ' => 'qq咨询',
        'AD_CONVERT_TYPE_LOTTERY' => '抽奖',
        'AD_CONVERT_TYPE_VOTE' => '投票',
        'AD_CONVERT_TYPE_ACTIVE_REGISTER' => '激活且注册',
        'AD_CONVERT_TYPE_PAY' => '激活且付费',
        'AD_CONVERT_TYPE_INSTALL_FINISH' => '安装完成',
        'AD_CONVERT_TYPE_PHONE_CONFIRM' => '智能电话-确认拨打',
        'AD_CONVERT_TYPE_PHONE_CONNECT' => '智能电话-确认接通',
        'AD_CONVERT_TYPE_PHONE_EFFECTIVE' => '智能电话-有效接通',
        'AD_CONVERT_TYPE_CONSULT_EFFECTIVE' => '有效咨询',
        'AD_CONVERT_TYPE_APP_ORDER' => 'app内下单（电商）',
        'AD_CONVERT_TYPE_APP_UV' => 'app内访问',
        'AD_CONVERT_TYPE_APP_CART' => 'app内添加购物车（电商）',
        'AD_CONVERT_TYPE_APP_PAY' => 'app内付费',
        'AD_CONVERT_TYPE_SALES_LEAD' => '销售线索',
        'AD_CONVERT_TYPE_GAME_ADDICTION' => '关键行为（原深度转化）',
        'AD_CONVERT_TYPE_CUSTOMER_EFFECTIVE' => '有效获客',
        'AD_CONVERT_TYPE_EFFECTIVE_COPY' => '关键页面到达&有效内容复制',
        'AD_CONVERT_TYPE_COUPON' => '卡券领取',
        'AD_CONVERT_TYPE_APP_DETAIL_UV' => 'app内详情页到站uv',
        'AD_CONVERT_TYPE_RSS' => '账号关注',
        'AD_CONVERT_TYPE_FORM_CONNECT' => '表单提交-已接通',
        'AD_CONVERT_TYPE_FORM_ANSWER' => '有效沟通',
        'AD_CONVERT_TYPE_DIALBACK' => '提交回呼电话',
        'AD_CONVERT_TYPE_DIALBACK_CONFIRM' => '回呼电话-确认拨打',
        'AD_CONVERT_TYPE_DIALBACK_CONNECT' => '回呼电话-确认接通',
        'AD_CONVERT_TYPE_FORM_DEEP' => '分层表单',
        'AD_CONVERT_TYPE_UPDATE_LEVEL' => '激活且升级',
        'AD_CONVERT_TYPE_CREATE_GAMEROLE' => '激活且创建角色',
        'AD_CONVERT_TYPE_NEXT_DAY_OPEN' => '激活且次留',
        'AD_CONVERT_TYPE_INVALID_CLUE' => '无效线索',
        'AD_CONVERT_TYPE_INTENTION_CLUE' => '有意向客户',
        'AD_CONVERT_TYPE_HIGH_VALUE_CLUE' => '高价值客户',
        'AD_CONVERT_TYPE_PAID_CLUE' => '已成单',
        'AD_CONVERT_TYPE_NATIVE_ACTION' => '原生互动',
        'AD_CONVERT_TYPE_LIKE_ACTION' => '视频点赞',
        'AD_CONVERT_TYPE_FOLLOW_ACTION' => '账户关注',
        'AD_CONVERT_TYPE_COMMENT_ACTION' => '视频评论',
        'AD_CONVERT_TYPE_LOCATION_ACTION' => 'POI点击',
        'AD_CONVERT_TYPE_SHOPPING_ACTION' => '购物车点击',
        'AD_CONVERT_TYPE_REDIRECT_TO_SHOP' => '调起店铺',
        'AD_CONVERT_TYPE_LINK_ACTION' => 'link点击',
        'AD_CONVERT_TYPE_DEEP_PURCHASE' => '多次付费',
        'AD_CONVERT_TYPE_SUCCESSORDER_ACTION' => '小店转化',
        'AD_CONVERT_TYPE_POI_COLLECT' => 'poi地址点击',
        'AD_CONVERT_TYPE_POI_ADDRESS_CLICK' => 'poi收藏',
        'AD_CONVERT_TYPE_RESERVATION' => 'poi预定',
        'AD_CONVERT_TYPE_MESSAGE_ACTION' => '私信消息',
        'AD_CONVERT_TYPE_SHARE_ACTION' => '分享',
        'AD_CONVERT_TYPE_CLICK_LANDING_PAGE' => '访问推广详情页',
        'AD_CONVERT_TYPE_CLICK_SHOPWINDOW' => '访问主页商品橱窗',
        'AD_CONVERT_TYPE_CLICK_DOWNLOAD' => '访问主页下载应用',
        'AD_CONVERT_TYPE_CLICK_CALL_DY' => '点击主页内电话拨打',
        'AD_CONVERT_TYPE_CLICK_WEBSITE' => '访问主页官网',
        'AD_CONVERT_PAGE_VIEW' => '访问目标页面',
        'AD_CONVERT_TYPE_MESSAGE' => '短信',
        'AD_CONVERT_TYPE_REDIRECT' => '页面跳转',
        'AD_CONVERT_TYPE_SHOPPING' => '商品购买',
        'AD_CONVERT_TYPE_CONSULT' => '在线咨询',
        'AD_CONVERT_TYPE_WECHAT' => '微信',
        'AD_CONVERT_TYPE_OTHER' => '其他',
        'AD_CONVERT_TYPE_MULTIPLE' => '多转化事件',
        'AD_CONVERT_TYPE_POI_MULTIPLE' => 'POI门店多转化目标',
        'AD_CONVERT_TYPE_INTERACTION' => '互动',
        'AD_CONVERT_TYPE_LOAN_COMPLETION' => '互联网金融-完件',
        'AD_CONVERT_TYPE_PRE_LOAN_CREDIT' => '互联网金融-预授信',
        'AD_CONVERT_TYPE_LOAN_CREDIT' => '互联网金融-授信',
        'AD_CONVERT_TYPE_IDCARD_INFORMATION' => '身份证信息填写完成',
        'AD_CONVERT_TYPE_BANKCARD_INFORMATION' => '银行卡信息填写完成',
        'AD_CONVERT_TYPE_PERSONAL_INFORMATION' => '补充个人信息填写完成',
        'AD_CONVERT_TYPE_CERTIFICATION_INFORMATION' => '用户活体认证信息上传完成',
        'AD_CONVERT_TYPE_LT_ROI' => '广告变现ROI',
        'AD_CONVERT_TYPE_LIVE_HOMEPAGE' => '直播导流',
        'AD_CONVERT_TYPE_REDIRECT_TO_STORE' => '店铺导流',
        'AD_CONVERT_TYPE_FEED_LIVE_HOMEPAGE' => '火山feed进入直播页',
        'AD_CONVERT_TYPE_AUTHORIZATION' => '授权（电商）',
        'AD_CONVERT_TYPE_COMMODITY_CLICK' => '快上电商推广目的',
        'AD_CONVERT_TYPE_CONSULT_CLUE' => '留咨咨询',
        'AD_CONVERT_TYPE_BOOST' => '自然助推',
        'AD_CONVERT_TYPE_STAY_TIME' => '店铺停留',
        'AD_CONVERT_TYPE_PURCHASE_OF_GOODS' => '商品签收',
        'AD_CONVERT_TYPE_PURCHASE_ROI' => '付费ROI',
        'AD_CONVERT_TYPE_LIVE_NATIVE_ACITON' => '直播间原生互动',
        'AD_CONVERT_TYPE_LIVE_FOLLOW_ACITON' => '直播间关注',
        'AD_CONVERT_TYPE_LIVE_COMMENT_ACTION' => '直播间评论',
        'AD_CONVERT_TYPE_LIVE_GIFT_ACTION' => '直播间内打赏',
        'AD_CONVERT_TYPE_LIVE_SLIDECART_CLICK_ACTION' => '直播间查看购物车',
        'AD_CONVERT_TYPE_LIVE_CLICK_PRODUCT_ACTION' => '直播间查看商品',
        'AD_CONVERT_TYPE_LIVE_ENTER_ACTION' => '直播间观看',
        'AD_CONVERT_TYPE_LIVE_SUCCESSORDER_ACTION' => '直播间成单',
        'AD_CONVERT_TYPE_NOTIFY_DOWNLOAD' => '预约下载',
        'AD_CONVERT_TYPE_PREMIUM_PAYMENT' => '保险支付',
        'AD_CONVERT_TYPE_MESSAGE_CLICK' => '私信点击',
        'AD_CONVERT_TYPE_UG_ROI' => '内广roi',
        'AD_CONVERT_TYPE_ENTER_HOMEPAGE' => '进入个人主页',
        'AD_CONVERT_TYPE_SHOPPING_CART' => '商品购物车点击',
        'AD_CONVERT_TYPE_WECHAT_REGISTER' => '微信内注册',
        'AD_CONVERT_TYPE_WECHAT_PAY' => '微信内付费',
        'AD_CONVERT_TYPE_MESSAGE_INTERACTION' => '沟通互动',
        'AD_CONVERT_TYPE_LIVE_STAY_TIME' => '直播间停留',
        'AD_CONVERT_TYPE_NEW_FOLLOW_ACTION' => '粉丝增长',
        'AD_CONVERT_TYPE_APPLET_CLICK' => '小程序互动',
        'AD_CONVERT_TYPE_MESSAGE_SERVICE' => '私信服务',
        'AD_CONVERT_TYPE_MESSAGE_CLUE' => '私信留资',
        'AD_CONVERT_TYPE_LIVE_FANS_ACTION' => '直播间加入粉丝团',
        'AD_CONVERT_TYPE_CLUE_CONFIRM' => '回访_信息确认',
        'AD_CONVERT_TYPE_CLUE_INTERFLOW' => '回访_加为好友',
        'AD_CONVERT_TYPE_CLUE_HIGH_INTENTION' => '回访_高潜成交',
        'AD_CONVERT_TYPE_SUBMIT_CERTIFICATION' => '提交认证',
        'AD_CONVERT_TYPE_FIRST_RENTAL_ORDER' => '首次发单',
        'AD_CONVERT_TYPE_LIVE_COMPONENT_CLICK' => '组件点击',
        'AD_CONVERT_TYPE_LIVE_BUSINESS_FITTING' => '直播间组件点击',
        'AD_CONVERT_TYPE_CLUE_PAY_SUCCEED' => '支付_存在意向',
        'AD_CONVERT_TYPE_RETENTION_7D' => '7日留存',
        'AD_CONVERT_TYPE_OTO_STAY_TIME' => '团单浏览',
        'AD_CONVERT_TYPE_OTO_PAY' => '团购支付',
        'AD_CONVERT_TYPE_PREMIUM_ROI' => '保费ROI',
        'AD_CONVERT_TYPE_MESSAGE_ORDER_SUCCESS' => '私信成单',
        'AD_CONVERT_TYPE_MESSAGE_JOIN_GROUP' => '私信用户入群',
        'AD_CONVERT_TYPE_LIVE_JOIN_GROUP' => '粉丝入群',
        'AD_CONVERT_TYPE_LIVE_APPOINTMENT' => '预约直播',
        'AD_CONVERT_TYPE_FOLLOW_LIVE_ENTER' => '粉丝访问直播',
        'AD_CONVERT_TYPE_FOLLOW_CLICK_PRODUCT' => '关注并加购',
        'AD_CONVERT_TYPE_FOLLOW_VIDEO_PLAY_FINISH' => '粉丝观看',
        'AD_CONVERT_TYPE_GAMESTATION_DOWNLOAD_DOUPLUS' => '游戏站下载 - DOU +',
        'AD_CONVERT_TYPE_QC_FOLLOW_ACTION' => '提升粉丝',
        'AD_CONVERT_TYPE_QC_MUST_BUY' => '内容种草',
        'AD_CONVERT_TYPE_NOTIFY_FORM' => '预约表单',
        'AD_CONVERT_TYPE_FIRST_CLASS' => '到课',
        'AD_CONVERT_TYPE_CONVERSION_CLASS' => '正价课购买',
        'AD_CONVERT_TYPE_IPU_QUALIFY' => '激活首日广告展示达标',
        'AD_CONVERT_TYPE_ANCHOR_CLICK' => '直播支付ROI',
        'AD_CONVERT_TYPE_INAPP_NEXT_DAY_OPEN' => '拉活次留',
        'AD_CONVERT_TYPE_PURCHASE_ROI_2D' => '付费roi-2日',
        'AD_CONVERT_TYPE_PURCHASE_ROI_7D' => '付费roi-7日',
        'AD_CONVERT_TYPE_LIVE_OTO_PAY' => '直播间团购支付',
        'AD_CONVERT_TYPE_LIVE_OTO_PAY_CLICK' => '直播间团购发起支付',
        'AD_CONVERT_TYPE_LIVE_OTO_CLICK' => '直播间团购点击',
        'AD_CONVERT_TYPE_WECHAT_WECOM_ADD' => '添加企业微信',
        'AD_CONVERT_TYPE_WECHAT_FIRST_MSG' => '微信_用户首次消息',
        'AD_CONVERT_TYPE_RETENTION_DAYS' => '留存天数',
        'AD_CONVERT_TYPE_INTELLIGENT_SERVICE_OPEN' => '智能客服_用户开口',
        'AD_CONVERT_TYPE_FORTUNE_SMALL_DEAL' => '财富小单成交',
        'OPTIMIZATIONGOAL_NONE' => 'none',
        'OPTIMIZATIONGOAL_BRAND_CONVERSION' => '指定页面曝光',
        'OPTIMIZATIONGOAL_FOLLOW' => '关注',
        'OPTIMIZATIONGOAL_CLICK' => '点击',
        'OPTIMIZATIONGOAL_IMPRESSION' => '曝光',
        'OPTIMIZATIONGOAL_APP_DOWNLOAD' => '下载',
        'OPTIMIZATIONGOAL_ECOMMERCE_CHECKOUT' => 'H5 付费次数（待废弃）',
        'OPTIMIZATIONGOAL_LEADS' => '表单预约（微信流量，待废弃）',
        'OPTIMIZATIONGOAL_PROMOTION_CLICK_KEY_PAGE' => 'H5 注册（待废弃）',
        'OPTIMIZATIONGOAL_DELIVERY' => '发货',
        'OPTIMIZATIONGOAL_MESSAGE_AFTER_FOLLOW' => '公众号内发消息',
        'OPTIMIZATIONGOAL_CLICK_MENU_AFTER_FOLLOW' => '公众号内点击菜单栏',
        'OPTIMIZATIONGOAL_LEADS_COLLECT' => '综合线索收集',
        'OPTIMIZATIONGOAL_APPLY' => '进件',
        'OPTIMIZATIONGOAL_WITHDRAW_DEPOSITS' => '提现',
        'OPTIMIZATIONGOAL_ECOMMERCE_ADD_TO_WISHLIST' => '商品收藏',
        'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_RESERVATION' => '有效表单预约（待废弃）',
        'OPTIMIZATIONGOAL_PAGE_SCAN_CODE' => '加企业微信客服',
        'OPTIMIZATIONGOAL_SELECT_COURSE' => '选课',
        'OPTIMIZATIONGOAL_CONFIRM_POTENTIAL_CUSTOMER_PHONE' => '电话潜在客户',
        'OPTIMIZATIONGOAL_MOBILE_APP_ACCREDIT' => '小游戏授权',
        'OPTIMIZATIONGOAL_PURCHASE_MEMBER_CARD' => '首次会员购买',
        'OPTIMIZATIONGOAL_PAGE_CONFIRM_EFFECTIVE_LEADS' => '有效综合线索',
        'OPTIMIZATIONGOAL_ADD_DESKTOP' => '快应用加桌面',
        'OPTIMIZATIONGOAL_RESERVATION' => '微信流量预约行为',
        'OPTIMIZATIONGOAL_FIRST_ECOMMERCE_ORDER' => '首次下单',
        'OPTIMIZATIONGOAL_FIRST_TWENTY_FOUR_HOUR_ECOMMERCE_ORDER' => '24 小时下单',
        'OPTIMIZATIONGOAL_EXTERNAL_LINK_CLICK' => '外链点击',
        'OPTIMIZATIONGOAL_BUY_COUPONS' => '购券',
        'OPTIMIZATIONGOAL_ONE_DAY_RETENTION_RATIO' => '次留率',
        'OPTIMIZATIONGOAL_PROMOTION_READ_ARTICLE' => '阅读文章',
        'OPTIMIZATIONGOAL_RESERVATION_CHECK' => '意向表单',
        'OPTIMIZATIONGOAL_OPEN_ACCOUNT' => '券商开户',
        'OPTIMIZATIONGOAL_SEVEN_DAY_ECOMMERCE_ORDER' => '7 日下单',
        'AD_CONVERT_TYPE_ACIONT_BAR' => '行为数（ actionBar 点击）',
        'AD_CONVERT_TYPE_PHONE_CAR_ACTIVE' => '电话卡激活',
        'AD_CONVERT_TYPE_MEASURE_ROOM' => '量房',
        'AD_CONVERT_TYPE_AROUSAL_APP' => '唤起应用',
        'AD_CONVERT_TYPE_ORDER_SUBMIT' => '订单提交',
        'AD_CONVERT_TYPE_WECHAT_COPY_GOAL' => '微信复制优化目标',
        'AD_CONVERT_TYPE_VIEW_TIMES' => '广告观看次数',
        'AD_CONVERT_TYPE_VIEW_TIMES_FIVE' => '广告观看 5 次',
        'AD_CONVERT_TYPE_VIEW_TIMES_TEN' => '广告观看 10 次',
        'AD_CONVERT_TYPE_VIEW_TIMES_TWENTY' => '广告观看 20 次',
        'AD_CONVERT_TYPE_SEVEN_DAY_VIEW_TIMES' => '7 日付费次数',
        'AD_CONVERT_TYPE_ORDER_PAY' => '小店通商品和主页推广，转化目标“订单支付”，62',
        'AD_CONVERT_TYPE_AWEME_ROI' => '直播推广 ROI',
        'AD_CONVERT_TYPE_NOTIFY_FORM_CLICK_ACTION' => '预约点击跳转',
        'AD_CONVERT_TYPE_ADVICE_CLICK_ACTION' => '咨询按钮点击',
        'AD_CONVERT_TYPE_PHONE_CALL_CLICK_ACTION' => '电话按钮点击',
        'AD_CONVERT_TYPE_NOTIFY_FORM_SUBMIT' => '表单提交成功',
        'AD_CONVERT_TYPE_ADVICE_THREE_WORDS' => '三句话咨询',
        'AD_CONVERT_TYPE_ADVICE_ONE_WORD' => '一句话咨询',
        'AD_CONVERT_TYPE_APPLICATION' => '申请（小流量）',
        'AD_CONVERT_TYPE_LOGIN' => '登录（注册激活后登录）',
        'AD_CONVERT_TYPE_WECHAT_AROUSAL_ACTION' => '微信调起按钮点击',
        'AD_CONVERT_TYPE_TALK_ABOUT_BUSINESS' => '聊到相关业务（小流量）',
        'AD_CONVERT_TYPE_TO_STORE' => '到店（小流量）',
        'AD_CONVERT_TYPE_DIALBACK_INTENTION_FOUND' => '回访-发现意向',
        'AD_CONVERT_TYPE_DIALBACK_ODERED_CUSTOMER' => '回访-成单客户',
        'AD_CONVERT_TYPE_PAY_TO_WATCH_SHOW' => '付费观剧（小流量）',
    ];

    // 深度转化类型（头条/快手/腾讯/百度）
    const DEEP_BID_TYPE = [
        'DEEP_BID_DEFAULT' => '不启用，无深度优化',
        'DEEP_BID_PACING' => '自动优化（手动出价方式下）',
        'DEEP_BID_MIN' => '自定义双出价（手动出价方式下）',
        'SMARTBID' => '自动优化（自动出价方式下）',
        'AUTO_MIN_SECOND_STAGE' => '自定义双出价（自动出价方式下）',
        'ROI_COEFFICIENT' => 'ROI系数',
        'ROI_PACING' => 'ROI系数——自动优化',
        'ROI_DIRECT_MAIL' => 'ROI直投',
        'MIN_SECOND_STAGE' => '两阶段优化',
        'PACING_SECOND_STAGE' => '动态两阶段',
        'BID_PER_ACTION' => '每次付费出价',
        'SOCIAL_ROI' => 'ROI三出价',
        'DEEP_BID_TYPE_RETENTION_DAYS' => '留存天数',
        'GOAL_30DAY_ORDER_ROAS' => '下单 ROI',
        'GOAL_1DAY_PURCHASE_ROAS' => '首日付费 ROI',
        'GOAL_1DAY_MONETIZATION_ROAS' => '首日变现 ROI',
        'GOAL_7DAY_RETENTION_TIMES' => '七日内留存天数',
        'GOAL_7DAY_LONGTERM_PURCHASE_ROAS' => '七日长效付费',
        'GOAL_14DAY_LONGTERM_PURCHASE_ROAS' => '十四日长效付费',
        'GOAL_30DAY_LONGTERM_PURCHASE_ROAS' => '三十日长效付费',
        'DEEP_BID_TYPE_PAY' => '付费',
        'DEEP_BID_TYPE_NEXT_DAY_OPEN' => '次日留存',
        'DEEP_BID_TYPE_LOAN_COMPLETION' => '完件',
        'DEEP_BID_TYPE_LOAN_CREDIT' => '授信',
        'DEEP_BID_TYPE_ECOMMERCE_CART' => '添加购物车',
        'DEEP_BID_TYPE_ORDER_SUBMIT' => '提交订单',
        'DEEP_BID_TYPE_SHOPPING' => '购买',
        'DEEP_BID_TYPE_PAGE_CONFIRM_EFFECTIVE_LEADS' => '有效线索',
        'DEEP_BID_TYPE_REGISTER_TWENTY_FOUR_HOUR_NEXT_DAY_OPEN' => '激活后24H次日留存',
        'DEEP_BID_TYPE_REGISTER' => '注册（小流量）',
        'DEEP_BID_TYPE_CUSTOM' => '客户自定义（小流量）',
        'DEEP_BID_TYPE_CREDIT' => '授信（小流量）',
        'DEEP_BID_TYPE_ECOMMERCE_ORDER' => '商品下单成功',
        'DEEP_BID_TYPE_PURCHASE_OF_GOODS' => '收货成功',
        'DEEP_BID_TYPE_TALK_ABOUT_BUSINESS' => '聊到相关业务（小流量）',
        'DEEP_BID_TYPE_DIALBACK_CONNECT' => '回访-电话接通（小流量）',
        'DEEP_BID_TYPE_TO_STORE' => '到店（小流量）',
        'DEEP_BID_TYPE_CLUE_CONFIRM' => '回访-信息确认（小流量）',
        'DEEP_BID_TYPE_DIALBACK_INTENTION_FOUND' => '回访-发现意向（小流量）',
        'DEEP_BID_TYPE_CLUE_HIGH_INTENTION' => '回访-高潜成交（小流量）',
        'DEEP_BID_TYPE_DIALBACK_ODERED_CUSTOMER' => '回访-成单客户（小流量）',
        'DEEP_BID_TYPE_WECHAT_WECOM_ADD' => '微信加粉成功（小流量）'
    ];

    //广点通二级status
    const GDT_ADGROUP_STATUS = [
        'STATUS_UNKNOWN' => '未知状态',
        'STATUS_PENDING' => '审核中',
        'STATUS_DENIED' => '审核不通过',
        'STATUS_FROZEN' => '冻结',
        'STATUS_SUSPEND' => '暂停中',
        'STATUS_READY' => '未到投放时间',
        'STATUS_ACTIVE' => '投放中',
        'STATUS_STOP' => '投放结束',
        'STATUS_PREPARE' => '准备中',
        'STATUS_DELETED' => '已删除',
        'STATUS_ACTIVE_ACCOUNT_FROZEN' => '广告被暂停（账户资金被冻结）',
        'STATUS_ACTIVE_ACCOUNT_EMPTY' => '广告被暂停（账户余额不足）',
        'STATUS_ACTIVE_ACCOUNT_LIMIT' => '广告被暂停（账户达日限额）',
        'STATUS_ACTIVE_CAMPAIGN_LIMIT' => '广告被暂停（推广计划达日限额）',
        'STATUS_ACTIVE_CAMPAIGN_SUSPEND' => '广告被暂停（推广计划暂停）',
        'STATUS_ACTIVE_AD_LIMIT' => '广告被暂停（广告达日限额）',
        'STATUS_PART_READY' => '部分待投放',
        'STATUS_PART_ACTIVE' => '部分投放中'
    ];

    // 广点通场景类型
    const GDT_SMART_DELIVERY_PLATFORM = [
        'SMART_DELIVERY_PLATFORM_EDITION_MINI_GAME_PROMOTION' => '小游戏跑量',
        'SMART_DELIVERY_PLATFORM_EDITION_MINI_GAME_MIXED_MONETIZATION' => '小游戏混变',
        'SMART_DELIVERY_PLATFORM_EDITION_GAME_PROMOTION' => '游戏大推',
        'SMART_DELIVERY_PLATFORM_EDITION_IAAP' => '小游戏混变IAAP',
        '' => '无',
    ];

    // 广点通跑量加强状态
    const GDT_ECOM_PKAM_SWITCH = [
        'ECOM_PKAM_SWITCH_CLOSE' => '关闭',
        'ECOM_PKAM_SWITCH_OPEN' => '开启',
        '' => '空',
    ];

    //广点通出价场景
    const GDT_BID_SCENE = [
        'BID_SCENE_UNKNOWN' => '默认值',
        'BID_SCENE_NORMAL_AVERAGE' => '常规投放-稳定投放',
        'BID_SCENE_NORMAL_TARGET' => '常规投放-放量投放',
        'BID_SCENE_NORMAL_MAX' => '优先最大转化'
    ];

    const YES_OR_NO = [
        0 => '否',
        1 => '是'
    ];

    const ON_OR_OFF = [
        0 => '关闭',
        1 => '开启'
    ];

    const TENCENT_COMPONENT_TYPE = [
        'IMAGE_LIST' => '组图',
        'IMAGE' => '图片',
        'VIDEO' => '视频'
    ];

    const BAIDU_TRANS_TYPE = [
        1 => "咨询按钮点击",
        2 => "电话按钮点击",
        3 => "表单提交成功",
        4 => "激活",
        5 => "表单按钮点击",
        6 => "下载（预约）按钮点击（小流量）",
        10 => "购买成功",
        14 => "订单提交成功",
        17 => "三句话咨询",
        18 => "留线索",
        19 => "一句话咨询",
        20 => "关键页面浏览",
        25 => "注册（小流量）",
        26 => "付费（小流量）",
        30 => "电话拨通",
        35 => "微信复制按钮点击（小流量）",
        41 => "申请（小流量）",
        42 => "授信（小流量）",
        45 => "商品下单成功",
        46 => "加入购物车",
        47 => "商品收藏",
        48 => "商品详情页到达",
        49 => "登录（注册激活后登录）",
        56 => "到店（小流量）",
        57 => "店铺调起",
        67 => "微信调起按钮点击",
        68 => "粉丝关注成功",
        71 => "应用调起",
        72 => "聊到相关业务（小流量）",
        73 => "回访-电话接通（小流量）",
        74 => "回访-信息确认（小流量）",
        75 => "回访-发现意向（小流量）",
        76 => "回访-高潜成交（小流量）",
        77 => "回访-成单客户（小流量）",
        79 => "微信加粉成功（小流量）",
        80 => "直播间成单（小流量）",
        82 => "直播间观看（小流量）",
        83 => "直播间商品按钮点击（小流量）",
        84 => "直播间停留（小流量）",
        85 => "直播间评论（小流量）",
        86 => "直播间打赏（小流量）",
        87 => "直播间购物袋点击（小流量）",
        90 => "商品支付成功",
        93 => "付费阅读（小流量）",
        118 => "付费观剧（小流量）",
        119 => "关键行为（小流量）"
    ];

    const BAIDU_DEEP_TRANS_TYPE = [
        10 => '购买成功',
        18 => '留线索（小流量）',
        25 => '注册（小流量）',
        26 => '付费（小流量）',
        27 => '客户自定义（小流量）',
        28 => '次日留存（小流量）',
        42 => '授信（小流量）',
        45 => '商品下单成功',
        53 => '订单核对成功',
        54 => '收货成功',
        56 => '到店（小流量）',
        72 => '聊到相关业务（小流量）',
        73 => '回访-电话接通（小流量）',
        74 => '回访-信息确认（小流量）',
        75 => '回访-发现意向（小流量）',
        76 => '回访-高潜成交（小流量）',
        77 => '回访-成单客户（小流量）',
        79 => '微信加粉成功（小流量）',
    ];

    //头条起量状态操作名称映射
    const TT_RAISE_STATUS_OPT = [
        'ENABLE_RAISE' => '开启',
        'DISABLE_RAISE' => '不可用',
        'RAISING' => '进行中',
        'FINISH_RAISE' => '开启',
        'ENABLE_PRERAISE' => '可预约',
        'HAS_PRERAISE' => '已预约'
    ];

    //头条起量状态
    const TT_RAISE_STATUS = [
        'ENABLE_RAISE' => '可以使用一键起量',
        'DISABLE_RAISE' => '不可以使用一键起量',
        'RAISING' => '一键起量中',
        'FINISH_RAISE' => '一键起量完成',
        'ENABLE_PRERAISE' => '可预约',
        'HAS_PRERAISE' => '已预约'
    ];

    //腾讯一键起量状态
    const GDT_AUTO_ACQUISITION_STATUS = [
        'AUTO_ACQUISTION_STATUS_UNKNOW' => '未开启一键起量功能',
        'AUTO_ACQUISTION_STATUS_PENDING' => '开启一键起量，探索中',
        'AUTO_ACQUISTION_STATUS_END_LESS_THAN_24H' => '起量完成(探索结束，预算花完，功能开启后未满 24h)',
        'AUTO_ACQUISTION_STATUS_END_MORE_THAN_24H' => '起量完成(探索结束，预算花完，功能开启后已满 24h)',
        'AUTO_ACQUISTION_STATUS_COMPLETED' => '起量结束(探索结束，距离广告开启已满 6h，但预算未花完（实际花费＜起量预算*90%）)',
        'AUTO_ACQUISTION_STATUS_SUSPEND_ON_LEARNING_FAIL' => '起量中止(探索过程中，因广告起量情况太差，从而探索中止)',
        'AUTO_ACQUISTION_STATUS_SUSPEND_ON_PLAYING_FAIL' => '起量中止(探索过程中，因广告无法播放，从而起量中止（包括广告主动或被动下线或 timeset 不连续）)',
        'AUTO_ACQUISTION_STATUS_ADVERTISER_CLOSED' => '广告主主动关闭一键起量功能'
    ];

    //头条清理素材类型
    const TOUTIAO_CLEAR_MATERIAL_TYPES = [
        'INEFFICIENT_MATERIAL' => '低效素材',
        'SIMILAR_MATERIAL' => '同质化挤压严重素材',
        'SIMILAR_QUEUE_MATERIAL' => '同质化排队素材'
    ];

    // 星图主播价格类型
    const TOUTIAO_STAR_LIVE_PRICE_TYPE = [
        1 => '小时',
        2 => '场次',
//        3 => '包天',
//        4 => '包月',
        5 => 'CPS',
        6 => 'CPA',
        7 => '短视频',
        8 => '维护玩家付费',
    ];

    // 星图达人类型
    const TOUTIAO_STAR_AUTHOR_TYPE = [
        0 => '普通号',
        1 => '游点蓝V',
        2 => '贪玩蓝V',
        3 => '员工号',
    ];

    const TOUTIAO_STAR_PAY_WAY = [
        1 => '对公',
        2 => '个人',
    ];

    // 星图订单类型
    const TOUTIAO_STAR_DSP_ORDER_TYPE = [
        1 => '媒体',
        2 => '内部',
    ];

    // 星图直播间类型
    const TOUTIAO_STAR_LIVE_MODE_TYPE = [
        1 => '线上',
        2 => '线下',
    ];

    // 星图视频类型
    const TOUTIAO_STAR_VIDEO_TYPE = [
        9 => '直播按小时',
        56 => '直播按天',
        1 => '视频1~20s',
        2 => '视频21-60s',
        71 => '视频60s+',
    ];
}
