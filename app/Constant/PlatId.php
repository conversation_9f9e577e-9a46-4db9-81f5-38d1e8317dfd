<?php

namespace App\Constant;


use App\Exception\AppException;

/**
 * 常量的命名规则：只要是小游戏类的 plat_id,一律需要添加 "MINI"。便于其他地方使用。
 * 例如：DY_MINI、QQ_MINI。
 */
class PlatId
{
    const YY = 1;       // 页游
    const SY = 2;       // 手游
    const H5 = 3;       // H5
    const MINI = 7;     // 微信小游戏
    const PC = 8;       // PC
    const KYX = 9;       // 快游戏
    const DY_MINI = 10;  // 抖音小游戏
    const MT_MINI = 11;  // 美团小游戏
    const KS_MINI = 12;  // 快手小游戏
    const QQ_MINI = 13;  // QQ小游戏
    const DY_MINI_PROGRAM = 14;  // 字节小程序
    const ALIPAY_MINI = 15;  // 支付宝游戏类型
    const HUAWEI_MINI = 17;  // 华为小游戏
    const BAIDU_MINI = 18;  // 百度小游戏

    // 映射
    const MAP = [
        self::YY              => '页游',
        self::SY              => '手游',
        self::H5              => 'H5',
        self::PC              => 'PC端游',
        self::MINI            => '小游戏',
        self::KYX             => '快游戏',
        self::DY_MINI         => '抖音小游戏',
        self::MT_MINI         => '美团小游戏',
        self::KS_MINI         => '快手小游戏',
        self::QQ_MINI         => 'QQ小游戏',
        self::DY_MINI_PROGRAM => '字节小程序',
        self::ALIPAY_MINI     => '支付宝小游戏',
        self::HUAWEI_MINI     => '华为小游戏',
        self::BAIDU_MINI     => '百度小游戏',
    ];


    /**
     * 返回 sql需要 in的数组，例如 (7,10,11,12,13,14,15)
     *
     * @return string
     */
    public static function getMiniPlatIDSql()
    {
        $mini_plat_ids = self::getMiniPlatIDArray();
        return "(" . implode(',', $mini_plat_ids) . ")";
    }


    /**
     * 返回名字带有mini的PlatID
     * 也就是小游戏类型
     *
     * @return array
     */
    public static function getMiniPlatIDArray()
    {
        // 使用 get_called_class() 来获取当前调用的类名，以支持多态。
        $reflection = new \ReflectionClass(get_called_class());
        $plat_ids = $reflection->getConstants();

        return array_filter($plat_ids, function ($key) {
            return strpos($key, 'MINI') !== false;
        }, ARRAY_FILTER_USE_KEY);

    }

    /**
     * plat_id 到权限全选的 type之间的映射
     * const TYPE_YY = 5; // 页游所有权限
     * const TYPE_SY = 6; // 手游所有权限
     * const TYPE_H5 = 7; // H5所有权限
     * const TYPE_MINI = 9; // 小游戏所有权限
     * 以上是rank_permission_all表原来的 type，为了兼容旧数据，
     * 做现在的map方法。除了小游戏，其实都可以直接+4
     *
     * @param $plat_id
     * @return integer   返回的是rank_permission_all的 type
     */
    public static function platIDtoAllTypeMap($plat_id)
    {
        if ($plat_id === self::MINI) {
            return 9;
        }
        // 做一个判断 防止踩坑 理论上没有这几个plat_id，就怕后面加了但是这个方法忘记了就踩大坑了
        if (in_array($plat_id, [4, 5, 6], true)) {
            throw new AppException('plat_id配置错误');
        }

        return $plat_id + 4;
    }

    /**
     *
     * 权限全选的type到plat_id的映射
     * const TYPE_YY = 5; // 页游所有权限
     * const TYPE_SY = 6; // 手游所有权限
     * const TYPE_H5 = 7; // H5所有权限
     * const TYPE_MINI = 9; // 小游戏所有权限
     * 以上是rank_permission_all表原来的 type，为了兼容旧数据，
     * 做现在的map方法。除了小游戏，其实都可以直接+4
     *
     * @param $type
     * @return integer   返回的是rank_permission_all的 type
     */
    public static function allTypeToPlatId($type)
    {
        $type = intval($type);
        if ($type === 9) {
            return self::MINI;
        }

        $plat_id = $type - 4;

        if (!isset(self::MAP[$plat_id])) {
            throw new AppException('all permission type 配置错误');
        }

        return $plat_id;
    }
}

