<?php
/**
 * Clickhouse用到的数据配置
 */

namespace App\Constant;

class ClickhouseDataConfig
{
    /**
     * 这几个id都需要查name
     */
    public const ID_TO_NAME = [
        'game_id', 'main_game_id', 'root_game_id', 'agent_id', 'agent_group_id', 'site_id', 'account_id',
        'channel_id', 'csite', 'campaign_id', 'adgroup_id', 'adcre_id', 'clique_id', 'aweme_account', 'apple_ppid'];


    public const PLAT_DIMENSION = [
        'account_id', 'campaign_id', 'adgroup_id', 'adcre_id', 'csite', 'ctype', 'create_type',
        'port_version', 'aweme_account', 'interface_person_group_name', 'interface_person', 'is_mix_os',
        'company_name', 'ods_company_name', 'convert_type', 'deep_bid_type', 'aweme_account_type',
    ];
//    public const PLAT_DIMENSION = ['account_id', 'campaign_id', 'adgroup_id', 'adcre_id', 'csite', 'ctype', 'create_type', 'port_version', 'is_mix_os', 'convert_type', 'deep_bid_type'];

    // 维度当索引。 这里需要结合 NO_DIMENSION_TABLE 一起添加
    public const NO_DIMENSION = [
        'is_simulator' => [
            'table' => [
                'dws_day_click_log_rmt_all',
                'dwd_day_cost_log_rmt_all',
                'dwd_hour_cost_log_rmt_all',
                'dws_hour_click_log_rmt_all',
                'dws_hour_root_game_device_action_log_rmt_all',
                'dwd_media_ad2_common_day_data_log_rmt_all',
                'dws_day_show_log_rmt_all',
            ],
            'type'  => 'int',
        ],

        'is_old_clique_game_muid' => [
            'table' => ['dwd_day_cost_log_rmt_all', 'dwd_hour_cost_log_rmt_all'],
            'type'  => 'int',
        ],
        'is_old_clique_pay_muid'  => [
            'table' => ['dwd_day_cost_log_rmt_all', 'dwd_hour_cost_log_rmt_all'],
            'type'  => 'int',
        ],
        'is_old_root_game_muid'   => [
            'table' => ['dwd_day_cost_log_rmt_all', 'dwd_hour_cost_log_rmt_all'],
            'type'  => 'int',
        ],
        'is_old_root_pay_muid'    => [
            'table' => ['dwd_day_cost_log_rmt_all', 'dwd_hour_cost_log_rmt_all'],
            'type'  => 'int',
        ],
    ];

    // 表名当索引
    public const NO_DIMENSION_TABLE = [
        'dws_day_click_log_rmt_all'                    => ['is_simulator' => 'int'],
        'dwd_media_ad2_common_day_data_log_rmt_all'    => ['is_simulator' => 'int'],
        'dwd_day_cost_log_rmt_all'                     => ['is_simulator' => 'int'],
        'dwd_hour_cost_log_rmt_all'                    => ['is_simulator' => 'int'],
        'dws_hour_click_log_rmt_all'                   => ['is_simulator' => 'int'],
        'dws_hour_root_game_device_action_log_rmt_all' => ['is_simulator' => 'int'],
    ];

    // 数据表的聚合字段
    // 请不要改动这个数组组合值，只可以新增！！！
    // 表名=>[聚合日期字段的名称与类型]
    // 若这里的表名相同，但聚合字段不一样，可以在handlerAggregation时传入对应字段即可
    public const TABLE_AGGREGATION_DATE_COLUMN = [
        'dws_day_click_log_rmt_all'                         => ['name' => 'click_date', 'type' => 'date'],
        'dws_day_show_log_rmt_all'                          => ['name' => 'show_date', 'type' => 'date'],
        'dwd_root_game_device_action_log_rmt_all'           => ['name' => 'action_time', 'type' => 'datetime'],
        'dws_day_root_game_device_action_log_rmt_all'       => ['name' => 'action_date', 'type' => 'date'],
        'dwd_root_game_uid_reg_log_rmt_all'                 => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_day_cost_log_rmt_all'                          => ['name' => 'tdate', 'type' => 'date'],
        'dwd_hour_cost_log_rmt_all'                         => ['name' => 'tdate', 'type' => 'date'],
        'dwd_day_root_game_uid_login_log_rmt_all'           => ['name' => 'login_time', 'type' => 'datetime'],
        'ods_root_game_uid_pay_order_log_rmt_all'           => ['name' => 'pay_time', 'type' => 'datetime'],
        'dwd_root_game_pay_order_log_rmt_all'               => ['name' => 'pay_time', 'type' => 'datetime'],
        'dws_day_root_game_pay_log_rmt_all'                 => ['name' => 'pay_date', 'type' => 'date'],
        'dwd_root_game_uid_pay_money_log_rmt_all'           => ['name' => 'root_game_reg_time', 'type' => 'datetime'],

        // 回流表
        'dwd_day_root_game_back_uid_login_log_rmt_all'      => ['name' => 'login_time', 'type' => 'datetime'],
        'dwd_root_game_back_device_action_log_rmt_all'      => ['name' => 'action_time', 'type' => 'datetime'],
        'dwd_root_game_back_pay_order_log_rmt_all'          => ['name' => 'pay_time', 'type' => 'datetime'],
        'dwd_root_game_back_uid_reg_log_rmt_all'            => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dws_day_root_game_back_device_action_log_rmt_all'  => ['name' => 'action_date', 'type' => 'date'],
        'dws_day_root_game_back_pay_log_rmt_all'            => ['name' => 'pay_date', 'type' => 'date'],
        'dws_hour_root_game_back_device_action_log_rmt_all' => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_back_pay_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_back_reg_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],

        'ods_device_action_log_rmt_all'     => ['name' => 'action_time', 'type' => 'datetime'],
        'dws_day_device_action_log_rmt_all' => ['name' => 'action_date', 'type' => 'date'],
        'dwd_game_uid_reg_log_rmt_all'      => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dwd_day_uid_login_log_rmt_all'     => ['name' => 'login_time', 'type' => 'datetime'],
        'ods_game_pay_order_log_rmt_all'    => ['name' => 'pay_time', 'type' => 'datetime'],
        'dwd_game_pay_order_log_rmt_all'    => ['name' => 'pay_time', 'type' => 'datetime'],
        'dws_day_pay_log_rmt_all'           => ['name' => 'pay_date', 'type' => 'date'],

        'dws_hour_root_game_pay_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_pay_log_rmt_all'                     => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'ods_ad_inspire_cost_log_rmt_all'              => ['name' => "stat_date", 'type' => "date"],

        // 运营总览-区服
        'dwd_role_create_log_rmt_all'                  => ['name' => 'create_time', 'type' => 'datetime'],
        'dwd_day_role_login_log_rmt_all'               => ['name' => 'login_date', 'type' => 'date'],
        'dws_day_role_pay_log_rmt_all'                 => ['name' => 'pay_date', 'type' => 'date'],

        // 分时总览
        'dws_hour_click_log_rmt_all'                   => ['name' => 'click_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_device_action_log_rmt_all' => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_reg_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_device_action_log_rmt_all'           => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_game_reg_log_rmt_all'                => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],

        // 小游戏消耗
        'dwd_media_ad2_common_day_data_log_rmt_all'    => ['name' => 'cost_date', 'type' => 'date'],


        'dwd_media_ad3_common_hour_data_log_rmt_all' => ['name' => 'cost_date_hour', 'type' => 'datetime'],
    ];

    // 只有main_game_id字段的表，没game_id
    public const TABLE_MAIN_GAME_ID_COLUMN = [
        'dws_day_role_pay_log_rmt_all',
        'dws_day_role_login_log_rmt_all',
    ];

    // 没有game_id字段的数据表
    public const NO_GAME_ID_COLUMN_TABLE = [
        'dws_day_role_pay_log_rmt_all',
        'dws_day_role_login_log_rmt_all',
    ];

    // 没有agent_id字段的数据表
    public const NO_AGENT_ID_COLUMN_TABLE = [
    ];

    // 最终group by时转化字符串的字段
    public const TO_STRING_COLUMN = [
        'is_simulator',
        'is_old_clique_game_muid',
        'is_old_clique_pay_muid',
        'is_old_root_game_muid',
        'is_old_root_pay_muid',
    ];

    // 回流表
    const BACK_TABLE = [
        'dwd_day_root_game_back_uid_login_log_rmt_all',
        'dwd_root_game_back_device_action_log_rmt_all',
        'dwd_root_game_back_pay_order_log_rmt_all',
        'dwd_root_game_back_uid_reg_log_rmt_all',
        'dws_day_root_game_back_device_action_log_rmt_all',
        'dws_day_root_game_back_login_log_rmt_all',
        'dws_day_root_game_back_pay_log_rmt_all',
        'dws_day_root_game_back_pay_log_rmt_all',
        'dws_hour_root_game_back_device_action_log_rmt_all',
        'dws_hour_root_game_back_pay_log_rmt_all',
        'dws_hour_root_game_back_reg_log_rmt_all',
    ];
}
