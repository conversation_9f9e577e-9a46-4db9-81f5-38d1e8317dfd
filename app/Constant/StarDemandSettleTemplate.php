<?php

namespace App\Constant;

class StarDemandSettleTemplate
{
    public function getSettlementTemplate($special_column_name)
    {
        return '
        <!DOCTYPE html>
        <html
            xmlns:o="urn:schemas-microsoft-com:office:office"
            xmlns:x="urn:schemas-microsoft-com:office:excel"
        >
        <head>
            <meta charset="UTF-8">
            <title>结算单</title>
            <!--[if gte mso 9]>
            <xml>
                <x:ExcelWorkbook>
                    <x:ExcelWorksheets>
                        <x:ExcelWorksheet>
                            <x:Name></x:Name>
                            <x:WorksheetOptions>
                                <x:DisplayGridlines/>
                            </x:WorksheetOptions>
                        </x:ExcelWorksheet>
                    </x:ExcelWorksheets>
                </x:ExcelWorkbook>
            </xml><![endif]-->
            <style>
                table {border-collapse: collapse; width: 100%;}
                th, td {padding: 8px; text-align: left; height: 50px}
                .header-row th{text-align: center; background-color: #ffff03;}
                .merged-cell {text-align: center; font-weight: bold; font-size: 34px;}
                .align-center th, .align-center td {text-align: center;}
            </style>
        </head>
        <body>
        <table border="1" style="border: 2px solid #000; width 100%;">
            <!-- 表头部分 -->
            <tr>
                <td colspan="15" class="merged-cell">{{$live_media_type}}-信息服务执行单-{{$date}}-{{$price_type}}</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">结算标识：{{$trade_no}}</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">付款单位：{{$pay_company_name}}</td>
            </tr>
            <!--<tr>
                <td colspan="15" style="height: 30px">发票类型： 增值税专用发票█&nbsp;&nbsp;增值税普通发票□&nbsp;&nbsp;无□</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">开票内容： 信息服务费█&nbsp;&nbsp;广告费□&nbsp;&nbsp;技术服务费□&nbsp;&nbsp;信息费□</td>
            </tr>-->
            <tr>
                <td colspan="15" style="height: 30px">结算方式：预付款□&nbsp;&nbsp;月结█&nbsp;&nbsp;半月结□&nbsp;&nbsp;周结□&nbsp;&nbsp;其他：（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">主播收款方式：{{$pay_way}}</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">是否承担开票税：{{$com_is_extra}}</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">对方开票税：（{{$company_tax}}）</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">承担云账户打款税点：（{{$cloud_account_tax_author_cover_rate}}）</td>
            </tr>
            <tr>
                <td colspan="15" style="height: 30px">订单类型：{{$dsp_order_type}}</td>
            </tr>
        
            <!-- 数据表头 -->
            <tr class="header-row">
                <th width="150">开播日期</th>
                <th width="150">主播名</th>
                <th width="150">直播账号</th>
                <th width="150">直播间ID</th>
                <th width="100">合同游戏</th>
                <th width="100">是否承担星图提现手续费</th>
                <th width="100">计算工资的直播时长</th>
                <th width="100">单价/价格</th>
                <th width="100">价格类型</th>
                <th width="100">星图下单金额</th>
                <th width="100">主播提现金额</th>
                <th width="100">'. $special_column_name .'</th>
                <th width="100">应返款金额/应补款金额</th>
                <th width="100">实际到账金额/需补款金额</th>
                <th width="100">备注(加班/扣罚理由)</th>
            </tr>
        
            <!-- 数据行 -->
            <!-- 示例数据行（可根据实际数据扩展） -->
            {{$data_list}}
            <!-- 更多数据行... -->
        
            <!-- 合计行 -->
            <tr class="align-center">
                <td colspan="8">合计</td>
                <td></td>
                <td>{{$amount_total}}</td>
                <td>{{$anchor_order_fee_total}}</td>
                <td>{{$anchor_cost_total}}</td>
                <td>{{$should_amount_total}}</td>
                <td>{{$actual_amount_total}}</td>
                <td></td>
            </tr>
        
            <!-- 大写人民币 -->
            <tr class="align-center">
                <td colspan="8">大写人民币</td>
                <td></td>
                <td>{{$amount_total_upper}}</td>
                <td>{{$anchor_order_fee_total_upper}}</td>
                <td>{{$anchor_cost_total_upper}}</td>
                <td>{{$should_amount_total_upper}}</td>
                <td>{{$actual_amount_total_upper}}</td>
                <td></td>
            </tr>
        
            <!-- 付款方/收款方信息 -->
            <tr>
                <td>付款方：</td>
                {{$pay_company}}
                <!--<td colspan="7">{{$pay_company}}</td>-->
                <td>收款方：</td>
                {{$payee_company}}
                <!--<td colspan="6">{{$payee_company}}</td>-->
            </tr>
            <tr>
                <td>地址：</td>
                {{$pay_address}}
                <!--<td colspan="7">{{$pay_address}}</td>-->
                <td>地址：</td>
                {{$payee_address}}
                <!--<td colspan="6">{{$payee_address}}</td>-->
            </tr>
            <tr>
                <td>开户银行：</td>
                {{$pay_bank}}
                <!--<td colspan="7">{{$pay_bank}}</td>-->
                <td>开户银行：</td>
                {{$payee_bank}}
                <!--<td colspan="6">{{$payee_bank}}</td>-->
            </tr>
            <tr>
                <td>银行账号：</td>
                {{$pay_bank_account}}
                <!--<td colspan="7" style="vnd.ms-excel.numberformat:@">{{$pay_bank_account}}</td>-->
                <td>银行账号：</td>
                {{$payee_bank_account}}
                <!--<td colspan="6" style="vnd.ms-excel.numberformat:@">{{$payee_bank_account}}</td>-->
            </tr>
            <tr>
                <td>实际金额：</td>
                <!--{{$pay_operator}}-->
                <td colspan="7">{{$actual_amount_total}}</td>
                <td>实际金额：</td>
                <!--{{$payee_operator}}-->
                <td colspan="6">{{$actual_amount_total_upper}}</td>
            </tr>
            <!--<tr>
                <td>身份证号：</td>
                {{$pay_id_card}}
                <td>身份证号：</td>
                {{$payee_id_card}}
            </tr>
            <tr>
                <td>手机号：</td>
                {{$pay_telephone}}
                <td>手机号：</td>
                {{$payee_telephone}}
            </tr>-->
            <tr>
                <td>业务经办人：</td>
                {{$pay_operator}}
                <!--<td colspan="7">{{$pay_operator}}</td>-->
                <td>业务经办人：</td>
                {{$payee_operator}}
                <!--<td colspan="6">{{$payee_operator}}</td>-->
            </tr>
            <!--<tr>
                <td>签字：</td>
                <td colspan="7"></td>
                <td>签字：</td>
                <td colspan="6"></td>
            </tr>-->
            <tr>
                <td>签章：</td>
                <td colspan="7"></td>
                <td>签章：</td>
                <td colspan="6"></td>
            </tr>
        </table>
        </body>
        </html>
    ';
    }

    /**
     * 游点专用结算单模板
     * @param $special_column_name
     * @return string
     */
    public function getYoudianSettlementTemplate($special_column_name)
    {
        return '
        <!DOCTYPE html>
        <html
            xmlns:o="urn:schemas-microsoft-com:office:office"
            xmlns:x="urn:schemas-microsoft-com:office:excel"
        >
        <head>
            <meta charset="UTF-8">
            <title>结算单</title>
            <!--[if gte mso 9]>
            <xml>
                <x:ExcelWorkbook>
                    <x:ExcelWorksheets>
                        <x:ExcelWorksheet>
                            <x:Name></x:Name>
                            <x:WorksheetOptions>
                                <x:DisplayGridlines/>
                            </x:WorksheetOptions>
                        </x:ExcelWorksheet>
                    </x:ExcelWorksheets>
                </x:ExcelWorkbook>
            </xml><![endif]-->
            <style>
                table {border-collapse: collapse; width: 100%;}
                th, td {padding: 8px; text-align: left; height: 50px}
                .header-row th{text-align: center; background-color: #ffff03;}
                .merged-cell {text-align: center; font-weight: bold; font-size: 34px;}
                .align-center th, .align-center td {text-align: center;}
            </style>
        </head>
        <body>
        <table border="1" style="border: 2px solid #000; width 100%">
            <!-- 表头部分 -->
            <tr>
                <td colspan="13" class="merged-cell">{{$live_media_type}}-信息服务执行单-{{$date}}-{{$price_type}}</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">结算标识：{{$trade_no}}</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">付款单位：{{$pay_company_name}}</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">发票类型： 增值税专用发票█&nbsp;&nbsp;增值税普通发票□&nbsp;&nbsp;无□</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">开票内容： 信息服务费█&nbsp;&nbsp;技术服务费□&nbsp;&nbsp;信息费□</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">结算方式：预付款□&nbsp;&nbsp;月结█&nbsp;&nbsp;半月结□&nbsp;&nbsp;周结□&nbsp;&nbsp;其他：（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">主播收款方式：{{$pay_way}}</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">是否承担开票税：{{$com_is_extra}}</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">对方开票税：（{{$company_tax}}）</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">承担云账户打款税点：（{{$cloud_account_tax_author_cover_rate}}）</td>
            </tr>
            <tr>
                <td colspan="13" style="height: 30px">订单类型：{{$dsp_order_type}}</td>
            </tr>
        
            <!-- 数据表头 -->
            <tr class="header-row">
                <th width="150">开播日期</th>
                <th width="150">主播名</th>
                <th width="150">直播账号</th>
                <th width="150">直播间ID</th>
                <th width="100">合同游戏</th>
                <th width="100">是否承担星图提现手续费</th>
                <th width="100">计算工资的直播时长</th>
                <th width="100">单价/价格</th>
                <th width="100">价格类型</th>
                <th width="100">星图下单金额</th>
                <th width="100">主播提现金额</th>
                <th width="100">游点下发工资</th>
                <th width="150">备注(加班/扣罚理由)</th>
            </tr>
        
            <!-- 数据行 -->
            <!-- 示例数据行（可根据实际数据扩展） -->
            {{$data_list}}
            <!-- 更多数据行... -->
        
            <!-- 合计行 -->
            <tr class="align-center">
                <td colspan="8">合计</td>
                <td></td>
                <td>{{$amount_total}}</td>
                <td>{{$anchor_order_fee_total}}</td>
                <td>{{$yd_company_fee_total}}</td>
                <td></td>
            </tr>
        
            <!-- 大写人民币 -->
            <tr class="align-center">
                <td colspan="8">大写人民币</td>
                <td></td>
                <td>{{$amount_total_upper}}</td>
                <td>{{$anchor_order_fee_total_upper}}</td>
                <td>{{$yd_company_fee_total_upper}}</td>
                <td></td>
            </tr>
        
            <!-- 付款方/收款方信息 -->
            <tr>
                <td>付款方：</td>
                {{$pay_company}}
                <!--<td colspan="7">{{$pay_company}}</td>-->
                <td>收款方：</td>
                {{$payee_company}}
                <!--<td colspan="6">{{$payee_company}}</td>-->
            </tr>
            <tr>
                <td>地址：</td>
                {{$pay_address}}
                <!--<td colspan="7">{{$pay_address}}</td>-->
                <td>地址：</td>
                {{$payee_address}}
                <!--<td colspan="6">{{$payee_address}}</td>-->
            </tr>
            <tr>
                <td>开户银行：</td>
                {{$pay_bank}}
                <!--<td colspan="7">{{$pay_bank}}</td>-->
                <td>开户银行：</td>
                {{$payee_bank}}
                <!--<td colspan="6">{{$payee_bank}}</td>-->
            </tr>
            <tr>
                <td>银行账号：</td>
                {{$pay_bank_account}}
                <!--<td colspan="7" style="vnd.ms-excel.numberformat:@">{{$pay_bank_account}}</td>-->
                <td>银行账号：</td>
                {{$payee_bank_account}}
                <!--<td colspan="6" style="vnd.ms-excel.numberformat:@">{{$payee_bank_account}}</td>-->
            </tr>
            <tr>
                <td>实际金额：</td>
                <td colspan="7">{{$yd_company_fee_total}}</td>
                <!--<td colspan="7">{{$pay_operator}}</td>-->
                <td>实际金额：</td>
                <td colspan="4">{{$yd_company_fee_total}}</td>
                <!--<td colspan="6">{{$payee_operator}}</td>-->
            </tr>
            <tr>
                <td>身份证号：</td>
                {{$pay_id_card}}
                <td>身份证号：</td>
                {{$payee_id_card}}
            </tr>
            <tr>
                <td>手机号：</td>
                {{$pay_telephone}}
                <td>手机号：</td>
                {{$payee_telephone}}
            </tr>
            <tr>
                <td>业务经办人：</td>
                {{$pay_operator}}
                <!--<td colspan="7">{{$pay_operator}}</td>-->
                <td>业务经办人：</td>
                {{$payee_operator}}
                <!--<td colspan="6">{{$payee_operator}}</td>-->
            </tr>
            <tr>
                <td>签字：</td>
                <td colspan="7"></td>
                <td>签字：</td>
                <td colspan="4"></td>
            </tr>
            <tr>
                <td>盖章：</td>
                <td colspan="7"></td>
                <td>盖章：</td>
                <td colspan="4"></td>
            </tr>
        </table>
        </body>
        </html>
    ';
    }

    /**
     * @param $pay_info
     * @param $payee_info
     * @param int $pay_colspan 付款方合并列数
     * @param int $payee_colspan 收款方合并列数
     * @return array
     */
    public function genPayAndPayeeInfo($pay_info, $payee_info, $pay_colspan = 7, $payee_colspan = 6)
    {
        $pay_company_name = [];
        $pay_company = $pay_address = $pay_bank = $pay_bank_account = $pay_operator = $pay_id_card = $pay_telephone = '';
        $payee_company = $payee_address = $payee_bank = $payee_bank_account = $payee_operator = $payee_id_card = $payee_telephone = '';
        $pay_info_num = count($pay_info);
        $payee_info_num = count($payee_info);
        foreach ($pay_info as $item) {
            if ($pay_info_num === 1) {
                $pay_company .= '<td colspan="' . $pay_colspan . '">' . $item['company'] . '</td>';
                $pay_address .= '<td colspan="' . $pay_colspan . '">' . $item['address'] . '</td>';
                $pay_bank .= '<td colspan="' . $pay_colspan . '">' . $item['bank'] . '</td>';
                $pay_bank_account .= '<td colspan="' . $pay_colspan . '" style="vnd.ms-excel.numberformat:@">' . $item['bank_card_no'] . '</td>';
                $pay_operator .= '<td colspan="' . $pay_colspan . '">' . '</td>';
                $pay_id_card .= '<td colspan="' . $pay_colspan . '" style="vnd.ms-excel.numberformat:@">' . ($item['id_card'] ?? '') . '</td>';
                $pay_telephone .= '<td colspan="' . $pay_colspan . '">' . ($item['telephone'] ?? '') . '</td>';
            } else {
                $pay_company .= '<td>' . $item['company'] . '</td>';
                $pay_address .= '<td>' . $item['address'] . '</td>';
                $pay_bank .= '<td>' . $item['bank'] . '</td>';
                $pay_bank_account .= '<td style="vnd.ms-excel.numberformat:@">' . $item['bank_card_no'] . '</td>';
                $pay_operator .= '<td>' . '</td>';
                $pay_id_card .= '<td style="vnd.ms-excel.numberformat:@">' . ($item['id_card'] ?? '') . '</td>';
                $pay_telephone .= '<td>' . ($item['telephone'] ?? '') . '</td>';
            }
            $pay_company_name[] = $item['company'];
        }

        if ($pay_info_num > 1) {
            // 补齐空的单元格
            for ($i = 0; $i < ($pay_colspan - $pay_info_num); $i++) {
                $pay_company .= '<td>' . '</td>';
                $pay_address .= '<td>' . '</td>';
                $pay_bank .= '<td>' . '</td>';
                $pay_bank_account .= '<td>' . '</td>';
                $pay_operator .= '<td>' . '</td>';
                $pay_id_card .= '<td>' . '</td>';
                $pay_telephone .= '<td>' . '</td>';
            }
        }

        foreach ($payee_info as $item) {
            if ($payee_info_num === 1) {
                $payee_company .= '<td colspan="' . $payee_colspan . '">' . $item['company'] . '</td>';
                $payee_address .= '<td colspan="' . $payee_colspan . '">' . $item['address'] . '</td>';
                $payee_bank .= '<td colspan="' . $payee_colspan . '">' . $item['bank'] . '</td>';
                $payee_bank_account .= '<td colspan="' . $payee_colspan . '" style="vnd.ms-excel.numberformat:@">' . $item['bank_card_no'] . '</td>';
                $payee_operator .= '<td colspan="' . $payee_colspan . '">' . '</td>';
                $payee_id_card .= '<td colspan="' . $payee_colspan . '" style="vnd.ms-excel.numberformat:@">' . ($item['id_card'] ?? '') . '</td>';
                $payee_telephone .= '<td colspan="' . $payee_colspan . '">' . ($item['telephone'] ?? '') . '</td>';
            } else {
                $payee_company .= '<td>' . $item['company'] . '</td>';
                $payee_address .= '<td>' . $item['address'] . '</td>';
                $payee_bank .= '<td>' . $item['bank'] . '</td>';
                $payee_bank_account .= '<td style="vnd.ms-excel.numberformat:@">' . $item['bank_card_no'] . '</td>';
                $payee_operator .= '<td>' . '</td>';
                $payee_id_card .= '<td style="vnd.ms-excel.numberformat:@">' . ($item['id_card'] ?? '') . '</td>';
                $payee_telephone .= '<td>' . ($item['telephone'] ?? '') . '</td>';
            }
        }

        if ($payee_info_num > 1) {
            // 补齐空的单元格
            for ($i = 0; $i < ($payee_colspan - $payee_info_num); $i++) {
                $payee_company .= '<td>' . '</td>';
                $payee_address .= '<td>' . '</td>';
                $payee_bank .= '<td>' . '</td>';
                $payee_bank_account .= '<td>' . '</td>';
                $payee_operator .= '<td>' . '</td>';
                $payee_id_card .= '<td>' . '</td>';
                $payee_telephone .= '<td>' . '</td>';
            }
        }

        $pay_company_name = implode(",", $pay_company_name);

        return [
            $pay_company_name,
            $pay_company, $pay_address, $pay_bank, $pay_bank_account, $pay_operator, $pay_id_card, $pay_telephone,
            $payee_company, $payee_address, $payee_bank, $payee_bank_account, $payee_operator, $payee_id_card, $payee_telephone
        ];
    }
}
