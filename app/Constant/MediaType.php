<?php

/**
 * auto generate file.
 * Created at: 2025-06-27 11:55:32
 * auto generate
 */

namespace App\Constant;

class MediaType
{

	const TOUTIAO = 1;
	const TENCENT = 2;
	const KUAISHOU = 3;
	const BAIDU = 4;
	const MP = 5;
	const UC = 6;
	const XULI = 7;
	const OW = 8;
	const OTHER = 9;
	const IQIYI = 10;
	const SEARCH = 11;
	const MOMO = 12;
	const NETEASE = 13;
	const BAIDU_SEARCH = 14;
	const SOUGOU_SEARCH = 15;
	const SHENMA_SEARCH = 16;
	const MEDIA_SCHEDULE = 17;
	const PRE_INSTALL = 18;
	const TECH_TEST = 19;
	const SMS = 20;
	const DOUYIN_STAR = 21;
	const KUAISHOU_STAR = 22;
	const TOUTIAO_APP = 23;
	const QIHU_SEARCH = 24;
	const ZUIYOU = 26;
	const SIBMOB = 27;
	const SIGMOB = 28;
	const HUYA = 29;
	const CHENZHONG = 30;
	const HAOLIANG = 31;
	const MEITU = 32;
	const AD_BRIGHT = 33;
	const DAOMEN = 34;
	const GOUWAN = 35;
	const HAIKANG = 36;
	const JIAOBU = 37;
	const JINGUIZI = 38;
	const JIXIN = 39;
	const KEKETU = 40;
	const KUYOU = 41;
	const MAILUO = 42;
	const QIANYI = 43;
	const QUTIAN = 44;
	const RUIYI = 45;
	const SHANGNIJI = 46;
	const SHUABAO = 47;
	const XINQU = 48;
	const XIMALAYA = 49;
	const ZHONGXU = 50;
	const ZHUOYUE = 51;
	const YOUHUA = 52;
	const STORE = 53;
	const LDY = 54;
	const ICON = 55;
	const JUGAO = 56;
	const RUIKE = 57;
	const YIXIAO = 58;
	const YOUKU = 59;
	const PINXUAN = 61;
	const SEARCH_PINZHUAN = 62;
	const ANRUN = 63;
	const ASO = 64;
	const WEIDU = 65;
	const JINGMENG = 66;
	const DONGXINTONG = 67;
	const ASTRA = 68;
	const TAKEN = 69;
	const DONGQIUDI = 71;
	const DAMEI = 72;
	const YUYIN = 73;
	const MAIJI = 74;
	const SIRUIWOTE = 75;
	const NANJING_YIYUN = 76;
	const ZHIXIAO = 77;
	const SHUANGYANG = 78;
	const QIMAI = 79;
	const SIYU = 80;
	const PINDUODUO = 81;
	const CHUANGLE = 82;
	const SHIHUYING = 83;
	const XINYUANHUDONG = 84;
	const WEIBO = 85;
	const MIFENG = 86;
	const WEIXIAO = 87;
	const FENGHUANG = 88;
	const YOUMENGHUYU = 89;
	const DOUYU = 90;
	const INMOBI = 91;
	const ZHIZHEN = 92;
	const JUZHANG = 93;
	const AIWAN = 94;
	const NVRENTONG = 95;
	const TIANWEICHENGTUO = 96;
	const JUYOULIANG = 97;
	const YIFUSHUCHUANG = 98;
	const QUTOUTIAO = 99;
	const FUKUO = 100;
	const GUTE = 101;
	const WENHUANG = 102;
	const AIPU = 103;
	const HENGSHIRONGDA = 104;
	const SHUCHUAN = 105;
	const YOUQU = 106;
	const HONGBAOTIXIAN = 107;
	const TOUTIAO_PINPAI = 108;
	const BOXUNLINGDONG = 109;
	const CHENYUE = 110;
	const HAIQU = 111;
	const ZHONGYU = 112;
	const AIDIANHUYU = 113;
	const JIUDIANBAN = 114;
	const FENXIANGSHI = 115;
	const TUBI = 116;
	const ZHONGZHI = 117;
	const SHENKAI = 118;
	const QUSIDE = 119;
	const JUXIAO = 120;
	const LULUFA = 121;
	const YOUCHAO = 122;
	const AIXI = 123;
	const FENGMENG = 124;
	const XINLANGFUYI = 125;
	const YOULONG = 126;
	const LENGDIAN = 127;
	const DUOCAI = 128;
	const HONGYU = 129;
	const YANGJING = 130;
	const NANXUN = 131;
	const HAOYE = 132;
	const TAIYUE = 133;
	const QIMINGHUDONG = 134;
	const MUZI = 135;
	const XINHUO = 136;
	const AIMAICHI = 137;
	const WOYOU = 138;
	const WEICHUANBO = 139;
	const CHUYOU = 140;
	const SHANGWEN = 141;
	const SHANCHENG = 142;
	const JIECHUANG = 143;
	const LONGDEYAO = 144;
	const AISIZHUSHOU = 145;
	const QIANFANG = 146;
	const LAIMI = 147;
	const LIANYU = 148;
	const YUNMO = 149;
	const DONGFANGTOUTIAO = 150;
	const OPPO = 151;
	const ZHILIAO = 152;
	const KUAIBU = 153;
	const HULIAN = 154;
	const ZHITOU = 155;
	const CUNXIN = 156;
	const HONGCHENG = 157;
	const QUANJING = 158;
	const LONGMAOCHUANMEI = 159;
	const YILE = 160;
	const NO_DOC = 161;
	const NEIDE = 162;
	const BILIBILI = 163;
	const KUAISHOU_PINPAI = 164;
	const ZIYING_PLATFORM = 165;
	const BINGXUE_PLATFORM = 166;
	const YIXING = 167;
	const JN_PUSH = 168;
	const APP_ASSISTANT = 169;
	const HAOTE = 170;
	const CPA = 171;
	const CPS = 172;
	const YOUJU = 173;
	const QIANKA = 174;
	const HUPU = 175;
	const YIYUN = 176;
	const HUAWEI = 177;
	const HESHENG = 178;
	const TOUTIAO_PINPAI_LIVE = 179;
	const YUNJU = 180;
	const FACEBOOK = 181;
	const YUWAN = 182;
	const JIFENQIANG = 183;
	const ASA = 184;
	const ZIRANLIANG = 185;
	const GOOGLE_ADWORDS = 186;
	const SOUGOU_OCPC = 187;
	const FEIYING = 188;
	const ZHONGXIN = 189;
	const GUSI = 190;
	const HUAWEI_OCPD = 191;
	const FANQIE = 192;
	const SOHU = 193;
	const LAHONG = 194;
	const TAPTAP = 195;
	const QUHUAN = 196;
	const BUPET = 197;
	const DAHENG = 198;
	const TIANCHENG = 199;
	const WEISHI = 200;
	const LINDONG = 201;
	const XINMEI = 202;
	const XIANGRONG = 203;
	const MITUTU = 204;
	const TIKTOK = 205;
	const PRIVATE_WEBSITE = 206;
	const KUAIKANMANHUA = 207;
	const YANGLE = 209;
	const QIHU_JINGJIA = 210;
	const FEISHU = 211;
	const YEYOU_SEARCH = 212;
	const WEIXING = 213;
	const XIAOQIANQIAN = 214;
	const UNITY = 215;
	const TWITTER = 216;
	const SINA = 217;
	const LEME = 218;
	const UC_LIVE = 219;
	const BAITE = 220;
	const XINGTU = 221;
	const RUICHU = 223;
	const YUCHI = 224;
	const HUOBANYUN = 225;
	const BLUESTACKS = 226;
	const BIGNOX = 227;
	const YUNXIANG = 228;
	const QIWU_CPS = 229;
	const LAITEMOBI = 230;
	const MOYING = 231;
	const HAODONG = 232;
	const HAXI = 233;
	const YURUYOU = 234;
	const DUOTE = 235;
	const FANZHUO = 236;
	const YUNSI = 237;
	const QIDIAN = 238;
	const NETEASE_YOUDAO = 239;
	const ESPACEADS = 240;
	const AD_OWN_CASH = 241;
	const HAXI_DOUYU = 242;
	const YOUTUBE = 243;
	const YOUJIAYUAN = 244;
	const ZHIHU = 245;
	const YUCHE = 246;
	const BYTEDANCE_GLOBAL = 247;
	const YEAHMOBI = 248;
	const BATCLOUD = 249;
	const MINTEGRAL = 250;
	const LDPLAYER = 251;
	const PRIVATE_TRAFFIC = 252;
	const XIAOHONGSHU = 253;
	const SOUL = 254;
	const DOUYIN = 255;
	const QIANCHUAN = 256;
	const KUAI_KAN = 257;
	const HONOR = 258;
	const HEMING = 259;
	const ALIPAY = 260;
	const MOVABLE = 261;
	const NOXMOBI = 262;
	const DEFAULT = 263;
	const AUDO = 264;
	const ORGANIC = 265;
	const NETMARVEL = 266;
	const APPIER = 267;
	const WANLIANG = 268;
	const YAHOO = 269;
	const ZHIBO8 = 270;
	const QIMAO = 271;
	const YOUBIKESI = 272;
	const NETEASE_NEWS = 273;
	const YIDONG_SEARCH = 274;
	const SOYBEANMOB = 275;
	const OCCUADLTD = 276;
	const CHANNELS_LIVE = 277;
	const BILIBILI_STAR = 278;
	const JINSHOUZHI = 279;
	const DOUYIN_ENTERPRISE = 280;
	const MOBISUMMER = 281;
	const YIZHI = 282;
	const WEILAIYOU = 283;
	const BILIBILI_LY = 284;
	const BRBT = 285;
	const DAILY_NEWS_AUS = 286;
	const OPERATIONAL = 287;
	const XIAOMI = 288;
	const VIVO = 289;
	const LONGZHU = 290;
	const JUGUANG = 291;
	const HAOYOU_KUAIBAO = 292;
	const OUAI = 293;
	const TANWAN_GAME_BOX = 294;
	const XINGTU_AD = 295;
	const XINCHENG_BOX = 296;
	const TANGTANG_TEC = 297;
	const MENGYOU = 298;
	const HYPERTAP = 299;
	const DOUBAN = 300;
	const ADX = 301;
	const WEIBO_PINXUAN = 302;
	const XINGTU_STAR = 303;
	const WEIBO_STAR = 304;
	const HUAWEI_PUSH = 305;
	const TAPTAP_PINXUAN = 306;
	const MOLOCO = 307;
	const HAPOX = 308;
	const MAIMAI = 309;
	const RUNQUAN = 310;
	const HUAWEI_JINGHONG = 311;
	const QIHU_SHOW = 312;
	const SNSCHANNEL = 313;
	const KUAISHOU_JUXING = 314;
	const BING = 315;
	const GADMOBE = 316;
	const HUIIMEDIA = 317;
	const JIUXIANG = 318;
	const GDT_DATANEXUES = 319;
	const UA_STATION = 320;
	const QOO_APP = 321;
	const YITUO = 322;
	const DIANJING = 323;
	const XINGCHEN_CPS = 324;
	const CALLIVO = 325;
	const MOBIPROBE = 326;
	const HANDAN_DALIANG = 327;
	const SISANJIUJIU = 328;
	const LEIDIAN = 329;
	const MUMU = 330;
	const NUBIA = 331;
	const MEIZU = 332;
	const SAMSUNG = 333;
	const ERSANSAN_LEYUAN = 334;
	const MEITUAN = 335;
	const BAIDU_LIANYUN = 336;
	const XINGTU_CPS = 337;
	const ADWIEN = 338;
	const TRAFFICABCCC = 339;
	const KUAIFA_LIANYUN = 340;
	const KUWAN = 341;
	const TOUTIAO_DEVELOPER = 342;
	const JIANGUO = 343;
	const HUAWEI_MONETIZATION = 344;
	const JUNYU = 345;
	const ZB_FANS = 346;
	const OPEN_GAME = 347;
	const ADELIVER = 348;
	const HAPOX_TEC = 349;
	const FUNMOBI_AGENCE = 350;
	const WILEWHALE = 351;
	const ZEBRA_LIANYUN = 352;
	const YANYANG = 353;
	const ZHANGHUI = 354;
	const TAIDONG = 355;
	const MANXING = 356;
	const ZHISUO = 357;
	const ADBRIDGE = 358;
	const HUANYU = 359;
	const IPSYSKE = 360;
	const YY = 361;
	const OPERATIONSSODAVCMOB = 362;
	const WANKA_LIANYUN = 363;
	const HONGKONG_JOOWATE = 364;
	const SHUABANG = 365;
	const YIDENGYUN = 366;
	const ADSTADT = 367;
	const CHENGYOU = 368;
	const UNICOM = 369;
	const APPNEXT = 370;
	const SMART_CONNECT = 371;
	const CHUANYIN = 372;
	const QIEZI = 373;
	const BILIBILI_LIVE = 374;


	const MEDIA_TYPE_MAP = [
		self::TOUTIAO => '今日头条',
		self::TENCENT => '广点通',
		self::KUAISHOU => '快手',
		self::BAIDU => '百度信息流',
		self::MP => '微信',
		self::UC => 'UC汇川',
		self::XULI => 'XL',
		self::OW => '官网',
		self::OTHER => '其他',
		self::IQIYI => '爱奇艺',
		self::SEARCH => '搜索',
		self::MOMO => '陌陌',
		self::NETEASE => '网易',
		self::BAIDU_SEARCH => '百度搜索',
		self::SOUGOU_SEARCH => '搜狗搜索',
		self::SHENMA_SEARCH => '神马搜索',
		self::MEDIA_SCHEDULE => '排期媒体',
		self::PRE_INSTALL => '预装',
		self::TECH_TEST => '技术测试',
		self::SMS => '短信',
		self::DOUYIN_STAR => '抖音红人',
		self::KUAISHOU_STAR => '快手红人',
		self::TOUTIAO_APP => '头条应用管理中心',
		self::QIHU_SEARCH => '360搜索',
		self::ZUIYOU => '最右',
		self::SIBMOB => 'sibmob',
		self::SIGMOB => 'sigmob',
		self::HUYA => '虎牙',
		self::CHENZHONG => '快接单',
		self::HAOLIANG => '皓量',
		self::MEITU => '美图',
		self::AD_BRIGHT => 'adbright',
		self::DAOMEN => '道门',
		self::GOUWAN => '够玩',
		self::HAIKANG => '海康',
		self::JIAOBU => '脚步',
		self::JINGUIZI => '金龟子',
		self::JIXIN => '吉欣',
		self::KEKETU => '可可兔',
		self::KUYOU => '酷游',
		self::MAILUO => '脉络',
		self::QIANYI => '千易',
		self::QUTIAN => '曲天',
		self::RUIYI => '瑞翼',
		self::SHANGNIJI => '尚尼吉',
		self::SHUABAO => '刷宝',
		self::XINQU => '心趣',
		self::XIMALAYA => '喜马拉雅',
		self::ZHONGXU => '中旭',
		self::ZHUOYUE => '灼悦',
		self::YOUHUA => '优化',
		self::STORE => '商店',
		self::LDY => '落地页',
		self::ICON => 'icon图',
		self::JUGAO => '聚告',
		self::RUIKE => '锐客',
		self::YIXIAO => '易效',
		self::YOUKU => '优酷',
		self::PINXUAN => '品宣',
		self::SEARCH_PINZHUAN => '搜索品专',
		self::ANRUN => '安润',
		self::ASO => 'aso',
		self::WEIDU => '维度',
		self::JINGMENG => '京盟',
		self::DONGXINTONG => '动信通',
		self::ASTRA => 'astra',
		self::TAKEN => '塔肯',
		self::DONGQIUDI => '懂球帝',
		self::DAMEI => '达美',
		self::YUYIN => '榆印',
		self::MAIJI => '麦极',
		self::SIRUIWOTE => '斯瑞沃特',
		self::NANJING_YIYUN => '南京亿云',
		self::ZHIXIAO => '知效',
		self::SHUANGYANG => '双阳',
		self::QIMAI => '七麦',
		self::SIYU => '肆羽',
		self::PINDUODUO => '拼多多',
		self::CHUANGLE => '创乐',
		self::SHIHUYING => '势互赢',
		self::XINYUANHUDONG => '新元互动',
		self::WEIBO => '微博',
		self::MIFENG => '蜜蜂试玩',
		self::WEIXIAO => '维效',
		self::FENGHUANG => '凤凰新闻',
		self::YOUMENGHUYU => '友梦互娱',
		self::DOUYU => '斗鱼',
		self::INMOBI => 'inmobi',
		self::ZHIZHEN => '至真',
		self::JUZHANG => '巨掌aso',
		self::AIWAN => '爱玩aso',
		self::NVRENTONG => '女人通',
		self::TIANWEICHENGTUO => '天威晟拓',
		self::JUYOULIANG => '聚优良',
		self::YIFUSHUCHUANG => '易服数创',
		self::QUTOUTIAO => '趣头条',
		self::FUKUO => '复阔',
		self::GUTE => '古特',
		self::WENHUANG => '文皇',
		self::AIPU => '爱普',
		self::HENGSHIRONGDA => '恒世荣达',
		self::SHUCHUAN => '数传',
		self::YOUQU => '有趣',
		self::HONGBAOTIXIAN => '红包提现',
		self::TOUTIAO_PINPAI => '头条品牌',
		self::BOXUNLINGDONG => '柏讯灵动',
		self::CHENYUE => '晨悦',
		self::HAIQU => '嗨趣',
		self::ZHONGYU => '众娱',
		self::AIDIANHUYU => '爱点互娱',
		self::JIUDIANBAN => '九点半',
		self::FENXIANGSHI => '分享时',
		self::TUBI => '图币',
		self::ZHONGZHI => '中至',
		self::SHENKAI => '绅凯',
		self::QUSIDE => '趣思得',
		self::JUXIAO => '聚效',
		self::LULUFA => '陆陆发',
		self::YOUCHAO => '悠超',
		self::AIXI => '艾希',
		self::FENGMENG => '峰盟',
		self::XINLANGFUYI => '新浪扶翼',
		self::YOULONG => '游龙',
		self::LENGDIAN => '棱点',
		self::DUOCAI => '多彩',
		self::HONGYU => '虹宇',
		self::YANGJING => '扬京',
		self::NANXUN => '南讯',
		self::HAOYE => '浩业',
		self::TAIYUE => '钛跃',
		self::QIMINGHUDONG => '启明互动',
		self::MUZI => '木子',
		self::XINHUO => '薪火',
		self::AIMAICHI => '艾卖驰',
		self::WOYOU => '沃游',
		self::WEICHUANBO => '微传播',
		self::CHUYOU => '初友',
		self::SHANGWEN => '商闻',
		self::SHANCHENG => '山城',
		self::JIECHUANG => '捷创',
		self::LONGDEYAO => '龙德曜',
		self::AISIZHUSHOU => '爱思助手',
		self::QIANFANG => '千方',
		self::LAIMI => '徕米',
		self::LIANYU => '连雨',
		self::YUNMO => '云漠',
		self::DONGFANGTOUTIAO => '东方头条',
		self::OPPO => 'OPPO',
		self::ZHILIAO => '知了',
		self::KUAIBU => '快步',
		self::HULIAN => '互联',
		self::ZHITOU => '智投',
		self::CUNXIN => '寸心',
		self::HONGCHENG => '红橙',
		self::QUANJING => '全景',
		self::LONGMAOCHUANMEI => '龙猫传媒',
		self::YILE => '易乐',
		self::NO_DOC => '无文档-通用',
		self::NEIDE => '内德',
		self::BILIBILI => '哔哩哔哩',
		self::KUAISHOU_PINPAI => '快手品牌',
		self::ZIYING_PLATFORM => '自营平台',
		self::BINGXUE_PLATFORM => '冰雪平台',
		self::YIXING => '意行',
		self::JN_PUSH => '锦囊智推',
		self::APP_ASSISTANT => 'app助手',
		self::HAOTE => '好特',
		self::CPA => 'CPA',
		self::CPS => 'CPS',
		self::YOUJU => '优聚',
		self::QIANKA => '钱咖aso',
		self::HUPU => '虎扑',
		self::YIYUN => '易云',
		self::HUAWEI => '华为',
		self::HESHENG => '合声aso',
		self::TOUTIAO_PINPAI_LIVE => '头条品牌直播',
		self::YUNJU => '云聚aso',
		self::FACEBOOK => 'facebook',
		self::YUWAN => '渝丸',
		self::JIFENQIANG => '积分墙',
		self::ASA => 'ASA',
		self::ZIRANLIANG => '自然量',
		self::GOOGLE_ADWORDS => 'google_adwords',
		self::SOUGOU_OCPC => '搜狗ocpc',
		self::FEIYING => '飞鹰',
		self::ZHONGXIN => '众鑫',
		self::GUSI => '顾思',
		self::HUAWEI_OCPD => '华为ocpd',
		self::FANQIE => '番茄',
		self::SOHU => '搜狐',
		self::LAHONG => '拉轰',
		self::TAPTAP => 'taptap',
		self::QUHUAN => '趣幻',
		self::BUPET => 'Bupet',
		self::DAHENG => '大恒',
		self::TIANCHENG => '天程',
		self::WEISHI => '微视',
		self::LINDONG => '邻动',
		self::XINMEI => '新美',
		self::XIANGRONG => '湘荣',
		self::MITUTU => '芈兔兔',
		self::TIKTOK => 'TikTok',
		self::PRIVATE_WEBSITE => '私服网站',
		self::KUAIKANMANHUA => '快看漫画',
		self::YANGLE => '羊乐',
		self::QIHU_JINGJIA => '360竞价',
		self::FEISHU => '飞书',
		self::YEYOU_SEARCH => '页游搜索',
		self::WEIXING => '伟兴',
		self::XIAOQIANQIAN => '小钱钱',
		self::UNITY => 'Unity',
		self::TWITTER => 'Twitter',
		self::SINA => '新浪',
		self::LEME => 'leme',
		self::UC_LIVE => 'UC直播',
		self::BAITE => '百特',
		self::XINGTU => '星图',
		self::RUICHU => '睿初',
		self::YUCHI => '羽驰',
		self::HUOBANYUN => '伙伴云',
		self::BLUESTACKS => 'Bluestacks',
		self::BIGNOX => 'Bignox',
		self::YUNXIANG => '云响',
		self::QIWU_CPS => '齐悟CPS',
		self::LAITEMOBI => '莱特摩比',
		self::MOYING => '魔鹰',
		self::HAODONG => '浩动',
		self::HAXI => '哈希',
		self::YURUYOU => '裕如优',
		self::DUOTE => '多特',
		self::FANZHUO => '凡卓',
		self::YUNSI => '云思ASO',
		self::QIDIAN => '奇点',
		self::NETEASE_YOUDAO => '网易有道',
		self::ESPACEADS => 'Espaceads',
		self::AD_OWN_CASH => '自建广告变现',
		self::HAXI_DOUYU => '哈希斗鱼',
		self::YOUTUBE => 'Youtube',
		self::YOUJIAYUAN => '优嘉源',
		self::ZHIHU => '知乎',
		self::YUCHE => '雨澈ASO',
		self::BYTEDANCE_GLOBAL => '字节跳动全球',
		self::YEAHMOBI => 'Yeahmobi',
		self::BATCLOUD => 'Batcloud',
		self::MINTEGRAL => 'Mintegral',
		self::LDPLAYER => 'LDPlayer',
		self::PRIVATE_TRAFFIC => '私域',
		self::XIAOHONGSHU => '小红书',
		self::SOUL => 'soul',
		self::DOUYIN => '抖音',
		self::QIANCHUAN => '千川',
		self::KUAI_KAN => '快看',
		self::HONOR => '荣耀',
		self::HEMING => '和鸣',
		self::ALIPAY => '支付宝',
		self::MOVABLE => 'movable',
		self::NOXMOBI => 'noxmobi',
		self::DEFAULT => 'default',
		self::AUDO => 'audo',
		self::ORGANIC => 'organic',
		self::NETMARVEL => 'netmarvel',
		self::APPIER => 'appier',
		self::WANLIANG => '万量',
		self::YAHOO => '雅虎',
		self::ZHIBO8 => '直播吧',
		self::QIMAO => '七猫',
		self::YOUBIKESI => '优比克思',
		self::NETEASE_NEWS => '网易新闻',
		self::YIDONG_SEARCH => '移动搜索',
		self::SOYBEANMOB => 'Soybeanmob',
		self::OCCUADLTD => 'OccuadLtd',
		self::CHANNELS_LIVE => '微信视频号直播',
		self::BILIBILI_STAR => '哔哩哔哩红人',
		self::JINSHOUZHI => '金手指ASO',
		self::DOUYIN_ENTERPRISE => '抖音企业号',
		self::MOBISUMMER => 'Mobisummer',
		self::YIZHI => '倚智ASO',
		self::WEILAIYOU => '未来游ASO',
		self::BILIBILI_LY => '哔哩哔哩-联运',
		self::BRBT => '澳加美',
		self::DAILY_NEWS_AUS => 'DailyNewsAus',
		self::OPERATIONAL => '运营业务',
		self::XIAOMI => '小米',
		self::VIVO => 'VIVO',
		self::LONGZHU => 'ASO龙珠试玩',
		self::JUGUANG => '聚光',
		self::HAOYOU_KUAIBAO => '好游快爆',
		self::OUAI => '欧爱',
		self::TANWAN_GAME_BOX => '贪玩游戏盒子',
		self::XINGTU_AD => '星广联投AD版',
		self::XINCHENG_BOX => '馨程盒子',
		self::TANGTANG_TEC => '棠棠科技',
		self::MENGYOU => '猛游ASO',
		self::HYPERTAP => 'Hypertap',
		self::DOUBAN => '豆瓣',
		self::ADX => 'ADX',
		self::WEIBO_PINXUAN => '微博品宣',
		self::XINGTU_STAR => '星图红人',
		self::WEIBO_STAR => '微博红人',
		self::HUAWEI_PUSH => '华为推送',
		self::TAPTAP_PINXUAN => 'taptap品宣',
		self::MOLOCO => 'moloco',
		self::HAPOX => 'hapox',
		self::MAIMAI => '脉脉',
		self::RUNQUAN => '润泉ASO',
		self::HUAWEI_JINGHONG => '华为鲸鸿动能',
		self::QIHU_SHOW => '360展示',
		self::SNSCHANNEL => 'SNSchannel',
		self::KUAISHOU_JUXING => '磁力聚星',
		self::BING => '必应',
		self::GADMOBE => 'gadmobe',
		self::HUIIMEDIA => 'Huiimedia',
		self::JIUXIANG => '九想ASO',
		self::GDT_DATANEXUES => 'gdt_datanexues',
		self::UA_STATION => 'UAStation',
		self::QOO_APP => 'QooApp',
		self::YITUO => '易拓',
		self::DIANJING => '360点睛',
		self::XINGCHEN_CPS => '卓越星辰CPS',
		self::CALLIVO => 'Callivo',
		self::MOBIPROBE => 'Mobiprobe',
		self::HANDAN_DALIANG => '邯郸达量',
		self::SISANJIUJIU => '4399',
		self::LEIDIAN => '雷电',
		self::MUMU => 'mumu',
		self::NUBIA => '努比亚',
		self::MEIZU => '魅族',
		self::SAMSUNG => '三星',
		self::ERSANSAN_LEYUAN => '233乐园',
		self::MEITUAN => '美团',
		self::BAIDU_LIANYUN => '百度联运',
		self::XINGTU_CPS => '星途CPS',
		self::ADWIEN => 'Adwien',
		self::TRAFFICABCCC => 'TrafficABCCC',
		self::KUAIFA_LIANYUN => '快发-联运',
		self::KUWAN => '酷玩',
		self::TOUTIAO_DEVELOPER => '头条开发者',
		self::JIANGUO => '坚果',
		self::HUAWEI_MONETIZATION => '华为流量变现',
		self::JUNYU => '君娱',
		self::ZB_FANS => 'ZB粉丝群',
		self::OPEN_GAME => 'OpenGame',
		self::ADELIVER => 'Adeliver',
		self::HAPOX_TEC => 'HapoxTEC',
		self::FUNMOBI_AGENCE => 'Funmobi-Agence',
		self::WILEWHALE => 'Wilewhale',
		self::ZEBRA_LIANYUN => '斑马联运',
		self::YANYANG => '研漾',
		self::ZHANGHUI => '掌慧dsp',
		self::TAIDONG => '钛动dsp',
		self::MANXING => '漫星',
		self::ZHISUO => '智索',
		self::ADBRIDGE => 'AdBridge',
		self::HUANYU => '环语',
		self::IPSYSKE => 'IPSYSKE',
		self::YY => 'YY语音',
		self::OPERATIONSSODAVCMOB => 'Operationssodavcmob',
		self::WANKA_LIANYUN => '玩咖联运',
		self::HONGKONG_JOOWATE => 'HongKongJoowate',
		self::SHUABANG => '刷榜',
		self::YIDENGYUN => '一灯云',
		self::ADSTADT => 'Adstadt',
		self::CHENGYOU => '乘游',
		self::UNICOM => '联通',
		self::APPNEXT => 'Appnext',
		self::SMART_CONNECT => 'SmartConnect',
		self::CHUANYIN => '传音',
		self::QIEZI => '茄子',
		self::BILIBILI_LIVE => '哔哩哔哩直播',

	];

	const CONST_NAME_LIST = [self::TOUTIAO => 'TOUTIAO', self::TENCENT => 'TENCENT', self::KUAISHOU => 'KUAISHOU', self::BAIDU => 'BAIDU', self::MP => 'MP', self::UC => 'UC', self::XULI => 'XULI', self::OW => 'OW', self::OTHER => 'OTHER', self::IQIYI => 'IQIYI', self::SEARCH => 'SEARCH', self::MOMO => 'MOMO', self::NETEASE => 'NETEASE', self::BAIDU_SEARCH => 'BAIDU_SEARCH', self::SOUGOU_SEARCH => 'SOUGOU_SEARCH', self::SHENMA_SEARCH => 'SHENMA_SEARCH', self::MEDIA_SCHEDULE => 'MEDIA_SCHEDULE', self::PRE_INSTALL => 'PRE_INSTALL', self::TECH_TEST => 'TECH_TEST', self::SMS => 'SMS', self::DOUYIN_STAR => 'DOUYIN_STAR', self::KUAISHOU_STAR => 'KUAISHOU_STAR', self::TOUTIAO_APP => 'TOUTIAO_APP', self::QIHU_SEARCH => 'QIHU_SEARCH', self::ZUIYOU => 'ZUIYOU', self::SIBMOB => 'SIBMOB', self::SIGMOB => 'SIGMOB', self::HUYA => 'HUYA', self::CHENZHONG => 'CHENZHONG', self::HAOLIANG => 'HAOLIANG', self::MEITU => 'MEITU', self::AD_BRIGHT => 'AD_BRIGHT', self::DAOMEN => 'DAOMEN', self::GOUWAN => 'GOUWAN', self::HAIKANG => 'HAIKANG', self::JIAOBU => 'JIAOBU', self::JINGUIZI => 'JINGUIZI', self::JIXIN => 'JIXIN', self::KEKETU => 'KEKETU', self::KUYOU => 'KUYOU', self::MAILUO => 'MAILUO', self::QIANYI => 'QIANYI', self::QUTIAN => 'QUTIAN', self::RUIYI => 'RUIYI', self::SHANGNIJI => 'SHANGNIJI', self::SHUABAO => 'SHUABAO', self::XINQU => 'XINQU', self::XIMALAYA => 'XIMALAYA', self::ZHONGXU => 'ZHONGXU', self::ZHUOYUE => 'ZHUOYUE', self::YOUHUA => 'YOUHUA', self::STORE => 'STORE', self::LDY => 'LDY', self::ICON => 'ICON', self::JUGAO => 'JUGAO', self::RUIKE => 'RUIKE', self::YIXIAO => 'YIXIAO', self::YOUKU => 'YOUKU', self::PINXUAN => 'PINXUAN', self::SEARCH_PINZHUAN => 'SEARCH_PINZHUAN', self::ANRUN => 'ANRUN', self::ASO => 'ASO', self::WEIDU => 'WEIDU', self::JINGMENG => 'JINGMENG', self::DONGXINTONG => 'DONGXINTONG', self::ASTRA => 'ASTRA', self::TAKEN => 'TAKEN', self::DONGQIUDI => 'DONGQIUDI', self::DAMEI => 'DAMEI', self::YUYIN => 'YUYIN', self::MAIJI => 'MAIJI', self::SIRUIWOTE => 'SIRUIWOTE', self::NANJING_YIYUN => 'NANJING_YIYUN', self::ZHIXIAO => 'ZHIXIAO', self::SHUANGYANG => 'SHUANGYANG', self::QIMAI => 'QIMAI', self::SIYU => 'SIYU', self::PINDUODUO => 'PINDUODUO', self::CHUANGLE => 'CHUANGLE', self::SHIHUYING => 'SHIHUYING', self::XINYUANHUDONG => 'XINYUANHUDONG', self::WEIBO => 'WEIBO', self::MIFENG => 'MIFENG', self::WEIXIAO => 'WEIXIAO', self::FENGHUANG => 'FENGHUANG', self::YOUMENGHUYU => 'YOUMENGHUYU', self::DOUYU => 'DOUYU', self::INMOBI => 'INMOBI', self::ZHIZHEN => 'ZHIZHEN', self::JUZHANG => 'JUZHANG', self::AIWAN => 'AIWAN', self::NVRENTONG => 'NVRENTONG', self::TIANWEICHENGTUO => 'TIANWEICHENGTUO', self::JUYOULIANG => 'JUYOULIANG', self::YIFUSHUCHUANG => 'YIFUSHUCHUANG', self::QUTOUTIAO => 'QUTOUTIAO', self::FUKUO => 'FUKUO', self::GUTE => 'GUTE', self::WENHUANG => 'WENHUANG', self::AIPU => 'AIPU', self::HENGSHIRONGDA => 'HENGSHIRONGDA', self::SHUCHUAN => 'SHUCHUAN', self::YOUQU => 'YOUQU', self::HONGBAOTIXIAN => 'HONGBAOTIXIAN', self::TOUTIAO_PINPAI => 'TOUTIAO_PINPAI', self::BOXUNLINGDONG => 'BOXUNLINGDONG', self::CHENYUE => 'CHENYUE', self::HAIQU => 'HAIQU', self::ZHONGYU => 'ZHONGYU', self::AIDIANHUYU => 'AIDIANHUYU', self::JIUDIANBAN => 'JIUDIANBAN', self::FENXIANGSHI => 'FENXIANGSHI', self::TUBI => 'TUBI', self::ZHONGZHI => 'ZHONGZHI', self::SHENKAI => 'SHENKAI', self::QUSIDE => 'QUSIDE', self::JUXIAO => 'JUXIAO', self::LULUFA => 'LULUFA', self::YOUCHAO => 'YOUCHAO', self::AIXI => 'AIXI', self::FENGMENG => 'FENGMENG', self::XINLANGFUYI => 'XINLANGFUYI', self::YOULONG => 'YOULONG', self::LENGDIAN => 'LENGDIAN', self::DUOCAI => 'DUOCAI', self::HONGYU => 'HONGYU', self::YANGJING => 'YANGJING', self::NANXUN => 'NANXUN', self::HAOYE => 'HAOYE', self::TAIYUE => 'TAIYUE', self::QIMINGHUDONG => 'QIMINGHUDONG', self::MUZI => 'MUZI', self::XINHUO => 'XINHUO', self::AIMAICHI => 'AIMAICHI', self::WOYOU => 'WOYOU', self::WEICHUANBO => 'WEICHUANBO', self::CHUYOU => 'CHUYOU', self::SHANGWEN => 'SHANGWEN', self::SHANCHENG => 'SHANCHENG', self::JIECHUANG => 'JIECHUANG', self::LONGDEYAO => 'LONGDEYAO', self::AISIZHUSHOU => 'AISIZHUSHOU', self::QIANFANG => 'QIANFANG', self::LAIMI => 'LAIMI', self::LIANYU => 'LIANYU', self::YUNMO => 'YUNMO', self::DONGFANGTOUTIAO => 'DONGFANGTOUTIAO', self::OPPO => 'OPPO', self::ZHILIAO => 'ZHILIAO', self::KUAIBU => 'KUAIBU', self::HULIAN => 'HULIAN', self::ZHITOU => 'ZHITOU', self::CUNXIN => 'CUNXIN', self::HONGCHENG => 'HONGCHENG', self::QUANJING => 'QUANJING', self::LONGMAOCHUANMEI => 'LONGMAOCHUANMEI', self::YILE => 'YILE', self::NO_DOC => 'NO_DOC', self::NEIDE => 'NEIDE', self::BILIBILI => 'BILIBILI', self::KUAISHOU_PINPAI => 'KUAISHOU_PINPAI', self::ZIYING_PLATFORM => 'ZIYING_PLATFORM', self::BINGXUE_PLATFORM => 'BINGXUE_PLATFORM', self::YIXING => 'YIXING', self::JN_PUSH => 'JN_PUSH', self::APP_ASSISTANT => 'APP_ASSISTANT', self::HAOTE => 'HAOTE', self::CPA => 'CPA', self::CPS => 'CPS', self::YOUJU => 'YOUJU', self::QIANKA => 'QIANKA', self::HUPU => 'HUPU', self::YIYUN => 'YIYUN', self::HUAWEI => 'HUAWEI', self::HESHENG => 'HESHENG', self::TOUTIAO_PINPAI_LIVE => 'TOUTIAO_PINPAI_LIVE', self::YUNJU => 'YUNJU', self::FACEBOOK => 'FACEBOOK', self::YUWAN => 'YUWAN', self::JIFENQIANG => 'JIFENQIANG', self::ASA => 'ASA', self::ZIRANLIANG => 'ZIRANLIANG', self::GOOGLE_ADWORDS => 'GOOGLE_ADWORDS', self::SOUGOU_OCPC => 'SOUGOU_OCPC', self::FEIYING => 'FEIYING', self::ZHONGXIN => 'ZHONGXIN', self::GUSI => 'GUSI', self::HUAWEI_OCPD => 'HUAWEI_OCPD', self::FANQIE => 'FANQIE', self::SOHU => 'SOHU', self::LAHONG => 'LAHONG', self::TAPTAP => 'TAPTAP', self::QUHUAN => 'QUHUAN', self::BUPET => 'BUPET', self::DAHENG => 'DAHENG', self::TIANCHENG => 'TIANCHENG', self::WEISHI => 'WEISHI', self::LINDONG => 'LINDONG', self::XINMEI => 'XINMEI', self::XIANGRONG => 'XIANGRONG', self::MITUTU => 'MITUTU', self::TIKTOK => 'TIKTOK', self::PRIVATE_WEBSITE => 'PRIVATE_WEBSITE', self::KUAIKANMANHUA => 'KUAIKANMANHUA', self::YANGLE => 'YANGLE', self::QIHU_JINGJIA => 'QIHU_JINGJIA', self::FEISHU => 'FEISHU', self::YEYOU_SEARCH => 'YEYOU_SEARCH', self::WEIXING => 'WEIXING', self::XIAOQIANQIAN => 'XIAOQIANQIAN', self::UNITY => 'UNITY', self::TWITTER => 'TWITTER', self::SINA => 'SINA', self::LEME => 'LEME', self::UC_LIVE => 'UC_LIVE', self::BAITE => 'BAITE', self::XINGTU => 'XINGTU', self::RUICHU => 'RUICHU', self::YUCHI => 'YUCHI', self::HUOBANYUN => 'HUOBANYUN', self::BLUESTACKS => 'BLUESTACKS', self::BIGNOX => 'BIGNOX', self::YUNXIANG => 'YUNXIANG', self::QIWU_CPS => 'QIWU_CPS', self::LAITEMOBI => 'LAITEMOBI', self::MOYING => 'MOYING', self::HAODONG => 'HAODONG', self::HAXI => 'HAXI', self::YURUYOU => 'YURUYOU', self::DUOTE => 'DUOTE', self::FANZHUO => 'FANZHUO', self::YUNSI => 'YUNSI', self::QIDIAN => 'QIDIAN', self::NETEASE_YOUDAO => 'NETEASE_YOUDAO', self::ESPACEADS => 'ESPACEADS', self::AD_OWN_CASH => 'AD_OWN_CASH', self::HAXI_DOUYU => 'HAXI_DOUYU', self::YOUTUBE => 'YOUTUBE', self::YOUJIAYUAN => 'YOUJIAYUAN', self::ZHIHU => 'ZHIHU', self::YUCHE => 'YUCHE', self::BYTEDANCE_GLOBAL => 'BYTEDANCE_GLOBAL', self::YEAHMOBI => 'YEAHMOBI', self::BATCLOUD => 'BATCLOUD', self::MINTEGRAL => 'MINTEGRAL', self::LDPLAYER => 'LDPLAYER', self::PRIVATE_TRAFFIC => 'PRIVATE_TRAFFIC', self::XIAOHONGSHU => 'XIAOHONGSHU', self::SOUL => 'SOUL', self::DOUYIN => 'DOUYIN', self::QIANCHUAN => 'QIANCHUAN', self::KUAI_KAN => 'KUAI_KAN', self::HONOR => 'HONOR', self::HEMING => 'HEMING', self::ALIPAY => 'ALIPAY', self::MOVABLE => 'MOVABLE', self::NOXMOBI => 'NOXMOBI', self::DEFAULT => 'DEFAULT', self::AUDO => 'AUDO', self::ORGANIC => 'ORGANIC', self::NETMARVEL => 'NETMARVEL', self::APPIER => 'APPIER', self::WANLIANG => 'WANLIANG', self::YAHOO => 'YAHOO', self::ZHIBO8 => 'ZHIBO8', self::QIMAO => 'QIMAO', self::YOUBIKESI => 'YOUBIKESI', self::NETEASE_NEWS => 'NETEASE_NEWS', self::YIDONG_SEARCH => 'YIDONG_SEARCH', self::SOYBEANMOB => 'SOYBEANMOB', self::OCCUADLTD => 'OCCUADLTD', self::CHANNELS_LIVE => 'CHANNELS_LIVE', self::BILIBILI_STAR => 'BILIBILI_STAR', self::JINSHOUZHI => 'JINSHOUZHI', self::DOUYIN_ENTERPRISE => 'DOUYIN_ENTERPRISE', self::MOBISUMMER => 'MOBISUMMER', self::YIZHI => 'YIZHI', self::WEILAIYOU => 'WEILAIYOU', self::BILIBILI_LY => 'BILIBILI_LY', self::BRBT => 'BRBT', self::DAILY_NEWS_AUS => 'DAILY_NEWS_AUS', self::OPERATIONAL => 'OPERATIONAL', self::XIAOMI => 'XIAOMI', self::VIVO => 'VIVO', self::LONGZHU => 'LONGZHU', self::JUGUANG => 'JUGUANG', self::HAOYOU_KUAIBAO => 'HAOYOU_KUAIBAO', self::OUAI => 'OUAI', self::TANWAN_GAME_BOX => 'TANWAN_GAME_BOX', self::XINGTU_AD => 'XINGTU_AD', self::XINCHENG_BOX => 'XINCHENG_BOX', self::TANGTANG_TEC => 'TANGTANG_TEC', self::MENGYOU => 'MENGYOU', self::HYPERTAP => 'HYPERTAP', self::DOUBAN => 'DOUBAN', self::ADX => 'ADX', self::WEIBO_PINXUAN => 'WEIBO_PINXUAN', self::XINGTU_STAR => 'XINGTU_STAR', self::WEIBO_STAR => 'WEIBO_STAR', self::HUAWEI_PUSH => 'HUAWEI_PUSH', self::TAPTAP_PINXUAN => 'TAPTAP_PINXUAN', self::MOLOCO => 'MOLOCO', self::HAPOX => 'HAPOX', self::MAIMAI => 'MAIMAI', self::RUNQUAN => 'RUNQUAN', self::HUAWEI_JINGHONG => 'HUAWEI_JINGHONG', self::QIHU_SHOW => 'QIHU_SHOW', self::SNSCHANNEL => 'SNSCHANNEL', self::KUAISHOU_JUXING => 'KUAISHOU_JUXING', self::BING => 'BING', self::GADMOBE => 'GADMOBE', self::HUIIMEDIA => 'HUIIMEDIA', self::JIUXIANG => 'JIUXIANG', self::GDT_DATANEXUES => 'GDT_DATANEXUES', self::UA_STATION => 'UA_STATION', self::QOO_APP => 'QOO_APP', self::YITUO => 'YITUO', self::DIANJING => 'DIANJING', self::XINGCHEN_CPS => 'XINGCHEN_CPS', self::CALLIVO => 'CALLIVO', self::MOBIPROBE => 'MOBIPROBE', self::HANDAN_DALIANG => 'HANDAN_DALIANG', self::SISANJIUJIU => 'SISANJIUJIU', self::LEIDIAN => 'LEIDIAN', self::MUMU => 'MUMU', self::NUBIA => 'NUBIA', self::MEIZU => 'MEIZU', self::SAMSUNG => 'SAMSUNG', self::ERSANSAN_LEYUAN => 'ERSANSAN_LEYUAN', self::MEITUAN => 'MEITUAN', self::BAIDU_LIANYUN => 'BAIDU_LIANYUN', self::XINGTU_CPS => 'XINGTU_CPS', self::ADWIEN => 'ADWIEN', self::TRAFFICABCCC => 'TRAFFICABCCC', self::KUAIFA_LIANYUN => 'KUAIFA_LIANYUN', self::KUWAN => 'KUWAN', self::TOUTIAO_DEVELOPER => 'TOUTIAO_DEVELOPER', self::JIANGUO => 'JIANGUO', self::HUAWEI_MONETIZATION => 'HUAWEI_MONETIZATION', self::JUNYU => 'JUNYU', self::ZB_FANS => 'ZB_FANS', self::OPEN_GAME => 'OPEN_GAME', self::ADELIVER => 'ADELIVER', self::HAPOX_TEC => 'HAPOX_TEC', self::FUNMOBI_AGENCE => 'FUNMOBI_AGENCE', self::WILEWHALE => 'WILEWHALE', self::ZEBRA_LIANYUN => 'ZEBRA_LIANYUN', self::YANYANG => 'YANYANG', self::ZHANGHUI => 'ZHANGHUI', self::TAIDONG => 'TAIDONG', self::MANXING => 'MANXING', self::ZHISUO => 'ZHISUO', self::ADBRIDGE => 'ADBRIDGE', self::HUANYU => 'HUANYU', self::IPSYSKE => 'IPSYSKE', self::YY => 'YY', self::OPERATIONSSODAVCMOB => 'OPERATIONSSODAVCMOB', self::WANKA_LIANYUN => 'WANKA_LIANYUN', self::HONGKONG_JOOWATE => 'HONGKONG_JOOWATE', self::SHUABANG => 'SHUABANG', self::YIDENGYUN => 'YIDENGYUN', self::ADSTADT => 'ADSTADT', self::CHENGYOU => 'CHENGYOU', self::UNICOM => 'UNICOM', self::APPNEXT => 'APPNEXT', self::SMART_CONNECT => 'SMART_CONNECT', self::CHUANYIN => 'CHUANYIN', self::QIEZI => 'QIEZI', self::BILIBILI_LIVE => 'BILIBILI_LIVE'];


	// <custom-code-start>
	const REPORT_MEDIA_TYPE = [self::TOUTIAO, self::TENCENT, self::KUAISHOU, self::MP, self::BAIDU, self::UC, self::TOUTIAO_APP, self::BAIDU_SEARCH, self::FACEBOOK, self::IQIYI, self::GOOGLE_ADWORDS, self::TIKTOK, self::BILIBILI, self::WEIBO];
	// <custom-code-end>
}

