<?php

namespace App\Constant;

use Common\EnvConfig;

class MaterialAuthorEffectReportFilterSqlMap
{
    const TABLE = [
        'agent_leader_group' => EnvConfig::MYSQL['datahub']['database'] . '.v2_dim_agent_leader_group',
        'material' => 'tanwan_datamedia.ods_material_log',
        'material_file' => 'tanwan_datamedia.ods_material_file_log',
        'material_authors' => 'tanwan_datamedia.ods_material_authors_weight',

        'ad1_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad1_common_log'
        ],
        // **驱动表** 媒体广告二级公共维度表
        'ad2_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad2_common_log'
        ],
        // **驱动表** 媒体广告三级公共维度表
        'ad3_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad3_common_log'
        ],
        // **驱动表** 媒体广告四级公共维度表
        'ad4_common_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad4_common_log'
        ],
        // 媒体广告三级每小时的数据表(
        // 用于计算每个素材每小时的实际消耗价格[媒体给的折扣价(cost)]，每个素材每小时原消耗价格(ori_cost)，每个素材每小时的展示数,
        // 每个素材每小时的点击数,每个素材每小时的激活数,每个素材每小时的转化数,每个素材每小时的注册数,每个素材每小时的付费人数,
        // 每个素材每小时所属计划的site_id，每个素材每小时所属计划site_id中当即小时的game_id(每个小时的game_id会改变)
        // )
        'hour_data_log' => [
            0 => 'tanwan_datamedia.dwd_media_ad4_common_day_data_log'
        ],
        'account_log_table' => [
            0 => 'tanwan_datamedia.dwd_media_account_common_log'
        ],

        //// 用户注册记录表(用于算出累计充值金额，累计充值人数，累计注册人数)
        //'reg_log' => 'tanwan_datahub.v2_dwd_game_uid_reg_log',
        // 媒体广告三级每日业务数据(用于计算出每日付费金额，每日付费人数，每日激活设备数，每日注册人数，每日注册设备数，创角数)
        'overview_log' => [
            'tanwan_datahub.v3_ads_day_root_game_overview_log',
            3 => 'tanwan_datahub.v3_ads_day_root_game_back_overview_log'
        ],
        // 渠道&广告配置关联表(用于与驱动表进行关联，控制驱动表的agent_leader和site_id)
        'dim_agent_site' => 'tanwan_datahub.v2_dim_agent_site_id',
        // 游戏
        'dim_game' => 'tanwan_datahub.v2_dim_game_id',
        // 广告配置&game_id关联表(用于与驱动表和dim_agent_site进行关联，控制驱动表的game_id)
        'dim_site_game' => 'tanwan_datamedia.dim_site_game_id',
        //
        'dim_user_position' => 'web.dim_user_position'
    ];

    //
    const MAIN_COMPUTE = [
        0 => [
            0 => [
                'agent_leader_group_name' => ['forth_ad_log.agent_leader_group_name as agent_leader_group_name'],
                'author' => ['material_authors.author as author'],
                'department' => ['user_position.platform_id', 'user_position.department_id', 'user_position.department_name as department'],
                'department_group' => ['user_position.platform_id', 'user_position.department_group_id', 'user_position.department_group_name as department_group'],
                'department_platform' => ['user_position.platform_id', 'user_position.department_group_id', 'user_position.platform_name as department_platform'],
                'material_num' => ['COUNT(DISTINCT material_log.platform, material_log.material_id) as material_num',
                    "COUNT(DISTINCT material_log.platform, material_log.material_id, IF(material_log.is_del = '1', TRUE, null)) as material_num_del"],
                'material_file_num' => ["COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '0', TRUE, null)) as material_file_num",
                    "COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '0' AND material_log.file_is_del = '1', TRUE, null)) as material_file_num_del"],
                'material_file_ext_num' => ["COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '1', TRUE, null)) as material_file_ext_num",
                    "COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '1' AND material_log.file_is_del = '1', TRUE, null)) as material_file_ext_num_del"],

                //业务数据
                'platform' => ['forth_ad_log.platform as platform'],
                'os' => ['forth_ad_log.os as os'],
                'create_type' => ['forth_ad_log.create_type as create_type'],
                'game_id' => ['forth_ad_log.game_id as game_id'],
                'game_name' => ['forth_ad_log.game_name as game_name'],
                'main_game_id' => ['forth_ad_log.main_game_id as main_game_id'],
                'main_game_name' => ['forth_ad_log.main_game_name as main_game_name'],
                'root_game_id' => ['forth_ad_log.root_game_id as root_game_id'],
                'root_game_name' => ['forth_ad_log.root_game_name as root_game_name'],
                'site_id' => ['forth_ad_log.site_id as site_id'],
                'agent_leader' => ['forth_ad_log.agent_leader as agent_leader'],
                'cost' => ['sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'], // 返点后消耗
                'reg_uid_count' => ['sum(overview_log.reg_uid_count * (IFNULL(material_authors.weight, 100)/100)) as reg_uid_count'], // 注册数
                'action_uid_reg_rate' => [
                    'sum(overview_log.reg_uid_count * (IFNULL(material_authors.weight, 100)/100)) as sum_day_reg_uid_count',
                    'sum(overview_log.sum_day_action_muid_distinct_count * (IFNULL(material_authors.weight, 100)/100)) as sum_day_action_muid_distinct_count'
                ], // 激活注册率
                'cost_per_reg' => [
                    'IFNULL ( CAST ( ( sum(data_log.cost) / sum(overview_log.reg_uid_count)) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_per_reg',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost',
                    'sum(overview_log.reg_uid_count * (IFNULL(material_authors.weight, 100)/100)) as reg_uid_count'
                ], // 注册成本
                'reg_muid_count' => ['sum(overview_log.reg_muid_count * (IFNULL(material_authors.weight, 100)/100)) as reg_muid_count'], // 注册设备数
                'action_muid_count' => ['sum(overview_log.action_muid_count * (IFNULL(material_authors.weight, 100)/100)) as action_muid_count'], //激活设备数
                'first_day_ltv' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_money) / sum(overview_log.reg_uid_count)) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_ltv',
                    'sum(overview_log.first_day_pay_money * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_money',
                    'sum(overview_log.reg_uid_count * (IFNULL(material_authors.weight, 100)/100)) as sum_day_reg_uid_count'
                ], // 首日ltv
                'first_day_pay_count' => ['sum(overview_log.first_day_pay_count * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_count'], // 首日付费人数
                'first_day_pay_rate' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_count) / sum(overview_log.reg_uid_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_pay_rate',
                    'sum(overview_log.first_day_pay_count * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_count',
                    'sum(overview_log.reg_uid_count * (IFNULL(material_authors.weight, 100)/100)) as reg_uid_count'
                ], // 首日付费率
                'cost_per_first_day_pay' => [
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost',
                    'sum(overview_log.first_day_pay_count * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_count'
                ],
                // 首日付费成本
                'first_day_pay_money' => [
                    'sum(overview_log.first_day_pay_money) * (IFNULL(material_authors.weight, 100)/100) as first_day_pay_money'
                ],
                // 7日付费金额
                'seventh_day_pay_money' => [
                    'sum(overview_log.seventh_day_pay_money) * (IFNULL(material_authors.weight, 100)/100) as seventh_day_pay_money'
                ],
                // 15日付费金额
                'fifteenth_day_pay_money' => [
                    'sum(overview_log.fifteenth_day_pay_money) * (IFNULL(material_authors.weight, 100)/100) as fifteenth_day_pay_money'
                ],
                // 30日付费金额
                'thirty_day_pay_money' => [
                    'sum(overview_log.thirty_day_pay_money) * (IFNULL(material_authors.weight, 100)/100) as thirty_day_pay_money'
                ],

                'first_day_arppu' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_money) / sum(overview_log.first_day_pay_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_arppu',
                    'sum(overview_log.first_day_pay_money * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_money',
                    'sum(overview_log.first_day_pay_count * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_count'
                ], // 首日arppu
                'first_day_roi' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_money) / sum(data_log.cost) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_roi',
                    'sum(overview_log.first_day_pay_money * (IFNULL(material_authors.weight, 100)/100)) as first_day_pay_money',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'], // 首日roi
                'first_day_roi_standard_value' => [
                    'SUM(data_log.1_day_total_standard_value * (IFNULL(material_authors.weight, 100)/100)) as 1_day_total_standard_value',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
                'total_roi' => [
                    'sum(overview_log.total_pay_money * (IFNULL(material_authors.weight, 100)/100)) as sum_total_pay_money',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ], // 累计roi
                'rate_day_roi_7' => [
                    'SUM(overview_log.sum_seventh_day_pay_money * (IFNULL(material_authors.weight, 100)/100)) as sum_seventh_day_pay_money',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
                'rate_day_roi_30' => [
                    'SUM(overview_log.sum_thirty_day_pay_money * (IFNULL(material_authors.weight, 100)/100)) as sum_thirty_day_pay_money',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
                'rate_day_roi_7_standard_value' => [
                    'SUM(data_log.7_day_total_standard_value * (IFNULL(material_authors.weight, 100)/100)) as 7_day_total_standard_value',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
                'rate_day_roi_30_standard_value' => [
                    'SUM(data_log.30_day_total_standard_value * (IFNULL(material_authors.weight, 100)/100)) as 30_day_total_standard_value',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
                'cost_in_30' => ['sum(data_log.cost_in_30 * (IFNULL(material_authors.weight, 100)/100)) as cost_in_30'],
                'cost_first_30_day_this_month' => [
                    'SUM(data_cost_first_log.cost_first_30_day_this_month * (IFNULL(material_authors.weight, 100)/100)) as cost_first_30_day_this_month'
                ],
                'cost_first_60_day_this_month' => [
                    'SUM(data_cost_first_log.cost_first_60_day_this_month * (IFNULL(material_authors.weight, 100)/100)) as cost_first_60_day_this_month'
                ],
                'rate_day_standard_30' => [
                    'SUM(overview_log.sum_thirty_day_pay_money * (IFNULL(material_authors.weight, 100)/100)) as sum_thirty_day_pay_money',
                    'SUM(data_log.30_day_total_standard_value * (IFNULL(material_authors.weight, 100)/100)) as 30_day_total_standard_value',
                ],
                //预估终生付费
                'lifetime_money' => [
                    'sum(overview_log.lifetime_money * (IFNULL(material_authors.weight, 100)/100)) as lifetime_money'
                ],
                //预估终生回本
                'lifetime_roi' => [
                    'IFNULL ( CAST ( ( sum(overview_log.lifetime_money) / sum(data_log.cost) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS lifetime_roi',
                    'sum(overview_log.lifetime_money * (IFNULL(material_authors.weight, 100)/100)) as lifetime_money',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
            ],
            1 => [
                'agent_leader_group_name' => ['forth_ad_log.agent_leader_group_name as agent_leader_group_name'],
                'author' => ['material_log.author as author'],
                'department' => ['user_position.platform_id', 'user_position.department_id', 'user_position.department_name as department'],
                'department_group' => ['user_position.platform_id', 'user_position.department_group_id', 'user_position.department_group_name as department_group'],
                'department_platform' => ['user_position.platform_id', 'user_position.department_group_id', 'user_position.platform_name as department_platform'],
                'material_num' => ['COUNT(DISTINCT material_log.platform, material_log.material_id) as material_num',
                    "COUNT(DISTINCT material_log.platform, material_log.material_id, IF(material_log.is_del = '1', TRUE, null)) as material_num_del"],
                'material_file_num' => ["COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '0', TRUE, null)) as material_file_num",
                    "COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '0' AND material_log.file_is_del = '1', TRUE, null)) as material_file_num_del"],
                'material_file_ext_num' => ["COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '1', TRUE, null)) as material_file_ext_num",
                    "COUNT(DISTINCT material_log.material_file_id, IF(material_log.is_ext = '1' AND material_log.file_is_del = '1', TRUE, null)) as material_file_ext_num_del"],

                //业务数据
                'platform' => ['forth_ad_log.platform as platform'],
                'os' => ['forth_ad_log.os as os'],
                'create_type' => ['forth_ad_log.create_type as create_type'],
                'game_id' => ['forth_ad_log.game_id as game_id'],
                'game_name' => ['forth_ad_log.game_name as game_name'],
                'main_game_id' => ['forth_ad_log.main_game_id as main_game_id'],
                'main_game_name' => ['forth_ad_log.main_game_name as main_game_name'],
                'root_game_id' => ['forth_ad_log.root_game_id as root_game_id'],
                'root_game_name' => ['forth_ad_log.root_game_name as root_game_name'],
                'site_id' => ['forth_ad_log.site_id as site_id'],
                'agent_leader' => ['forth_ad_log.agent_leader as agent_leader'],
                'cost' => ['sum(data_log.cost) as cost'], // 返点后消耗
                'reg_uid_count' => ['sum(overview_log.reg_uid_count) as reg_uid_count'], // 注册数
                'action_uid_reg_rate' => [
                    'sum(overview_log.reg_uid_count) as sum_day_reg_uid_count',
                    'sum(overview_log.sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
                ], // 激活注册率
                'cost_per_reg' => [
                    'IFNULL ( CAST ( ( sum(data_log.cost) / sum(overview_log.reg_uid_count)) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_per_reg',
                    'sum(data_log.cost) as cost',
                    'sum(overview_log.reg_uid_count) as reg_uid_count'
                ], // 注册成本
                'reg_muid_count' => ['sum(overview_log.reg_muid_count) as reg_muid_count'], // 注册设备数
                'action_muid_count' => ['sum(overview_log.action_muid_count) as action_muid_count'], //激活设备数
                'first_day_ltv' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_money) / sum(overview_log.reg_uid_count)) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_ltv',
                    'sum(overview_log.first_day_pay_money) as first_day_pay_money',
                    'sum(overview_log.reg_uid_count) as sum_day_reg_uid_count'
                ], // 首日ltv
                'first_day_pay_count' => ['sum(overview_log.first_day_pay_count) as first_day_pay_count'], // 首日付费人数
                'first_day_pay_rate' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_count) / sum(overview_log.reg_uid_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_pay_rate',
                    'sum(overview_log.first_day_pay_count) as first_day_pay_count',
                    'sum(overview_log.reg_uid_count) as reg_uid_count'
                ], // 首日付费率
                'cost_per_first_day_pay' => [
                    'sum(data_log.cost) as cost',
                    'sum(overview_log.first_day_pay_count) as first_day_pay_count'
                ],
                // 首日付费成本
                'first_day_pay_money' => [
                    'sum(overview_log.first_day_pay_money) as first_day_pay_money'
                ],
                // 7日付费金额
                'seventh_day_pay_money' => [
                    'sum(overview_log.seventh_day_pay_money) as seventh_day_pay_money'
                ],
                // 15日付费金额
                'fifteenth_day_pay_money' => [
                    'sum(overview_log.fifteenth_day_pay_money) as fifteenth_day_pay_money'
                ],
                // 30日付费金额
                'thirty_day_pay_money' => [
                    'sum(overview_log.thirty_day_pay_money) as thirty_day_pay_money'
                ],
                'first_day_arppu' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_money) / sum(overview_log.first_day_pay_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_arppu',
                    'sum(overview_log.first_day_pay_money) as first_day_pay_money',
                    'sum(overview_log.first_day_pay_count) as first_day_pay_count'
                ], // 首日arppu
                'first_day_roi' => [
                    'IFNULL ( CAST ( ( sum(overview_log.first_day_pay_money) / sum(data_log.cost) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS first_day_roi',
                    'sum(overview_log.first_day_pay_money) as first_day_pay_money',
                    'sum(data_log.cost) as cost'], // 首日roi
                'first_day_roi_standard_value' => [
                    'SUM(data_log.1_day_total_standard_value) as 1_day_total_standard_value',
                    'SUM(data_log.cost) as cost'
                ],
                'total_roi' => [
                    'sum(overview_log.total_pay_money) as sum_total_pay_money',
                    'sum(data_log.cost) as cost'
                ], // 累计roi
                'rate_day_roi_7' => [
                    'SUM(overview_log.sum_seventh_day_pay_money) as sum_seventh_day_pay_money',
                    'SUM(data_log.cost) as cost'
                ],
                'rate_day_roi_30' => [
                    'SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money',
                    'SUM(data_log.cost) as cost'
                ],
                'rate_day_roi_7_standard_value' => [
                    'SUM(data_log.7_day_total_standard_value) as 7_day_total_standard_value',
                    'SUM(data_log.cost) as cost'
                ],
                'rate_day_roi_30_standard_value' => [
                    'SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value',
                    'SUM(data_log.cost) as cost'
                ],
                'rate_day_standard_30' => [
                    'SUM(overview_log.sum_thirty_day_pay_money) as sum_thirty_day_pay_money',
                    'SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value',
                ],
                //预估终生付费
                'lifetime_money' => [
                    'sum(overview_log.lifetime_money * (IFNULL(material_authors.weight, 100)/100)) as lifetime_money'
                ],
                //预估终生回本
                'lifetime_roi' => [
                    'IFNULL ( CAST ( ( sum(overview_log.lifetime_money) / sum(data_log.cost) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS lifetime_roi',
                    'sum(overview_log.lifetime_money * (IFNULL(material_authors.weight, 100)/100)) as lifetime_money',
                    'sum(data_log.cost * (IFNULL(material_authors.weight, 100)/100)) as cost'
                ],
            ]
        ]
    ];
    const AD_LOG = [
        0 => [
            'media_type' => ['ad_log.media_type'],
            'platform' => ['ad_log.platform'],
            'os' => ['game.os'],
            'create_type' => ['ad_log.create_type'],
            'account_id' => ['ad_log.account_id'],
            'ad2_id' => ['ad_log.ad2_id'],
            'ad2_name' => ['ad_log.ad2_name'],
            'site_id' => ['ad_log.site_id'],

            //通用属性
            'ad2_create_time' => ['ad_log.ad2_create_time'],
        ]
    ];
    const DATA_LOG = [
        0 => [
            'cost_date' => ['data_log.cost_date'],
            'cost' => ['SUM(data_log.cost) as cost'],
            'cost_per_reg' => ['SUM(data_log.cost) as cost'],
            'cost_process' => ["SUM(if(cost_date = CURRENT_DATE,data_log.ori_cost,0)) as sum_ori_cost_for_cost_process"],
            'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost'],
            'first_day_roi' => ['SUM(data_log.cost) as cost'],
            'first_day_roi_standard_value' => [
                "sum(cost*day_1_standard_value) AS 1_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'total_roi' => ['SUM(data_log.cost) as cost'],
            'cost_in_30' => ["SUM( IF ( cost_date BETWEEN signature_create.insert_time AND adddate( signature_create.insert_time, INTERVAL '30' DAY ), ori_cost, 0 ) ) AS cost_in_30"],
            'rate_day_roi_7' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_30' => ['SUM(data_log.cost) as cost'],
            'rate_day_roi_7_standard_value' => [
                "sum(cost*day_7_standard_value) AS 7_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_roi_30_standard_value' => [
                "sum(cost*day_30_standard_value) AS 30_day_total_standard_value",
                'SUM(data_log.cost) as cost'
            ],
            'rate_day_standard_30' => [
                'sum(cost*day_30_standard_value) AS 30_day_total_standard_value',
            ],
        ]
    ];
    const ACCOUNT_LOG = [
        0 => [
            'account_name' => ['account_name'],
        ]
    ];
    const FIRST_AD_LOG = [
        0 => [
//         'ad1_id' => 'ad1_id as ad1_id',
            'ad1_name' => ['ad1_name as ad1_name'],
        ]
    ];
    const FORTH_AD_LOG = [
        0 => [
            'agent_leader_group_name' => ['agent_leader_group.agent_leader_group_name as agent_leader_group_name'],
            'platform' => ['forth_ad_log.platform', "if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'ad2_id' => ['forth_ad_log.ad2_id'],
            'ad3_id' => ['forth_ad_log.ad3_id'],
            'ad4_id' => ['forth_ad_log.ad4_id'],

            'urls' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature", 'forth_ad_log.file_type'],
            'file_type' => ['forth_ad_log.file_type'],

            'os' => ["game.os as os"],
            'create_type' => ['ad_log.create_type as create_type'],
            'game_id' => ['game.game_id as game_id'],
            'game_name' => ['game.game_name as game_name'],
            'main_game_id' => ['game.main_game_id as main_game_id'],
            'main_game_name' => ['game.main_game_name as main_game_name'],
            'root_game_id' => ['game.root_game_id as root_game_id'],
            'root_game_name' => ['game.root_game_name as root_game_name'],
            'site_id' => ['ad_log.site_id as site_id'],
            'agent_leader' => ['agent_site.agent_leader as agent_leader'],

            'author' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'department' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'department_group' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],

            'cost_process' => ["SUM(if(ad_log.opt_status = 'AD_STATUS_ENABLE',ad_log.budget,0)) as sum_budget_for_cost_process"],
        ]
    ];
    const OVERVIEW_LOG = [
        'action_uid_reg_rate' => [
            'SUM(overview_log.day_action_muid_distinct_count) as sum_day_action_muid_distinct_count',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        'reg_muid_count' => ['IFNULL(SUM(overview_log.day_reg_muid_distinct_count), 0 ) as reg_muid_count'],
        'action_muid_count' => ['IFNULL(SUM(overview_log.day_action_muid_distinct_count), 0) as action_muid_count'],
        'first_day_ltv' => [
            'SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        'first_day_pay_count' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'first_day_pay_rate' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count', 'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'cost_per_first_day_pay' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        'first_day_pay_money' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money'],
        'seventh_day_pay_money' => ['SUM(overview_log.day_seventh_day_pay_money) as seventh_day_pay_money'],
        'fifteenth_day_pay_money' => ['SUM(overview_log.day_fifteenth_day_pay_money) as fifteenth_day_pay_money'],
        'thirty_day_pay_money' => ['SUM(overview_log.day_thirty_day_pay_money) as thirty_day_pay_money'],
        'first_day_arppu' => [
            'SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
            'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'
        ],
        'first_day_roi' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money'],
        'reg_uid_count' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'cost_per_reg' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        'total_roi' => ['SUM(overview_log.day_total_pay_money) as total_pay_money'],
        'rate_day_roi_7' => ['SUM(overview_log.day_seventh_day_pay_money) as sum_seventh_day_pay_money'],
        'rate_day_roi_30' => ['SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'],
        'rate_day_standard_30' => ['SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'],
        //预估终生付费
        'lifetime_money' => [
            'SUM(overview_log.lifetime_money) as lifetime_money'
        ],
        //预估终生回本
        'lifetime_roi' => [
            'SUM(overview_log.lifetime_money) as lifetime_money'
        ],
    ];

    //
    const AD_LOG_FILTER = [
        0 => [
//            'os' => 'ad_log.os',
            'platform' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',
            'agent_leader_group_name' => 'agent_leader_group.agent_leader_group_name',
            'where操作的字段名' => '数据表真实字段名',
        ]
    ];
    const DATA_LOG_FILTER = [
        0 => [
            'platform' => 'data_log.platform',
        ]
    ];
    const FORTH_AD_LOG_FILTER = [
        0 => [
            'ad4_id' => 'forth_ad_log.ad4_id',
            'platform' => 'forth_ad_log.platform',
            'agent_leader_group_name' => 'agent_leader_group.agent_leader_group_name',
//            'os' => 'ad_log.platform',
            'platform-site_id' => 'ad_log.site_id',]
    ];
    const MAIN_FILTER = [
        0 => [
//            'platform' => 'ad_log.platform',
//            'os' => 'os',
        ]
    ];
    //
    const OVERVIEW_LOG_FILTER = [
        'platform' => 'overview_log.platform',
    ];
    const AGENT_SITE_FILTER = [
        'agent_leader' => 'agent_site.agent_leader'];
    const GAME_FILTER = [
        'os' => 'game.os',
        'platform-game_id' => 'game_id',
        'platform-main_game_id' => 'main_game_id',
        'platform-root_game_id' => 'root_game_id',
    ];
    const MATERIAL_FILTER = [
        'platform' => 'material.platform',
        'material_name' => 'material.name',
        'platform-material_id' => 'material.material_id',
        'theme_id' => 'material.theme_id',
        'platform-theme_pid' => 'material.theme_id',
        'author' => 'material.author',
        'c_author' => 'material.c_author',
        'a_author' => 'material.a_author',
        'm1_author' => 'material.m1_author',
        'm2_author' => 'material.m2_author',
        'm3_author' => 'material.m3_author',
        'm4_author' => 'material.m4_author',
        'm5_author' => 'material.m5_author',
        'actor' => 'material.actor',
        'shoot' => 'material.shoot',
        'material_create_date' => 'material.insert_time',
        'file_type' => 'material.file_type',
        'original' => 'material.original',
        'effect_grade7' => 'material.effect_grade7',
        'effect_grade30' => 'material.effect_grade30',
        'is_priority' => 'material.is_priority',
        'is_3d' => 'material.is_3d'
    ];
    const MATERIAL_FILE_FILTER = [
        'material_filename' => 'material_file.filename',
        'material_file_id' => 'material_file.id',
        'signature' => 'material_file.signature',
        'file_type' => 'material_file.file_type',
        'size' => "CONCAT(material_file.width, '*', material_file.height)"
    ];


    const AD_CREATE_TIME = [
        0 => 'ad_log.ad2_create_time'
    ];
    //不同广告ad_log和data_log连接条件
    const JOIN_DATA_ON = [
        0 => [
            ['data_log.platform', '=', 'forth_ad_log.platform'],
            ['data_log.ad4_id', '=', 'forth_ad_log.ad4_id'],
            ['data_log.media_type', '=', 'forth_ad_log.media_type']
        ]
    ];
    //
    const JOIN_OVERVIEW_ON = [
        0 => [
            ['forth_ad_log.platform', '=', 'overview_log.platform'],
            ['forth_ad_log.ad4_id', '=', 'overview_log.ad4_id'],
            ['agent_site.media_type_id', '=', 'forth_ad_log.media_type']
        ]
    ];
    //不同广告ad_log和account_log连接条件
    const JOIN_ACCOUNT_ON = [
        0 => [
            ['account_log.platform', '=', 'ad_log.platform'],
            ['account_log.account_id', '=', 'ad_log.account_id']
        ]
    ];
    //不同广告ad_log和first_ad_log连接条件
    const FIRST_AD_LOG_ON = [
        0 => [
            ['first_ad_log.platform', '=', 'ad_log.platform'],
            ['first_ad_log.ad1_id', '=', 'ad_log.ad1_id'],
        ]
    ];
    //不同广告ad_log和forth_ad_log连接条件
    const AD_LOG_ON = [
        0 => [
            ['ad_log.platform', '=', 'forth_ad_log.platform'],
            ['ad_log.ad2_id', '=', 'forth_ad_log.ad2_id'],
            ['ad_log.media_type', '=', 'forth_ad_log.media_type']
        ]
    ];
    const JOIN_MATERIAL_FILE_ON = [
        ['material_log.signature', '=', 'forth_ad_log.signature'],
    ];
    const JOIN_MATERIAL_ON = [
        ['material.platform', '=', 'material_file.platform'],
        ['material.material_id', '=', 'material_file.material_id']
    ];
    const JOIN_MATERIAL_AUTHORS_ON = [
        ['material_authors.platform', '=', 'material_log.platform'],
        ['material_authors.material_id', '=', 'material_log.material_id']
    ];

    const AD_LOG_GROUP_BY = [
        0 => [
            'signature' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
            'platform' => 'ad_log.platform',
            'ad2_id' => 'forth_ad_log.ad2_id',
            'ad4_id' => 'forth_ad_log.ad4_id',
            'root_game_id' => 'game.root_game_id',
            'main_game_id' => 'game.main_game_id',
            'game_id' => 'game.game_id',
            'os' => 'game.os',
            'agent_leader_group_name' => 'agent_leader_group.agent_leader_group_name'
        ]
    ];


    const JOIN_AD_LOG_SELECT = [
        0 => [
            'agent_leader_group_name' => ['agent_leader_group.agent_leader_group_name as agent_leader_group_name'],
            'media_type' => ['ad_log.media_type as media_type'],

            'platform' => ['forth_ad_log.platform'],
            'game_id' => ['game.game_id'],
            'main_game_id' => ['game.main_game_id'],
            'root_game_id' => ['game.root_game_id'],
            'os' => ['game.os'],
            'agent_leader' => ['agent_site.agent_leader'],
            'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['forth_ad_log.account_id'],
            'account_name' => ['account_log.account_name as account_name'],
            'ad1_id' => ['first_ad_log.ad1_id as ad1_id'],
            'ad1_name' => ['first_ad_log.ad1_name as ad1_name'],
            'ad2_id' => ['ad_log.ad2_id'],
            'ad2_name' => ['ad_log.ad2_name'],
            'ad4_id' => ['forth_ad_log.ad4_id as ad4_id'],
            'ad3_name' => ['forth_ad_log.ad3_name as ad3_name'],
            'ad2_create_time' => ['ad_log.ad2_create_time'],
            'ad2_status' => ['ad_log.status as ad2_status'],

            'signature' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'material_id' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"],
            'author' => ["if(ad_log.signature != '',ad_log.signature,forth_ad_log.signature ) AS signature"]
        ]
    ];

    const JOIN_AD_LOG_GROUP_BY = [
        'author' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
        'department' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
        'department_group' => "if(ad_log.signature != '', ad_log.signature, forth_ad_log.signature)",
        'platform' => 'ad_log.platform',
        'ad4_id' => 'forth_ad_log.ad4_id',
        'ad2_id' => 'forth_ad_log.ad2_id',
        'root_game_id' => 'game.root_game_id',
        'main_game_id' => 'game.main_game_id',
        'game_id' => 'game.game_id',
        'os' => 'game.os',
        'agent_leader_group_name' => 'agent_leader_group.agent_leader_group_name'
    ];

    const MAIN_GROUP_BY = [
        'department_group' => ['platform_id', 'department_group_id'],
        'department' => ['platform_id', 'department_id'],
        'department_platform' => ['platform_id'],
        'author' => ['author'],
        'root_game_id' => ['root_game_id'],
        'main_game_id' => ['main_game_id'],
        'game_id' => ['game_id'],
        'os' => ['os'],
        'agent_leader_group_name' => ['agent_leader_group_name'],

    ];
}
