<?php

/**
 * auto generate file.
 * Created at: 2020-04-13 15:06:32
 * auto generate
 */

namespace App\Constant;

use App\Exception\AppException;
use App\Utils\Helpers;

class ADLabClassMap
{
    const OTHER_SETTING_CLASS_NAME = 'ADLabOtherSettingParam';

    /**
     * 获取用处理媒体类名格式
     * @param int $media_type
     * @param string $class_name
     * @return string
     */
    public static function getClassName(int $media_type, string $class_name): string
    {
        $media_name = Helpers::pascal(ucfirst(strtolower(MediaType::CONST_NAME_LIST[$media_type])));

        if ($media_type == 5) {
            $media_name = "ToutiaoProject";
        }

        if ($media_name) {
            $src = "App\\Param\\ADLab\\{$media_name}";
        } else {
            throw new AppException('找不到对应content类的路径');
        }

        return "{$src}\\{$class_name}";
    }
}

