<?php

namespace App\Constant;

class ADAnalysisClickhouseSqlMap
{
    const DATA_MEDIA = 'tanwan_datamedia';
    const TABLE = [
        //素材表
        'material_log' => self::DATA_MEDIA . '.ods_material_log',
        //素材文件表
        'material_file_log' => self::DATA_MEDIA . '.ods_material_file_log',
//        'material_label' => self::DATA_MEDIA . '.ods_material_label',
        //数据总览表
        'overview_log' => [
            'game_overview' => self::DATA_MEDIA . '.ads_day_game_overview_log_common_rmt_all',
            'root_game_overview' => self::DATA_MEDIA . '.ads_day_root_game_overview_log_common_rmt_all'
        ],
        //数值绑定表
        'calc_bind_log' => self::DATA_MEDIA . '.ods_ad_bind_calc_rule_log_join',
        //转化类型
        'convert_log' => self::DATA_MEDIA . '.ods_toutiao_convert_log',
        //agency_change_log代理公司表
        'agency_change_log' => self::DATA_MEDIA . '.ods_media_account_agency_change_log',
        //dim_site_id表
        'site' => self::DATA_MEDIA . '.dim_site_id',
        //广告一级表
//         'ad1_log' => [
//             0 => [
//                 -1 => self::DATA_MEDIA . '.dwd_media_ad1_common_log_join',
//                 0 => self::DATA_MEDIA . '.',
//                 ],
//             self::DATA_MEDIA . '.dwd_media_ad1_common_log_join',
//             MediaType::TOUTIAO => self::DATA_MEDIA . '.ods_toutiao_campaign_log',
//             MediaType::TENCENT => self::DATA_MEDIA . '.ods_tencent_campaign_log',
//             MediaType::KUAISHOU => self::DATA_MEDIA . '.ods_kuaishou_campaign_log',
//         ],
        //广告二级表
        'ad_log' => [
            0 => self::DATA_MEDIA . '.dwd_media_ad_common_log_rmt_all',
//            0 => self::DATA_MEDIA . '.dwd_media_ad_common_log_rmt_tmp_all',
            MediaType::TOUTIAO => self::DATA_MEDIA . '.ods_toutiao_ad_log_rmt_all',
            MediaType::TENCENT => self::DATA_MEDIA . '.ods_tencent_adgroup_log_rmt_all',
            MediaType::KUAISHOU => self::DATA_MEDIA . '.ods_kuaishou_unit_log_rmt_all',
        ],
        'ad2_v_ad3' => [
            0 => self::DATA_MEDIA . '.dwd_media_ad2_v_ad3_common_log_rmt_all',
            MediaType::TOUTIAO => self::DATA_MEDIA . '.ods_toutiao_ad_log_rmt_all',
            MediaType::TENCENT => self::DATA_MEDIA . '.ods_tencent_adgroup_log_rmt_all',
            MediaType::KUAISHOU => self::DATA_MEDIA . '.ods_kuaishou_unit_log_rmt_all',
        ],
        //广告三级表
        'ad3_log' => [
            0 => self::DATA_MEDIA . '.dwd_media_ad3_common_log',
            MediaType::TOUTIAO => self::DATA_MEDIA . '.ods_toutiao_creative_log',
            MediaType::TENCENT => self::DATA_MEDIA . '.ods_tencent_ad_log',
            MediaType::KUAISHOU => self::DATA_MEDIA . '.ods_kuaishou_creative_log',
        ],
        //小时消耗表
        'hour_data_log' => [
            0 => self::DATA_MEDIA . '.dwd_media_ad3_common_day_data_log_rmt_all',
            MediaType::TOUTIAO => self::DATA_MEDIA . '.ods_toutiao_creative_hour_data_log',
            MediaType::TENCENT => self::DATA_MEDIA . '.ods_tencent_ad_hour_data_log',
            MediaType::KUAISHOU => self::DATA_MEDIA . '.ods_kuaishou_creative_hour_data_log',
        ],
        //账户表
        'account_log' => [
            0 => self::DATA_MEDIA . '.dwd_media_account_common_log_rmt',
            MediaType::TOUTIAO => self::DATA_MEDIA . '.ods_toutiao_account_log_rmt',
            MediaType::TENCENT => self::DATA_MEDIA . '.ods_tencent_account_log_rmt',
            MediaType::KUAISHOU => self::DATA_MEDIA . '.ods_kuaishou_account_log_rmt',
        ],
    ];

    const EXTENSION_FIELDS = [
        'account_name' => ['account_id'],
        'account_id' => ['account_name'],
        'ad1_name' => ['ad1_id', 'project_id'],
        'ad1_id' => ['ad1_name', 'project_id'],
        'ad2_name' => ['ad2_id', 'ad1_name', 'ad1_id'],
        'ad2_id' => ['ad2_name', 'ad1_name', 'ad1_id'],
        'ad3_name' => ['ad3_id', 'ad2_id', 'ad2_name', 'ad1_name', 'ad1_id'],
        'ad3_id' => ['ad3_name', 'ad2_id', 'ad2_name', 'ad1_name', 'ad1_id'],
        'material_file_id' => ['material_file_name'],
        'material_id' => ['material_name'],
        'urls' => ['signature', 'file_type'],
        'game_id' => ['game_name'],
        'game_name' => ['game_id'],
        'main_game_id' => ['main_game_name'],
        'main_game_name' => ['main_game_id'],
        'root_game_id' => ['root_game_name'],
        'root_game_name' => ['root_game_id'],
        'site_id' => ['site_name'],
        'aweme_account_id' => ['aweme_account_name'],
        'theme_pid' => ['theme_pname'],
//        'agent_id' => ['agent_name'],
        'clique_id' => ['clique_name'],
        'first_day_roi' => ['first_day_roi_standard_value'],
        'rate_day_roi_2' => ['rate_day_roi_2_standard_value'],
        'rate_day_roi_3' => ['rate_day_roi_3_standard_value'],
        'rate_day_roi_7' => ['rate_day_roi_7_standard_value'],
    ];

    const REAL_GROUP_BY = [
        'account_name' => 'account_id',
        'ad1_name' => 'ad1_id',
        'ad2_name' => 'ad2_id',
        'ad3_name' => 'ad3_id',
    ];

    //必有字段
    const MUST_HAVE_NO_MATTER_WHAT = ['account_id', 'media_type', 'platform', 'port_version', 'cost'];

    //需要三级表的字段
    const NEED_JOIN_THIRD_AD_LOG_FIELDS = ['ad3_id', 'ad3_name', 'talent_account', 'gdt_adcreative_template_id', 'signature', 'material_id', 'material_creator', 'count_ad3', 'tt_material_id', 'tt_material_filename'];

    //业务字段
    const BUSINESS_FIELDS = ['site_id', 'os', 'game_id', 'main_game_id', 'root_game_id'];

    const MAIN_SELECT = [
        'media_type' => ['max(media_type) as media_type'],
        'port_version' => ['max(port_version) as port_version'],
        'project_id' => ['max(project_id) as project_id'],
        //有效消耗
        'standard_reached_cost' => ['sum(standard_reached_cost) AS standard_reached_cost'],
        //无效消耗
        'standard_unreached_cost' => ['sum(standard_unreached_cost) AS standard_unreached_cost'],
        //投放开始时间
        'start_time' => ['ad_log.start_time'],
        //投放结束时间
        'end_time' => ['ad_log.end_time'],
        //素材文件名｜ID
        'material_file_id' => [
            'max(material_file_id) as material_file_id',
            'max(material_file_name) as material_file_name'
        ],
        //素材尺寸
        'size' => ['max(size) as size'],
        //素材名｜ID
        'material_id' => [
            'max(material_id) as material_id',
            'max(material_name) as material_name'
        ],
        //素材标签
        'label_pid' => ['max(material_id) as material_id'],
        //素材细分标签
        'label' => ['max(material_id) as material_id'],
        //创意标题
        'creative_title' => ['max(creative_title) as creative_title'],
        //缩略图
        'urls' => ['max(signature) as signature', 'max(file_type) as file_type'],
        //创意素材操作状态
        'ad3_opt_status' => ['max(ad3_opt_status) as ad3_opt_status'],
        //视频md5值
        'signature' => ['max(signature) as signature'],
        //广告创意创建时间
        'ad3_create_time' => ['max(ad3_create_time) as ad3_create_time'],
        //广告创意更新时间
        'ad3_modify_time' => ['max(ad3_modify_time) as ad3_modify_time'],
        //创意素材状态
        'ad3_status' => ['max(ad3_status) as ad3_status'],
        //二级修改时间
        'ad2_modify_time' => ['max(ad2_modify_time) as ad2_modify_time'],
        //开始消耗时间
        'start_cost_time' => ['max(start_cost_time) as start_cost_time'],
        //深度优化出价
        'deep_cpabid' => ['max(deep_cpabid) as deep_cpabid'],
        //roi系数
        'roi_goal' => ['max(roi_goal) as roi_goal'],

        'platform' => ['max(platform_tmp) as platform'],

        'os' => ['os as os'],

        'agent_leader' => ['agent_leader as agent_leader'],

        'site_id' => [
            'site_id as site_id',
            'max(site_name) as site_name'
        ],

//        'action_track_type' => ['action_track_type as action_track_type'],

        'aweme_account_id' => [
            'aweme_account_id as aweme_account_id',
            'max(aweme_account_name) as aweme_account_name'
        ],

        'agent_id' => [
            'agent_id as agent_id',
            'max(agent_name) as agent_name'
        ],

        'agent_group_id' => [
            'agent_group_id as agent_group_id',
            'max(agent_group_name) as agent_group_name'
        ],

        'agency_full_name' => ['agency_full_name_tmp as agency_full_name'],

        'create_type' => ['create_type as create_type'],

        'clique_id' => [
            'clique_id as clique_id',
            'max(clique_name) as clique_name'
        ],

        'root_game_id' => [
            'root_game_id as root_game_id',
            'max(root_game_name) as root_game_name'
        ],

        'main_game_id' => [
            'main_game_id as main_game_id',
            'max(main_game_name) as main_game_name'
        ],

        'game_id' => [
            'game_id as game_id',
            'max(game_name) as game_name'
        ],

        'ad2_os' => ['ad2_os as ad2_os'],

        'account_id' => ['max(account_id_tmp) as account_id'],

        'account_name' => [
            'max(account_name_tmp) as account_name',
//            'max(account_id) as account_id'
        ],

        'ad1_id' => ['max(ad1_id) as ad1_id'],

        'ad1_name' => [
            'max(ad1_name) as ad1_name',
//            'max(ad1_id) as ad1_id'
        ],

        'ad2_id' => ['max(ad2_id) as ad2_id'],

        'ad2_name' => [
            'max(ad2_name) as ad2_name',
//            'max(ad2_id) as ad2_id'
        ],

        'ad3_id' => ['ad3_id as ad3_id'],

        'ad3_name' => [
            'max(ad3_name) as ad3_name',
//            'ad3_id as ad3_id'
        ],

        'talent_account' => ['talent_account as talent_account'],

        'game_name' => ['max(game_name) as game_name'],

        //默认cost_date就是查询字段
        'cost_date' => ['max(cost_date) as cost_date'],

        'ad2_create_time' => ['ad2_create_time as ad2_create_time'],

        'ad2_status' => ['ad2_status as ad2_status'],
        //计数项数据
        'count' => ['max(count) as count'],

        'count_ad2' => ['max(count_ad2) as count_ad2'],

        'count_ad2_deliveried' => ['max(count_ad2_deliveried) as count_ad2_deliveried'],
        'count_ad2_delivering' => ['max(count_ad2_delivering) as count_ad2_delivering'],
        'count_ad2_undeliveried' => ["max(count_ad2_undeliveried) as count_ad2_undeliveried"],
        //cost不能一起聚合，这个计算要放在外面
        'count_cost_date' => ['max(count_cost_date) as count_cost_date'],
        'count_ad3' => ['max(count_ad3) as count_ad3'],
        /**
         * 业务数据
         */
        'cost' => ['max(cost) as cost'],
        'reg_uid_count' => ['max(reg_uid_count) as reg_uid_count'],
        //激活设备注册率
        'action_uid_reg_rate' => [
            'max(reg_uid_count) as reg_uid_count',
            'max(sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
        ],
        //激活注册率
        'action_reg_rate' => [
            'max(reg_uid_count) as reg_uid_count',
            'max(sum_day_action_muid_count) as sum_day_action_muid_count'
        ],
        //激活注册率（设备）
        'action_reg_rate_device' => [
            'max(sum_day_reg_muid_distinct_count) as sum_day_reg_muid_distinct_count',
            'max(sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
        ],
        //注册成本
        'cost_per_reg' => [
            'if (max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.cost) / max(sub_main_log.reg_uid_count), 4)) AS cost_per_reg',
            'max(cost) as cost',
            'max(reg_uid_count) as reg_uid_count'
        ],
//         'cost_per_reg' => [
//             'IFNULL ( CAST ( (
//         data_log.cost / reg_log.reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_per_reg',
//             'data_log.cost',
//             'reg_log.reg_uid_count'
//         ],
        //注册设备数
        'reg_muid_count' => ['max(reg_muid_count) as reg_muid_count'],
        //激活设备数
        'action_muid_count' => ['max(action_muid_count) as action_muid_count'],
        //首日ltv
//         'first_day_ltv' => ['overview_log.first_day_pay_money', 'overview_log.reg_uid_count'],
        'first_day_ltv' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_money) / max(sub_main_log.reg_uid_count), 4)) AS first_day_ltv',
            'max(first_day_pay_money) as first_day_pay_money',
            'max(reg_uid_count) as reg_uid_count'
        ],
        //首日付费人数
        'first_day_pay_count' => ['max(first_day_pay_count) as first_day_pay_count'],
        //首日付费率
        'first_day_pay_rate' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_count) / max(sub_main_log.reg_uid_count), 4)) AS first_day_pay_rate',
            'max(first_day_pay_count) as first_day_pay_count',
            'max(reg_uid_count) as reg_uid_count'
        ],
        //首日付费成本
        'cost_per_first_day_pay' => [
            'max(cost) as cost',
            'max(first_day_pay_count) as first_day_pay_count'
        ],
        //首日付费金额
        'first_day_pay_money' => ['max(first_day_pay_money) as first_day_pay_money'],
        //首日arppu
//         'first_day_arppu' => ['overview_log.first_day_pay_money', 'overview_log.first_day_pay_count'],
        'first_day_arppu' => [
            'if(max(sub_main_log.first_day_pay_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_money) / max(sub_main_log.first_day_pay_count), 4)) AS first_day_arppu',
            'max(first_day_pay_money) as first_day_pay_money',
            'max(first_day_pay_count) as first_day_pay_count'
        ],
        //首日ROI
//         'first_day_roi' => ['overview_log.first_day_pay_money', 'data_log.ori_cost'],
        'first_day_roi' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_money) / max(sub_main_log.cost), 4)) AS first_day_roi',
            'max(first_day_pay_money) as first_day_pay_money',
            'max(cost) as cost',
            'max(1_day_total_standard_value) as 1_day_total_standard_value'
        ],
        //二回
        'rate_day_roi_2' => [
            'max(sum_second_day_pay_money) as sum_second_day_pay_money',
            'max(cost) cost',
            'max(2_day_total_standard_value) as 2_day_total_standard_value'
        ],
        //三回
        'rate_day_roi_3' => [
            'max(sum_third_day_pay_money) as sum_third_day_pay_money',
            'max(cost) as cost',
            'max(3_day_total_standard_value) as 3_day_total_standard_value'
        ],
        //七回
        'rate_day_roi_7' => [
            'max(sum_seven_day_pay_money) as sum_seven_day_pay_money',
            'max(cost) as cost',
            'max(7_day_total_standard_value) as 7_day_total_standard_value'
        ],
        //累计付费回本
        'total_roi' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_total_pay_money) / max(sub_main_log.cost), 4)) AS total_roi',
            'max(sum_total_pay_money) as sum_total_pay_money',
            'max(cost) as cost'
        ],
        //次留
        'rate_day_stay_2' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.count_is_second_login) / max(sub_main_log.reg_uid_count), 4)) AS rate_day_stay_2',
            'max(count_is_second_login) as count_is_second_login',
            'max(reg_uid_count) as reg_uid_count'
        ],
        //三留
        'rate_day_stay_3' => [
            'max(count_is_third_login) as count_is_third_login',
            'max(reg_uid_count) as reg_uid_count'
        ],
        //七留
        'rate_day_stay_7' => [
            'max(count_is_seventh_login) as count_is_seventh_login',
            'max(reg_uid_count) as reg_uid_count'
        ],
        //十五留
        'rate_day_stay_15' => [
            'max(count_is_fifteenth_login) as count_is_fifteenth_login',
            'max(reg_uid_count) as reg_uid_count'
        ],

        /**
         * 通用属性（switch在model中特殊处理）
         */
        //开关
        'switch' => ['max(switch) as switch'],
        //操作状态
        'advertising_opt_status' => ['max(advertising_opt_status) as advertising_opt_status'],
        //投放时段
        'advertising_schedule' => ['max(advertising_schedule) as advertising_schedule'],
        //资源位
        'inventory_type' => ['inventory_type as inventory_type'],
        //出价
        'cpa_bid' => ['max(cpa_bid) as cpa_bid'],
        //广告预算
        'budget' => ['max(budget) as budget'],
        //付费方式
        'pricing' => ['max(pricing) as pricing'],
        //综合评分
        'predict_overall_score' => ['max(predict_overall_score) as predict_overall_score'],
        //当天账户币消耗/预算
        'cost_process' => [
            'if(max(sub_main_log.sum_budget_for_cost_process) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_ori_cost_for_cost_process) / max(sub_main_log.sum_budget_for_cost_process), 4)) AS cost_process',
            'max(sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
            'max(sum_budget_for_cost_process) as sum_budget_for_cost_process'
        ],
        /**
         * 通用展示
         */
        //cpc
        'cpc' => ['max(click) as click', 'max(ori_cost) as ori_cost'],
        //点击数
        'click' => ['max(click) as click'],
        //cpm
        'cpm' => ['max(show) as show', 'max(ori_cost) as ori_cost'],
        //show
        'show' => ['max(show) as show'],
        //总花费
        'ori_cost' => ['max(ori_cost) as ori_cost'],
        /**
         * 通用转化
         */
        //点击率
//         'click_rate' => ['data_log.click', 'data_log.`show`'],
        'click_rate' => [
            'if(max(sub_main_log.show) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.click) / max(sub_main_log.show), 4)) AS click_rate',
            'max(click) as click',
            'max(show) as show'
        ],
        //转化成本
        'cost_per_convert' => [
            'max(ori_cost) as ori_cost',
            'max(convert) as convert'
        ],
        //转化数
        'convert' => ['max(convert) as convert'],
        //激活成本
        'cost_per_active' => ['max(ori_cost) as ori_cost', 'max(active_count) as active_count'],
        //付费成本
        'cost_per_pay' => ['max(ori_cost) as ori_cost', 'max(pay_count) as pay_count'],
        //付费数
        'pay_count' => ['max(pay_count) as pay_count'],
        //注册率
        'reg_rate' => ['max(reg_count) as reg_count', 'max(click) as click'],
        //激活率
        'active_rate' => ['max(active_count) as active_count', 'max(click) as click'],
        //激活数
        'active_count' => ['max(active_count) as active_count'],
        //转化率
//         'convert_rate' => ['data_log.`convert`', 'data_log.click'],
        'convert_rate' => [
            'if(max(sub_main_log.click) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.convert) / max(sub_main_log.click), 4)) AS convert_rate',
            'max(convert) as convert',
            'max(click) as click'
        ],
        //注册数
        'reg_count' => ['max(reg_count) as reg_count'],
        //注册成本(媒体)
        'media_cost_per_reg' => [
            'if(max(sub_main_log.reg_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.reg_count), 4)) AS media_cost_per_reg',
            'max(ori_cost) as ori_cost',
            'max(reg_count) as reg_count'
        ],
        //付费率
//         'pay_rate' => ['data_log.pay_count', 'data_log.reg_count'],
        'pay_rate' => [
            'if(max(sub_main_log.reg_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.pay_count) / max(sub_main_log.reg_count), 4)) AS pay_rate',
            'max(pay_count) as pay_count',
            'max(reg_count) as reg_count'
        ],
        //广告投放速度类型
        'flow_control_mode' => ['flow_control_mode as flow_control_mode'],
        /**
         * 账户通用属性
         */
        //公司
        'company' => ['company_tmp as company'],
        //代理商
        'agency_name' => ['max(agency_name_tmp) as agency_name'],
        //总余额
        'balance' => ['max(balance) as balance'],
        //广告主状态
        'account_status' => ['max(account_status_tmp) as account_status'],
        //账户审核拒绝原因
        'reason' => ['max(reason) as reason'],
        //媒体账户负责人
        'account_leader' => ['max(account_leader_tmp) as account_leader'],
        //账户预算
        'account_budget' => ['max(account_budget_tmp) as account_budget'],

        //一级预算
        'ad1_budget' => ['max(ad1_budget) as ad1_budget'],
        //一级状态
        'ad1_status' => ['max(ad1_status) as ad1_status'],
        //推广目的
        'ad1_type' => ['max(ad1_type) as ad1_type'],
        //一级创建时间
        'ad1_create_time' => ['max(ad1_create_time) as ad1_create_time'],
        //一级修改时间
        'ad1_modify_time' => ['max(ad1_modify_time) as ad1_modify_time'],
        //首日付费次数成本
        'cost_first_day_pay_times' => [
//             'IFNULL ( CAST ( (
//         data_log.cost / overview_log.first_day_pay_times
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_first_day_pay_times',
            'max(cost) as cost',
            'max(first_day_pay_times) as first_day_pay_times'
        ],
        //累计付费次数成本
        'cost_total_pay_times' => [
//             'IFNULL ( CAST ( (
//         data_log.cost / overview_log.total_pay_times
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_total_pay_times',
            'max(cost) as cost',
            'max(total_pay_times) as total_pay_times'
        ],
        //首日付费次数
        'first_day_pay_times' => ['max(first_day_pay_times) as first_day_pay_times'],
        //累计付费次数
        'total_pay_times' => ['max(total_pay_times) as total_pay_times'],
        //业务点击数
        'click_count' => ['max(click_count) as click_count'],
        //次留成本
        'cost_day_second_login' => [
//             'IFNULL ( CAST ( (
//         data_log.cost / overview_log.count_is_second_login
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_day_second_login',
            'max(cost) as cost',
            'max(count_is_second_login) as count_is_second_login'
        ],
        //累计付费人数
        'total_pay_count' => ['max(total_pay_count) as total_pay_count'],
        //累计付费金额
        'total_pay_money' => ['max(total_pay_money) as total_pay_money'],
        //上报媒体首日付费金额
        'callback_first_day_pay_money' => ['max(callback_first_day_pay_money) as callback_first_day_pay_money'],
        //上报媒体首日付费人数
        'callback_first_day_pay_uid_count' => ['max(callback_first_day_pay_uid_count) as callback_first_day_pay_uid_count'],
        //注册(根老设备)
        'reg_old_muid_count' => ['max(reg_old_muid_count) as reg_old_muid_count'],
        //注册(集团老设备)
        'reg_old_clique_muid_count' => ['max(reg_old_clique_muid_count) as reg_old_clique_muid_count'],
        //登录三次以上人数
        'day_three_login_uid_count' => ['max(day_three_login_uid_count) as day_three_login_uid_count'],
        //注册(根老设备)占比
        'reg_old_muid_percentage' => [
//             'IFNULL ( CAST ( (
//         overview_log.reg_old_muid_count / overview_log.day_reg_muid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS reg_old_muid_percentage',
            'max(reg_old_muid_count) as reg_old_muid_count',
            'max(day_reg_muid_count) as day_reg_muid_count'
        ],
        //注册(集团老设备)占比
        'reg_old_clique_muid_percentage' => [
//             'IFNULL ( CAST ( (
//         overview_log.reg_old_clique_muid_count / overview_log.day_reg_muid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS reg_old_clique_muid_percentage',
            'max(reg_old_clique_muid_count) as reg_old_clique_muid_count',
            'max(day_reg_muid_count) as day_reg_muid_count'
        ],
        //登录三次以上人数占比
        'day_three_login_uid_percentage' => [
//             'IFNULL ( CAST ( (
//         overview_log.day_three_login_uid_count / overview_log.reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS day_three_login_uid_percentage',
            'max(day_three_login_uid_count) as day_three_login_uid_count',
            'max(reg_uid_count) as reg_uid_count'
        ],
        //首次付费当日充值金额
        'new_pay_day_money' => ['max(new_pay_day_money) as new_pay_day_money'],
        //首次付费当日充值人数
        'new_pay_day_count' => ['max(new_pay_day_count) as new_pay_day_count'],
    ];

//    const SUB_MAIN_SELECT = [
//        'port_version' => ['max(port_version) as port_version'],
//        'platform' => ['any(platform_tmp) as platform'],
//        //素材文件名｜ID
//        'material_file_id' => ['max(material_file_id) as material_file_id', 'max(material_file_name) as material_file_name'],
//        //素材尺寸
//        'size' => ['max(size) as size'],
//        //素材名｜ID
//        'material_id' => ['max(material_id) as material_id', 'max(material_name) as material_name'],
//        //素材标签
//        'label_pid' => ['max(material_id) as material_id'],
//        //素材细分标签
//        'label' => ['max(material_id) as material_id'],
//        //创意标题
//        'creative_title' => ['max(creative_title) as creative_title'],
//        //缩略图
//        'urls' => ['max(signature) as signature', 'max(file_type) as file_type'],
//        //创意素材操作状态
//        'ad3_opt_status' => ['max(ad3_opt_status) as ad3_opt_status'],
//        //视频md5值
//        'signature' => ['max(signature) as signature'],
//        //广告创意创建时间
//        'ad3_create_time' => ['max(ad3_create_time) as ad3_create_time'],
//        //广告创意更新时间
//        'ad3_modify_time' => ['max(ad3_modify_time) as ad3_modify_time'],
//        //创意素材状态
//        'ad3_status' => ['max(ad3_status) as ad3_status'],
//        //二级修改时间
//        'ad2_modify_time' => ['max(ad2_modify_time) as ad2_modify_time'],
//        //开始消耗时间
//        'start_cost_time' => ['max(start_cost_time) as start_cost_time'],
//        //深度优化出价
//        'deep_cpabid' => ['max(deep_cpabid) as deep_cpabid'],
//        //roi系数
//        'roi_goal' => ['max(roi_goal) as roi_goal'],
//
////         'platform' => ['ad_log.platform'],
//        'os' => ['os as os'],
//
//        'agent_leader' => ['agent_leader as agent_leader'],
//
//        'site_id' => ['site_id as site_id', 'any(site_name) as site_name'],
//
//        'action_track_type' => ['action_track_type as action_track_type'],
//
//        'agent_id' => ['agent_id as agent_id', 'any(agent_name) as agent_name'],
//
//        'agent_group_id' => ['agent_group_id as agent_group_id', 'any(agent_group_name) as agent_group_name'],
//
//        'agency_full_name' => ['agency_full_name_tmp as agency_full_name'],
//
//        'create_type' => ['create_type as create_type'],
//
//        'clique_id' => ['clique_id as clique_id', 'any(clique_name) as clique_name'],
//
//        'root_game_id' => ['root_game_id as root_game_id', 'any(root_game_name) as root_game_name'],
//
//        'main_game_id' => ['main_game_id as main_game_id', 'any(main_game_name) as main_game_name'],
//
//        'game_id' => ['game_id as game_id', 'any(game_name) as game_name'],
//        'ad2_os' => ['ad2_os as ad2_os'],
//        'account_id' => ['any(account_id_tmp) as account_id'],
//        'account_name' => ['any(account_name_tmp) as account_name'],
//        'ad1_id' => ['any(ad1_id) as ad1_id'],
//        'ad1_name' => ['any(ad1_name) as ad1_name'],
//        'ad2_id' => ['any(ad2_id) as ad2_id'],
//        'ad2_name' => ['any(ad2_name) as ad2_name'],
//        'ad3_id' => ['ad3_id as ad3_id'],
//        'ad3_name' => ['any(ad3_name) as ad3_name'],
////        'game_name' => ['IFNULL (ad_log.game_name, overview_log.game_name) as game_name'],
//
//        //默认cost_date就是查询字段
////        'cost_date' => ['data_log.cost_date'],
//        'ad2_create_time' => ['ad2_create_time as ad2_create_time'],
//        'ad2_status' => ['ad2_status as ad2_status'],
//        //计数项数据
//        'count' => ['SUM(count) as count'],
//        'count_ad2' => ['SUM(count_ad2) as count_ad2'],
//        'count_ad2_deliveried' => ['SUM(count_ad2_deliveried) as count_ad2_deliveried'],
//        'count_ad2_delivering' => ['SUM(count_ad2_delivering) as count_ad2_delivering'],
//        'count_ad2_undeliveried' => ['SUM(count_ad2_undeliveried) as count_ad2_undeliveried'],
//        //cost不能一起聚合，这个计算要放在外面
//        'count_cost_date' => ['SUM(count_cost_date) as count_cost_date'],
//        'count_ad3' => ['SUM(count_ad3) as count_ad3'],
//        /**
//         * 业务数据
//         */
//        'standard_reached_cost' => [
//            '0 as standard_reached_cost',
//        ],
//        'standard_unreached_cost' => [
//            'SUM(day_30_standard_value) as day_30_standard_value',
//            'SUM(sum_thirty_day_pay_money) as sum_thirty_day_pay_money',
//            'SUM(cost) as cost'
//        ],
//        'cost' => ['SUM(cost) as cost'],
//        'reg_uid_count' => ['SUM(reg_uid_count) as reg_uid_count'],
//        //激活设备注册率
//        'action_uid_reg_rate' => [
//            'SUM(reg_uid_count) as reg_uid_count',
//            'SUM(sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
//        ],
//        //激活注册率
//        'action_reg_rate' => [
//            'SUM(reg_uid_count) as reg_uid_count',
//            'SUM(sum_day_action_muid_count) as sum_day_action_muid_count'
//        ],
//        //激活注册率（设备）
//        'action_reg_rate_device' => [
//            'SUM(sum_day_reg_muid_distinct_count) as sum_day_reg_muid_distinct_count',
//            'SUM(sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
//        ],
//        //注册成本
//        'cost_per_reg' => [
//            'SUM(cost) as cost',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //TODO::临时注册成本
//
////         'cost_per_reg' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / reg_log.reg_uid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_per_reg',
////             'data_log.cost',
////             'reg_log.reg_uid_count'
////         ],
//        //注册设备数
//        'reg_muid_count' => ['SUM(reg_muid_count) as reg_muid_count'],
//        //激活设备数
//        'action_muid_count' => ['SUM(action_muid_count) as action_muid_count'],
//        //首日ltv
////         'first_day_ltv' => ['overview_log.first_day_pay_money', 'overview_log.reg_uid_count'],
//        'first_day_ltv' => [
//            'SUM(first_day_pay_money) as first_day_pay_money',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //首日付费人数
//        'first_day_pay_count' => ['SUM(first_day_pay_count) as first_day_pay_count'],
//        //首日付费率
//        'first_day_pay_rate' => [
//            'SUM(first_day_pay_count) as first_day_pay_count',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //首日付费成本
//        'cost_per_first_day_pay' => ['SUM(cost) as cost', 'SUM(first_day_pay_count) as first_day_pay_count'],
//        //首日付费金额
//        'first_day_pay_money' => ['SUM(first_day_pay_money) as first_day_pay_money'],
//        //首日arppu
////         'first_day_arppu' => ['overview_log.first_day_pay_money', 'overview_log.first_day_pay_count'],
//        'first_day_arppu' => [
//            'SUM(first_day_pay_money) as first_day_pay_money',
//            'SUM(first_day_pay_count) as first_day_pay_count'
//        ],
//        //首日ROI
////         'first_day_roi' => ['overview_log.first_day_pay_money', 'data_log.ori_cost'],
//        'first_day_roi' => [
//            'SUM(first_day_pay_money) as first_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(1_day_total_standard_value) as 1_day_total_standard_value'
//        ],
//        //二回
//        'rate_day_roi_2' => [
//            'SUM(sum_second_day_pay_money) as sum_second_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(2_day_total_standard_value) as 2_day_total_standard_value'
//        ],
//        //三回
//        'rate_day_roi_3' => [
//            'SUM(sum_third_day_pay_money) as sum_third_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(3_day_total_standard_value) as 3_day_total_standard_value'
//        ],
//        //七回
//        'rate_day_roi_7' => [
//            'SUM(sum_seven_day_pay_money) as sum_seven_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(7_day_total_standard_value) as 7_day_total_standard_value'
//        ],
//        //累计付费回本
//        'total_roi' => [
//            'SUM(sum_total_pay_money) as sum_total_pay_money',
//            'SUM(cost) as cost'
//        ],
//        //次留
//        'rate_day_stay_2' => [
//            'SUM(count_is_second_login) as count_is_second_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //三留
//        'rate_day_stay_3' => [
//            'SUM(count_is_third_login) as count_is_third_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //七留
//        'rate_day_stay_7' => [
//            'SUM(count_is_seventh_login) as count_is_seventh_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //十五留
//        'rate_day_stay_15' => [
//            'SUM(count_is_fifteenth_login) as count_is_fifteenth_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//
//        /**
//         * 通用属性（switch在model中特殊处理）
//         */
//        //开关
//        'switch' => ['max(switch) as switch'],
//        //操作状态
//        'advertising_opt_status' => ['max(advertising_opt_status) as advertising_opt_status'],
//        //投放时段
//        'advertising_schedule' => ['max(advertising_schedule) as advertising_schedule'],
//        //资源位
//        'inventory_type' => ['inventory_type as inventory_type'],
//        //出价
//        'cpa_bid' => ['max(cpa_bid) as cpa_bid'],
//        //广告预算
//        'budget' => ['max(budget) as budget'],
//        //付费方式
//        'pricing' => ['max(pricing) as pricing'],
//        //当天账户币消耗/预算
//        'cost_process' => [
//            'SUM(sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
//            'SUM(sum_budget_for_cost_process) as sum_budget_for_cost_process'
//        ],
//        /**
//         * 通用展示
//         */
//        //cpc
//        'cpc' => ['SUM(click) as click', 'SUM(ori_cost) as ori_cost'],
//        //点击数
//        'click' => ['SUM(click) as click'],
//        //cpm
//        'cpm' => ['SUM(`show`) as show', 'SUM(ori_cost) as ori_cost'],
//        //show
//        'show' => ['SUM(`show`) as show'],
//        //总花费
//        'ori_cost' => ['SUM(ori_cost) as ori_cost'],
//        /**
//         * 通用转化
//         */
//        //点击率
////         'click_rate' => ['data_log.click', 'data_log.`show`'],
//        'click_rate' => [
//            'SUM(click) as click',
//            'SUM(`show`) as show'
//        ],
//        //转化成本
//        'cost_per_convert' => ['SUM(ori_cost) as ori_cost', 'SUM(`convert`) as convert'],
//        //转化数
//        'convert' => ['SUM(`convert`) as convert'],
//        //激活成本
//        'cost_per_active' => ['SUM(ori_cost) as ori_cost', 'SUM(active_count) as active_count'],
//        //付费成本
//        'cost_per_pay' => ['SUM(`convert`) as convert', 'SUM(pay_count) as pay_count'],
//        //付费数
//        'pay_count' => ['SUM(pay_count) as pay_count'],
//        //注册率
//        'reg_rate' => ['SUM(reg_count) as reg_count', 'SUM(click) as click'],
//        //激活率
//        'active_rate' => ['SUM(active_count) as active_count', 'SUM(click) as click'],
//        //激活数
//        'active_count' => ['SUM(active_count) as active_count'],
//        //转化率
////         'convert_rate' => ['SUM(`convert`) as convert', 'SUM(click) as click'],
//        'convert_rate' => [
//            'SUM(`convert`) as convert',
//            'SUM(click) as click'
//        ],
//        //注册数
//        'reg_count' => ['SUM(reg_count) as reg_count'],
//        //注册成本(媒体)
//        'media_cost_per_reg' => [
//            'SUM(ori_cost) as ori_cost',
//            'SUM(reg_count) as reg_count'
//        ],
//        //付费率
////         'pay_rate' => ['data_log.pay_count', 'data_log.reg_count'],
//        'pay_rate' => [
//            'SUM(pay_count) as pay_count',
//            'SUM(reg_count) as reg_count'
//        ],
//        //广告投放速度类型
//        'flow_control_mode' => ['flow_control_mode as flow_control_mode'],
//        /**
//         * 账户通用属性
//         */
//        //公司
//        'company' => ['company_tmp as company'],
//        //代理商
//        'agency_name' => ['max(agency_name_tmp) as agency_name'],
//        //总余额
//        'balance' => ['max(balance) as balance'],
//        //广告主状态
//        'account_status' => ['max(account_status_tmp) as account_status'],
//        //账户审核拒绝原因
//        'reason' => ['max(reason) as reason'],
//        //媒体账户负责人
//        'account_leader' => ['max(account_leader_tmp) as account_leader'],
//        //账户预算
//        'account_budget' => ['max(account_budget_tmp) as account_budget'],
//
//        //一级预算
//        'ad1_budget' => ['max(ad1_budget) as ad1_budget'],
//        //一级状态
//        'ad1_status' => ['max(ad1_status) as ad1_status'],
//        //推广目的
//        'ad1_type' => ['max(ad1_type) as ad1_type'],
//        //一级创建时间
//        'ad1_create_time' => ['max(ad1_create_time) as ad1_create_time'],
//        //一级修改时间
//        'ad1_modify_time' => ['max(ad1_modify_time) as ad1_modify_time'],
//        //首日付费次数成本
//        'cost_first_day_pay_times' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / overview_log.first_day_pay_times
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_first_day_pay_times',
//            'SUM(cost) as cost',
//            'SUM(first_day_pay_times) as first_day_pay_times'
//        ],
//        //累计付费次数成本
//        'cost_total_pay_times' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / overview_log.total_pay_times
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_total_pay_times',
//            'SUM(cost) as cost',
//            'SUM(total_pay_times) as total_pay_times'
//        ],
//        //首日付费次数
//        'first_day_pay_times' => ['SUM(first_day_pay_times) as first_day_pay_times'],
//        //累计付费次数
//        'total_pay_times' => ['SUM(total_pay_times) as total_pay_times'],
//        //业务点击数
//        'click_count' => ['SUM(click_count) as click_count'],
//        //次留成本
//        'cost_day_second_login' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / overview_log.count_is_second_login
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_day_second_login',
//            'SUM(cost) as cost',
//            'SUM(count_is_second_login) as count_is_second_login'
//        ],
//        //累计付费人数
//        'total_pay_count' => ['SUM(total_pay_count) as total_pay_count'],
//        //累计付费金额
//        'total_pay_money' => ['SUM(total_pay_money) as total_pay_money'],
//        //注册(根老设备)
//        'reg_old_muid_count' => ['SUM(reg_old_muid_count) as reg_old_muid_count'],
//        //注册(集团老设备)
//        'reg_old_clique_muid_count' => ['SUM(reg_old_clique_muid_count) as reg_old_clique_muid_count'],
//        //登录三次以上人数
//        'day_three_login_uid_count' => ['SUM(day_three_login_uid_count) as day_three_login_uid_count'],
//        //注册(根老设备)占比
//        'reg_old_muid_percentage' => [
////             'IFNULL ( CAST ( (
////         overview_log.reg_old_muid_count / overview_log.day_reg_muid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS reg_old_muid_percentage',
//            'SUM(reg_old_muid_count) as reg_old_muid_count',
//            'SUM(day_reg_muid_count) as day_reg_muid_count'
//        ],
//        //注册(集团老设备)占比
//        'reg_old_clique_muid_percentage' => [
////             'IFNULL ( CAST ( (
////         overview_log.reg_old_clique_muid_count / overview_log.day_reg_muid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS reg_old_clique_muid_percentage',
//            'SUM(reg_old_clique_muid_count) as reg_old_clique_muid_count',
//            'SUM(day_reg_muid_count) as day_reg_muid_count'
//        ],
//        //登录三次以上人数占比
//        'day_three_login_uid_percentage' => [
////             'IFNULL ( CAST ( (
////         overview_log.day_three_login_uid_count / overview_log.reg_uid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS day_three_login_uid_percentage',
//            'SUM(day_three_login_uid_count) as day_three_login_uid_count',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //首次付费当日充值金额
//        'new_pay_day_money' => ['SUM(new_pay_day_money) as new_pay_day_money'],
//        //首次付费当日充值人数
//        'new_pay_day_count' => ['SUM(new_pay_day_count) as new_pay_day_count'],
//
//    ];

    const AD_LOG = [
        0 => [
            'project_id' => ['any(project_id) as project_id'],
            'port_version' => ['any(ad_log.port_version) as port_version'],
            //素材文件名｜ID
            'material_file_id' => [
                "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'id', cityHash64(ad_log.signature))) as material_file_id",
                "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'filename', cityHash64(ad_log.signature))) as material_file_name"
            ],
            //素材尺寸
            'size' => ["any(concat(toString(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'width', cityHash64(ad_log.signature))), '*', toString(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'height', cityHash64(ad_log.signature))))) as size"],
            //素材名｜ID
            'material_id' => [
                "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'material_id', cityHash64(ad_log.signature))) as material_id",
                "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'name', cityHash64(ad_log.signature))) as material_name"
            ],
            //媒体类型
            'media_type' => ['any(ad_log.media_type) as media_type'],
            'platform' => ['any(ad_log.platform) as platform_tmp'],
            //投放开始时间
            'start_time' => ['any(ad_log.start_time) as start_time'],
            //投放结束时间
            'end_time' => ['any(ad_log.end_time) as end_time'],
            //创意标题
            'creative_title' => ['any(ad_log.title) as creative_title'],
            //缩略图
            'urls' => ['any(ad_log.signature) as signature', 'any(ad_log.file_type) as file_type'],
            //创意素材操作状态
            'ad3_opt_status' => ['any(ad_log.ad3_opt_status) as ad3_opt_status'],
            //视频md5值
            'signature' => ['any(ad_log.signature) as signature'],
            //广告创意创建时间
            'ad3_create_time' => ['any(ad_log.creative_create_time) as ad3_create_time'],
            //广告创意更新时间
            'ad3_modify_time' => ['any(ad_log.creative_modify_time) as ad3_modify_time'],

            //创意素材状态
            'ad3_status' => ['any(ad_log.ad3_status) as ad3_status'],
            //二级修改时间
            'ad2_modify_time' => ['any(ad_log.ad2_modify_time) as ad2_modify_time'],
            //开始消耗时间
            'start_cost_time' => ['any(ad_log.start_cost_time) as start_cost_time'],
            //深度优化出价
            'deep_cpabid' => ['AVG(ad_log.deep_cpabid) as deep_cpabid'],

            'roi_goal' => ['AVG(ad_log.roi_goal) as roi_goal'],

            'game_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'game_id', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id)))) as game_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_name', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id))))) as game_name"
            ],
            'main_game_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id)))) as main_game_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_name', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id))))) as main_game_name"
            ],
            'root_game_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_id', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id)))) as root_game_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_name', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id))))) as root_game_name"
            ],
            'clique_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'clique_id', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id)))) as clique_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_name', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id))))) as clique_name"
            ],
            'os' => ["dictGet(tanwan_datahub.dim_game_id_dict, 'os', cityHash64(ad_log.platform, dictGet(tanwan_datamedia.dim_site_game_id_dict, 'game_id', cityHash64(ad_log.platform, ad_log.site_id)))) as os"],
            'ad2_os' => ['ad_log.os as ad2_os'],
            'agent_leader' => ["dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_leader', cityHash64(ad_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', cityHash64(ad_log.platform, ad_log.site_id))), toDateTime(ad_log.ad2_create_time)) as agent_leader"],
//             'game_name' => ['game.game_name'],
            'site_id' => [
                'ad_log.site_id',
                "any(dictGet(tanwan_datahub.dim_site_id_dict, 'site_name', cityHash64(ad_log.platform, ad_log.site_id))) as site_name"],

//            'action_track_type' => ["dictGet(tanwan_datahub.dim_site_id_dict, 'action_track_type', cityHash64(ad_log.platform, ad_log.site_id)) as action_track_type"],

            'aweme_account_id' => [
                "ad_log.map_aweme_account as aweme_account_id",
                "any(dictGet(tanwan_datamedia.ods_live_user_name_log_aweme_account_dict, 'aweme_name', cityHash64(if(dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(ad_log.platform, ad_log.site_id)) = '', map_aweme_account, dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(ad_log.platform, ad_log.site_id))), (ad_log.media_type)), toDateTime((ad_log.ad2_create_time)))) as aweme_account_name"
            ],

            'agent_id' => [
                "dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(ad_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', cityHash64(ad_log.platform, ad_log.site_id))), toDateTime(ad_log.ad2_create_time)) as agent_id",
                "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_name', cityHash64(ad_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', cityHash64(ad_log.platform, ad_log.site_id))), toDateTime(ad_log.ad2_create_time))) as agent_name"
            ],
            'agent_group_id' => [
                "dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id', cityHash64(ad_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', cityHash64(ad_log.platform, ad_log.site_id))), toDateTime(ad_log.ad2_create_time)) as agent_group_id",
                "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_name', cityHash64(ad_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', cityHash64(ad_log.platform, ad_log.site_id))), toDateTime(ad_log.ad2_create_time))) as agent_group_name"
            ],
            'agency_full_name' => ['ad_log.agency_full_name as agency_full_name_tmp'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['any(ad_log.account_id) as account_id_tmp'],
            'account_name' => [
                //因为弱智clickhouse别名不能重复，所以叫account_name_tmp
                "any(ad_log.account_name) as account_name_tmp",
//                 'any(ad_log.account_id) as account_id'
            ],
            'ad1_id' => ['any(ad_log.ad1_id) as ad1_id'],
            'ad1_name' => [
                "any(ad_log.ad1_name) as ad1_name",
//                 "joinGet(tanwan_datamedia.dwd_media_ad1_common_log_join, 'ad1_id', cityHash64(ad_log.platform, ad_log.ad1_id, ad_log.media_type)) as ad1_id"
            ],
            'ad2_id' => ['any(ad_log.ad2_id) as ad2_id'],
            'ad2_name' => ['any(ad_log.ad2_name) as ad2_name'],
            'ad3_id' => ['ad_log.ad3_id as ad3_id'],
            'talent_account' => ['ad_log.talent_account as talent_account'],
            'ad3_name' => ['any(ad_log.title) as ad3_name'],
//         'cost_date' => ['data_log.cost_date as cost_date'],
            'ad2_create_time' => ['ad_log.ad2_create_time as ad2_create_time'],
            'ad2_status' => ['ad_log.status as ad2_status'],

            //计数项数据
            'count' => ['COUNT(DISTINCT ad_log.web_creator) as count'],
            'count_ad2' => ['COUNT(DISTINCT ad_log.ad2_id) as count_ad2'],
            'count_ad2_delivering' => ["COUNT( DISTINCT ad_log.ad2_id,
          IF (ad_log.status='AD_STATUS_DELIVERY_OK', 1, null) ) as count_ad2_delivering"],
            'count_ad2_undeliveried' => ["COUNT( DISTINCT ad_log.ad2_id, IF (ad_log.is_cost=0, 1, null) ) 
         as count_ad2_undeliveried"],
            'count_ad3' => ['COUNT( DISTINCT ad_log.ad3_id ) as count_ad3'],

            /**
             * 通用属性（switch在model中特殊处理）
             */
            //开关
//         'switch' => ['ad_log.opt_status as switch'],
            //操作状态
            'advertising_opt_status' => ['any(ad_log.opt_status) as advertising_opt_status'],
            //投放时段
            'advertising_schedule' => ['any(ad_log.schedule_time) as advertising_schedule'],
            //资源位
            'inventory_type' => ['ad_log.inventory_type as inventory_type'],
            //出价
            'cpa_bid' => ['AVG(ad_log.cpa_bid) as cpa_bid'],
            //广告预算
            'budget' => ['AVG(ad_log.budget) as budget'],
            //广告投放速度类型
            'flow_control_mode' => ['ad_log.flow_control_mode as flow_control_mode'],
            //付费方式
            'pricing' => ['any(ad_log.pricing) as pricing'],
            //综合评分
            'predict_overall_score' => ['any(predict_overall_score) as predict_overall_score'],
            //当天账户币消耗/预算
            'cost_process' =>
                [
                    "SUM(if(ad_log.opt_status = 'AD_STATUS_ENABLE',if(ad_log.budget = -1, 0, ad_log.budget),0)) as sum_budget_for_cost_process",
                    '0 as sum_ori_cost_for_cost_process'
                    ],
            /**
             * 账户通用属性
             */
            //公司
            'company' => ["(ad_log.company) as company_tmp"],
            //代理商
            'agency_name' => ["any(ad_log.agency_name) as agency_name_tmp"],
            //总余额
            'balance' => ["(SUM(DISTINCT(toDecimal64(ad_log.account_id / 10000, 2) + ad_log.total_balance)) - SUM(DISTINCT toDecimal64(ad_log.account_id / 10000, 2))) as balance"],
            //广告主状态
            'account_status' => ['any(ad_log.account_status) as account_status_tmp'],
            //账户审核拒绝原因
            'reason' => ['any(ad_log.reject_message) as reason'],
            //媒体账户负责人
            'account_leader' => ['any(ad_log.account_leader) as account_leader_tmp'],
            //账户预算
            'account_budget' => ['any(ad_log.account_budget) as account_budget_tmp'],

            //一级预算
            'ad1_budget' => ['any(ad_log.ad1_budget) as ad1_budget'],
            //一级状态
            'ad1_status' => ['any(ad_log.ad1_status) as ad1_status'],
            //推广目的
            'ad1_type' => ['any(ad_log.ad1_type) as ad1_type'],
            //一级修改时间
            'ad1_modify_time' => ['any(ad_log.ad1_modify_time) as ad1_modify_time'],
            //一级创建时间
            'ad1_create_time' => ['any(ad_log.ad1_create_time) as ad1_create_time'],

        ],
        //TODO::头条
        MediaType::TOUTIAO => [
            'media_type' => [MediaType::TOUTIAO . ' as media_type'],
            'platform' => ['ad_log.platform'],
            //投放开始时间
            'start_time' => ['ad_log.start_time'],
            //投放结束时间
            'end_time' => ['ad_log.end_time'],
            //创意标题
            'creative_title' => ['ad_log.title as creative_title'],
            //缩略图
            'urls' => ['ad_log.signature', 'ad_log.file_type'],
            //创意素材状态
            'ad3_status' => ['third_ad_log.status as ad3_status'],
            //创意素材操作状态
            'ad3_opt_status' => ['third_ad_log.opt_status as ad3_opt_status'],
            //视频md5值
            'signature' => ['third_ad_log.signature'],
            //广告创意创建时间
            'ad3_create_time' => ['third_ad_log.creative_create_time as ad3_create_time'],
            //广告创意更新时间
            'ad3_modify_time' => ['third_ad_log.creative_modify_time as ad3_modify_time'],
            //二级修改时间
            'ad2_modify_time' => ['ad_log.ad_modify_time as ad2_modify_time'],
            //开始消耗时间
            'start_cost_time' => ['ad_log.start_cost_time'],

            //穿山甲流量细分
            'tt_rit' => ['data_log.rit as tt_rit'],
            //资源位细分
            'tt_inventory_subdivision' => ['data_log.inventory_name as tt_inventory_subdivision'],
            //点击检测连接
            'tt_action_track_url' => ['third_ad_log.action_track_url as tt_action_track_url'],
            //应用名
            'tt_app_name' => ['third_ad_log.app_name as tt_app_name'],
            //创意类型
            'tt_creative_material_mode' => ['third_ad_log.creative_material_mode as tt_creative_material_mode'],
            //场景广告位
            'tt_scene_inventory' => ['third_ad_log.scene_inventory as tt_scene_inventory'],
            //是否使用优选广告位
            'tt_smart_inventory' => ['third_ad_log.smart_inventory as tt_smart_inventory'],
            //文章来源
            'tt_source' => ['third_ad_log.source as tt_source'],
            //副标题
            'tt_sub_title' => ['third_ad_log.sub_title as tt_sub_title'],
            //Android应用下载详情页
            'tt_web_url' => ['third_ad_log.web_url as tt_web_url'],
            //创意标签
            'tt_ad_keywords' => ['third_ad_log.ad_keywords as tt_ad_keywords'],
            //创意分类
            'tt_third_industry_id' => ['third_ad_log.third_industry_id as tt_third_industry_id'],
            //创意投放位置
            'tt_inventory_type' => ['third_ad_log.inventory_type as tt_inventory_type'],
            //智能放量详情
            'tt_auto_extend_targets' => ['ad_log.auto_extend_targets as tt_auto_extend_targets'],

            'tt_audience' => ['ad_log.audience as tt_audience'],
            //包含人群包
//         'tt_retargeting_tags_include' => ['ad_log.retargeting_tags_include as tt_retargeting_tags_include'],
            'tt_retargeting_tags_include' => ['ad_log.retargeting_tags_name_include as tt_retargeting_tags_include'],
            //排除人群包
//         'tt_retargeting_tags_exclude' => ['ad_log.retargeting_tags_exclude as tt_retargeting_tags_exclude'],
            'tt_retargeting_tags_exclude' => ['ad_log.retargeting_tags_name_exclude as tt_retargeting_tags_exclude'],
            //后台定向包ID
            'tt_web_targeting_id' => ['ad_log.web_targeting_id as tt_web_targeting_id'],
            //广告投放时段
            'tt_schedule_time' => ['ad_log.schedule_time as tt_schedule_time'],
            'tt_smart_bid_type' => ['ad_log.smart_bid_type as tt_smart_bid_type'],
            'tt_convert_type' => ['convert_log.convert_type as tt_convert_type'],
            'tt_download_type' => ['ad_log.download_type as tt_download_type'],
            'tt_auto_extend_enabled' => ['ad_log.auto_extend_enabled as tt_auto_extend_enabled'],
            'tt_age' => ['ad_log.age as tt_age'],
            'tt_gender' => ['ad_log.gender as tt_gender'],
            //定向人群包名
            'tt_retargeting_tags_name_include' =>
                ['ad_log.retargeting_tags_name_include as tt_retargeting_tags_name_include'],
            //排除人群包名
            'tt_retargeting_tags_name_exclude' =>
                ['ad_log.retargeting_tags_name_exclude as tt_retargeting_tags_name_exclude'],

//         'platform' => ['ad_log.platform'],
            'game_id' => ['game.game_id', 'game.game_name'],
            'main_game_id' => ['game.main_game_id', 'game.main_game_name'],
            'root_game_id' => ['game.root_game_id', 'game.root_game_name'],
            'clique_id' => ['game.clique_id', 'game.clique_name'],
            'os' => ['game.os'],
            'ad2_os' => ['ad_log.os as ad2_os'],
            'agent_leader' => ['agent_site.agent_leader'],
//             'game_name' => ['game.game_name'],
            'site_id' => ['ad_log.site_id', 'agent_site.site_name'],
            'agent_id' => ['agent_site.agent_id', 'agent_site.agent_name'],
            'agent_group_id' => ['agent_site.agent_group_id', 'agent_site.agent_group_name'],
            'agency_full_name' => ['agency_change_log.agency_full_name'],
            'create_type' => ['ad_log.create_type as create_type'],

            'account_id' => ['ad_log.account_id as account_id'],
            'account_name' => ['account_log.account_name', 'ad_log.account_id as account_id'],
            'ad1_id' => ['first_ad_log.campaign_id as ad1_id'],
            'ad1_name' => ['first_ad_log.campaign_name as ad1_name', 'first_ad_log.campaign_id as ad1_id'],
            'ad2_id' => ['any(ad_log.ad_id) as ad2_id'],
            'ad2_name' => ['ad_log.ad_name as ad2_name', 'any(ad_log.ad_id) as ad2_id'],
            'ad3_id' => ['third_ad_log.creative_id as ad3_id'],
            'ad3_name' => ['third_ad_log.title as ad3_name', 'third_ad_log.creative_id as ad3_id'],
            //cost_date放在最外面特殊处理
//         'cost_date' => 'data_log.cost_date as cost_date',
            'ad2_create_time' => ['ad_log.ad_create_time as ad2_create_time'],
            'ad2_status' => ['ad_log.status as ad2_status'],

            //计数项数据
            'count' => ['COUNT(DISTINCT ad_log.web_creator) as count'],
            'count_ad2' => ['COUNT(DISTINCT ad_log.ad_id) as count_ad2'],
            //TODO 单词拼错了，之后改一下，应该是delivered
            'count_ad2_delivering' => ["COUNT( DISTINCT ad_log.ad_id, 
         IF (ad_log.status='AD_STATUS_DELIVERY_OK', 1, null) ) as count_ad2_delivering"],
            'count_ad2_undeliveried' =>
                ["COUNT( DISTINCT ad_log.ad_id, IF (ad_log.is_cost=0, 1, null) ) as count_ad2_undeliveried"],
            'count_ad3' => ['COUNT( DISTINCT third_ad_log.creative_id ) as count_ad3'],
            /**
             * 通用属性（switch在model中特殊处理）
             */
            //开关
//         'switch' => ['ad_log.opt_status as switch'],
            //操作状态
            'advertising_opt_status' => ['ad_log.opt_status as advertising_opt_status'],
            //投放时段
            'advertising_schedule' => ['ad_log.schedule_time as advertising_schedule'],
            //资源位
            'inventory_type' => ['ad_log.inventory_type'],
            //出价
            'cpa_bid' => ['AVG(ad_log.cpa_bid) as cpa_bid'],
            //广告预算
            'budget' => ['AVG(ad_log.budget) as budget'],
            //当天账户币消耗/预算
            'cost_process' =>
                ["SUM(if(ad_log.opt_status = 'AD_STATUS_ENABLE',ad_log.budget,0)) as sum_budget_for_cost_process"],

            //深度优化出价
            'deep_cpabid' => ['AVG(ad_log.deep_cpabid) as deep_cpabid'],

            'roi_goal' => ['AVG(ad_log.roi_goal) as roi_goal'],

            //广告投放速度类型
            'flow_control_mode' => ['ad_log.flow_control_mode as flow_control_mode'],

            //付费方式
            'pricing' => ['ad_log.pricing as pricing'],
            //综合评分
            'predict_overall_score' => ['any(predict_overall_score) as predict_overall_score'],

            /**
             * 今日头条计划属性
             */
            //学习期状态
            'tt_learning_phase' => ['ad_log.learning_phase as tt_learning_phase'],
            //广告应用下载包名
            'tt_package' => ['ad_log.package as tt_package'],
            //广告投放时间类型
            'tt_schedule_type' => ['ad_log.schedule_type as tt_schedule_type'],
            //convert_id
            'tt_convert_id' => ['ad_log.convert_id as tt_convert_id'],
            //深度转化ROI系数
            'tt_roi_goal' => ['ad_log.roi_goal as tt_roi_goal'],
            //adjust_cpa
            'tt_adjust_cpa' => ['ad_log.adjust_cpa as tt_adjust_cpa'],
            //深度优化方式
            'tt_deep_bid_type' => ['ad_log.deep_bid_type as tt_deep_bid_type'],
            //download_url
            'tt_download_url' => ['ad_log.download_url as tt_download_url'],
            //external_url
            'tt_external_url' => ['ad_log.external_url as tt_external_url'],
            //穿山甲类型
            'tt_union_video_type' => ['ad_log.union_video_type as tt_union_video_type'],
            //预算类型
            'tt_ad2_budget_mode' => ['ad_log.budget_mode as tt_ad2_budget_mode'],
            /**
             * 账户通用属性
             */
            //公司
            'company' => ['account_log.company'],
            //代理商
            'agency_name' => ['account_log.agency_name'],
            //总余额
            'balance' => ['(SUM(DISTINCT(toDecimal64(ad_log.account_id / 10000, 2) + ad_log.total_balance)) - SUM(DISTINCT toDecimal64(ad_log.account_id / 10000, 2))) as balance'],
            //广告主状态
            'account_status' => ['account_log.status as account_status'],
            //账户审核拒绝原因
            'reason' => ['account_log.reason'],
            //媒体账户负责人
            'account_leader' => ['account_log.account_leader'],
            //账户预算
            'account_budget' => ['account_log.account_budget'],
            /**
             * 今日头条账户属性
             */
            //邮箱
            'tt_email' => ['account_log.email as tt_email'],
            //可用现金余额
            'tt_valid_cash' => ['CAST ( SUM( DISTINCT ( account_log.valid_cash + account_log.account_id / 10000 ) ) - SUM ( DISTINCT account_log.account_id / 10000 ) AS DECIMAL (12, 2) ) AS tt_valid_cash'],
            //可用赠款余额
            'tt_valid_grant' => ['CAST ( SUM( DISTINCT ( account_log.valid_grant + account_log.account_id / 10000 ) ) - SUM ( DISTINCT account_log.account_id / 10000 ) AS DECIMAL (12, 2) ) AS tt_valid_grant'],
            //可用返货余额
            'tt_valid_return_goods_abs' => ['CAST ( SUM( DISTINCT ( account_log.valid_return_goods_abs + account_log.account_id / 10000 ) ) - SUM ( DISTINCT account_log.account_id / 10000 ) AS DECIMAL (12, 2) ) AS tt_valid_return_goods_abs'],

            //可用总余额
            'tt_valid_balance' => ['SUM(account_log.valid_balance) as tt_valid_balance'],
            //现金余额
            'tt_cash' => ['SUM(account_log.cash) as tt_cash'],
            //赠款余额
            'tt_grant' => ['SUM(account_log.grant) as tt_grant'],
            //返货余额
            'tt_return_goods_abs' => ['SUM(account_log.return_goods_abs) as tt_return_goods_abs'],
            //返货支出
            'tt_return_goods_cost' => ['SUM(account_log.return_goods_cost) as tt_return_goods_cost'],
            //一级预算
            'ad1_budget' => ['AVG(first_ad_log.budget) as ad1_budget'],
            //一级状态
            'ad1_status' => ['first_ad_log.status as ad1_status'],
            //推广目的
            'ad1_type' => ['first_ad_log.landing_type as ad1_type'],
            //广告组预算类型
            'tt_ad1_budget_mode' => ['first_ad_log.budget_mode as tt_ad1_budget_mode '],
            //一级修改时间
            'ad1_modify_time' => ['first_ad_log.campaign_modify_time as ad1_modify_time'],
            //一级创建时间
            'ad1_create_time' => ['first_ad_log.campaign_create_time as ad1_create_time'],

            'target名字' => '计算这个指标本表会用到的字段'
        ],

    ];

    const GENERAL_GROUP_BY = [
        0 => [
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            'media_type' => 'media_type',
            //资源位
            'inventory_type' => 'inventory_type',
            'platform' => 'platform',
            'game_id' => 'game_id',
            'main_game_id' => 'main_game_id',
            'root_game_id' => 'root_game_id',
            'clique_id' => 'clique_id',
            'os' => 'os',
            'ad2_os' => "ad2_os",
            'agent_leader' => 'agent_leader',
            'site_id' => 'site_id',
            'port_version' => 'port_version',
            'aweme_account_id' => 'aweme_account_id',
            'agent_id' => 'agent_id',
            'agent_group_id' => 'agent_group_id',
            'agency_full_name' => 'agency_full_name',
            'company' => 'company',
            'create_type' => 'create_type',
            'account_id' => 'account_id',
            'account_name' => 'account_id',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_id',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_id',
            'ad3_id' => 'ad3_id',
            'ad3_name' => 'ad3_id',
            'talent_account' => 'talent_account',
            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
            'ad2_create_time' => 'ad2_create_time',
            'ad2_status' => 'ad2_status',
        ],
        MediaType::TOUTIAO => [
            //穿山甲流量细分
            'tt_rit' => 'tt_rit',
            //细分资源位
            'tt_inventory_subdivision' => 'tt_inventory_subdivision',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            'media_type' => 'media_type',
            //资源位
            'inventory_type' => 'inventory_type',
            'platform' => 'platform',
            'game_id' => 'game_id',
            'main_game_id' => 'main_game_id',
            'root_game_id' => 'root_game_id',
            'clique_id' => 'clique_id',
            'os' => 'os',
            'ad2_os' => "ad2_os",
            'agent_leader' => 'agent_leader',
            'site_id' => 'site_id',
            'port_version' => 'port_version',
            'agent_id' => 'agent_id',
            'agent_group_id' => 'agent_group_id',
            'agency_full_name' => 'agency_full_name',
            'company' => 'company',
            'create_type' => 'create_type',
            'account_id' => 'account_id',
            'account_name' => 'account_id',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_id',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_id',
            'ad3_id' => 'ad3_id',
            'ad3_name' => 'ad3_id',
            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
            'ad2_create_time' => 'ad2_create_time',
            'ad2_status' => 'ad2_status',
        ],
    ];

    const AD_LOG_GROUP_BY = [
        0 => [
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            'media_type' => 'media_type',
            //资源位
            'inventory_type' => 'inventory_type',
            'platform' => 'platform',
            'game_id' => 'game_id',
            'main_game_id' => 'main_game_id',
            'root_game_id' => 'root_game_id',
            'clique_id' => 'clique_id',
            'os' => 'os',
            'ad2_os' => "ad2_os",
            'agent_leader' => 'agent_leader',
            'site_id' => 'site_id',
            'agent_id' => 'agent_id',
            'agent_group_id' => 'agent_group_id',
            'agency_full_name' => 'agency_full_name',
            'company' => 'company',
            'create_type' => 'create_type',
            'account_id' => 'account_id',
            'account_name' => 'account_id',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_id',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_id',
            'ad3_id' => 'ad3_id',
            'ad3_name' => 'ad3_id',
            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
            'ad2_create_time' => 'ad2_create_time',
            'ad2_status' => 'ad2_status',
        ],

        MediaType::TOUTIAO => [
            //穿山甲流量细分
            'tt_rit' => 'tt_rit',
            //资源位细分
            'tt_inventory_subdivision' => 'tt_inventory_subdivision',
            //深度优化方式
            'tt_deep_bid_type' => 'tt_deep_bid_type',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //智能放量详情
            'tt_auto_extend_targets' => 'tt_auto_extend_targets',
            //资源位
            'inventory_type' => 'inventory_type',
            'tt_smart_bid_type' => 'tt_smart_bid_type',
            'tt_convert_type' => 'tt_convert_type',
            'tt_download_type' => 'tt_download_type',
            'tt_auto_extend_enabled' => 'tt_auto_extend_enabled',
            'tt_age' => "tt_age",
            'tt_gender' => "tt_gender",
            //定向人群包名
            'tt_retargeting_tags_name_include' => "tt_retargeting_tags_name_include",
            //排除人群包名
            'tt_retargeting_tags_name_exclude' => "tt_retargeting_tags_name_exclude",

            'platform' => 'platform',
            'game_id' => 'game_id',
            'main_game_id' => 'main_game_id',
            'root_game_id' => 'root_game_id',
            'clique_id' => 'clique_id',
            'os' => 'os',
            'ad2_os' => "ad2_os",
            'agent_leader' => 'agent_leader',
            'site_id' => 'site_id',
            'agent_id' => 'agent_id',
            'agent_group_id' => 'agent_group_id',
            'agency_full_name' => 'agency_full_name',
            'company' => 'company',
            'create_type' => 'create_type',
            'account_id' => 'account_id',
            'account_name' => 'account_id',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_id',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_id',
            'ad3_id' => 'ad3_id',
            'ad3_name' => 'ad3_id',
            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
            'ad2_create_time' => 'ad2_create_time',
            'ad2_status' => 'ad2_status',
        ],

    ];

    const AD_LOG_FILTER = [
        0 => [
            'port_version' => 'port_version',
            'media_type' => 'media_type',
            //是否自动扩量
            'is_auto_extend' => 'auto_extend_enabled',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //创建方式
            'create_type' => 'create_type',
            'platform' => 'platform',
            'site_id' => 'platform-site_id',
            'agency_full_name' => 'agency_full_name',
            'account_id' => 'account_id',
            'account_name' => 'account_name',
            'company' => 'company',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_name',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_name',
            'ad2_os' => 'os',
            'ad2_status' => 'status',
            'advertising_opt_status' => 'opt_status',
            'signature' => 'signature',
            'creative_title' => 'title',
            'inventory_type' => 'inventory_type',
            'aweme_account_id' => 'map_aweme_account',
            'file_type' => 'file_type',
            'talent_account' => 'talent_account',
        ],
        MediaType::TOUTIAO => [
            'port_version' => 'port_version',
            //         //资源位细分
//         'tt_inventory_subdivision' => 'tt_inventory_subdivision',
            //是否自动扩量
            'is_auto_extend' => 'auto_extend_enabled',
            //深度优化方式
            'tt_deep_bid_type' => 'deep_bid_type',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //智能放量详情
            'tt_auto_extend_targets' => 'auto_extend_targets',
            'tt_age' => "age",
            'tt_gender' => "gender",
            //定向人群包名
            'tt_retargeting_tags_name_include' => "retargeting_tags_name_include",
            //排除人群包名
            'tt_retargeting_tags_name_exclude' => "retargeting_tags_name_exclude",
            //创建方式
            'create_type' => 'create_type',
            'platform' => 'platform',
//         'os' => 'os',
            'site_id' => 'platform-site_id',
            'account_id' => 'account_id',
            'ad2_id' => 'ad_id',
            'ad2_name' => 'ad_name',
            'ad2_os' => 'os',
            'ad2_status' => 'status',
            'advertising_opt_status' => 'opt_status',
            'inventory_type' => 'inventory_type',
            'tt_smart_bid_type' => 'smart_bid_type',
            'tt_learning_phase' => 'learning_phase',
            'tt_download_type' => 'download_type',
            'tt_auto_extend_enabled' => 'auto_extend_enabled',
            'tt_union_video_type' => 'union_video_type',
            'file_type' => 'file_type',
        ],
    ];

    const DATA_LOG_FILTER = [
        0 => [
            'port_version' => 'port_version',
            'media_type' => 'media_type',
            //是否自动扩量
            'is_auto_extend' => 'auto_extend_enabled',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //创建方式
            'create_type' => 'create_type',
            'platform' => 'platform',
            'site_id' => 'platform-site_id',
            'agency_full_name' => 'agency_full_name',
            'account_id' => 'account_id',
            'account_name' => 'account_name',
            'company' => 'company',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_name',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_name',
            'ad2_os' => 'ad2_os',
            'ad2_status' => 'status',
            'advertising_opt_status' => 'opt_status',
            'signature' => 'signature',
            'creative_title' => 'title',
            'inventory_type' => 'inventory_type',
            'aweme_account_id' => 'map_aweme_account',
            'file_type' => 'file_type',
        ],
        MediaType::TOUTIAO => [
            'port_version' => 'port_version',
            //         //资源位细分
//         'tt_inventory_subdivision' => 'tt_inventory_subdivision',
            //是否自动扩量
            'is_auto_extend' => 'is_auto_extend',
            //深度优化方式
            'tt_deep_bid_type' => 'deep_bid_type',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //智能放量详情
            'tt_auto_extend_targets' => 'auto_extend_targets',
            'tt_age' => "age",
            'tt_gender' => "gender",
            //定向人群包名
            'tt_retargeting_tags_name_include' => "retargeting_tags_name_include",
            //排除人群包名
            'tt_retargeting_tags_name_exclude' => "retargeting_tags_name_exclude",
            //创建方式
            'create_type' => 'create_type',
            'platform' => 'platform',
//         'os' => 'os',
            'site_id' => 'platform-site_id',
            'account_id' => 'account_id',
            'ad2_id' => 'ad_id',
            'ad2_name' => 'ad_name',
            'ad2_os' => 'ad2_os',
            'ad2_status' => 'status',
            'advertising_opt_status' => 'opt_status',
            'inventory_type' => 'inventory_type',
            'tt_smart_bid_type' => 'smart_bid_type',
            'tt_learning_phase' => 'learning_phase',
            'tt_download_type' => 'download_type',
            'tt_auto_extend_enabled' => 'auto_extend_enabled',
            'tt_union_video_type' => 'union_video_type',
            'file_type' => 'file_type',
        ],
    ];

    const OVERVIEW_LOG_FILTER = [
        0 => [
            'port_version' => 'port_version',
            'media_type' => 'media_type',
            //是否自动扩量
            'is_auto_extend' => 'auto_extend_enabled',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //创建方式
            'create_type' => 'create_type',
            'platform' => 'platform',
            'site_id' => 'platform-site_id',
            'agency_full_name' => 'agency_full_name',
            'account_id' => 'account_id',
            'account_name' => 'account_name',
            'company' => 'company',
            'ad1_id' => 'ad1_id',
            'ad1_name' => 'ad1_name',
            'ad2_id' => 'ad2_id',
            'ad2_name' => 'ad2_name',
            'ad2_os' => 'ad2_os',
            'ad2_status' => 'status',
            'advertising_opt_status' => 'opt_status',
            'signature' => 'signature',
            'creative_title' => 'title',
            'inventory_type' => 'inventory_type',
            'aweme_account_id' => 'map_aweme_account',
            'file_type' => 'file_type',
        ],
        MediaType::TOUTIAO => [
            'port_version' => 'port_version',
            //         //资源位细分
//         'tt_inventory_subdivision' => 'tt_inventory_subdivision',
            //是否自动扩量
            'is_auto_extend' => 'auto_extend_enabled',
            //深度优化方式
            'tt_deep_bid_type' => 'deep_bid_type',
            //广告投放速度类型
            'flow_control_mode' => 'flow_control_mode',
            //智能放量详情
            'tt_auto_extend_targets' => 'auto_extend_targets',
            'tt_age' => "age",
            'tt_gender' => "gender",
            //定向人群包名
            'tt_retargeting_tags_name_include' => "retargeting_tags_name_include",
            //排除人群包名
            'tt_retargeting_tags_name_exclude' => "retargeting_tags_name_exclude",
            //创建方式
            'create_type' => 'create_type',
            'platform' => 'platform',
//         'os' => 'os',
            'site_id' => 'platform-site_id',
            'account_id' => 'account_id',
            'ad2_id' => 'ad_id',
            'ad2_name' => 'ad_name',
            'ad2_os' => 'ad2_os',
            'ad2_status' => 'status',
            'advertising_opt_status' => 'opt_status',
            'inventory_type' => 'inventory_type',
            'tt_smart_bid_type' => 'smart_bid_type',
            'tt_learning_phase' => 'learning_phase',
            'tt_download_type' => 'download_type',
            'tt_auto_extend_enabled' => 'auto_extend_enabled',
            'tt_union_video_type' => 'union_video_type',
            'file_type' => 'file_type',
        ],
    ];

    const DATA_LOG = [
        0 => [
            /**
             * ad_log的部分
             **/
            //投放开始时间
//            'start_time' => ['any(ad_log.start_time) as start_time'],
            //投放结束时间
//            'end_time' => ['any(ad_log.end_time) as end_time'],
            //素材文件名｜ID
            'material_file_id' => [
                "0 as material_file_id",
                "'' as material_file_name"
            ],
            //素材尺寸
            'size' => ["'' as size"],
            //素材名｜ID
            'material_id' => [
                "0 as material_id",
                "'' as material_name"
            ],
            //创意标题
            'creative_title' => ["'' as creative_title"],
            //缩略图
            'urls' => ["'' as signature", "'' as file_type"],
            //创意素材操作状态
            'ad3_opt_status' => ["'' as ad3_opt_status"],
            //视频md5值
            'signature' => ["any(data_log.signature) as signature"],
            //广告创意创建时间
            'ad3_create_time' => ["toDateTime('1970-01-01') as ad3_create_time"],
            //广告创意更新时间
            'ad3_modify_time' => ["toDateTime('1970-01-01') as ad3_modify_time"],

            //创意素材状态
            'ad3_status' => ["'' as ad3_status"],
            //二级修改时间
            'ad2_modify_time' => ["toDateTime('1970-01-01') as ad2_modify_time"],
            //开始消耗时间
            'start_cost_time' => ["toDateTime('1970-01-01') as start_cost_time"],
            //深度优化出价
            'deep_cpabid' => ['0 as deep_cpabid'],
            'roi_goal' => ['0 as roi_goal'],

            //计数项数据
            'count' => ['0 as count'],
            'count_ad2' => ['0 as count_ad2'],
            'count_ad2_delivering' => ['0 as count_ad2_delivering'],
            'count_ad2_undeliveried' => ['0 as count_ad2_undeliveried'],
            'count_ad3' => ['0 as count_ad3'],
            //开关
//            'switch' => ["'' as switch"],
            //操作状态
            'advertising_opt_status' => ["'' as advertising_opt_status"],
            //投放时段
            'advertising_schedule' => ["'' as advertising_schedule"],
            //出价
            'cpa_bid' => ['0 as cpa_bid'],
            //广告预算
            'budget' => ['0 as budget'],
            //付费方式
            'pricing' => ["'' as pricing"],
            //综合评分
            'predict_overall_score' => ["'' as predict_overall_score"],
            //当天账户币消耗/预算
//            'cost_process' => ['0 as sum_budget_for_cost_process'],
            /**
             * 账户通用属性
             */
            //代理商
            'agency_name' => ["'' as agency_name_tmp"],
            //总余额
            'balance' => ['0 as balance'],
            //广告主状态
            'account_status' => ["'' as account_status_tmp"],
            //账户审核拒绝原因
            'reason' => ["'' as reason"],
            //媒体账户负责人
            'account_leader' => ["'' as account_leader_tmp"],
            //账户预算
            'account_budget' => ['0 as account_budget_tmp'],

            //一级预算
            'ad1_budget' => ['0 as ad1_budget'],
            //一级状态
            'ad1_status' => ["'' as ad1_status"],
            //推广目的
            'ad1_type' => ["'' as ad1_type"],
            //一级修改时间
            'ad1_modify_time' => ["toDateTime('1970-01-01') as ad1_modify_time"],
            //一级创建时间
            'ad1_create_time' => ["toDateTime('1970-01-01') as ad1_create_time"],

            //ad_log中group by的
            //广告投放速度类型
            'flow_control_mode' => ['data_log.flow_control_mode as flow_control_mode'],
            'media_type' => ['any(data_log.media_type) as media_type'],
            //资源位
            'inventory_type' => ['data_log.inventory_type as inventory_type'],
            'platform' => ['any(data_log.platform) as platform_tmp'],
            'game_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'game_id', game_key) as game_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_name', game_key)) as game_name"
            ],
            'main_game_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key) as main_game_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_name', game_key)) as main_game_name"
            ],
            'root_game_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_id', game_key) as root_game_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_name', game_key)) as root_game_name"
            ],
            'clique_id' => [
                "dictGet(tanwan_datahub.dim_game_id_dict, 'clique_id', game_key) as clique_id",
                "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_name', game_key)) as clique_name"
            ],
            'os' => ["dictGet(tanwan_datahub.dim_game_id_dict, 'os', game_key) as os"],
            'ad2_os' => ['data_log.ad2_os as ad2_os'],
            'agent_leader' => ["dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_leader', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date)) as agent_leader"],
            'site_id' => [
                'data_log.site_id as site_id',
                "any(dictGet(tanwan_datahub.dim_site_id_dict, 'site_name', cityHash64(data_log.platform, data_log.site_id))) as site_name"
            ],
//            'action_track_type' => ["dictGet(tanwan_datahub.dim_site_id_dict, 'action_track_type', cityHash64(data_log.platform, data_log.site_id)) as action_track_type"],

            'aweme_account_id' => [
                "data_log.map_aweme_account as aweme_account_id",
                "any(dictGet(tanwan_datamedia.ods_live_user_name_log_aweme_account_dict, 'aweme_name', cityHash64(if(dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(data_log.platform, data_log.site_id)) = '', map_aweme_account, dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(data_log.platform, data_log.site_id))), (data_log.media_type)), toDateTime((data_log.ad2_create_time)))) as aweme_account_name"
            ],

            'agent_id' => [
                "dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date)) as agent_id",
                "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_name', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_name"
            ],
            'agent_group_id' => [
                "dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date)) as agent_group_id",
                "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_name', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_group_name"
            ],
            'agency_full_name' => ['data_log.agency_full_name as agency_full_name_tmp'],
            'company' => ['data_log.company as company_tmp'],
            'create_type' => ['data_log.create_type as create_type'],
            'account_id' => ['any(data_log.account_id) as account_id_tmp'],
            'account_name' => ['any(data_log.account_name) as account_name_tmp'],
            'ad1_id' => ['any(data_log.ad1_id) as ad1_id'],
            'ad1_name' => ['any(data_log.ad1_name) as ad1_name'],
            'ad2_id' => ['any(data_log.ad2_id) as ad2_id'],
            'ad2_name' => ['any(data_log.ad2_name) as ad2_name'],
            'ad3_id' => ['data_log.ad3_id as ad3_id'],
            'ad3_name' => ['any(data_log.title) as ad3_name'],
            'talent_account' => ['data_log.talent_account as talent_account'],
//            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
            'ad2_create_time' => ['data_log.map_ad2_create_time as ad2_create_time'],
            'ad2_status' => ['data_log.status as ad2_status'],

            /**
             * data_log原本的
             **/
            'project_id' => ['0 as project_id'],
            'port_version' => ['any(data_log.port_version) as port_version'],
            //有效消耗
            'standard_reached_cost' => [
                '0 as standard_reached_cost'
//                'SUM(data_log.cost) as cost',
//                "any(dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_30_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as day_30_standard_value",
//                '0 as sum_thirty_day_pay_money'
            ],
            //无效消耗
            'standard_unreached_cost' => [
                '0 as standard_unreached_cost',
//                'SUM(data_log.cost) as cost',
//                "any(dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_30_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as day_30_standard_value",
//                '0 as sum_thirty_day_pay_money'
            ],
            //首日付费次数成本
            'cost_first_day_pay_times' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_times'],
            //首日付费次数成本
            'cost_total_pay_times' => ['SUM(data_log.cost) as cost', '0 as total_pay_times'],
            //次留成本
            'cost_day_second_login' => ['SUM(data_log.cost) as cost', '0 as count_is_second_login'],

            'cost_date' => ['data_log.cost_date as cost_date'],
            'count_ad2_deliveried' =>
                ['COUNT( DISTINCT data_log.ad2_id, IF ( data_log.cost > 0, 1, NULL ) ) AS count_ad2_deliveried'],
            'count_cost_date' => ['COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, 1, null) ) 
         as count_cost_date'],
            'cost' => ['SUM(data_log.cost) as cost'],
            'cost_per_reg' => ['SUM(data_log.cost) as cost', '0 as reg_uid_count'],
            'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_count'],
            'first_day_roi' => [
                'SUM(data_log.cost) as cost',
                '0 as first_day_pay_money',
                "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_1_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 1_day_total_standard_value"
                ],
            'total_roi' => ['SUM(data_log.cost) as cost', '0 as sum_total_pay_money'],
            'cost_process' => [
                '0 as sum_budget_for_cost_process',
                "SUM(if(data_log.cost_date = today(), data_log.ori_cost, 0)) as sum_ori_cost_for_cost_process"
            ],
            'cpc' => ['SUM(data_log.click) as click', 'SUM(data_log.ori_cost) as ori_cost'],
            'click' => ['SUM(data_log.click) as click'],
            'cpm' => ['SUM(data_log.`show`) as `show`', 'SUM(data_log.ori_cost) as ori_cost'],
            'show' => ['SUM(data_log.`show`) as `show`'],
            'ori_cost' => ['SUM(data_log.ori_cost) as ori_cost'],
            'click_rate' => ['SUM(data_log.click) as click', 'SUM(data_log.`show`) as `show`'],
            'cost_per_convert' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`'],
            'convert' => ['SUM(data_log.`convert`) as `convert`'],
            'cost_per_active' => ['SUM(data_log.active) as active_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'cost_per_pay' => ['SUM(data_log.pay_count) as pay_count', 'SUM(data_log.ori_cost) as ori_cost'],
            'pay_count' => ['SUM(data_log.pay_count) as pay_count'],
            'reg_rate' => ['SUM(data_log.register) as reg_count', 'SUM(data_log.click) as click'],
            'active_rate' => ['SUM(data_log.active) as active_count', 'SUM(data_log.click) as click'],
            'active_count' => ['SUM(data_log.active) as active_count'],
            'convert_rate' => ['SUM(data_log.`convert`) as `convert`', 'SUM(data_log.click) as click'],
            'reg_count' => ['SUM(data_log.register) as reg_count'],
            'media_cost_per_reg' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.register) as reg_count'],
            'pay_rate' => ['SUM(data_log.pay_count) as pay_count', 'SUM(data_log.register) as reg_count'],
            //二回
            'rate_day_roi_2' => [
                '0 as sum_second_day_pay_money',
                'SUM(data_log.cost) as cost',
                "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_2_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 2_day_total_standard_value"
                ],
            //三回
            'rate_day_roi_3' => [
                '0 as sum_third_day_pay_money',
                'SUM(data_log.cost) as cost',
                "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_3_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 3_day_total_standard_value"
            ],
            //七回
            'rate_day_roi_7' => [
                '0 as sum_seven_day_pay_money',
                'SUM(data_log.cost) as cost',
                "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_7_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 7_day_total_standard_value"
            ],

            /**
             * overview_log
             */
            //激活设备注册率
            'action_uid_reg_rate' => [
                '0 as reg_uid_count',
                '0 as sum_day_action_muid_distinct_count'
            ],
            //激活注册率
            'action_reg_rate' => [
                '0 as reg_uid_count',
                '0 as sum_day_action_muid_count'
            ],
            //激活注册率（设备）
            'action_reg_rate_device' => [
                '0 as sum_day_reg_muid_distinct_count',
                '0 as sum_day_action_muid_distinct_count'
            ],
            //注册设备数
            'reg_muid_count' => ['0 as reg_muid_count'],
            //激活设备数
            'action_muid_count' => ['0 as action_muid_count'],
            //首日LTV
            'first_day_ltv' => [
                '0 as first_day_pay_money',
                '0 as reg_uid_count'
            ],
            //首日付费人数
            'first_day_pay_count' => ['0 as first_day_pay_count'],
            //首日付费率
            'first_day_pay_rate' => [
                '0 as first_day_pay_count',
                '0 as reg_uid_count'
            ],
            //首日付费成本
//            'cost_per_first_day_pay' => ['0 as first_day_pay_count'],
            //首日付费金额
            'first_day_pay_money' => ['0 as first_day_pay_money'],
            //有效消耗
//            'standard_reached_cost' => ['0 as sum_thirty_day_pay_money'],
            //无效消耗
//            'standard_unreached_cost' => ['0 as sum_thirty_day_pay_money'],
            //首日arppu
            'first_day_arppu' => [
                '0 as first_day_pay_money',
                '0 as first_day_pay_count'
            ],
            //首日roi
//            'first_day_roi' => ['0 as first_day_pay_money'],
            //注册成本
//            'cost_per_reg' => ['0 as reg_uid_count'],
            //注册数
            'reg_uid_count' => ['0 as reg_uid_count'],
            //累计付费回本
//            'total_roi' => ['0 as sum_total_pay_money'],
            //次留
            'rate_day_stay_2' => [
                '0 as count_is_second_login',
                '0 as reg_uid_count'
            ],
            //三留
            'rate_day_stay_3' => [
                '0 as count_is_third_login',
                '0 as reg_uid_count'
            ],
            //七留
            'rate_day_stay_7' => [
                '0 as count_is_seventh_login',
                '0 as reg_uid_count'
            ],
            //十五留
            'rate_day_stay_15' => [
                '0 as count_is_fifteenth_login',
                '0 as reg_uid_count'
            ],
            //二回
//            'rate_day_roi_2' => ['0 as sum_second_day_pay_money'],
            //三回
//            'rate_day_roi_3' => ['0 as sum_third_day_pay_money'],
            //首日付费次数
            'first_day_pay_times' => ['0 as first_day_pay_times'],
            //累计付费次数
            'total_pay_times' => ['0 as total_pay_times'],
            //首日付费次数成本
//            'cost_first_day_pay_times' => ['0 as first_day_pay_times'],
            //累计付费次数成本
//            'cost_total_pay_times' => ['0 as total_pay_times'],
            //业务点击数
            'click_count' => ['0 as click_count'],
            //次留成本
//            'cost_day_second_login' => ['0 as count_is_second_login'],
            //累计付费人数
            'total_pay_count' => ['0 as total_pay_count'],
            //累计付费金额
            'total_pay_money' => ['0 as total_pay_money'],
            //上报媒体首日付费金额
            'callback_first_day_pay_money' => ['0 as callback_first_day_pay_money'],
            //上报媒体首日付费人数
            'callback_first_day_pay_uid_count' => ['0 as callback_first_day_pay_uid_count'],
            //注册(根老设备)
            'reg_old_muid_count' => ['0 as reg_old_muid_count'],
            //注册(集团老设备)
            'reg_old_clique_muid_count' => ['0 as reg_old_clique_muid_count'],
            //登录三次以上人数
            'day_three_login_uid_count' => ['0 as day_three_login_uid_count'],
            //注册(根老设备)占比
            'reg_old_muid_percentage' => [
                '0 as reg_old_muid_count',
                '0 as day_reg_muid_count'
            ],
            //注册(集团老设备)占比
            'reg_old_clique_muid_percentage' => [
                '0 as reg_old_clique_muid_count',
                '0 as day_reg_muid_count'
            ],
            //登录三次以上人数占比
            'day_three_login_uid_percentage' => [
                '0 as day_three_login_uid_count',
                '0 as reg_uid_count'
            ],
            //首次付费当日充值金额
            'new_pay_day_money' => ['0 as new_pay_day_money'],
            //首次付费当日充值人数
            'new_pay_day_count' => ['0 as new_pay_day_count'],
            //七回
//            'rate_day_roi_7' => ['0 as sum_seven_day_pay_money'],
        ]

    ];

    const DATA_LOG_GROUP_BY = [
        0 => [
            'cost_date' => 'data_log.cost_date',
        ],
        MediaType::TOUTIAO => [
//            'tt_rit' => 'tt_rit',
//            'tt_inventory_subdivision' => 'tt_inventory_subdivision',
            'cost_date' => 'data_log.cost_date',
        ]
    ];

    const OVERVIEW_LOG = [
        'project_id' => ['0 as project_id'],
        'port_version' => ['any(overview_log.port_version) as port_version'],
        /**
         * ad_log的部分
         **/
        //投放开始时间
//            'start_time' => ['any(ad_log.start_time) as start_time'],
        //投放结束时间
//            'end_time' => ['any(ad_log.end_time) as end_time'],

        //素材文件名｜ID
        'material_file_id' => [
            "0 as material_file_id",
            "'' as material_file_name"
        ],
        //素材尺寸
        'size' => ["'' as size"],
        //素材名｜ID
        'material_id' => [
            "0 as material_id",
            "'' as material_name"
        ],
        //创意标题
        'creative_title' => ["'' as creative_title"],
        //缩略图
        'urls' => ["'' as signature", "'' as file_type"],
        //创意素材操作状态
        'ad3_opt_status' => ["'' as ad3_opt_status"],
        //视频md5值
        'signature' => ["any(overview_log.signature) as signature"],
        //广告创意创建时间
        'ad3_create_time' => ["toDateTime('1970-01-01') as ad3_create_time"],
        //广告创意更新时间
        'ad3_modify_time' => ["toDateTime('1970-01-01') as ad3_modify_time"],

        //创意素材状态
        'ad3_status' => ["'' as ad3_status"],
        //二级修改时间
        'ad2_modify_time' => ["toDateTime('1970-01-01') as ad2_modify_time"],
        //开始消耗时间
        'start_cost_time' => ["toDateTime('1970-01-01') as start_cost_time"],
        //深度优化出价
        'deep_cpabid' => ['0 as deep_cpabid'],
        'roi_goal' => ['0 as roi_goal'],

        //计数项数据
        'count' => ['0 as count'],
        'count_ad2' => ['0 as count_ad2'],
        'count_ad2_delivering' => ['0 as count_ad2_delivering'],
        'count_ad2_undeliveried' => ['0 as count_ad2_undeliveried'],
        'count_ad3' => ['0 as count_ad3'],
        //开关
//        'switch' => ["'' as switch"],
        //操作状态
        'advertising_opt_status' => ["'' as advertising_opt_status"],
        //投放时段
        'advertising_schedule' => ["'' as advertising_schedule"],
        //出价
        'cpa_bid' => ['0 as cpa_bid'],
        //广告预算
        'budget' => ['0 as budget'],
        //付费方式
        'pricing' => ["'' as pricing"],
        //综合评分
        'predict_overall_score' => ["'' as predict_overall_score"],
        //当天账户币消耗/预算
//            'cost_process' => ['0 as sum_budget_for_cost_process'],
        /**
         * 账户通用属性
         */
        //代理商
        'agency_name' => ["'' as agency_name_tmp"],
        //总余额
        'balance' => ['0 as balance'],
        //广告主状态
        'account_status' => ["'' as account_status_tmp"],
        //账户审核拒绝原因
        'reason' => ["'' as reason"],
        //媒体账户负责人
        'account_leader' => ["'' as account_leader_tmp"],
        //账户预算
        'account_budget' => ['0 as account_budget_tmp'],

        //一级预算
        'ad1_budget' => ['0 as ad1_budget'],
        //一级状态
        'ad1_status' => ["'' as ad1_status"],
        //推广目的
        'ad1_type' => ["'' as ad1_type"],
        //一级修改时间
        'ad1_modify_time' => ["toDateTime('1970-01-01') as ad1_modify_time"],
        //一级创建时间
        'ad1_create_time' => ["toDateTime('1970-01-01') as ad1_create_time"],

        //ad_log中group by的
        //广告投放速度类型
        'flow_control_mode' => ['overview_log.flow_control_mode as flow_control_mode'],
        'media_type' => ['any(overview_log.media_type) as media_type'],
        //资源位
        'inventory_type' => ['overview_log.inventory_type as inventory_type'],
        'platform' => ['any(overview_log.platform) as platform_tmp'],
        'game_id' => [
            "dictGet(tanwan_datahub.dim_game_id_dict, 'game_id', game_key) as game_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_name', game_key)) as game_name"
        ],
        'main_game_id' => [
            "dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key) as main_game_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_name', game_key)) as main_game_name"
        ],
        'root_game_id' => [
            "dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_id', game_key) as root_game_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_name', game_key)) as root_game_name"
        ],
        'clique_id' => [
            "dictGet(tanwan_datahub.dim_game_id_dict, 'clique_id', game_key) as clique_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_name', game_key)) as clique_name"
        ],
        'os' => ["dictGet(tanwan_datahub.dim_game_id_dict, 'os', game_key) as os"],
        'ad2_os' => ['overview_log.ad2_os as ad2_os'],
        'agent_leader' => ["dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_leader', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date)) as agent_leader"],
        'site_id' => [
            'overview_log.site_id as site_id',
            "any(dictGet(tanwan_datahub.dim_site_id_dict, 'site_name', cityHash64(overview_log.platform, overview_log.site_id))) as site_name"
        ],
//        'action_track_type' => ["dictGet(tanwan_datahub.dim_site_id_dict, 'action_track_type', cityHash64(overview_log.platform, overview_log.site_id)) as action_track_type"],

        'aweme_account_id' => [
            "overview_log.map_aweme_account as aweme_account_id",
            "any(dictGet(tanwan_datamedia.ods_live_user_name_log_aweme_account_dict, 'aweme_name', cityHash64(if(dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(overview_log.platform, overview_log.site_id)) = '', map_aweme_account, dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(overview_log.platform, overview_log.site_id))), (overview_log.media_type)), toDateTime((overview_log.ad2_create_time)))) as aweme_account_name"
        ],

        'agent_id' => [
            "dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date)) as agent_id",
            "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_name', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_name"
        ],
        'agent_group_id' => [
            "dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date)) as agent_group_id",
            "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_name', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_group_name"
        ],
        'agency_full_name' => ['overview_log.agency_full_name as agency_full_name_tmp'],
        'company' => ['overview_log.company as company_tmp'],
        'create_type' => ['overview_log.create_type as create_type'],
        'account_id' => ['any(overview_log.account_id) as account_id_tmp'],
        'account_name' => ['any(overview_log.account_name) as account_name_tmp'],
        'ad1_id' => ['any(overview_log.ad1_id) as ad1_id'],
        'ad1_name' => ['any(overview_log.ad1_name) as ad1_name'],
        'ad2_id' => ['any(overview_log.ad2_id) as ad2_id'],
        'ad2_name' => ['any(overview_log.ad2_name) as ad2_name'],
        'ad3_id' => ['overview_log.ad3_id as ad3_id'],
        'ad3_name' => ['any(overview_log.title) as ad3_name'],
        'talent_account' => ['overview_log.talent_account as talent_account'],
//            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
        'ad2_create_time' => ['overview_log.ad2_create_time as ad2_create_time'],
        'ad2_status' => ['overview_log.status as ad2_status'],

//        'media_type' => ['overview_log.media_type as media_type'],
        //穿山甲流量细分
        'tt_rit' => ['1 as tt_rit'],
        //资源位细分
        'tt_inventory_subdivision' => ["case
		when overview_log.csite <= '10000' then 'INVENTORY_FEED'
		when overview_log.csite >= '80000'
		and overview_log.csite <= '110001' then 'INVENTORY_FEED'
		when overview_log.csite >= '10001'
		and overview_log.csite <= '10099' then 'INVENTORY_VIDEO_FEED'
		when overview_log.csite >= '26001'
		and overview_log.csite <= '26099' then 'INVENTORY_VIDEO_FEED'
		when overview_log.csite >= '30001'
		and overview_log.csite <= '30099' then 'INVENTORY_HOTSOON_FEED'
		when overview_log.csite >= '40001'
		and overview_log.csite <= '40099' then 'INVENTORY_AWEME_FEED'
		when overview_log.csite = '800000000' then 'INVENTORY_UNION_SPLASH_SLOT'
		when overview_log.csite = '900000000' then 'INVENTORY_UNION_SLOT'
		when overview_log.csite = '33013' then 'INVENTORY_UNIVERSAL'
		when overview_log.csite = '38016' then 'INVENTORY_SEARCH'
		ELSE ''
	END as tt_inventory_subdivision"],

        'gdt_inventory_subdivision' => ["CASE
		WHEN (overview_log.csite=100 OR overview_log.csite = 'SITE_SET_KANDIAN') THEN 'SITE_SET_KANDIAN'
	WHEN (overview_log.csite=101 OR overview_log.csite = 'SITE_SET_QQ_MUSIC_GAME') THEN 'SITE_SET_QQ_MUSIC_GAME'
	WHEN (overview_log.csite=28 OR overview_log.csite = 'SITE_SET_TENCENT_VIDEO') THEN 'SITE_SET_TENCENT_VIDEO'
	WHEN (overview_log.csite=25 OR overview_log.csite = 'SITE_SET_MOBILE_INNER') THEN 'SITE_SET_MOBILE_INNER'
	WHEN (overview_log.csite=27 OR overview_log.csite = 'SITE_SET_TENCENT_NEWS') THEN 'SITE_SET_TENCENT_NEWS'
	WHEN (overview_log.csite=102 OR overview_log.csite = 'SITE_SET_MOMENTS') THEN 'SITE_SET_MOMENTS'
	WHEN (overview_log.csite=15 OR overview_log.csite = 'SITE_SET_MOBILE_UNION') THEN 'SITE_SET_MOBILE_UNION'
	WHEN (overview_log.csite=21 OR overview_log.csite = 'SITE_SET_WECHAT') THEN 'SITE_SET_WECHAT'
	ELSE 'UNKNOWN'
	END as gdt_inventory_subdivision"],
        //自动扩量细分
        'gdt_is_expand_targeting' => ['5 as gdt_is_expand_targeting'],

        //激活设备注册率
        'action_uid_reg_rate' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count',
            'SUM(overview_log.day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'],
        //激活注册率
        'action_reg_rate' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count',
            'SUM(overview_log.day_action_muid_count) as sum_day_action_muid_count'],
        //激活注册率（设备）
        'action_reg_rate_device' => ['SUM(overview_log.day_reg_muid_distinct_count) as sum_day_reg_muid_distinct_count',
            'SUM(overview_log.day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'],
        //注册设备数
        'reg_muid_count' => ['IFNULL(SUM(overview_log.day_reg_muid_distinct_count), 0 ) as reg_muid_count'],
        //激活设备数
        'action_muid_count' => ['IFNULL(SUM(overview_log.day_action_muid_distinct_count), 0) as action_muid_count'],
        //首日LTV
        'first_day_ltv' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        //首日付费人数
        'first_day_pay_count' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        //首日付费率
        'first_day_pay_rate' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        //首日付费成本
        'cost_per_first_day_pay' => [
            '0 as cost',
            'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'
        ],
        //首日付费金额
        'first_day_pay_money' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money'],
        //有效消耗
        'standard_reached_cost' => [
            '0 as standard_reached_cost',
//            '0 as cost',
//            '0 as day_30_standard_value',
//            'SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'
        ],
        //无效消耗
        'standard_unreached_cost' => [
            '0 as standard_unreached_cost',
//            '0 as cost',
//            '0 as day_30_standard_value',
//            'SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'
        ],
        //首日arppu
        'first_day_arppu' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
            'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
        //首日roi
        'first_day_roi' => [
            'SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
            '0 as cost',
            '0 as 1_day_total_standard_value'
        ],
        //注册成本
        'cost_per_reg' => [
            '0 as cost',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //注册数
        'reg_uid_count' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        //累计付费回本
        'total_roi' => [
            '0 as cost',
            'SUM(overview_log.day_total_pay_money) as sum_total_pay_money'
        ],
        //次留
        'rate_day_stay_2' => [
            'SUM(overview_log.day_second_login_count) as count_is_second_login',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //三留
        'rate_day_stay_3' => [
            'SUM(overview_log.day_third_login_count) as count_is_third_login',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //七留
        'rate_day_stay_7' => [
            'SUM(overview_log.day_seventh_login_count) as count_is_seventh_login',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //十五留
        'rate_day_stay_15' => [
            'SUM(overview_log.day_fifteenth_login_count) as count_is_fifteenth_login',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
        ],
        //二回
        'rate_day_roi_2' => [
            'SUM(overview_log.day_second_day_pay_money) as sum_second_day_pay_money',
            '0 as cost',
            '0 as 2_day_total_standard_value'
        ],
        //三回
        'rate_day_roi_3' => [
            'SUM(overview_log.day_third_day_pay_money) as sum_third_day_pay_money',
            '0 as cost',
            '0 as 3_day_total_standard_value'
        ],
        //首日付费次数
        'first_day_pay_times' => ['SUM(overview_log.day_first_day_pay_times) as first_day_pay_times'],
        //累计付费次数
        'total_pay_times' => ['SUM(overview_log.total_pay_times) as total_pay_times'],
        //首日付费次数成本
        'cost_first_day_pay_times' => [
            '0 as cost',
            'SUM(overview_log.day_first_day_pay_times) as first_day_pay_times'
        ],
        //累计付费次数成本
        'cost_total_pay_times' => [
            '0 as cost',
            'SUM(overview_log.total_pay_times) as total_pay_times'
        ],
        //业务点击数
        'click_count' => ['SUM(overview_log.click_count) as click_count'],
        //次留成本
        'cost_day_second_login' => [
            '0 as cost',
            'SUM(overview_log.day_second_login_count) as count_is_second_login'
        ],
        //累计付费人数
        'total_pay_count' => ['SUM(overview_log.day_total_pay_count) as total_pay_count'],
        //累计付费金额
        'total_pay_money' => ['SUM(overview_log.day_total_pay_money) as total_pay_money'],
        //上报媒体首日付费金额
        'callback_first_day_pay_money' => ['SUM(overview_log.callback_first_day_pay_money) as callback_first_day_pay_money'],
        //上报媒体首日付费人数
        'callback_first_day_pay_uid_count' => ['SUM(overview_log.callback_first_day_pay_uid_count) as callback_first_day_pay_uid_count'],
        //注册(根老设备)
        'reg_old_muid_count' =>
            ['SUM(IF(overview_log.is_old_root_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_muid_count'],
        //注册(集团老设备)
        'reg_old_clique_muid_count' =>
            ['SUM(IF(overview_log.is_old_clique_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_clique_muid_count'],
        //登录三次以上人数
        'day_three_login_uid_count' => ['SUM(overview_log.day_three_login_uid_count) as day_three_login_uid_count'],
        //注册(根老设备)占比
        'reg_old_muid_percentage' => [
            'SUM(IF(overview_log.is_old_root_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_muid_count',
            'SUM(overview_log.day_reg_muid_count) as day_reg_muid_count'
        ],
        //注册(集团老设备)占比
        'reg_old_clique_muid_percentage' => [
            'SUM(IF(overview_log.is_old_clique_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_clique_muid_count',
            'SUM(overview_log.day_reg_muid_count) as day_reg_muid_count'
        ],
        //登录三次以上人数占比
        'day_three_login_uid_percentage' => ['SUM(overview_log.day_three_login_uid_count) as day_three_login_uid_count',
            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
        //首次付费当日充值金额
        'new_pay_day_money' => ['SUM(overview_log.day_new_pay_day_money) as new_pay_day_money'],
        //首次付费当日充值人数
        'new_pay_day_count' => ['SUM(overview_log.day_new_pay_day_count) as new_pay_day_count'],
        //七回
        'rate_day_roi_7' => [
            'SUM(overview_log.day_seventh_day_pay_money) as sum_seven_day_pay_money',
            '0 as cost',
            '0 as 7_day_total_standard_value'
        ],

        /**
         * data_log
         */
        //有效消耗
//        'standard_reached_cost' => ['0 as cost', '0 as sum_thirty_day_pay_money'],
        //无效消耗
//        'standard_unreached_cost' => ['0 as cost', '0 as sum_thirty_day_pay_money'],
        //首日付费次数成本
//        'cost_first_day_pay_times' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_times'],
        //首日付费次数成本
//        'cost_total_pay_times' => ['SUM(data_log.cost) as cost', '0 as total_pay_times'],
        //次留成本
//        'cost_day_second_login' => ['SUM(data_log.cost) as cost', '0 as count_is_second_login'],

        'cost_date' => ["toDateTime('1970-01-01') as cost_date"],
        'count_ad2_deliveried' => ['0 as count_ad2_deliveried'],
        'count_cost_date' => ['0 as count_cost_date'],
        'cost' => ['0 as cost'],
//        'cost_per_reg' => ['SUM(data_log.cost) as cost', '0 as reg_uid_count'],
//        'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_count'],
//        'first_day_roi' => [
//            'SUM(data_log.cost) as cost',
//            '0 as first_day_pay_money',
//            '0 as 1_day_total_standard_value'
//        ],
//        'total_roi' => ['SUM(data_log.cost) as cost', '0 as sum_total_pay_money'],
        'cost_process' => ['0 as sum_budget_for_cost_process', '0 as sum_ori_cost_for_cost_process'],
        'cpc' => ['0 as click', '0 as ori_cost'],
        'click' => ['0 as click'],
        'cpm' => ['0 as `show`', '0 as ori_cost'],
        'show' => ['0 as `show`'],
        'ori_cost' => ['0 as ori_cost'],
        'click_rate' => ['0 as click', '0 as `show`'],
        'cost_per_convert' => ['0 as ori_cost', '0 as `convert`'],
        'convert' => ['0 as `convert`'],
        'cost_per_active' => ['0 as active_count', '0 as ori_cost'],
        'cost_per_pay' => ['0 as pay_count', '0 as ori_cost'],
        'pay_count' => ['0 as pay_count'],
        'reg_rate' => ['0 as reg_count', '0 as click'],
        'active_rate' => ['0 as active_count', '0 as click'],
        'active_count' => ['0 as active_count'],
        'convert_rate' => ['0 as `convert`', '0 as click'],
        'reg_count' => ['0 as reg_count'],
        'media_cost_per_reg' => ['0 as ori_cost', '0 as reg_count'],
        'pay_rate' => ['0 as pay_count', '0 as reg_count'],
        //二回
//        'rate_day_roi_2' => [
//            'SUM(data_log.cost) as cost',
//            '0 as sum_second_day_pay_money',
//            '0 as 2_day_total_standard_value'
//        ],
        //三回
//        'rate_day_roi_3' => [
//            'SUM(data_log.cost) as cost',
//            '0 as sum_third_day_pay_money',
//            '0 as 3_day_total_standard_value'
//        ],
        //七回
//        'rate_day_roi_7' => [
//            'SUM(data_log.cost) as cost',
//            '0 as sum_seven_day_pay_money',
//            '0 as 7_day_total_standard_value'
//        ],
    ];

    const OVERVIEW_LOG_GROUP_BY = [
        //穿山甲流量细分
        'tt_rit' => 'tt_rit',
        //细分资源位
        'tt_inventory_subdivision' => 'tt_inventory_subdivision',
        'gdt_inventory_subdivision' => 'gdt_inventory_subdivision',
        //自动扩量细分
        'gdt_is_expand_targeting' => 'gdt_is_expand_targeting',
    ];


    const ACCOUNT_LOG = [
        0 => [
            //媒体类型
            'media_type' => ['any(account_log.media_type) as media_type'],
            'platform' => ['any(account_log.platform) as platform'],
            'account_id' => ['any(account_log.account_id) as account_id'],
            'account_name' => ['any(account_log.account_name) as account_name'],
            'agency_full_name' => ["ad_log.agency_full_name) as agency_full_name"],
            /**
             * 账户通用属性
             */
            //公司
            'company' => ['any(account_log.company) as company'],
            //代理商
            'agency_name' => ['any(account_log.agency_name) as agency_name'],
            //总余额
            'balance' => ['(SUM(DISTINCT(toDecimal64(ad_log.account_id / 10000, 2) + ad_log.total_balance)) - SUM(DISTINCT toDecimal64(ad_log.account_id / 10000, 2))) as balance'],
            //广告主状态
            'account_status' => ['account_log.status'],
            //账户审核拒绝原因
            'reason' => ['account_log.reject_message'],
            //媒体账户负责人
            'account_leader' => ['account_log.account_leader'],
            //账户预算
            'account_budget' => ['account_log.account_budget'],
        ],

        MediaType::TOUTIAO => [
            //媒体类型
            'media_type' => [MediaType::TOUTIAO . ' as media_type'],
            'platform' => ['account_log.platform as platform'],
            'account_id' => ['any(account_log.account_id) as account_id'],
            'account_name' => ['account_log.account_name'],
            'agency_full_name' => ['ad_log.agency_full_name as agency_full_name'],
            /**
             * 账户通用属性
             */
            //公司
            'company' => ['account_log.company'],
            //代理商
            'agency_name' => ['account_log.agency_name'],
            //总余额
            'balance' => ['(SUM(DISTINCT(toDecimal64(ad_log.account_id / 10000, 2) + ad_log.total_balance)) - SUM(DISTINCT toDecimal64(ad_log.account_id / 10000, 2))) as balance'],
            //广告主状态
            'account_status' => ['any(account_log.status) as account_status'],
            //账户审核拒绝原因
            'reason' => ['any(account_log.reason) as reason'],
            //媒体账户负责人
            'account_leader' => ['account_log.account_leader'],
            //账户预算
            'account_budget' => ['account_log.account_budget'],

            //邮箱
            'tt_email' => ['account_log.email as tt_email'],
            //可用现金余额
            'tt_valid_cash' => ['account_log.valid_cash as tt_valid_cash'],
            //可用赠款余额
            'tt_valid_grant' => ['account_log.valid_grant as tt_valid_grant'],
            //可用返货余额
            'tt_valid_return_goods_abs' => ['account_log.valid_return_goods_abs as tt_valid_return_goods_abs'],
            //可用总余额
            'tt_valid_balance' => ['account_log.valid_balance as tt_valid_balance'],
            //现金余额
            'tt_cash' => ['account_log.cash as tt_cash'],
            //赠款余额
            'tt_grant' => ['account_log.grant as tt_grant'],
            //返货余额
            'tt_return_goods_abs' => ['account_log.return_goods_abs as tt_return_goods_abs'],
            //返货支出
            'tt_return_goods_cost' => ['account_log.return_goods_cost as tt_return_goods_cost'],

        ],

        MediaType::TENCENT => [
            /**
             * 腾讯账户属性
             */
            //余额
            'tx_total_balance' => ['total_balance as tx_total_balance'],
            //当日花费
            'tx_total_realtime_cost' => ['total_realtime_cost as tx_total_realtime_cost'],
            //日预算
            'tx_daily_budget' => ['daily_budget as tx_daily_budget'],
            //现金余额
            'tx_cash_balance' => ['cash_balance as tx_cash_balance'],
            //赠款余额
            'tx_gift_balance' => ['gift_balance as tx_gift_balance'],
            //分成余额
            'tx_shared_balance' => ['shared_balance as tx_shared_balance'],
            //银证余额
            'tx_bank_balance' => ['bank_balance as tx_bank_balance'],
            //竞价信用余额
            'tx_credit_roll_balance' => ['credit_roll_balance as tx_credit_roll_balance'],
            //临时信用余额
            'tx_credit_temporary_balance' => ['credit_temporary_balance as tx_credit_temporary_balance'],
            //补偿虚拟金余额
            'tx_compensate_virtual_balance' => ['compensate_virtual_balance as tx_compensate_virtual_balance'],
            //现金实时消耗
            'tx_cash_realtime_cost' => ['cash_realtime_cost as tx_cash_realtime_cost'],
            //赠款实时消耗
            'tx_gift_realtime_cost' => ['gift_realtime_cost as tx_gift_realtime_cost'],
            //分成实时消耗
            'tx_shared_realtime_cost' => ['shared_realtime_cost as tx_shared_realtime_cost'],
            //银证实时消耗
            'tx_bank_realtime_cost' => ['bank_realtime_cost as tx_bank_realtime_cost'],
            //竞价信用金实时消耗
            'tx_credit_roll_realtime_cost' => ['credit_roll_realtime_cost as tx_credit_roll_realtime_cost'],
            //临时信用金实时消耗
            'tx_credit_temporary_realtime_cost' => ['credit_temporary_realtime_cost as tx_credit_temporary_realtime_cost'],
            //补偿虚拟金余额
            'tx_compensate_virtual_realtime_cost' => ['compensate_virtual_realtime_cost as tx_compensate_virtual_realtime_cost'],
            //用户行为数据源
            'tx_user_action_sets' => ['user_action_sets as tx_user_action_sets'],

            'account_name' => ['account_name'],
            /**
             * 账户通用属性
             */
            //公司
            'company' => ['company'],
            //代理商
            'agency_name' => ['agency_name'],
            //总余额
            'balance' => ['balance'],
            //广告主状态
            'account_status' => ['status'],
            //账户审核拒绝原因
            'reason' => ['reason'],
            //媒体账户负责人
            'account_leader' => ['account_leader'],
            //账户预算
            'account_budget' => ['account_budget'],
        ],
    ];

    const MATERIAL_FILTER = [
        //素材文件名｜ID
        'material_file_id' => 'id',

        'material_file_name' => 'filename',

        //素材名｜ID
        'material_id' => 'material_id',

        'material_name' => 'name',

        'material_file_type' => 'material_file_type',
    ];

    const MATERIAL_SIZE_FILTER = [
        //素材尺寸
        'size' => "concat(toString(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'width', signature_key)), '*', toString(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'height', signature_key)))",
    ];

    const GAME_FILTER = [
        'os' => "os",
        'game_id' => "platform-game_id",
        'main_game_id' => "platform-main_game_id",
        'root_game_id' => "platform-root_game_id",
        'clique_id' => "clique_id",
    ];

    const AGENT_SITE_FILTER = [
        'agent_leader' => 'agent_leader',
//        'aweme_account_id' => 'aweme_account_id',
        'agent_id' => 'platform-agent_id',
        'agent_group_id' => 'platform-agent_group_id',
    ];

    CONST SITE_FILTER = [
//        'action_track_type' => 'action_track_type'
    ];

//    const AGENT_SITE_FILTER = [
//        'agent_leader' => "platform-dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_leader', cityHash64(platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', cityHash64(platform, site_id))), toDateTime(ad2_create_time))",
//        'platform-agent_id' => 'platform-agent_id',
//        'platform-agent_group_id' => 'platform-agent_group_id',
//    ];

    const AGENCY_CHANGE_LOG_FILTER = [
        'agency_full_name' => "dictGet(tanwan_datamedia.ods_media_account_agency_change_log_dict, 'agency_full_name', cityHash64(account_id), toDate(ad2_create_time))"
    ];

    //广告开关映射
    const SWITCH = [
        1 => [
            0 => "any(ad_log.ad1_configured_status) as switch",
            MediaType::TOUTIAO => "any(ad_log.status) as switch",
            MediaType::TENCENT => 'any(ad_log.configured_status) as switch',
            MediaType::KUAISHOU => 'any(ad_log.campaign_put_status) as switch'
        ],

        2 => [
            0 => 'any(ad_log.opt_status) as switch',
            MediaType::TOUTIAO => 'any(ad_log.opt_status) as switch',
            MediaType::TENCENT => 'any(ad_log.configured_status) as switch',
            MediaType::KUAISHOU => 'any(ad_log.ad_put_status) as switch'
        ],

        3 => [
            0 => 'any(ad_log.ad3_opt_status) as switch',
            MediaType::TOUTIAO => 'any(ad_log.opt_status) as switch',
            MediaType::TENCENT => 'any(ad_log.configured_status) as switch',
            MediaType::KUAISHOU => 'any(ad_log.put_status) as switch',
        ]
    ];

    const AD_CREATE_TIME = [
        'ad_log' => [
            0 => 'ad_log.ad2_create_time',
            MediaType::TOUTIAO => 'ad_log.ad_create_time',
            MediaType::TENCENT => 'ad_log.created_time',
            MediaType::KUAISHOU => 'ad_log.ad_create_time',
        ],
        'data_log' => [
            0 => 'data_log.map_ad2_create_time',
            MediaType::TOUTIAO => 'data_log.ad_create_time',
            MediaType::TENCENT => 'data_log.created_time',
            MediaType::KUAISHOU => 'data_log.ad_create_time',
        ],
        'overview_log' => [
            0 => 'overview_log.ad2_create_time',
            MediaType::TOUTIAO => 'overview_log.ad_create_time',
            MediaType::TENCENT => 'overview_log.created_time',
            MediaType::KUAISHOU => 'overview_log.ad_create_time',
        ],
    ];

    //替换无any()版
    const SPECIAL_GROUP_BY_AD_SELECT = [
        0 => [
            'media_type' => ['media_type as media_type'],
            'port_version' => ['port_version as port_version'],
            'platform' => ['platform as platform'],
            'account_id' => ['account_id as account_id'],
            'ad3_id' => ['ad3_id as ad3_id'],
            'ad2_id' => ['ad2_id as ad2_id'],
            'ad1_id' => ['ad1_id as ad1_id'],
        ],
        MediaType::TOUTIAO => [
            'media_type' => [MediaType::TOUTIAO . ' as media_type'],
            'port_version' => ['port_version as port_version'],
            'platform' => ['platform as platform'],
            'account_id' => ['account_id as account_id'],
            'ad3_id' => ['creative_id as ad3_id'],
            'ad2_id' => ['ad_id as ad2_id'],
            'ad1_id' => ['campaign_id as ad1_id'],
        ],
    ];

    const SPECIAL_GROUP_BY_SUB_MAIN_SELECT = [
        'media_type' => ['media_type as media_type'],
        'port_version' => ['port_version as port_version'],
        'platform' => ['platform as platform'],
        'account_id' => ['account_id as account_id'],
        'ad3_id' => ['ad3_id as ad3_id'],
        'ad2_id' => ['ad2_id as ad2_id'],
        'ad1_id' => ['ad1_id as ad1_id'],
    ];

    const SPECIAL_GROUP_BY_MAIN_SELECT = [
        'media_type' => ['media_type as media_type'],
        'port_version' => ['port_version as port_version'],
        'platform' => ['platform as platform'],
        'account_id' => ['account_id as account_id'],
        'ad3_id' => ['ad3_id as ad3_id'],
        'ad2_id' => ['ad2_id as ad2_id'],
        'ad1_id' => ['ad1_id as ad1_id'],
    ];

    //以account来控制权限时select更换的字段
    const ACCOUNT_CTRL_PERMISSION_SELECT = [
        'media_type' => ['account_log.media_type as media_type'],
        'platform' => ['account_log.platform as platform'],
        'account_id' => ['account_log.account_id as account_id'],
        'agent_leader' => ['account_log.account_leader as agent_leader']
    ];

    const ACCOUNT_CTRL_PERMISSION_FILTER = [
        'platform' => 'platform',
        'agent_leader' => 'account_leader',
        'account_id' => 'account_id',
        'account_name' => 'account_name',
        'company' => 'company',
        'agency_full_name' => "dictGet(tanwan_datamedia.ods_media_account_agency_change_log_dict, 'agency_full_name', cityHash64(ad_log.account_id), toDate('2100-01-01'))"
    ];

    //以account来控制权限时group by更换的字段
//     const ACCOUNT_CTRL_PERMISSION_GROUP_BY = [
//         'agent_leader' => 'agent_leader'
//     ];

    //需要以account_log来进行权限控制的聚合字段
    const PERMISSION_CTRL_BY_ACCOUNT_DIMENSION = ['platform', 'agent_leader', 'account_id', 'account_name', 'company', 'agency_full_name'];
    //需要以account_log来进行权限控制的自定义指标字段
    const PERMISSION_CTRL_BY_ACCOUNT_TARGET = ['balance', 'tt_valid_cash', 'tt_valid_grant',
        'tt_valid_return_goods_abs'];

    //为了一起union，需要查一些子查询里没有但是需要给空值的字段
    const HAVE_NOT_BUT_NEED_SELECT = [
//        'ad_log' => [

            //投放开始时间
//            'start_time' => ['any(ad_log.start_time) as start_time'],
            //投放结束时间
//            'end_time' => ['any(ad_log.end_time) as end_time'],
            //创意标题
//            'creative_title' => ['"" as creative_title'],
//            //缩略图
//            'urls' => ['"" as signature', '"" as file_type'],
//            //创意素材操作状态
//            'ad3_opt_status' => ['"" as ad3_opt_status'],
//            //视频md5值
//            'signature' => ['"" as signature'],
//            //广告创意创建时间
//            'ad3_create_time' => ['toDateTime("1970-01-01") as ad3_create_time'],
//            //广告创意更新时间
//            'ad3_modify_time' => ['toDateTime("1970-01-01") as ad3_modify_time'],
//
//            //创意素材状态
//            'ad3_status' => ['"" as ad3_status'],
//            //二级修改时间
//            'ad2_modify_time' => ['toDateTime("1970-01-01") as ad2_modify_time'],
//            //开始消耗时间
//            'start_cost_time' => ['toDateTime("1970-01-01") as start_cost_time'],
//            //深度优化出价
//            'deep_cpabid' => ['0 as deep_cpabid'],
//            'roi_goal' => ['0 as roi_goal'],
//
//            //计数项数据
//            'count' => ['0 as count'],
//            'count_ad2' => ['0 as count_ad2'],
//            'count_ad2_delivering' => ['0 as count_ad2_delivering'],
//            'count_ad2_undeliveried' => ['0 as count_ad2_undeliveried'],
//            'count_ad3' => ['0 as count_ad3'],
//            //开关
//            'switch' => ['" as switch'],
//            //操作状态
//            'advertising_opt_status' => ['"" as advertising_opt_status'],
//            //投放时段
//            'advertising_schedule' => ['"" as advertising_schedule'],
//            //出价
//            'cpa_bid' => ['0 as cpa_bid'],
//            //广告预算
//            'budget' => ['0 as budget'],
//            //付费方式
//            'pricing' => ['"" as pricing'],
//            //当天账户币消耗/预算
//            'cost_process' => ['0 as sum_budget_for_cost_process'],
//            /**
//             * 账户通用属性
//             */
//            //代理商
//            'agency_name' => ['"" as agency_name'],
//            //总余额
//            'balance' => ['0 as balance'],
//            //广告主状态
//            'account_status' => ['"" as account_status'],
//            //账户审核拒绝原因
//            'reason' => ['"" as reason'],
//            //媒体账户负责人
//            'account_leader' => ['"" as account_leader'],
//            //账户预算
//            'account_budget' => ['0 as account_budget'],
//
//            //一级预算
//            'ad1_budget' => ['0 as ad1_budget'],
//            //一级状态
//            'ad1_status' => ['"" as ad1_status'],
//            //推广目的
//            'ad1_type' => ['"" as ad1_type'],
//            //一级修改时间
//            'ad1_modify_time' => ['toDateTime("1970-01-01") as ad1_modify_time'],
//            //一级创建时间
//            'ad1_create_time' => ['toDateTime("1970-01-01") as ad1_create_time'],

//            ],
//        'data_log' => [
            //有效消耗
            'standard_reached_cost' => ['0 as standard_reached_cost'],
            //无效消耗
            'standard_unreached_cost' => ['0 as standard_unreached_cost'],
            //首日付费次数成本
            'cost_first_day_pay_times' => ['0 as cost', '0 as first_day_pay_times'],
            //首日付费次数成本
            'cost_total_pay_times' => ['0 as cost', '0 as total_pay_times'],
            //次留成本
            'cost_day_second_login' => ['0 as cost', '0 as count_is_second_login'],

            'cost_date' => ["toDateTime('1970-01-01') as cost_date"],
            'count_ad2_deliveried' => ['0 AS count_ad2_deliveried'],
            'count_cost_date' => ['0 as count_cost_date'],
            'cost' => ['0 as cost'],
            'cost_per_reg' => ['0 as cost', '0 as reg_uid_count'],
            'cost_per_first_day_pay' => ['0 as cost', '0 as first_day_pay_count'],
            'first_day_roi' => ['0 as cost', '0 as first_day_pay_money', '0 as 1_day_total_standard_value'],
            'total_roi' => ['0 as cost', '0 as sum_total_pay_money'],
//            'cost_process' => ['0 as sum_ori_cost_for_cost_process'],
            'cpc' => ['0 as click', '0 as ori_cost'],
            'click' => ['0 as click'],
            'cpm' => ['0 as `show`', '0 as ori_cost'],
            'show' => ['0 as `show`'],
            'ori_cost' => ['0 as ori_cost'],
            'click_rate' => ['0 as click', '0 as `show`'],
            'cost_per_convert' => ['0 as ori_cost', '0 as `convert`'],
            'convert' => ['0 as `convert`'],
            'cost_per_active' => ['0 as active_count', '0 as ori_cost'],
            'cost_per_pay' => ['0 as pay_count', '0 as ori_cost'],
            'pay_count' => ['0 as pay_count'],
            'reg_rate' => ['0 as reg_count', '0 as click'],
            'active_rate' => ['0 as active_count', '0 as click'],
            'active_count' => ['0 as active_count'],
            'convert_rate' => ['0 as `convert`', '0 as click'],
            'reg_count' => ['0 as reg_count'],
            'media_cost_per_reg' => ['0 as ori_cost', '0 as reg_count'],
            'pay_rate' => ['0 as pay_count', '0 as reg_count'],
            //二回
            'rate_day_roi_2' => ['0 as sum_second_day_pay_money', '0 as cost', '0 as 2_day_total_standard_value'],
            //三回
            'rate_day_roi_3' => ['0 as sum_third_day_pay_money', '0 as cost', '0 as 3_day_total_standard_value'],
            //七回
            'rate_day_roi_7' => ['0 as sum_seven_day_pay_money', '0 as cost', '0 as 7_day_total_standard_value'],

            //穿山甲流量细分
            'tt_rit' => ['0 as tt_rit'],
            //资源位细分
            'tt_inventory_subdivision' => ["'' as tt_inventory_subdivision"],

            'gdt_inventory_subdivision' => ["''  as gdt_inventory_subdivision"],
            //自动扩量细分
            'gdt_is_expand_targeting' => ['0 as gdt_is_expand_targeting'],
//        ],

//        'overview_log' => [
            //激活设备注册率
            'action_uid_reg_rate' => ['0 as reg_uid_count', '0 as sum_day_action_muid_distinct_count'],
            //激活注册率
            'action_reg_rate' => ['0 as reg_uid_count', '0 as sum_day_action_muid_count'],
            //激活注册率（设备）
            'action_reg_rate_device' => ['0 as sum_day_reg_muid_distinct_count', '0 as sum_day_action_muid_distinct_count'],
            //注册设备数
            'reg_muid_count' => ['0 as reg_muid_count'],
            //激活设备数
            'action_muid_count' => ['0 as action_muid_count'],
            //首日LTV
            'first_day_ltv' => ['0 as first_day_pay_money', '0 as reg_uid_count'],
            //首日付费人数
            'first_day_pay_count' => ['0 as first_day_pay_count'],
            //首日付费率
            'first_day_pay_rate' => ['0 as first_day_pay_count', '0 as reg_uid_count'],
            //首日付费金额
            'first_day_pay_money' => ['0 as first_day_pay_money'],
            //首日arppu
            'first_day_arppu' => ['0 as first_day_pay_money', '0 as first_day_pay_count'],
            //注册数
            'reg_uid_count' => ['0 as reg_uid_count'],
            //次留
            'rate_day_stay_2' => [
                '0 as count_is_second_login',
                '0 as reg_uid_count'
            ],
            //三留
            'rate_day_stay_3' => [
                '0 as count_is_third_login',
                '0 as reg_uid_count'
            ],
            //七留
            'rate_day_stay_7' => [
                '0 as count_is_seventh_login',
                '0 as reg_uid_count'
            ],
            //十五留
            'rate_day_stay_15' => [
                '0 as count_is_fifteenth_login',
                '0 as reg_uid_count'
            ],
            /**
             * new
             */
            //首日付费次数
            'first_day_pay_times' => ['0 as first_day_pay_times'],
            //累计付费次数
            'total_pay_times' => ['0 as total_pay_times'],
            //业务点击数
            'click_count' => ['0 as click_count'],
            //累计付费人数
            'total_pay_count' => ['0 as total_pay_count'],
            //累计付费金额
            'total_pay_money' => ['0 as total_pay_money'],
            //上报媒体首日付费金额
            'callback_first_day_pay_money' => ['0 as callback_first_day_pay_money'],
            //上报媒体首日付费人数
            'callback_first_day_pay_uid_count' => ['0 as callback_first_day_pay_uid_count'],
            //注册(根老设备)
            'reg_old_muid_count' => ['0 as reg_old_muid_count'],
            //注册(集团老设备)
            'reg_old_clique_muid_count' => ['0 as reg_old_clique_muid_count'],
            //登录三次以上人数
            'day_three_login_uid_count' => ['0 as day_three_login_uid_count'],
            //注册(根老设备)占比
            'reg_old_muid_percentage' => [
                '0 as reg_old_muid_count',
                '0 as day_reg_muid_count'
            ],
            //注册(集团老设备)占比
            'reg_old_clique_muid_percentage' => [
                '0 as reg_old_clique_muid_count',
                '0 as day_reg_muid_count'
            ],
            //登录三次以上人数占比
            'day_three_login_uid_percentage' => ['0 as day_three_login_uid_count', '0 as reg_uid_count'],
            //首次付费当日充值金额
            'new_pay_day_money' => ['0 as new_pay_day_money'],
            //首次付费当日充值人数
            'new_pay_day_count' => ['0 as new_pay_day_count'],
//        ],

    ];

    const MATERIAL_LOG = [
        //素材文件名｜ID
        'material_file_id' => [
            "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'id', cityHash64(ad_log.signature))) as material_file_id",
            "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'filename', cityHash64(ad_log.signature))) as material_file_name"
        ],
        //素材尺寸
        'size' => ["any(concat(toString(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'width', cityHash64(ad_log.signature))), '*', toString(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'height', cityHash64(ad_log.signature))))) as size"],
        //素材名｜ID
        'material_id' => [
            "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'material_id', cityHash64(ad_log.signature))) as material_id",
            "any(dictGet(tanwan_datamedia.dwd_material_cklast_dict, 'name', cityHash64(ad_log.signature))) as material_name"
        ],
    ];

    const TRUE_COST = "cast(
        SUM(
                if(ifNull(dictGetOrNull(tanwan_datahub.dim_agent_true_rebate_id_dict, 'true_rebate', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', agent_key)), toDateTime(cost_date))), toDateTime(cost_date)), ifNull(dictGetOrNull(tanwan_datahub.dim_agent_group_true_rebate_id_dict, 'true_rebate', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id',cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', agent_key)), toDateTime(cost_date))), toDateTime(cost_date)), 1)) != 1, data_log.`ori_cost` / ifNull(dictGetOrNull(tanwan_datahub.dim_agent_true_rebate_id_dict, 'true_rebate', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', agent_key)), toDateTime(cost_date))), toDateTime(cost_date)), ifNull(dictGetOrNull(tanwan_datahub.dim_agent_group_true_rebate_id_dict, 'true_rebate', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', agent_key)), toDateTime(cost_date))), toDateTime(cost_date)), 1)), data_log.`ori_cost`)
            ) AS Decimal64(4)
    ) AS true_cost";

    const JOIN_CALC_ON = [
        0 => ['platform', 'media_type', 'ad2_id'],
        MediaType::TOUTIAO => ['platform', 'ad_id'],
        MediaType::TENCENT => ['platform', 'adgroup_id']
    ];

    const CALC_BIND_RULE_FILTER = [
        'calc_rule_id' => 'calc_bind.calc_rule_id'
    ];

    //需要用计算百分比的字段
    const ALL_CALCULATE_PERCENTAGE = [
        'action_uid_reg_rate', 'action_reg_rate', 'action_reg_rate_device', 'first_day_pay_rate', 'click_rate', 'reg_rate', 'active_rate', 'convert_rate',
        'first_day_roi', 'total_roi', 'cost_process', 'pay_rate', 'tt_wifi_play_rate', 'tt_play_over_rate',
        'tt_valid_play_rate', 'tt_loan_completion_rate', 'tt_install_finish_rate', 'tt_download_start_rate',
        'tt_deep_convert_rate', 'tt_download_finish_rate', 'tt_next_day_open_rate', 'tt_game_addiction_rate',
        'rate_day_stay_2', 'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3',
        'rate_day_roi_7', 'reg_old_muid_percentage', 'reg_old_clique_muid_percentage', 'day_three_login_uid_percentage', 'first_day_roi_standard_value', 'rate_day_roi_2_standard_value', 'rate_day_roi_3_standard_value', 'rate_day_roi_7_standard_value',
    ];

    const ALL_CALCULATE_DIVISION = [
        'cost_per_reg', 'first_day_ltv', 'cost_per_first_day_pay', 'first_day_arppu', 'cpc', 'cpm',
        'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'media_cost_per_reg', 'tt_average_play_time_per_play',
        'tt_valid_play_cost', 'tt_loan_completion_cost', 'tt_download_finish_cost', 'tt_download_start_cost',
        'tt_install_finish_cost', 'tt_deep_convert_cost', 'tt_next_day_open_cost', 'tt_game_addiction_cost',
        'cost_first_day_pay_times', 'cost_total_pay_times', 'cost_day_second_login',
    ];

    const ALL_CALCULATE_AVERAGE = ['cpa_bid', 'deep_cpabid', 'roi_goal', 'ad1_budget'];

    //需要单纯累加但是要保留两位小数的字段
    const ALL_NEED_KEEP_TWO_DECIMAL = ['cost', 'ori_cost', 'budget', 'balance', 'tt_valid_cash', 'tt_valid_grant',
        'tt_valid_return_goods_abs', 'gdt_wechat_cost_stage1', 'gdt_wechat_cost_stage2'];
    //所有需要计算的字段
    const ALL_NEED_CALCULATE = [
        'count', 'count_ad2', 'count_ad2_deliveried', 'count_ad2_delivering', 'count_ad2_undeliveried',
        'count_cost_date', 'count_ad3', 'cost', 'reg_uid_count', 'reg_muid_count', 'action_muid_count', 'first_day_ltv',
        'first_day_pay_count', 'first_day_pay_money', 'first_day_arppu', 'first_day_roi', 'cost_process', 'click',
        'show', 'ori_cost', 'click_rate', 'convert', 'pay_count', 'active_count', 'convert_rate', 'reg_count',
        'media_cost_per_reg', 'pay_rate', 'deep_cpabid', 'roi_goal', 'tt_attribution_next_day_open_cnt', 'tt_loan_completion',
        'tt_download_finish', 'tt_install_finish', 'tt_download_start', 'tt_click_install', 'tt_deep_convert',
        'tt_next_day_open', 'tt_game_addiction', 'tt_play_75_feed_break', 'tt_play_100_feed_break',
        'tt_play_duration_sum', 'tt_valid_play', 'tt_play_25_feed_break', 'tt_total_play', 'tt_wifi_play',
        'tt_play_50_feed_break', 'tt_play_over', 'tt_location_click', 'tt_comment', 'tt_share', 'tt_follow',
        'tt_home_visited', 'tt_like', 'tt_ies_music_click', 'tt_ies_challenge_click', 'cpa_bid', 'budget',
        'action_uid_reg_rate', 'action_reg_rate', 'action_reg_rate_device', 'cost_per_reg', 'first_day_pay_rate', 'cost_per_first_day_pay', 'total_roi', 'cpc',
        'cpm', 'cost_per_convert', 'cost_per_active', 'cost_per_pay', 'reg_rate', 'active_rate',
        'tt_average_play_time_per_play', 'tt_wifi_play_rate', 'tt_play_over_rate', 'tt_valid_play_cost',
        'tt_valid_play_rate', 'tt_loan_completion_cost', 'tt_loan_completion_rate', 'tt_download_finish_cost',
        'tt_install_finish_rate', 'tt_download_start_rate', 'tt_download_start_cost', 'tt_install_finish_cost',
        'tt_deep_convert_rate', 'tt_deep_convert_cost', 'tt_download_finish_rate', 'tt_next_day_open_rate',
        'tt_next_day_open_cost', 'tt_game_addiction_rate', 'tt_game_addiction_cost',  'rate_day_stay_2',
        'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'rate_day_roi_2', 'rate_day_roi_3', 'rate_day_roi_7',
        'first_day_pay_times', 'total_pay_times', 'cost_first_day_pay_times', 'cost_total_pay_times', 'click_count',
        'cost_day_second_login', 'total_pay_count', 'total_pay_money', 'callback_first_day_pay_money', 'callback_first_day_pay_uid_count', 'reg_old_muid_count', 'reg_old_clique_muid_count',
        'day_three_login_uid_count', 'reg_old_muid_percentage', 'reg_old_clique_muid_percentage',
        'day_three_login_uid_percentage', 'new_pay_day_money', 'new_pay_day_count', 'balance', 'tt_valid_cash', 'tt_valid_grant',
        'tt_valid_return_goods_abs', 'first_day_roi_standard_value', 'rate_day_roi_2_standard_value', 'rate_day_roi_3_standard_value', 'rate_day_roi_7_standard_value', 'standard_reached_cost', 'standard_unreached_cost', 'gdt_wechat_cost_stage1', 'gdt_wechat_cost_stage2',
        'ad1_budget'
    ];

    //需要转化成中文的字段
    const ALL_TRANSLATE_TO_CN = [
        'tt_smart_bid_type', 'tt_download_type', 'tt_auto_extend_targets', 'tt_gender', 'ad2_status',
        'inventory_type', 'account_status', 'ad1_status', 'advertising_opt_status', 'ad3_status', 'ad3_opt_status',
        'tt_learning_phase', 'tt_schedule_type', 'tt_flow_control_mode', 'tt_union_video_type',
        'tt_creative_material_mode', 'tt_deep_bid_type', 'tt_inventory_type', 'tt_ad1_budget_mode',
        'tt_ad2_budget_mode', 'tt_auto_extend_enabled', 'gdt_deep_optimization_action_type', 'ad1_type',
        'flow_control_mode', 'pricing', 'gdt_adcreative_template_id', 'gdt_expand_enabled', 'gdt_optimization_goal',
        'gdt_deep_conversion_type', 'gdt_deep_conversion_behavior_goal', 'gdt_deep_conversion_worth_goal',
        'tt_inventory_subdivision', 'gdt_inventory_subdivision', 'gdt_is_expand_targeting', 'gdt_ad2_promoted_object_type',
        'gdt_location_types', 'gdt_bid_strategy', 'gdt_regions', 'gdt_consumption_type', 'gdt_paid_user',
        'gdt_deprecated_region', 'gdt_mobile_union', 'gdt_exclude_mobile_union', 'gdt_display_scene',
        'gdt_is_rewarded_video_ad', 'gdt_automatic_site_enabled', 'gdt_ad1_speed_mode', 'create_type', 'gdt_ad3_audit_spec',
        'gdt_ad3_site_set', 'gdt_ad3_page_type', 'gdt_ad3_promoted_object_type', 'tt_convert_type',
        'baidu_campaign_schedule', 'baidu_ad_bidtype', 'ad2_os', 'port_version'
    ];

    const CALC_STANDARD_SUB_DATA_LOG = [
        0 => [
                /**
                 * ad_log的部分
                 **/
                //投放开始时间
//            'start_time' => ['any(ad_log.start_time) as start_time'],
                //投放结束时间
//            'end_time' => ['any(ad_log.end_time) as end_time'],
                //素材文件名｜ID
//                'material_file_id' => [
//                    "0 as material_file_id",
//                    "'' as material_file_name"
//                ],
//                //素材尺寸
//                'size' => ["'' as size"],
//                //素材名｜ID
//                'material_id' => [
//                    "0 as material_id",
//                    "'' as material_name"
//                ],
//                //创意标题
//                'creative_title' => ["'' as creative_title"],
//                //缩略图
//                'urls' => ["'' as signature", "'' as file_type"],
//                //创意素材操作状态
//                'ad3_opt_status' => ["'' as ad3_opt_status"],
//                //视频md5值
//                'signature' => ["'' as signature"],
//                //广告创意创建时间
//                'ad3_create_time' => ['toDateTime("1970-01-01") as ad3_create_time'],
//                //广告创意更新时间
//                'ad3_modify_time' => ['toDateTime("1970-01-01") as ad3_modify_time'],
//
//                //创意素材状态
//                'ad3_status' => ["'' as ad3_status"],
//                //二级修改时间
//                'ad2_modify_time' => ['toDateTime("1970-01-01") as ad2_modify_time'],
//                //开始消耗时间
//                'start_cost_time' => ['toDateTime("1970-01-01") as start_cost_time'],
//                //深度优化出价
//                'deep_cpabid' => ['0 as deep_cpabid'],
//                'roi_goal' => ['0 as roi_goal'],
//
//                //计数项数据
//                'count' => ['0 as count'],
//                'count_ad2' => ['0 as count_ad2'],
//                'count_ad2_delivering' => ['0 as count_ad2_delivering'],
//                'count_ad2_undeliveried' => ['0 as count_ad2_undeliveried'],
//                'count_ad3' => ['0 as count_ad3'],
//                //开关
////            'switch' => ["'' as switch"],
//                //操作状态
//                'advertising_opt_status' => ["'' as advertising_opt_status"],
//                //投放时段
//                'advertising_schedule' => ["'' as advertising_schedule"],
//                //出价
//                'cpa_bid' => ['0 as cpa_bid'],
//                //广告预算
//                'budget' => ['0 as budget'],
//                //付费方式
//                'pricing' => ["'' as pricing"],
//                //当天账户币消耗/预算
////            'cost_process' => ['0 as sum_budget_for_cost_process'],
//                /**
//                 * 账户通用属性
//                 */
//                //代理商
//                'agency_name' => ["'' as agency_name"],
//                //总余额
//                'balance' => ['0 as balance'],
//                //广告主状态
//                'account_status' => ["'' as account_status"],
//                //账户审核拒绝原因
//                'reason' => ["'' as reason"],
//                //媒体账户负责人
//                'account_leader' => ["'' as account_leader"],
//                //账户预算
//                'account_budget' => ['0 as account_budget'],
//
//                //一级预算
//                'ad1_budget' => ['0 as ad1_budget'],
//                //一级状态
//                'ad1_status' => ["'' as ad1_status"],
//                //推广目的
//                'ad1_type' => ["'' as ad1_type"],
//                //一级修改时间
//                'ad1_modify_time' => ['toDateTime("1970-01-01") as ad1_modify_time'],
//                //一级创建时间
//                'ad1_create_time' => ['toDateTime("1970-01-01") as ad1_create_time'],

                //ad_log中group by的
                //广告投放速度类型
                'flow_control_mode' => ['any(data_log.flow_control_mode) as flow_control_mode'],
                'media_type' => ['any(data_log.media_type) as media_type'],
                //资源位
                'inventory_type' => ['any(data_log.inventory_type) as inventory_type'],
                'platform' => ['any(data_log.platform) as platform_tmp'],
                'game_id' => [
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_id', game_key)) as game_id",
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_name', game_key)) as game_name"
                ],
                'main_game_id' => [
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)) as main_game_id",
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_name', game_key)) as main_game_name"
                ],
                'root_game_id' => [
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_id', game_key)) as root_game_id",
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_name', game_key)) as root_game_name"
                ],
                'clique_id' => [
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_id', game_key)) as clique_id",
                    "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_name', game_key)) as clique_name"
                ],
                'os' => ["any(dictGet(tanwan_datahub.dim_game_id_dict, 'os', game_key)) as os"],
                'ad2_os' => ['any(data_log.ad2_os) as ad2_os'],
                'agent_leader' => ["any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_leader', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_leader"],
                'site_id' => [
                    'any(data_log.site_id) as site_id',
                    "any(dictGet(tanwan_datahub.dim_site_id_dict, 'site_name', cityHash64(data_log.platform, data_log.site_id))) as site_name"
                ],
//                'action_track_type' => ["any(dictGet(tanwan_datahub.dim_site_id_dict, 'action_track_type', cityHash64(data_log.platform, data_log.site_id))) as action_track_type"],

                'aweme_account_id' => [
                    "data_log.map_aweme_account as aweme_account_id",
                    "any(dictGet(tanwan_datamedia.ods_live_user_name_log_aweme_account_dict, 'aweme_name', cityHash64(if(dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(data_log.platform, data_log.site_id)) = '', map_aweme_account, dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(data_log.platform, data_log.site_id))), (data_log.media_type)), toDateTime((data_log.ad2_create_time)))) as aweme_account_name"
                ],

                'agent_id' => [
                    "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_id",
                    "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_name', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_name"
                ],
                'agent_group_id' => [
                    "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_group_id",
                    "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_name', cityHash64(data_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(cost_date))) as agent_group_name"
                ],
                'agency_full_name' => ['any(data_log.agency_full_name) as agency_full_name_tmp'],
                'company' => ['any(data_log.company) as company_tmp'],
                'create_type' => ['any(data_log.create_type) as create_type'],
                'account_id' => ['any(data_log.account_id) as account_id_tmp'],
                'account_name' => ['any(data_log.account_name) as account_name_tmp'],
                'ad1_id' => ['any(data_log.ad1_id) as ad1_id'],
                'ad1_name' => ['any(data_log.ad1_name) as ad1_name'],
                'ad2_id' => ['any(data_log.ad2_id) as ad2_id'],
                'ad2_name' => ['any(data_log.ad2_name) as ad2_name'],
                'ad3_id' => ['data_log.ad3_id as ad3_id'],
                'ad3_name' => ['any(data_log.title) as ad3_name'],
//            'game_name' => 'game_name',
//         'cost_date' => 'cost_date',
                'ad2_create_time' => ['any(data_log.map_ad2_create_time) as ad2_create_time_tmp'],
                'ad2_status' => ['any(data_log.status) as ad2_status'],

                /**
                 * data_log原本的
                 **/
                'port_version' => ['any(data_log.port_version) as port_version'],
                //有效消耗
                'standard_reached_cost' => [
                    'SUM(data_log.cost) as cost',
                    "any(dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_30_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as day_30_standard_value",
                    '0 as sum_thirty_day_pay_money'
                ],
                //无效消耗
                'standard_unreached_cost' => [
                    'SUM(data_log.cost) as cost',
                    "any(dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_30_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as day_30_standard_value",
                    '0 as sum_thirty_day_pay_money'
                ],
//                //首日付费次数成本
//                'cost_first_day_pay_times' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_times'],
//                //首日付费次数成本
//                'cost_total_pay_times' => ['SUM(data_log.cost) as cost', '0 as total_pay_times'],
//                //次留成本
//                'cost_day_second_login' => ['SUM(data_log.cost) as cost', '0 as count_is_second_login'],

                'cost_date' => ['data_log.cost_date as cost_date'],
//                'count_ad2_deliveried' =>
//                    ['COUNT( DISTINCT data_log.ad2_id, IF ( data_log.cost > 0, 1, NULL ) ) AS count_ad2_deliveried'],
//                'count_cost_date' => ['COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, 1, null) )
//         as count_cost_date'],
//                'cost' => ['SUM(data_log.cost) as cost'],
//                'cost_per_reg' => ['SUM(data_log.cost) as cost', '0 as reg_uid_count'],
//                'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_count'],
//                'first_day_roi' => [
//                    'SUM(data_log.cost) as cost',
//                    '0 as first_day_pay_money',
//                    "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_1_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 1_day_total_standard_value"
//                ],
//                'total_roi' => ['SUM(data_log.cost) as cost', '0 as sum_total_pay_money'],
//                'cost_process' => ["SUM(if(data_log.cost_date = today(), data_log.ori_cost, 0)) as sum_ori_cost_for_cost_process"],
//                'cpc' => ['SUM(data_log.click) as click', 'SUM(data_log.ori_cost) as ori_cost'],
//                'click' => ['SUM(data_log.click) as click'],
//                'cpm' => ['SUM(data_log.`show`) as `show`', 'SUM(data_log.ori_cost) as ori_cost'],
//                'show' => ['SUM(data_log.`show`) as `show`'],
//                'ori_cost' => ['SUM(data_log.ori_cost) as ori_cost'],
//                'click_rate' => ['SUM(data_log.click) as click', 'SUM(data_log.`show`) as `show`'],
//                'cost_per_convert' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`'],
//                'convert' => ['SUM(data_log.`convert`) as `convert`'],
//                'cost_per_active' => ['SUM(data_log.active) as active_count', 'SUM(data_log.ori_cost) as ori_cost'],
//                'cost_per_pay' => ['SUM(data_log.pay_count) as pay_count', 'SUM(data_log.ori_cost) as ori_cost'],
//                'pay_count' => ['SUM(data_log.pay_count) as pay_count'],
//                'reg_rate' => ['SUM(data_log.register) as reg_count', 'SUM(data_log.click) as click'],
//                'active_rate' => ['SUM(data_log.active) as active_count', 'SUM(data_log.click) as click'],
//                'active_count' => ['SUM(data_log.active) as active_count'],
//                'convert_rate' => ['SUM(data_log.`convert`) as `convert`', 'SUM(data_log.click) as click'],
//                'reg_count' => ['SUM(data_log.register) as reg_count'],
//                'media_cost_per_reg' => ['SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.register) as reg_count'],
//                'pay_rate' => ['SUM(data_log.pay_count) as pay_count', 'SUM(data_log.register) as reg_count'],
//                //二回
//                'rate_day_roi_2' => [
//                    'SUM(data_log.cost) as cost',
//                    '0 as sum_second_day_pay_money',
//                    "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_2_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 2_day_total_standard_value"
//                ],
//                //三回
//                'rate_day_roi_3' => [
//                    'SUM(data_log.cost) as cost',
//                    '0 as sum_third_day_pay_money',
//                    "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_3_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 3_day_total_standard_value"
//                ],
//                //七回
//                'rate_day_roi_7' => [
//                    'SUM(data_log.cost) as cost',
//                    '0 as sum_seven_day_pay_money',
//                    "SUM(data_log.cost * dictGet(tanwan_datamedia.dim_game_standard_value_days_incolumn_dict, 'day_7_standard_value', cityHash64(data_log.platform, toInt64(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)), toInt8(0), toInt8(1)), toDate(cost_date))) as 7_day_total_standard_value"
//                ],
//
//                /**
//                 * overview_log
//                 */
//                //激活设备注册率
//                'action_uid_reg_rate' => [
//                    '0 as reg_uid_count',
//                    '0 as sum_day_action_muid_distinct_count'
//                ],
//                //激活注册率
//                'action_reg_rate' => [
//                    '0 as reg_uid_count',
//                    '0 as sum_day_action_muid_count'
//                ],
//                //激活注册率（设备）
//                'action_reg_rate_device' => [
//                    '0 as sum_day_reg_muid_distinct_count',
//                    '0 as sum_day_action_muid_distinct_count'
//                ],
//                //注册设备数
//                'reg_muid_count' => ['0 as reg_muid_count'],
//                //激活设备数
//                'action_muid_count' => ['0 as action_muid_count'],
//                //首日LTV
//                'first_day_ltv' => [
//                    '0 as first_day_pay_money',
//                    '0 as reg_uid_count'
//                ],
//                //首日付费人数
//                'first_day_pay_count' => ['0 as first_day_pay_count'],
//                //首日付费率
//                'first_day_pay_rate' => [
//                    '0 as first_day_pay_count',
//                    '0 as reg_uid_count'
//                ],
//                //首日付费成本
////            'cost_per_first_day_pay' => ['0 as first_day_pay_count'],
//                //首日付费金额
//                'first_day_pay_money' => ['0 as first_day_pay_money'],
//                //有效消耗
////            'standard_reached_cost' => ['0 as sum_thirty_day_pay_money'],
//                //无效消耗
////            'standard_unreached_cost' => ['0 as sum_thirty_day_pay_money'],
//                //首日arppu
//                'first_day_arppu' => [
//                    '0 as first_day_pay_money',
//                    '0 as first_day_pay_count'
//                ],
//                //首日roi
////            'first_day_roi' => ['0 as first_day_pay_money'],
//                //注册成本
////            'cost_per_reg' => ['0 as reg_uid_count'],
//                //注册数
//                'reg_uid_count' => ['0 as reg_uid_count'],
//                //累计付费回本
////            'total_roi' => ['0 as sum_total_pay_money'],
//                //次留
//                'rate_day_stay_2' => [
//                    '0 as count_is_second_login',
//                    '0 as reg_uid_count'
//                ],
//                //三留
//                'rate_day_stay_3' => [
//                    '0 as count_is_third_login',
//                    '0 as reg_uid_count'
//                ],
//                //七留
//                'rate_day_stay_7' => [
//                    '0 as count_is_seventh_login',
//                    '0 as reg_uid_count'
//                ],
//                //十五留
//                'rate_day_stay_15' => [
//                    '0 as count_is_fifteenth_login',
//                    '0 as reg_uid_count'
//                ],
//                //二回
////            'rate_day_roi_2' => ['0 as sum_second_day_pay_money'],
//                //三回
////            'rate_day_roi_3' => ['0 as sum_third_day_pay_money'],
//                //首日付费次数
//                'first_day_pay_times' => ['0 as first_day_pay_times'],
//                //累计付费次数
//                'total_pay_times' => ['0 as total_pay_times'],
//                //首日付费次数成本
////            'cost_first_day_pay_times' => ['0 as first_day_pay_times'],
//                //累计付费次数成本
////            'cost_total_pay_times' => ['0 as total_pay_times'],
//                //业务点击数
//                'click_count' => ['0 as click_count'],
//                //次留成本
////            'cost_day_second_login' => ['0 as count_is_second_login'],
//                //累计付费人数
//                'total_pay_count' => ['0 as total_pay_count'],
//                //累计付费金额
//                'total_pay_money' => ['0 as total_pay_money'],
//                //注册(根老设备)
//                'reg_old_muid_count' => ['0 as reg_old_muid_count'],
//                //注册(集团老设备)
//                'reg_old_clique_muid_count' => ['0 as reg_old_clique_muid_count'],
//                //登录三次以上人数
//                'day_three_login_uid_count' => ['0 as day_three_login_uid_count'],
//                //注册(根老设备)占比
//                'reg_old_muid_percentage' => [
//                    '0 as reg_old_muid_count',
//                    '0 as day_reg_muid_count'
//                ],
//                //注册(集团老设备)占比
//                'reg_old_clique_muid_percentage' => [
//                    '0 as reg_old_clique_muid_count',
//                    '0 as day_reg_muid_count'
//                ],
//                //登录三次以上人数占比
//                'day_three_login_uid_percentage' => [
//                    '0 as day_three_login_uid_count',
//                    '0 as reg_uid_count'
//                ],
//                //首次付费当日充值金额
//                'new_pay_day_money' => ['0 as new_pay_day_money'],
//                //首次付费当日充值人数
//                'new_pay_day_count' => ['0 as new_pay_day_count'],
                //七回
//            'rate_day_roi_7' => ['0 as sum_seven_day_pay_money'],
            ]

    ];

    const CALC_STANDARD_SUB_OVERVIEW_LOG = [
        'port_version' => ['any(overview_log.port_version) as port_version'],
        /**
         * ad_log的部分
         **/
        //投放开始时间
//            'start_time' => ['any(ad_log.start_time) as start_time'],
        //投放结束时间
//            'end_time' => ['any(ad_log.end_time) as end_time'],

        //素材文件名｜ID
//        'material_file_id' => [
//            "0 as material_file_id",
//            "'' as material_file_name"
//        ],
//        //素材尺寸
//        'size' => ["'' as size"],
//        //素材名｜ID
//        'material_id' => [
//            "0 as material_id",
//            "'' as material_name"
//        ],
//        //创意标题
//        'creative_title' => ["'' as creative_title"],
//        //缩略图
//        'urls' => ["'' as signature", "'' as file_type"],
//        //创意素材操作状态
//        'ad3_opt_status' => ["'' as ad3_opt_status"],
//        //视频md5值
//        'signature' => ["'' as signature"],
//        //广告创意创建时间
//        'ad3_create_time' => ['toDateTime("1970-01-01") as ad3_create_time'],
//        //广告创意更新时间
//        'ad3_modify_time' => ['toDateTime("1970-01-01") as ad3_modify_time'],
//
//        //创意素材状态
//        'ad3_status' => ["'' as ad3_status"],
//        //二级修改时间
//        'ad2_modify_time' => ['toDateTime("1970-01-01") as ad2_modify_time'],
//        //开始消耗时间
//        'start_cost_time' => ['toDateTime("1970-01-01") as start_cost_time'],
//        //深度优化出价
//        'deep_cpabid' => ['0 as deep_cpabid'],
//        'roi_goal' => ['0 as roi_goal'],
//
//        //计数项数据
//        'count' => ['0 as count'],
//        'count_ad2' => ['0 as count_ad2'],
//        'count_ad2_delivering' => ['0 as count_ad2_delivering'],
//        'count_ad2_undeliveried' => ['0 as count_ad2_undeliveried'],
//        'count_ad3' => ['0 as count_ad3'],
//        //开关
////        'switch' => ["'' as switch"],
//        //操作状态
//        'advertising_opt_status' => ["'' as advertising_opt_status"],
//        //投放时段
//        'advertising_schedule' => ["'' as advertising_schedule"],
//        //出价
//        'cpa_bid' => ['0 as cpa_bid'],
//        //广告预算
//        'budget' => ['0 as budget'],
//        //付费方式
//        'pricing' => ["'' as pricing"],
//        //当天账户币消耗/预算
////            'cost_process' => ['0 as sum_budget_for_cost_process'],
//        /**
//         * 账户通用属性
//         */
//        //代理商
//        'agency_name' => ["'' as agency_name"],
//        //总余额
//        'balance' => ['0 as balance'],
//        //广告主状态
//        'account_status' => ["'' as account_status"],
//        //账户审核拒绝原因
//        'reason' => ["'' as reason"],
//        //媒体账户负责人
//        'account_leader' => ["'' as account_leader"],
//        //账户预算
//        'account_budget' => ['0 as account_budget'],
//
//        //一级预算
//        'ad1_budget' => ['0 as ad1_budget'],
//        //一级状态
//        'ad1_status' => ["'' as ad1_status"],
//        //推广目的
//        'ad1_type' => ["'' as ad1_type"],
//        //一级修改时间
//        'ad1_modify_time' => ['toDateTime("1970-01-01") as ad1_modify_time'],
//        //一级创建时间
//        'ad1_create_time' => ['toDateTime("1970-01-01") as ad1_create_time'],

        //ad_log中group by的
        //广告投放速度类型
        'flow_control_mode' => ['any(overview_log.flow_control_mode) as flow_control_mode'],
        'media_type' => ['any(overview_log.media_type) as media_type'],
        //资源位
        'inventory_type' => ['any(overview_log.inventory_type) as inventory_type'],
        'platform' => ['any(overview_log.platform) as platform_tmp'],
        'game_id' => [
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_id', game_key)) as game_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'game_name', game_key)) as game_name"
        ],
        'main_game_id' => [
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_id', game_key)) as main_game_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'main_game_name', game_key)) as main_game_name"
        ],
        'root_game_id' => [
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_id', game_key)) as root_game_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'root_game_name', game_key)) as root_game_name"
        ],
        'clique_id' => [
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_id', game_key)) as clique_id",
            "any(dictGet(tanwan_datahub.dim_game_id_dict, 'clique_name', game_key)) as clique_name"
        ],
        'os' => ["any(dictGet(tanwan_datahub.dim_game_id_dict, 'os', game_key)) as os"],
        'ad2_os' => ['any(overview_log.ad2_os) as ad2_os'],
        'agent_leader' => ["any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_leader', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_leader"],
        'site_id' => [
            'any(overview_log.site_id) as site_id',
            "any(dictGet(tanwan_datahub.dim_site_id_dict, 'site_name', cityHash64(overview_log.platform, overview_log.site_id))) as site_name"
        ],
//        'action_track_type' => ["any(dictGet(tanwan_datahub.dim_site_id_dict, 'action_track_type', cityHash64(overview_log.platform, overview_log.site_id))) as action_track_type"],

        'aweme_account_id' => [
            "overview_log.map_aweme_account as aweme_account_id",
            "any(dictGet(tanwan_datamedia.ods_live_user_name_log_aweme_account_dict, 'aweme_name', cityHash64(if(dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(overview_log.platform, overview_log.site_id)) = '', map_aweme_account, dictGet(tanwan_datahub.dim_site_id_dict, 'aweme_account', cityHash64(overview_log.platform, overview_log.site_id))), (overview_log.media_type)), toDateTime((overview_log.ad2_create_time)))) as aweme_account_name"
        ],

        'agent_id' => [
            "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_id', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_id",
            "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_name', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_name"
        ],
        'agent_group_id' => [
            "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_id', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_group_id",
            "any(dictGet(tanwan_datahub.dim_agent_id_dict, 'agent_group_name', cityHash64(overview_log.platform, dictGet(tanwan_datahub.dim_site_id_dict, 'agent_id', site_key)), toDateTime(log_date))) as agent_group_name"
        ],
        'agency_full_name' => ['any(overview_log.agency_full_name) as agency_full_name_tmp'],
        'company' => ['any(overview_log.company) as company_tmp'],
        'create_type' => ['any(overview_log.create_type) as create_type'],
        'account_id' => ['any(overview_log.account_id) as account_id_tmp'],
        'account_name' => ['any(overview_log.account_name) as account_name_tmp'],
        'ad1_id' => ['any(overview_log.ad1_id) as ad1_id'],
        'ad1_name' => ['any(overview_log.ad1_name) as ad1_name'],
        'ad2_id' => ['any(overview_log.ad2_id) as ad2_id'],
        'ad2_name' => ['any(overview_log.ad2_name) as ad2_name'],
        'ad3_id' => ['overview_log.ad3_id as ad3_id'],
        'ad3_name' => ['any(overview_log.title) as ad3_name'],
//            'game_name' => 'game_name',
        'ad2_create_time' => ['any(overview_log.ad2_create_time) as ad2_create_time_tmp'],
        'ad2_status' => ['any(overview_log.status) as ad2_status'],

//        'media_type' => ['overview_log.media_type as media_type'],
        //穿山甲流量细分
        'tt_rit' => ['1 as tt_rit'],
        //资源位细分
        'tt_inventory_subdivision' => ["case
		when overview_log.csite <= '10000' then 'INVENTORY_FEED'
		when overview_log.csite >= '80000'
		and overview_log.csite <= '110001' then 'INVENTORY_FEED'
		when overview_log.csite >= '10001'
		and overview_log.csite <= '10099' then 'INVENTORY_VIDEO_FEED'
		when overview_log.csite >= '26001'
		and overview_log.csite <= '26099' then 'INVENTORY_VIDEO_FEED'
		when overview_log.csite >= '30001'
		and overview_log.csite <= '30099' then 'INVENTORY_HOTSOON_FEED'
		when overview_log.csite >= '40001'
		and overview_log.csite <= '40099' then 'INVENTORY_AWEME_FEED'
		when overview_log.csite = '800000000' then 'INVENTORY_UNION_SPLASH_SLOT'
		when overview_log.csite = '900000000' then 'INVENTORY_UNION_SLOT'
		when overview_log.csite = '33013' then 'INVENTORY_UNIVERSAL'
		when overview_log.csite = '38016' then 'INVENTORY_SEARCH'
		ELSE ''
	END as tt_inventory_subdivision"],

        'gdt_inventory_subdivision' => ["CASE
		WHEN overview_log.csite = '100' THEN 'SITE_SET_KANDIAN'
		WHEN overview_log.csite = '101' THEN 'SITE_SET_QQ_MUSIC_GAME'
		WHEN overview_log.csite = '28' THEN 'SITE_SET_TENCENT_VIDEO'
		WHEN overview_log.csite = '25' THEN 'SITE_SET_MOBILE_INNER'
		WHEN overview_log.csite = '27' THEN 'SITE_SET_TENCENT_NEWS'
		WHEN overview_log.csite = '102' THEN 'SITE_SET_MOMENTS'
		WHEN overview_log.csite = '15' THEN 'SITE_SET_MOBILE_UNION'
		WHEN overview_log.csite = '21' THEN 'SITE_SET_WECHAT'
		ELSE 'UNKNOWN'
	END as gdt_inventory_subdivision"],
        //自动扩量细分
        'gdt_is_expand_targeting' => ['5 as gdt_is_expand_targeting'],

        //激活设备注册率
//        'action_uid_reg_rate' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count',
//            'SUM(overview_log.day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'],
//        //激活注册率
//        'action_reg_rate' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count',
//            'SUM(overview_log.day_action_muid_count) as sum_day_action_muid_count'],
//        //激活注册率（设备）
//        'action_reg_rate_device' => ['SUM(overview_log.day_reg_muid_distinct_count) as sum_day_reg_muid_distinct_count',
//            'SUM(overview_log.day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'],
//        //注册设备数
//        'reg_muid_count' => ['IFNULL(SUM(overview_log.day_reg_muid_distinct_count), 0 ) as reg_muid_count'],
//        //激活设备数
//        'action_muid_count' => ['IFNULL(SUM(overview_log.day_action_muid_distinct_count), 0) as action_muid_count'],
//        //首日LTV
//        'first_day_ltv' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
//        //首日付费人数
//        'first_day_pay_count' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
//        //首日付费率
//        'first_day_pay_rate' => ['SUM(overview_log.day_first_day_pay_count) as first_day_pay_count',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
//        //首日付费成本
//        'cost_per_first_day_pay' => [
//            '0 as cost',
//            'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'
//        ],
//        //首日付费金额
//        'first_day_pay_money' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money'],
        //有效消耗
        'standard_reached_cost' => [
            '0 as cost',
            '0 as day_30_standard_value',
            'SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'
        ],
        //无效消耗
        'standard_unreached_cost' => [
            '0 as cost',
            '0 as day_30_standard_value',
            'SUM(overview_log.day_thirty_day_pay_money) as sum_thirty_day_pay_money'
        ],
        //首日arppu
//        'first_day_arppu' => ['SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
//            'SUM(overview_log.day_first_day_pay_count) as first_day_pay_count'],
//        //首日roi
//        'first_day_roi' => [
//            'SUM(overview_log.day_first_day_pay_money) as first_day_pay_money',
//            '0 as cost',
//            '0 as 1_day_total_standard_value'
//        ],
//        //注册成本
//        'cost_per_reg' => [
//            '0 as cost',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
//        ],
//        //注册数
//        'reg_uid_count' => ['SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
//        //累计付费回本
//        'total_roi' => [
//            '0 as cost',
//            'SUM(overview_log.day_total_pay_money) as sum_total_pay_money'
//        ],
//        //次留
//        'rate_day_stay_2' => [
//            'SUM(overview_log.day_second_login_count) as count_is_second_login',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
//        ],
//        //三留
//        'rate_day_stay_3' => [
//            'SUM(overview_log.day_third_login_count) as count_is_third_login',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
//        ],
//        //七留
//        'rate_day_stay_7' => [
//            'SUM(overview_log.day_seventh_login_count) as count_is_seventh_login',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
//        ],
//        //十五留
//        'rate_day_stay_15' => [
//            'SUM(overview_log.day_fifteenth_login_count) as count_is_fifteenth_login',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'
//        ],
//        //二回
//        'rate_day_roi_2' => [
//            'SUM(overview_log.day_second_day_pay_money) as sum_second_day_pay_money',
//            '0 as cost',
//            '0 as 2_day_total_standard_value'
//        ],
//        //三回
//        'rate_day_roi_3' => [
//            'SUM(overview_log.day_third_day_pay_money) as sum_third_day_pay_money',
//            '0 as cost',
//            '0 as 3_day_total_standard_value'
//        ],
//        //首日付费次数
//        'first_day_pay_times' => ['SUM(overview_log.day_first_day_pay_times) as first_day_pay_times'],
//        //累计付费次数
//        'total_pay_times' => ['SUM(overview_log.total_pay_times) as total_pay_times'],
//        //首日付费次数成本
//        'cost_first_day_pay_times' => [
//            '0 as cost',
//            'SUM(overview_log.day_first_day_pay_times) as first_day_pay_times'
//        ],
//        //累计付费次数成本
//        'cost_total_pay_times' => [
//            '0 as cost',
//            'SUM(overview_log.total_pay_times) as total_pay_times'
//        ],
//        //业务点击数
//        'click_count' => ['SUM(overview_log.click_count) as click_count'],
//        //次留成本
//        'cost_day_second_login' => [
//            '0 as cost',
//            'SUM(overview_log.day_second_login_count) as count_is_second_login'
//        ],
//        //累计付费人数
//        'total_pay_count' => ['SUM(overview_log.day_total_pay_count) as total_pay_count'],
//        //累计付费金额
//        'total_pay_money' => ['SUM(overview_log.day_total_pay_money) as total_pay_money'],
//        //注册(根老设备)
//        'reg_old_muid_count' =>
//            ['SUM(IF(overview_log.is_old_root_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_muid_count'],
//        //注册(集团老设备)
//        'reg_old_clique_muid_count' =>
//            ['SUM(IF(overview_log.is_old_clique_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_clique_muid_count'],
//        //登录三次以上人数
//        'day_three_login_uid_count' => ['SUM(overview_log.day_three_login_uid_count) as day_three_login_uid_count'],
//        //注册(根老设备)占比
//        'reg_old_muid_percentage' => [
//            'SUM(IF(overview_log.is_old_root_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_muid_count',
//            'SUM(overview_log.day_reg_muid_count) as day_reg_muid_count'
//        ],
//        //注册(集团老设备)占比
//        'reg_old_clique_muid_percentage' => [
//            'SUM(IF(overview_log.is_old_clique_game_muid = 1,overview_log.day_reg_uid_count,0)) as reg_old_clique_muid_count',
//            'SUM(overview_log.day_reg_muid_count) as day_reg_muid_count'
//        ],
//        //登录三次以上人数占比
//        'day_three_login_uid_percentage' => ['SUM(overview_log.day_three_login_uid_count) as day_three_login_uid_count',
//            'SUM(overview_log.day_reg_uid_count) as reg_uid_count'],
//        //首次付费当日充值金额
//        'new_pay_day_money' => ['SUM(overview_log.day_new_pay_day_money) as new_pay_day_money'],
//        //首次付费当日充值人数
//        'new_pay_day_count' => ['SUM(overview_log.day_new_pay_day_count) as new_pay_day_count'],
//        //七回
//        'rate_day_roi_7' => [
//            'SUM(overview_log.day_seventh_day_pay_money) as sum_seven_day_pay_money',
//            '0 as cost',
//            '0 as 7_day_total_standard_value'
//        ],

        /**
         * data_log
         */
        //有效消耗
//        'standard_reached_cost' => ['0 as cost', '0 as sum_thirty_day_pay_money'],
        //无效消耗
//        'standard_unreached_cost' => ['0 as cost', '0 as sum_thirty_day_pay_money'],
        //首日付费次数成本
//        'cost_first_day_pay_times' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_times'],
        //首日付费次数成本
//        'cost_total_pay_times' => ['SUM(data_log.cost) as cost', '0 as total_pay_times'],
        //次留成本
//        'cost_day_second_login' => ['SUM(data_log.cost) as cost', '0 as count_is_second_login'],

        'cost_date' => ['log_date as cost_date'],
//        'count_ad2_deliveried' => ['0 as count_cost_date'],
//        'cost' => ['0 as cost'],
////        'cost_per_reg' => ['SUM(data_log.cost) as cost', '0 as reg_uid_count'],
////        'cost_per_first_day_pay' => ['SUM(data_log.cost) as cost', '0 as first_day_pay_count'],
////        'first_day_roi' => [
////            'SUM(data_log.cost) as cost',
////            '0 as first_day_pay_money',
////            '0 as 1_day_total_standard_value'
////        ],
////        'total_roi' => ['SUM(data_log.cost) as cost', '0 as sum_total_pay_money'],
//        'cost_process' => ['0 as sum_ori_cost_for_cost_process'],
//        'cpc' => ['0 as click', '0 as ori_cost'],
//        'click' => ['0 as click'],
//        'cpm' => ['0 as `show`', '0 as ori_cost'],
//        'show' => ['0 as `show`'],
//        'ori_cost' => ['0 as ori_cost'],
//        'click_rate' => ['0 as click', '0 as `show`'],
//        'cost_per_convert' => ['0 as ori_cost', '0 as `convert`'],
//        'convert' => ['0 as `convert`'],
//        'cost_per_active' => ['0 as active_count', '0 as ori_cost'],
//        'cost_per_pay' => ['0 as pay_count', '0 as ori_cost'],
//        'pay_count' => ['0 as pay_count'],
//        'reg_rate' => ['0 as reg_count', '0 as click'],
//        'active_rate' => ['0 as active_count', '0 as click'],
//        'active_count' => ['0 as active_count'],
//        'convert_rate' => ['0 as `convert`', '0 as click'],
//        'reg_count' => ['0 as reg_count'],
//        'media_cost_per_reg' => ['0 as ori_cost', '0 as reg_count'],
//        'pay_rate' => ['0 as pay_count', '0 as reg_count'],
        //二回
//        'rate_day_roi_2' => [
//            'SUM(data_log.cost) as cost',
//            '0 as sum_second_day_pay_money',
//            '0 as 2_day_total_standard_value'
//        ],
        //三回
//        'rate_day_roi_3' => [
//            'SUM(data_log.cost) as cost',
//            '0 as sum_third_day_pay_money',
//            '0 as 3_day_total_standard_value'
//        ],
        //七回
//        'rate_day_roi_7' => [
//            'SUM(data_log.cost) as cost',
//            '0 as sum_seven_day_pay_money',
//            '0 as 7_day_total_standard_value'
//        ],
    ];

    const CALC_STANDARD_SUB_MAIN_SELECT = [
//        'media_type' => ['max(media_type) as media_type'],
        'port_version' => ['max(port_version) as port_version'],
        'platform' => ['max(platform_tmp) as platform'],
//        //素材文件名｜ID
//        'material_file_id' => ['max(material_file_id) as material_file_id', 'max(material_file_name) as material_file_name'],
//        //素材尺寸
//        'size' => ['max(size) as size'],
//        //素材名｜ID
//        'material_id' => ['max(material_id) as material_id', 'max(material_name) as material_name'],
//        //素材标签
//        'label_pid' => ['max(material_id) as material_id'],
//        //素材细分标签
//        'label' => ['max(material_id) as material_id'],
//        //创意标题
//        'creative_title' => ['max(creative_title) as creative_title'],
//        //缩略图
//        'urls' => ['max(signature) as signature', 'max(file_type) as file_type'],
//        //创意素材操作状态
//        'ad3_opt_status' => ['max(ad3_opt_status) as ad3_opt_status'],
//        //视频md5值
//        'signature' => ['max(signature) as signature'],
//        //广告创意创建时间
//        'ad3_create_time' => ['max(ad3_create_time) as ad3_create_time'],
//        //广告创意更新时间
//        'ad3_modify_time' => ['max(ad3_modify_time) as ad3_modify_time'],
//        //创意素材状态
//        'ad3_status' => ['max(ad3_status) as ad3_status'],
//        //二级修改时间
//        'ad2_modify_time' => ['max(ad2_modify_time) as ad2_modify_time'],
//        //开始消耗时间
//        'start_cost_time' => ['max(start_cost_time) as start_cost_time'],
//        //深度优化出价
//        'deep_cpabid' => ['max(deep_cpabid) as deep_cpabid'],
//        //roi系数
//        'roi_goal' => ['max(roi_goal) as roi_goal'],

//         'platform' => ['ad_log.platform'],
        'os' => ['max(os) as os'],

        'agent_leader' => ['max(agent_leader) as agent_leader'],

        'site_id' => ['max(site_id) as site_id', 'max(site_name) as site_name'],

//        'action_track_type' => ['max(action_track_type) as action_track_type'],

        'aweme_account_id' => ['max(aweme_account_id) as aweme_account_id', 'max(aweme_account_name) as aweme_account_name'],

        'agent_id' => ['max(agent_id) as agent_id', 'max(agent_name) as agent_name'],

        'agent_group_id' => ['max(agent_group_id) as agent_group_id', 'max(agent_group_name) as agent_group_name'],

        'agency_full_name' => ['max(agency_full_name_tmp) as agency_full_name'],

        'create_type' => ['max(create_type) as create_type'],

        'clique_id' => ['max(clique_id) as clique_id', 'max(clique_name) as clique_name'],

        'root_game_id' => ['max(root_game_id) as root_game_id', 'max(root_game_name) as root_game_name'],

        'main_game_id' => ['max(main_game_id) as main_game_id', 'max(main_game_name) as main_game_name'],

        'game_id' => ['max(game_id) as game_id', 'max(game_name) as game_name'],
        'ad2_os' => ['max(ad2_os) as ad2_os'],
        'account_id' => ['max(account_id_tmp) as account_id'],
        'account_name' => ['max(account_name_tmp) as account_name'],
        'ad1_id' => ['max(ad1_id) as ad1_id'],
        'ad1_name' => ['max(ad1_name) as ad1_name'],
        'ad2_id' => ['max(ad2_id) as ad2_id'],
        'ad2_name' => ['max(ad2_name) as ad2_name'],
        'ad3_id' => ['ad3_id as ad3_id'],
        'ad3_name' => ['max(ad3_name) as ad3_name'],
//        'game_name' => ['IFNULL (ad_log.game_name, overview_log.game_name) as game_name'],

        //默认cost_date就是查询字段
//        'cost_date' => ['data_log.cost_date'],
        'ad2_create_time' => ['max(ad2_create_time_tmp) as ad2_create_time'],
        'ad2_status' => ['max(ad2_status) as ad2_status'],
        //广告投放速度类型
        'flow_control_mode' => ['max(flow_control_mode) as flow_control_mode'],
//        //计数项数据
//        'count' => ['SUM(count) as count'],
//        'count_ad2' => ['SUM(count_ad2) as count_ad2'],
//        'count_ad2_deliveried' => ['SUM(count_ad2_deliveried) as count_ad2_deliveried'],
//        'count_ad2_delivering' => ['SUM(count_ad2_delivering) as count_ad2_delivering'],
//        'count_ad2_undeliveried' => ['SUM(count_ad2_undeliveried) as count_ad2_undeliveried'],
//        //cost不能一起聚合，这个计算要放在外面
//        'count_cost_date' => ['SUM(count_cost_date) as count_cost_date'],
//        'count_ad3' => ['SUM(count_ad3) as count_ad3'],
        /**
         * 业务数据
         */
//        'standard_reached_cost' => [
//            'SUM(day_30_standard_value) as day_30_standard_value',
//            'SUM(sum_thirty_day_pay_money) as sum_thirty_day_pay_money',
//            'SUM(cost) as cost'
//        ],
//        'standard_unreached_cost' => [
//            'SUM(day_30_standard_value) as day_30_standard_value',
//            'SUM(sum_thirty_day_pay_money) as sum_thirty_day_pay_money',
//            'SUM(cost) as cost'
//        ],
        //有效消耗
        'standard_reached_cost' => ['if(toFloat64(max(calc_standard_sub.sum_thirty_day_pay_money)) / toFloat64(max(calc_standard_sub.cost)) > toFloat64(max(calc_standard_sub.day_30_standard_value)), max(calc_standard_sub.cost), 0) AS standard_reached_cost'],
        //无效消耗
        'standard_unreached_cost' => ['if(toFloat64(max(calc_standard_sub.sum_thirty_day_pay_money)) / toFloat64(max(calc_standard_sub.cost)) < toFloat64(max(calc_standard_sub.day_30_standard_value)), max(calc_standard_sub.cost), 0) AS standard_unreached_cost'],

//        'cost' => ['SUM(cost) as cost'],
//        'reg_uid_count' => ['SUM(reg_uid_count) as reg_uid_count'],
//        //激活设备注册率
//        'action_uid_reg_rate' => [
//            'SUM(reg_uid_count) as reg_uid_count',
//            'SUM(sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
//        ],
//        //激活注册率
//        'action_reg_rate' => [
//            'SUM(reg_uid_count) as reg_uid_count',
//            'SUM(sum_day_action_muid_count) as sum_day_action_muid_count'
//        ],
//        //激活注册率（设备）
//        'action_reg_rate_device' => [
//            'SUM(sum_day_reg_muid_distinct_count) as sum_day_reg_muid_distinct_count',
//            'SUM(sum_day_action_muid_distinct_count) as sum_day_action_muid_distinct_count'
//        ],
//        //注册成本
//        'cost_per_reg' => [
//            'SUM(cost) as cost',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],

//         'cost_per_reg' => [
//             'IFNULL ( CAST ( (
//         data_log.cost / reg_log.reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_per_reg',
//             'data_log.cost',
//             'reg_log.reg_uid_count'
//         ],
//        //注册设备数
//        'reg_muid_count' => ['SUM(reg_muid_count) as reg_muid_count'],
//        //激活设备数
//        'action_muid_count' => ['SUM(action_muid_count) as action_muid_count'],
//        //首日ltv
////         'first_day_ltv' => ['overview_log.first_day_pay_money', 'overview_log.reg_uid_count'],
//        'first_day_ltv' => [
//            'SUM(first_day_pay_money) as first_day_pay_money',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //首日付费人数
//        'first_day_pay_count' => ['SUM(first_day_pay_count) as first_day_pay_count'],
//        //首日付费率
//        'first_day_pay_rate' => [
//            'SUM(first_day_pay_count) as first_day_pay_count',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //首日付费成本
//        'cost_per_first_day_pay' => ['SUM(cost) as cost', 'SUM(first_day_pay_count) as first_day_pay_count'],
//        //首日付费金额
//        'first_day_pay_money' => ['SUM(first_day_pay_money) as first_day_pay_money'],
//        //首日arppu
////         'first_day_arppu' => ['overview_log.first_day_pay_money', 'overview_log.first_day_pay_count'],
//        'first_day_arppu' => [
//            'SUM(first_day_pay_money) as first_day_pay_money',
//            'SUM(first_day_pay_count) as first_day_pay_count'
//        ],
//        //首日ROI
////         'first_day_roi' => ['overview_log.first_day_pay_money', 'data_log.ori_cost'],
//        'first_day_roi' => [
//            'SUM(first_day_pay_money) as first_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(1_day_total_standard_value) as 1_day_total_standard_value'
//        ],
//        //二回
//        'rate_day_roi_2' => [
//            'SUM(sum_second_day_pay_money) as sum_second_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(2_day_total_standard_value) as 2_day_total_standard_value'
//        ],
//        //三回
//        'rate_day_roi_3' => [
//            'SUM(sum_third_day_pay_money) as sum_third_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(3_day_total_standard_value) as 3_day_total_standard_value'
//        ],
//        //七回
//        'rate_day_roi_7' => [
//            'SUM(sum_seven_day_pay_money) as sum_seven_day_pay_money',
//            'SUM(cost) as cost',
//            'SUM(7_day_total_standard_value) as 7_day_total_standard_value'
//        ],
//        //累计付费回本
//        'total_roi' => [
//            'SUM(sum_total_pay_money) as sum_total_pay_money',
//            'SUM(cost) as cost'
//        ],
//        //次留
//        'rate_day_stay_2' => [
//            'SUM(count_is_second_login) as count_is_second_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //三留
//        'rate_day_stay_3' => [
//            'SUM(count_is_third_login) as count_is_third_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //七留
//        'rate_day_stay_7' => [
//            'SUM(count_is_seventh_login) as count_is_seventh_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //十五留
//        'rate_day_stay_15' => [
//            'SUM(count_is_fifteenth_login) as count_is_fifteenth_login',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//
//        /**
//         * 通用属性（switch在model中特殊处理）
//         */
//        //开关
//        'switch' => ['max(switch) as switch'],
//        //操作状态
//        'advertising_opt_status' => ['max(advertising_opt_status) as advertising_opt_status'],
//        //投放时段
//        'advertising_schedule' => ['max(advertising_schedule) as advertising_schedule'],
//        //资源位
        'inventory_type' => ['max(inventory_type) as inventory_type'],
//        //出价
//        'cpa_bid' => ['max(cpa_bid) as cpa_bid'],
//        //广告预算
//        'budget' => ['max(budget) as budget'],
//        //付费方式
//        'pricing' => ['max(pricing) as pricing'],
//        //当天账户币消耗/预算
//        'cost_process' => [
//            'SUM(sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
//            'SUM(sum_budget_for_cost_process) as sum_budget_for_cost_process'
//        ],
//        /**
//         * 通用展示
//         */
//        //cpc
//        'cpc' => ['SUM(click) as click', 'SUM(ori_cost) as ori_cost'],
//        //点击数
//        'click' => ['SUM(click) as click'],
//        //cpm
//        'cpm' => ['SUM(`show`) as show', 'SUM(ori_cost) as ori_cost'],
//        //show
//        'show' => ['SUM(`show`) as show'],
//        //总花费
//        'ori_cost' => ['SUM(ori_cost) as ori_cost'],
//        /**
//         * 通用转化
//         */
//        //点击率
////         'click_rate' => ['data_log.click', 'data_log.`show`'],
//        'click_rate' => [
//            'SUM(click) as click',
//            'SUM(`show`) as show'
//        ],
//        //转化成本
//        'cost_per_convert' => ['SUM(ori_cost) as ori_cost', 'SUM(`convert`) as convert'],
//        //转化数
//        'convert' => ['SUM(`convert`) as convert'],
//        //激活成本
//        'cost_per_active' => ['SUM(ori_cost) as ori_cost', 'SUM(active_count) as active_count'],
//        //付费成本
//        'cost_per_pay' => ['SUM(`convert`) as convert', 'SUM(pay_count) as pay_count'],
//        //付费数
//        'pay_count' => ['SUM(pay_count) as pay_count'],
//        //注册率
//        'reg_rate' => ['SUM(reg_count) as reg_count', 'SUM(click) as click'],
//        //激活率
//        'active_rate' => ['SUM(active_count) as active_count', 'SUM(click) as click'],
//        //激活数
//        'active_count' => ['SUM(active_count) as active_count'],
//        //转化率
////         'convert_rate' => ['SUM(`convert`) as convert', 'SUM(click) as click'],
//        'convert_rate' => [
//            'SUM(`convert`) as convert',
//            'SUM(click) as click'
//        ],
//        //注册数
//        'reg_count' => ['SUM(reg_count) as reg_count'],
//        //注册成本(媒体)
//        'media_cost_per_reg' => [
//            'SUM(ori_cost) as ori_cost',
//            'SUM(reg_count) as reg_count'
//        ],
//        //付费率
////         'pay_rate' => ['data_log.pay_count', 'data_log.reg_count'],
//        'pay_rate' => [
//            'SUM(pay_count) as pay_count',
//            'SUM(reg_count) as reg_count'
//        ],

        /**
         * 账户通用属性
         */
        //公司
        'company' => ['max(company_tmp) as company'],
//        //代理商
//        'agency_name' => ['max(agency_name_tmp) as agency_name'],
//        //总余额
//        'balance' => ['max(balance) as balance'],
//        //广告主状态
//        'account_status' => ['max(account_status_tmp) as account_status'],
//        //账户审核拒绝原因
//        'reason' => ['max(reason) as reason'],
//        //媒体账户负责人
//        'account_leader' => ['max(account_leader_tmp) as account_leader'],
//        //账户预算
//        'account_budget' => ['max(account_budget_tmp) as account_budget'],
//
//        //一级预算
//        'ad1_budget' => ['max(ad1_budget) as ad1_budget'],
//        //一级状态
//        'ad1_status' => ['max(ad1_status) as ad1_status'],
//        //推广目的
//        'ad1_type' => ['max(ad1_type) as ad1_type'],
//        //一级创建时间
//        'ad1_create_time' => ['max(ad1_create_time) as ad1_create_time'],
//        //一级修改时间
//        'ad1_modify_time' => ['max(ad1_modify_time) as ad1_modify_time'],
//        //首日付费次数成本
//        'cost_first_day_pay_times' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / overview_log.first_day_pay_times
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_first_day_pay_times',
//            'SUM(cost) as cost',
//            'SUM(first_day_pay_times) as first_day_pay_times'
//        ],
//        //累计付费次数成本
//        'cost_total_pay_times' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / overview_log.total_pay_times
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_total_pay_times',
//            'SUM(cost) as cost',
//            'SUM(total_pay_times) as total_pay_times'
//        ],
//        //首日付费次数
//        'first_day_pay_times' => ['SUM(first_day_pay_times) as first_day_pay_times'],
//        //累计付费次数
//        'total_pay_times' => ['SUM(total_pay_times) as total_pay_times'],
//        //业务点击数
//        'click_count' => ['SUM(click_count) as click_count'],
//        //次留成本
//        'cost_day_second_login' => [
////             'IFNULL ( CAST ( (
////         data_log.cost / overview_log.count_is_second_login
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_day_second_login',
//            'SUM(cost) as cost',
//            'SUM(count_is_second_login) as count_is_second_login'
//        ],
//        //累计付费人数
//        'total_pay_count' => ['SUM(total_pay_count) as total_pay_count'],
//        //累计付费金额
//        'total_pay_money' => ['SUM(total_pay_money) as total_pay_money'],
//        //注册(根老设备)
//        'reg_old_muid_count' => ['SUM(reg_old_muid_count) as reg_old_muid_count'],
//        //注册(集团老设备)
//        'reg_old_clique_muid_count' => ['SUM(reg_old_clique_muid_count) as reg_old_clique_muid_count'],
//        //登录三次以上人数
//        'day_three_login_uid_count' => ['SUM(day_three_login_uid_count) as day_three_login_uid_count'],
//        //注册(根老设备)占比
//        'reg_old_muid_percentage' => [
////             'IFNULL ( CAST ( (
////         overview_log.reg_old_muid_count / overview_log.day_reg_muid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS reg_old_muid_percentage',
//            'SUM(reg_old_muid_count) as reg_old_muid_count',
//            'SUM(day_reg_muid_count) as day_reg_muid_count'
//        ],
//        //注册(集团老设备)占比
//        'reg_old_clique_muid_percentage' => [
////             'IFNULL ( CAST ( (
////         overview_log.reg_old_clique_muid_count / overview_log.day_reg_muid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS reg_old_clique_muid_percentage',
//            'SUM(reg_old_clique_muid_count) as reg_old_clique_muid_count',
//            'SUM(day_reg_muid_count) as day_reg_muid_count'
//        ],
//        //登录三次以上人数占比
//        'day_three_login_uid_percentage' => [
////             'IFNULL ( CAST ( (
////         overview_log.day_three_login_uid_count / overview_log.reg_uid_count
////         ) AS DECIMAL ( 10, 4 ) ), 0 ) AS day_three_login_uid_percentage',
//            'SUM(day_three_login_uid_count) as day_three_login_uid_count',
//            'SUM(reg_uid_count) as reg_uid_count'
//        ],
//        //首次付费当日充值金额
//        'new_pay_day_money' => ['SUM(new_pay_day_money) as new_pay_day_money'],
//        //首次付费当日充值人数
//        'new_pay_day_count' => ['SUM(new_pay_day_count) as new_pay_day_count'],

    ];

    const CALC_STANDARD_MAIN_SELECT = [
        'media_type' => ['any(media_type) as media_type'],
        //有效消耗
        'standard_reached_cost' => ['sum(standard_reached_cost) as standard_reached_cost'],
        //无效消耗
        'standard_unreached_cost' => ['sum(standard_unreached_cost) as standard_unreached_cost'],
        'port_version' => ['any(port_version) as port_version'],

        //素材文件名｜ID
        'material_file_id' => [
            "0 as material_file_id",
            "'' as material_file_name"
        ],
        //素材尺寸
        'size' => ["'' as size"],
        //素材名｜ID
        'material_id' => [
            "0 as material_id",
            "'' as material_name"
        ],
        //创意标题
        'creative_title' => ["'' as creative_title"],
        //缩略图
        'urls' => ["'' as signature", "'' as file_type"],
        //创意素材操作状态
        'ad3_opt_status' => ["'' as ad3_opt_status"],
        //视频md5值
        'signature' => ["'' as signature"],
        //广告创意创建时间
        'ad3_create_time' => ["toDateTime('1970-01-01') as ad3_create_time"],
        //广告创意更新时间
        'ad3_modify_time' => ["toDateTime('1970-01-01') as ad3_modify_time"],

        //创意素材状态
        'ad3_status' => ["'' as ad3_status"],
        //二级修改时间
        'ad2_modify_time' => ["toDateTime('1970-01-01') as ad2_modify_time"],
        //开始消耗时间
        'start_cost_time' => ["toDateTime('1970-01-01') as start_cost_time"],
        //深度优化出价
        'deep_cpabid' => ['0 as deep_cpabid'],
        'roi_goal' => ['0 as roi_goal'],

        //计数项数据
        'count' => ['0 as count'],
        'count_ad2' => ['0 as count_ad2'],
        'count_ad2_delivering' => ['0 as count_ad2_delivering'],
        'count_ad2_undeliveried' => ['0 as count_ad2_undeliveried'],
        'count_ad3' => ['0 as count_ad3'],
        //开关
//        'switch' => ["'' as switch"],
        //操作状态
        'advertising_opt_status' => ["'' as advertising_opt_status"],
        //投放时段
        'advertising_schedule' => ["'' as advertising_schedule"],
        //出价
        'cpa_bid' => ['0 as cpa_bid'],
        //广告预算
        'budget' => ['0 as budget'],
        //付费方式
        'pricing' => ["'' as pricing"],
        //综合评分
        'predict_overall_score' => ["'' as predict_overall_score"],
        //当天账户币消耗/预算
//            'cost_process' => ['0 as sum_budget_for_cost_process'],
        /**
         * 账户通用属性
         */
        //代理商
        'agency_name' => ["'' as agency_name"],
        //总余额
        'balance' => ['0 as balance'],
        //广告主状态
        'account_status' => ["'' as account_status"],
        //账户审核拒绝原因
        'reason' => ["'' as reason"],
        //媒体账户负责人
        'account_leader' => ["'' as account_leader"],
        //账户预算
        'account_budget' => ['0 as account_budget'],

        //一级预算
        'ad1_budget' => ['0 as ad1_budget'],
        //一级状态
        'ad1_status' => ["'' as ad1_status"],
        //推广目的
        'ad1_type' => ["'' as ad1_type"],
        //一级修改时间
        'ad1_modify_time' => ["toDateTime('1970-01-01') as ad1_modify_time"],
        //一级创建时间
        'ad1_create_time' => ["toDateTime('1970-01-01') as ad1_create_time"],


        //素材标签
        'label_pid' => ['0 as material_id'],
        //素材细分标签
        'label' => ['0 as material_id'],

        'platform' => ['any(platform) as platform_tmp'],

        'os' => ['os as os'],

        'agent_leader' => ['agent_leader as agent_leader'],

        'site_id' => [
            'site_id as site_id',
            'any(site_name) as site_name'
        ],

//        'action_track_type' => ['action_track_type as action_track_type'],

        'aweme_account_id' => [
            'aweme_account_id as aweme_account_id',
            'any(aweme_account_name) as aweme_account_name'
        ],

        'agent_id' => [
            'agent_id as agent_id',
            'any(agent_name) as agent_name'
        ],

        'agent_group_id' => [
            'agent_group_id as agent_group_id',
            'any(agent_group_name) as agent_group_name'
        ],

        'agency_full_name' => ['agency_full_name as agency_full_name_tmp'],

        'create_type' => ['create_type as create_type'],

        'clique_id' => [
            'clique_id as clique_id',
            'any(clique_name) as clique_name'
        ],

        'root_game_id' => [
            'root_game_id as root_game_id',
            'any(root_game_name) as root_game_name'
        ],

        'main_game_id' => [
            'main_game_id as main_game_id',
            'any(main_game_name) as main_game_name'
        ],

        'game_id' => [
            'game_id as game_id',
            'any(game_name) as game_name'
        ],

        'ad2_os' => ['ad2_os as ad2_os'],

        'account_id' => ['any(account_id) as account_id_tmp'],

        'account_name' => [
            'any(account_name) as account_name_tmp',
//            'any(account_id) as account_id'
        ],

        'ad1_id' => ['any(ad1_id) as ad1_id'],

        'ad1_name' => [
            'any(ad1_name) as ad1_name',
//            'any(ad1_id) as ad1_id'
        ],

        'ad2_id' => ['any(ad2_id) as ad2_id'],

        'ad2_name' => [
            'any(ad2_name) as ad2_name',
//            'any(ad2_id) as ad2_id'
        ],

        'ad3_id' => ['ad3_id as ad3_id'],

        'ad3_name' => [
            'any(ad3_name) as ad3_name',
//            'ad3_id as ad3_id'
        ],

        'game_name' => ['any(game_name) as game_name'],

        'ad2_create_time' => ['ad2_create_time as ad2_create_time'],

        'ad2_status' => ['ad2_status as ad2_status'],

        /**
         * 通用属性（switch在model中特殊处理）
         */
        //开关
//        'switch' => ["'' as switch"],
        //资源位
        'inventory_type' => ['inventory_type as inventory_type'],
        //广告投放速度类型
        'flow_control_mode' => ['flow_control_mode as flow_control_mode'],
        /**
         * 账户通用属性
         */
        //公司
        'company' => ['company as company_tmp'],

        /*----------------------TODO*/
        //首日付费次数成本
        'cost_first_day_pay_times' => ['0 as cost', '0 as first_day_pay_times'],
        //首日付费次数成本
        'cost_total_pay_times' => ['0 as cost', '0 as total_pay_times'],
        //次留成本
        'cost_day_second_login' => ['0 as cost', '0 as count_is_second_login'],

        'cost_date' => ["'' as cost_date"],
        'count_ad2_deliveried' => ['0 AS count_ad2_deliveried'],
        'count_cost_date' => ['0 as count_cost_date'],
        'cost' => ['0 as cost'],
        'cost_per_reg' => ['0 as cost', '0 as reg_uid_count'],
        'cost_per_first_day_pay' => ['0 as cost', '0 as first_day_pay_count'],
        'first_day_roi' => ['0 as cost', '0 as first_day_pay_money', '0 as 1_day_total_standard_value'],
        'total_roi' => ['0 as cost', '0 as sum_total_pay_money'],
        'cost_process' => ['0 as sum_budget_for_cost_process', '0 as sum_ori_cost_for_cost_process'],
        'cpc' => ['0 as click', '0 as ori_cost'],
        'click' => ['0 as click'],
        'cpm' => ['0 as `show`', '0 as ori_cost'],
        'show' => ['0 as `show`'],
        'ori_cost' => ['0 as ori_cost'],
        'click_rate' => ['0 as click', '0 as `show`'],
        'cost_per_convert' => ['0 as ori_cost', '0 as `convert`'],
        'convert' => ['0 as `convert`'],
        'cost_per_active' => ['0 as active_count', '0 as ori_cost'],
        'cost_per_pay' => ['0 as pay_count', '0 as ori_cost'],
        'pay_count' => ['0 as pay_count'],
        'reg_rate' => ['0 as reg_count', '0 as click'],
        'active_rate' => ['0 as active_count', '0 as click'],
        'active_count' => ['0 as active_count'],
        'convert_rate' => ['0 as `convert`', '0 as click'],
        'reg_count' => ['0 as reg_count'],
        'media_cost_per_reg' => ['0 as ori_cost', '0 as reg_count'],
        'pay_rate' => ['0 as pay_count', '0 as reg_count'],
        //二回
        'rate_day_roi_2' => ['0 as sum_second_day_pay_money', '0 as cost', '0 as 2_day_total_standard_value'],
        //三回
        'rate_day_roi_3' => ['0 as sum_third_day_pay_money', '0 as cost', '0 as 3_day_total_standard_value'],
        //七回
        'rate_day_roi_7' => ['0 as sum_seven_day_pay_money', '0 as cost', '0 as 7_day_total_standard_value'],

        //穿山甲流量细分
        'tt_rit' => ['0 as tt_rit'],
        //资源位细分
        'tt_inventory_subdivision' => ["'' as tt_inventory_subdivision"],

        'gdt_inventory_subdivision' => ["''  as gdt_inventory_subdivision"],
        //自动扩量细分
        'gdt_is_expand_targeting' => ['0 as gdt_is_expand_targeting'],
//        ],

//        'overview_log' => [
        //激活设备注册率
        'action_uid_reg_rate' => ['0 as reg_uid_count', '0 as sum_day_action_muid_distinct_count'],
        //激活注册率
        'action_reg_rate' => ['0 as reg_uid_count', '0 as sum_day_action_muid_count'],
        //激活注册率（设备）
        'action_reg_rate_device' => ['0 as sum_day_reg_muid_distinct_count', '0 as sum_day_action_muid_distinct_count'],
        //注册设备数
        'reg_muid_count' => ['0 as reg_muid_count'],
        //激活设备数
        'action_muid_count' => ['0 as action_muid_count'],
        //首日LTV
        'first_day_ltv' => ['0 as first_day_pay_money', '0 as reg_uid_count'],
        //首日付费人数
        'first_day_pay_count' => ['0 as first_day_pay_count'],
        //首日付费率
        'first_day_pay_rate' => ['0 as first_day_pay_count', '0 as reg_uid_count'],
        //首日付费金额
        'first_day_pay_money' => ['0 as first_day_pay_money'],
        //首日arppu
        'first_day_arppu' => ['0 as first_day_pay_money', '0 as first_day_pay_count'],
        //注册数
        'reg_uid_count' => ['0 as reg_uid_count'],
        //次留
        'rate_day_stay_2' => [
            '0 as count_is_second_login',
            '0 as reg_uid_count'
        ],
        //三留
        'rate_day_stay_3' => [
            '0 as count_is_third_login',
            '0 as reg_uid_count'
        ],
        //七留
        'rate_day_stay_7' => [
            '0 as count_is_seventh_login',
            '0 as reg_uid_count'
        ],
        //十五留
        'rate_day_stay_15' => [
            '0 as count_is_fifteenth_login',
            '0 as reg_uid_count'
        ],
        /**
         * new
         */
        //首日付费次数
        'first_day_pay_times' => ['0 as first_day_pay_times'],
        //累计付费次数
        'total_pay_times' => ['0 as total_pay_times'],
        //业务点击数
        'click_count' => ['0 as click_count'],
        //累计付费人数
        'total_pay_count' => ['0 as total_pay_count'],
        //累计付费金额
        'total_pay_money' => ['0 as total_pay_money'],
        //上报媒体首日付费金额
        'callback_first_day_pay_money' => ['0 as callback_first_day_pay_money'],
        //上报媒体首日付费人数
        'callback_first_day_pay_uid_count' => ['0 as callback_first_day_pay_uid_count'],
        //注册(根老设备)
        'reg_old_muid_count' => ['0 as reg_old_muid_count'],
        //注册(集团老设备)
        'reg_old_clique_muid_count' => ['0 as reg_old_clique_muid_count'],
        //登录三次以上人数
        'day_three_login_uid_count' => ['0 as day_three_login_uid_count'],
        //注册(根老设备)占比
        'reg_old_muid_percentage' => [
            '0 as reg_old_muid_count',
            '0 as day_reg_muid_count'
        ],
        //注册(集团老设备)占比
        'reg_old_clique_muid_percentage' => [
            '0 as reg_old_clique_muid_count',
            '0 as day_reg_muid_count'
        ],
        //登录三次以上人数占比
        'day_three_login_uid_percentage' => ['0 as day_three_login_uid_count', '0 as reg_uid_count'],
        //首次付费当日充值金额
        'new_pay_day_money' => ['0 as new_pay_day_money'],
        //首次付费当日充值人数
        'new_pay_day_count' => ['0 as new_pay_day_count'],
    ];

    const BUSINESS_TARGET = [
        'reg_uid_count', 'action_uid_reg_rate', 'action_reg_rate', 'action_reg_rate_device', 'reg_muid_count', 'action_muid_count', 'first_day_ltv', 'first_day_pay_count', 'first_day_pay_rate', 'first_day_pay_money', 'first_day_arppu', 'first_day_roi', 'rate_day_roi_2','rate_day_roi_3', 'rate_day_stay_2', 'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_15', 'first_day_pay_times', 'total_pay_times', 'click_count', 'total_pay_count', 'total_pay_money', 'callback_first_day_pay_money', 'callback_first_day_pay_uid_count', 'reg_old_muid_count', 'reg_old_clique_muid_count', 'day_three_login_uid_count', 'reg_old_muid_percentage', 'reg_old_clique_muid_percentage', 'day_three_login_uid_percentage', 'new_pay_day_money', 'new_pay_day_count', 'rate_day_roi_7'
    ];

    /**
     * 排序所用字段
     */
    const ORDER_SELECT = [
        //一阶段花费(微信)
        'gdt_wechat_cost_stage1' => ['gdt_wechat_cost_stage1'],
        //二阶段花费(微信)
        'gdt_wechat_cost_stage2' => ['gdt_wechat_cost_stage2'],
        //计数项数据
        'count' => ['count'],
        'count_ad2' => ['count_ad2'],
        'count_ad2_deliveried' => ['count_ad2_deliveried'],
        'count_ad2_delivering' => ['count_ad2_delivering'],
        'count_ad2_undeliveried' => ["count_ad2_undeliveried"],
        'count_cost_date' => ['count_cost_date'],
        'count_ad3' => ['count_ad3'],
        /**
         * 业务数据
         */
        'cost' => ['cost'],
        'reg_uid_count' => ['reg_uid_count'],
        //激活设备注册率
        'action_uid_reg_rate' => [
            'if(max(sub_main_log.sum_day_action_muid_distinct_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.reg_uid_count) / max(sub_main_log.sum_day_action_muid_distinct_count), 4))',
//            'IFNULL ( CAST ( (
//             reg_uid_count / sum_day_action_muid_distinct_count
//             ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //激活注册率
        'action_reg_rate' => [
            'if(max(sub_main_log.sum_day_action_muid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.reg_uid_count) / max(sub_main_log.sum_day_action_muid_count), 4))',
//            'IFNULL ( CAST ( (
//             reg_uid_count / sum_day_action_muid_count
//             ) AS DECIMAL ( 10, 4 ) ), 0)'
        ],
        //激活注册率（设备）
        'action_reg_rate_device' => [
            'if(max(sub_main_log.sum_day_action_muid_distinct_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_day_reg_muid_distinct_count) / max(sub_main_log.sum_day_action_muid_distinct_count), 4))',
//            'IFNULL ( CAST ( (
//             sum_day_reg_muid_distinct_count / sum_day_action_muid_distinct_count
//             ) AS DECIMAL ( 10, 4 ) ), 0)'
        ],
        //注册成本
        'cost_per_reg' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.cost) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         cost / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //注册设备数
        'reg_muid_count' => ['reg_muid_count'],
        //激活设备数
        'action_muid_count' => ['action_muid_count'],
        //首日ltv
        'first_day_ltv' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_money) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//             first_day_pay_money / reg_uid_count
//             ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //首日付费人数
        'first_day_pay_count' => ['first_day_pay_count'],
        //首日付费率
        'first_day_pay_rate' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_count) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         first_day_pay_count / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //首日付费成本
        'cost_per_first_day_pay' => [
            'if(max(sub_main_log.first_day_pay_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.cost) / max(sub_main_log.first_day_pay_count), 4))',
//            'IFNULL ( CAST ( (
//         cost / first_day_pay_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //首日付费金额
        'first_day_pay_money' => ['first_day_pay_money'],
        //首日arppu
        'first_day_arppu' => [
            'if(max(sub_main_log.first_day_pay_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_money) / max(sub_main_log.first_day_pay_count), 4))',
//            'IFNULL ( CAST ( (
//         first_day_pay_money / first_day_pay_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //首日ROI
        'first_day_roi' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.first_day_pay_money) / max(sub_main_log.cost), 4))',
//            'IFNULL ( CAST ( (
//         first_day_pay_money / cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //二回
        'rate_day_roi_2' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_second_day_pay_money) / max(sub_main_log.cost), 4))',
//            'IFNULL ( CAST ( (
//         sum_second_day_pay_money / cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //三回
        'rate_day_roi_3' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_third_day_pay_money) / max(sub_main_log.cost), 4))',
//            'IFNULL ( CAST ( (
//         sum_third_day_pay_money / cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //累计付费回本
        'total_roi' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_total_pay_money) / max(sub_main_log.cost), 4))',
//            'IFNULL ( CAST ( (
//         sum_total_pay_money / cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //次留
        'rate_day_stay_2' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.count_is_second_login) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         count_is_second_login / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //三留
        'rate_day_stay_3' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.count_is_third_login) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         count_is_third_login / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //七留
        'rate_day_stay_7' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.count_is_seventh_login) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         count_is_seventh_login / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //十五留
        'rate_day_stay_15' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.count_is_fifteenth_login) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         count_is_fifteenth_login / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],

        /**
         * 通用属性（switch在model中特殊处理）
         */
//         //开关
//         'switch' => ['switch'],
//         //操作状态
//         'advertising_opt_status' => ['advertising_opt_status'],
//         //投放时段
//         'advertising_schedule' => ['advertising_schedule'],
//         //资源位
//         'inventory_type' => ['inventory_type'],
        //出价
        'cpa_bid' => ['cpa_bid'],
        //广告预算
        'budget' => ['budget'],
        //深度优化出价
        'deep_cpabid' => ['deep_cpabid'],
        //roi系数
        'roi_goal' => ['roi_goal'],
        //付费方式
//         'pricing' => ['pricing'],
//         //广告投放速度类型
//         'flow_control_mode' => ['flow_control_mode'],

        //当天账户币消耗/预算
        'cost_process' => [
            'if(max(sub_main_log.sum_budget_for_cost_process) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_ori_cost_for_cost_process) / max(sub_main_log.sum_budget_for_cost_process), 4))',
//            'IFNULL ( CAST ( (
//         sum_ori_cost_for_cost_process / sum_budget_for_cost_process
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        /**
         * 通用展示
         */
        //cpc
        'cpc' => [
            'if(max(sub_main_log.ori_cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.click) / max(sub_main_log.ori_cost), 4))',
//            'IFNULL ( CAST ( (
//         click / ori_cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //点击数
        'click' => ['click'],
        //cpm
        'cpm' => [
            'if(max(sub_main_log.ori_cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.show) / max(sub_main_log.ori_cost), 4))',
//            'IFNULL ( CAST ( (
//         `show` / ori_cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //show
        'show' => ['`show`'],
        //总花费
        'ori_cost' => ['ori_cost'],
        /**
         * 通用转化
         */
        //点击率
        'click_rate' => [
            'if(max(sub_main_log.show) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.click) / max(sub_main_log.show), 4))',
//            'IFNULL ( CAST ( (
//         click / `show`
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //转化成本
        'cost_per_convert' => [
            'if(max(sub_main_log.convert) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.convert), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / `convert`
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //转化数
        'convert' => ['`convert`'],
        //激活成本
        'cost_per_active' => [
            'if(max(sub_main_log.ori_cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.active_count) / max(sub_main_log.ori_cost), 4))',
//            'IFNULL ( CAST ( (
//         active_count / ori_cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //付费成本
        'cost_per_pay' => [
            'if(max(sub_main_log.ori_cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.pay_count) / max(sub_main_log.ori_cost), 4))',
//            'IFNULL ( CAST ( (
//         pay_count / ori_cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //付费数
        'pay_count' => ['pay_count'],
        //注册率
        'reg_rate' => [
            'if(max(sub_main_log.click) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.reg_count) / max(sub_main_log.click), 4))',
//            'IFNULL ( CAST ( (
//         reg_count / click
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //激活率
        'active_rate' => [
            'if(max(sub_main_log.click) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.active_count) / max(sub_main_log.click), 4))',
//            'IFNULL ( CAST ( (
//         active_count / click
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //激活数
        'active_count' => ['active_count'],
        //转化率
        'convert_rate' => [
            'if(max(sub_main_log.click) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.convert) / max(sub_main_log.click), 4))',
//            'IFNULL ( CAST ( (
//         `convert` / click
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //注册数
        'reg_count' => ['reg_count'],
        //注册成本(媒体)
        'media_cost_per_reg' => [
            'if(max(sub_main_log.reg_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.reg_count), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / reg_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //付费率
        'pay_rate' => [
            'if(max(sub_main_log.reg_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.pay_count) / max(sub_main_log.reg_count), 4))',
//            'IFNULL ( CAST ( (
//         pay_count / reg_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        /**
         * 今日头条计划属性
         */
//         //学习期状态
//         'tt_learning_phase' => ['tt_learning_phase'],
//         //广告应用下载包名
//         'tt_package' => ['tt_package'],
//         //广告投放时间类型
//         'tt_schedule_type' => ['tt_schedule_type'],
//         //convert_id
//         'tt_convert_id' => ['tt_convert_id'],
//         //深度转化ROI系数
//         'tt_roi_goal' => ['tt_roi_goal'],
//         //adjust_cpa
//         'tt_adjust_cpa' => ['tt_adjust_cpa'],
//         //深度优化方式
//         'tt_deep_bid_type' => ['tt_deep_bid_type'],
//         //download_url
//         'tt_download_url' => ['tt_download_url'],
//         //external_url
//         'tt_external_url' => ['tt_external_url'],
//         //穿山甲类型
//         'tt_union_video_type' => ['tt_union_video_type'],
//         //预算类型
//         'tt_ad2_budget_mode' => ['tt_ad2_budget_mode'],

        /**
         * 今日头条转化数据
         */
        //次留回传数
        'tt_attribution_next_day_open_cnt' => ['tt_attribution_next_day_open_cnt'],
        //完件数
        'tt_loan_completion' => ['tt_loan_completion'],
        //完件成本
        'tt_loan_completion_cost' => [
            'if(max(sub_main_log.tt_loan_completion) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_loan_completion), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_loan_completion
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //完件率
        'tt_loan_completion_rate' => [
            'if(max(sub_main_log.reg_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_loan_completion) / max(sub_main_log.reg_count), 4))',
//            'IFNULL ( CAST ( (
//         tt_loan_completion / reg_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //安卓下载完成数
        'tt_download_finish' => ['tt_download_finish'],
        //安卓安装完成数
        'tt_install_finish' => ['tt_install_finish'],
        //安卓下载完成成本
        'tt_download_finish_cost' => [
            'if(max(sub_main_log.tt_download_finish) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_download_finish), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_download_finish
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //安卓安装完成率
        'tt_install_finish_rate' => [
            'if(max(sub_main_log.tt_download_start) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_install_finish) / max(sub_main_log.tt_download_start), 4))',
//            'IFNULL ( CAST ( (
//         tt_install_finish / tt_download_start
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //安卓下载开始率
        'tt_download_start_rate' => [
            'if(max(sub_main_log.click) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_download_start) / max(sub_main_log.click), 4))',
//            'IFNULL ( CAST ( (
//         tt_download_start / click
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //安卓下载开始成本
        'tt_download_start_cost' => [
            'if(max(sub_main_log.tt_download_start) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_download_start), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_download_start
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //安卓下载开始数
        'tt_download_start' => ['tt_download_start'],
        //安卓安装完成成本
        'tt_install_finish_cost' => [
            'if(max(sub_main_log.tt_install_finish) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_install_finish), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_install_finish
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //点击安装数
        'tt_click_install' => ['tt_click_install'],
        //深度转化率
        'tt_deep_convert_rate' => [
            'if(max(sub_main_log.tt_convert) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_deep_convert) / max(sub_main_log.tt_convert), 4))',
//            'IFNULL ( CAST ( (
//         tt_deep_convert / tt_convert
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //深度转化数
        'tt_deep_convert' => ['tt_deep_convert'],
        //深度转化成本
        'tt_deep_convert_cost' => [
            'if(max(sub_main_log.tt_deep_convert) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_deep_convert), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_deep_convert
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //安卓下载完成率
        'tt_download_finish_rate' => [
            'if(max(sub_main_log.tt_download_start) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_download_finish) / max(sub_main_log.tt_download_start), 4))',
//            'IFNULL ( CAST ( (
//         tt_download_finish / tt_download_start
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //次留率
        'tt_next_day_open_rate' => [
            'if(max(sub_main_log.active_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_next_day_open) / max(sub_main_log.active_count), 4))',
//            'IFNULL ( CAST ( (
//         tt_next_day_open / active_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //次留成本
        'tt_next_day_open_cost' => [
            'if(max(sub_main_log.tt_next_day_open) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_next_day_open), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_next_day_open
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //次留
        'tt_next_day_open' => ['tt_next_day_open'],
        //关键行为数
        'tt_game_addiction' => ['tt_game_addiction'],
        //关键行为率
        'tt_game_addiction_rate' => [
            'if(max(sub_main_log.active_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_game_addiction) / max(sub_main_log.active_count), 4))',
//            'IFNULL ( CAST ( (
//         tt_game_addiction / active_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //关键行为成本
        'tt_game_addiction_cost' => [
            'if(max(sub_main_log.tt_game_addiction) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_game_addiction), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_game_addiction
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        /**
         * 今日头条视频数据
         */
        //75%进度播放数
        'tt_play_75_feed_break' => ['tt_play_75_feed_break'],
        //99%进度播放数
        'tt_play_100_feed_break' => ['tt_play_100_feed_break'],
        //平均单次播放时长
        'tt_average_play_time_per_play' => [
            'if(max(sub_main_log.tt_total_play) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_play_duration_sum) / max(sub_main_log.tt_total_play), 4))',
//            'IFNULL ( CAST ( (
//         tt_play_duration_sum / tt_total_play
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //播放时长
        'tt_play_duration_sum' => ['tt_play_duration_sum'],
        //Wi-Fi播放占比
        'tt_wifi_play_rate' => [
            'if(max(sub_main_log.tt_total_play) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_wifi_play) / max(sub_main_log.tt_total_play), 4))',
//            'IFNULL ( CAST ( (
//         tt_wifi_play / tt_total_play
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //有效播放数
        'tt_valid_play' => ['tt_valid_play'],
        //25%进度播放数
        'tt_play_25_feed_break' => ['tt_play_25_feed_break'],
        //播放率
        'tt_play_over_rate' => [
            'if(max(sub_main_log.tt_total_play) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_play_over) / max(sub_main_log.tt_total_play), 4))',
//            'IFNULL ( CAST ( (
//         tt_play_over / tt_total_play
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //播放数
        'tt_total_play' => ['tt_total_play'],
        //有效播放成本
        'tt_valid_play_cost' => [
            'if(max(sub_main_log.tt_valid_play) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.ori_cost) / max(sub_main_log.tt_valid_play), 4))',
//            'IFNULL ( CAST ( (
//         ori_cost / tt_valid_play
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //有效播放率
        'tt_valid_play_rate' => [
            'if(max(sub_main_log.show) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.tt_valid_play) / max(sub_main_log.show), 4))',
//            'IFNULL ( CAST ( (
//         tt_valid_play / `show`
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //Wi-Fi播放量
        'tt_wifi_play' => ['tt_wifi_play'],
        //50%进度播放数
        'tt_play_50_feed_break' => ['tt_play_50_feed_break'],
        //播放完成数
        'tt_play_over' => ['tt_play_over'],
        /**
         * 今日头条互动数据
         */
        //POI点击数
        'tt_location_click' => ['tt_location_click'],
        //评论数
        'tt_comment' => ['tt_comment'],
        //分享数
        'tt_share' => ['tt_share'],
        //新增关注数
        'tt_follow' => ['tt_follow'],
        //主页访问量
        'tt_home_visited' => ['tt_home_visited'],
        //点赞数
        'tt_like' => ['tt_like'],
        //音乐查看数
        'tt_ies_music_click' => ['tt_ies_music_click'],
        //挑战赛查看数
        'tt_ies_challenge_click' => ['tt_ies_challenge_click'],
        /**
         * 今日头条质量得分
         */
        //落地页响应得分
        'tt_quality_score' => [],
        //创意质量得分
        'tt_ctr_score' => [],
        //落地页素材得分
        'tt_web_score' => [],
        //计划综合质量得分
        'tt_cvr_score' => [],

        /**
         * 账户通用属性
         */
        //公司
//         'company' => ['company'],
//         //代理商
//         'agency_name' => ['agency_name'],
        //总余额
        'balance' => ['balance'],
        //广告主状态
//         'account_status' => ['account_status'],
//         //账户审核拒绝原因
//         'reason' => ['reason'],
//         //媒体账户负责人
//         'account_leader' => ['account_leader'],
//         //邮箱
//         'tt_email' => ['tt_email'],
        //账户预算
        'account_budget' => ['account_budget'],
        //可用现金余额
        'tt_valid_cash' => ['tt_valid_cash'],
        //可用赠款余额
        'tt_valid_grant' => ['tt_valid_grant'],
        //可用返货余额
        'tt_valid_return_goods_abs' => ['tt_valid_return_goods_abs'],
        //可用总余额
        'tt_valid_balance' => ['tt_valid_balance'],
        //现金余额
        'tt_cash' => ['tt_cash'],
        //赠款余额
        'tt_grant' => ['tt_grant'],
        //返货余额
        'tt_return_goods_abs' => ['tt_return_goods_abs'],
        //返货支出
        'tt_return_goods_cost' => ['tt_return_goods_cost'],
        //一级预算
        'ad1_budget' => ['ad1_budget'],
        //一级状态
//         'ad1_status' => ['ad1_status'],
//         //推广目的
//         'ad1_type' => ['ad1_type'],
//         //广告组预算类型
//         'tt_ad1_budget_mode' => ['tt_ad1_budget_mode'],
//         //一级修改时间
//         'ad1_create_time' => ['ad1_create_time'],
//         //一级修改时间
//         'ad1_modify_time' => ['ad1_modify_time'],

        //首日付费次数成本
        'cost_first_day_pay_times' => [
            'if(max(sub_main_log.first_day_pay_times) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.cost) / max(sub_main_log.first_day_pay_times), 4))',
//            'IFNULL ( CAST ( (
//         cost / first_day_pay_times
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //累计付费次数成本
        'cost_total_pay_times' => [
            'if(max(sub_main_log.total_pay_times) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.cost) / max(sub_main_log.total_pay_times), 4))',
//            'IFNULL ( CAST ( (
//         cost / total_pay_times
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //首日付费次数
        'first_day_pay_times' => ['first_day_pay_times'],
        //累计付费次数
        'total_pay_times' => ['total_pay_times'],
        //业务点击数
        'click_count' => ['click_count'],
        //次留成本
        'cost_day_second_login' => [
            'if(max(sub_main_log.count_is_second_login) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.cost) / max(sub_main_log.count_is_second_login), 4))',
//            'IFNULL ( CAST ( (
//         cost / count_is_second_login
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //累计付费人数
        'total_pay_count' => ['total_pay_count'],
        //累计付费金额
        'total_pay_money' => ['total_pay_money'],
        //上报媒体首日付费金额
        'callback_first_day_pay_money' => ['callback_first_day_pay_money'],
        //上报媒体首日付费人数
        'callback_first_day_pay_uid_count' => ['callback_first_day_pay_uid_count'],
        //注册(根老设备)
        'reg_old_muid_count' => ['reg_old_muid_count'],
        //注册(集团老设备)
        'reg_old_clique_muid_count' => ['reg_old_clique_muid_count'],
        //登录三次以上人数
        'day_three_login_uid_count' => ['day_three_login_uid_count'],
        //注册(根老设备)占比
        'reg_old_muid_percentage' => [
            'if(max(sub_main_log.day_reg_muid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.reg_old_muid_count) / max(sub_main_log.day_reg_muid_count), 4))',
//            'IFNULL ( CAST ( (
//         reg_old_muid_count / day_reg_muid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //注册(集团老设备)占比
        'reg_old_clique_muid_percentage' => [
            'if(max(sub_main_log.day_reg_muid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.reg_old_clique_muid_count) / max(sub_main_log.day_reg_muid_count), 4))',
//            'IFNULL ( CAST ( (
//         reg_old_clique_muid_count / day_reg_muid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //登录三次以上人数占比
        'day_three_login_uid_percentage' => [
            'if(max(sub_main_log.reg_uid_count) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.day_three_login_uid_count) / max(sub_main_log.reg_uid_count), 4))',
//            'IFNULL ( CAST ( (
//         day_three_login_uid_count / reg_uid_count
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],
        //首次付费当日充值金额
        'new_pay_day_money' => ['new_pay_day_money'],
        //首次付费当日充值人数
        'new_pay_day_count' => ['new_pay_day_count'],
        //七回
        'rate_day_roi_7' => [
            'if(max(sub_main_log.cost) = 0, toDecimal64(0, 4), toDecimal64(max(sub_main_log.sum_seven_day_pay_money) / max(sub_main_log.cost), 4))',
//            'IFNULL ( CAST ( (
//         sum_seven_day_pay_money / cost
//         ) AS DECIMAL ( 10, 4 ) ), 0 )'
        ],

    ];

}