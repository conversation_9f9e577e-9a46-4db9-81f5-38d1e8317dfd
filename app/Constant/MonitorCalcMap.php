<?php

namespace App\Constant;

/**
 * 数值监控规则
 * Class MonitorCalcMap
 * @package App\Constant
 */
class MonitorCalcMap
{
//    const CALC_FIELDS = [
//        //消耗
//        'cost' => ['cost'],
//        //消耗天数
//        'cost_date_count' => ['cost_date_hour_count'],
//        //预算消耗进度
////        'cost_process' => [],
//        //点击率
//        'click_rate' => ['click', 'show'],
//        'cpc' => ['click', 'ori_cost'],
//        'cpm' => ['show', 'ori_cost'],
//        //转化率
//        'convert_rate' => ['convert', 'click'],
//        //转化数
//        'convert' => ['convert'],
//        //深度转化数
//        'deep_convert' => ['deep_convert'],
//        //深度转化率
//        'deep_convert_rate' => ['deep_convert', 'convert'],
//        //媒体注册数
//        'reg_count' => ['register'],
//        //媒体注册率
//        'reg_rate' => ['register', 'click'],
//        //转化成本
//        'cost_per_convert' => ['ori_cost', 'convert'],
//        //深度转化成本
//        'deep_convert_cost' => ['ori_cost', 'deep_convert'],
//        //出价计费比（转化成本/ocpm出价）
//        'bid_billing_rate' => ['ori_cost', 'convert'],
//        //深度出价计费比
//        'deep_bid_billing_rate' => ['ori_cost', 'deep_convert'],
//        //关键行为数
//        'game_addiction' => ['game_addiction'],
//        //关键行为率
//        'game_addiction_rate' => ['game_addiction', 'active'],
//        //关键行为成本
//        'game_addiction_cost' => ['ori_cost', 'game_addiction'],
//        //业务注册数
//        'reg_uid_count' => ['reg_uid_count'],
//        //业务注册成本
//        'cost_per_reg' => ['cost', 'reg_uid_count'],
//        //首日付费成本
//        'cost_per_first_day_pay' => ['cost', 'first_day_pay_count'],
//        //首日付费人数
//        'first_day_pay_count' => ['first_day_pay_count'],
//        //累计付费人数
//        'total_pay_count' => ['total_pay_count'],
//        //累计付费成本
//        'cost_per_pay' => ['ori_cost', 'total_pay_count'],
//        //次留率
//        'rate_day_stay_2' => ['second_login_count', 'reg_uid_count'],
//        //首日roi
//        'first_day_roi' => ['first_day_pay_money', 'cost'],
//        //累计roi
//        'total_roi' => ['total_pay_money', 'cost'],
//
//    ];

    /**
     * 头条/腾讯投放时段，除去某一天的的值
     */
    const TOUTIAO_SCHEDULE_TIME = [
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000000000000000000000',
        '000000000000000000000000000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111000000000000000000000000000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000000000000000000000111111111111111111111111111111111111111111111111',

    ];

    const KUAISHOU_BAIDU_SCHEDULE_TIME = [
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000',
        '000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000111111111111111111111111111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000111111111111111111111111111111111111111111111111',
        '111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000111111111111111111111111',
    ];
}