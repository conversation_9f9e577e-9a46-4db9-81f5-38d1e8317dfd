<?php

/**
 * 平台能力
 */

namespace App\Constant;


class PlatformAbility
{
    const MP_REPORT = ['TW'];

    const INSIDE_AD_API = [];

    const MIGRATE_SITE_MAP = ['ZW'];

    const SITE_CONFIG_TEMPLATE_ADDRESS = ['ZW', 'GR', 'GRBB'];

    const CHECK_SITE_PACKAGE_SPECIAL = ['GR', 'GRBB'];

    const CHECK_SITE_PACKAGE_SKIP_MEDIA_TYPE = ['ZW'];

    const CHECK_GAME_PACK = ['TW'];

    const CHECK_AGENT_REBATE_INPUT = ['TW', 'GR'];

    const DEFAULT_COST_GAME = [
        'TW' => '3041',
        'ZW' => '13',
        'GR' => '3902',
        'GRBB' => '4763'
    ];

    static public function privateURL($platform, $game_id)
    {
        $privateURL = '';
        switch ($platform) {
            case 'TW':
                $privateURL = 'https://www.tanwan.com/app/xieyi.html';
                break;
            case 'ZW':
                $privateURL = 'https://m.hnzwwl.cn/kf/xieyi.html?buoy=1';
                break;
            case 'GR':
                $privateURL = 'https://web.89yoo.com/news/details?id=1593';
                break;
            case 'GRBB':
                $privateURL = 'https://web.gaore.com/web/m/html/userPrivacy.html';
                break;
        }
        return $privateURL;
    }

    static public function permissionURL($platform, $game_id)
    {
        $permissionURL = '';
        switch ($platform) {
            case 'TW':
                $permissionURL = 'https://m.tanwan.com/dev/xieyi/424741.html';
                break;
        }
        return $permissionURL;
    }
}

