<?php
/**
 * Clickhouse DictGet用到的数据配置
 */

namespace App\Constant;

class ClickhouseDictConfig
{
    // 这里仅供DictGet使用 !!!!!
    // RANGE_HASHED 类型字典表需要配合数据表的日期字段使用
    // 表名日期字段的名称与类型
    public const TABLE_DATE_COLUMN = [

        // 回流表
        'dwd_day_root_game_back_uid_login_log_rmt_all'      => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_root_game_back_device_action_log_rmt_all'      => ['name' => 'action_time', 'type' => 'datetime'],
        'dwd_root_game_back_pay_order_log_rmt_all'          => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_root_game_back_uid_reg_log_rmt_all'            => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dws_day_root_game_back_device_action_log_rmt_all'  => ['name' => 'action_date', 'type' => 'date'],
        'dws_day_root_game_back_pay_log_rmt_all'            => ['name' => 'game_reg_date', 'type' => 'date'],
        'dws_hour_root_game_back_device_action_log_rmt_all' => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_back_pay_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_back_reg_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_day_root_game_back_login_log_rmt_all'          => ['name' => 'game_reg_date', 'type' => 'date'],


        'dws_day_click_log_rmt_all'                   => ['name' => 'click_date', 'type' => 'date'],
        'dws_day_show_log_rmt_all'                    => ['name' => 'show_date', 'type' => 'date'],
        'dwd_root_game_device_action_log_rmt_all'     => ['name' => 'action_time', 'type' => 'datetime'],
        'dws_day_root_game_device_action_log_rmt_all' => ['name' => 'action_date', 'type' => 'date'],
        'dwd_root_game_uid_reg_log_rmt_all'           => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_day_cost_log_rmt_all'                    => ['name' => 'tdate', 'type' => 'date'],
        'dwd_hour_cost_log_rmt_all'                   => ['name' => 'tdate', 'type' => 'date'],
        'dwd_day_root_game_uid_login_log_rmt_all'     => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'ods_root_game_uid_pay_order_log_rmt_all'     => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_root_game_pay_order_log_rmt_all'         => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dws_day_root_game_pay_log_rmt_all'           => ['name' => 'game_reg_date', 'type' => 'date'],

        'ods_device_action_log_rmt_all'     => ['name' => 'action_time', 'type' => 'datetime'],
        'dws_day_device_action_log_rmt_all' => ['name' => 'action_date', 'type' => 'date'],
        'dwd_game_uid_reg_log_rmt_all'      => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dwd_day_uid_login_log_rmt_all'     => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'ods_game_pay_order_log_rmt_all'    => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dwd_game_pay_order_log_rmt_all'    => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dws_day_pay_log_rmt_all'           => ['name' => 'game_reg_date', 'type' => 'date'],

        'dws_day_root_game_login_log_rmt_all' => ['name' => 'game_reg_date', 'type' => 'date'],
        'dws_day_login_log_rmt_all'           => ['name' => 'game_reg_date', 'type' => 'date'],

        'dws_hour_root_game_pay_log_rmt_all' => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_pay_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],

        'dwd_role_create_log_rmt_all'    => ['name' => 'create_time', 'type' => 'datetime'],
        'dwd_day_role_login_log_rmt_all' => ['name' => 'login_date', 'type' => 'date'],
        'dws_day_role_pay_log_rmt_all'   => ['name' => 'pay_date', 'type' => 'date'],

        'dws_hour_click_log_rmt_all'                                     => ['name' => 'click_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_device_action_log_rmt_all'                   => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_reg_log_rmt_all'                             => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_game_reg_log_rmt_all'                                  => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_device_action_log_rmt_all'                             => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_day_role_login_log_rmt_all'                                 => ['name' => 'create_date', 'type' => 'date'],
        'dwd_root_game_uid_pay_money_log_rmt_all'                        => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'tanwan_datamedia.dwd_media_ad_common_log_rmt_all'               => ['name' => 'ad2_create_time', 'type' => 'datetime'],
        'tanwan_datamedia.ads_day_root_game_overview_log_common_rmt_all' => ['name' => 'log_date', 'type' => 'date'],
        'tanwan_datamedia.ads_day_game_overview_log_common_rmt_all'      => ['name' => 'log_date', 'type' => 'date'],
        'tanwan_datamedia.dwd_media_ad3_common_day_data_log_rmt_all'     => ['name' => 'cost_date', 'type' => 'date'],
        'ods_ad_inspire_cost_log_rmt_all'                                => ['name' => "stat_date", 'type' => "date"],

        // 小游戏消耗
        'dwd_media_ad2_common_day_data_log_rmt_all'                      => ['name' => 'cost_date', 'type' => 'date'],
        'dwd_media_ad3_common_hour_data_log_rmt_all'                     => ['name' => 'cost_date_hour', 'type' => 'datetime'],
    ];

    // 字典配置 [字典表名, 字典唯一值, 唯一值组合方式, 字典唯一值组合字段, 字典类型]
    public const DICT_TABLE = [
        'agent'                       => [
            'table'        => 'tanwan_datahub.dim_agent_id_dict',
            'key_name'     => 'agent_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'agent_id'],
            'type'         => 'RANGE_HASHED',
            //            'date_type' => "toDateTime", // 默认使用toDateTime
        ],
        'site'                        => [
            'table'        => 'tanwan_datahub.dim_site_id_dict',
            'key_name'     => 'site_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'site_id'],
            'type'         => 'HASHED',
        ],
        'site_aweme'                  => [
            'table'        => 'tanwan_datahub.dim_site_id_aweme_dict',
            'key_name'     => 'site_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'site_id'],
            'type'         => 'HASHED',
        ],
        'game'                        => [
            'table'        => 'tanwan_datahub.dim_game_id_dict',
            'key_name'     => 'game_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'game_id'],
            'type'         => 'HASHED',
        ],
        'leader_group'                => [
            'table'        => 'tanwan_datahub.dim_agent_leader_group_dict',
            'key_name'     => 'agent_leader_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['agent_leader'],
            'type'         => 'HASHED',
        ],
        'device'                      => [
            'table'        => 'tanwan_datahub.dwd_device_information_log_dict',
            'key_name'     => 'device_model_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['device_model'],
            'type'         => 'HASHED',
        ],
        'settle_company'              => [
            'table'        => 'tanwan_datahub.dim_game_settle_company_dict',
            'key_name'     => 'company_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 't.game_id'],
            'type'         => 'RANGE_HASHED',
            //            'date_type' => "toDateTime", // 默认使用toDateTime
        ],
        'web_adcre'                   => [
            'table'        => 'tanwan_datahub.dim_web_adcre_id_dict',
            'key_name'     => 'web_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'adcre_id'],
            'type'         => 'HASHED',
        ],
        'agent_day_cost'              => [
            'table'        => 'tanwan_datahub.dws_agent_day_cost_log_rmt_dict',
            'key_name'     => 'agent_cost_date_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'agent_id', 'cost_date'],
            'type'         => 'HASHED',
        ],
        'csite'                       => [
            'table'        => 'tanwan_datahub.dim_csite_rmt_dict',
            'key_name'     => 'csite_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'site_id', 'csite'],
            'type'         => 'HASHED',
        ],
        'main_game'                   => [
            'table'        => 'tanwan_datahub.dim_main_game_id_dict',
            'key_name'     => 'main_game_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'main_game_id'],
            'type'         => 'HASHED',
        ],
        'mix_main_game'               => [
            'table'        => 'tanwan_datahub.dim_mix_main_game_id_dict',
            'key_name'     => 'main_game_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'main_game_id'],
            'type'         => 'HASHED',
        ],
        'server'                      => [
            'table'        => 'tanwan_datahub.dim_server_open_time_dict',
            'key_name'     => 'server_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'main_game_id', 'ori_server_id', 'open_type', 'combine_times'],
            'type'         => 'RANGE_HASHED',
            'date_type'    => "toDate",
        ],
        'live'                        => [
            'table'        => 'tanwan_datamedia.ods_live_user_name_log_dict',
            'key_name'     => 'aweme_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['media_type', 'live_user'],
            'type'         => 'RANGE_HASHED',
            //            'date_type' => "toDateTime", // 默认使用toDateTime
        ],
        'official'                    => [
            'table'        => 'tanwan_datahub.dwd_official_data_log_dict',
            'key_name'     => 'official_data_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'game_id', 'apportion_agent_id', 'log_date', 'statistics_type'],
            'type'         => 'RANGE_HASHED',
            'date_type'    => "toDate", // 默认使用toDateTime
        ],
        'interface_person_group'      => [
            'table'        => 'tanwan_datahub.dim_interface_person_group_dict',
            'key_name'     => 'interface_person_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['interface_person'],
            'type'         => 'HASHED',
        ],
        'interface_person_change_log' => [
            'table'        => 'tanwan_datamedia.ods_live_user_interface_person_change_log_dict',
            'key_name'     => 'interface_person_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['interface_person'],
            'type'         => 'RANGE_HASHED',
        ],
        'material'                    => [
            'table'        => 'tanwan_datamedia.dwd_material_cklast_dict',
            'key_name'     => 'signature_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['signature'],
            'type'         => 'HASHED',
        ],
        'rank_material'               => [
            'table'        => 'tanwan_datamedia.ods_rank_material_dict',
            'key_name'     => 'rank_material_key',
            'union_type'   => 'cityHash64',
            'union_column' => [],
            'type'         => 'HASHED',
        ],
        'game_dimension'              => [
            'table'        => 'tanwan_datahub.dim_game_dimension_dict',
            'key_name'     => 'root_game_key',
            'union_type'   => 'cityHash64',
            'union_column' => ['platform', 'root_game_id'],
            'type'         => 'RANGE_HASHED',
        ],
    ];

    // 设置字段使用dict时所需函数，默认为 dictGet
    public const DICT_METHOD = [
        'agent_id'                    => 'dictGetOrNull',
        'game_id'                     => 'dictGet',
        'root_game_id'                => 'dictGet',
        'site_id'                     => 'dictGetOrNull',
        'agent_account_id'            => 'dictGetOrNull',
        'account_id'                  => 'dictGetOrNull', // 此处是 渠道-账户ID
        'agent_leader_group_name'     => 'dictGetOrNull',
        'device_name'                 => 'dictGetOrNull',
        'device_price_section'        => 'dictGetOrNull',
        'settle_company_name'         => 'dictGetOrNull',
        'adcre_name'                  => 'dictGetOrNull',
        'interface_person'            => 'dictGet',
        'interface_person_group_name' => 'dictGet',
        'media_type_id'               => 'dictGet',
        'is_old_game'                 => 'dictGetOrNull', // 新老游戏, null 前端显示空
    ];


    public const GAME_DIMENSION_MAP = [

        // 回流表
        'dwd_day_root_game_back_uid_login_log_rmt_all'      => ['name' => 'login_date', 'type' => 'date'],
        'dwd_root_game_back_device_action_log_rmt_all'      => ['name' => 'action_time', 'type' => 'datetime'],
        'dwd_root_game_back_pay_order_log_rmt_all'          => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_root_game_back_uid_reg_log_rmt_all'            => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dws_day_root_game_back_device_action_log_rmt_all'  => ['name' => 'action_date', 'type' => 'date'],
        'dws_day_root_game_back_pay_log_rmt_all'            => ['name' => 'pay_date', 'type' => 'date'],
        'dws_hour_root_game_back_device_action_log_rmt_all' => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_back_pay_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_back_reg_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_day_root_game_back_login_log_rmt_all'          => ['name' => 'game_reg_date', 'type' => 'date'],


        'dws_day_click_log_rmt_all'                   => ['name' => 'click_date', 'type' => 'date'],
        'dws_day_show_log_rmt_all'                    => ['name' => 'show_date', 'type' => 'date'],
        'dwd_root_game_device_action_log_rmt_all'     => ['name' => 'action_time', 'type' => 'datetime'],
        'dws_day_root_game_device_action_log_rmt_all' => ['name' => 'action_date', 'type' => 'date'],
        'dwd_root_game_uid_reg_log_rmt_all'           => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_day_cost_log_rmt_all'                    => ['name' => 'tdate', 'type' => 'date'],
        'dwd_hour_cost_log_rmt_all'                   => ['name' => 'tdate', 'type' => 'date'],
        'dwd_day_root_game_uid_login_log_rmt_all'     => ['name' => 'login_date', 'type' => 'date'],
        'ods_root_game_uid_pay_order_log_rmt_all'     => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dwd_root_game_pay_order_log_rmt_all'         => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'dws_day_root_game_pay_log_rmt_all'           => ['name' => 'pay_date', 'type' => 'date'],

        'ods_device_action_log_rmt_all'     => ['name' => 'action_time', 'type' => 'datetime'],
        'dws_day_device_action_log_rmt_all' => ['name' => 'action_date', 'type' => 'date'],
        'dwd_game_uid_reg_log_rmt_all'      => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dwd_day_uid_login_log_rmt_all'     => ['name' => 'login_date', 'type' => 'date'],
        'ods_game_pay_order_log_rmt_all'    => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dwd_game_pay_order_log_rmt_all'    => ['name' => 'game_reg_time', 'type' => 'datetime'],
        'dws_day_pay_log_rmt_all'           => ['name' => 'pay_date', 'type' => 'date'],

        'dws_day_root_game_login_log_rmt_all' => ['name' => 'game_reg_date', 'type' => 'date'],
        'dws_day_login_log_rmt_all'           => ['name' => 'game_reg_date', 'type' => 'date'],

        'dws_hour_root_game_pay_log_rmt_all' => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_pay_log_rmt_all'           => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],

        'dwd_role_create_log_rmt_all'    => ['name' => 'create_time', 'type' => 'datetime'],
        'dwd_day_role_login_log_rmt_all' => ['name' => 'login_date', 'type' => 'date'],
        'dws_day_role_pay_log_rmt_all'   => ['name' => 'pay_date', 'type' => 'date'],

        'dws_hour_click_log_rmt_all'                                     => ['name' => 'click_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_device_action_log_rmt_all'                   => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_hour_root_game_reg_log_rmt_all'                             => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_game_reg_log_rmt_all'                                  => ['name' => 'game_reg_date_hour', 'type' => 'datetime'],
        'dws_hour_device_action_log_rmt_all'                             => ['name' => 'action_date_hour', 'type' => 'datetime'],
        'dws_day_role_login_log_rmt_all'                                 => ['name' => 'create_date', 'type' => 'date'],
        'dwd_root_game_uid_pay_money_log_rmt_all'                        => ['name' => 'root_game_reg_time', 'type' => 'datetime'],
        'tanwan_datamedia.dwd_media_ad_common_log_rmt_all'               => ['name' => 'ad2_create_time', 'type' => 'datetime'],
        'tanwan_datamedia.ads_day_root_game_overview_log_common_rmt_all' => ['name' => 'log_date', 'type' => 'date'],
        'tanwan_datamedia.ads_day_game_overview_log_common_rmt_all'      => ['name' => 'log_date', 'type' => 'date'],
        'tanwan_datamedia.dwd_media_ad3_common_day_data_log_rmt_all'     => ['name' => 'cost_date', 'type' => 'date'],
        'ods_ad_inspire_cost_log_rmt_all'                                => ['name' => "stat_date", 'type' => "date"],

        // 小游戏消耗
        'dwd_media_ad2_common_day_data_log_rmt_all'                      => ['name' => 'cost_date', 'type' => 'date'],
        //
        'dwd_media_ad3_common_hour_data_log_rmt_all'                     => ['name' => 'cost_date_hour', 'type' => 'datetime'],
    ];
}
