<?php

namespace App\Constant;

class ADIntelligentMonitorToutiaoV2SqlMap
{
    const TOUTIAOV2_FORMULA = [
        'account_id' => 'ods_toutiao_account_log.account_id',
        'account_balance' => 'ods_toutiao_account_log.balance',
        'account_ori_cost' => 'ifnull(ods_toutiao_creative_hour_data_log.ori_cost,0)',
        'account_register' => 'ifnull(sum(v3_ads_day_root_game_back_overview_log.day_reg_uid_count),0)',
        'account_fist_day_pay_count' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'account_first_day_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'project_ori_cost' => 'ifnull(ods_toutiao_creative_hour_data_log.ori_cost,0)',
        'project_register' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'project_id' => 'ifnull(ods_toutiao_campaign_log.campaign_id,0)',
        'project_first_day_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'project_total_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'project_fist_day_pay_count' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'project_create_time' => 'ifnull(ods_toutiao_campaign_log.campaign_create_time,\'\')',
        'project_show' => 'ifnull(ods_toutiao_creative_hour_data_log.show,0)',
        'promotion_ori_cost' => 'ifnull(ods_toutiao_creative_hour_data_log.ori_cost,0)',
        'promotion_register' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'promotion_id' => 'ifnull(ods_toutiao_ad_log.ad_id,0)',
        'promotion_first_day_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'promotion_total_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'promotion_fist_day_pay_count' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
        'promotion_suggestion' => 'ifnull(ods_toutiao_ad_log.suggestion,\'\')',
        'promotion_create_time' => 'ifnull(ods_toutiao_ad_log.ad_create_time,\'\')',
        'promotion_show' => 'ifnull(ods_toutiao_creative_hour_data_log.show,0)',
        'creative_material_id' => 'ifnull(ods_toutiao_creative_log.creative_id,0)',
        'creative_material_ori_cost' => 'ifnull(ods_toutiao_creative_hour_data_log.ori_cost,0)',
        'creative_material_register' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_reg_uid_count,0))',
        'creative_material_first_day_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_money,0))',
        'creative_material_total_pay_money' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_total_pay_money,0))',
        'creative_material_fist_day_pay_count' => 'sum(ifnull(v3_ads_day_root_game_back_overview_log.day_first_day_pay_count,0))',
    ];


    const TOUTIAOV2_FORMULA_ALIAS = [
        'account_id' => self::TOUTIAOV2_FORMULA['account_id'] . ' as account_id',
        'account_balance' => self::TOUTIAOV2_FORMULA['account_balance'] . ' as account_balance',
        'account_ori_cost' => self::TOUTIAOV2_FORMULA['account_ori_cost'] . ' as account_ori_cost',
        'account_register' => self::TOUTIAOV2_FORMULA['account_register'] . ' as account_register',
        'account_fist_day_pay_count' => self::TOUTIAOV2_FORMULA['account_fist_day_pay_count'] . ' as account_fist_day_pay_count',
        'account_first_day_pay_money' => self::TOUTIAOV2_FORMULA['account_first_day_pay_money'] . ' as account_first_day_pay_money',
        'project_ori_cost' => self::TOUTIAOV2_FORMULA['project_ori_cost'] . ' as project_ori_cost',
        'project_register' => self::TOUTIAOV2_FORMULA['project_register'] . ' as project_register',
        'project_id' => self::TOUTIAOV2_FORMULA['project_id'] . ' as campaign_id',
        'project_first_day_pay_money' => self::TOUTIAOV2_FORMULA['project_first_day_pay_money'] . ' as project_first_day_pay_money',
        'project_total_pay_money' => self::TOUTIAOV2_FORMULA['project_total_pay_money'] . ' as project_total_pay_money',
        'project_fist_day_pay_count' => self::TOUTIAOV2_FORMULA['project_fist_day_pay_count'] . ' as project_fist_day_pay_count',
        'project_create_time' => self::TOUTIAOV2_FORMULA['project_create_time'] . ' as project_create_time',
        'project_create_time_relative' => self::TOUTIAOV2_FORMULA['project_create_time'] . ' as project_create_time_relative',
        'project_show' => self::TOUTIAOV2_FORMULA['project_show'] . ' as project_show',
        'promotion_ori_cost' => self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' as promotion_ori_cost',
        'promotion_register' => self::TOUTIAOV2_FORMULA['promotion_register'] . ' as promotion_register',
        'promotion_id' => self::TOUTIAOV2_FORMULA['promotion_id'] . ' as ad_id',
        'promotion_first_day_pay_money' => self::TOUTIAOV2_FORMULA['promotion_first_day_pay_money'] . ' as promotion_first_day_pay_money',
        'promotion_total_pay_money' => self::TOUTIAOV2_FORMULA['promotion_total_pay_money'] . ' as promotion_total_pay_money',
        'promotion_fist_day_pay_count' => self::TOUTIAOV2_FORMULA['promotion_fist_day_pay_count'] . ' as promotion_fist_day_pay_count',
        'promotion_suggestion' => self::TOUTIAOV2_FORMULA['promotion_suggestion'] . ' as promotion_suggestion',
        'promotion_create_time' => self::TOUTIAOV2_FORMULA['promotion_create_time'] . ' as promotion_create_time',
        'promotion_create_time_relative' => self::TOUTIAOV2_FORMULA['promotion_create_time'] . ' as promotion_create_time_relative',
        'promotion_show' => self::TOUTIAOV2_FORMULA['promotion_show'] . ' as promotion_show',
        'creative_material_id' => self::TOUTIAOV2_FORMULA['creative_material_id'] . ' as creative_id',
        'creative_material_register' => self::TOUTIAOV2_FORMULA['creative_material_register'] . ' as creative_material_register',
        'creative_material_first_day_pay_money' => self::TOUTIAOV2_FORMULA['creative_material_first_day_pay_money'] . ' as creative_material_first_day_pay_money',
        'creative_material_total_pay_money' => self::TOUTIAOV2_FORMULA['creative_material_total_pay_money'] . ' as creative_material_total_pay_money',
        'creative_material_fist_day_pay_count' => self::TOUTIAOV2_FORMULA['creative_material_fist_day_pay_count'] . ' as creative_material_fist_day_pay_count',
    ];

    const TOUTIAOV2_TARGET = [
        // account
        'account_balance' => [
            'ods_toutiao_account_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_id'],
                self::TOUTIAOV2_FORMULA_ALIAS['account_balance']
            ]
        ],
        'account_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['account_ori_cost'] . ') AS account_ori_cost'
            ],
            'ods_toutiao_account_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_id'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => []
        ],
        'account_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((SUM(' . self::TOUTIAOV2_FORMULA['account_ori_cost'] . ') /' . self::TOUTIAOV2_FORMULA['account_register'] . ') AS DECIMAL(12, 4)), 0) as account_cost_per_reg',
                'SUM(' . self::TOUTIAOV2_FORMULA['account_ori_cost'] . ') AS account_ori_cost'
            ],
            'ods_toutiao_account_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_register'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [],
        ],
        'account_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((SUM(' . self::TOUTIAOV2_FORMULA['account_ori_cost'] . ') /' . self::TOUTIAOV2_FORMULA['account_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS account_cost_per_first_day_pay',
            ],
            'ods_toutiao_account_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_fist_day_pay_count'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [],
        ],
        'account_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['account_first_day_pay_money'] . '/ SUM(' . self::TOUTIAOV2_FORMULA['account_ori_cost'] . ')) AS DECIMAL(12, 4)), 0) AS account_first_day_roi',
                'SUM(' . self::TOUTIAOV2_FORMULA['account_ori_cost'] . ') AS account_ori_cost'
            ],
            'ods_toutiao_account_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['account_first_day_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => []
        ],
        // project
        'project_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') AS project_ori_cost'
            ],
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => []
        ],
        'project_register' => [
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_register'],
            ]
        ],
        'project_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['project_first_day_pay_money'] . '/ SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ')) AS DECIMAL(12, 4)), 0) AS project_first_day_roi'
            ],
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_first_day_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') AS project_ori_cost'
            ]
        ],
        'project_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['project_total_pay_money'] . '/ SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ')) AS DECIMAL(12, 4)), 0) AS project_total_roi'
            ],
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_total_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') AS project_ori_cost'
            ]
        ],
        'project_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') /' . self::TOUTIAOV2_FORMULA['project_register'] . ') AS DECIMAL(12, 4)), 0) as project_cost_per_reg'
            ],
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_register'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') AS project_ori_cost'
            ],
        ],
        'project_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') /' . self::TOUTIAOV2_FORMULA['project_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS project_cost_per_first_day_pay'
            ],
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_fist_day_pay_count'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . ') AS project_ori_cost'
            ],
        ],
        'project_total_pay_money' => [
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_total_pay_money'],
            ]
        ],
        'project_create_time' => [
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
                self::TOUTIAOV2_FORMULA_ALIAS['project_create_time'],
            ],
        ],
        'project_create_time_relative' => [
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
                self::TOUTIAOV2_FORMULA_ALIAS['project_create_time_relative'],
            ],
        ],
        'project_cpm' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['project_ori_cost'] . '/(' . self::TOUTIAOV2_FORMULA['project_show'] . ' / 1000)) AS DECIMAL(12, 4)), 0) AS project_cpm'
            ],
            'ods_toutiao_campaign_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['project_id'],
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['project_ori_cost'] . ' AS project_ori_cost',
                self::TOUTIAOV2_FORMULA['project_show'] . ' AS project_show',
            ],
        ],
        // promotion
        'promotion_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ') AS promotion_ori_cost'
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => []
        ],
        'promotion_register' => [
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_register'],
            ]
        ],
        'promotion_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['promotion_first_day_pay_money'] . '/ ' . self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS promotion_first_day_roi'
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_first_day_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' AS promotion_ori_cost',
            ]
        ],
        'promotion_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['promotion_total_pay_money'] . '/' . self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS promotion_total_roi'
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_total_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' AS promotion_ori_cost',
            ]
        ],
        'promotion_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' /' . self::TOUTIAOV2_FORMULA['promotion_register'] . ') AS DECIMAL(12, 4)), 0) as promotion_cost_per_reg'
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_register'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' AS promotion_ori_cost',
            ],
        ],
        'promotion_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . '/' . self::TOUTIAOV2_FORMULA['promotion_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS promotion_cost_per_first_day_pay'
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_fist_day_pay_count'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' AS promotion_ori_cost',
            ],
        ],
        'promotion_total_pay_money' => [
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_total_pay_money'],
            ]
        ],
        'promotion_suggestion' => [
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_suggestion']
            ]
        ],
        'promotion_create_time' => [
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_create_time'],
            ],
        ],
        'promotion_create_time_relative' => [
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_create_time_relative'],
            ],
        ],
        'promotion_cpm' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . '/(' . self::TOUTIAOV2_FORMULA['promotion_show'] . ' / 1000)) AS DECIMAL(12, 4)), 0) AS promotion_cpm'
            ],
            'ods_toutiao_ad_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['promotion_id'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['promotion_ori_cost'] . ' AS promotion_ori_cost',
                self::TOUTIAOV2_FORMULA['promotion_show'] . ' AS promotion_show',
            ],
        ],
        // creative_material
        'creative_material_ori_cost' => [
            '__FORMULA__' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ') AS creative_material_ori_cost'
            ],
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => []
        ],
        'creative_material_register' => [
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_register'],
            ]
        ],
        'creative_material_first_day_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['creative_material_first_day_pay_money'] . '/ ' . self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_material_first_day_roi'
            ],
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_first_day_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ' AS creative_material_ori_cost',
            ]
        ],
        'creative_material_total_roi' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['creative_material_total_pay_money'] . '/' . self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ') AS DECIMAL(12, 4)), 0) AS creative_material_total_roi'
            ],
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_total_pay_money'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ' AS creative_material_ori_cost',
            ]
        ],
        'creative_material_cost_per_reg' => [
            '__FORMULA__' => [
                'IFNULL(CAST((' . self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ' /' . self::TOUTIAOV2_FORMULA['creative_material_register'] . ') AS DECIMAL(12, 4)), 0) as creative_material_cost_per_reg'
            ],
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_register'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ' AS creative_material_ori_cost',
            ],
        ],
        'creative_material_cost_per_first_day_pay' => [
            '__FORMULA__' => [
                'IFNULL(CAST((SUM(' . self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ') /' . self::TOUTIAOV2_FORMULA['creative_material_fist_day_pay_count'] . ') AS DECIMAL(12, 4)), 0) AS creative_material_cost_per_first_day_pay'
            ],
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_fist_day_pay_count'],
            ],
            'group-by-ods_toutiao_creative_hour_data_log' => [
                'SUM(' . self::TOUTIAOV2_FORMULA['creative_material_ori_cost'] . ') AS creative_material_ori_cost'
            ],
        ],
        'creative_material_total_pay_money' => [
            'ods_toutiao_creative_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_id'],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                self::TOUTIAOV2_FORMULA_ALIAS['creative_material_total_pay_money'],
            ]
        ],
    ];

    const TOUTIAOV2_JOIN = [
        // account
        'account_ori_cost' => [
            'ods_toutiao_account_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['account_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'ods_toutiao_creative_hour_data_log.account_id'],
                ],
            ]
        ],
        'account_cost_per_reg' => [
            'ods_toutiao_account_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['account_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'v3_ads_day_root_game_back_overview_log.account_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['account_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'ods_toutiao_creative_hour_data_log.account_id'],
                ],
                'where' => []
            ]
        ],
        'account_cost_per_first_day_pay' => [
            'ods_toutiao_account_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['account_id', 'SUM(day_first_day_pay_count) as day_first_day_pay_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'v3_ads_day_root_game_back_overview_log.account_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['account_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'ods_toutiao_creative_hour_data_log.account_id'],
                ],
                'where' => []
            ]
        ],
        'account_first_day_roi' => [
            'ods_toutiao_account_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['account_id', 'SUM(day_first_day_pay_money) as day_first_day_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'v3_ads_day_root_game_back_overview_log.account_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['account_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['account_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_account_log.account_id', 'ods_toutiao_creative_hour_data_log.account_id'],
                ],
                'where' => []
            ]
        ],
        // project
        'project_ori_cost' => [
            'ods_toutiao_campaign_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['campaign_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['campaign_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_creative_hour_data_log.campaign_id'],
                ],
                'where' => []
            ]
        ],
        'project_register' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ]
        ],
        'project_first_day_roi' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_first_day_pay_money) as day_first_day_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'project_total_roi' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_total_pay_money) as day_total_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'project_cost_per_reg' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'project_cost_per_first_day_pay' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_first_day_pay_count) as day_first_day_pay_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'project_total_pay_money' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_total_pay_money) as day_total_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ]
        ],
        'project_cpm' => [
            'ods_toutiao_campaign_log' . '-' . 'ods_toutiao_ad_log' => [
                'on' => [
                    ['ods_toutiao_campaign_log.campaign_id', 'ods_toutiao_ad_log.campaign_id'],
                ],
            ],
            'ods_toutiao_ad_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost', 'SUM(`show`) as `show`'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        // promotion
        'promotion_ori_cost' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'promotion_register' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ]
        ],
        'promotion_first_day_roi' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_first_day_pay_money) as day_first_day_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'promotion_total_roi' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_total_pay_money) as day_total_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'promotion_cost_per_reg' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'promotion_cost_per_first_day_pay' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_first_day_pay_count) as day_first_day_pay_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        'promotion_total_pay_money' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad2_id', 'SUM(day_total_pay_money) as day_total_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad2_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'v3_ads_day_root_game_back_overview_log.ad2_id'],
                ],
            ]
        ],
        'promotion_cpm' => [
            'ods_toutiao_ad_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['ad_id', 'SUM(ori_cost) as ori_cost', 'SUM(`show`) as `show`'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['ad_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_ad_log.ad_id', 'ods_toutiao_creative_hour_data_log.ad_id'],
                ],
                'where' => []
            ]
        ],
        // creative_material
        'creative_material_ori_cost' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'ods_toutiao_creative_hour_data_log.creative_id'],
                ],
                'where' => []
            ]
        ],
        'creative_material_register' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad3_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad3_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
            ]
        ],
        'creative_material_first_day_roi' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad3_id', 'SUM(day_first_day_pay_money) as day_first_day_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad3_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'ods_toutiao_creative_hour_data_log.creative_id'],
                ],
                'where' => []
            ]
        ],
        'creative_material_total_roi' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad3_id', 'SUM(day_total_pay_money) as day_total_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad3_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'ods_toutiao_creative_hour_data_log.creative_id'],
                ],
                'where' => []
            ]
        ],
        'creative_material_cost_per_reg' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad3_id', 'SUM(day_reg_uid_count) as day_reg_uid_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad3_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'ods_toutiao_creative_hour_data_log.creative_id'],
                ],
                'where' => []
            ]
        ],
        'creative_material_cost_per_first_day_pay' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad3_id', 'SUM(day_first_day_pay_count) as day_first_day_pay_count'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad3_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
            ],
            'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' . '-' . 'group-by-ods_toutiao_creative_hour_data_log' => [
                'group-by' => [
                    'ods_toutiao_creative_hour_data_log' => [
                        'select' => ['creative_id', 'SUM(ori_cost) as ori_cost'],
                        'where' => [
                            ['ods_toutiao_creative_hour_data_log.cost_date_hour', 'between', '__time_range__']
                        ],
                        'group' => ['creative_id'],
                    ]
                ],
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'ods_toutiao_creative_hour_data_log.creative_id'],
                ],
                'where' => []
            ]
        ],
        'creative_material_total_pay_money' => [
            'ods_toutiao_creative_log' . '-' . 'group-by-tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                'group-by' => [
                    'tanwan_datahub.v3_ads_day_root_game_back_overview_log' => [
                        'select' => ['ad3_id', 'SUM(day_total_pay_money) as day_total_pay_money'],
                        'where' => [
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.log_date', 'between', '__time_range__'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id', '>', '0'],
                            ['tanwan_datahub.v3_ads_day_root_game_back_overview_log.media_type', '=', MediaType::TOUTIAO]
                        ],
                        'group' => ['ad3_id'],
                    ]
                ],
                'group-by-as' => 'v3_ads_day_root_game_back_overview_log',
                'on' => [
                    ['ods_toutiao_creative_log.creative_id', 'v3_ads_day_root_game_back_overview_log.ad3_id'],
                ],
            ]
        ],
    ];

    const TOUTIAOV2_GROUP = [
        'account_balance' => 'ods_toutiao_account_log.account_id',
        'account_ori_cost' => 'ods_toutiao_account_log.account_id',
        'account_cost_per_reg' => 'ods_toutiao_account_log.account_id',
        'account_cost_per_first_day_pay' => 'ods_toutiao_account_log.account_id',
        'account_first_day_roi' => 'ods_toutiao_account_log.account_id',
        'project_ori_cost' => 'ods_toutiao_campaign_log.campaign_id',
        'project_register' => 'ods_toutiao_ad_log.campaign_id',
        'project_first_day_roi' => 'ods_toutiao_ad_log.campaign_id',
        'project_total_roi' => 'ods_toutiao_ad_log.campaign_id',
        'project_cost_per_reg' => 'ods_toutiao_ad_log.campaign_id',
        'project_cost_per_first_day_pay' => 'ods_toutiao_ad_log.campaign_id',
        'project_total_pay_money' => 'ods_toutiao_ad_log.campaign_id',
        'project_create_time' => 'ods_toutiao_campaign_log.campaign_id',
        'project_create_time_relative' => 'ods_toutiao_campaign_log.campaign_id',
        'project_cpm' => 'ods_toutiao_campaign_log.campaign_id',
        'promotion_ori_cost' => 'ods_toutiao_ad_log.ad_id',
        'promotion_register' => 'ods_toutiao_ad_log.ad_id',
        'promotion_first_day_roi' => 'ods_toutiao_ad_log.ad_id',
        'promotion_total_roi' => 'ods_toutiao_ad_log.ad_id',
        'promotion_cost_per_reg' => 'ods_toutiao_ad_log.ad_id',
        'promotion_cost_per_first_day_pay' => 'ods_toutiao_ad_log.ad_id',
        'promotion_total_pay_money' => 'ods_toutiao_ad_log.ad_id',
        'promotion_suggestion' => 'ods_toutiao_ad_log.ad_id',
        'promotion_create_time' => 'ods_toutiao_ad_log.ad_id',
        'promotion_create_time_relative' => 'ods_toutiao_ad_log.ad_id',
        'promotion_cpm' => 'ods_toutiao_ad_log.ad_id',
        'creative_material_ori_cost' => 'ods_toutiao_creative_log.creative_id',
        'creative_material_register' => 'ods_toutiao_creative_log.creative_id',
        'creative_material_first_day_roi' => 'ods_toutiao_creative_log.creative_id',
        'creative_material_total_roi' => 'ods_toutiao_creative_log.creative_id',
        'creative_material_cost_per_reg' => 'ods_toutiao_creative_log.creative_id',
        'creative_material_cost_per_first_day_pay' => 'ods_toutiao_creative_log.creative_id',
        'creative_material_total_pay_money' => 'ods_toutiao_creative_log.creative_id',
    ];

    const TOUTIAOV2_WHERE = [
        'account_balance' => [
            'account' => 'ods_toutiao_account_log.account_id',
        ],
        'account_ori_cost' => [
            'account' => 'ods_toutiao_account_log.account_id',
        ],
        'account_cost_per_reg' => [
            'account' => 'ods_toutiao_account_log.account_id',
        ],
        'account_cost_per_first_day_pay' => [
            'account' => 'ods_toutiao_account_log.account_id',
        ],
        'account_first_day_roi' => [
            'account' => 'ods_toutiao_account_log.account_id',
        ],
        // project
        'project_ori_cost' => [
            'account' => 'ods_toutiao_campaign_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_register' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_first_day_roi' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_total_roi' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_cost_per_reg' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_cost_per_first_day_pay' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_total_pay_money' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_create_time' => [
            'account' => 'ods_toutiao_campaign_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_create_time_relative' => [
            'account' => 'ods_toutiao_campaign_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        'project_cpm' => [
            'account' => 'ods_toutiao_campaign_log.account_id',
            'ad1' => 'ods_toutiao_campaign_log.campaign_id',
        ],
        // promotion
        'promotion_ori_cost' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_register' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_first_day_roi' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_total_roi' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_cost_per_reg' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_cost_per_first_day_pay' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_total_pay_money' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_suggestion' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_create_time' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_create_time_relative' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'promotion_cpm' => [
            'account' => 'ods_toutiao_ad_log.account_id',
            'ad1' => 'ods_toutiao_ad_log.campaign_id',
            'ad2' => 'ods_toutiao_ad_log.ad_id',
        ],
        'creative_material_ori_cost' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
        'creative_material_register' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
        'creative_material_first_day_roi' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
        'creative_material_total_roi' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
        'creative_material_cost_per_reg' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
        'creative_material_cost_per_first_day_pay' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
        'creative_material_total_pay_money' => [
            'account' => 'ods_toutiao_creative_log.account_id',
            'ad1' => 'ods_toutiao_creative_log.campaign_id',
            'ad2' => 'ods_toutiao_creative_log.ad_id',
            'ad3' => 'ods_toutiao_creative_log.creative_id',
        ],
    ];
}
