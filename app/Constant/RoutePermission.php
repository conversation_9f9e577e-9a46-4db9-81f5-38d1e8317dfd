<?php
/**
 * route_permission表cate字段定义
 * User: melody
 * Date: 2020-04-17
 * Time: 11:11
 */


namespace App\Constant;


class RoutePermission
{

    const DIMENSION_FILTER = 1;
    const DIMENSION = 2;
    const TARGET = 3;
    const KPI = 4;
    const CARD = 5;
    const TABLE = 6;
    const CHARTS = 7;
    const KPI_INDICATOR = 8;
    const RANK_SHOW_TYPE = 9;
    const GAME_BINDING = 10;
    const MIX_GAME_BINDING = 11;
    const GAME_TAG = 12;
    const OPEN_GAME = 13;
    const APK = 14;
    const BUTTON = 15;
    const SERVER_WARNING_THRESHOLD = 16;
    const MATERIAL_DOWNLOAD = 17;
    const ADS_SLOT_ID = 18;
    const WECHAT_PUSH_CONFIG = 19;
    const AGENT_CONFIG = 20;
    const RAW_BIND_ROOT_GAME = 21;
    const ORDER_PAY_RATE_PUSH_CONFIG = 24;
    const OUTER_FILTER = 25; // 对外后台专属

    // 团队数据配置权限控制
    const TEAM_DATA_QUERY = 26;
    const TEAM_DATA_CONFIG = 27;
    const TEAM_GROWTH_FACTOR_CONFIG = 28;

    // 数据配置的按根按子查询的表的权限控制
    const ROOT_GAME_TABLE = 22;
    const GAME_TABLE = 23;
    const ROOT_GAME_BACK_TABLE = 34;

    const CHANNEL_BINDING = 29;
    const SITE_CALLBACK_STRATEGY = 30;
    const ENABLE_BI = 31;

    // 新老游戏配置
    const GAME_DIMENSION = 32;

    const INPUT_PERMISSION = 33; // 录入权限 - 产品利润配置专属

    const CATE_MAP = [
        self::DIMENSION_FILTER => '维度筛选',
        self::DIMENSION => '维度选择',
        self::TARGET => '指标选择',
        self::KPI => 'KPI',
        self::CARD => '数据卡片',
        self::TABLE => '数据表格',
        self::CHARTS => '表格图表',
        self::KPI_INDICATOR => '指标',
        self::RANK_SHOW_TYPE => '显示类型',
        self::GAME_BINDING => '游戏绑定',
        self::MIX_GAME_BINDING => '混服绑定',
        self::GAME_TAG => '游戏标签',
        self::OPEN_GAME => '开服时间',
        self::APK => 'APK',
        self::BUTTON => '按钮',
        self::SERVER_WARNING_THRESHOLD => '新服量级预警阈值',
        self::ADS_SLOT_ID => '激励广告代码位',
        self::MATERIAL_DOWNLOAD => '下载次数',
        self::WECHAT_PUSH_CONFIG => '微信推送配置',
        self::AGENT_CONFIG => '官网充值渠道配置',
        self::RAW_BIND_ROOT_GAME => '跨根归因绑定',
        self::ROOT_GAME_TABLE => '按根',
        self::ROOT_GAME_BACK_TABLE => '按回流',
        self::GAME_TABLE => '按子',
        self::ORDER_PAY_RATE_PUSH_CONFIG => '订单推送配置',
        self::OUTER_FILTER => '支持无筛选查询',
        self::TEAM_DATA_QUERY => '数据查询',
        self::TEAM_DATA_CONFIG => '数据配置',
        self::TEAM_GROWTH_FACTOR_CONFIG => '终身倍数配置',
        self::CHANNEL_BINDING => '渠道组绑定',
        self::SITE_CALLBACK_STRATEGY => '扣量配置',
        self::ENABLE_BI => 'BI功能',
        self::GAME_DIMENSION => '新老游戏配置',
        self::INPUT_PERMISSION => '录入权限',
    ];
}
