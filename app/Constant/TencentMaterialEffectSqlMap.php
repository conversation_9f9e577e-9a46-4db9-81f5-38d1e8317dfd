<?php

namespace App\Constant;

class TencentMaterialEffectSqlMap {

    const NEED_JOIN_INEFFICIENT_MATERIAL = [
        'is_inefficient',
        'tt_is_similar_material',
        'tt_is_ad_high_quality',
        'tt_is_first_publish_material',
        'tt_is_ad_low_quality_material',
        'tt_is_ecp_low_quality_material',
        'tt_message_ad_low_quality_material',
        'tt_message_ecp_low_quality_material'
    ];


    const TABLE = [
        'material'          => 'tanwan_datamedia.ods_material_log',
        'material_file'     => 'tanwan_datamedia.ods_material_file_log',
        'material_theme'    => 'tanwan_datamedia.ods_material_theme',
        'material_label'    => 'tanwan_datamedia.ods_material_label',
        'video_log' => 'tanwan_datamedia.ods_tencent_video_log',
        // 媒体广告三级每小时的数据表(
        // 用于计算每个素材每小时的实际消耗价格[媒体给的折扣价(cost)]，每个素材每小时原消耗价格(ori_cost)，每个素材每小时的展示数,
        // 每个素材每小时的点击数,每个素材每小时的激活数,每个素材每小时的转化数,每个素材每小时的注册数,每个素材每小时的付费人数,
        // 每个素材每小时所属计划的site_id，每个素材每小时所属计划site_id中当即小时的game_id(每个小时的game_id会改变)
        // )
        'day_data_log'      => 'ods_tencent_component_day_data_log',
        'ad_log'      => 'ods_tencent_adgroup_log',
        'account_log_table' => 'tanwan_datamedia.ods_tencent_account_log',

        // 渠道&广告配置关联表(用于与驱动表进行关联，控制驱动表的agent_leader和site_id)
        'dim_agent_site'    => 'tanwan_datahub.v2_dim_agent_site_id',
        // 游戏
        'dim_game'          => 'tanwan_datahub.v2_dim_game_id',
        // 广告配置&game_id关联表(用于与驱动表和dim_agent_site进行关联，控制驱动表的game_id)
        'dim_site_game'     => 'tanwan_datamedia.dim_site_game_id',
    ];

    //
    const MAIN_COMPUTE = [
        'tencent_media_material_id' => 'data_log.tencent_media_material_id as tencent_media_material_id',
        'component_type' => 'data_log.component_type as component_type',
        'media_type' => MediaType::TENCENT . ' as media_type',
        //资源位细分
        'site_set'          => 'data_log.site_set as site_set',
//        'inventory_type'                     => 'third_ad_log.inventory_type as inventory_type',
        //10%进度播放量
        'gdt_video_outer_play10_count'       => 'SUM(data_log.gdt_video_outer_play10_count) as gdt_video_outer_play10_count',
        //25%进度播放量
        'gdt_video_outer_play25_count'       => 'SUM(data_log.gdt_video_outer_play25_count) as gdt_video_outer_play25_count',
        //50%进度播放量
        'gdt_video_outer_play50_count'       => 'SUM(data_log.gdt_video_outer_play50_count) as gdt_video_outer_play50_count',
        //75%进度播放量
        'gdt_video_outer_play75_count'       => 'SUM(data_log.gdt_video_outer_play75_count) as gdt_video_outer_play75_count',
        //95%进度播放量
        'gdt_video_outer_play95_count'       => 'SUM(data_log.gdt_video_outer_play95_count) as gdt_video_outer_play95_count',
        //100%进度播放量
        'gdt_video_outer_play100_count'      => 'SUM(data_log.gdt_video_outer_play100_count) as gdt_video_outer_play100_count',
        //平均有效播放时长
        'gdt_video_outer_play_time_count'    =>
            'SUM(gdt_video_outer_total_play_time_count) as gdt_video_outer_total_play_time_count, SUM(data_log.gdt_video_outer_play_count) as gdt_video_outer_play_count',
        //平均有效播放进度
        'gdt_video_outer_play_time_avg_rate' =>
            'SUM(gdt_video_outer_total_play_time_count) as gdt_video_outer_total_play_time_count, SUM(data_log.gdt_video_total_play_time_count) as gdt_video_total_play_time_count',
        //有效播放率
        'gdt_video_outer_play_rate'          =>
            'SUM(data_log.gdt_video_outer_play_count) as gdt_video_outer_play_count, SUM(data_log.`show`) as `show`',
        //有效播放成本
        'gdt_video_outer_play_cost'          =>
            'SUM(data_log.ori_cost) as ori_cost, SUM(data_log.gdt_video_outer_play_count) as gdt_video_outer_play_count',
        //3s播放完成量
        'gdt_video_outer_play3s_count'       => 'SUM(data_log.gdt_video_outer_play3s_count) as gdt_video_outer_play3s_count',
        //5s播放完成量
        'gdt_video_outer_play5s_count'       => 'SUM(data_log.gdt_video_outer_play5s_count) as gdt_video_outer_play5s_count',
        //7s播放完成量
        'gdt_video_outer_play7s_count'       => 'SUM(data_log.gdt_video_outer_play7s_count) as gdt_video_outer_play7s_count',

        //素材属性数据
        'working_hours'                      => 'material_log.working_hours as working_hours',
        'material_platform'                  => 'material_log.material_platform as material_platform',
        'material_name'                      => 'material_log.name as material_name',
        'material_id'                        => 'material_log.material_id as material_id',
        'material_filename'                  => 'material_log.filename as material_filename',
        'material_file_id'                   => 'material_log.id as material_file_id',
        'signature'                          => 'data_log.signature as signature',
        'author'                             => 'material_log.author as author',
        'theme_id'                           => 'material_log.theme_id as theme_id',
        'theme_pid'                          => 'material_log.theme_pid as theme_pid',
//        'file_type'                          => 'material_log.file_type as file_type',
        'create_time'                        => 'material_log.insert_time as create_time',
        'urls'                               => 'data_log.signature as signature, material_log.file_type as file_type, material_log.zx_play_url as zx_play_url',
        'c_author'                           => 'material_log.c_author as c_author',
        'a_author'                           => 'material_log.a_author as a_author',
        'm1_author'                          => 'material_log.m1_author as m1_author',
        'm2_author'                          => 'material_log.m2_author as m2_author',
        'm3_author'                          => 'material_log.m3_author as m3_author',
        'm4_author'                          => 'material_log.m4_author as m4_author',
        'm5_author'                          => 'material_log.m5_author as m5_author',
        'actor'                              => 'material_log.actor as actor',
        'shoot'                              => 'material_log.shoot as shoot',
        'material_create_date'               => 'material_log.insert_time as material_create_date',
        'original'                           => 'material_log.original as original',
        'size'                               => "material_log.size as size",
        'effect_grade7'                      => "material_log.effect_grade7 as effect_grade7",
        'effect_grade30'                     => "material_log.effect_grade30 as effect_grade30",
        'is_priority'                        => "material_log.is_priority as is_priority",
        'is_3d'                              => "material_log.is_3d as is_3d",
        'is_immortal'                        => "material_log.is_immortal as is_immortal",
        'label'                              => "material_log.label_id as label",

        //计数项数据
        'count'                              => "COUNT( DISTINCT third_ad_log.web_creator ) as count",
        'count_ad2'                          => "COUNT( DISTINCT third_ad_log.count_ad2 ) as count_ad2",
        'count_ad2_deliveried'               => "COUNT( DISTINCT data_log.data_log_ad2_id, IF ( data_log.cost > 0, TRUE, NULL ) ) AS count_ad2_deliveried",
        'count_ad2_delivering'               => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.system_status='AD_GROUP_STATUS_NORMAL', TRUE, NULL ) ) AS count_ad2_delivering",
        'count_ad2_undeliveried'             => "COUNT( DISTINCT third_ad_log.count_ad2, IF ( third_ad_log.is_cost = 0, TRUE, NULL ) ) AS count_ad2_undeliveried",
        //'count_cost_date' => [
        //    0 => 'data_log.count_cost_date',
        //    1 => 'SUM(data_log.count_cost_date) as count_cost_date'
        //],
        'count_creative'                     => "SUM(third_ad_log.count_creative) as count_creative",
        //业务数据
        'platform'                           => 'data_log.platform as platform',
        'os'                                 => 'data_log.os as os',
        'create_type'                        => 'third_ad_log.create_type as create_type',
        'account_id'                         => 'data_log.account_id',
        'account_name'                       => 'data_log.account_name, data_log.account_id',
        'ad1_id'                             => 'third_ad_log.ad1_id',
        'ad1_name'                           => 'third_ad_log.ad1_name, third_ad_log.ad1_id',
        'ad2_id'                             => 'third_ad_log.ad2_id as ad2_id',
        'ad2_name'                           => 'third_ad_log.ad2_name, third_ad_log.ad2_id as ad2_id',
        'ad3_id'                             => 'third_ad_log.ad3_id',
        'ad3_name'                           => 'third_ad_log.ad3_name, third_ad_log.ad3_id',
        'game_id'                            => 'data_log.game_id as game_id',
        'game_name'                          => 'data_log.game_name as game_name',
        'main_game_id'                       => 'data_log.main_game_id as main_game_id',
        'main_game_name'                     => 'data_log.main_game_name as main_game_name',
        'root_game_id'                       => 'data_log.root_game_id as root_game_id',
        'root_game_name'                     => 'data_log.root_game_name as root_game_name',
        'site_id'                            => 'data_log.site_id as site_id',
        'agent_leader'                       => 'data_log.agent_leader as agent_leader',
        'agent_leader_group_name'            => 'data_log.agent_leader_group_name as agent_leader_group_name',
        'agent_id'                           => 'data_log.agent_name as agent_name, data_log.agent_id as agent_id',
        'agent_group_id'                     => 'data_log.agent_group_name as agent_group_name, data_log.agent_group_id as agent_group_id',
        'standard_reached_cost'              => [
            0 => 'standard_reached_cost',
            1 => 'SUM(standard_reached_cost) as standard_reached_cost'
        ],
        'standard_unreached_cost'            => [
            0 => 'standard_unreached_cost',
            1 => 'SUM(standard_unreached_cost) as standard_unreached_cost'
        ],
        'cost_first_date'                    => 'min(data_cost_first_log.cost_first_date) as cost_first_date',
        'cost_first_30_day'                  => [
            0 => 'data_cost_first_log.cost_first_30_day',
            1 => 'SUM(data_cost_first_log.cost_first_30_day) as cost_first_30_day'
        ],
        'cost_first_30_day_this_month'       => [
            0 => 'data_cost_first_log.cost_first_30_day_this_month',
            1 => 'SUM(data_cost_first_log.cost_first_30_day_this_month) as cost_first_30_day_this_month'
        ],

        'cost_first_60_day_this_month'   => [
            0 => 'data_cost_first_log.cost_first_60_day_this_month',
            1 => 'SUM(data_cost_first_log.cost_first_60_day_this_month) as cost_first_60_day_this_month'
        ],
        'cost'                           => [
            0 => 'data_log.cost',
            1 => 'SUM(data_log.cost) as cost'
        ], // 返点后消耗
        'first_day_roi_standard_value'   => [
            0 => [ 'data_log.1_day_total_standard_value', 'data_log.cost' ],
            1 => [
                'SUM(data_log.1_day_total_standard_value) as 1_day_total_standard_value',
                'SUM(data_log.cost) as cost'
            ]
        ],
        'rate_day_roi_2_standard_value'  => [
            0 => [ 'data_log.2_day_total_standard_value', 'data_log.cost' ],
            1 => [
                'SUM(data_log.2_day_total_standard_value) as 2_day_total_standard_value',
                'SUM(data_log.cost) as cost'
            ]
        ],
        'rate_day_roi_3_standard_value'  => [
            0 => [ 'data_log.3_day_total_standard_value', 'data_log.cost' ],
            1 => [
                'SUM(data_log.3_day_total_standard_value) as 3_day_total_standard_value',
                'SUM(data_log.cost) as cost'
            ]
        ],
        'rate_day_roi_7_standard_value'  => [
            0 => [ 'data_log.7_day_total_standard_value', 'data_log.cost' ],
            1 => [
                'SUM(data_log.7_day_total_standard_value) as 7_day_total_standard_value',
                'SUM(data_log.cost) as cost'
            ]
        ],
        'rate_day_roi_15_standard_value' => [
            0 => [ 'data_log.15_day_total_standard_value', 'data_log.cost' ],
            1 => [
                'SUM(data_log.15_day_total_standard_value) as 15_day_total_standard_value',
                'SUM(data_log.cost) as cost'
            ]
        ],
        'rate_day_roi_30_standard_value' => [
            0 => [ 'data_log.30_day_total_standard_value', 'data_log.cost' ],
            1 => [
                'SUM(data_log.30_day_total_standard_value) as 30_day_total_standard_value',
                'SUM(data_log.cost) as cost'
            ]
        ],
        //媒体通用数据
        'cost_process'                       => [
            0 => [
                'IFNULL ( CAST ( ( data_log.sum_ori_cost_for_cost_process / third_ad_log.sum_budget_for_cost_process ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_process',
                'data_log.sum_ori_cost_for_cost_process',
                'third_ad_log.sum_budget_for_cost_process'
            ],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.sum_ori_cost_for_cost_process) / SUM(third_ad_log.sum_budget_for_cost_process) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cost_process',
                'SUM(data_log.sum_ori_cost_for_cost_process) as sum_ori_cost_for_cost_process',
                'SUM(third_ad_log.sum_budget_for_cost_process) as sum_budget_for_cost_process'
            ]
        ], //当天账户币消耗/预算
        'cpc'                                => [
            0 => [ 'data_log.ori_cost', 'data_log.click' ],
            1 => [ 'SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.click) as click' ]
        ], // cpc COST / CLICK
        'click'                              => [
            0 => 'data_log.click',
            1 => 'SUM(data_log.click) as click'
        ], //click
        'cpm'                                => [
            0 => [ 'data_log.ori_cost', '`data_log`.`show`' ],
            1 => [ 'SUM(data_log.ori_cost) as ori_cost', 'SUM(`data_log`.`show`) as `show`' ]
        ], // cpm COST/(SHOW/1000)
        'show'                               => [
            0 => '`data_log`.`show`',
            1 => 'SUM(`data_log`.`show`) as `show`'
        ], // show
        'ori_cost'                           => [
            0 => 'data_log.ori_cost',
            1 => 'SUM(data_log.ori_cost) as ori_cost'
        ], // 总花费
        'click_rate'                         => [
            0 => [
                'IFNULL ( CAST ( ( data_log.click / `data_log`.`show` ) AS DECIMAL ( 10, 4 ) ), 0 ) AS click_rate',
                'data_log.click',
                '`data_log`.`show`'
            ],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.click) / SUM(`data_log`.`show`) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS click_rate',
                'SUM(data_log.click) as click',
                'SUM(`data_log`.`show`) as `show`'
            ]
        ], // 点击率
        'cost_per_convert'                   => [
            0 => [ 'data_log.ori_cost', 'data_log.`convert`' ],
            1 => [ 'SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.`convert`) as `convert`' ]
        ], // 转化成本
        'convert'                            => [
            0 => 'data_log.`convert`',
            1 => 'SUM(data_log.`convert`) as `convert`'
        ], // 转化数
        'cost_per_active'                    => [
            0 => [ 'data_log.ori_cost', 'data_log.active_count' ],
            1 => [ 'SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.active_count) as active_count' ]
        ], // 激活成本
        'cost_per_pay'                       => [
            0 => [ 'data_log.ori_cost', 'data_log.pay_count' ],
            1 => [ 'SUM(data_log.ori_cost) as ori_cost', 'SUM(data_log.pay_count) as pay_count' ]
        ], // 付费成本
        'pay_count'                          => [
            0 => 'data_log.pay_count',
            1 => 'SUM(data_log.pay_count) as pay_count'
        ], // 付费数
        'reg_rate'                           => [
            0 => [ 'data_log.reg_count', 'data_log.click' ],
            1 => [ 'SUM(data_log.reg_count) as reg_count', 'SUM(data_log.click) as click' ]
        ], // 注册率
        'active_rate'                        => [
            0 => [ 'data_log.active_count', 'data_log.click' ],
            1 => [ 'SUM(data_log.active_count) as active_count', 'SUM(data_log.click) as click' ]
        ], // 激活率
        'active_count'                       => [
            0 => 'data_log.active_count as active_count',
            1 => 'SUM(data_log.active_count) as active_count'
        ], // 激活数
        'convert_rate'                       => [
            0 => [
                'IFNULL ( CAST ( ( data_log.`convert` / data_log.click ) AS DECIMAL ( 10, 4 ) ), 0 ) AS convert_rate',
                'data_log.`convert`',
                'data_log.click'
            ],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.`convert`) / SUM(data_log.click) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS convert_rate',
                'SUM(data_log.`convert`) as `convert`',
                'SUM(data_log.click) as click'
            ]
        ], // 转化率
        'reg_count'                          => [
            0 => 'data_log.reg_count',
            1 => 'SUM(data_log.reg_count) as reg_count'
        ], // 注册数
        'media_cost_per_reg'                 => [
            0 => [
                'IFNULL ( CAST ( ( data_log.ori_cost / data_log.reg_count ) AS DECIMAL ( 10, 4 ) ), 0 ) AS media_cost_per_reg',
                'data_log.reg_count',
                'data_log.ori_cost'
            ],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.ori_cost) / SUM(data_log.reg_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS media_cost_per_reg',
                'SUM(data_log.reg_count) as reg_count',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],//注册成本(媒体)
        'pay_rate'                           => [
            0 => [
                'IFNULL ( CAST ( ( data_log.pay_count / data_log.active_count ) AS DECIMAL ( 10, 4 ) ), 0 ) AS pay_rate',
                'data_log.pay_count',
                'data_log.active_count'
            ],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.pay_count) / SUM(data_log.active_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS pay_rate',
                'SUM(data_log.pay_count) as pay_count',
                'SUM(data_log.active_count) as active_count'
            ]
        ], // 付费率

        'item_id'                     => 'third_ad_log.item_id as item_id',
        'aweme_title'                 => "third_ad_log.item_id as item_id, if(third_ad_log.item_id!= 0,ifnull( aweme_log.title, third_ad_log.title ), '') AS aweme_title",
        'aweme_account'               => 'third_ad_log.item_id as item_id, aweme_log.aweme_account',
        'aweme_name'                  => 'third_ad_log.item_id as item_id, aweme_log.aweme_name as aweme_name',
        'interface_person'            => 'third_ad_log.item_id as item_id, aweme_log.interface_person as interface_person',
        'interface_person_group_name' => 'third_ad_log.item_id as item_id, aweme_log.interface_person_group_name',
        'intermediary_person'         => 'third_ad_log.item_id as item_id, aweme_log.intermediary_person',
        'aweme_item_publish_time'     => 'third_ad_log.item_id as item_id, aweme_log.aweme_item_publish_time as aweme_item_publish_time',

        'talent_account'          => '"" as talent_account',
        'talent_name'             => 'talent_log.aweme_name as talent_name',
        'talent_interface_person' => 'talent_log.interface_person as talent_interface_person',

        /**
         * 'pay_count'                          => [
                0 => 'data_log.pay_count',
                1 => 'SUM(data_log.pay_count) as pay_count'
            ], // 付费数
        'reg_rate'                           => [
                0 => [ 'data_log.reg_count', 'data_log.click' ],
                1 => [ 'SUM(data_log.reg_count) as reg_count', 'SUM(data_log.click) as click' ]
        ], // 注册率
         * new
         */
        'activated_count' => [
            0 => 'data_log.activated_count',
            1 => 'SUM(data_log.activated_count) as activated_count'
        ],
        'activated_cost' => [
            0 => ['data_log.activated_count', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.activated_count) as activated_count',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'click_activated_rate' => [
            0 => ['data_log.activated_count', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.activated_count) as activated_count',
                'SUM(data_log.valid_click_count) as valid_click_count',
            ]
        ],
        'reg_pv' => [
            0 => 'data_log.reg_pv',
            1 => 'SUM(data_log.reg_pv) as reg_pv'
        ],
        'register_by_display_count' => [
            0 => 'data_log.register_by_display_count',
            1 => 'SUM(data_log.register_by_display_count) as register_by_display_count'
        ],
        'register_by_click_count' => [
            0 => 'data_log.register_by_click_count',
            1 => 'SUM(data_log.register_by_click_count) as register_by_click_count'
        ],
        'reg_cost' => [
            0 => ['data_log.reg_pv', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.reg_pv) as reg_pv',
                'SUM(data_log.ori_cost) as ori_cost'
            ],
        ],
        'reg_clk_rate' => [
            0 => ['data_log.reg_pv', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.reg_pv) as reg_pv',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'activate_register_rate' => [
            0 => ['data_log.reg_pv', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.reg_pv) as reg_pv',
                'SUM(data_log.activated_count) as activated_count'
            ],
        ],
        'reg_pla_pv' => [
            0 => 'data_log.reg_pla_pv',
            1 => 'SUM(data_log.reg_pla_pv) as reg_pla_pv'
        ],
        'web_register_uv' => [
            0 => 'data_log.web_register_uv',
            1 => 'SUM(data_log.web_register_uv) as web_register_uv'
        ],
        'mini_game_register_users' => [
            0 => 'data_log.mini_game_register_users',
            1 => 'SUM(data_log.mini_game_register_users) as mini_game_register_users'
        ],
        'mini_game_register_cost' => [
            0 => ['data_log.mini_game_register_users', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.mini_game_register_users) as mini_game_register_users',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'mini_game_register_rate' => [
            0 => ['data_log.mini_game_register_users', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.mini_game_register_users) as mini_game_register_users',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'first_pay_count' => [
            0 => 'data_log.first_pay_count',
            1 => 'SUM(data_log.first_pay_count) as first_pay_count'
        ],
        'first_pay_cost' => [
            0 => ['data_log.first_pay_count', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.first_pay_count) as first_pay_count',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'first_pay_rate' => [
            0 => ['data_log.first_pay_count', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.first_pay_count) as first_pay_count',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'leads_purchase_uv' => [
            0 => 'data_log.leads_purchase_uv',
            1 => 'SUM(data_log.leads_purchase_uv) as leads_purchase_uv'
        ],
        'mini_game_first_paying_users' => [
            0 => 'data_log.mini_game_first_paying_users',
            1 => 'SUM(data_log.mini_game_first_paying_users) as mini_game_first_paying_users'
        ],
        'key_behavior_conversions_count' => [
            0 => 'data_log.key_behavior_conversions_count',
            1 => 'SUM(data_log.key_behavior_conversions_count) as key_behavior_conversions_count'
        ],
        'key_behavior_conversions_cost' => [
            0 => ['data_log.key_behavior_conversions_count', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.key_behavior_conversions_count) as key_behavior_conversions_count',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'key_behavior_conversions_rate' => [
            0 => ['data_log.key_behavior_conversions_count', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.key_behavior_conversions_count) as key_behavior_conversions_count',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'game_authorize_count' => [
            0 => 'data_log.game_authorize_count',
            1 => 'SUM(data_log.game_authorize_count) as game_authorize_count'
        ],
        'game_create_role_count' => [
            0 => 'data_log.game_create_role_count',
            1 => 'SUM(data_log.game_create_role_count) as game_create_role_count'
        ],
        'mini_game_create_role_users' => [
            0 => 'data_log.mini_game_create_role_users',
            1 => 'SUM(data_log.mini_game_create_role_users) as mini_game_create_role_users'
        ],
        'mini_game_create_role_cost' => [
            0 => ['data_log.mini_game_create_role_users', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.mini_game_create_role_users) as mini_game_create_role_users',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'retention_count' => [
            0 => 'data_log.retention_count',
            1 => 'SUM(data_log.retention_count) as retention_count'
        ],
        'retention_cost' => [
            0 => ['data_log.retention_count', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.retention_count) as retention_count',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'retention_rate' =>  [
            0 => ['data_log.retention_count', 'data_log.activated_count'],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.retention_count) / SUM(data_log.activated_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS retention_rate',
                'SUM(data_log.retention_count) as retention_count',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'app_retention_d2_pv' => [
            0 => 'data_log.app_retention_d2_pv',
            1 => 'SUM(data_log.app_retention_d2_pv) as app_retention_d2_pv'
        ],
        'app_retention_d3_pv' => [
            0 => 'data_log.app_retention_d3_pv',
            1 => 'SUM(data_log.app_retention_d3_pv) as app_retention_d3_pv'
        ],
        'app_retention_d3_uv' => [
            0 => 'data_log.app_retention_d2_pv',
            1 => 'SUM(data_log.app_retention_d3_uv) as app_retention_d3_uv'
        ],
        'app_retention_d3_cost' => [
            0 => ['data_log.app_retention_d3_uv', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.app_retention_d3_uv) as app_retention_d3_uv',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'app_retention_d3_rate' => [
            0 => ['data_log.app_retention_d3_uv', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.app_retention_d3_uv) as app_retention_d3_uv',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'app_retention_d4_pv' => [
            0 => 'data_log.app_retention_d4_pv',
            1 => 'SUM(data_log.app_retention_d4_pv) as app_retention_d4_pv'
        ],
        'app_retention_d5_pv' => [
            0 => 'data_log.app_retention_d5_pv',
            1 => 'SUM(data_log.app_retention_d5_pv) as app_retention_d5_pv'
        ],
        'app_retention_d5_uv' => [
            0 => 'data_log.app_retention_d5_uv',
            1 => 'SUM(data_log.app_retention_d5_uv) as app_retention_d5_uv'
        ],
        'app_retention_d5_cost' => [
            0 => ['data_log.app_retention_d5_uv', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.app_retention_d5_uv) as app_retention_d5_uv',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'app_retention_d5_rate' => [
            0 => ['data_log.app_retention_d5_uv', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.app_retention_d5_uv) as app_retention_d5_uv',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'app_retention_d6_pv' => [
            0 => 'data_log.app_retention_d6_pv',
            1 => 'SUM(data_log.app_retention_d6_pv) as app_retention_d6_pv'
        ],
        'app_retention_d7_pv' => [
            0 => 'data_log.app_retention_d7_pv',
            1 => 'SUM(data_log.app_retention_d7_pv) as app_retention_d7_pv'
        ],
        'app_retention_d7_uv' => [
            0 => 'data_log.app_retention_d7_uv',
            1 => 'SUM(data_log.app_retention_d7_uv) as app_retention_d7_uv'
        ],
        'app_retention_d7_cost' => [
            0 => ['data_log.app_retention_d7_uv', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.app_retention_d7_uv) as app_retention_d7_uv',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'app_retention_d7_rate' => [
            0 => ['data_log.app_retention_d7_uv', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.app_retention_d7_uv) as app_retention_d7_uv',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'app_retention_lt7' => [
            0 => 'data_log.app_retention_lt7',
            1 => 'SUM(data_log.app_retention_lt7) as app_retention_lt7'
        ],
        'app_retention_lt7_cost' => [
            0 => ['data_log.activated_count', 'data_log.ori_cost', 'data_log.app_retention_lt7'],
            1 => [
                'SUM(data_log.activated_count) as activated_count',
                'SUM(data_log.ori_cost) as ori_cost',
                'SUM(data_log.app_retention_lt7) as app_retention_lt7'
            ]
        ],
        'mini_game_retention_d1' => [
            0 => 'data_log.mini_game_retention_d1',
            1 => 'SUM(data_log.mini_game_retention_d1) as mini_game_retention_d1'
        ],
        'mini_game_retention_d1_cost' => [
            0 => ['data_log.mini_game_retention_d1', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.mini_game_retention_d1) as mini_game_retention_d1',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'mini_game_retention_d1_rate' => [
            0 => ['data_log.mini_game_retention_d1', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.mini_game_retention_d1) as mini_game_retention_d1',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'purchase_pv' => [
            0 => 'data_log.purchase_pv',
            1 => 'SUM(data_log.purchase_pv) as purchase_pv'
        ],
        'purchase_imp_pv' => [
            0 => 'data_log.purchase_imp_pv',
            1 => 'SUM(data_log.purchase_imp_pv) as purchase_imp_pv'
        ],
        'purchase_clk_pv' => [
            0 => 'data_log.purchase_clk_pv',
            1 => 'SUM(data_log.purchase_clk_pv) as purchase_clk_pv'
        ],
        'purchase_amount' => [
            0 => 'data_log.purchase_amount',
            1 => 'SUM(data_log.purchase_amount) as purchase_amount'
        ],
        'purchase_cost' => [
            0 => ['data_log.purchase_pv', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.purchase_pv) as purchase_pv',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'purchase_clk_rate' => [
            0 => ['data_log.purchase_pv', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.purchase_pv) as purchase_pv',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'purchase_act_rate' => [
            0 => ['data_log.purchase_pv', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.purchase_pv) as purchase_pv',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'purchase_roi' => [
            0 => ['data_log.purchase_amount', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.purchase_amount) as purchase_amount',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'purchase_act_arpu' => [
            0 => ['data_log.purchase_pv', 'data_log.activated_count'],
            1 => [
                'SUM(data_log.purchase_pv) as purchase_pv',
                'SUM(data_log.activated_count) as activated_count',
            ]
        ],
        'purchase_reg_arpu' => [
            0 => ['data_log.purchase_amount', 'data_log.reg_pv'],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.purchase_amount) / SUM(data_log.reg_count) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS purchase_reg_arpu',
                'SUM(data_log.purchase_amount) as purchase_amount',
                'SUM(data_log.reg_count) as reg_count',
            ]
        ],
        'purchase_reg_arppu' => [
            0 => ['data_log.purchase_amount', 'data_log.purchase_pv'],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.purchase_amount) / SUM(data_log.purchase_pv) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS purchase_reg_arppu',
                'SUM(data_log.purchase_amount) as purchase_amount',
                'SUM(data_log.purchase_pv) as purchase_pv',
            ]
        ],
        'cheout_pv_1d' => [
            0 => 'data_log.cheout_pv_1d',
            1 => 'SUM(data_log.cheout_pv_1d) as cheout_pv_1d'
        ],
        'cheout_fd' => [
            0 => 'data_log.cheout_fd',
            1 => 'SUM(data_log.cheout_fd) as cheout_fd'
        ],
        'cheout_1d_cost' => [
            0 => ['data_log.cheout_pv_1d', 'data_log.ori_cost'],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.cheout_pv_1d) / SUM(data_log.ori_cost) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cheout_1d_cost',
                'SUM(data_log.cheout_pv_1d) as cheout_pv_1d',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_1d_rate' => [
            0 => ['data_log.cheout_pv_1d', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.cheout_pv_1d) as cheout_pv_1d',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'cheout_fd_reward' => [
            0 => ['data_log.cheout_fd', 'data_log.ori_cost'],
            1 => [
                'IFNULL ( CAST ( ( SUM(data_log.cheout_fd) / SUM(data_log.ori_cost) ) AS DECIMAL ( 10, 4 ) ), 0 ) AS cheout_fd_reward',
                'SUM(data_log.cheout_fd) as cheout_fd',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_pv_3d' => [
            0 => 'data_log.cheout_pv_3d',
            1 => 'SUM(data_log.cheout_pv_3d) as cheout_pv_3d'
        ],
        'cheout_td' => [
            0 => 'data_log.cheout_td',
            1 => 'SUM(data_log.cheout_td) as cheout_td'
        ],
        'cheout_3d_cost' => [
            0 => ['data_log.cheout_pv_3d', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_pv_3d) as cheout_pv_3d',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_3d_rate' => [
            0 => ['data_log.cheout_pv_3d', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.cheout_pv_3d) as cheout_pv_3d',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'cheout_td_reward' => [
            0 => ['data_log.cheout_td', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_td) as cheout_td',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_pv_5d' => [
            0 => 'data_log.cheout_pv_5d',
            1 => 'SUM(data_log.cheout_pv_5d) as cheout_pv_5d'
        ],
        'cheout_5d_rate' => [
            0 => ['data_log.cheout_pv_5d', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.cheout_pv_5d) as cheout_pv_5d',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'cheout_5d_cost' => [
            0 => ['data_log.cheout_pv_5d', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_pv_5d) as cheout_pv_5d',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_pv_7d' => [
            0 => 'data_log.cheout_pv_7d',
            1 => 'SUM(data_log.cheout_pv_7d) as cheout_pv_7d'
        ],
        'cheout_ow' => [
            0 => 'data_log.cheout_ow',
            1 => 'SUM(data_log.cheout_ow) as cheout_ow'
        ],
        'cheout_7d_cost' => [
            0 => ['data_log.cheout_pv_7d', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_pv_7d) as cheout_pv_7d',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_7d_rate' => [
            0 => ['data_log.cheout_pv_7d', 'data_log.valid_click_count'],
            1 => [
                'SUM(data_log.cheout_pv_7d) as cheout_pv_7d',
                'SUM(data_log.valid_click_count) as valid_click_count'
            ]
        ],
        'cheout_ow_reward' => [
            0 => ['data_log.cheout_ow', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_ow) as cheout_ow',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'cheout_tw' => [
            0 => 'data_log.cheout_tw',
            1 => 'SUM(data_log.cheout_tw) as cheout_tw'
        ],
        'cheout_tw_reward' => [
            0 => ['data_log.cheout_tw', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_tw) as cheout_tw',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'purchase_clk_15d_pv' => [
            0 => 'data_log.purchase_clk_15d_pv',
            1 => 'SUM(data_log.purchase_clk_15d_pv) as purchase_clk_15d_pv'
        ],
        'cheout_15d' => [
            0 => 'data_log.cheout_15d',
            1 => 'SUM(data_log.cheout_15d) as cheout_15d'
        ],
        'cheout_15d_reward' => [
            0 => ['data_log.cheout_15d', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_15d) as cheout_15d',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'purchase_clk_30d_pv' => [
            0 => 'data_log.purchase_clk_30d_pv',
            1 => 'SUM(data_log.purchase_clk_30d_pv) as purchase_clk_30d_pv'
        ],
        'cheout_om' => [
            0 => 'data_log.cheout_om',
            1 => 'SUM(data_log.cheout_om) as cheout_om'
        ],
        'cheout_om_reward' => [
            0 => ['data_log.cheout_om', 'data_log.ori_cost'],
            1 => [
                'SUM(data_log.cheout_om) as cheout_om',
                'SUM(data_log.ori_cost) as ori_cost'
            ]
        ],
        'mini_game_paying_arpu' => [
            0 => ['data_log.purchase_amount', 'data_log.mini_game_register_users'],
            1 => [
                'SUM(data_log.purchase_amount) as purchase_amount',
                'SUM(data_log.mini_game_register_users) as mini_game_register_users'
            ]
        ]
    ];

    const DATA_LOG = [
        'os' => ['game.os as os'],
        'tencent_media_material_id' => ['data_log.material_id as tencent_media_material_id'],
        'component_type' => ['data_log.component_type as component_type'],
        'game_id' => ['game.game_id as game_id'],
        'game_name' => ['game.game_name as game_name'],
        'main_game_id' => ['game.main_game_id as main_game_id'],
        'main_game_name' => ['game.main_game_name as main_game_name'],
        'root_game_id' => ['game.root_game_id as root_game_id'],
        'root_game_name' => ['game.root_game_name as root_game_name'],
        'agent_leader' => ['agent_site.agent_leader as agent_leader'],
        'agent_id' => ['agent_site.agent_id as agent_id', 'agent_site.agent_name as agent_name'],
        'agent_group_id' => ['agent_site.agent_group_id as agent_group_id', 'agent_site.agent_group_name as agent_group_name'],
//        'file_type' => ['data_log.material_type as file_type'],
        'account_id' => ['account_log.account_id as account_id', 'account_log.account_name as account_name'],
        'agent_leader_group_name' => ["IFNULL( alg.agent_leader_group_name, '未分组' ) AS agent_leader_group_name"],
//        'agent_leader' => ['account_log.account_leader as agent_leader'],
        'platform' => ['data_log.platform as platform'],
        //10%进度播放量
        'gdt_video_outer_play10_count'       => [ 'SUM(data_log.video_outer_play10_count) as gdt_video_outer_play10_count' ],
        //25%进度播放量
        'gdt_video_outer_play25_count'       => [ 'SUM(data_log.video_outer_play25_count) as gdt_video_outer_play25_count' ],
        //50%进度播放量
        'gdt_video_outer_play50_count'       => [ 'SUM(data_log.video_outer_play50_count) as gdt_video_outer_play50_count' ],
        //75%进度播放量
        'gdt_video_outer_play75_count'       => [ 'SUM(data_log.video_outer_play75_count) as gdt_video_outer_play75_count' ],
        //95%进度播放量
        'gdt_video_outer_play95_count'       => [ 'SUM(data_log.video_outer_play95_count) as gdt_video_outer_play95_count' ],
        //100%进度播放量
        'gdt_video_outer_play100_count'      => [ 'SUM(data_log.video_outer_play100_count) as gdt_video_outer_play100_count' ],
        //平均有效播放时长
        'gdt_video_outer_play_time_count'    => [
            'SUM(data_log.video_outer_play_count * data_log.video_outer_play_time_count) as gdt_video_outer_total_play_time_count',
            'SUM(data_log.video_outer_play_count) as gdt_video_outer_play_count'
        ],
        //平均有效播放进度
        'gdt_video_outer_play_time_avg_rate' => [
            'SUM(data_log.video_outer_play_count * data_log.video_outer_play_time_count) as gdt_video_outer_total_play_time_count',
            //视频有效播放量*视频时长=视频播放总时长
            'SUM((data_log.video_outer_play_count * data_log.video_outer_play_time_count ) / video_outer_play_time_avg_rate ) as gdt_video_total_play_time_count'
        ],
        //有效播放率
        'gdt_video_outer_play_rate'          => [
            'SUM(data_log.video_outer_play_count) as gdt_video_outer_play_count',
            'SUM(data_log.`view_count`) as `show`'
        ],
        //有效播放成本
        'gdt_video_outer_play_cost'          => [
            'SUM(data_log.ori_cost) as ori_cost',
            'SUM(data_log.video_outer_play_count) as gdt_video_outer_play_count'
        ],
        //3s播放完成量
        'gdt_video_outer_play3s_count'       => [ 'SUM(data_log.video_outer_play3s_count) as gdt_video_outer_play3s_count' ],
        //5s播放完成量
        'gdt_video_outer_play5s_count'       => [ 'SUM(data_log.video_outer_play5s_count) as gdt_video_outer_play5s_count' ],
        //7s播放完成量
        'gdt_video_outer_play7s_count'       => [ 'SUM(data_log.video_outer_play7s_count) as gdt_video_outer_play7s_count' ],
        //一阶段花费(微信)
        'gdt_wechat_cost_stage1'             => [ 'SUM(data_log.wechat_cost_stage1) as gdt_wechat_cost_stage1' ],
        //二阶段花费(微信)
        'gdt_wechat_cost_stage2'             => [ 'SUM(data_log.wechat_cost_stage2) as gdt_wechat_cost_stage2' ],
        //有效消耗
        'standard_reached_cost'              => [ 'SUM(data_log.cost) as cost' ],
        //无效消耗
        'standard_unreached_cost'            => [ 'SUM(data_log.cost) as cost' ],
        //首日付费次数成本
        'cost_first_day_pay_times'           => [ 'SUM(data_log.cost) as cost' ],
        //首日付费次数成本
        'cost_total_pay_times'               => [ 'SUM(data_log.cost) as cost' ],
        //次留成本
        'cost_day_second_login'              => [ 'SUM(data_log.cost) as cost' ],

        //资源位细分
        'site_set'          => [ 'data_log.site_set as site_set' ],
        //自动扩量细分
        'gdt_is_expand_targeting'            => [ 'data_log.is_expand_targeting as gdt_is_expand_targeting' ],
        'cost_date'                          => [ 'data_log.cost_date' ],
        'count_ad2_deliveried'               => [
            'data_log.adgroup_id as data_log_ad2_id',
            'SUM(data_log.cost) as cost'
        ],
//         'count_cost_date' => ['cost_date',  'SUM(ori_cost) as ori_cost'],
        'count_cost_date'                    => [
            'COUNT( DISTINCT data_log.cost_date, IF(data_log.cost>0, 1, null) ) 
         as count_cost_date'
        ],
        'cost'                               => [ 'SUM(data_log.cost) as cost' ],
        'cost_per_reg'                       => [ 'SUM(data_log.cost) as cost' ],
        'cost_per_first_day_pay'             => [ 'SUM(data_log.cost) as cost' ],
        'first_day_roi'                      => [ 'SUM(data_log.cost) as cost' ],
        'total_roi'                          => [ 'SUM(data_log.cost) as cost' ],
        'cost_process'                       =>
            [ "SUM(if(data_log.cost_date = CURRENT_DATE,data_log.ori_cost,0)) as sum_ori_cost_for_cost_process" ],
        'cpc'                                => [
            'SUM(data_log.valid_click_count) as click',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'click'                              => [ 'SUM(data_log.valid_click_count) as click' ],
        'cpm'                                => [
            'SUM(data_log.`view_count`) as `show`',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'show'                               => [ 'SUM(data_log.`view_count`) as `show`' ],
        'ori_cost'                           => [ 'SUM(data_log.ori_cost) as ori_cost' ],
        'click_rate'                         => [
            'SUM(data_log.valid_click_count) as click',
            'SUM(data_log.`view_count`) as `show`'
        ],
        'cost_per_convert'                   => [
            'SUM(data_log.ori_cost) as ori_cost',
            'SUM(data_log.`conversions_count`) as `convert`'
        ],
        'convert'                            => [ 'SUM(data_log.`conversions_count`) as `convert`' ],
        'cost_per_active'                    => [
            'SUM(data_log.activated_count) as active_count',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cost_per_pay'                       => [
            'SUM(data_log.purchase_pv) as pay_count',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'pay_count'                          => [ 'SUM(data_log.purchase_pv) as pay_count' ],
        'reg_rate'                           => [
            'SUM(data_log.reg_pv) as reg_count',
            'SUM(data_log.valid_click_count) as click'
        ],
        'active_rate'                        => [
            'SUM(data_log.activated_count) as active_count',
            'SUM(data_log.valid_click_count) as click'
        ],
        'active_count'                       => [ 'SUM(data_log.activated_count) as active_count' ],
        'convert_rate'                       => [
            'SUM(data_log.`conversions_count`) as `convert`',
            'SUM(data_log.valid_click_count) as click'
        ],
        'reg_count'                          => [ 'SUM(data_log.reg_pv) as reg_count' ],
        'media_cost_per_reg'                 => [
            'SUM(data_log.ori_cost) as ori_cost',
            'SUM(data_log.reg_pv) as reg_count'
        ],
        'pay_rate'                           => [
            'SUM(data_log.purchase_pv) as pay_count',
            'SUM(data_log.activated_count) as active_count'
        ],
        //二回
        'rate_day_roi_2'                     => [ 'SUM(data_log.cost) as cost' ],
        //三回
        'rate_day_roi_3'                     => [ 'SUM(data_log.cost) as cost' ],
        //七回
        'rate_day_roi_7'                     => [ 'SUM(data_log.cost) as cost' ],
        'rate_day_roi_15'                    => [ 'SUM(data_log.cost) as cost' ],
        'rate_day_roi_30'                    => [ 'SUM(data_log.cost) as cost' ],
        'first_day_roi_standard_value'       => [
            "SUM(cost*day_1_standard_value) AS 1_day_total_standard_value",
            'SUM(data_log.cost) as cost'
        ],
        'rate_day_roi_2_standard_value'      => [
            "SUM(cost*day_2_standard_value) AS 2_day_total_standard_value",
            'SUM(data_log.cost) as cost'
        ],
        'rate_day_roi_3_standard_value'      => [
            "SUM(cost*day_3_standard_value) AS 3_day_total_standard_value",
            'SUM(data_log.cost) as cost'
        ],
        'rate_day_roi_7_standard_value'      => [
            "SUM(cost*day_7_standard_value) AS 7_day_total_standard_value",
            'SUM(data_log.cost) as cost'
        ],
        'rate_day_roi_15_standard_value'     => [
            "SUM(cost*day_15_standard_value) AS 15_day_total_standard_value",
            'SUM(data_log.cost) as cost'
        ],
        'rate_day_roi_30_standard_value'     => [
            "SUM(cost*day_30_standard_value) AS 30_day_total_standard_value",
            'SUM(data_log.cost) as cost'
        ],
        /**
         * new
         */
        'activated_count' => ['SUM(data_log.activated_count) as activated_count'],
        'activated_cost' => [
            'SUM(data_log.activated_count) as activated_count',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'click_activated_rate' => [
            'SUM(data_log.activated_count) as activated_count',
            'SUM(data_log.valid_click_count) as valid_click_count',
        ],
        'reg_pv' => ['SUM(data_log.reg_pv) as reg_pv'],
        'register_by_display_count' => ['SUM(data_log.register_by_display_count) as register_by_display_count'],
        'register_by_click_count' => ['SUM(data_log.register_by_click_count) as register_by_click_count'],
        'reg_cost' => [
            'SUM(data_log.reg_pv) as reg_pv',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'reg_clk_rate' => [
            'SUM(data_log.reg_pv) as reg_pv',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'activate_register_rate' => [
            'SUM(data_log.reg_pv) as reg_pv',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'reg_pla_pv' => ['SUM(data_log.reg_pla_pv) as reg_pla_pv'],
        'web_register_uv' => ['SUM(data_log.web_register_uv) as web_register_uv'],
        'mini_game_register_users' => ['SUM(data_log.mini_game_register_users) as mini_game_register_users'],
        'mini_game_register_cost' => [
            'SUM(data_log.mini_game_register_users) as mini_game_register_users',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'mini_game_register_rate' => [
            'SUM(data_log.mini_game_register_users) as mini_game_register_users',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'first_pay_count' => ['SUM(data_log.first_pay_count) as first_pay_count'],
        'first_pay_cost' => [
            'SUM(data_log.first_pay_count) as first_pay_count',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'first_pay_rate' => [
            'SUM(data_log.first_pay_count) as first_pay_count',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'leads_purchase_uv' => ['SUM(data_log.leads_purchase_uv) as leads_purchase_uv'],
        'mini_game_first_paying_users' => ['SUM(data_log.mini_game_first_paying_users) as mini_game_first_paying_users'],
        'key_behavior_conversions_count' => ['SUM(data_log.key_behavior_conversions_count) as key_behavior_conversions_count'],
        'key_behavior_conversions_cost' => [
            'SUM(data_log.key_behavior_conversions_count) as key_behavior_conversions_count',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'key_behavior_conversions_rate' => [
            'SUM(data_log.key_behavior_conversions_count) as key_behavior_conversions_count',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'game_authorize_count' => ['SUM(data_log.game_authorize_count) as game_authorize_count'],
        'game_create_role_count' => ['SUM(data_log.game_create_role_count) as game_create_role_count'],
        'mini_game_create_role_users' => ['SUM(data_log.mini_game_create_role_users) as mini_game_create_role_users'],
        'mini_game_create_role_cost' => [
            'SUM(data_log.mini_game_create_role_users) as mini_game_create_role_users',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'retention_count' => ['SUM(data_log.retention_count) as retention_count'],
        'retention_cost' => [
            'SUM(data_log.retention_count) as retention_count',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'retention_rate' =>  [
            'SUM(data_log.retention_count) as retention_count',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'app_retention_d2_pv' => ['SUM(data_log.app_retention_d2_pv) as app_retention_d2_pv'],
        'app_retention_d3_pv' => ['SUM(data_log.app_retention_d3_pv) as app_retention_d3_pv'],
        'app_retention_d3_uv' => ['SUM(data_log.app_retention_d3_uv) as app_retention_d3_uv'],
        'app_retention_d3_cost' => [
            'SUM(data_log.app_retention_d3_uv) as app_retention_d3_uv',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'app_retention_d3_rate' => [
            'SUM(data_log.app_retention_d3_uv) as app_retention_d3_uv',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'app_retention_d4_pv' => ['SUM(data_log.app_retention_d4_pv) as app_retention_d4_pv'],
        'app_retention_d5_pv' => ['SUM(data_log.app_retention_d5_pv) as app_retention_d5_pv'],
        'app_retention_d5_uv' => ['SUM(data_log.app_retention_d5_uv) as app_retention_d5_uv'],
        'app_retention_d5_cost' => [
            'SUM(data_log.app_retention_d5_uv) as app_retention_d5_uv',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'app_retention_d5_rate' => [
            'SUM(data_log.app_retention_d5_uv) as app_retention_d5_uv',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'app_retention_d6_pv' => ['SUM(data_log.app_retention_d6_pv) as app_retention_d6_pv'],
        'app_retention_d7_pv' => ['SUM(data_log.app_retention_d7_pv) as app_retention_d7_pv'],
        'app_retention_d7_uv' => ['SUM(data_log.app_retention_d7_uv) as app_retention_d7_uv'],
        'app_retention_d7_cost' => [
            'SUM(data_log.app_retention_d7_uv) as app_retention_d7_uv',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'app_retention_d7_rate' => [
            'SUM(data_log.app_retention_d7_uv) as app_retention_d7_uv',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'app_retention_lt7' => ['SUM(data_log.app_retention_lt7) as app_retention_lt7'],
        'app_retention_lt7_cost' => [
            'SUM(data_log.activated_count) as activated_count',
            'SUM(data_log.ori_cost) as ori_cost',
            'SUM(data_log.app_retention_lt7) as app_retention_lt7'
        ],
        'mini_game_retention_d1' => ['SUM(data_log.mini_game_retention_d1) as mini_game_retention_d1'],
        'mini_game_retention_d1_cost' => [
            'SUM(data_log.mini_game_retention_d1) as mini_game_retention_d1',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'mini_game_retention_d1_rate' => [
            'SUM(data_log.mini_game_retention_d1) as mini_game_retention_d1',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'purchase_pv' => ['SUM(data_log.purchase_pv) as purchase_pv'],
        'purchase_imp_pv' => ['SUM(data_log.purchase_imp_pv) as purchase_imp_pv'],
        'purchase_clk_pv' => ['SUM(data_log.purchase_clk_pv) as purchase_clk_pv'],
        'purchase_amount' => ['SUM(data_log.purchase_amount) as purchase_amount'],
        'purchase_cost' => [
            'SUM(data_log.purchase_pv) as purchase_pv',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'purchase_clk_rate' => [
            'SUM(data_log.purchase_pv) as purchase_pv',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'purchase_act_rate' => [
            'SUM(data_log.purchase_pv) as purchase_pv',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'purchase_roi' => [
            'SUM(data_log.purchase_amount) as purchase_amount',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'purchase_act_arpu' => [
            'SUM(data_log.purchase_pv) as purchase_pv',
            'SUM(data_log.activated_count) as activated_count',
        ],
        'purchase_reg_arpu' => [
            'SUM(data_log.purchase_amount) as purchase_amount',
            'SUM(data_log.reg_pv) as reg_count',
        ],
        'purchase_reg_arppu' => [
            'SUM(data_log.purchase_amount) as purchase_amount',
            'SUM(data_log.purchase_pv) as purchase_pv',
        ],
        'cheout_pv_1d' => ['SUM(data_log.cheout_pv_1d) as cheout_pv_1d'],
        'cheout_fd' => ['SUM(data_log.cheout_fd) as cheout_fd'],
        'cheout_1d_cost' => [
            'SUM(data_log.cheout_pv_1d) as cheout_pv_1d',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_1d_rate' => [
            'SUM(data_log.cheout_pv_1d) as cheout_pv_1d',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'cheout_fd_reward' => [
            'SUM(data_log.cheout_fd) as cheout_fd',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_pv_3d' => ['SUM(data_log.cheout_pv_3d) as cheout_pv_3d'],
        'cheout_td' => ['SUM(data_log.cheout_td) as cheout_td'],
        'cheout_3d_cost' => [
            'SUM(data_log.cheout_pv_3d) as cheout_pv_3d',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_3d_rate' => [
            'SUM(data_log.cheout_pv_3d) as cheout_pv_3d',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'cheout_td_reward' => [
            'SUM(data_log.cheout_td) as cheout_td',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_pv_5d' => ['SUM(data_log.cheout_pv_5d) as cheout_pv_5d'],
        'cheout_5d_rate' => [
            'SUM(data_log.cheout_pv_5d) as cheout_pv_5d',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'cheout_5d_cost' => [
            'SUM(data_log.cheout_pv_5d) as cheout_pv_5d',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_pv_7d' => ['SUM(data_log.cheout_pv_7d) as cheout_pv_7d'],
        'cheout_ow' => ['SUM(data_log.cheout_ow) as cheout_ow'],
        'cheout_7d_cost' => [
            'SUM(data_log.cheout_pv_7d) as cheout_pv_7d',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_7d_rate' => [
            'SUM(data_log.cheout_pv_7d) as cheout_pv_7d',
            'SUM(data_log.valid_click_count) as valid_click_count'
        ],
        'cheout_ow_reward' => [
            'SUM(data_log.cheout_ow) as cheout_ow',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'cheout_tw' => ['SUM(data_log.cheout_tw) as cheout_tw'],
        'cheout_tw_reward' => [
            'SUM(data_log.cheout_tw) as cheout_tw',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'purchase_clk_15d_pv' => ['SUM(data_log.purchase_clk_15d_pv) as purchase_clk_15d_pv'],
        'cheout_15d' => ['SUM(data_log.cheout_15d) as cheout_15d'],
        'cheout_15d_reward' => [
            'SUM(data_log.cheout_15d) as cheout_15d',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'purchase_clk_30d_pv' => ['SUM(data_log.purchase_clk_30d_pv) as purchase_clk_30d_pv'],
        'cheout_om' => ['SUM(data_log.cheout_om) as cheout_om'],
        'cheout_om_reward' => [
            'SUM(data_log.cheout_om) as cheout_om',
            'SUM(data_log.ori_cost) as ori_cost'
        ],
        'mini_game_paying_arpu' => [
            'SUM(data_log.purchase_amount) as purchase_amount',
            'SUM(data_log.mini_game_register_users) as mini_game_register_users'
        ]
    ];

    const ACCOUNT_LOG = [
        'account_name' => [ 'account_name' ],
    ];

    const MATERIAL_LOG = [
        'file_type'        => [ 'material_file.file_type' ],
        'working_hours'        => [ 'material.working_hours' ],
        'urls'                 => [ "REPLACE(url,'.mp4','.wm') as zx_play_url" ],
        'material_platform'    => [ 'material.platform as material_platform' ],
        'material_name'        => [ 'material.name' ],
        'material_id'          => [ 'material_file.material_id' ],
        'author'               => [ 'material.author' ],
        'theme_id'             => [ 'material.theme_id' ],
        'theme_pid'            => [ 'material_theme.theme_pid' ],
        'create_time'          => [ 'material.insert_time' ],
        'c_author'             => [ 'material.c_author' ],
        'a_author'             => [ 'material.a_author' ],
        'm1_author'            => [ 'material.m1_author' ],
        'm2_author'            => [ 'material.m2_author' ],
        'm3_author'            => [ 'material.m3_author' ],
        'm4_author'            => [ 'material.m4_author' ],
        'm5_author'            => [ 'material.m5_author' ],
        'actor'                => [ 'material.actor' ],
        'shoot'                => [ 'material.shoot' ],
        'material_create_date' => [ 'material.insert_time' ],
        'original'             => [ 'material.original' ],
        'effect_grade7'        => [ 'material.effect_grade7' ],
        'effect_grade30'       => [ 'material.effect_grade30' ],
        'is_priority'          => [ 'material.is_priority' ],
        'is_3d'                => [ 'material.is_3d' ],
        'is_immortal'          => [ 'material.is_immortal' ],
        'label'                => [ 'material_label1.label_id' ],

        'material_filename' => [ 'material_file.filename' ],
        'material_file_id'  => [ 'material_file.id' ],
        'size'              => [ "CONCAT(material_file.width, '*', material_file.height) as size" ],
    ];

    const SITE_GAME_FILTER = [];

    const AGENT_SITE_FILTER = [
        'agent_leader'            => 'agent_site.agent_leader',
        'platform-agent_id'       => 'agent_site.agent_id',
        'platform-agent_group_id' => 'agent_site.agent_group_id'
    ];

    const AGENT_LEADER_GROUP_FILTER = [ 'agent_leader_group_name' => 'alg.agent_leader_group_name' ];

    const GAME_FILTER = [
        'os'                    => 'game.os',
        'platform-game_id'      => 'game_id',
        'platform-main_game_id' => 'main_game_id',
        'platform-root_game_id' => 'root_game_id',
    ];

    const ACCOUNT_LOG_FILTER = [
        'account_id'   => 'account_log.account_id',
        'account_name' => 'account_log.account_name',
        'agent_leader' => 'account_log.account_leader'
    ];

    const MATERIAL_FILTER = [
        'working_hours'        => 'material.working_hours',
        'material_name'        => 'material.name',
        'platform-material_id' => 'material.material_id',
        'platform-theme_id'    => 'material.theme_id',
        'platform-theme_pid'   => 'material.theme_id',
        'author'               => 'material.author',
        'c_author'             => 'material.c_author',
        'a_author'             => 'material.a_author',
        'm1_author'            => 'material.m1_author',
        'm2_author'            => 'material.m2_author',
        'm3_author'            => 'material.m3_author',
        'm4_author'            => 'material.m4_author',
        'm5_author'            => 'material.m5_author',
        'actor'                => 'material.actor',
        'shoot'                => 'material.shoot',
        'material_create_date' => 'material.insert_time',
        'original'             => 'material.original',
        'effect_grade7'        => 'material.effect_grade7',
        'effect_grade30'       => 'material.effect_grade30',
        'is_priority'          => 'material.is_priority',
        'is_3d'                => 'material.is_3d',
        'is_immortal'          => 'material.is_immortal',
    ];

    const MATERIAL_FILE_FILTER = [
        'material_platform' => 'material_file.platform',
        'material_filename' => 'material_file.filename',
        'material_file_id'  => 'material_file.id',
        //'signature' => 'material_file.signature',
        'size'              => "CONCAT(material_file.width, '*', material_file.height)"
    ];

    const MATERIAL_LABEL_FILTER = [
        'label'     => 'material_label.label_id',
        'label_pid' => 'material_label.label_pid'
    ];

    const AWEME_FILTER = [
        'item_id'                         => 'aweme_list.item_id',
        'aweme_title'                     => 'aweme_list.title',
        'aweme_account'                   => 'aweme_list.aweme_id',
        'aweme_name'                      => 'aweme_list.aweme_name',
        'interface_person'                => 'agent_site.interface_person',
        'interface_person_group_name'     => 'person_group.interface_person_group_name',
        'intermediary_person'             => 'agent_site.intermediary_person',
        'tt_three_day_cost_five_standard' => 'toutiao_item_cost.three_day_cost_five_standard',
        //重点素材首日达标率是否大于50%
        'tt_first_day_roi_standard'       => 'toutiao_item_cost.first_day_roi_standard',
    ];

    const DATA_LOG_GROUP_BY = [
        'tencent_media_material_id' => 'tencent_media_material_id',
        'component_type' => 'component_type',
        'game_id' => 'game_id',
        'game_name' => 'game_name',
        'main_game_id' => 'main_game_id',
        'main_game_name' => 'main_game_name',
        'root_game_id' => 'root_game_id',
        'root_game_name' => 'root_game_name',
//        'agent_leader' => ['agent_site.agent_leader as agent_leader'],
        'agent_id' => 'agent_id',
        'agent_group_id' => 'agent_group_id',
        'account_id' => 'account_id',
        'agent_leader_group_name' => 'agent_leader_group_name',
        'agent_leader' => 'agent_leader',
        'platform' => 'platform',
        //资源位细分
        'site_set' => 'site_set',
    ];

    const MATERIAL_FILE_GROUP_BY = [
        'signature' => 'signature',
        'platform'  => 'platform'
    ];

    const MATERIAL_GROUP_BY = [
        'material_id' => 'material_id',
        'platform'    => 'platform'
    ];

    const AWEME_GROUP_BY = [
        'item_id'                     => 'item_id',
        'aweme_title'                 => 'item_id',
        'aweme_account'               => 'item_id',
        'aweme_name'                  => 'item_id',
        'interface_person'            => 'item_id',
        'interface_person_group_name' => 'item_id',
        'intermediary_person'         => 'item_id',
        'aweme_item_publish_time'     => 'item_id'
    ];

    const DATA_LOG_FILTER = [
        'platform' => 'data_log.platform',
        'site_set' => 'data_log.site_set',
        'component_type' => 'data_log.component_type'
    ];
}