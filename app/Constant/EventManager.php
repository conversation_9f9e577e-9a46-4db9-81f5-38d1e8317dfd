<?php

namespace App\Constant;

class EventManager
{
    const ASSET_APP = 'APP';
    const ASSET_THIRD_EXTERNAL = 'THIRD_EXTERNAL';
    const ASSET_QUICK_APP = 'QUICK_APP';
    const ASSET_TETRIS_EXTERNAL = 'TETRIS_EXTERNAL';
    const ASSET_DY_MINI_GAME = "MINI_PROGRAME";

    // 资产中文映射
    const ASSET_MAP = [
        self::ASSET_APP => '应用资产',
        self::ASSET_THIRD_EXTERNAL => '三方落地页资产',
        self::ASSET_QUICK_APP => '快应用资产',
        self::ASSET_TETRIS_EXTERNAL => '橙子建站资产',
        self::ASSET_DY_MINI_GAME => '字节小游戏',
    ];

    const EVENT_ACTIVE = 'active';
    const EVENT_REGISTER = 'active_register';
    const EVENT_PAY = 'active_pay';
    const EVENT_NEXT_DAY_OPEN = 'next_day_open';
    const EVENT_PURCHASE_ROI = 'purchase_roi';
    const EVENT_PURCHASE_ROI_7D = 'purchase_roi_7d';

    // 事件中文映射
    const EVENT_MAP = [
        self::EVENT_ACTIVE => '激活',
        self::EVENT_REGISTER => '注册',
        self::EVENT_PAY => '付费',
        self::EVENT_NEXT_DAY_OPEN => '次留',
        self::EVENT_PURCHASE_ROI => '付费ROI',
        self::EVENT_PURCHASE_ROI_7D => '7日付费ROI',
    ];

    // 资产创建事件ID
    const EVENT_ID_MAP = [
        self::EVENT_ACTIVE => 8,
        self::EVENT_REGISTER => 13,
        self::EVENT_PAY => 14,
        self::EVENT_NEXT_DAY_OPEN => 39,
        self::EVENT_PURCHASE_ROI => 160,
        self::EVENT_PURCHASE_ROI_7D => 360,
    ];

    // 推广目的
    const LANDING_TYPE_LINK = 'LINK';
    const LANDING_TYPE_APP = 'APP';
    const LANDING_TYPE_QUICK_APP = 'QUICK_APP';

    // 营销目的
    const MARKETING_PURPOSE_CONVERSION = 'CONVERSION';

    // 资产来源
    const ASSET_ROLE_MINE = 'MY_CREATIONS';  // 自己创建的
    const ASSET_ROLE_SHARED = 'SHARING';     // 共享中
}

