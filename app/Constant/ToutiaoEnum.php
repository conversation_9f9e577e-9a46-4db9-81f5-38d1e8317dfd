<?php

namespace App\Constant;

/**
 * 头条枚举
 * Class ToutiaoEnum
 * @package App\Constant
 * User: zsp
 * Date: 2021/11/5 0005
 * Time: 14:39
 */
class ToutiaoEnum
{
    //营销链路开启状态
    const MARKETING_PURPOSE_STATUS = true;

    //营销目的 固定行为转化
    const MARKETING_PURPOSE_CONVERSION = 'CONVERSION';

    //广告计划状态
    const OPERATION_DISABLE = 'disable';
    const OPERATION_ENABLE = 'enable';

    const OPERATION_DISABLE_2 = 'DISABLE';
    const OPERATION_ENABLE_2 = 'ENABLE';

    //下载方式
    const DOWNLOAD_TYPE_DOWNLOAD_URL = 'DOWNLOAD_URL';
    const DOWNLOAD_TYPE_EXTERNAL_URL = 'EXTERNAL_URL';

    //下载的应用类型
    const APP_TYPE_ANDROID = 'APP_ANDROID';
    const APP_TYPE_IOS = 'APP_IOS';

    //下载模式
    const DOWNLOAD_MODE_APPSTORE = 'APP_STORE_DELIVERY';
    const DOWNLOAD_MODE_DEFAULT = 'DEFAULT';

    //投放内容
    const PROMOTION_TYPE_AWEME_HOME_PAGE = 'AWEME_HOME_PAGE';
    const PROMOTION_TYPE_LANDING_PAGE_LINK = 'LANDING_PAGE_LINK';

    //投放范围
    const DELIVERY_RANGE_DEFAULT = 'DEFAULT';    //默认
    const DELIVERY_RANGE_UNION = 'UNION';      //穿山甲
    const DELIVERY_RANGE_UNIVERSAL = 'UNIVERSAL';  //通投

    //广告位大类
    const INVENTORY_CATALOG_MANUAL = 'MANUAL';
    const INVENTORY_CATALOG_SCENE = 'SCENE';
    const INVENTORY_CATALOG_SMART = 'SMART';
    const INVENTORY_CATALOG_UNIVERSAL = 'UNIVERSAL_SMART';

    //优选广告位
    const SMART_INVENTORY_NORMAL = 'NORMAL';
    const SMART_INVENTORY_SMART = 'SMART';
    const SMART_INVENTORY_UNIVERSAL = 'UNIVERSAL_ALL';

    //投放形式（穿山甲视频创意类型）
    const UNION_VIDEO_TYPE_ORIGINAL_VIDEO = 'ORIGINAL_VIDEO'; //原生
    const UNION_VIDEO_TYPE_REWARDED_VIDEO = 'REWARDED_VIDEO'; //激励视频
    const UNION_VIDEO_TYPE_SPLASH_VIDEO = 'SPLASH_VIDEO';   //开屏

    //首选媒体是穿山甲
    const UNION_INVENTORY_TYPE_LIST = [
        "INVENTORY_UNION_SLOT", "INVENTORY_UNION_SPLASH_SLOT"
    ];

    //穿山甲开屏
    const UNION_INVENTORY_TYPE_UNION_SPLASH_SLOT = 'INVENTORY_UNION_SPLASH_SLOT';
    //穿山甲ohayoo精品游戏（广告投放）
    const UNION_INVENTORY_TYPE_UNION_BOUTIQUE_GAME = 'UNION_BOUTIQUE_GAME';

    //搜索快投关键词功能
    const FEED_DELIVERY_SEARCH_HAS_OPEN = 'HAS_OPEN';
    const FEED_DELIVERY_SEARCH_DISABLED = 'DISABLED';

    //搜索快投关键词功能
    const FEED_DELIVERY_SEARCH_AUDIENCE_EXTEND_ON= 'ON';
    const FEED_DELIVERY_SEARCH_AUDIENCE_EXTEND_OFF = 'OFF';

    //位置类型
    const LOCATION_TYPE_CURRENT = 'CURRENT';
    const LOCATION_TYPE_HOME = 'HOME';
    const LOCATION_TYPE_TRAVEL = 'TRAVEL';
    const LOCATION_TYPE_ALL = 'ALL';

    //性别
    const GENDER_NONE = 'NONE';
    const GENDER_FEMALE = 'GENDER_FEMALE';
    const GENDER_MALE = 'GENDER_MALE';

    const AGE_NONE = 'buxian';
    const AGE_CUSTOM = 'zidingyi';

    //行为兴趣
    const INTEREST_ACTION_MODE_UNLIMITED = 'UNLIMITED';
    const INTEREST_ACTION_MODE_CUSTOM = 'CUSTOM';
    const INTEREST_ACTION_MODE_RECOMMEND = 'RECOMMEND';

    //用户发生行为天数
    const ACTION_DAYS_7 = '7';
    const ACTION_DAYS_15 = '15';
    const ACTION_DAYS_30 = '30';
    const ACTION_DAYS_60 = '60';
    const ACTION_DAYS_90 = '90';
    const ACTION_DAYS_180 = '180';
    const ACTION_DAYS_365 = '365';

    //抖音达人互动行为时间范围
    const AWEME_FAN_TIME_SCOPE_15 = 'FIFTEEN_DAYS';
    const AWEME_FAN_TIME_SCOPE_30 = 'THIRTY_DAYS';
    const AWEME_FAN_TIME_SCOPE_60 = 'SIXTY_DAYS';

    //（抖音号、直播间推广特有）过滤高活跃用户
    const FILTER_AWEME_ABNORMAL_ACTIVE_TRUE = '1';//过滤
    const FILTER_AWEME_ABNORMAL_ACTIVE_FALSE = '0';//不过滤

    //（抖音号、直播间推广特有）过滤自己的粉丝
    const FILTER_OWN_AWEME_FANS_TRUE = '1';//过滤
    const FILTER_OWN_AWEME_FANS_FALSE = '0';//不过滤

    //平台
    const PLATFORM_ANDROID = 'ANDROID';
    const PLATFORM_IOS = 'IOS';
    const PLATFORM_PC = 'PC';

    //过滤已安装
    const HIDE_IF_EXISTS_NONE = '0';
    const HIDE_IF_EXISTS_FILTER = '1';
    const HIDE_IF_EXISTS_TARGET = '2';

    //过滤已转化用户类型
    const HIDE_IF_CONVERTED_NO_EXCLUDE = 'NO_EXCLUDE';
    const HIDE_IF_CONVERTED_AD = 'AD';
    const HIDE_IF_CONVERTED_CAMPAIGN = 'CAMPAIGN';
    const HIDE_IF_CONVERTED_ADVERTISER = 'ADVERTISER';
    const HIDE_IF_CONVERTED_APP = 'APP';
    const HIDE_IF_CONVERTED_CUSTOMER = 'CUSTOMER';

    //是否启用智能放量
    const AUTO_EXTEND_ENABLED_TRUE = '1';
    const AUTO_EXTEND_ENABLED_FALSE = '0';

    //投放场景(出价方式)
    const SMART_BID_TYPE_CUSTOM = 'SMART_BID_CUSTOM';//常规
    const SMART_BID_TYPE_CONSERVATIVE = 'SMART_BID_CONSERVATIVE';//放量
    const SMART_BID_TYPE_NO_BID = 'SMART_BID_NO_BID';//放量

    //是否调整自动出价
    const ADJUST_CPA_TRUE = 1;
    const ADJUST_CPA_FALSE = 0;

    //竞价策略(投放方式)
    const FLOW_CONTROL_MODE_FAST = 'FLOW_CONTROL_MODE_FAST';         //优先跑量（对应CPC的加速投放）
    const FLOW_CONTROL_MODE_SMOOTH = 'FLOW_CONTROL_MODE_SMOOTH';       //优先低成本（对应CPC的标准投放）
    const FLOW_CONTROL_MODE_BALANCE = 'FLOW_CONTROL_MODE_BALANCE';      //均衡投放
    const FLOW_CONTROL_MODE_TWO_PHASES = 'FLOW_CONTROL_MODE_TWO_PHASES';   //两阶段投放

    //预算类型
    const BUDGET_MODE_DAY = 'BUDGET_MODE_DAY';
    const BUDGET_MODE_TOTAL = 'BUDGET_MODE_TOTAL';
    const BUDGET_MODE_INFINITE = 'BUDGET_MODE_INFINITE';

    //付费方式
    const PRICING_CPC = 'PRICING_CPC';
    const PRICING_CPM = 'PRICING_CPM';
    const PRICING_OCPC = 'PRICING_OCPC';
    const PRICING_OCPM = 'PRICING_OCPM';
    const PRICING_CPV = 'PRICING_CPV';
    const PRICING_CPA = 'PRICING_CPA';
    const PRICING_CPC_OCPM = 'PRICING_CPC_OCPM';

    //数据发送方式
    const TRACK_URL_SEND_TYPE_SERVER = 'SERVER_SEND';
    const TRACK_URL_SEND_TYPE_CLIENT = 'CLIENT_SEND';


    //地域类型
    const DISTRICT_NONE = 'NONE';
    const DISTRICT_CITY = 'CITY';
    const DISTRICT_COUNTY = 'COUNTY';

    //游戏类型
    const GAME_TYPE_ANDROID = '安卓';
    const GAME_TYPE_IOS = 'IOS';

    //运营商
    const CARRIER_CUSTOM = 'zidingyi';
    const CARRIER_NONE = 'buxian';

    //新用户(新用户使用头条的时间)
    const ACTIVATE_TYPE_NONE = 'buxian';
    const ACTIVATE_TYPE_CUSTOM = 'zidingyi';

    //搜索快投关键词功能
    const SEARCH_KEYWORD_TYPE = 'feed_delivery_search';

    //人群包
    const PEOPLE_PACKAGE_NONE = 'buxian';

    //首选投放位置
    const INVENTORY_TYPE_UNION = 'INVENTORY_UNION_SLOT';//穿山甲
    const INVENTORY_AWEME_FEED = 'INVENTORY_AWEME_FEED';//抖音信息流

    //投放时间类型
    const SCHEDULE_TYPE_SCHEDULE_FROM_NOW = 'SCHEDULE_FROM_NOW';
    const SCHEDULE_TYPE_SCHEDULE_START_END = 'SCHEDULE_START_END';

    //投放场景
    const SMART_BID_TYPE_SMART_BID_CUSTOM = 'SMART_BID_CUSTOM';
    const SMART_BID_TYPE_SMART_BID_CONSERVATIVE = 'SMART_BID_CONSERVATIVE';

    //深度优化方式
    const DEEP_BID_TYPE_DEFAULT = 'DEEP_BID_DEFAULT';//无深度
    const DEEP_BID_TYPE_PACING = 'DEEP_BID_PACING';//深度自动优化
    const DEEP_BID_TYPE_DEEP_BID_MIN = 'DEEP_BID_MIN';//自定义双出价
    const DEEP_BID_TYPE_ROI_COEFFICIENT = 'ROI_COEFFICIENT';//ROI系数
    const DEEP_BID_TYPE_ROI_COEFFICIENT_7 = 'ROI_COEFFICIENT_7';//7日ROI系数
    const DEEP_BID_TYPE_ROI_PACING = 'ROI_PACING';//ROI自动
    const DEEP_BID_TYPE_BID_PER_ACTION = 'BID_PER_ACTION';//每次付费出价
    const DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI = 'FIRST_AND_SEVEN_PAY_ROI';//每次付费出价
    const DEEP_BID_TYPE_PER_AND_SEVEN_PAY_ROI = 'PER_AND_SEVEN_PAY_ROI';//每次付费出价

    //推广目的
    const LANDING_TYPE_APP = 'APP';    //app
    const LANDING_TYPE_LINK = 'LINK';    //link
    const LANDING_TYPE_LIVE = 'LIVE';   //直播
    const LANDING_TYPE_MICRO_GAME = 'MICRO_GAME';   //小游戏

    const PROMOTION_TYPE_APP = 'APP';    //app
    const PROMOTION_TYPE_LINK = 'LINK';    //link
    const PROMOTION_TYPE_LIVE = 'LIVE';   //直播
    const PROMOTION_TYPE_GAME = 'GAME';   //小手柄
    const PROMOTION_TYPE_HOT = 'HOT';   //原生加热

    //素材类型
    const IMAGE_MODE_CREATIVE_IMAGE_MODE_AWEME_LIVE = 'CREATIVE_IMAGE_MODE_AWEME_LIVE';

    //动态词包
    const WORD_MAP = [
        '地点' => '4',
        '日期' => '1727',
        '星期' => '1736',
        '年龄' => '1737',
        '行业' => '1750',
        '家用电器' => '1784',
        '考试' => '1785',
        '用餐类型' => '4321',
        '省份' => '4323',
        '运营商' => '4324',
        '男人女人' => '5124',
        '月份' => '5127',
        '节日' => '5128',
        '手机系统' => '5130',
        '区县' => '5700',
    ];


    //直播转化类型,写死
    const AD_CONVERT_TYPE_LIVE_COMPONENT_CLICK = 'AD_CONVERT_TYPE_LIVE_COMPONENT_CLICK';
    const AD_CONVERT_TYPE_LIVE_ENTER_ACTION = 'AD_CONVERT_TYPE_LIVE_ENTER_ACTION';
    const AD_CONVERT_TYPE_LIVE_STAY_TIME = 'AD_CONVERT_TYPE_LIVE_STAY_TIME';
    const AD_CONVERT_TYPE_ACTIVE = 'AD_CONVERT_TYPE_ACTIVE';
    const AD_CONVERT_TYPE_PAY = 'AD_CONVERT_TYPE_PAY';

    //roi转化类型
    const AD_CONVERT_TYPE_PURCHASE_ROI = 'AD_CONVERT_TYPE_PURCHASE_ROI';
    const AD_CONVERT_TYPE_PURCHASE_ROI_7D = 'AD_CONVERT_TYPE_PURCHASE_ROI_7D';

    //直播转化id,写死
    const CONVERT_ID_LIVE_COMPONENT_CLICK = 215;
    const CONVERT_ID_LIVE_ENTER_ACTION = 171;
    const CONVERT_ID_LIVE_STAY_TIME = 186;
    const CONVERT_ID_ACTIVE = 8;
    const CONVERT_ID_PAY = 14;

    //云游戏
    const CLOUD_GAME = 'CLOUD_GAME';

    //投放场景
    const INVENTORY_MODE_SCENE = 'scene_inventory';

    //继承账户类型
    const AUTO_INHERIT_SWITCH_ACCOUNT = 'INHERIT_FROM_ACCOUNT';
    const AUTO_INHERIT_SWITCH_CUSTOMER = 'INHERIT_FROM_CUSTOMER';

    //子目标
    const APP_PROMOTION_TYPE_DOWNLOAD = 'DOWNLOAD';
    const APP_PROMOTION_TYPE_LAUNCH = 'LAUNCH';
    const APP_PROMOTION_TYPE_RESERVE = 'RESERVE';

    // 出价方式
    const CPA_BID_MODE_NORMAL = 0;
    const CPA_BID_MODE_SCOPE = 1;
    const ROI_GOAL_MODE_NORMAL = 0;
    const ROI_GOAL_MODE_SCOPE = 1;

    const ORIGIN_AD_TYPE_ORIGIN = 'origin';
    const ORIGIN_AD_TYPE_DEFAULT = 'default';
    const ORIGIN_AD_TYPE_HOT = 'hot';
    const ORIGIN_AD_TYPE_STAR = 'star';

    //落地页类型 1=独立落地页 0=共享 2=多选落地页
    const DOWNLOAD_URL_TYPE_ALONE_MORE = 2;
    const DOWNLOAD_URL_TYPE_ALONE = 1;
    const DOWNLOAD_URL_TYPE_SHARE = 0;

    //原生锚点
    const ANCHOR_RELATED_TYPE_OFF = 'OFF'; // 不启用
    const ANCHOR_RELATED_TYPE_AUTO = 'AUTO';  // 自动
    const ANCHOR_RELATED_TYPE_SELECT = 'SELECT'; //手动
    const ANCHOR_TYPE_APP_GAME = 'APP_GAME'; //锚点类型  应用下载-游戏：APP_GAME

    const BUDGET_OPTIMIZE_SWITCH_OFF = 'OFF';
    const BUDGET_OPTIMIZE_SWITCH_ON = 'ON';

    const NO_BID = 'NO_BID';

    const BUDGET_MODE_PROCEDURAL = 'PROCEDURAL';
    const BUDGET_MODE_MANUAL = 'MANUAL';

    // 营销场景
    const MARKETING_GOAL_VIDEO_AND_IMAGE = 'VIDEO_AND_IMAGE';
    const MARKETING_GOAL_LIVE = 'LIVE';
    const AD_TYPE = 'ALL';
    const LIVE_MATERIALS = 'LIVE_MATERIALS';
    const PROMOTION_MATERIALS = 'PROMOTION_MATERIALS';
}

