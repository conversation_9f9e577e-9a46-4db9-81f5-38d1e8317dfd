<?php
/**
 * Created by PhpStorm.
 * User: lzh
 * Date: 2022/4/26
 * Time: 15:02
 */

namespace App\Constant;

class reportMonitorSqlMap
{
    //上报监控sql
    const REPORT_SQL = 'WITH ad AS (
	SELECT
		adgroup_id ,game_name,root_game_name
	FROM
		tanwan_datamedia.ods_tencent_adgroup_log hd
		INNER JOIN tanwan_datahub.v2_dim_agent_site_id ags ON hd.platform = ags.platform 
		AND hd.site_id = ags.site_id
		INNER JOIN tanwan_datamedia.dim_site_game_id sg ON hd.platform = sg.platform 
		AND hd.site_id = sg.site_id
		INNER JOIN tanwan_datahub.v2_dim_site_id s ON hd.platform = s.platform 
		AND hd.site_id = s.site_id
		INNER JOIN tanwan_datahub.v2_dim_game_id game ON hd.platform = game.platform 
		AND sg.game_id = game.game_id 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
		AND plat_id != 7 
		AND game_end_time > CURRENT_DATE 
		AND (
			hd.platform IN (  "flamingo", "43wan", "quwan", "shunwan" ) 
			OR ( hd.platform = "TW" AND ags.media_type_id = 2 ) 
			OR ( hd.platform = "TW" AND ags.media_type_id = 4 ) 
			OR ( hd.platform = "TW" AND ags.media_type_id = 164 AND ags.agent_id = 150036 ) 
			OR (
				hd.platform = "TW" 
				AND ags.media_type_id = 3 
				AND (
					ags.agent_id IN ( 157589, 154323 ) 
				OR ( ags.agent_id >= 159650 ))) 
			OR (
				hd.platform = "TW" 
			AND ags.media_type_id IN ( 163, 6 )) 
			OR ( ags.media_type_id = 1 AND s.convert_toolkit = "TOUTIAO_ASSET" ) -- 头条事件管理目前全平台由我们上报
		) 
		AND s.convert_source_type IN ( "AD_CONVERT_SOURCE_TYPE_APP_DOWNLOAD" ) 
	),
	current_hd AS (
	SELECT
		2 AS media_type_id,
		hd.platform,
		hd.game_id,
		game_name,root_game_name,
		hd.account_id,
		agent_leader,
		campaign_id,
		hd.adgroup_id,
		ad_id AS adcre_id,
		sum( activated_count ) AS activated_count,
		sum( first_day_pay_count ) AS first_day_pay_count,
		sum( first_day_pay_amount ) AS first_day_pay_amount,
		sum(first_day_first_pay_count) as first_day_first_pay_count,
		sum( ori_cost ) AS ori_cost,

		ifnull(
			sum( ori_cost )/ sum( activated_count ),
		sum( ori_cost )) AS current_cpa,
		ifnull(
			sum( ori_cost )/ sum( first_day_pay_count ),
		sum( ori_cost )) AS current_cps,
		sum( first_day_pay_amount )/ sum( ori_cost ) AS first_roi 
	FROM
		tanwan_datamedia.ods_tencent_ad_hour_data_log hd
		INNER JOIN ad ON ad.adgroup_id = hd.adgroup_id
		INNER JOIN tanwan_datahub.v2_dim_agent_site_id ags ON hd.platform = ags.platform 
		AND hd.site_id = ags.site_id 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
		AND cost_date = CURRENT_DATE 
		AND DATE_FORMAT( cost_date_hour, "%H" ) <= DATE_FORMAT( CURRENT_TIMESTAMP, "%H" ) 
	GROUP BY
		media_type_id,
		hd.platform,
		hd.game_id 
	),
	his_hd AS (
	SELECT
		2 AS media_type_id,
		hd.platform,
		hd.game_id,
		hd.account_id,-- 		agent_leader,
		campaign_id,
		hd.adgroup_id,
		ad_id AS adcre_id,
		count(DISTINCT cost_date,if(cost>0,true,null)) date_count,
		sum( activated_count ) AS activated_count,
		sum( first_day_pay_count ) AS first_day_pay_count,
		sum( first_day_pay_amount ) AS first_day_pay_amount,
		sum( ori_cost ) AS ori_cost,
		ifnull(
			sum( ori_cost )/ sum( activated_count ),
		sum( ori_cost )) AS his_cpa,
		ifnull(
			sum( ori_cost )/ sum( first_day_pay_count ),
		sum( ori_cost )) AS his_cps,
		sum( first_day_pay_amount )/ sum( ori_cost ) AS first_roi 
	FROM
		tanwan_datamedia.ods_tencent_ad_hour_data_log hd
		INNER JOIN ad ON ad.adgroup_id = hd.adgroup_id
		INNER JOIN tanwan_datahub.v2_dim_agent_site_id ags ON hd.platform = ags.platform 
		AND hd.site_id = ags.site_id 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
		AND cost_date BETWEEN CURRENT_DATE - INTERVAL 11 DAY 		AND CURRENT_DATE - INTERVAL 1 DAY 
		and cost_date not BETWEEN "2022-04-13" and "2022-04-17"

		AND DATE_FORMAT( cost_date_hour, "%H" ) <= DATE_FORMAT( CURRENT_TIMESTAMP, "%H" ) 
	GROUP BY
		media_type_id,
		hd.platform,
		hd.game_id 
	),
	reg AS (
	SELECT
		2 AS media_type_id,
		platform,
		game_id,
		root_game_name,
		game_name,
		count( DISTINCT uid ) AS activated_count,
		count(
			DISTINCT uid,
		IF
		( first_day_pay_money > 0, TRUE, NULL )) AS first_day_first_pay_count,
		sum(first_day_pay_times) as first_day_pay_count,
		sum( first_day_pay_money ) AS first_day_pay_amount 
	FROM
		tanwan_datahub.v2_dwd_game_uid_reg_log reg
		INNER JOIN ad ON reg.adgroup_id = ad.adgroup_id 
	WHERE
		game_reg_time > CURRENT_DATE 
	GROUP BY
		media_type_id,
		platform,
		game_id 
	) 
	SELECT 
	media_type_id,platform,game_id,reg.game_name,reg.root_game_name,current_hd.ori_cost,
	(current_hd.ori_cost-his_hd.ori_cost/date_count)/(his_hd.ori_cost/date_count) 消耗环比增加,
	(current_cpa-his_cpa)/his_cpa cpa环比增加,
	(current_cps-his_cps)/his_cps cps环比增加,
	(reg.activated_count - current_hd.activated_count)/reg.activated_count 激活数对比媒体,
	(reg.first_day_first_pay_count - current_hd.first_day_first_pay_count)/reg.first_day_pay_count 首日付费人数对比媒体,
	(reg.first_day_pay_count - current_hd.first_day_pay_count)/reg.first_day_pay_count 首日付费次数对比媒体
FROM
	current_hd
	LEFT JOIN his_hd USING ( platform, game_id, media_type_id )
	FULL OUTER JOIN reg USING ( platform, game_id, media_type_id ) 
WHERE
current_hd.ori_cost> 10000
	ORDER BY 
	current_hd.ori_cost asc
	LIMIT 100';

    const IOS_MATCH_SQL = 'WITH his_matched AS (
	SELECT
		platform,
		game_id,
		game_name,
		game.os,
		count( 1 ) / count( DISTINCT date( game_reg_time ) ) his_cnt,
		count( IF ( matched != 0, TRUE, NULL ) ) / count( DISTINCT date( game_reg_time ) ) his_matched_cnt,
		count( IF ( matched != 0, TRUE, NULL ) ) / count( 1 ) his_matched_ratio 
	FROM
		tanwan_datahub.v2_dwd_game_uid_reg_source_log source
		INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
	WHERE
		game_reg_time BETWEEN CURRENT_TIMESTAMP - INTERVAL 5 DAY 
		AND CURRENT_TIMESTAMP - INTERVAL 1 DAY 
		AND game.os = "IOS" 
	GROUP BY
		platform,
		game_id 
	),
	realtime_matched AS (
	SELECT
		platform,
		game_id,
		game_name,
		game.os,
		count( 1 ) realtime_cnt,
		count( IF ( matched != 0, TRUE, NULL ) ) / count( 1 ) realtime_matched_ratio 
	FROM
		tanwan_datahub.v2_dwd_game_uid_reg_source_log source
		INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
	WHERE
		game_reg_time BETWEEN CURRENT_TIMESTAMP - INTERVAL 120 MINUTE 
		AND CURRENT_TIMESTAMP - INTERVAL 20 MINUTE 
		AND game.os = "IOS" 
	GROUP BY
		platform,
		game_id 
	),
	today_matched AS (
	SELECT
		platform,
		game_id,
		game_name,
		game.os,
		count( 1 ) today_cnt,
		count( IF ( matched != 0, TRUE, NULL ) ) today_matched_cnt,
		count( IF ( matched != 0, TRUE, NULL ) ) / count( 1 ) today_matched_ratio 
	FROM
		tanwan_datahub.v2_dwd_game_uid_reg_source_log source
		INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
	WHERE
		game_reg_time BETWEEN CURRENT_DATE 
		AND CURRENT_TIMESTAMP - INTERVAL 90 MINUTE 
		AND game.os = "IOS" 
	GROUP BY
		platform,
		game_id 
	),
	cost AS (
	SELECT
		platform,
		game_id,
		sum( IF ( tdate = CURRENT_DATE, money, 0 ) ) today_money,
		sum( IF ( tdate != CURRENT_DATE, money, 0 ) ) / count( DISTINCT tdate ) his_money 
	FROM
		tanwan_datahub.v2_dwd_day_cost_log 
	WHERE
		money_type = 1 
		AND tdate >= CURRENT_DATE - INTERVAL 5 DAY 
	GROUP BY
		platform,
		game_id 
	) SELECT
	ifnull( his_matched.game_id, today_matched.game_id ) game_id,
	ifnull( his_matched.platform, today_matched.platform ) platform,
	ifnull( his_matched.game_name, today_matched.game_name ) game_name,
	ifnull( his_matched.os, today_matched.os ) os,
	cast( his_cnt AS INT ) AS his_cnt,
	today_cnt,
	realtime_cnt,
	today_money / today_matched_cnt today_cpa,
	his_money / his_matched_cnt his_cpa,
	cast( his_matched_ratio AS DECIMAL ( 12, 2 ) ) his_matched_ratio,
	cast( today_matched_ratio AS DECIMAL ( 12, 2 ) ) today_matched_ratio,
	cast( realtime_matched_ratio AS DECIMAL ( 12, 2 ) ) realtime_matched_ratio 
FROM
	his_matched
	FULL JOIN today_matched ON his_matched.platform = today_matched.platform 
	AND his_matched.game_id = today_matched.game_id
	LEFT JOIN realtime_matched ON today_matched.platform = realtime_matched.platform 
	AND today_matched.game_id = realtime_matched.game_id
	LEFT JOIN cost ON ifnull( today_matched.platform, his_matched.platform ) = cost.platform 
	AND ifnull( today_matched.game_id, his_matched.game_id ) = cost.game_id 
WHERE
	(
		(
			ifnull( his_matched_ratio, 0 ) / ifnull( today_matched_ratio, 0 ) > 1.15 
			AND ifnull( his_matched_ratio, 0 ) / ifnull( realtime_matched_ratio, 0 ) > 1.3 
			AND ifnull( his_matched_ratio, 0 ) != 0 
		) 
		OR ( realtime_cnt IS NULL AND today_cnt IS NOT NULL ) 
	) 
	AND ( his_cnt > 100 OR today_cnt > 50 ) 
	AND ifnull( his_matched.platform, today_matched.platform ) NOT IN ( "ZW" ) 
HAVING
	today_cpa / ifnull( his_cpa, today_cpa - 1 ) > 1.3 
ORDER BY
	today_cnt ASC 
	LIMIT 100';

    const ACTIVE_REG_SQL = 'WITH ra_ratio_his AS (
	SELECT
		ifnull( reg.platform, act.platform ) platform,
		ifnull( reg.game_id, act.game_id ) game_id,
		media_type_id,
		media_type_name,
		min( start_reg_time ) start_reg_time,
		max( end_reg_time ) end_reg_time,
		ifnull( reg.game_name, act.game_name ) game_name,
		ifnull( reg.root_game_name, act.root_game_name ) root_game_name,
		ifnull( reg.os, act.os ) os,
		sum(reg_sum) reg_sum,
		sum( act_cnt ) his_act_cnt,
		sum( reg_cnt ) his_reg_cnt,
		sum( reg_cnt ) / sum( act_cnt ) his_ratio 
	FROM
		(
		SELECT
			platform,
			min( game_reg_time ) start_reg_time,
			max( game_reg_time ) end_reg_time,
			game_id,
			game_name,
			game.os,
			root_game_name,
			agent_id,
			count(distinct muid,moaid,device_id,ip,ua,idfv) reg_sum,
			count( DISTINCT muid, moaid, device_id, ip, ua, idfv )/ count(
			DISTINCT date( game_reg_time )) reg_cnt 

		FROM
			tanwan_datahub.v2_dwd_game_uid_reg_log
			INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
		WHERE
			game_reg_time BETWEEN CURRENT_DATE - INTERVAL 10 DAY 
		AND CURRENT_DATE and action_id = 2
		GROUP BY
			platform,
			game_id,
			agent_id 
		) AS reg
		FULL JOIN (
		SELECT
			platform,
			game_id,
			game_name,
			root_game_name,
			game.os,
			agent_id,
			count( DISTINCT muid, moaid, device_id, ip, ua, idfv ) / count(
			DISTINCT date( action_time )) act_cnt 
		FROM
			tanwan_datahub.v2_ods_device_action_log
			INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
		WHERE
			action_time BETWEEN CURRENT_DATE - INTERVAL 10 DAY 
		AND CURRENT_DATE 
			AND action_id = 1 
		GROUP BY
			platform,
			game_id,
			agent_id 
		) AS act ON reg.platform = act.platform 
		AND reg.game_id = act.game_id 
		AND reg.agent_id = act.agent_id
		INNER JOIN tanwan_datahub.v2_dim_agent_id agent ON ifnull( reg.platform, act.platform ) = agent.platform 
		AND ifnull( reg.agent_id, act.agent_id ) = agent.agent_id 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
	GROUP BY
		platform,
		game_id,
		media_type_id 

	),
	ra_ratio_today AS (
	SELECT
		ifnull( reg.platform, act.platform ) platform,
		ifnull( reg.game_id, act.game_id ) game_id,
		ifnull( reg.game_name, act.game_name ) game_name,
		ifnull( reg.root_game_name, act.root_game_name ) root_game_name,
		ifnull( reg.os, act.os ) os,
		media_type_id,
		media_type_name,
		act_cnt1,
		reg_cnt1,
		sum( act_cnt ) today_act_cnt,
		sum( reg_cnt ) today_reg_cnt,
		sum( reg_cnt ) / sum( act_cnt ) today_ratio 
	FROM
		(
		SELECT
			platform,
			game_id,
			game_name,
			game.os,
			root_game_name,
			agent_id,
			count( 1 ) reg_cnt1,
			count( DISTINCT muid, moaid, device_id, ip, ua, idfv ) reg_cnt 
		FROM
			tanwan_datahub.v2_dwd_game_uid_reg_log
			INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
		WHERE
			game_reg_time > CURRENT_DATE 
		GROUP BY
			platform,
			game_id,
			agent_id 
		) AS reg
		FULL JOIN (
		SELECT
			platform,
			game_id,
			game_name,
			game.os,
			root_game_name,
			agent_id,
			count( 1 ) act_cnt1,
			count( DISTINCT muid, moaid, device_id, ip, ua, idfv ) act_cnt 
		FROM
			tanwan_datahub.v2_ods_device_action_log
			INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
		WHERE
			action_time > CURRENT_DATE 
			AND action_id = 1 
		GROUP BY
			platform,
			game_id,
			agent_id 
		) AS act ON reg.platform = act.platform 
		AND reg.game_id = act.game_id 
		AND reg.agent_id = act.agent_id
		INNER JOIN tanwan_datahub.v2_dim_agent_id agent ON ifnull( reg.platform, act.platform ) = agent.platform 
		AND ifnull( reg.agent_id, act.agent_id ) = agent.agent_id 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
	GROUP BY
		platform,
		game_id,
		media_type_id

	),
	ra_ratio_realtime AS (
	SELECT
		ifnull( reg.platform, act.platform ) platform,
		ifnull( reg.game_id, act.game_id ) game_id,
		ifnull( reg.game_name, act.game_name ) game_name,
		ifnull( reg.root_game_name, act.root_game_name ) root_game_name,
		ifnull( reg.os, act.os ) os,
		media_type_id,
		media_type_name,
		sum( act_cnt ) realtime_act_cnt,
		sum( reg_cnt ) realtime_reg_cnt,
		sum( reg_cnt ) / sum( act_cnt ) realtime_ratio 
	FROM
		(
		SELECT
			platform,
			game_id,
			game_name,
			root_game_name,
			game.os,
			agent_id,
			count( DISTINCT muid, moaid, device_id, ip, ua, idfv ) reg_cnt 
		FROM
			tanwan_datahub.v2_dwd_game_uid_reg_log
			INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
		WHERE
			game_reg_time BETWEEN CURRENT_TIMESTAMP - INTERVAL 20 MINUTE 
			AND CURRENT_TIMESTAMP - INTERVAL 5 MINUTE 
		GROUP BY
			platform,
			game_id,
			agent_id 
		) AS reg
		FULL JOIN (
		SELECT
			platform,
			game_id,
			game_name,
			root_game_name,
			game.os,
			agent_id,
			count( DISTINCT muid, moaid, device_id, ip, ua, idfv ) act_cnt 
		FROM
			tanwan_datahub.v2_ods_device_action_log
			INNER JOIN tanwan_datahub.v2_dim_game_id game USING ( platform, game_id ) 
		WHERE
			action_time BETWEEN CURRENT_TIMESTAMP - INTERVAL 20 MINUTE 
			AND CURRENT_TIMESTAMP - INTERVAL 3 MINUTE 
			AND action_id = 1 
		GROUP BY
			platform,
			game_id,
			agent_id 
		) AS act ON reg.platform = act.platform 
		AND reg.game_id = act.game_id 
		AND reg.agent_id = act.agent_id
		INNER JOIN tanwan_datahub.v2_dim_agent_id agent ON ifnull( reg.platform, act.platform ) = agent.platform 
		AND ifnull( reg.agent_id, act.agent_id ) = agent.agent_id 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
	GROUP BY
		platform,
		game_id,
		media_type_id 
	),
	cost AS (
	SELECT
		platform,
		game_id,
		agent.media_type_id,
		min( tdate ) start_date,
		max( tdate ) end_date,
		sum(
		IF
		( tdate = CURRENT_DATE, money, 0 )) today_money,
		sum(
		IF
			( tdate != CURRENT_DATE, money, 0 ))/(
			count( DISTINCT tdate )- 1 
		) his_money,
		sum(
		IF
		( tdate != CURRENT_DATE, money, 0 )) his_money_sum 
	FROM
		tanwan_datahub.v2_dwd_day_cost_log
		INNER JOIN tanwan_datahub.v2_dim_agent_id agent USING ( platform, agent_id ) 
	WHERE
		agent_leader_end_time > CURRENT_DATE 
		AND type = 1 
		AND tdate >= CURRENT_DATE - INTERVAL 10 DAY 
	GROUP BY
		platform,
		game_id,
		agent.media_type_id 
	) SELECT
	ifnull( ra_ratio_his.game_id, ra_ratio_today.game_id ) game_id,
	ifnull( ra_ratio_his.platform, ra_ratio_today.platform ) platform,
	ifnull( ra_ratio_his.game_name, ra_ratio_today.game_name ) game_name,
	ifnull( ra_ratio_his.os, ra_ratio_today.os ) os,
	ifnull( ra_ratio_his.media_type_id, ra_ratio_today.media_type_id ) media_type_id,
	ifnull( ra_ratio_his.media_type_name, ra_ratio_today.media_type_name ) media_type_name,
	cast( his_act_cnt AS INT ) his_act_cnt,
	cast( his_reg_cnt AS INT ) his_reg_cnt,
	his_money_sum/reg_sum his_cpa,
	his_money,
	today_money,
	today_money / today_reg_cnt today_cpa,
	today_act_cnt,
	today_reg_cnt,
	realtime_act_cnt,
	realtime_reg_cnt,
	cast(
	his_ratio AS DECIMAL ( 12, 4 )) his_ratio,
	cast(
	today_ratio AS DECIMAL ( 12, 4 )) today_ratio,
	cast(
	realtime_ratio AS DECIMAL ( 12, 4 )) realtime_ratio 
FROM
	ra_ratio_his
	FULL JOIN ra_ratio_today ON ra_ratio_his.platform = ra_ratio_today.platform 
	AND ra_ratio_his.game_id = ra_ratio_today.game_id 
	AND ra_ratio_his.media_type_id = ra_ratio_today.media_type_id
	LEFT JOIN ra_ratio_realtime ON ra_ratio_today.platform = ra_ratio_realtime.platform 
	AND ra_ratio_today.game_id = ra_ratio_realtime.game_id 
	AND ra_ratio_today.media_type_id = ra_ratio_realtime.media_type_id
	LEFT JOIN cost ON ifnull( ra_ratio_his.game_id, ra_ratio_today.game_id ) = cost.game_id 
	AND ifnull( ra_ratio_his.platform, ra_ratio_today.platform ) = cost.platform 
	AND ifnull( ra_ratio_his.media_type_id, ra_ratio_today.media_type_id ) = cost.media_type_id 
WHERE
	abs((
			ifnull(ifnull( his_ratio, 0 )/ ifnull( today_ratio, 0 )- 1 )
		))> 0.5 
	AND abs((
			ifnull(ifnull( his_ratio, 0 )/ ifnull( realtime_ratio, 0 )- 1 )
		))> 0.5 
	AND ( his_act_cnt > 200 OR today_act_cnt > 150 ) 
	AND his_ratio > 0.4 
	and ( his_money is not null or today_money is not null)
HAVING
	( today_cpa / his_cpa > 1.3 OR (today_cpa IS NULL and (his_reg_cnt>300 or today_reg_cnt>200))) 
ORDER BY
	his_money ASC,
	his_act_cnt ASC
	LIMIT 100';
}
