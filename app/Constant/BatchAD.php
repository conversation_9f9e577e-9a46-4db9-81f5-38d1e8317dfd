<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2021/12/27
 * Time: 15:58
 */
namespace App\Constant;

class BatchAD {

    // 广告组合 错误信息类型
    const COMPOSE_ERROR_TYPE_SETTING = 'setting';
    const COMPOSE_ERROR_TYPE_COMPOSE_CONFIG = 'compose_config';
    const COMPOSE_ERROR_TYPE_OTHER_SETTING = 'other_setting';
    const COMPOSE_ERROR_TYPE_MATERIAL = 'material';
    const COMPOSE_ERROR_TYPE_MATERIAL_WARNING = 'material_warning';
    const COMPOSE_ERROR_TYPE_MATERIAL_MEDIA_WARNING = 'material_media_warning';

    const COMPOSE_ERROR_TYPE_IOS_WARNING = 'ios_warning';
    const COMPOSE_ERROR_TYPE_ACCOUNT = 'account';
    const COMPOSE_ERROR_TYPE_TARGETING = 'targeting';
    const COMPOSE_ERROR_TYPE_WORD = 'word';
    const COMPOSE_ERROR_TYPE_TAG = 'tag';

    // 参数包组合方式
    const SETTING_COMPOSE_TYPE_INFO = 0;
    const SETTING_COMPOSE_TYPE_LIST = 1;

    // 定向包组合方式
    const TARGETING_COMPOSE_TYPE_INFO = 0;
    const TARGETING_COMPOSE_TYPE_LIST = 1;

    // 广告类型 (0是信息流)
    const MEDIA_AGENT_TYPE_DEFAULT = 0;
    const MEDIA_AGENT_TYPE_PROJECT = 1;

    // 广告一级的创建方式
    const AD1_CREATE_TYPE_SYSTEM = 0;
    const AD1_CREATE_TYPE_MEDIA = 1;

    // 文案组合方式
    const WORD_COMPOSE_TYPE_RAND = 0;
    const WORD_COMPOSE_TYPE_LIST = 1;

    // 素材组合方式
    const MATERIAL_COMPOSE_TYPE_RAND = 0;
    const MATERIAL_COMPOSE_TYPE_list = 1;

    // 创意类型(程序化 | 自定义)
    const CREATIVE_PROGRAM_MODE = 1;
    const CREATIVE_CUSTOM_MODE = 2;

    // 组合方式
    const COMPOSE_TYPE_COMMON = 'common';

    // 组合分配方式
    const DISTRIBUTE_TYPE = 'average';

    // 文案叉乘类型
    const WORD_TYPE_PRODUCT = 1;
    const WORD_TYPE_NO_PRODUCT = 0;

    // 封面叉乘类型
    const COVER_TYPE_PRODUCT = 1;
    const COVER_TYPE_NO_PRODUCT = 0;

    // 文案素材是否乘积
    const WORD_MATERIAL_COMPOSE_MODE_PRODUCT = 0;
    const WORD_MATERIAL_COMPOSE_MODE_NO_PRODUCT = 1;
}
