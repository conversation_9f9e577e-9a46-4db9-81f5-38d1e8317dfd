<?php

/**
 * 常用的MIME TYPE
 * https://www.iana.org/assignments/media-types/media-types.xhtml
 */

namespace App\Constant;

class MIMEType
{
    const IMAGE_GIF = 'image/gif';
    const IMAGE_JPG = 'image/jpg';
    const IMAGE_JPEG = 'image/jpeg';
    const IMAGE_PJPEG = 'image/pjpeg';
    const IMAGE_X_PNG = 'image/x-png';
    const IMAGE_PNG = 'image/png';
    const IMAGE_BMP = 'image/bmp';
    const VIDEO_MP4 = 'video/mp4';
    const VIDEO_AVI = 'video/avi';
    const VIDEO_MPEG = 'video/mpeg';
    const VIDEO_MPG = 'video/mpg';
    const VIDEO_M4V = 'video/m4v';
    const VIDEO_MOV = 'video/mov';
    const VIDEO_WMV = 'video/wmv';
    const VIDEO_ASX = 'video/asx';
    const AUDIO_MPEG = 'audio/mpeg';
    const AUDIO_WAV = 'audio/wav';
    const AUDIO_M4A = 'audio/m4a';
    const APPLICATION_PDF = 'application/pdf';
    const APPLICATION_ZIP = 'application/zip';
    const APPLICATION_X_ZIP_COMPRESSED = 'application/x-zip-compressed';
    const APPLICATION_VND_MS_EXCEL = 'application/vnd.ms-excel';
    const APPLICATION_OCTET_STREAM = 'application/octet-stream';
    const APPLICATION_TEXT = 'text/plain';

}

