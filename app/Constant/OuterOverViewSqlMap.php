<?php
/**
 * 对外数据分析的SQL总览映射关系
 * User: Melody
 * Date: 2020/6/17
 * Time: 19:33
 */

namespace App\Constant;


class OuterOverViewSqlMap
{
    /**
     * 需要用到click表的指标
     */
    const NEED_CLICK_TABLE = [
        'click_muid_distinct_count_ldy' => 'sum( IF (( t.ad_platform_id = 0 and t.type in (2,4)), t.day_click_muid_distinct_count, 0 ) ) AS click_muid_distinct_count_ldy',
        'click_old_muid_count_ldy' => 'sum( IF ( (t.ad_platform_id = 0 and t.type in (2,4) AND is_old_root_game_muid = 1), t.day_click_count, 0 ) ) AS click_old_muid_count_ldy',
        'click_old_clique_muid_count_ldy' => 'sum( IF ( ((t.ad_platform_id = 0 and t.type in (2,4)) AND ( is_old_clique_game_muid = 1 OR is_old_root_game_muid = 1 )), day_click_count, 0 ) ) AS click_old_clique_muid_count_ldy',
        'click_ip_distinct_count_ldy' => 'sum( IF( (t.ad_platform_id = 0 and t.type in (2,4)), t.day_click_ip_distinct_count, 0 ) ) AS click_ip_distinct_count_ldy',
        'click_muid_distinct_count' => 'sum( IF ( t.type=3 or (t.type=4 and t.ad_platform_id !=0), t.day_click_muid_distinct_count, 0 ) ) AS click_muid_distinct_count',
        'click_old_muid_count' => 'sum( IF ( t.type=3 or (t.type=4 and t.ad_platform_id !=0) AND is_old_root_game_muid = 1, t.day_click_count, 0 ) ) AS click_old_muid_count',
        'click_old_clique_muid_count' => 'sum( IF (  t.type=3 or (t.type=4 and t.ad_platform_id !=0) AND ( is_old_clique_game_muid = 1 OR is_old_root_game_muid = 1 ), day_click_count, 0 ) ) AS click_old_clique_muid_count',
        'click_ip_distinct_count' => 'sum( IF (  t.type=3 or (t.type=4 and t.ad_platform_id !=0), t.day_click_ip_distinct_count, 0 ) ) AS click_ip_distinct_count',
        'click_count_ldy' => 'sum( IF ( (t.ad_platform_id = 0 and t.type in (2,4)), t.day_click_count, 0 ) ) AS click_count_ldy',
        'click_muid_count_ldy' => 'sum( IF (t.ad_platform_id = 0 and t.type in (2,4), t.day_click_muid_count, 0 ) ) AS click_muid_count_ldy',
        'click_ip_count_ldy' => 'sum(IF( (t.ad_platform_id = 0 and t.type in (2,4)), t.day_click_ip_count, 0 )) AS click_ip_count_ldy',
        'click_count' => 'sum( IF ( t.type =3 or (t.type=4 and t.ad_platform_id !=0), t.day_click_count, 0 ) ) AS click_count',
        'click_muid_count' => 'sum( IF (  t.type=3 or (t.type=4 and t.ad_platform_id !=0) , t.day_click_muid_count, 0 ) ) AS click_muid_count',
        'click_ip_count' => 'sum(IF( t.type=3 or (t.type=4 and t.ad_platform_id !=0), t.day_click_ip_count, 0 )) AS click_ip_count',
        'click_count_yeyou_show' => 'sum( IF ( t.type=1, t.day_click_count, 0 ) ) AS click_count_yeyou_show',
        'click_ip_distinct_count_yeyou_show' => 'sum( IF  ( t.type=1, t.day_click_ip_distinct_count, 0 ) ) AS click_ip_distinct_count_yeyou_show',
        'click_ip_count_yeyou_show' => 'sum(IF(  t.type=1, t.day_click_ip_count, 0 )) AS click_ip_count_yeyou_show',
        'click_muid_distinct_count_yeyou_show' => 'sum( IF (  t.type=1, t.day_click_muid_distinct_count, 0 ) ) AS click_muid_distinct_count_yeyou_show',
        'click_old_muid_count_yeyou_show' => 'sum( IF ( t.type=1 AND is_old_root_game_muid = 1, t.day_click_count, 0  )) AS click_old_muid_count_yeyou_show',
        'click_old_clique_muid_count_yeyou_show' => 'sum( IF ( t.type=1 AND ( is_old_clique_game_muid = 1 OR is_old_root_game_muid = 1 ), day_click_count, 0 ) ) AS click_old_clique_muid_count_yeyou_show',
        'click_muid_count_yeyou_show' => 'sum( IF ( t.type=1, t.day_click_muid_count, 0 ) ) AS click_muid_count_yeyou_show',
    ];

    /**
     * 需要用到action表的指标
     */
    const NEED_ACTION_TABLE = [
        'action_muid_distinct_count' => 'sum( t.day_action_muid_distinct_count ) AS action_muid_distinct_count',
        'action_ip_distinct_count' => 'sum( t.day_action_ip_distinct_count ) AS action_ip_distinct_count',
        'action_old_muid_count' => 'sum( if(t.is_old_root_game_muid=1,t.day_action_count,0) ) AS action_old_muid_count',
        'action_old_clique_muid_count' => 'sum( if(t.is_old_clique_game_muid=1 OR t.is_old_root_game_muid=1,t.day_action_count,0) ) AS action_old_clique_muid_count',
        'action_count' => 'sum( t.day_action_count ) AS action_count',
        'action_muid_count' => 'sum( t.day_action_muid_count ) AS action_muid_count',
        'action_ip_count' => 'sum( t.day_action_ip_count ) AS action_ip_count',
    ];

    /**
     * 需要用到reg表的指标
     */
    const NEED_REG_TABLE = [
        'reg_muid_count' => 'sum( day_reg_muid_count ) AS reg_muid_count',
        'reg_ip_count' => 'sum( day_reg_ip_count ) AS reg_ip_count',
        'reg_old_muid_count' => 'sum( if(is_old_root_game_muid=1,day_reg_uid_count,0) ) AS reg_old_muid_count',
        'reg_old_clique_muid_count' => 'sum( if(is_old_clique_game_muid=1 OR is_old_root_game_muid=1,day_reg_uid_count,0) ) AS reg_old_clique_muid_count',
        'role_create_count' => 'sum( is_create_role ) AS role_create_count',
        'first_day_role_login_count' => 'sum( is_first_day_role_login_count ) AS first_day_role_login_count',
        'reg_uid_count' => 'count( 1 ) AS reg_uid_count',
        'reg_muid_distinct_count' => 'sum( day_reg_muid_distinct_count ) AS reg_muid_distinct_count',
        'reg_ip_distinct_count' => 'sum( day_reg_ip_distinct_count ) AS reg_ip_distinct_count',
        'second_login_count' => 'sum( is_second_login ) AS second_login_count',
        'third_login_count' => 'sum( is_third_login ) AS third_login_count',
        'seventh_login_count' => 'sum( is_seventh_login ) AS seventh_login_count',
        'fifteenth_login_count' => 'sum( is_fifteenth_login ) AS fifteenth_login_count',
        'reg_total_pay_count' => 'sum( IF ( t.total_pay_money > 0, 1, 0 ) ) AS reg_total_pay_count',
        //  'reg_total_pay_money' => 'sum( t.total_pay_money ) AS reg_total_pay_money',
        'day_main_uid_create_count' => 'sum( day_main_uid_create_count ) AS day_main_uid_create_count',
        'create_role_uid_count' => 'count( DISTINCT t.platform, t.game_id, t.uid ) AS create_role_uid_count',
        'day_three_login_uid_count' => 'sum( t.day_three_login_uid_count ) AS day_three_login_uid_count',
        'day_three_login_muid_distinct_count' => 'sum( t.day_three_login_muid_distinct_count ) AS day_three_login_muid_distinct_count',
        // 'reg_first_day_pay_money' => 'sum( t.first_day_pay_money ) AS reg_first_day_pay_money',
       //  'reg_first_day_pay_uid_count' => 'sum( IF(t.first_day_pay_money>0,1,0) ) AS reg_first_day_pay_uid_count',
    ];

    /**
     * 需要用到old表的指标
     */
    const NEED_OLD_TABLE = [
        'uid_count' => 'count(DISTINCT t.platform,t.game_id,t.uid) AS uid_count',
        'old_uid_count' => 'count(DISTINCT t.platform,t.game_id,t.uid) AS old_uid_count',
        'old_uid_pay_count' => 'count(DISTINCT t.platform,t.game_id,t.uid) AS old_uid_pay_count',
    ];

    /**
     * 需要用到pay表的指标 按子
     */
    const NEED_PAY_TABLE = [
        'old_uid_pay_money' => 'sum(IF(game_reg_date<YEAR_MONTH(pay_date),day_pay_money,0)) AS old_uid_pay_money',
        'first_day_pay_count' => 'count(distinct reg.platform,reg.game_id,reg.uid, IF ( date(reg.game_reg_time) = date(t.pay_time), true, null ) ) AS first_day_pay_count',
        'total_pay_money' => 'sum(pay_money) AS total_pay_money',
        'exclude_channel_fee_money'   => 'sum(pay_money *  ( 1-site.channel_fee_rate )) AS exclude_channel_fee_money',
        'reg_first_day_pay_money' => ' sum( IF( date( reg.game_reg_time ) = date( t.pay_time ), t.pay_money, 0 )  ) AS reg_first_day_pay_money',
        'reg_total_pay_money' => 'sum(sum( t.pay_money )) over(PARTITION by `reg`.`platform`,`agent`.`agent_group_id`,`agent`.`agent_id` ) AS reg_total_pay_money',
        'reg_first_day_pay_uid_count' => 'count(DISTINCT reg.platform,reg.game_id,reg.uid,IF( date( reg.game_reg_time ) = date( t.pay_time ), TRUE, NULL )) AS reg_first_day_pay_uid_count',
    ];

    /**
     * 需要用到pay表的指标 按根
     */
    const ROOT_GAME_NEED_PAY_TABLE = [
        'old_uid_pay_money' => 'sum(IF(game_reg_date<YEAR_MONTH(pay_date),day_pay_money,0)) AS old_uid_pay_money',
        'first_day_pay_count' => 'count(distinct reg.platform,reg.root_game_id,reg.uid, IF ( date(reg.root_game_reg_time) = date(t.pay_time), true, null ) ) AS first_day_pay_count',
        'total_pay_money' => 'sum(pay_money) AS total_pay_money',
        'exclude_channel_fee_money'   => 'sum(pay_money *  ( 1-site.channel_fee_rate )) AS exclude_channel_fee_money',
        'reg_first_day_pay_money' => ' sum( IF( date( reg.root_game_reg_time ) = date( t.pay_time ), t.pay_money, 0 )  ) AS reg_first_day_pay_money',
        'reg_total_pay_money' => 'sum(sum( t.pay_money )) over(PARTITION by `reg`.`platform`,`agent`.`agent_group_id`,`agent`.`agent_id` ) AS reg_total_pay_money',
        'reg_first_day_pay_uid_count' => 'count(DISTINCT reg.platform,reg.root_game_id,reg.uid,IF( date( reg.root_game_reg_time ) = date( t.pay_time ), TRUE, NULL )) AS reg_first_day_pay_uid_count',
    ];

    /**
     * 需要用到pay表的指标 按平台
     */
    const PLAT_GAME_NEED_PAY_TABLE = [
        'old_uid_pay_money' => 'sum(IF(game_reg_date<YEAR_MONTH(pay_date),day_pay_money,0)) AS old_uid_pay_money',
        'first_day_pay_count' => 'count(distinct reg.platform,reg.uid, IF ( date(reg.reg_time) = date(t.pay_time), true, null ) ) AS first_day_pay_count',
        'total_pay_money' => 'sum(pay_money) AS total_pay_money',
        'exclude_channel_fee_money'   => 'sum(pay_money *  ( 1-site.channel_fee_rate )) AS exclude_channel_fee_money',
        'reg_first_day_pay_money' => ' sum( IF( date( reg.reg_time ) = date( t.pay_time ), t.pay_money, 0 )  ) AS reg_first_day_pay_money',
        'reg_total_pay_money' => 'sum(sum( t.pay_money )) over(PARTITION by `reg`.`platform`,`agent`.`agent_group_id`,`agent`.`agent_id` ) AS reg_total_pay_money',
        'reg_first_day_pay_uid_count' => 'count(DISTINCT reg.platform,reg.uid,IF( date( reg.reg_time ) = date( t.pay_time ), TRUE, NULL )) AS reg_first_day_pay_uid_count',
    ];

    /**
     * 所有指标
     */
    const ALL_TARGET_MAP = [
        'click_ip_ldy_same_percent' => ['click_ip_ldy_same_percent', 'click_ip_distinct_count_ldy', 'click_ip_count_ldy'],
        'click_muid_ldy_same_percent' => ['click_muid_ldy_same_percent', 'click_muid_distinct_count_ldy', 'click_muid_count_ldy'],
        'click_old_muid_count_ldy_percent' => ['click_old_muid_count_ldy_percent', 'click_old_muid_count_ldy', 'click_count_ldy'],
        'click_ip_same_percent' => ['click_ip_same_percent', 'click_ip_distinct_count', 'click_ip_count'],
        'click_muid_same_percent' => ['click_muid_same_percent', 'click_muid_distinct_count', 'click_muid_count'],
        'click_old_muid_count_percent' => ['click_old_muid_count_percent', 'click_old_muid_count', 'click_count'],
        'action_ip_same_percent' => ['action_ip_same_percent', 'action_ip_distinct_count', 'action_ip_count'],
        'action_muid_same_percent' => ['action_muid_same_percent', 'action_muid_distinct_count', 'action_muid_count'],
        'action_old_muid_count_percent' => ['action_old_muid_count_percent', 'action_old_muid_count', 'action_count'],
        'reg_ip_same_percent' => ['reg_ip_same_percent', 'reg_ip_distinct_count', 'reg_ip_count'],
        'reg_muid_same_percent' => ['reg_muid_same_percent', 'reg_muid_distinct_count', 'reg_muid_count'],
        'reg_old_muid_count_percent' => ['reg_old_muid_count_percent', 'reg_old_muid_count', 'reg_uid_count'],
        'role_create_count_percent' => ['role_create_count_percent', 'role_create_count', 'reg_uid_count'],
        'first_day_role_login_count_percent' => ['first_day_role_login_count_percent', 'first_day_role_login_count', 'reg_uid_count'],
        'cost_per_reg' => ['cost_per_reg', 'cost_money', 'reg_uid_count'],
        'cost_per_pay' => ['cost_per_pay', 'cost_money', 'reg_first_day_pay_uid_count'],
        'cost_per_second_login' => ['cost_per_second_login', 'cost_money', 'second_login_count'],
        'cost_per_first_day_pay' => ['cost_per_first_day_pay', 'cost_money', 'first_day_pay_count'],
        'first_day_roi' => ['first_day_roi', 'reg_first_day_pay_money', 'cost_money'],
        'reg_first_day_pay_money' => ['reg_first_day_pay_money'],
        'roi' => ['roi', 'reg_total_pay_money', 'cost_money'],
        'first_day_pay_percent' => ['first_day_pay_percent', 'first_day_pay_count', 'reg_uid_count'],
        'first_day_arppu' => ['first_day_arppu', 'total_pay_money', 'old_uid_pay_money', 'first_day_pay_count'],
        'first_day_ltv' => ['first_day_ltv', 'total_pay_money', 'old_uid_pay_money', 'reg_uid_count'],
        'second_login_percent' => ['second_login_percent', 'second_login_count', 'reg_uid_count'],
        'third_login_percent' => ['third_login_percent', 'third_login_count', 'reg_uid_count'],
        'seventh_login_percent' => ['seventh_login_percent', 'seventh_login_count', 'reg_uid_count'],
        'fifteenth_login_percent' => ['fifteenth_login_percent', 'fifteenth_login_count', 'reg_uid_count'],
        'arppu' => ['arppu', 'reg_total_pay_money', 'reg_total_pay_count'],
        'ltv' => ['ltv', 'reg_total_pay_money', 'reg_uid_count'],
        'total_pay_percent' => ['total_pay_percent', 'reg_total_pay_count', 'reg_uid_count'],
        'range_new_arpu' => ['range_new_arpu', 'total_pay_money', 'old_uid_pay_money', 'reg_uid_count'],
        'range_new_arppu' => ['range_new_arppu', 'total_pay_money', 'old_uid_pay_money', 'first_day_pay_count'],
        'reg_uid_new_pay_percent' => ['reg_uid_new_pay_percent', 'first_day_pay_count', 'reg_uid_count'],
        'range_old_arpu' => ['range_old_arpu', 'old_uid_pay_money', 'old_uid_count'],
        'range_old_arppu' => ['range_old_arppu', 'old_uid_pay_money', 'old_uid_pay_count'],
        'old_uid_pay_percent' => ['old_uid_pay_percent', 'old_uid_pay_count', 'old_uid_count'],
        'range_arpu' => ['range_arpu', 'total_pay_money', 'uid_count'],
        'range_arppu' => ['range_arppu', 'total_pay_money', 'old_uid_pay_count', 'first_day_pay_count'],
        'range_pay_count' => ['range_pay_count', 'old_uid_pay_count', 'first_day_pay_count'],
        'reg_uid_new_pay_money' => ['reg_uid_new_pay_money', 'total_pay_money', 'old_uid_pay_money'],
        'range_uid_pay_percent' => ['range_uid_pay_percent', 'first_day_pay_count', 'old_uid_pay_count', 'uid_count'],
        'click_ldy_action_percent' => ['click_ldy_action_percent', 'action_count', 'click_count_ldy'],
        'click_action_percent' => ['click_action_percent', 'action_count', 'click_count'],
        'click_ldy_uid_reg_percent' => ['click_ldy_uid_reg_percent', 'reg_uid_count', 'click_count_ldy'],
        'click_uid_reg_percent' => ['click_uid_reg_percent', 'reg_uid_count', 'click_count'],
        'click_ldy_role_create_percent' => ['click_ldy_role_create_percent', 'role_create_count', 'click_count_ldy'],
        'click_role_create_percent' => ['click_role_create_percent', 'role_create_count', 'click_count'],
        'action_uid_reg_percent' => ['action_uid_reg_percent', 'reg_uid_count', 'action_count'],
        'action_role_create_percent' => ['action_role_create_percent', 'role_create_count', 'action_count'],
        'click_old_clique_muid_count_percent' => ['click_old_clique_muid_count_percent', 'click_old_clique_muid_count', 'click_count'],
        'action_old_clique_muid_count_percent' => ['action_old_clique_muid_count_percent', 'action_old_clique_muid_count', 'action_count'],
        'click_old_clique_muid_count_ldy_percent' => ['click_old_clique_muid_count_ldy_percent', 'click_old_clique_muid_count_ldy', 'click_count_ldy'],
        'reg_old_clique_muid_count_percent' => ['reg_old_clique_muid_count_percent', 'reg_old_clique_muid_count', 'reg_uid_count'],
        'action_reg_rate' => ['reg_uid_count', 'action_count', 'action_reg_rate'],
        'click_count_ldy' => ['click_count_ldy'],
        'click_ip_count_ldy' => ['click_ip_count_ldy'],
        'click_ip_distinct_count_ldy' => ['click_ip_distinct_count_ldy'],
        'click_muid_count_ldy' => ['click_muid_count_ldy'],
        'click_muid_distinct_count_ldy' => ['click_muid_distinct_count_ldy'],
        'click_old_muid_count_ldy' => ['click_old_muid_count_ldy'],
        'click_count' => ['click_count'],
        'click_ip_count' => ['click_ip_count'],
        'click_ip_distinct_count' => ['click_ip_distinct_count'],
        'click_muid_count' => ['click_muid_count'],
        'click_muid_distinct_count' => ['click_muid_distinct_count'],
        'click_old_muid_count' => ['click_old_muid_count'],
        'action_count' => ['action_count'],
        'action_ip_count' => ['action_ip_count'],
        'action_ip_distinct_count' => ['action_ip_distinct_count'],
        'action_muid_count' => ['action_muid_count'],
        'action_muid_distinct_count' => ['action_muid_distinct_count'],
        'action_old_muid_count' => ['action_old_muid_count'],
        'reg_uid_count' => ['reg_uid_count'],
        'reg_ip_count' => ['reg_ip_count'],
        'reg_ip_distinct_count' => ['reg_ip_distinct_count'],
        'reg_muid_count' => ['reg_muid_count'],
        'reg_muid_distinct_count' => ['reg_muid_distinct_count'],
        'reg_old_muid_count' => ['reg_old_muid_count'],
        'first_day_pay_count' => ['first_day_pay_count'],
        'second_login_count' => ['second_login_count'],
        'third_login_count' => ['third_login_count'],
        'seventh_login_count' => ['seventh_login_count'],
        'fifteenth_login_count' => ['fifteenth_login_count'],
        'reg_total_pay_count' => ['reg_total_pay_count'],
        'reg_total_pay_money' => ['reg_total_pay_money'],
        'old_uid_count' => ['old_uid_count'],
        'old_uid_pay_count' => ['old_uid_pay_count'],
        'old_uid_pay_money' => ['old_uid_pay_money'],
        'uid_count' => ['uid_count'],
        'total_pay_money' => ['total_pay_money'],
        'reg_old_clique_muid_count' => ['reg_old_clique_muid_count'],
        'click_old_clique_muid_count_ldy' => ['click_old_clique_muid_count_ldy'],
        'click_old_clique_muid_count' => ['click_old_clique_muid_count'],
        'action_old_clique_muid_count' => ['action_old_clique_muid_count'],
        'day_three_login_uid_count' => ['day_three_login_uid_count'],
        'day_three_login_muid_distinct_count' => ['day_three_login_muid_distinct_count'],
        'click_count_yeyou_show' => ['click_count_yeyou_show'],
        'click_ip_distinct_count_yeyou_show' => ['click_ip_distinct_count_yeyou_show'],
        'click_ip_count_yeyou_show' => ['click_ip_count_yeyou_show'],
        'click_muid_distinct_count_yeyou_show' => ['click_muid_distinct_count_yeyou_show'],
        'click_old_muid_count_yeyou_show' => ['click_old_muid_count_yeyou_show'],
        'click_old_clique_muid_count_yeyou_show' => ['click_old_clique_muid_count_yeyou_show'],
        'click_muid_count_yeyou_show' => ['click_muid_count_yeyou_show'],
        'show_old_muid_count_percent' => ['show_old_muid_count_percent', 'click_count_yeyou_show', 'click_old_muid_count_yeyou_show'],
        'show_old_clique_muid_count_percent' => ['show_old_clique_muid_count_percent', 'click_count_yeyou_show', 'click_old_clique_muid_count_yeyou_show'],
        'first_day_pay_money' => ['first_day_pay_money', 'total_pay_money', 'old_uid_pay_money'],
        'reg_first_day_pay_uid_count' => ['reg_first_day_pay_uid_count'],
        'reg_first_day_pay_percent' => ['reg_first_day_pay_percent', 'reg_first_day_pay_uid_count', 'reg_uid_count'],
        'reg_first_day_ltv' => ['reg_first_day_ltv', 'reg_first_day_pay_money', 'reg_uid_count'],
        'role_create_count' => ['role_create_count'],
        'per_role_create_cost' => ['per_role_create_cost', 'cost_money', 'role_create_count'],
        'exclude_channel_fee_money'               => ['exclude_channel_fee_money'],
    ];

    const FORMAT_COLUMN = [
        'first_day_pay_money',
        'reg_total_pay_money',
        'old_uid_pay_money',
        'total_pay_money',
        'exclude_channel_fee_money',
    ];
}
