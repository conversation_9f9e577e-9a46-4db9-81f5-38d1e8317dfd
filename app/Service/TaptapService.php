<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Container;
use App\Model\HttpModel\Taptap\Report\ReportModel;
use App\Param\MediaAccountInfoParam;

class TaptapService
{
    /**
     * @param $platform
     * @param $account_content
     * @param $access_token
     * @return string
     */
    public function addAccount($platform, $account_content, $access_token): string
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $account_content = trim($account_content);
        $account_lines = explode("\n", $account_content);

        $message = '';
        $service = new MediaService();
        $model = new ReportModel();
        $start_date = date("Y-m-d");
        $end_date = date("Y-m-d");
        foreach ($account_lines as $account_line) {
            $account_item = explode(' ', trim($account_line));
            $account = $account_item[0] ?? '';
            $name = $account_item[1] ?? '';
            if (empty($account)) {
                continue;
            }
            if (count($account_item) < 2) {
                $message .= "{$account}-信息不全，请检查;";
                continue;
            }
            if (!is_numeric($account)) {
                $message .= "{$account}-含有非数字字符;";
                continue;
            }

            // 检查token是否有权限访问账号
            try {
                $model->getADAccountReport($access_token, $account, $start_date, $end_date);
            } catch (\Throwable $e) {
                $message .= "{$account}-账号与token关系错误，错误信息：" . $e->getMessage();
                continue;
            }

            $tmp_message = $service->saveAuthAccount(new MediaAccountInfoParam([
                'media_type' => MediaType::TAPTAP,
                'platform' => $platform,
                'account_id' => $account,
                'account_name' =>  $name,
                'access_token' => $access_token,
                'access_token_expires' => 0,
                'refresh_token' => '',
                'refresh_token_expires' => 0,
                'toutiao_majordomo_id' => 0,
            ]), $creator_id, $creator_name);
            $message .= "$tmp_message;";
        }

        return $message;
    }
}
