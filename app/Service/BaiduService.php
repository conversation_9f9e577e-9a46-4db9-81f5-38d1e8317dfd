<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Model\HttpModel\BaiduSearch\AppCenterJobService\AppCenterJobModel;
use App\Model\HttpModel\BaiduSearch\AppCenterPackageService\AppCenterPackageModel;
use App\Model\HttpModel\BaiduSearch\ShareService\ShareModel;
use App\Model\SqlModel\DataMedia\OdsBaiduAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceUploadFileModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\ChannelPackageParam;
use App\Param\MediaAccountInfoParam;
use App\Param\MediaMajordomoAccountInfoParam;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use Common\EnvConfig;
use App\Utils\Helpers;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\HttpModel\Baidu\MccFeed\MccFeedServiceModel;
use App\Model\HttpModel\BaiduSearch\AccountService\AccountServiceModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use Throwable;

class BaiduService implements AccountLeader, DMP
{
    const MAJORDOMO = 1;
    const SUB_ACC = 2;

    public function addAccount($media_type, $platform, $username, $password, $token, $account_type)
    {
        // 百度数据库字段调整
        // account_id 被操作用户id
        // account_name 被操作用户名
        // company 管家账户名
        // access_token 管家密码
        if (!isset(EnvConfig::PLATFORM_MAP[$platform])) {
            throw new AppException('找不到该平台');
        }
        $media_service = new MediaService();
        $req_model = new MccFeedServiceModel();
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $media_account_model = new MediaAccountModel();

        if ($media_type == MediaType::BAIDU_SEARCH) {
            $req_model->setBaiduSearchRole();
        }

        if ($account_type === self::SUB_ACC) {
            $first_account = $media_account_model->getDataByAccountName($username, $media_type);
        } else {
            $first_account = $media_account_model->getBaiduFirstAccountInfo($username, $media_type);
        }
        if ($first_account && $first_account->creator_id != $creator_id) {
            throw new AppException("该管家{$username}已被{$first_account->creator}授权。");
        }

        if ($account_type === self::SUB_ACC) {
            $req_model = new AccountServiceModel();
            $list = $req_model->get($username, $password, $token, '', ['userId']);
            $active_accounts = [];
            foreach ($list as $item) {
                $active_accounts[] = [
                    'userid' => $item['userId'],
                    'username' => $username
                ];
            }
            $username = '';
        } else {
            $active_accounts = $req_model->list($username, $password, $token);
            if (empty($active_accounts)) {
                throw new AppException("该管家暂无可授权的子账户");
            }
            $media_service->saveAuthMajordomoAccount(
                new MediaMajordomoAccountInfoParam([
                    'platform' => $platform,
                    'media_type' => $media_type,
                    'account_id' => $active_accounts[0]['mccid'],
                    'account' => $username,
                    'password' => $password,
                    'access_token' => $token,
                ]), $creator_id, $creator_name
            );

        }
        $active_accounts = $this->filterMediaAccount($media_type, $active_accounts);
        $message = '';
        foreach ($active_accounts as $account) {
            $media_account_param = new MediaAccountInfoParam([
                'media_type' => $media_type,
                'platform' => $platform,
                'account_id' => $account['userid'],
                'account_name' => $account['username'] ?? '',
                'access_token' => $password,
                'access_token_expires' => 0,
                'refresh_token' => $token,
                'refresh_token_expires' => 0,
                'company' => $username,
                'toutiao_majordomo_id' => $account['mccid'] ?? 0
            ]);
            $tmp_message = $media_service->saveAuthAccount($media_account_param, $creator_id, $creator_name);
            $message .= "{$account['username']}-$tmp_message;";
        }

        return $message;
    }

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $audience_change_log_model = new AudienceChangeLogModel();
        $audience_upload_file_model = new AudienceUploadFileModel();
        $device_task_info = (new DeviceTaskModel())->getData($device_task_id);
        $file_list = json_decode($device_task_info->file_list, true);
        $audience_upload_file_map = [];
        $nowadays = date('Y-m-d');
        if ($device_task_info->auto_update === DeviceTaskModel::FULL) {
            $audience_upload_file_map = $audience_upload_file_model
                ->getLastRecordsByDeviceTaskId($device_task_id, strtotime($nowadays))
                ->pluck('id', 'name')
                ->toArray();
        }
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);

            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::BAIDU);

            foreach ($file_list as $file) {
                if ($file['row'] <= 0) {
                    continue;
                }

                // 百度无OPEN_ID类型，过滤不处理
                if ($file['data_type'] == UserIDType::OPEN_ID) {
                    continue;
                }

                $audience_upload_file_name = "task-{$device_task_id}-all-" . UserIDType::FILE_NAME_MAP[$file['data_type']] . ".txt";
                $device_audience_name = "{$audience_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']];
                $table_audience_id = $audience_model->add(
                    $user_id,
                    $username,
                    $device_task_id,
                    MediaType::BAIDU,
                    $company['name'],
                    $seed_account,
                    $device_audience_name,
                    $audience_desc,
                    $push_account_list,
                    AudienceModel::TYPE_EXPORT,
                    0,
                    '',
                    $media_account_info->platform,
                    $data_source_type
                );
                $table_audience_ids[] = $table_audience_id;
                // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
                ///** @uses \App\Task\BaiduTask::createAudience() */
                //Container::getServer()->task(['action' => 'BaiduTask.createAudience', 'data' => ['table_audience_id' => $table_audience_id]]);
                if ($device_task_info->auto_update === DeviceTaskModel::FULL) {
                    if (!isset($audience_upload_file_map[$audience_upload_file_name])) {
                        copy(DeviceService::getFile($device_task_id, $file['data_type']), AudienceService::AUDIENCE_DIR . '/' . $audience_upload_file_name);
                        $file_desc = "{$nowadays} dmp任务id:{$device_task_id}首次推送的文件";
                        $audience_upload_file_map[$audience_upload_file_name] = $audience_upload_file_model->add(
                            $audience_upload_file_name,
                            md5_file(AudienceService::AUDIENCE_DIR . '/' . $audience_upload_file_name),
                            $file['data_type'],
                            $file_desc,
                            AudienceUploadFileModel::SYSTEM_UPLOAD,
                            $file['row']
                        );
                    }
                    $audience_change_log_model->add(
                        1,
                        '超管',
                        $table_audience_id,
                        0,
                        $audience_upload_file_map[$audience_upload_file_name],
                        AudienceChangeLogModel::RESET,
                        MediaType::BAIDU,
                        strtotime('-1 day', strtotime($nowadays)),
                        AudienceChangeLogModel::SUCCESS
                    );
                }
            }
        }
        return $table_audience_ids;
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::BAIDU);
            $table_audience_id = $audience_model->add(
                $user_id,
                $username,
                $device_task_id,
                MediaType::BAIDU,
                $company['name'],
                $seed_account,
                $audience_name,
                $audience_desc,
                $push_account_list,
                AudienceModel::TYPE_FILE_ADDITION,
                0,
                '',
                $media_account_info->platform,
                $data_source_type
            );
            $table_audience_ids[] = $table_audience_id;
            // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
        }
        return $table_audience_ids;
    }

    public function editPushAccountList($id, $account_ids)
    {
        $audience_model = new AudienceModel();
        $audience_info = $audience_model->getData($id);
        $media_model = new MediaAccountModel();
        $media_account_info = $media_model->getDataByAccountId($audience_info->account_id);
        $share_model = new ShareModel();
        array_map(function ($push_account_list) use ($share_model, $audience_info, $media_account_info) {
            $share_model->saveSharingBatchDr(
                $media_account_info->company,
                $media_account_info->access_token,
                $media_account_info->refresh_token,
                $media_account_info->account_name,
                [$audience_info->audience_id],
                $push_account_list
            );
        }, array_chunk($account_ids, 1000));
        $already_push_account_list = json_decode($audience_info->push_account_list, true);
        return $audience_model->editPushAccountList($id, json_encode(array_merge($already_push_account_list, $account_ids)));
    }

    public function createDataSource($table_audience_id, array $file)
    {
        $file_count = 0;
        $file_index = 0;
        // 每个分片不大于100M
        $row_limit = 500000;
        $filename = $file['name'];
        $user_id_type = UserIDType::BAIDU_TYPE_MAP[$file['data_type']];
        if (!file_exists($filename)) {
            Helpers::getLogger('audience')->error("file not exists", [
                'audience_id' => $table_audience_id,
                'filename' => $filename,
                'media_type' => MediaType::BAIDU,
            ]);
            throw new AppException('file not exists');
        }

        $handle = fopen($filename, 'rb');
        $line_number = 0;
        while (!feof($handle)) {
            $content = '';
            for ($i = 0; $i < $row_limit; ++$i) {
                $line = fgets($handle);
                $line = trim($line);
                if (empty($line)) {
                    continue;
                }

                if ($file['need_md5']) {
                    $line = md5($line);
                }

                $line_number++;
                $content .= $line . PHP_EOL;
            }
            file_put_contents(AudienceService::getAudienceDir(MediaType::BAIDU) . "/$table_audience_id-{$file_count}.txt", $content);
            $file_count++;
        }
        fclose($handle);
        $file_paths = [
            'row' => $line_number,
            'user_id_type' => $user_id_type,
            'paths' => []
        ];
        for (; $file_index <= $file_count - 1; $file_index++) {
            $file_paths['paths'][] = [
                'file' => AudienceService::getAudienceDir(MediaType::BAIDU) . "/$table_audience_id-{$file_index}.txt",
            ];
        }
        return $file_paths;
    }

    public function filterMediaAccount($media_type, array $account_list)
    {
        if ($media_type == MediaType::BAIDU) {
            $prefix = '原生';
            $prefix_new = 'baidu-原生';
            $patten = "/(baidu)(.*)(原生)/";
        } else if ($media_type == MediaType::BAIDU_SEARCH) {
            $prefix = 'baidu';
            $prefix_new = 'baidu-搜索';
            $patten = "/(baidu)(.*)(搜索)/";
        } else {
            throw new AppException("媒体类型错误");
        }
        $common_prefix = "baidu-原生";

        $len = strlen($prefix);
        $len_new = strlen($prefix_new);
        $accounts = [];
        foreach ($account_list as $account) {
            $target = $account['username'];
            // 百度搜索旧前缀与百度信息流新前缀重合 得跳过
            if ($media_type == MediaType::BAIDU_SEARCH && strncmp($target, $common_prefix, strlen($common_prefix)) == 0) {
                continue;
            }

            if (preg_match($patten, $target) === 1 || strncmp($target, $prefix, $len) == 0 || strncmp($target, $prefix_new, $len_new) == 0) {
                $accounts[] = $account;
            }
        }
        return $accounts;
    }

    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsBaiduAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }


    public function updateAndroidChannelPackage($platform, $site_id)
    {
        $site_info = (new SiteModel())->getDataByPlatformSiteId($platform, $site_id);

        if ($site_info->game_type !== '安卓') {
            throw new AppException("不是安卓游戏");
        }

        if (!is_numeric($site_info->app_android_channel_package_id) || $site_info->app_android_channel_package_id <= 0) {
            throw new AppException("不存在渠道包id");
        }

        $media_account_info = (new MediaAccountModel())->getDataByAccountId($site_info->account_id);
        if (empty($media_account_info)) {
            throw new AppException("获取账户信息失败");
        }

        $job = [
            'packageId' => (int)$site_info->app_android_channel_package_id,
            'packageLink' => $site_info->download_url,
            'autoUpdate' => true,
            'privacyProtectionAgreement' => true,
        ];
        $job_list[] = $job;
        $unique_id = md5(json_encode($job_list) . time());
        $body = [
            'uniqueId' => $unique_id,
            'jobList' => $job_list,
        ];

        return (new AppCenterJobModel())->update(
            $media_account_info->account_name,
            $media_account_info->access_token,
            $media_account_info->company,
            $media_account_info->refresh_token,
            $body
        );
    }

    public function getAndroidChannelPackageTask($platform, $site_id)
    {
        $site_info = (new SiteModel())->getDataByPlatformSiteId($platform, $site_id);

        if ($site_info->game_type !== '安卓') {
            throw new AppException("不是安卓游戏");
        }

        if (!is_numeric($site_info->app_android_channel_package_id) || $site_info->app_android_channel_package_id <= 0) {
            throw new AppException("不存在渠道包id");
        }

        $media_account_info = (new MediaAccountModel())->getDataByAccountId($site_info->account_id);
        if (empty($media_account_info)) {
            throw new AppException("获取账户信息失败");
        }
        return (new AppCenterPackageModel())->get(
            $media_account_info->company,
            $media_account_info->access_token,
            $media_account_info->refresh_token,
            $media_account_info->account_name,
            1, 1,
            $site_info->app_android_channel_package_id
        );
    }


}
