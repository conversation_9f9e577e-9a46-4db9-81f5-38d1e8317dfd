<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\UC\Account\AccountModel;
use App\Model\SqlModel\DataMedia\OdsUCAccountLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\MediaAccountInfoParam;
use App\Param\MediaMajordomoAccountInfoParam;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use App\Utils\Helpers;
use Throwable;
use ZipArchive;

class UCService implements AccountLeader, DMP
{
    const MAJORDOMO = 1;
    const SUB_ACC = 2;

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];
        $device_task_info = (new DeviceTaskModel())->getData($device_task_id);
        $file_list = json_decode($device_task_info->file_list, true);
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::UC);

            foreach ($file_list as $file) {
                if ($file['row'] <= 0) {
                    continue;
                }

                // UC无OPEN_ID类型，过滤不处理
                if ($file['data_type'] == UserIDType::OPEN_ID) {
                    continue;
                }

                $device_audience_name = "{$audience_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']];
                $table_audience_id = $audience_model->add(
                    $user_id,
                    $username,
                    $device_task_id,
                    MediaType::UC,
                    $company['name'],
                    $seed_account,
                    $device_audience_name,
                    $audience_desc,
                    $push_account_list,
                    AudienceModel::TYPE_EXPORT,
                    0,
                    '',
                    $media_account_info->platform,
                    $data_source_type
                );
                $table_audience_ids[] = $table_audience_id;
                // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
                ///** @uses \App\Task\UCTask::createAudience()*/
                //Container::getServer()->task(['action' => 'UCTask.createAudience', 'data' => ['table_audience_id' => $table_audience_id]]);
            }
        }
        return $table_audience_ids;
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::UC);
            $table_audience_id = $audience_model->add(
                $user_id,
                $username,
                $device_task_id,
                MediaType::KUAISHOU,
                $company['name'],
                $seed_account,
                $audience_name,
                $audience_desc,
                $push_account_list,
                AudienceModel::TYPE_FILE_ADDITION,
                0,
                '',
                $media_account_info->platform,
                $data_source_type
            );
            $table_audience_ids[] = $table_audience_id;
        }
        return $table_audience_ids;
    }

    public function addAccount($platform, $account_name, $account_password, $access_token, $account_type)
    {
        if ($account_type == self::SUB_ACC) {
            return $this->authSubAccount($platform, $account_name, $account_password, $access_token);
        } else {
            return $this->authAgentAccount($platform, $account_name, $account_password, $access_token);
        }
    }

    private function authAgentAccount($platform, $account_name, $account_password, $access_token)
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $req_model = new AccountModel();
        $media_service = new MediaService();
        $message = '';

        $accounts = $req_model->getChildrenAccountByAccountId($account_name, $account_password, $access_token)['childrenAccounts'];

        if (empty($accounts)) {
            throw new AppException("该管家下无子账号，授权失败");
        }

        $media_service->saveAuthMajordomoAccount(
            new MediaMajordomoAccountInfoParam([
                'platform' => $platform,
                'media_type' => MediaType::UC,
                'account' => $account_name,
                'password' => $account_password,
                'access_token' => $access_token,
            ]), $creator_id, $creator_name
        );

        foreach ($accounts as $acc) {
            // 判断账号是否UC智投账号
            try {
                $res_data = $req_model->getAccountInfo($account_name, $account_password, $access_token, $acc['name']);
            } catch (Throwable $e) {
                Helpers::getLogger('UC')->error('授权失败', [
                    'account_name' => $account_name,
                    'account_password' => $account_password,
                    'access_token' => $access_token,
                    'sub_account' => 'null',
                    'message' => $e->getMessage()
                ]);
                $message .= "{$account_name}授权失败, 请检查账户名是否正确";
                continue;
            }

            $account_info = $res_data['accountInfoType'];
            $media_account_param = new MediaAccountInfoParam([
                'media_type' => MediaType::UC,
                'platform' => $platform,
                'account_id' => $acc['id'],
                'account_name' => $acc['name'],
                'account_password' => $account_password,
                'access_token' => $access_token,
                'access_token_expires' => 0,
                'refresh_token' => '',
                'refresh_token_expires' => 0,
                'company' => $acc['companyName'],
                'wechat_account_name' => $account_name,
                'agent' => $account_info['bizType'] ?? 1
            ]);
            $tmp_message = $media_service->saveAuthAccount($media_account_param, $creator_id, $creator_name);
            $message .= "{$acc['name']}-$tmp_message;";
        }
        return $message;
    }

    private function authSubAccount($platform, $account_name, $account_password, $access_token)
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $req_model = new AccountModel();
        $media_service = new MediaService();
        try {
            $res_data = $req_model->getAccountInfo($account_name, $account_password, $access_token, null);
        } catch (Throwable $e) {
            Helpers::getLogger('UC')->error('授权失败', [
                'account_name' => $account_name,
                'account_password' => $account_password,
                'access_token' => $access_token,
                'sub_account' => 'null',
                'message' => $e->getMessage()
            ]);
            $msg = "{$account_name}授权失败, 请检查账户名是否正确";
            throw new AppException($msg);
        }
        $account_info = $res_data['accountInfoType'];
        $media_account_param = new MediaAccountInfoParam([
            'media_type' => MediaType::UC,
            'platform' => $platform,
            'account_id' => $account_info['accountId'],
            'account_name' => $account_info['userName'],
            'account_password' => $account_password,
            'access_token' => $access_token,
            'access_token_expires' => 0,
            'refresh_token' => '',
            'refresh_token_expires' => 0,
            'company' => $account_info['companyName'],
            'agent' => $account_info['bizType'] ?? 1
        ]);
        $tmp_message = $media_service->saveAuthAccount($media_account_param, $creator_id, $creator_name);
        $message = "{$account_info['userName']}-$tmp_message";
        return $message;
    }

    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsUCAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }

    public function editPushAccountList($id, $account_ids)
    {
        throw new AppException('UC不能共享人群包');
    }

    public function saveFile($file_uuid, $filename)
    {
        $audience_filename = AudienceService::getAudienceDir(MediaType::UC) . "/$file_uuid.txt";
        copy($filename, $audience_filename);
        return $audience_filename;
    }

    public function appendFile($file_uuid, $filename)
    {
        $audience_filename = AudienceService::getAudienceDir(MediaType::UC) . "/$file_uuid.txt";
        if (file_exists($audience_filename)) {
            file_put_contents($audience_filename, file_get_contents($filename), FILE_APPEND);
        }
        return $audience_filename;
    }

    public function createDataSource($file_uuid, array $file)
    {
        // 压缩包内号码总量不少于1千个，不多于3千万个，压缩包最大1G
        $row_min = 1000;
        $row_max = ********;
        $zip_max_size = **********;
        $filename = $file['name'];
        $user_id_type = UserIDType::UC_TYPE_MAP[$file['data_type']];
        if (!file_exists($filename)) {
            Helpers::getLogger('audience')->error("file not exists", [
                'file_uuid' => $file_uuid,
                'filename' => $filename,
                'media_type' => MediaType::UC,
            ]);
            throw new AppException('file not exists');
        }

        $handle = fopen($filename, 'rb');
        $line_number = 0;
        $content = '';
        while (!feof($handle)) {
            $line = fgets($handle);
            if (empty($line)) {
                break;
            }

            if ($file['need_md5']) {
                $line = trim($line);
                $line = md5($line) . PHP_EOL;
            }

            $line_number++;
            $content .= $line;
        }
        fclose($handle);
        if ($line_number < $row_min || $line_number > $row_max) {
            throw new AppException('压缩包内号码总量不少于1千个，不多于3千万个');
        }

        $tmp_filename = AudienceService::getAudienceDir(MediaType::UC) . "/$file_uuid-tmp.txt";
        file_put_contents($tmp_filename, $content);
        $zip = new ZipArchive;
        $zip_path = AudienceService::getAudienceDir(MediaType::UC) . "/$file_uuid.zip";
        $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
        if ($code === TRUE) {
            $zip->addFile($tmp_filename, "$file_uuid.txt");
            $zip->close();
            unlink($tmp_filename);
        }
        if (filesize($zip_path) > $zip_max_size) {
            throw new AppException('压缩包最大1G');
        }
        return [
            'row' => $line_number,
            'user_id_type' => $user_id_type,
            'path' => $zip_path
        ];
    }
}
