<?php

namespace App\Service;

use App\Exception\AppException;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModeModel;
use App\Model\SqlModel\Zeda\MaterialThemeModel;
use App\Param\ADServing\ADMaterialPacketParam;

class MaterialModeService
{
    /**
     * @param ADMaterialPacketParam $param
     * @return ADMaterialPacketParam
     */
    static public function deleteErrorMaterial(ADMaterialPacketParam $param): ADMaterialPacketParam
    {
        $mode_list = (new MaterialModeModel())->getByMediaType($param->media_type);
        $key_list = ['video_vertical_list', 'video_list', 'group_image_list', 'group_video_list', 'small_image_list', 'large_image_list', 'large_vertical_image_list', 'audio_list'];
        foreach ($key_list as $key) {
            $param->{$key} = array_values(array_filter($param->{$key}, function ($item) use ($mode_list) {
                foreach ($mode_list as $mode) {
                    if (self::judge((array)$item, (array)$mode)) {
                        return true;
                    }
                }
                return false;
            }));
        }
        return $param;
    }

    /**
     * @param array $item
     * @param array $mode
     * @return bool
     */
    static private function judge(array $item, array $mode): bool
    {
        if ($mode['file_type'] != $item['file_type']) {
            return false;
        }
        if ($mode['width'] != $item['width']) {
            return false;
        }
        if ($mode['height'] != $item['height']) {
            return false;
        }
        if ($mode['max_file_size'] * 1024 < $item['size']) {
            return false;
        }
        return true;
    }
}
