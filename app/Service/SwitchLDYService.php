<?php
/**
 * 切广告落地页
 * User: zzh
 * Date: 2021/04/12
 * Time: 11:04
 */

namespace App\Service;

use App\Constant\AliOssMap;
use App\Constant\LDYSetting;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\GamePackModel;
use App\Model\SqlModel\Zeda\SiteLdyLogModel;
use App\Param\LDYFrameParam;
use App\Param\LDYHrefParam;
use App\Param\LDYInfoParam;
use App\Param\LDYTemplateParam;
use App\Service\PlatformAD\PlatformInside;
use Throwable;

class SwitchLDYService
{
    /**
     * 完成落地页异步切换任务
     */
    public function syncLDY()
    {
        $site_ldy_log_model = new SiteLdyLogModel();

        $need_switch_ldy_list = $site_ldy_log_model->getSyncLogList();

        $need_switch_ldy_list_num = count($need_switch_ldy_list);

        echo date('Y-m-d H:i:s') . "当前获得切换落地页任务数{$need_switch_ldy_list_num}" . PHP_EOL;

        foreach ($need_switch_ldy_list as $ldy_info) {
            $ldy_info_param = new LDYInfoParam($ldy_info);

            if (!isset($ldy_info_param->ldy_ad_list['href']) || empty($ldy_info_param->ldy_ad_list['href'])) {
                echo date('Y-m-d H:i:s') . "落地页切换任务id:$ldy_info->id,因ldy_ad_list字段中href内容为空,切换失败" . PHP_EOL;
                continue;
            }

            try {
                switch ($ldy_info_param->template_type) {
                    case 'nojump.html':
                    case 'nojump_https.html':
                    case 'shuangduan.html':
                        if (!$this->noJumpLDY($ldy_info_param)) {
                            continue 2;
                        }
                        break;
//                    case 'ios302.php':
//                    case 'ios301.php':
//                        if (!$this->ios302LDY($ldy_info_param)) {
//                            continue 2;
//                        }
//                        break;
                    case 'ios_link.html':
                        if (!$this->iosLinkLDY($ldy_info_param)) {
                            continue 2;
                        }
                        break;
                    case 'weixin.html':
                        if (!$this->weChatLDY($ldy_info_param)) {
                            continue 2;
                        }
                        break;
                    default:
                        echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info->id})出错，找不到对应的落地页类型({$ldy_info_param->template_type})" . PHP_EOL;
                        continue 2;
                }

                // 落地页生成成功修改任务状态
                $site_ldy_log_model->finishLog($ldy_info_param->id);

            } catch (Throwable $exception) {
                echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info->id})出错，错误信息:{$exception->getMessage()}" . PHP_EOL;
            }

        }
    }

    /**
     * IOS直跳落地页
     * @param LDYInfoParam $ldy_info_param
     * @return bool
     */
    private function iosLinkLDY(LDYInfoParam $ldy_info_param)
    {
        $ldy_href_param = new LDYHrefParam($ldy_info_param->ldy_ad_list['href'][0]);

        $game_data = (new V2DimGameIdModel())->getDataByGameId($ldy_info_param->platform, $ldy_href_param->game_id);

        if ($game_data) {
            if ($game_data->os == 'IOS') {
                $ldy_href_param->download_url = "https://itunes.apple.com/cn/app/id{$game_data->app_id}?mt=8";
            } else {
                echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，游戏id({$ldy_href_param->game_id})，类型为{$game_data->os}，不能用于切换IOS302,IOS301,shuangduan2模板" . PHP_EOL;
                return false;
            }
        } else {
            echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，找不到游戏id({$ldy_href_param->game_id})的下载地址" . PHP_EOL;
            return false;
        }

        $ldy_frame_param = new LDYFrameParam();
        $ldy_frame_param->setLDYFrameTemplate($ldy_info_param->template_type);
        $ldy_frame_param->setJquery();
        $ldy_frame_param->setProp('platform', $ldy_info_param->platform);
        $ldy_frame_param->setProp('game_id', $ldy_href_param->game_id);
        $ldy_frame_param->setProp('agent_id', $ldy_info_param->agent_id);
        $ldy_frame_param->setProp('site_id', $ldy_info_param->site_id);
        $ldy_frame_param->setProp('title', $game_data->game_name);
        $ldy_frame_param->setProp('go_url', $ldy_href_param->download_url);
        $ldy_frame_param->setProp('tj_url', LDYSetting::TJ_URL);
        $ldy_frame_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}.html");

        return true;
    }

    /**
     * 微信模板落地页
     * @param LDYInfoParam $ldy_info_param
     * @return bool
     */
    private function weChatLDY(LDYInfoParam $ldy_info_param)
    {
        foreach ($ldy_info_param->ldy_ad_list['href'] as $turn_num => $ldy_href_info) {

            $ldy_href_param = new LDYHrefParam($ldy_href_info);

            $game_data = (new V2DimGameIdModel())->getDataByGameId($ldy_info_param->platform, $ldy_href_param->game_id);

            if ($game_data) {
                if ($game_data->os == 'IOS') {
                    $ldy_href_param->download_url = "https://itunes.apple.com/cn/app/id{$game_data->app_id}?mt=8";
                } else {
                    $android_game_data = (new GamePackModel())->getDataByPlatformSiteGameId(
                        $ldy_info_param->platform,
                        $ldy_info_param->site_id,
                        $ldy_href_param->game_id
                    );
                    if ($android_game_data) {
                        $ldy_href_param->download_url = (new PlatformInside($android_game_data->platform))->getAPKURL(
                            $android_game_data->platform,
                            $android_game_data->game_id,
                            $android_game_data->media_type,
                            $android_game_data->site_id
                        );
                    } else {
                        echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，找不到 安卓 游戏id({$ldy_href_param->game_id})的下载地址" . PHP_EOL;
                        return false;
                    }
                }
            } else {
                echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，找不到游戏id({$ldy_href_param->game_id})的下载地址" . PHP_EOL;
                return false;
            }

            $ldy_template_param = new LDYTemplateParam();
            $ldy_template_param->setLDYTemplate((int)$ldy_href_param->adid);
            $ldy_template_param->setBeiAn($ldy_info_param->beian ?: '广告仅供参考，请以游戏为准');
            $ldy_template_param->setAHref($ldy_href_param->download_url);
            $ldy_template_param->setUploadLogic(SwitchLDYResourceService::getJSContent(
                $ldy_info_param->platform,
                $ldy_href_param->download_url,
                $ldy_info_param->agent_id,
                $ldy_href_param->adid,
                $ldy_info_param->site_id,
                $ldy_href_param->game_id,
                $ldy_info_param->game_type,
                $ldy_info_param->auto_download,
                $ldy_info_param->auto_download_second
            ));
            $ldy_template_param->setStaticSrc("/static/{$ldy_href_param->adid}/");

            if ($game_data->os == 'IOS') {
                $game_type = 2;
            } else {
                $game_type = 1;
            }
            $ldy_template_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}_{$game_type}.html");
        }

        $ldy_frame_param = new LDYFrameParam();
        $ldy_frame_param->setLDYFrameTemplate($ldy_info_param->template_type);
        $ldy_frame_param->setJquery();
        $ldy_frame_param->setProp('platform', $ldy_info_param->platform);
        $ldy_frame_param->setProp('agent_id', $ldy_info_param->agent_id);
        $ldy_frame_param->setProp('site_id', $ldy_info_param->site_id);
        $ldy_frame_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}.html");

        return true;
    }

    /**
     * ios302模板落地页
     * @param LDYInfoParam $ldy_info_param
     * @return bool
     */
    private function ios302LDY(LDYInfoParam $ldy_info_param)
    {
        $ldy_href_param = new LDYHrefParam($ldy_info_param->ldy_ad_list['href'][0]);

        $game_data = (new V2DimGameIdModel())->getDataByGameId($ldy_info_param->platform, $ldy_href_param->game_id);

        if ($game_data) {
            if ($game_data->os == 'IOS') {
                $ldy_href_param->download_url = "https://itunes.apple.com/cn/app/id{$game_data->app_id}?mt=8";
            } else {
                echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，游戏id({$ldy_href_param->game_id})，类型为{$game_data->os}，不能用于切换IOS302,IOS301,shuangduan2模板" . PHP_EOL;
                return false;
            }
        } else {
            echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，找不到游戏id({$ldy_href_param->game_id})的下载地址" . PHP_EOL;
            return false;
        }

        $ldy_frame_param = new LDYFrameParam();
        $ldy_frame_param->setLDYFrameTemplate($ldy_info_param->template_type);
        $ldy_frame_param->setProp('platform', $ldy_info_param->platform);
        $ldy_frame_param->setProp('game_id', $ldy_href_param->game_id);
        $ldy_frame_param->setProp('agent_id', $ldy_info_param->agent_id);
        $ldy_frame_param->setProp('site_id', $ldy_info_param->site_id);
        $ldy_frame_param->setProp('title', $game_data->game_name);
        $ldy_frame_param->setProp('go_url', $ldy_href_param->download_url);
        $ldy_frame_param->setProp('tj_url', LDYSetting::TJ_URL);
        $ldy_frame_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}.html");

        return true;
    }

    /**
     * 直链模板落地页
     * @param LDYInfoParam $ldy_info_param
     * @return bool
     */
    private function noJumpLDY(LDYInfoParam $ldy_info_param)
    {
        foreach ($ldy_info_param->ldy_ad_list['href'] as $turn_num => $ldy_href_info) {

            $ldy_href_param = new LDYHrefParam($ldy_href_info);

            $game_data = (new V2DimGameIdModel())->getDataByGameId($ldy_info_param->platform, $ldy_href_param->game_id);

            if ($game_data) {
                if ($game_data->os == 'IOS') {
                    $ldy_href_param->download_url = "https://itunes.apple.com/cn/app/id{$game_data->app_id}?mt=8";
                } else {
                    $android_game_data = (new GamePackModel())->getDataByPlatformSiteGameId(
                        $ldy_info_param->platform,
                        $ldy_info_param->site_id,
                        $ldy_href_param->game_id
                    );
                    if ($android_game_data) {
                        $ldy_href_param->download_url = (new PlatformInside($android_game_data->platform))->getAPKURL(
                            $android_game_data->platform,
                            $android_game_data->game_id,
                            $android_game_data->media_type,
                            $android_game_data->site_id
                        );
                    } else {
                        echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，找不到 安卓 游戏id({$ldy_href_param->game_id})的下载地址" . PHP_EOL;
                        return false;
                    }
                }
            } else {
                echo date('Y-m-d H:i:s') . "切换落地页任务id({$ldy_info_param->id})出错，找不到游戏id({$ldy_href_param->game_id})的下载地址" . PHP_EOL;
                return false;
            }

            $ldy_template_param = new LDYTemplateParam();
            $ldy_template_param->setLDYTemplate((int)$ldy_href_param->adid);
            $ldy_template_param->setBeiAn($ldy_info_param->beian ?: '广告仅供参考，请以游戏为准');
            $ldy_template_param->setAHref($ldy_href_param->download_url);
            $ldy_template_param->setUploadLogic(SwitchLDYResourceService::getJSContent(
                $ldy_info_param->platform,
                $ldy_href_param->download_url,
                $ldy_info_param->agent_id,
                $ldy_href_param->adid,
                $ldy_info_param->site_id,
                $ldy_href_param->game_id,
                $ldy_info_param->game_type,
                $ldy_info_param->auto_download,
                $ldy_info_param->auto_download_second
            ));
            $ldy_template_param->setStaticSrc("/static/{$ldy_href_param->adid}/");

            if (count($ldy_info_param->ldy_ad_list['href']) > 1) {
                $ldy_turn_num = $turn_num + 1;
                $ldy_template_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}_{$ldy_turn_num}.html");
            } else {
                $ldy_template_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}.html");
            }
        }

        if (count($ldy_info_param->ldy_ad_list['href']) > 1) {    //判断创意数量
            $ldy_frame_param = new LDYFrameParam();
            $ldy_frame_param->setLDYFrameTemplate($ldy_info_param->template_type);
            $ldy_frame_param->setProp('platform', $ldy_info_param->platform);
            $ldy_frame_param->setProp('agent_id', $ldy_info_param->agent_id);
            $ldy_frame_param->setProp('site_id', $ldy_info_param->site_id);
            $ldy_frame_param->put($ldy_info_param->getLDYRootPath(), AliOssMap::LDY_DIR . "/code/{$ldy_info_param->platform}/{$ldy_info_param->agent_id}/{$ldy_info_param->site_id}.html");
        }
        return true;
    }
}