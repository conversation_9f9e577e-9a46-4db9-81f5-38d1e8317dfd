<?php


namespace App\Service;

use App\Constant\AgentType;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\AgentSiteLogModel;

class AgentSiteLogService
{
    public function editAgentLog(array $ori_data, array $data, $user_id, $username)
    {
        $data_name = [
            'media_type' => '媒体',
            'agent_name' => '渠道名',
            'agent_leader' => '负责人',
            'agent_type' => '渠道类型',
            'own' => '自有渠道',
            'account_type' => '账户类型',
            'id_card' => '身份证号码',
            'id_img' => '身份证照片',
            'bank_area' => '开户银行地区',
            'bank_name' => '银行',
            'bank_card_number' => '银行卡号',
            'bank_holder' => '开户人',
            'person' => '联系人',
            'qq' => 'QQ',
            'email' => '邮箱',
            'mobile' => '手机号码',
            'tel' => '联系电话',
            'address' => '通讯地址',
            'detail_address' => '详细地址',
            'protocol_number' => '合同(协议号)',
            'protocol_type' => '合同协议类型',
            'state' => '渠道状态',
        ];

        $log_str = '';

        foreach ($data_name as $key => $name) {
            if ($data[$key] === $ori_data[$key]) {
                continue;
            }
            switch ($key) {
                case 'media_type':
                    $map = MediaType::MEDIA_TYPE_MAP;
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'agent_type':
                    $map = AgentType::AGENT_TYPE_MAP;
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'own':
                    $map = ['否', '是'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'account_type':
                    $map = [1 => '个人', 2 => '企业'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'state':
                    $map = ['未审核', '已审核'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                default:
                    $log_str .= "{$name}由{$ori_data[$key]}变为{$data[$key]},";
                    break;
            }
        }
        $log_str = rtrim($log_str, ',');
        if ($log_str === '') {
            return;
        }
        (new AgentSiteLogModel())->add([
            'platform' => $data['platform'],
            'agent_id' => $data['agent_id'],
            'content' => $log_str,
            'create_time' => time(),
            'creator' => $username,
            'creator_id' => $user_id,
        ]);
    }

    public function editSiteLog(array $ori_data, array $data, $user_id, $username)
    {
        $data_name = [
            'game_id' => '游戏ID',
            'site_name' => '渠道名',
            'convert_type' => '转化类型',
            'convert_source_type' => '转化来源',
            'ad_turn' => '转化来源',
            'ad_price' => '广告单价',
            'ad_pop_zk' => '广告位扣量',
            'ad_pop_zk_type' => '广告位扣量类型',
            'auto_download' => '自动下载',
            'auto_download_second' => '自动下载秒数',
            'cps_divide_rate' => 'CPS分成比例',
            'forbid_tuitan' => '禁用退弹',
            'template_type' => '自定义模板',
            'template_address' => '临时模板地址',
            'pay_type' => '支付类型',
            'upt_state' => '上报状态',
            'upt_rate' => '上报比例',
            'pay_discount' => '付费上报扣量',
            'reg_discount' => '注册上报扣量',
            'is_third' => '非贪玩域名',
            'deep_external_action' => '深度转化目标',
            'appid' => '应用ID',
            'state' => '审核状态',
            'game_pack' => '打包选项'
        ];

        $log_str = '';

        foreach ($data_name as $key => $name) {
            if ($data[$key] === $ori_data[$key]) {
                continue;
            }
            switch ($key) {
                case 'game_id':
                    $game_list = (new V2DimGameIdModel())->getListInGameId($data['platform'], [$ori_data['game_id'], $data['game_id']]);
                    $ori_game_name = $game_list->where('game_id', $ori_data['game_id'])->first()->game_name ?? '';
                    $game_name = $game_list->where('game_id', $data['game_id'])->first()->game_name ?? '';
                    $log_str .= "{$name}由{$ori_game_name}变为{$game_name},";
                    break;
                case 'convert_type':
                    if (isset(ConvertType::MAP[$data['media_type']])) {
                        $map = ConvertType::MAP[$data['media_type']];
                        $ori_convert_type = $map[$ori_data[$key]] ?? '';
                        $log_str .= "{$name}由{$ori_convert_type}变为{$map[$data[$key]]},";
                    }
                    break;
                case 'pay_type':
                    $map = [1 => 'cpm', 'cpc', 'cpa', '包月', '免费', 'cps', 'cpt'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'auto_download':
                case 'forbid_tuitan':
                case 'is_third':
                    $map = ['否', '是'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'upt_state':
                    $map = ['关闭', '开启'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                case 'state':
                    $map = ['未审核', '已审核'];
                    $log_str .= "{$name}由{$map[$ori_data[$key]]}变为{$map[$data[$key]]},";
                    break;
                default:
                    $log_str .= "{$name}由{$ori_data[$key]}变为{$data[$key]},";
                    break;
            }
        }
        $log_str = rtrim($log_str, ',');
        if ($log_str === '') {
            return;
        }
        (new AgentSiteLogModel())->add([
            'platform' => $data['platform'],
            'agent_id' => $data['agent_id'],
            'site_id' => $data['site_id'],
            'content' => $log_str,
            'create_time' => time(),
            'creator' => $username,
            'creator_id' => $user_id
        ]);
    }

    public function batchEditSiteLog(array $data, $user_id, $username)
    {
        $data_name = [
            'ad_turn' => '转化来源',
            'ad_price' => '广告单价',
            'ad_pop_zk' => '广告位扣量',
            'ad_pop_zk_type' => '广告位扣量类型',
            'pay_discount' => '付费上报扣量',
            'reg_discount' => '注册上报扣量',
            'auto_download' => '自动下载',
            'auto_download_second' => '自动下载秒数',
            'forbid_tuitan' => '禁用退弹',
            'template_type' => '自定义模板',
            'pay_type' => '支付类型',
            'cps_divide_rate' => 'CPS分成比例',
            'upt_state' => '上报状态',
            'state' => '审核状态',
        ];

        $log_str = '';
        foreach ($data_name as $key => $name) {
            if (!isset($data[$key]) && empty($data[$key])) {
                continue;
            }
            switch ($key) {
                case 'pay_type':
                    $map = [1 => 'cpm', 'cpc', 'cpa', '包月', '免费', 'cps', 'cpt'];
                    $log_str .= "{$name}变为{$map[$data[$key]]},";
                    break;
                case 'auto_download':
                case 'forbid_tuitan':
                    $map = ['否', '是'];
                    $log_str .= "{$name}变为{$map[$data[$key]]},";
                    break;
                case 'upt_state':
                    $map = ['关闭', '开启'];
                    $log_str .= "{$name}变为{$map[$data[$key]]},";
                    break;
                case 'state':
                    $map = ['未审核', '已审核'];
                    $log_str .= "{$name}变为{$map[$data[$key]]},";
                    break;
                default:
                    $log_str .= "{$name}变为{$data[$key]},";
                    break;
            }
        }
        $log_str = rtrim($log_str, ',');
        if ($log_str === '') {
            return;
        }
        $log_data = [];
        foreach ($data['site_ids'] as $site_id) {
            $log_data[] = [
                'platform' => $data['platform'],
                'agent_id' => $data['agent_id'],
                'site_id' => $site_id,
                'content' => $log_str,
                'create_time' => time(),
                'creator' => $username,
                'creator_id' => $user_id
            ];
        }
        (new AgentSiteLogModel())->add($log_data);
    }

    public function reportTestLog(array $data, $user_id, $username)
    {
        $log_str = '测试上报';
        (new AgentSiteLogModel())->add([
            'platform' => $data['platform'],
            'agent_id' => $data['agent_id'],
            'site_id' => $data['site_id'],
            'content' => $log_str,
            'create_time' => time(),
            'creator' => $username,
            'creator_id' => $user_id,
        ]);
    }

    public function emptySwitchADLog($platform, $agent_id, $user_id, $username)
    {
        $insert_data = [
            'platform' => $platform,
            'agent_id' => $agent_id,
            'content' => "快速空切",
            'create_time' => time(),
            'creator' => $username,
            'creator_id' => $user_id,
        ];
        (new AgentSiteLogModel())->add($insert_data);
    }

    public function switchGameLog($platform, $agent_ids, $ori_game_id, $game_id, $user_id, $username)
    {
        $game_list = (new V2DimGameIdModel())->getListInGameId($platform, [$ori_game_id, $game_id]);
        $ori_game_name = $game_list->where('game_id', $ori_game_id)->first()->game_name ?? '';
        $game_name = $game_list->where('game_id', $game_id)->first()->game_name ?? '';
        $insert_data = [];
        foreach ($agent_ids as $agent_id) {
            $insert_data[] = [
                'platform' => $platform,
                'agent_id' => $agent_id,
                'content' => "游戏由{$ori_game_name}变为{$game_name}",
                'create_time' => time(),
                'creator' => $username,
                'creator_id' => $user_id,
            ];
        }
        (new AgentSiteLogModel())->add($insert_data);
    }

    public function setAPKLog(array $data, $type, $user_id, $username)
    {
        $game_info = (new V2DimGameIdModel())->getDataByGameId($data['platform'], $data['game_id']);
        $game_name = $game_info->game_name ?? '';
        if ($type === 'site') {
            $log_str = "{$game_name}重新打包";
        } else {
            $log_str = "所有{$game_name}重新打包";
        }
        (new AgentSiteLogModel())->add([
            'platform' => $data['platform'],
            'agent_id' => $data['agent_id'],
            'site_id' => $data['site_id'],
            'content' => $log_str,
            'create_time' => time(),
            'creator' => $username,
            'creator_id' => $user_id,
        ]);
    }

    public function batchSetAPKLog($platform, $agent_id, $site_ids, $game_id, $user_id, $username)
    {
        $game_info = (new V2DimGameIdModel())->getDataByGameId($platform, $game_id);
        $game_name = $game_info->game_name ?? '';
        $log_str = "切换游戏,{$game_name}重新打包";
        $data = [];
        foreach ($site_ids as $site_id) {
            $data[] = [
                'platform' => $platform,
                'agent_id' => $agent_id,
                'site_id' => $site_id,
                'content' => $log_str,
                'create_time' => time(),
                'creator' => $username,
                'creator_id' => $user_id,
            ];
        }
        (new AgentSiteLogModel())->add($data);
    }
}
