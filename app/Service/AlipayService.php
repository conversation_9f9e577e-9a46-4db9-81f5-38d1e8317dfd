<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\TrinoTask\AlipayTaskModel;
use App\Param\MediaAccountInfoParam;
use Common\EnvConfig;

class AlipayService
{
    public function addAccount($data)
    {
        $platform = $data["platform"];
        $account_id = $data["account_id"];
        $account_name = $data["account_name"];
        $biz_token = trim($data["access_token"]);
        $ali_public_key = trim($data["alipay_public_key"]);
        $ali_app_public_key = trim($data["alipay_app_public_key"]);
        $ali_app_private_key = trim($data["alipay_app_private_key"]);
        $account_type = $data['account_type'];
        $company_short_name = $data['company_short_name'];
        $developer_app_id = $data['developer_app_id'];

        if (empty($account_id)) {
            throw new AppException('请输入账号ID');
        }

        $message = "";
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;

        $service = new MediaService();

        if (!is_numeric($account_id)) {
            $message .= "{$account_id}-含有非数字字符;";
            return $message;
        }

        // 检查应用IP白名单
        if ($account_type == 2) {
            $pull_bill_active = (new AlipayTaskModel())->pullBillLog($platform, $account_id, $developer_app_id, $biz_token, $ali_app_private_key);
            $pull_bill_white = (new AlipayTaskModel())->pullIsWhiteList($developer_app_id, $ali_app_private_key, $ali_public_key);
            if (
                $pull_bill_active['status'] !== 'SUCCESS'
                || $pull_bill_white['status'] !== 'SUCCESS'
                || strpos($pull_bill_white['result'], '当前调用IP不在可信名单中') === false
            ) {
                throw new AppException('请检查应用是否已上线或IP白名单、应用APP_ID、应用私钥配置是否正确');
            }
        }

        $tmp_message = $service->saveAuthAccount(new MediaAccountInfoParam([
            'media_type' => MediaType::ALIPAY,
            'platform' => $platform,
            'account_id' => $account_id,
            'account_name' => $account_name,
            'access_token' => "",
            'access_token_expires' => 0,
            'refresh_token' => '',
            'refresh_token_expires' => 0,
            'majordomo_name' => "",
            'account_password' => "",
            'agent' => $developer_app_id,
            'ext' => [
                "account_type" => $account_type,
                "public_key" => $ali_public_key, // 支付宝公钥
                "app_public_key" => $ali_app_public_key, // 应用公钥
                "app_private_key" => $ali_app_private_key, // 应用私钥
                "biz_token" => $biz_token, // 灯火token 市场拿账号给支付宝运营获取得来
                "company_short_name" => $company_short_name, // 归属主体简称
            ],
        ]), $creator_id, $creator_name);
        $message .= "$tmp_message;";

        return $message;
    }
}
