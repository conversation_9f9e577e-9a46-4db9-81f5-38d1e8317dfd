<?php

namespace App\Service;


use App\Constant\EnterpriseDiDiEmployeeConfig;
use App\Exception\AppException;
use App\Model\HttpModel\DiDi\ApprovalModel;
use App\Model\HttpModel\DiDi\AuthModel;
use App\Model\HttpModel\DiDi\MemberModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\SqlModel\Zeda\DiDiTravelPartnerModel;
use App\Model\SqlModel\Zeda\EnterpriseDidiEmployeeModel;
use App\Model\SqlModel\Zeda\EnterpriseDidiRegulationIDModel;
use App\Model\SqlModel\Zeda\FeiShuUserModel;
use App\MysqlConnection;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Common\EnvConfig;
use DateTime;
use Redis;
use RedisException;

/**
 * 企业滴滴
 */
class EnterpriseDiDiService
{

    protected $data = [
        'type'            => 1, // 1审批发起人，2被共享额度的人
        'approval_code'   => '审批实例id',
        'share_user_list' => ['xxx', 'xxxx', 'xxxx', 'xxxx'], // 多个被共享额度的人id
    ];

    /**
     * 特殊制度，这两个制度是80元上限
     */
    const SPECIAL_REGULATION_ID = [
        '1125964049896809',
        '1125964050696743',
        '1125968712533951',
        '1125968712221400',
    ];

    const AS_KEY = 'didi_access_token_';


    /**
     * 22点打卡后的用户集合
     */
    const CLOCK_OUT_SET_KEY = 'clock_out_set_';

    /**
     * 外出审批的审批code
     */
    const OUT_APPROVAL_CODE = 'E83AE510-3A47-4126-B7B0-9C0ACE8DB07D';

    /**
     * 加班共享打车额度的审批code
     */
    const CLOCK_OUT_APPROVAL_CODE = '744AD97C-BF36-414C-A8FF-06804111B5FF';

    const SYNC_USER_LOCK_KEY = 'sync_user_lock_key_didi';


    /**
     * @param $input_data
     * @return void
     * @throws RedisException
     */
    public function syncUser($input_data)
    {
        $logger = Helpers::getLogger('didi-employee');
        $logger->info('任务开始', ['data' => $input_data]);

        $company = $input_data['company'];
        $resign_date = $input_data['resign_date'] ?? '';
        if (!EnvConfig::DD[$company]) {
            $logger->info('主体不存在');
            throw new AppException('主体不存在');
        }

        if (strlen($input_data['phone']) !== 11) {
            throw new AppException('手机号码不正确');
        }

        // 判断员工是否存在，存在则修改，不存在则新增
        $model = new EnterpriseDidiEmployeeModel();
        $member_model = new MemberModel();

        // 用户数据
        $data = [
            'member_type'       => 1, // 员工类型，工号
            'phone'             => $input_data['phone'],
            'realname'          => $input_data['name'],
            'employee_number'   => $input_data['employee_no'],
            'use_company_money' => 1,
        ];
        if ($input_data['regulation_id']) {
            $data['regulation_id'] = $input_data['regulation_id'] . '_' . $input_data['out_regulation_id'];
        } else {
            $data['regulation_id'] = $input_data['out_regulation_id'];
        }

        // 存在离职日期，则需要删除
        if ($resign_date) {
            $this->deleteUser($company, $input_data['employee_no']);
            $logger->info('用户删除完成', ['employee_no' => $input_data['employee_no']]);
            return;
        }

        // 从滴滴那里获取员工信息 是否存在员工信息要以滴滴后台为准 因为会有其他奇怪的报错导致数据库跟滴滴后台的对不上，要以滴滴后台为准
        $dd_user_info = $this->didiUserInfo($input_data['employee_no']);

        // 存在，则修改。
        // 需要判断一下company是否修改了 如果修改了 则需要先删除再添加，如果没有改公司，就是普通的修改用户
        // 还需要判断一下数据库里面的company和滴滴企业后台的能不能对的上 ，对不上则删除后添加
        if ($dd_user_info) {
            if ($dd_user_info['company'] === $company) {
                $res = $member_model->editUser($company, $input_data['employee_no'], $data);
                if ($res['errno'] == 0) {
                    $logger->info('用户修改成功', $data);
                } else {
                    $logger->info('用户修改失败', ['errmsg' => $res['errmsg'], 'user_info' => $data]);
                    throw new AppException('用户修改失败');
                }
            } else {
                // 先删除用户，再添加
                $this->deleteUser($dd_user_info['company'], $input_data['employee_no']);
                // 新增
                $res = $member_model->addUser($company, $data);
                if ($res['errno'] == 0) {
                    $logger->info('用户添加成功', $data);
                } else {
                    $logger->info('用户添加失败', ['errmsg' => $res['errmsg'], 'user_info' => $data]);
                    throw new AppException('用户添加失败');
                }
            }


        } else {
            // 新增
            $res = $member_model->addUser($company, $data);
            if ($res['errno'] == 0) {
                $logger->info('用户添加成功', $data);
            } else {
                if ($res['errno'] == 50202) {
                    $logger->info('用户已存在，覆盖', $data);
                } else {
                    $logger->info('用户添加失败', ['errmsg' => $res['errmsg'], 'user_info' => $data]);
                    throw new AppException('用户添加失败');
                }
            }
        }


        // 入库
        $insert_data = [
            'phone'             => $input_data['phone'],
            'name'              => $input_data['name'],
            'company'           => $input_data['company'],
            'employee_no'       => $input_data['employee_no'],
            'regulation_id'     => $input_data['regulation_id'] ?? '',
            'out_regulation_id' => $input_data['out_regulation_id'],
            'regulation_name'   => $input_data['regulation_name'] ?? '',
        ];
        $model->addUser($insert_data);
    }

    private function deleteUser($company, $employee_no, $type = 1)
    {
        $logger = Helpers::getLogger('didi-employee');
        $model = new EnterpriseDidiEmployeeModel();
        $member_model = new MemberModel();
        $res = $member_model->deleteUser($company, $employee_no);

        if ($res['errno'] == 0) {
            $logger->info('滴滴后台用户删除成功', ['employee_no' => $employee_no]);
            if ($type == 1) {
                $model->deleteUser($employee_no);
                $logger->info('数据库删除成功');
            }
        } else {
            $logger->info('滴滴后台用户删除失败', ['errmsg' => $res['errmsg'], 'employee_no' => $employee_no]);
        }
    }


    /**
     * 同步所有用户到didi
     *
     * @return void
     * @throws RedisException
     */
    public function syncAllUser()
    {
        $logger = Helpers::getLogger('didi-user');
        $user_list = EnterpriseDiDiEmployeeConfig::EMPLOYEE_CONFIG;
        $member_model = new MemberModel();
        $model = new EnterpriseDidiEmployeeModel();
        $logger->info('任务开始');
        foreach ($user_list as $user_info) {
            $data = [
                'member_type'       => 1, // 员工类型，工号
                'phone'             => $user_info[2],
                'realname'          => $user_info[1],
                'employee_number'   => $user_info[0],
                'use_company_money' => 1,
                'regulation_id'     => $user_info[5] . '_' . $user_info[7],
            ];
            $company = $user_info[3];


            $res = $member_model->addUser($company, $data);
            if ($res['errno'] == 0) {
                $logger->info('用户添加成功', $user_info);
                $insert_data = [
                    'employee_no'       => $user_info[0],
                    'name'              => $user_info[1],
                    'phone'             => $user_info[2],
                    'company'           => $user_info[3],
                    'regulation_name'   => $user_info[4],
                    'regulation_id'     => $user_info[5],
                    'out_regulation_id' => $user_info[7],
                ];
                $res = $model->addUser($insert_data);
                $logger->info('用户入库成功', ['res' => $res]);
            } else {
                $logger->info('用户添加失败', ['errmsg' => $res['errmsg'], 'user_info' => $user_info]);
            }

        }

        $logger->info('任务完成');

    }


    /**
     * 加班拼车审批事件
     *
     * @return void
     */
    public function clockOutApproval($decrypt)
    {
        $logger = Helpers::getLogger('didi-clock-out');
        $logger->info('加班拼车审批通过', $decrypt);
        $instance_code = $decrypt['event']['instance_code'];

        try {
            // 审批通过，获取审批详情
            $tenant_access_token = (new \App\Model\HttpModel\Feishu\AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
            $approval_detail = (new \App\Model\HttpModel\Feishu\Approval\ApprovalModel())->getInstances($instance_code, $tenant_access_token);

            if ($approval_detail['code'] != 0) {
                throw new AppException('获取实例信息错误');
            }

            // 获取控件的值，有日期和拼车人
            $form_data = json_decode($approval_detail['data']['form'], true);

            // 日期时间
            $date = new DateTime($form_data[0]['value']);
            $date = $date->format('Y-m-d');
            $end_time = intval($approval_detail['data']['end_time'] / 1000);

            // 同行人，需要用open_id转换成工号
            $open_ids = $form_data[1]['open_ids'];

            $user_list = (new FeiShuUserModel())->getUserByOpenID(GroupAssistantService::APP_ID, $open_ids);

            // 审批人发起人
            $create_user = (new FeiShuUserModel())->getUserByOpenID(GroupAssistantService::APP_ID, $approval_detail['data']['open_id']);
            if ($create_user->isEmpty()) {
                $employee_no = $approval_detail['data']['user_id'];
            } else {
                $employee_no = $create_user->first()->employee_no;
            }

            // 判断发起人是否已经在同行人列表，如果在，不插入这条记录
            if ((new DiDiTravelPartnerModel())->inUserList($employee_no, $date)) {
                $logger->error('审批发起人已经在同行人列表里面，不应该发起审批', ['employee_no' => $employee_no, 'date' => $date]);
                return;
            }


            // 判断一下是否已经存在审批记录，存在则合并user_list
            $approval_info = (new DiDiTravelPartnerModel())->getDataByPk($employee_no, $date);
            $exist_user_list = [];
            if ($approval_info->isNotEmpty()) {
                $exist_user_list = json_decode($approval_info->first()->user_list, true);
                $exist_user_list = array_column($exist_user_list, null, 'employee_no');
                $logger->info('存在之前的记录', ['employee_no' => $employee_no, 'date' => $date, 'exist_user_list' => $exist_user_list, 'user_list' => $user_list->pluck('employee_no')->toArray()]);
            }


            // 每个同行人都要判断一下状态，判断是否有效打卡
            $format_user_list = [];
            foreach ($user_list as $user_info) {
                if (isset($exist_user_list[$user_info->employee_no])) {
                    continue;
                }
                $format_user_list[$user_info->employee_no] = [
                    'employee_no' => $user_info->employee_no,
                    'status'      => $this->availableClockOut($user_info->employee_no, $date) === TRUE ? 1 : 0, // 是否有效打卡
                    'is_use'      => 0, // 是否被使用过
                ];
            }
            // 合并 exist_user_list的值，需要覆盖前的值，以exist_user_list为准。
            $format_user_list = array_values(array_merge($format_user_list, $exist_user_list));


            // 入库
            $insert_data = [
                'employee_no'   => $employee_no,
                'user_list'     => $format_user_list,
                'approval_code' => $instance_code,
                'date'          => $date,
            ];
            (new DiDiTravelPartnerModel())->addData($insert_data);

            // 判断审批通过时间是否超过十点,并且审批用车时间是当天
            $end_hour = date('G', $end_time);
            if ($end_hour >= 22 || $end_hour < 6) {
//            if (1) {
                // 处理审批人逻辑
                $create_result = $this->handlerApprovalCreate($employee_no, $date, $format_user_list);

                // 处理同行人逻辑 申请人正常使用共享金额的时候，才需要处理同行人
                if ($create_result) {
                    $this->handlerTravelPartner($format_user_list, $date);
                }


                $logger->info('任务结束', ['instance_code' => $instance_code]);
            } else {
                $logger->info('审批时间没超过十点,不需要处理其他逻辑,任务结束', ['instance_code' => $instance_code, 'date' => $date, 'end_hour' => $end_hour]);
            }

        } catch (\Throwable $exception) {
            $logger->error('拼车审批运行错误，错误信息：' . $exception->getMessage(), $decrypt);
        }

    }


    /**
     * 处理发起人逻辑
     * @param $employee_no
     * @param $date
     * @param $user_list
     * @param string $trigger_user 触发的用户 空表示审批通过触发的
     * @return bool
     * @throws RedisException
     */
    private function handlerApprovalCreate($employee_no, $date, $user_list, string $trigger_user = '')
    {
        $logger = Helpers::getLogger('didi-clock-out');
        $logger->info('处理审批人逻辑', ['employee_no' => $employee_no, 'date' => $date, 'user_list' => $user_list, 'trigger_user' => $trigger_user]);
        // 判断发起人是否存在有效打卡，不存在，则不需要处理发起人
        if (!$this->availableClockOut($employee_no, $date)) {
            $logger->info('发起人没有有效打卡，不需要处理发起人', ['employee_no' => $employee_no, 'date' => $date, 'trigger_user' => $trigger_user]);
            return false;
        }

        // 获取用户制度配置
        $employee_no_list = array_column($user_list, 'employee_no');
        $employee_no_list[] = $employee_no;
        $employee_no_list = (new EnterpriseDidiEmployeeModel())->getList($employee_no_list)->keyBy('employee_no')->toArray();

        // 需要创建审批单的情况
        // 1. 用户没有审批单
        // 2. 用户需要更新审批单

        // 先判断用户之前是否存在审批单
        $created = RedisCache::getInstance()->get($this->genClockOutCreateKey($employee_no, $date));

        // 判断用户是否需要更新审批单
        $update = false;
        // 审批通过触发的，没有指定触发用户的，则一定需要更新
        if ($trigger_user === '') {
            $update = true;
        } else {
            // 存在is_use = 0的才需要判断，否则一定不需要更新
            if (in_array(0, array_column($user_list, 'is_use'), true)) {
                foreach ($user_list as $item) {
                    if ($item['employee_no'] === $trigger_user && $item['is_use'] === 0) {
                        $update = true;
                    }
                }
            }
        }

        // 如果已经存在过审批单并且不需要更新，则流程结束
        if ($created && $update === false) {
            $logger->info('已经存在过审批单并且不需要更新，流程结束', ['employee_no' => $employee_no, 'user_list' => $user_list]);
            return false;
        }


        // 重建审批单。遍历用户获取金额
        // 基础金额从原来的审批单里面拿
        $ori_data = [];
        if ($created) {
            $ori_data = unserialize($created);
            $base_money = $ori_data['money'];

            // 如果原来的审批单存在，则在更新之前需要判断一下用户原来的审批单是否已经进行中，如果是，则本次拼车无效
            if ($this->isApplicationUsed($ori_data['company'], $ori_data['approval_id'])) {
                $logger->info('该发起用户的申请单已经在进行中或者已经结束，不能再使用', ['employee_no' => $employee_no]);
                return false;
            }

        } else {
            $base_money = $this->getMoneyByRegulationID($employee_no_list[$employee_no]->regulation_id);
        }

        //回滚的数据
        $rollback = [];
        foreach ($user_list as &$user_info) {
            if (!$user_info['status']) {
                continue;
            }

            // 有效打卡用户还需要判断一下是否已经被使用过，被使用过是不可以重新被分享的（全局判断）
            if ($this->sharMoneyUse($user_info['employee_no'], $date)) {
                $logger->info('该用户已经被共享过额度，不能再使用', ['employee_no' => $user_info['employee_no']]);
                continue;
            }

            // 有效打卡用户还需要判断一下是否已经被使用过，被使用过是不可以重新被分享的（本单自己判断，有点多余，也可以不用判断，正常情况下不可能进来这个逻辑，但是需要标记）
            if ($user_info['is_use'] == 1) {
                $logger->info('该用户已经使用过，不能再使用', ['employee_no' => $user_info['employee_no']]);
                continue;
            }

            // 再判断一下当前分享用户的申请单是否已经在进行中或者已经结束，也就是被自己用掉了。需要先判断一下用户是否有申请单
            $share_user_created = RedisCache::getInstance()->get($this->genClockOutCreateKey($user_info['employee_no'], $date));
            if ($share_user_created) {
                $share_user_ori_data = unserialize($share_user_created);
                if ($this->isApplicationUsed($share_user_ori_data['company'], $share_user_ori_data['approval_id'])) {
                    $logger->info('该用户的申请单已经在进行中或者已经结束，不能再使用', ['employee_no' => $user_info['employee_no']]);
                    continue;
                }
            }

            // 累加
            $money = $this->getMoneyByRegulationID($employee_no_list[$user_info['employee_no']]->regulation_id);
            $base_money += $money;

            // 全局标记已经使用
            $shar_key = $this->genSharMoneyUserKey($user_info['employee_no'], $date);
            RedisCache::getInstance()->set($shar_key, 1, ['ex' => 86400]);

            // 本次拼车审批的标记
            $user_info['is_use'] = 1;

            // 记录一下，等会失败要回退,记录一个key就行了
            $rollback[] = $shar_key;
        }

        // 创建申请单
        $business_trip_detail = [
            'start_time'           => date("Y-m-d H:i:s"),
            'end_time'             => date("Y-m-d H:i:s", strtotime("+8 hour")),
            'perorder_money_quota' => $base_money,
            'trip_times'           => 1,
        ];
        $result = (new ApprovalModel())->create($employee_no_list[$employee_no]->company, $employee_no_list[$employee_no]->regulation_id,
            $employee_no_list[$employee_no]->phone, $employee_no, 3, $business_trip_detail
        );
        if ($result['errno'] == 0) {
            $approval_id = $result['data']['approval_id'];
            $logger->info('创建申请单成功', ['employee_no' => $employee_no, 'approval_id' => $approval_id, 'business_trip_detail' => $business_trip_detail]);
            // 更新数据库
            (new DiDiTravelPartnerModel())->updateUserListByEmployee($user_list, $employee_no, $date);
            $logger->info('审批记录的数据库更新成功', ['employee_no' => $employee_no, 'user_list' => $user_list, 'date' => $date]);

            // 取消原来的审批单
            if ($created) {
                $cancel_result = (new ApprovalModel())->cancel($ori_data['company'], $ori_data['approval_id']);
                $logger->info('取消用户原来的审批单', ['cancel_result' => $cancel_result, 'employee_no' => $employee_no]);
            }


            // 记录缓存，8个小时内不能再创建
            $cache_data = [
                'approval_id'   => $approval_id,
                'company'       => $employee_no_list[$employee_no]->company,
                'regulation_id' => $employee_no_list[$employee_no]->regulation_id,
                'date'          => $date,
                'money'         => $base_money,
            ];
            RedisCache::getInstance()->set($this->genClockOutCreateKey($employee_no, $date), serialize($cache_data), ['ex' => 8 * 3600]);
            return true;
        } else {
            $logger->error('创建申请单失败，回滚数据', ['result' => $result, 'employee_no' => $employee_no]);
            foreach ($rollback as $item_key) {
                RedisCache::getInstance()->del($item_key);
            }

            return false;
        }

    }

    /**
     * 如果原来的同行人有审批单，则取消。
     *
     * @param $user_list
     * @param $date
     * @return void
     * @throws RedisException
     */
    private function handlerTravelPartner($user_list, $date)
    {
        $logger = Helpers::getLogger('didi-clock-out');
        $logger->info('处理同行人逻辑', ['user_list' => $user_list, 'date' => $date]);
        foreach ($user_list as $user_info) {
            // 非有效打卡状态，肯定没有审批单
            if (!$user_info['status']) {
                continue;
            }

            $employee_no = $user_info['employee_no'];
            // 存在有效打卡，则判断一下是否存在审批单（讲道理一般是存在的,因为打卡就会创建审批单）存在则撤销
            $ori_approval_data = RedisCache::getInstance()->get($this->genClockOutCreateKey($employee_no, $date));
            if ($ori_approval_data) {
                $ori_approval_data = unserialize($ori_approval_data);
                $cancel_result = (new ApprovalModel())->cancel($ori_approval_data['company'], $ori_approval_data['approval_id']);
                $logger->info('取消同行用户原来的审批单', ['cancel_result' => $cancel_result, 'employee_no' => $employee_no]);
            } else {
                $logger->info('同行人没有审批单，不需要取消', ['employee_no' => $employee_no]);
            }
        }

    }


    /**
     * 外出审批
     *
     * @param $decrypt
     * @return void
     * @throws RedisException
     */
    public function outApproval($decrypt)
    {
        $logger = Helpers::getLogger('didi-out-approval');
        $logger->info('审批单通过', $decrypt);
//        $open_id = $decrypt['event']['open_id'];
        // 审批单实例的code值
        $instance_code = $decrypt['event']['instance_code'];

        // 获取审批详情
        $tenant_access_token = (new \App\Model\HttpModel\Feishu\AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        $approval_detail = (new \App\Model\HttpModel\Feishu\Approval\ApprovalModel())->getInstances($instance_code, $tenant_access_token);

        if ($approval_detail['code'] != 0) {
            $logger->info('获取实例信息错误', $decrypt);
            throw new AppException('获取实例信息错误');
        }
        $open_id = $approval_detail['data']['open_id'];

        // 通过open_id 去获取用户的工号
        $user_info = (new FeiShuUserModel())->getUserByOpenID(GroupAssistantService::APP_ID, $open_id);
        if ($user_info->isEmpty()) {
            $logger->info('用户不存在', $decrypt);
            // 尝试拿兜底的use_id
            $employee_no = $approval_detail['data']['user_id'];
            $logger->info('用户不存在，尝试拿兜底的use_id', ['employee_no' => $employee_no]);
        } else {
            // 工号
            $employee_no = $user_info->where('employee_no', "!=", "")->first()->employee_no;
            // 兜底
            if (!$employee_no) {
                $employee_no = $approval_detail['data']['user_id'];
                $logger->info('用户不存在，尝试拿兜底的use_id', ['employee_no' => $employee_no]);
            }
        }

//        // 验证白名单 TODO
//        if (!$this->checkWhiteList($employee_no, $logger)) {
//            return;
//        }

        // 申请单创建的Redis key
        $create_key = 'didi_out_approval_' . $instance_code;

        // 判断一下是否重复触发
        $approval_data = RedisCache::getInstance()->get($create_key);
        if ($approval_data) {
            $logger->info('重复触发', ['decrypt' => $decrypt]);
            return;
        }

        $data = (new EnterpriseDidiEmployeeModel())->getData($employee_no);
        if ($data->isEmpty()) {
            $logger->info('不在员工配置表里面，尝试去滴滴后台获取', ['employee_no' => $employee_no]);
            // 去滴滴后台看看有没有
            $dd_user_info = $this->getUserInfoByDidi($employee_no, 2);
            if (!$dd_user_info) {
                $logger->info('两次获取都不在员工配置表里面，流程结束', ['employee_no' => $employee_no]);
                return;
            }

            $data = $dd_user_info;
        } else {
            $data = (array)$data->first();
        }


        // 创建申请单
        $business_trip_detail = [
            'start_time' => $decrypt['event']['out_start_time'],
            'end_time'   => $decrypt['event']['out_end_time'],
            'trip_times' => 4,// 写死四次
        ];
        $result = (new ApprovalModel())->create($data['company'], $data['out_regulation_id'], $data['phone'], $employee_no, 2, $business_trip_detail);
        if ($result['errno'] == 0) {
            $approval_id = $result['data']['approval_id'];
            $logger->info('创建外出申请单成功', ['employee_no' => $employee_no, 'approval_id' => $approval_id]);
            // 记录缓存，两周过期，主要是审批单撤销的时候可以取消审批单 一般外出也不会超过两周
            $approval_data = ['approval_id' => $approval_id, 'company' => $data['company']];
            RedisCache::getInstance()->set($create_key, serialize($approval_data), ['ex' => 14 * 3600]);
        } else {
            $logger->error('创建外出申请单失败', ['result' => $result, 'employee_no' => $employee_no]);
        }

    }

    /**
     * 外出审批撤销
     *
     * @param $decrypt
     * @return void
     * @throws RedisException
     */
    public function outApprovalRevert($decrypt)
    {
        $logger = Helpers::getLogger('didi-out-approval');
        $logger->info('审批单取消', $decrypt);
        // 审批单实例的code值
        $instance_code = $decrypt['event']['instance_code'];

        // 申请单创建的Redis key
        $create_key = 'didi_out_approval_' . $instance_code;
        $approval_data = RedisCache::getInstance()->get($create_key);
        if ($approval_data) {
            $approval_data = unserialize($approval_data);
            $result = (new ApprovalModel())->cancel($approval_data['company'], $approval_data['approval_id']);
            $logger->info('审批单取消结果', ['res' => $result]);
            return;
        }

        $logger->info('获取不到审批数据,任务完成', $decrypt);
    }

    /**
     * 用户打卡
     *
     * @throws RedisException
     */
    public function userClockOut($decrypt)
    {
        $logger = Helpers::getLogger('didi-clock-out');
        $logger->info('用户打卡', $decrypt);
        // 用户工号
        $employee_no = $decrypt['event']['employee_no'];
        // 用户打卡时间戳
        $check_time = $decrypt['event']['check_time'];
        // 打卡日期
        $date = $this->getDate($check_time);

        // 申请单创建的Redis key
        $create_key = $this->genClockOutCreateKey($employee_no, $date);

        // 打卡事件只要大于22:00或者小于第二天6点就行
        $hour = date("G", $check_time);
        if (!($hour >= 22 || $hour < 6)) {
            return;
        }

//        // 验证白名单 TODO
//        if (!$this->checkWhiteList($employee_no, $logger)) {
//            return;
//        }

        // 验证配置
        $data = (new EnterpriseDidiEmployeeModel())->getData($employee_no);
        if ($data->isEmpty()) {
            $logger->info('不在员工配置表里面，尝试去滴滴后台获取', ['employee_no' => $employee_no]);
            // 去滴滴后台看看有没有
            $dd_user_info = $this->getUserInfoByDidi($employee_no, 1);
            if (!$dd_user_info) {
                $logger->info('两次获取都不在员工配置表里面，流程结束', ['employee_no' => $employee_no]);
                return;
            }

            $data = $dd_user_info;
        } else {
            $data = (array)$data->first();
        }
        // 加入打卡集合
        $this->addSet($employee_no, $date);

        // 判断用户是否有存在于拼车审批单里面
        $travel_partner_data = (new DiDiTravelPartnerModel())->getDataByEmployee($employee_no, $date);

        // 有审批，则处理审批流程
        if ($travel_partner_data->isNotEmpty()) {
            $logger->info('存在审批流,处理审批流逻辑', ['employee_no' => $employee_no]);
            // 判断自己是审批人还是同行人
            $travel_partner_data = (array)$travel_partner_data->first();

            // 获取锁
            $this->getLock($travel_partner_data['approval_code']);

            $user_list = json_decode($travel_partner_data['user_list'], true);
            // 自己是同行人
            if ($employee_no !== $travel_partner_data['employee_no']) {
                // 需要修改user_list里面的用户状态为有效打卡状态
                foreach ($user_list as &$item) {
                    if ($item['employee_no'] === $employee_no) {
                        $item['status'] = 1; // 有效打卡状态
                        break;
                    }
                }
                (new DiDiTravelPartnerModel())->updateUserList($user_list, $travel_partner_data['id']);
                $logger->info('自己是同行人，同行人的user_list更新成功', ['employee_no' => $employee_no, 'user_list' => $user_list]);
            }

            // 先触发审批人逻辑
            $create_result = $this->handlerApprovalCreate($travel_partner_data['employee_no'], $date, $user_list, $employee_no);
            // 处理同行人逻辑 申请人正常使用共享金额的时候，才需要处理同行人
            if ($create_result) {
                $this->handlerTravelPartner($user_list, $date);
            }

            $logger->info('流程结束', ['employee_no' => $employee_no, 'user_list' => $user_list]);
            $this->unlock($travel_partner_data['approval_code']);
            return;
        }

        $logger->info('没有审批流，正常逻辑', ['employee_no' => $employee_no]);

        // 缓存里面有，说明创建过了，不需要再创建
        $created = RedisCache::getInstance()->get($create_key);
        if ($created) {
            $logger->info('创建过一次，不能重复创建', ['employee_no' => $employee_no]);
            return;
        }


        // 创建申请单
        $perorder_money_quota = $this->getMoneyByRegulationID($data['regulation_id']);
        $business_trip_detail = [
            'start_time'           => date("Y-m-d H:i:s"),
            'end_time'             => date("Y-m-d H:i:s", strtotime("+8 hour")),
            'perorder_money_quota' => $perorder_money_quota,
            'trip_times'           => 1,
        ];
        $result = (new ApprovalModel())->create($data['company'], $data['regulation_id'], $data['phone'], $employee_no, 3, $business_trip_detail);
        if ($result['errno'] == 0) {
            $approval_id = $result['data']['approval_id'];
            $logger->info('创建申请单成功', ['employee_no' => $employee_no, 'approval_id' => $approval_id, 'business_trip_detail' => $business_trip_detail]);
            // 记录缓存，8个小时内不能再创建
            $cache_data = [
                'approval_id'   => $approval_id,
                'company'       => $data['company'],
                'regulation_id' => $data['regulation_id'],
                'date'          => $date,
                'money'         => $perorder_money_quota,
            ];
            RedisCache::getInstance()->set($create_key, serialize($cache_data), ['ex' => 8 * 3600]);
        } else {
            $logger->error('创建申请单失败', ['result' => $result, 'employee_no' => $employee_no]);
        }
    }


    /**
     * @return string
     * @throws RedisException
     */
    static public function getAccessToken($client_id, $client_secret, $sign_key)
    {
        // 先从缓存中获取
        $key = self::AS_KEY . $client_id;
        $access_token_info = RedisCache::getInstance()->get($key);
        if (!$access_token_info) {
            $access_token_info = (new AuthModel())->getAccessToken($client_id, $client_secret, $sign_key);
            if (isset($access_token_info['access_token'])) {
                RedisCache::getInstance()->set($key, serialize($access_token_info), ['ex' => 1500]); // 25分钟过期
                return $access_token_info['access_token'];
            } else {
                Helpers::getLogger('didi')->err('获取企业滴滴的access_token失败', ['res' => $access_token_info]);
                throw new AppException('获取企业滴滴的access_token失败');
            }
        }

        $access_token_info = unserialize($access_token_info);
        return $access_token_info['access_token'];
    }


    /**
     * 判断员工是否存在滴滴企业后台。要多个公司轮询去判断
     *
     * @param string $employee_no
     * @return array
     */
    public function didiUserInfo(string $employee_no)
    {
        $company_list = EnvConfig::DD;
        $member_model = new MemberModel();
        foreach ($company_list as $company => $company_info) {
            $result = $member_model->userInfo($company, $employee_no);
            if ($result['errno'] == 0 && !empty($result['data'])) {
                $result['data']['company'] = $company;
                return $result['data'];
            }
        }

        return [];
    }

    /**
     * @param $employee_no
     * @param $logger
     * @return bool
     */
    private function checkWhiteList($employee_no, $logger)
    {

        $white_list = [
            'TW5741',
            'TW4343',
            'TW1908',
            'TW6513',
            'TW0087',
            'TW4573',
            'TW0985',
            'TW0845',
            'TW1720',
            'TW2871',
            'TW5809',
            'TW5321',
            'TW1807',
            'TW1445',
            'TW1031',
            'TW0381',
            'TW0226',
            'TW2651',
            'TW0328',
            'TW0711',
        ];

        if (!in_array($employee_no, $white_list)) {
            $logger->info('不在白名单里面，先不需要创建审批单', ['employee_no' => $employee_no]);
            return false;
        }
        return true;
    }

    /**
     * @param $employee_no
     * @param $date
     * @return void
     * @throws RedisException
     */
    private function addSet($employee_no, $date)
    {
        $redis = RedisCache::getInstance();
        $key = self::CLOCK_OUT_SET_KEY . $date;

        // 判断集合是否存在
        if (!$redis->exists($key)) {
            $redis->sAdd($key, $employee_no);
            $redis->expire($key, 3600 * 8);
        } else {
            $redis->sAdd($key, $employee_no);
        }
    }

    /**
     * 判断是否是有效打卡的工号
     * @param $employee_no
     * @param $date
     * @return bool|Redis
     * @throws RedisException
     */
    private function availableClockOut($employee_no, $date)
    {
        $redis = RedisCache::getInstance();
        $key = self::CLOCK_OUT_SET_KEY . $date;
        return $redis->sismember($key, $employee_no);
    }

    private function genAvailableClockOutKey($date)
    {
        return self::CLOCK_OUT_SET_KEY . $date;
    }

    /**
     * 获取日期
     *
     * @param $check_time
     * @return false|string
     */
    private function getDate($check_time)
    {
        // 0-6点算上一天，其他算当天
        $hour = date("G", $check_time);
        if ($hour < 6) {
            return date("Y-m-d", strtotime("-1 days"));
        }
        return date("Y-m-d", $check_time);

    }

    public function cleanCache($employee_no, $date)
    {
        $key1 = $this->genClockOutCreateKey($employee_no, $date);
        $key2 = $this->genSharMoneyUserKey($employee_no, $date);
        $key3 = $this->genAvailableClockOutKey($date);

        RedisCache::getInstance()->del($key1, $key2, $key3);
    }

    private function sharMoneyUse($employee_no, $date)
    {
        $key = $this->genSharMoneyUserKey($employee_no, $date);
        $result = RedisCache::getInstance()->get($key);
        if ($result) {
            return true;
        }
        return false;
    }

    private function genClockOutCreateKey($employee_no, $date)
    {
        return 'didi_clock_out_' . $date . '_' . $employee_no;
    }

    private function genSharMoneyUserKey($employee_no, $date)
    {
        return 'didi_shar_money_' . $date . '_' . $employee_no;
    }

    public function getMoneyByRegulationID($regulation_id)
    {
        if (in_array($regulation_id, self::SPECIAL_REGULATION_ID, true)) {
            return 8000;// 80元 8000分
        }

        return 5000; // 50元 5000分
    }

    /**
     * 判断当前申请单是否已经被使用过
     *
     * @param $company
     * @param $approval_id
     * @return bool
     * @throws RedisException
     */
    public function isApplicationUsed($company, $approval_id)
    {
        $data = (new ApprovalModel())->getOrder($company, $approval_id);

        // 说明申请单已经被使用过
        if ($data['errno'] == 0 && $data['data']['total'] > 0) {
            return true;
        }

        return false;
    }

    /**
     * 测试用
     *
     * @param $approval_id_list
     * @return void
     * @throws RedisException
     */
    public function cancelApproval($approval_id_list)
    {
        $logger = Helpers::getLogger('didi-clock-out');
        $company = '广州中旭未来科技有限公司';
        foreach ($approval_id_list as $approval_id) {
            $cancel_result = (new ApprovalModel())->cancel($company, $approval_id);
            $logger->info('取消申请单成功', $cancel_result);
        }

    }


    /**
     * 获取锁，获取不到则等待两分钟
     * 两分钟后无论如何都返回
     *
     * @param $instance_code
     * @return bool
     * @throws RedisException
     */
    public function getLock($instance_code)
    {
        $logger = Helpers::getLogger('didi-clock-out');
        $redis = RedisCache::getInstance();
        $wait_time = 0;
        $retry_interval = 500000; // 500ms
        $timeout = 120;// 120s 两分钟
        $lock_key = $this->genLockKey($instance_code);

        while ($wait_time < $timeout * 1000000) {
            // 尝试获取锁
            if ($redis->setnx($lock_key, 1)) {
                // 设置锁的过期时间
                $redis->expire($lock_key, $timeout);
                return true;
            }

            $logger->info('获取不到锁，等待一段时间后重试', ['instance_code' => $instance_code]);
            // 等待一段时间后重试
            usleep($retry_interval);
            $wait_time += $retry_interval;
        }

        return true;
    }

    /**
     * 解锁
     *
     * @param $instance_code
     * @return void
     * @throws RedisException
     */
    public function unlock($instance_code)
    {
        $lock_key = $this->genLockKey($instance_code);
        RedisCache::getInstance()->del($lock_key);
    }

    private function genLockKey($instance_code)
    {
        return 'didi_clock_out_lock_' . $instance_code;
    }

    /**
     * 获取锁，获取不到则等待两分钟
     * 两分钟后无论如何都返回
     *
     * @return bool
     * @throws RedisException
     */
    public function getSyncUserLock($data)
    {
        $logger = Helpers::getLogger('didi-employee');
        $redis = RedisCache::getInstance();
        $wait_time = 0;
        $retry_interval = 150000; // 150ms
        $timeout = 120;// 600 2分钟
        $lock_key = self::SYNC_USER_LOCK_KEY;

        while ($wait_time < $timeout * 1000000) {
            // 尝试获取锁
            if ($redis->setnx($lock_key, 1)) {
                // 设置锁的过期时间
                $redis->expire($lock_key, $timeout);
                return true;
            }

            // 等待一段时间后重试
            usleep($retry_interval);
            $wait_time += $retry_interval;
        }

        $logger->info('达到最大等待时间，自动解锁', ['data' => $data]);
        return true;
    }

    /**
     * 解锁
     *
     * @return void
     * @throws RedisException
     */
    public function unSyncUserLock()
    {
        $lock_key = self::SYNC_USER_LOCK_KEY;
        RedisCache::getInstance()->del($lock_key);
    }

    /**
     * @param $employee_no
     * @param int $type 1=加班场景，2=外出场景
     * @return array
     */
    public function getUserInfoByDidi($employee_no, $type = 1)
    {
        // 去滴滴后台看看有没有
        $dd_user_info = $this->didiUserInfo($employee_no);
        if (!$dd_user_info) {
            return [];
        }

        // 获取制度
        $regulation_id_list = (new EnterpriseDidiRegulationIDModel())->getDataByType($type);
        // 匹配用户已有的制度，取交集
        $res_regulation_id = array_intersect($regulation_id_list, $dd_user_info['regulation_id']);
        if (empty($res_regulation_id)) {
            Helpers::getLogger('didi')->error('匹配不到制度', ['didi_user_info' => $dd_user_info, 'regulation_id' => $regulation_id_list]);
            return [];
        }

        // 保持结构一致
        if ($type === 1) {
            $dd_user_info['regulation_id'] = $res_regulation_id[0];
        } else {
            $dd_user_info['out_regulation_id'] = $res_regulation_id[0];
        }

        return $dd_user_info;
    }
}