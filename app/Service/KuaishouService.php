<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Model\HttpModel\Kuaishou\DMP\PopulationModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouAccountLogModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\AudienceUploadFileModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use App\Utils\Helpers;
use ZipArchive;

class KuaishouService implements AccountLeader, DMP
{

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $audience_change_log_model = new AudienceChangeLogModel();
        $audience_upload_file_model = new AudienceUploadFileModel();
        $device_task_info = (new DeviceTaskModel())->getData($device_task_id);
        $file_list = json_decode($device_task_info->file_list, true);
        $nowadays = date('Y-m-d');
        if ($device_task_info->auto_update === DeviceTaskModel::FULL) {
            $audience_upload_file_map = $audience_upload_file_model
                ->getLastRecordsByDeviceTaskId($device_task_id, strtotime($nowadays))
                ->pluck('id', 'name')
                ->toArray();
        }
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::KUAISHOU);

            foreach ($file_list as $file) {
                if ($file['row'] <= 0) {
                    continue;
                }

                // 快手无OPEN_ID类型，过滤不处理
                if ($file['data_type'] == UserIDType::OPEN_ID) {
                    continue;
                }

                $audience_upload_file_name = "task-{$device_task_id}-all-" . UserIDType::FILE_NAME_MAP[$file['data_type']] . ".txt";
                $device_audience_name = "{$audience_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']];
                $table_audience_id = $audience_model->add(
                    $user_id,
                    $username,
                    $device_task_id,
                    MediaType::KUAISHOU,
                    $company['name'],
                    $seed_account,
                    $device_audience_name,
                    $audience_desc,
                    $push_account_list,
                    AudienceModel::TYPE_EXPORT,
                    0,
                    '',
                    $media_account_info->platform,
                    $data_source_type
                );
                $table_audience_ids[] = $table_audience_id;
                // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
                ///** @uses \App\Task\KuaishouTask::createAudience() */
                //Container::getServer()->task(['action' => 'KuaishouTask.createAudience', 'data' => ['table_audience_id' => $table_audience_id]]);
                if ($device_task_info->auto_update === DeviceTaskModel::FULL) {
                    if (!isset($audience_upload_file_map[$audience_upload_file_name])) {
                        copy(DeviceService::getFile($device_task_id, $file['data_type']), AudienceService::AUDIENCE_DIR . '/' . $audience_upload_file_name);
                        $file_desc = "{$nowadays} dmp任务id:{$device_task_id}首次推送的文件";
                        $audience_upload_file_map[$audience_upload_file_name] = $audience_upload_file_model->add(
                            $audience_upload_file_name,
                            md5_file(AudienceService::AUDIENCE_DIR . '/' . $audience_upload_file_name),
                            $file['data_type'],
                            $file_desc,
                            AudienceUploadFileModel::SYSTEM_UPLOAD,
                            $file['row']
                        );
                    }
                    $audience_change_log_model->add(
                        1,
                        '超管',
                        $table_audience_id,
                        0,
                        $audience_upload_file_map[$audience_upload_file_name],
                        AudienceChangeLogModel::RESET,
                        MediaType::KUAISHOU,
                        strtotime('-1 day', strtotime($nowadays)),
                        AudienceChangeLogModel::SUCCESS
                    );
                }
            }
        }
        return $table_audience_ids;
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::KUAISHOU);
            $table_audience_id = $audience_model->add(
                $user_id,
                $username,
                $device_task_id,
                MediaType::KUAISHOU,
                $company['name'],
                $seed_account,
                $audience_name,
                $audience_desc,
                $push_account_list,
                AudienceModel::TYPE_FILE_ADDITION,
                0,
                '',
                $media_account_info->platform,
                $data_source_type
            );
            $table_audience_ids[] = $table_audience_id;
            // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
        }
        return $table_audience_ids;
    }

    public function editPushAccountList($id, $account_ids)
    {
        $audience_model = new AudienceModel();
        $audience_info = $audience_model->getData($id);
        $media_model = new MediaAccountModel();
        $account_info = $media_model->getDataByAccountId($audience_info->account_id);
        $population = new PopulationModel();
        $population->accountPush($audience_info->account_id, $account_info->access_token, $audience_info->audience_id, $account_ids);
        $already_push_account_list = json_decode($audience_info->push_account_list, true);
        return $audience_model->editPushAccountList($id, json_encode(array_merge($already_push_account_list, $account_ids)));
    }

    /**
     * 创建数据源文件
     *
     * @param int $file_uuid
     * @param array $file
     * @return array|bool
     * <AUTHOR>
     */
    public function createDataSourceFile($file_uuid, $file)
    {
        $row_limit = 4000000;
        $file_count = 0;
        $zip_paths = [];
        $file_index = 0;
        $filename = $file['name'];
        if (!file_exists($filename)) {
            Helpers::getLogger('audience')->error("file not exists", [
                'file_uuid' => $file_uuid,
                'filename' => $filename,
                'media_type' => MediaType::KUAISHOU,
            ]);
            return [];
        }
        $handle = fopen($filename, 'r');
        while (!feof($handle)) {
            // md5 一行 33字节 (带换行)
            $content = Helpers::getFileContent($handle, $row_limit, $file['need_md5']);
            if (empty($content)) {
                break;
            }
            file_put_contents(AudienceService::getAudienceDir(MediaType::KUAISHOU) . "/$file_uuid-{$file_count}.txt", $content);
            $file_count++;
        }
        fclose($handle);
        for (; $file_index < $file_count; $file_index++) {
            $zip_path = AudienceService::getAudienceDir(MediaType::KUAISHOU) . "/$file_uuid-{$file_index}.zip";
            $zip = new ZipArchive;
            $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
            if ($code === true) {
                $zip->addFile(AudienceService::getAudienceDir(MediaType::KUAISHOU) . "/$file_uuid-{$file_index}.txt", "$file_uuid-{$file_index}.txt");
                $zip->close();
                unlink(AudienceService::getAudienceDir(MediaType::KUAISHOU) . "/$file_uuid-{$file_index}.txt");
                $zip_paths[] = $zip_path;
            } else {
                Helpers::getLogger('audience')->error("create data source zip fail", [
                    'file_uuid' => $file_uuid,
                    'zip_error_code' => $code,
                    'media_type' => MediaType::KUAISHOU,
                ]);
            }
        }
        return $zip_paths;
    }

    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsKuaishouAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }
}
