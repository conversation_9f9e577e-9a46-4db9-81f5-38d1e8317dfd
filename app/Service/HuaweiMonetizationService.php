<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Container;
use App\Model\HttpModel\Huawei\Ads\Oauth2Model;
use App\Param\MediaAccountInfoParam;

class HuaweiMonetizationService
{
    /**
     * @param $platform
     * @param $account_content
     * @return string
     */
    public function addAccount($platform, $account_content): string
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $account_content = trim($account_content);
        $account_lines = explode("\n", $account_content);

        $message = '';
        $service = new MediaService();
        $auth_model = new Oauth2Model();
        foreach ($account_lines as $account_line) {
            $account_item = explode(' ', trim($account_line));
            $account_id = $account_item[0] ?? '';
            $name = $account_item[1] ?? '';
            $secret = $account_item[2] ?? '';
            if (empty($account_id) || empty($secret)) {
                continue;
            }
            if (count($account_item) < 3) {
                $message .= "{$account_id}-信息不全，请检查;";
                continue;
            }
            if (!is_numeric($account_id)) {
                $message .= "{$account_id}-含有非数字字符;";
                continue;
            }

            // 获取token
            try {
                $token_info = $auth_model->clientAccessToken($account_id, $secret);
            } catch (\Throwable $e) {
                $message .= "{$account_id}-获取token失败，错误信息：" . $e->getMessage();
                continue;
            }

            $tmp_message = $service->saveAuthAccount(new MediaAccountInfoParam([
                'media_type' => MediaType::HUAWEI_MONETIZATION,
                'platform' => $platform,
                'account_id' => $account_id, // 开发者APP_ID
                'account_password' => $secret, // 开发者密钥
                'account_name' =>  $name,
                'access_token' => $token_info['access_token'],
                'access_token_expires' => time() + $token_info['expires_in'],
                'refresh_token' => '',
                'refresh_token_expires' => 0,
                'majordomo_name' => '',
            ]), $creator_id, $creator_name);
            $message .= "$tmp_message;";
        }

        return $message;
    }
}
