<?php

namespace App\Service;

use App\Model\HttpModel\FeishuProject\AuthenModel;
use App\Model\HttpModel\FeishuProject\FieldModel;
use App\Model\HttpModel\FeishuProject\FileModel;
use App\Model\HttpModel\FeishuProject\UserModel;
use App\Model\HttpModel\FeishuProject\WorkItemModel;
use App\Model\RedisModel\FeishuProjectRedisModel;

class FeishuProjectService
{
    /**
     * plugin-token的使用必须结合user-key,飞书项目席位有限，固定用测试账号15521275182的user_key
     * @link https://project.feishu.cn/b/helpcenter/1p8d7djs/4bsmoql6#89672214
     */
    const USER_KEY = '7424348510570446849';

    /**
     * @var string
     */
    public $plugin_token = '';

    /**
     * @param $plugin_id
     * @param $plugin_secret
     */
    public function __construct($plugin_id, $plugin_secret)
    {
        $this->plugin_token = $this->getPluginAccessToken($plugin_id, $plugin_secret);
    }

    public function bizDownloadDemo()
    {
        $project_key = '6704a1fe0bce5e7bc6a6b90c';
        $work_item_type_key = '670cdda47cf044309ca89373';
        $work_item_id = '4973994462';
        $uuid = '9587ce2a-616b-4a8c-832a-fbfe25a8e1c3';// 图片

        $filename = TMP_DIR . '/feishu_project.jpg';
        $content = (new FileModel())->bizDownload($this->plugin_token, self::USER_KEY, $project_key, $work_item_type_key, $work_item_id, $uuid);

        file_put_contents($filename, $content);
        // 运行结果查看生成的$filename文件
    }

    /**
     * @param array $user_keys
     * @return array
     */
    public function getUserDetail(array $user_keys): array
    {
        return (new UserModel())->userDetail($this->plugin_token, $user_keys);
    }


    /**
     * @param $project_key
     * @param $work_item_type_key
     * @return array
     */
    public function getFieldDetail($project_key, $work_item_type_key): array
    {
        return (new FieldModel())->getFieldDetail($this->plugin_token, self::USER_KEY, $project_key, $work_item_type_key);
    }

    /**
     * @param $project_key
     * @param $work_item_type_key
     * @param array $work_item_ids
     * @param array $fields
     * @param array $expand
     * @return array
     */
    public function getWorkItemDetail($project_key, $work_item_type_key, array $work_item_ids, array $fields = [], array $expand = []): array
    {
        return (new WorkItemModel())->workItemDetail($this->plugin_token, self::USER_KEY, $project_key, $work_item_type_key, $work_item_ids);
    }

    /**
     * @param $project_key
     * @param $work_item_type_key
     * @param $work_item_id
     * @param $uuid
     * @return bool|string
     */
    public function bizDownload($project_key, $work_item_type_key, $work_item_id, $uuid)
    {
        return (new FileModel())->bizDownload($this->plugin_token, self::USER_KEY, $project_key, $work_item_type_key, $work_item_id, $uuid);
    }

    /**
     * @param $plugin_id
     * @param $plugin_secret
     * @return false|mixed|string
     */
    public function getPluginAccessToken($plugin_id, $plugin_secret)
    {
        $redis_mode = new FeishuProjectRedisModel();
        if ($redis_mode->getPluginAccessToken($plugin_id)) {
            $plugin_access_token = $redis_mode->getPluginAccessToken($plugin_id);
        } else {
            $result = (new AuthenModel())->getPluginAccessToken($plugin_id, $plugin_secret);
            $plugin_access_token = $result['token'];
            $expire = $result['expire_time'];

            $redis_mode->setPluginAccessTokenKey($plugin_id, $plugin_access_token, $expire);
        }
        return $plugin_access_token;
    }

    /**
     * @param $project_key
     * @param $work_item_type_key
     * @param $work_item_id
     * @param $update_feishu_data
     * @return array
     */
    public function updateField($project_key, $work_item_type_key, $work_item_id, $update_feishu_data)
    {
        return (new FieldModel())->updateField($this->plugin_token, self::USER_KEY, $project_key, $work_item_type_key, $work_item_id, $update_feishu_data);
    }
}
