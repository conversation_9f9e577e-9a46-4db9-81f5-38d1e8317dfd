<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Model\SqlModel\Zeda\AudienceUploadFileModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Service\MediaServiceInterface\DMP;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Utils\Helpers;
use Common\EnvConfig;

class JnPushService implements DMP
{
    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $device_task_info = (new DeviceTaskModel())->getData($device_task_id);
        $file_list = json_decode($device_task_info->file_list, true);
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);

            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::JN_PUSH);

            foreach ($file_list as $file) {
                if ($file['row'] <= 0) {
                    continue;
                }

                // 仅支持IMEI_MD5、OAID
                if (!in_array($file['data_type'], [UserIDType::IMEI_MD5, UserIDType::OAID])) {
                    continue;
                }
                $device_audience_name = "{$audience_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']] . '-0';
                $table_audience_id = $audience_model->add(
                    $user_id,
                    $username,
                    $device_task_id,
                    MediaType::JN_PUSH,
                    $company['name'],
                    $seed_account,
                    $device_audience_name,
                    $audience_desc,
                    $push_account_list,
                    AudienceModel::TYPE_EXPORT,
                    0,
                    '',
                    $media_account_info->platform,
                    0
                );
                $table_audience_ids[] = $table_audience_id;
                // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
                ///** @uses \App\Task\JNPushTask::createAudience() */
                //Container::getServer()->task(['action' => 'JNPushTask.createAudience', 'data' => ['table_audience_id' => $table_audience_id]]);
            }
        }
        return $table_audience_ids;
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];

        $upload_file_info = (new AudienceUploadFileModel())->getData($device_task_id);
        // 仅支持IMEI_MD5、OAID
        if (!in_array($upload_file_info->data_type, [UserIDType::IMEI_MD5, UserIDType::OAID])) {
            throw new AppException('仅支持IMEI_MD5、OAID文件');
        }

        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::JN_PUSH);
            $table_audience_id = $audience_model->add(
                $user_id,
                $username,
                $device_task_id,
                MediaType::JN_PUSH,
                $company['name'],
                $seed_account,
                $audience_name,
                $audience_desc,
                $push_account_list,
                AudienceModel::TYPE_FILE_ADDITION,
                0,
                '',
                $media_account_info->platform,
                0
            );
            $table_audience_ids[] = $table_audience_id;
            // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
        }
        return $table_audience_ids;
    }

    public function editPushAccountList($id, $account_ids)
    {
        throw new AppException('无推送接口');
    }

    public function createDataSourceURL($table_audience_id, array $file)
    {
        $file_count = 0;
        $row_limit = 2000000;
        $filename = $file['name'];
        $user_id_type = UserIDType::JN_PUSH_TYPE_MAP[$file['data_type']];
        if (!file_exists($filename)) {
            throw new AppException('file not exists');
        }

        $handle = fopen($file['name'], 'r');
        $list = [];
        while (!feof($handle)) {
            $content = '';
            for ($row_number = 0; $row_number < $row_limit; ++$row_number) {
                $line = fgets($handle);
                if (empty($line)) {
                    break;
                }
                if ($file['need_md5']) {
                    $line = trim($line);
                    $line = md5($line) . PHP_EOL;
                }
                $content .= $line;
            }
            if (empty($content)) {
                break;
            }
            file_put_contents(AudienceService::getAudienceDir(MediaType::JN_PUSH) . "/$table_audience_id-$file_count.txt", $content);
            $list[] = [
                'row' => $row_number,
                'url' => EnvConfig::MATERIAL_DOMAIN_NAME . EnvConfig::UPLOAD_PATH . "/audience/$table_audience_id-$file_count.txt",
            ];
            $file_count++;
        }
        fclose($handle);

        return [
            'user_id_type' => $user_id_type,
            'list' => $list,
        ];
    }
}