<?php

/**
 * 朋友圈service
 */

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsWeChatAccountLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use App\Utils\Helpers;
use SplFileObject;
use ZipArchive;


class MpService implements AccountLeader, DMP
{
    const RECORD_PATH = AudienceService::AUDIENCE_DIR . '/tencent';
    const ERROR_RECORD_SUFFIX = '_reduce_error.txt';

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_EXPORT);
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_FILE_ADDITION);
    }

    /**
     * 创建数据源文件
     *
     * @param int $table_audience_id
     * @param array $file_list
     * @return array|bool
     * <AUTHOR>
     */
    public function createDataSourceFile($table_audience_id, array $file_list)
    {
        $row_limit = 2000000;
        $file_count = 0;
        $zip_paths = [];
        foreach ($file_list as $file) {
            $filename = $file['name'];
            $user_id_type = $file['data_type'];
            if (!file_exists($filename)) {
                Helpers::getLogger('audience')->warning("file not exists", [
                    'audience_id' => $table_audience_id,
                    'filename' => $filename,
                    'media_type' => MediaType::MP,
                    'media_type_name' => '朋友圈',
                ]);
                continue;
            }
            $handle = fopen($filename, 'r');
            $zip = new ZipArchive;
            if ($user_id_type == UserIDType::getTencentType(UserIDType::OPEN_ID)) {
                $sub_file_arr = [];
                while (!feof($handle)) {
                    $line = fgets($handle);
                    if (empty($line)) {
                        break;
                    }

                    $explode_data = explode('@', $line);
                    if (count($explode_data) != 2) continue;
                    $app_id = $explode_data[0];
                    $open_id = $explode_data[1];

                    if (!isset($app_id_line_count_map[$app_id])) {
                        $line_row = $app_id_line_count_map[$app_id] = 0;
                    }else {
                        $line_row = $app_id_line_count_map[$app_id];
                    }
                    $shang = intval($line_row / $row_limit);//文件行数限制。商值想下取整得到的值作为文件名的顺序索引
                    $basename = "$table_audience_id-{$app_id}-{$shang}";
                    $sub_file = AudienceService::AUDIENCE_DIR . "/$basename.txt";
                    if (!isset($sub_file_arr[$app_id])) {
                        $sub_file_arr[$app_id][] = $sub_file;
                    } elseif (!in_array($sub_file, $sub_file_arr[$app_id])) {
                        $sub_file_arr[$app_id][] = $sub_file;
                    }

                    file_put_contents($sub_file, $open_id, FILE_APPEND);
                    $app_id_line_count_map[$app_id]++;
                }

                foreach ($sub_file_arr as $app_id => $sub_file_path_arr) {
                    foreach ($sub_file_path_arr as $sub_file_path) {
                        $basename = "$table_audience_id-{$file_count}";
                        $zip_path = AudienceService::AUDIENCE_DIR . "/$basename.zip";
                        $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
                        if (true !== $code) {
                            Helpers::getLogger('audience')->error("create data source zip fail", [
                                'table_audience_id' => $table_audience_id,
                                'zip_error_code' => $code,
                                'media_type' => MediaType::TENCENT,
                                'media_type_name' => '广点通',
                            ]);
                            continue;
                        }
                        $zip->addFile($sub_file_path, "$basename.txt");
                        $zip->close();
                        unlink($sub_file_path);
                        $zip_paths[] = [
                            'file' => $zip_path,
                            'user_id_type' => $user_id_type,
                            'app_id' => $app_id
                        ];

                        $file_count++;
                    }
                }

            } else {
                while (!feof($handle)) {
                    // md5 一行 33字节 (带换行)
                    $content = Helpers::getFileContent($handle, $row_limit);
                    if (empty($content)) {
                        break;
                    }
                    $basename = "$table_audience_id-{$file_count}";
                    $zip_path = AudienceService::AUDIENCE_DIR . "/$basename.zip";
                    $sub_file = AudienceService::AUDIENCE_DIR . "/$basename.txt";
                    file_put_contents($sub_file, $content);
                    $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
                    if (true !== $code) {
                        Helpers::getLogger('audience')->error("create data source zip fail", [
                            'table_audience_id' => $table_audience_id,
                            'zip_error_code' => $code,
                            'media_type' => MediaType::MP,
                            'media_type_name' => '朋友圈',
                        ]);
                        continue;
                    }
                    $zip->addFile($sub_file, "$basename.txt");
                    $zip->close();
                    unlink($sub_file);
                    $zip_paths[] = [
                        'file' => $zip_path,
                        'user_id_type' => $user_id_type,
                        'app_id' => ''
                    ];

                    $file_count++;
                }
            }
            fclose($handle);
        }
        return $zip_paths;
    }

    /**
     * 添加增量记录
     *
     * @param int $audience_id
     * @param array $zip_path
     * @return void
     */
    public function appendRecord($audience_id, $zip_path)
    {
        $filename = self::RECORD_PATH . '/' . $audience_id . '.txt';
        $fp = fopen($filename, 'ab+');
        foreach ($zip_path as $value) {
            fwrite($fp, serialize($value) . PHP_EOL);
        }
        fclose($fp);
    }

    private function addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, $addition_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $connection = MysqlConnection::getConnection();
        $table_audience_ids = [];

        $connection->beginTransaction();
        try {
            foreach ($companies as $company) {
                $majordomo_id = $company['name'];
                foreach ($company['account'] as $account_id) {
                    $media_info = $media_model->getDataHasMajordomo(MediaType::MP, $account_id);
                    if (!$media_info) {
                        throw new AppException('找不到该媒体账户');
                    }

                    $table_audience_id = $audience_model->add(
                        $user_id,
                        $username,
                        $device_task_id,
                        MediaType::MP,
                        $media_info->majordomo_name,
                        $account_id,
                        $audience_name,
                        $audience_desc,
                        '[]',
                        $addition_type,
                        $majordomo_id,
                        $media_info->wechat_account_name,
                        $media_info->platform,
                        $data_source_type
                    );
                    if (!($table_audience_id > 0)) {
                        Helpers::getLogger('audience')->error("add audience record fail", [
                            'device_task_id' => $device_task_id,
                            'account_id' => $account_id,
                            'majordomo_id' => $majordomo_id,
                            'audience_name' => $audience_name,
                            'audience_desc' => $audience_desc,
                            'media_type' => MediaType::MP,
                            'creator_id' => $user_id,
                            'creator' => $username,
                        ]);
                        continue;
                    }
                    $table_audience_ids[] = $table_audience_id;
                }
            }
            $connection->commit();
            return $table_audience_ids;
        } catch (\Throwable $e) {
            $connection->rollBack();
            throw $e;
        }
    }

    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsWeChatAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }

    public function editPushAccountList($id, $account_ids)
    {
        throw new AppException('朋友圈不能分享人群包');
    }
}
