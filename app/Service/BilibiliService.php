<?php

namespace App\Service;

use App\Container;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Bilibili\AuthModel;
use App\Param\MediaAccountInfoParam;

class BilibiliService
{
    /**
     * @param $platform
     * @param $account_content
     * @param $app_key
     * @param $secret
     * @return string
     */
    public function addAccount($platform, $account_content, $app_key, $secret): string
    {
        if (empty($app_key) || empty($secret)) {
            throw new AppException('请检查app_key或secret');
        }

        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $account_content = trim($account_content);
        $account_lines = explode("\n", $account_content);

        $message = '';
        $service = new MediaService();
        $auth_model = new AuthModel();
        $count = 0;
        foreach ($account_lines as $account_line) {
            $account_item = explode(' ', trim($account_line));
            $account_id = $account_item[0] ?? '';
            $name = $account_item[1] ?? '';
            if (empty($account_id)) {
                continue;
            }
            if (count($account_item) < 2) {
                $message .= "{$account_id}-信息不全，请检查;";
                continue;
            }
            if (!is_numeric($account_id)) {
                $message .= "{$account_id}-含有非数字字符;";
                continue;
            }

            // 获取token
            try {
                $token_info = $auth_model->refreshToken($account_id, $app_key, $secret);
            } catch (\Throwable $e) {
                $message .= "{$account_id}-获取token失败，错误信息：" . $e->getMessage();
                continue;
            }

            $count++;
            if ($count === 10) {
                // 防止接口频控报错
                sleep(5);
                $count = 0;
            }

            // B站刷新token后，老token失效，导致大数据9点拉数总是撞上同时间段会报错
            // 检查过期时间是否在9点，是的话过期时间往前推一小时
            $expire_time = request_time() + 86400;
            if (date("G", $expire_time) == 9) {
                $expire_time -= 3600;
            }

            $tmp_message = $service->saveAuthAccount(new MediaAccountInfoParam([
                'media_type' => MediaType::BILIBILI,
                'platform' => $platform,
                'account_id' => $account_id,
                'account_name' =>  $name,
                'access_token' => $token_info['new_token'],
                'access_token_expires' => $expire_time,
                'refresh_token' => '',
                'refresh_token_expires' => 0,
                'majordomo_name' => $app_key,
                'account_password' => $secret
            ]), $creator_id, $creator_name);
            $message .= "$tmp_message;";
        }

        return $message;
    }
}
