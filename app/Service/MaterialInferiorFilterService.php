<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Model\SqlModel\DataMedia\OdsMaterialLowEffectRejectLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoInefficientMaterialLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoSimilarMaterialLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use Illuminate\Support\Collection;

class MaterialInferiorFilterService
{
    /**
     * @param $media_type
     * @param $platform
     * @param int $game_id
     * @param array $material_file_md5s_list
     * @return Collection
     */
    static public function inferiorMaterial($media_type, $platform, int $game_id, array $material_file_md5s_list)
    {
        $game_info = (new V2DimGameIdModel())->getDataByGameId($platform, $game_id);
        return (new OdsMaterialLowEffectRejectLogModel())->getLowMaterialByMD5($media_type, $game_info->clique_id, $material_file_md5s_list);
    }

    static public function mediaInferiorMaterial($media_type, array $material_file_md5s_list)
    {
        if ($media_type == MediaType::TOUTIAO) {
            return (new OdsToutiaoSimilarMaterialLogModel())->getLowMaterialByMD5($material_file_md5s_list);
        }

        return [];
    }
}
