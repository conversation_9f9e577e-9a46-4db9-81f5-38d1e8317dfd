<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: melody
 * Date: 2019-08-09
 * Time: 11:41
 */

namespace App\Service;

use App\Constant\PlatId;
use App\Constant\RankMaterial;
use App\Constant\RouteID;
use App\Constant\RoutePermission;
use App\Container;
use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic;
use App\Logic\FA\SBULogic;
use App\Model\SqlModel\DatahubLY\ViewV2DimSiteIdModel;
use App\Model\SqlModel\DataMedia\OdsMaterialThemeFromZedaModel;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Tanwan\V2DimAgentLeaderGroupModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\DepartmentEightLevelModel;
use App\Model\SqlModel\Zeda\DepartmentGroupModel;
use App\Model\SqlModel\Zeda\DepartmentGroupPositionModel;
use App\Model\SqlModel\Zeda\DepartmentGroupPositionWorkerModel;
use App\Model\SqlModel\Zeda\DepartmentModel;
use App\Model\SqlModel\Zeda\DepartmentSevenLevelModel;
use App\Model\SqlModel\Zeda\DepartmentSixLevelModel;
use App\Model\SqlModel\Zeda\MaterialThemeModel;
use App\Model\SqlModel\Zeda\PlatformModel;
use App\Model\SqlModel\Zeda\RankAgentGroupModel;
use App\Model\SqlModel\Zeda\RankAgentModel;
use App\Model\SqlModel\Zeda\RankCoPositionModel;
use App\Model\SqlModel\Zeda\RankIntersectLeaderAndAgentModel;
use App\Model\SqlModel\Zeda\RankLeaderAgentModel;
use App\Model\SqlModel\Zeda\RankLeaderGroupModel;
use App\Model\SqlModel\Zeda\RankLeaderModel;
use App\Model\SqlModel\Zeda\RankLYAgentGroupModel;
use App\Model\SqlModel\Zeda\RankLYAgentModel;
use App\Model\SqlModel\Zeda\RankLYGameModel;
use App\Model\SqlModel\Zeda\RankMainGameModel;
use App\Model\SqlModel\Zeda\RankMaterialThemeModel;
use App\Model\SqlModel\Zeda\RankPermissionAllModel;
use App\Model\SqlModel\Zeda\RankPlatformModel;
use App\Model\SqlModel\Zeda\RankProxyTypeModel;
use App\Model\SqlModel\Zeda\RankRootGameModel;
use App\Model\SqlModel\Zeda\RankRouteListModel;
use App\Model\SqlModel\Zeda\RankRoutePermissionModel;
use App\Model\SqlModel\Zeda\RankSbuModel;
use App\Model\SqlModel\Zeda\RankSettleCompanyModel;
use App\Model\SqlModel\Zeda\RouteModel;
use App\Model\SqlModel\Zeda\RoutePermissionModel;
use App\Model\SqlModel\Zeda\UserLevelModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Model\SqlModel\Zeda\ViewPositionModel;
use App\MysqlConnection;
use App\Param\SyncPositionPermissionParam;
use App\Utils\FrontendTool;
use Common\EnvConfig;
use Exception;
use Illuminate\Support\Collection;

class PermissionService
{
    /**
     * @param        $level
     * @param        $rank_id
     *
     * @return array
     */
    public function getPlatform($level, $rank_id)
    {
        // level 就是超管级别
        $platform = collect();
        if ($level !== UserService::LEVEL_SUPER) {
            $platform = (new RankPlatformModel())->getAllByRank($level, $rank_id);
        }
        $platform_list = [];
        foreach (EnvConfig::PLATFORM_MAP as $platform_id => $platform_name) {
            if ($platform->search($platform_id) !== false || $level === UserService::LEVEL_SUPER) {
                $platform_list[] = [
                    'platform_id' => (string)$platform_id,
                    'platform_name' => $platform_name
                ];
            }
        }
        return [
            'platform_list' => $platform_list,
        ];
    }

    public function getAgentLeaderGroup()
    {
        return (new V2DimAgentLeaderGroupModel())->getOptions();
    }

    /**
     * @param        $level
     * @param        $rank_id
     * @param        $platform
     *
     * @return array
     */
    public function getMaterialTheme($level, $rank_id, $platform)
    {
        // level 就是超管级别
        if ($level === UserService::LEVEL_SUPER) {
            $theme = (new MaterialThemeModel())->getAllByPlatform($platform);
        } else {
            $theme = (new RankMaterialThemeModel())->getAllWithSubLeftJoinMaterialTheme($level, $rank_id, $platform, RankMaterial::TYPE_STORE);
        }
        return [
            'theme_list' => $theme,
        ];
    }

    /**
     * @param        $level
     * @param        $rank_id
     * @param        $platform
     *
     * @return array
     */
    public function getMaterialThemeForOutside($platform)
    {
        // level 就是超管级别
        $theme = (new OdsMaterialThemeFromZedaModel())->getListByPlatform($platform);
        return [
            'theme_list' => $theme,
        ];
    }

    /**
     * 获取某个路由权限
     *
     * @param $level
     * @param $rank_id
     * @param $route_id
     *
     * @return Collection
     */
    public function getRankRoutePermission($level, $rank_id, $route_id)
    {
        // 超管获取所有权限
        if (UserService::isSuperManager($level)) {
            $route_permission_list = (new RoutePermissionModel())->getAllByRouteId($route_id);
        } else {
            $route_permission_list = (new RankRoutePermissionModel())->getAllByRouteId($level, $rank_id, $route_id);
        }
        return $route_permission_list;
    }

    /**
     *
     * @param $user_level
     * @param $route_id
     *
     * @return Collection
     */
    public function getUserLevelRoutePermission($user_level, $route_id)
    {
        // 超管获取所有权限
        if (UserService::isSuperManager($user_level)) {
            $route_permission_list = (new RoutePermissionModel())->getAllByRouteId($route_id);
        } else {
            $route_permission_list = (new RankRoutePermissionModel())->getUserLevelRoutePermission($user_level);
        }
        return $route_permission_list;
    }

    /**
     * 获取维度和指标路由权限
     *
     * @param $level
     * @param $rank_id
     * @param $route_id
     *
     * @return array
     */
    public function getDimAndTargetRoutePermission($level, $rank_id, $route_id)
    {
        $route_permission_list = $this->getRankRoutePermission($level, $rank_id, $route_id);
        $dimension_filter_list = FrontendTool::dimensionFilterStruct($route_permission_list);
        $dimension_list = FrontendTool::dimensionStruct($route_permission_list);
        $target_list = FrontendTool::targetStruct($route_permission_list);

        return [
            'dimension_filter_list' => (object)$dimension_filter_list,
            'dimension_list' => (object)$dimension_list,
            'target_list' => (object)$target_list,
        ];
    }


    /**
     * 获取某个菜单的路由权限，指标、维度等。
     *
     * @param $level
     * @param $rank_id
     * @param $route_id
     * @return array[]
     */
    public function getRoutePermissionByRouteID($level, $rank_id, $route_id)
    {
        // 验证维度、指标、维度筛选等权限
        $route_permission_list = $this->getRankRoutePermission($level, $rank_id, $route_id);
        $dimension_filter = [];
        $dimensions = [];
        $targets = [];
        $button = [];
        foreach ($route_permission_list as $item) {
            $ext_arr = json_decode($item->ext, true);
            // 维度筛选提取 ext的 key
            if ($item->cate == RoutePermission::DIMENSION_FILTER) {
                $dimension_filter[] = $ext_arr['key'];
            } elseif ($item->cate == RoutePermission::DIMENSION) {
                $dimensions[] = $ext_arr['column'];
            } elseif ($item->cate == RoutePermission::TARGET) {
                $targets[] = $ext_arr['column'];
            } else {
                $button[] = $item->name;
            }
        }

        return [$dimension_filter, $dimensions, $targets, $button];
    }

    /**
     * 获取某个用户的所有路由的维度路由权限
     *
     *
     * @return array
     */
    public function getAllDimRoutePermissionByLoginUser()
    {
        $user_level = Container::getSession()->get('user_level');
        $route_permission_list = $this->getUserLevelRoutePermission($user_level, 0);
        $route_permission_list = $route_permission_list->groupBy('route_id');
        $res = [];
        foreach ($route_permission_list as $route_id => $route_permission_item) {
            $dimension_filter_list = FrontendTool::dimensionFilterStruct($route_permission_item);
            $dimension_list = FrontendTool::dimensionStruct($route_permission_item);
            $res[] = [
                'route_id' => $route_id,
                'route_name' => array_search($route_id, RouteID::ROUTE_NAME_MAP),
                'dimension_filter_list' => $dimension_filter_list,
                'dimension_list' => $dimension_list,
            ];
        }

        return $res;
    }

    public function getRechargePermission($level, $rank_id, $route_id)
    {
        $route_permission_list = $this->getRankRoutePermission($level, $rank_id, $route_id);
        $dimension_filter_list = FrontendTool::dimensionFilterStruct($route_permission_list);
        $show_type_filter_list = $route_permission_list->where('cate', RoutePermission::RANK_SHOW_TYPE)->where('state', 1)->sortBy('sort')->map(function ($item) {
            $item->ext = json_decode($item->ext, true);
            return $item;
        })->pluck('ext');

        return [
            'dimension_filter_list' => (object)$dimension_filter_list,
            'show_type_filter_list' => $show_type_filter_list
        ];
    }

    /**
     * 获取数据看板路由权限
     *
     * @param $module
     *
     * @return array
     */
    public function getDashboardRoutePermission($module)
    {
        [$level, $rank_id] = UserService::getLoginUserLevelAndRankId($module);
        $route_permission_list = $this->getRankRoutePermission($level, $rank_id, RouteID::HOME_DASHBOARD);
        return [
            'list' => $route_permission_list->groupBy('cate')
        ];
    }

    /**
     * 获取数据看板路由权限
     *
     * @param $level
     * @param $rank_id
     *
     * @return array
     */
    public function getKPIRoutePermission($level, $rank_id)
    {
        $route_permission_list = $this->getRankRoutePermission($level, $rank_id, RouteID::INDIVIDUATION_KPI);
        return [
            'indicator_list' => $route_permission_list->where('cate', 8)->sortBy('sort')->map(function ($item) {
                $item->ext = json_decode($item->ext, true);
                return $item;
            })->pluck('ext'),
        ];
    }

    /**
     * 获取登录用户dms的游戏权限和渠道权限
     *
     * @return array
     */
    public function getLoginUserDMSDataPermission()
    {
        // 获取权限
        $permission_logic = new PermissionLogic();
        $game_permission = $permission_logic->getLoginUserGamePermission();
        $agent_permission = $permission_logic->getLoginUserAgentPermission();

        return [
            'game_permission' => $game_permission,
            'agent_permission' => $agent_permission,
        ];
    }

    /**
     * 获取登录用户ly的游戏权限和渠道权限
     *
     * @return array
     */
    public function getLoginUserLYDataPermission()
    {
        // 获取权限
        $permission_logic = new \App\Logic\LY\PermissionLogic();
        return $permission_logic->getLoginUserDataPermission();
    }

    /**
     * 获取某个等级下面的用户列表
     *
     * @param $level
     * @param $rank_id
     *
     * @return Collection
     */
    public function getUserList($level, $rank_id)
    {
        $model = new UserModel();
        switch ($level) {
            case UserService::LEVEL_SUPER:
                $list = $model->getSuperUser();
                break;
            case UserService::LEVEL_PLATFORM:
                $list = $model->getAllPlatformUser($rank_id, 1);
                break;
            case UserService::LEVEL_DEPARTMENT:
                $list = $model->getAllDepartmentUser($rank_id, 1);
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP:
                $list = $model->getAllGroupUser($rank_id, 1);
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                $list = $model->getAllPositionUser($rank_id, 1);
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                $list = $model->getAllWorkerUser($rank_id, 1);
                break;
            case UserService::LEVEL_SIX:
                $list = $model->getAllRankUser(UserService::LEVEL_SIX, $rank_id, 1);
                break;
            case UserService::LEVEL_SEVEN:
                $list = $model->getAllRankUser(UserService::LEVEL_SEVEN, $rank_id, 1);
                break;
            case UserService::LEVEL_EIGHT:
                $list = $model->getAllRankUser(UserService::LEVEL_EIGHT, $rank_id, 1);
                break;
            default:
                throw new AppException('参数异常');
        }

        foreach ($list as $item) {
            $item->password = '';
        }

        return $list;
    }

    public function getUserRankId($module_level_info)
    {
        $column_name = UserService::getColumnNameByLevel($module_level_info['level']);
        return $module_level_info[$column_name];
    }


    /**
     * 给定目标用户ID ，判断目标用户是不是当前登录用户的下级
     *
     * @param int $obj_user_id
     *
     * @return bool
     */
    public function nextLevel(int $obj_user_id): bool
    {
        //  要登录的目标用户的user_level
        $obj_user_level = (new UserLevelModel())->getUserLevel($obj_user_id);
        if ($obj_user_level->isEmpty()) {
            return false;
        }

        $current_user_level = Container::getSession()->get('user_level');
        foreach ($current_user_level as $module => $current_level_info) {
            $obj_level_info = $obj_user_level->where('module', '=', $module);
            $obj_level_info = (array)$obj_level_info->first();
            // 判断当前登录用户的level是不是大于等于目标用户
            if ($current_level_info['level'] >= $obj_level_info['level']) {
                return false;
            }

            // 判断是不是上下级关系
            $column_name = UserService::getColumnNameByLevel($obj_level_info['level']);
            if ($obj_level_info[$column_name] != $current_level_info[$column_name]) {
                return false;
            }
            return true;

        }

        return false;
    }

    public function getUserLeaderList($module_level_info, $module, $user_info)
    {

        $user_level_model = new UserLevelModel();
        $user_leader_list = Collection::make();
        // 超管
        if ($module_level_info === -1) {
            $user_leader_list = $user_level_model->getAllLeader($module);
        } else {
            $column_name = UserService::getColumnNameByLevel($module_level_info['level']);
            $user_leader_list = $user_level_model->getAllLeaderLowThanRankID($module_level_info[$column_name], $module_level_info['level'], $module)->pluck('name');
        }

        // 现在所有人，都是 leader 都要把自己塞进去
        $user_leader_list->push($user_info['name']);

        return $user_leader_list;
    }

    public function getPlatformList($keyword, $module)
    {
        $model = new PlatformModel();
        return $model->getModulePlatform($module, $keyword);
    }

    public function getPlatformInfo($platform_id)
    {
        $model = new PlatformModel();
        return $model->getData($platform_id);
    }

    /**
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addPlatform($name, $module)
    {
        return (new PlatformModel())->add($name, $module);
    }

    /**
     * @param $id
     * @param $name
     *
     * @return int
     */
    public function editPlatform($id, $name)
    {
        return (new PlatformModel())->edit($id, $name);
    }

    /**
     * @param $id
     *
     * @return bool
     */
    public function delPlatform($id)
    {
        $department_list = (new DepartmentModel())->getAllByPlatformId($id);
        if ($department_list->isNotEmpty()) {
            throw new AppException('请先删除部门');
        }

        $platform_user_list = (new UserModel())->getAllPlatformUser($id);
        if ($platform_user_list->isNotEmpty()) {
            throw new AppException('请先删除平台负责人');
        }

        $effect_rows = (new PlatformModel())->remove($id);
        if ($effect_rows < 0) {
            throw new AppException('删除平台失败');
        }
        return true;
    }


    /**
     * 获取部门列表
     *
     * @param $platform_id
     *
     * @return Collection
     */
    public function getDepartmentList($platform_id)
    {
        $model = new DepartmentModel();
        return $model->getAllByPlatformId($platform_id);
    }

    /**
     * 添加部门
     *
     * @param     $platform_id
     * @param     $name
     * @param     $module
     *
     * @param int $type
     *
     * @return bool
     */
    public function addDepartment($platform_id, $name, $module, $type = 0)
    {
        $model = new DepartmentModel();

        // 验证平台id是否正确
        $platform_info = $this->getPlatformInfo($platform_id);
        if (empty($platform_info)) {
            throw new AppException('平台信息不存在，请重新选择平台');
        }

        $department_id = $model->add($platform_id, $name, $module, $type);
        if (!($department_id > 0)) {
            throw new AppException('部门添加失败');
        }

        return $department_id;
    }

    /**
     * 编辑部门
     *
     * @param $department_id
     * @param $name
     */
    public function editDepartment($department_id, $name)
    {
        $model = new DepartmentModel();
        $model->edit($department_id, $name);
    }

    /**
     * 删除部门
     *
     * @param $department_id
     *
     * @return bool
     */
    public function delDepartment($department_id)
    {
        $department_group_list = (new DepartmentGroupModel())->getAllByDepartmentId($department_id);
        if ($department_group_list->isNotEmpty()) {
            throw new AppException('请先删除部门下的小组');
        }

        $department_user_list = (new UserModel())->getAllDepartmentUser($department_id);
        if ($department_user_list->isNotEmpty()) {
            throw new AppException('请先删除部门负责人');
        }

        $effect_rows = (new DepartmentModel())->remove($department_id);
        if ($effect_rows < 0) {
            throw new AppException('删除部门失败');
        }
        return true;
    }

    /**
     * 获取部门信息
     *
     * @param $department_id
     *
     * @return array
     */
    public function getDepartmentInfo($department_id)
    {
        $model = new DepartmentModel();
        return $model->getData($department_id);
    }

    /**
     * 获取部门分组列表
     *
     * @param $department_id
     *
     * @return Collection
     */
    public function getDepartmentGroupList($department_id)
    {
        $model = new DepartmentGroupModel();
        return $model->getAllByDepartmentId($department_id);
    }

    /**
     * 添加部门分组
     *
     * @param $department_id
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addDepartmentGroup($department_id, $name, $module)
    {
        $model = new DepartmentGroupModel();
        $department_info = $this->getDepartmentInfo($department_id);
        if (empty($department_info)) {
            throw new AppException('部门不存在，请重新选择部门');
        }
        $department_group_id = $model->add($department_info['platform_id'], $department_id, $name, $module, $department_info['type']);

        if (!($department_group_id > 0)) {
            throw new AppException('部门分组添加失败');
        }

        return $department_group_id;
    }

    /**
     * 编辑部门分组
     *
     * @param $department_group_id
     * @param $name
     */
    public function editDepartmentGroup($department_group_id, $name)
    {
        $model = new DepartmentGroupModel();
        $model->edit($department_group_id, $name);
    }

    /**
     * 删除部门分组
     *
     * @param $department_group_id
     *
     * @return bool
     */
    public function delDepartmentGroup($department_group_id)
    {
        $department_group_list = (new DepartmentGroupPositionModel())->getAllByDepartmentGroupId($department_group_id);
        if ($department_group_list->isNotEmpty()) {
            throw new AppException('请先删除小组下的岗位');
        }

        $department_user_list = (new UserModel())->getAllGroupUser($department_group_id);
        if ($department_user_list->isNotEmpty()) {
            throw new AppException('请先删除小组负责人');
        }

        $effect_rows = (new DepartmentGroupModel())->remove($department_group_id);
        if ($effect_rows < 0) {
            throw new AppException('删除小组失败');
        }
        return true;
    }

    /**
     * 获取部门分组信息
     *
     * @param $department_group_id
     *
     * @return array
     */
    public function getDepartmentGroupInfo($department_group_id)
    {
        $model = new DepartmentGroupModel();
        return $model->getData($department_group_id);
    }


    /**
     * 获取部门分组岗位列表
     *
     * @param $department_group_id
     *
     * @return Collection
     */
    public function getDepartmentGroupPositionList($department_group_id)
    {
        $model = new DepartmentGroupPositionModel();
        return $model->getAllByDepartmentGroupId($department_group_id);
    }

    /**
     * 添加部门分组岗位
     *
     * @param $department_group_id
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addDepartmentGroupPosition($department_group_id, $name, $module)
    {
        $model = new DepartmentGroupPositionModel();
        $department_group_info = $this->getDepartmentGroupInfo($department_group_id);
        if (empty($department_group_info)) {
            throw new AppException('部门分组不存在，请重新选择部门');
        }
        $platform_id = $department_group_info['platform_id'];
        $department_id = $department_group_info['department_id'];
        $type = $department_group_info['type'];
        $position_id = $model->add($platform_id, $department_id, $department_group_id, $name, $module, $type);

        if (!($position_id > 0)) {
            throw new AppException('部门岗位添加失败');
        }
        return $position_id;
    }

    /**
     * 编辑部门分组
     *
     * @param $department_group_position_id
     * @param $name
     */
    public function editDepartmentGroupPosition($department_group_position_id, $name)
    {
        $model = new DepartmentGroupPositionModel();
        $model->edit($department_group_position_id, $name);
    }

    /**
     * 删除岗位
     *
     * @param $department_group_position_id
     *
     * @return bool
     */
    public function delDepartmentGroupPosition($department_group_position_id)
    {
        $department_user_list = (new UserModel())->getAllPositionUser($department_group_position_id);
        if ($department_user_list->isNotEmpty()) {
            throw new AppException('请先删除成员');
        }

        $effect_rows = (new DepartmentGroupPositionModel())->remove($department_group_position_id);
        if ($effect_rows < 0) {
            throw new AppException('删除岗位失败');
        }
        return true;
    }

    /**
     * 获取部门分组信息
     *
     * @param $department_group_position_id
     *
     * @return array
     */
    public function getDepartmentGroupPositionInfo($department_group_position_id)
    {
        $model = new DepartmentGroupPositionModel();
        return $model->getData($department_group_position_id);
    }

    /**
     * 获取部门分组信息
     *
     * @param $id
     *
     * @return array
     */
    public function getDepartmentGroupPositionWorkerInfo($id)
    {
        $model = new DepartmentGroupPositionWorkerModel();
        return $model->getData($id);
    }

    /**
     * 获取部门分组信息
     *
     * @param $id
     *
     * @return array
     */
    public function getDepartmentSixInfo($id)
    {
        $model = new DepartmentSixLevelModel();
        return $model->getData($id);
    }

    /**
     * 获取部门分组信息
     *
     * @param $id
     *
     * @return array
     */
    public function getDepartmentSevenInfo($id)
    {
        $model = new DepartmentSevenLevelModel();
        return $model->getData($id);
    }

    /**
     * 获取部门分组信息
     *
     * @param $id
     *
     * @return array
     */
    public function getDepartmentEightInfo($id)
    {
        $model = new DepartmentEightLevelModel();
        return $model->getData($id);
    }

    /**
     * 获取部门分组岗位worker列表
     *
     * @param $department_group_position_id
     *
     * @return Collection
     */
    public function getDepartmentGroupPositionWorkerList($department_group_position_id)
    {
        $model = new DepartmentGroupPositionWorkerModel();
        return $model->getAllByDepartmentGroupPositionId($department_group_position_id);
    }

    /**
     * 获取6级列表
     *
     * @param $department_group_position_worker_id
     *
     * @return Collection
     */
    public function getDepartmentSixList($department_group_position_worker_id)
    {
        $model = new DepartmentSixLevelModel();
        return $model->getAllByLastLevelID($department_group_position_worker_id);
    }

    /**
     * 获取7级列表
     *
     * @param $department_six_id
     *
     * @return Collection
     */
    public function getDepartmentSevenList($department_six_id)
    {
        $model = new DepartmentSevenLevelModel();
        return $model->getAllByLastLevelID($department_six_id);
    }

    /**
     * 获取8级列表
     *
     * @param $department_seven_id
     *
     * @return Collection
     */
    public function getDepartmentEightList($department_seven_id)
    {
        $model = new DepartmentEightLevelModel();
        return $model->getAllByLastLevelID($department_seven_id);
    }

    /**
     * 添加部门分组岗位
     *
     * @param $department_group_position_id
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addDepartmentGroupPositionWorker($department_group_position_id, $name, $module)
    {
        $model = new DepartmentGroupPositionWorkerModel();
        $department_group_info = $this->getDepartmentGroupPositionInfo($department_group_position_id);
        if (empty($department_group_info)) {
            throw new AppException('部门岗位不存在，请重新选择分组');
        }
        $platform_id = $department_group_info['platform_id'];
        $department_id = $department_group_info['department_id'];
        $department_group_id = $department_group_info['department_group_id'];
        $type = $department_group_info['type'];
        $position_id = $model->add($platform_id, $department_id, $department_group_id, $department_group_position_id, $name, $module, $type);

        if (!($position_id > 0)) {
            throw new AppException('部门子岗位添加失败');
        }
        return $position_id;
    }

    /**
     * 编辑部门分组
     *
     * @param $department_group_position_worker_id
     * @param $name
     */
    public function editDepartmentGroupPositionWorker($department_group_position_worker_id, $name)
    {
        $model = new DepartmentGroupPositionWorkerModel();
        $model->edit($department_group_position_worker_id, $name);
    }

    /**
     * 添加6级岗位
     *
     * @param $department_group_position_worker_id
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addDepartmentSix($department_group_position_worker_id, $name, $module)
    {
        $model = new DepartmentSixLevelModel();
        $department_group_info = $this->getDepartmentGroupPositionWorkerInfo($department_group_position_worker_id);
        if (empty($department_group_info)) {
            throw new AppException('部门协作岗位不存在，请重新选择分组');
        }
        $platform_id = $department_group_info['platform_id'];
        $department_id = $department_group_info['department_id'];
        $department_group_id = $department_group_info['department_group_id'];
        $department_group_position_id = $department_group_info['department_group_position_id'];
        $type = $department_group_info['type'];
        $position_id = $model->add($platform_id, $department_id, $department_group_id,
            $department_group_position_id, $department_group_position_worker_id, $name, $module, $type);

        if (!($position_id > 0)) {
            throw new AppException('部门子岗位添加失败');
        }
        return $position_id;
    }

    /**
     * 编辑部门分组
     *
     * @param $id
     * @param $name
     */
    public function editDepartmentSix($id, $name)
    {
        $model = new DepartmentSixLevelModel();
        $model->edit($id, $name);
    }


    /**
     * 添加7级岗位
     *
     * @param $department_six_id
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addDepartmentSeven($department_six_id, $name, $module)
    {
        $model = new DepartmentSevenLevelModel();
        $department_group_info = $this->getDepartmentSixInfo($department_six_id);
        if (empty($department_group_info)) {
            throw new AppException('部门六级岗位不存在，请重新选择分组');
        }
        $platform_id = $department_group_info['platform_id'];
        $department_id = $department_group_info['department_id'];
        $department_group_id = $department_group_info['department_group_id'];
        $department_group_position_id = $department_group_info['department_group_position_id'];
        $department_group_position_worker_id = $department_group_info['department_group_position_worker_id'];
        $type = $department_group_info['type'];
        $position_id = $model->add($platform_id, $department_id, $department_group_id,
            $department_group_position_id, $department_group_position_worker_id, $department_six_id, $name, $module, $type);

        if (!($position_id > 0)) {
            throw new AppException('部门子岗位添加失败');
        }
        return $position_id;
    }

    /**
     * 编辑7级
     *
     * @param $id
     * @param $name
     */
    public function editDepartmentSeven($id, $name)
    {
        $model = new DepartmentSevenLevelModel();
        $model->edit($id, $name);
    }

    /**
     * 添加8级岗位
     *
     * @param $department_seven_id
     * @param $name
     * @param $module
     *
     * @return int
     */
    public function addDepartmentEight($department_seven_id, $name, $module)
    {
        $model = new DepartmentEightLevelModel();
        $department_group_info = $this->getDepartmentSevenInfo($department_seven_id);
        if (empty($department_group_info)) {
            throw new AppException('部门七级岗位不存在，请重新选择分组');
        }
        $platform_id = $department_group_info['platform_id'];
        $department_id = $department_group_info['department_id'];
        $department_group_id = $department_group_info['department_group_id'];
        $department_group_position_id = $department_group_info['department_group_position_id'];
        $department_group_position_worker_id = $department_group_info['department_group_position_worker_id'];
        $department_six_id = $department_group_info['department_six_id'];
        $type = $department_group_info['type'];
        $position_id = $model->add($platform_id, $department_id, $department_group_id,
            $department_group_position_id, $department_group_position_worker_id, $department_six_id, $department_seven_id, $name, $module, $type);

        if (!($position_id > 0)) {
            throw new AppException('部门子岗位添加失败');
        }
        return $position_id;
    }

    /**
     * 编辑8级
     *
     * @param $id
     * @param $name
     */
    public function editDepartmentEight($id, $name)
    {
        $model = new DepartmentEightLevelModel();
        $model->edit($id, $name);
    }

    /**
     * 删除岗位
     *
     * @param $department_group_position_worker_id
     *
     * @return bool
     */
    public function delDepartmentGroupPositionWorker($department_group_position_worker_id)
    {
        $department_user_list = (new UserModel())->getAllWorkerUser($department_group_position_worker_id);
        if ($department_user_list->isNotEmpty()) {
            throw new AppException('请先删除成员');
        }

        $effect_rows = (new DepartmentGroupPositionWorkerModel())->remove($department_group_position_worker_id);
        if ($effect_rows < 0) {
            throw new AppException('删除子岗位失败');
        }
        return true;
    }


    /**
     * 删除6级岗位
     *
     * @param $department_six_id
     *
     * @return bool
     */
    public function delDepartmentSix($department_six_id)
    {
        $user_list = (new UserModel())->getAllRankUser(UserService::LEVEL_SIX, $department_six_id);
        if ($user_list->isNotEmpty()) {
            throw new AppException('请先删除成员');
        }

        $effect_rows = (new DepartmentSixLevelModel())->remove($department_six_id);
        if ($effect_rows < 0) {
            throw new AppException('删除子岗位失败');
        }
        return true;
    }

    /**
     * 删除7级岗位
     *
     * @param $department_seven_id
     *
     * @return bool
     */
    public function delDepartmentSeven($department_seven_id)
    {
        $user_list = (new UserModel())->getAllRankUser(UserService::LEVEL_SEVEN, $department_seven_id);
        if ($user_list->isNotEmpty()) {
            throw new AppException('请先删除成员');
        }

        $effect_rows = (new DepartmentSevenLevelModel())->remove($department_seven_id);
        if ($effect_rows < 0) {
            throw new AppException('删除子岗位失败');
        }
        return true;
    }

    /**
     * 删除8级岗位
     *
     * @param $department_eight_id
     *
     * @return bool
     */
    public function delDepartmentEight($department_eight_id)
    {
        $user_list = (new UserModel())->getAllRankUser(UserService::LEVEL_EIGHT, $department_eight_id);
        if ($user_list->isNotEmpty()) {
            throw new AppException('请先删除成员');
        }

        $effect_rows = (new DepartmentEightLevelModel())->remove($department_eight_id);
        if ($effect_rows < 0) {
            throw new AppException('删除子岗位失败');
        }
        return true;
    }


    /**
     * 获取面包屑
     *
     * @param $level
     * @param $module
     *
     * @return array
     */
    public function getBreadcrumbs($level, $module)
    {
        if ($level === UserService::LEVEL_SUPER) return [];

        switch ($level) {
            case UserService::LEVEL_PLATFORM:
                $all_breadcrumbs = (new PlatformModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['platform_id']);
                return [['id' => $all_breadcrumbs['id'], 'name' => $all_breadcrumbs['name']]];
            case UserService::LEVEL_DEPARTMENT:
                $all_breadcrumbs = (new DepartmentModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']]
                ];
            case UserService::LEVEL_DEPARTMENT_GROUP:
                $all_breadcrumbs = (new DepartmentGroupModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_group_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']],
                    ['id' => $all_breadcrumbs['department_group_id'], 'name' => $all_breadcrumbs['department_group_name']]
                ];
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                $all_breadcrumbs = (new DepartmentGroupPositionModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_group_position_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']],
                    ['id' => $all_breadcrumbs['department_group_id'], 'name' => $all_breadcrumbs['department_group_name']],
                    ['id' => $all_breadcrumbs['department_group_position_id'], 'name' => $all_breadcrumbs['department_group_position_name']]
                ];
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                $all_breadcrumbs = (new DepartmentGroupPositionWorkerModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_group_position_worker_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']],
                    ['id' => $all_breadcrumbs['department_group_id'], 'name' => $all_breadcrumbs['department_group_name']],
                    ['id' => $all_breadcrumbs['department_group_position_id'], 'name' => $all_breadcrumbs['department_group_position_name']],
                    ['id' => $all_breadcrumbs['department_group_position_worker_id'], 'name' => $all_breadcrumbs['department_group_position_worker_name']],
                ];

            case UserService::LEVEL_SIX:
                $all_breadcrumbs = (new DepartmentSixLevelModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_six_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']],
                    ['id' => $all_breadcrumbs['department_group_id'], 'name' => $all_breadcrumbs['department_group_name']],
                    ['id' => $all_breadcrumbs['department_group_position_id'], 'name' => $all_breadcrumbs['department_group_position_name']],
                    ['id' => $all_breadcrumbs['department_group_position_worker_id'], 'name' => $all_breadcrumbs['department_group_position_worker_name']],
                    ['id' => $all_breadcrumbs['department_six_id'], 'name' => $all_breadcrumbs['department_six_name']],
                ];

            case UserService::LEVEL_SEVEN:
                $all_breadcrumbs = (new DepartmentSevenLevelModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_seven_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']],
                    ['id' => $all_breadcrumbs['department_group_id'], 'name' => $all_breadcrumbs['department_group_name']],
                    ['id' => $all_breadcrumbs['department_group_position_id'], 'name' => $all_breadcrumbs['department_group_position_name']],
                    ['id' => $all_breadcrumbs['department_group_position_worker_id'], 'name' => $all_breadcrumbs['department_group_position_worker_name']],
                    ['id' => $all_breadcrumbs['department_six_id'], 'name' => $all_breadcrumbs['department_six_name']],
                    ['id' => $all_breadcrumbs['department_seven_id'], 'name' => $all_breadcrumbs['department_seven_name']],
                ];


            case UserService::LEVEL_EIGHT:
                $all_breadcrumbs = (new DepartmentEightLevelModel())->getBreadcrumbs(Container::getSession()->getUserDetail($module)['department_eight_id']);
                return [
                    ['id' => $all_breadcrumbs['platform_id'], 'name' => $all_breadcrumbs['platform_name']],
                    ['id' => $all_breadcrumbs['department_id'], 'name' => $all_breadcrumbs['department_name']],
                    ['id' => $all_breadcrumbs['department_group_id'], 'name' => $all_breadcrumbs['department_group_name']],
                    ['id' => $all_breadcrumbs['department_group_position_id'], 'name' => $all_breadcrumbs['department_group_position_name']],
                    ['id' => $all_breadcrumbs['department_group_position_worker_id'], 'name' => $all_breadcrumbs['department_group_position_worker_name']],
                    ['id' => $all_breadcrumbs['department_six_id'], 'name' => $all_breadcrumbs['department_six_name']],
                    ['id' => $all_breadcrumbs['department_seven_id'], 'name' => $all_breadcrumbs['department_seven_name']],
                    ['id' => $all_breadcrumbs['department_eight_id'], 'name' => $all_breadcrumbs['department_eight_name']],
                ];


        }

        return null;
    }

    /**
     * @param $module
     *
     * @return array
     */
    public function getRankPositionCascader($module)
    {
        $platform_list = Collection::make();
        $department_list = Collection::make();
        $department_group_list = Collection::make();
        $department_group_position_list = Collection::make();
        [$level] = UserService::getLoginUserLevelAndRankId($module);
        if ($level === 1) {
            $platform_list = (new PlatformModel())->getModulePlatform($module);
        }

        switch ($level) {
            case 0:
            case 1:
                $platform_list = (new PlatformModel())->getModulePlatform($module);
                $platform_id = Container::getSession()->getUserDetail($module)['platform_id'];
                if ($platform_id !== 0) {
                    $platform_list = $platform_list->filter(function ($item) use ($platform_id) {
                        return $item->id === $platform_id;
                    });
                }
            case 2:
                $department_list = (new DepartmentModel())->getModuleDepartment($module);
                $department_id = Container::getSession()->getUserDetail($module)['department_id'];
                if ($department_id !== 0) {
                    $department_list = $department_list->filter(function ($item) use ($department_id) {
                        return $item->id === $department_id;
                    });
                }
            case 3:
                $department_group_list = (new DepartmentGroupModel())->getAll();
                $department_group_id = Container::getSession()->getUserDetail($module)['department_group_id'];
                if ($department_group_id !== 0) {
                    $department_group_list = $department_group_list->filter(function ($item) use ($department_group_id) {
                        return $item->id === $department_group_id;
                    });
                }
            case 4:
                $department_group_position_list = (new DepartmentGroupPositionModel())->getAll();
                $department_group_position_id = Container::getSession()->getUserDetail($module)['department_group_position_id'];
                if ($department_group_position_id !== 0) {
                    $department_group_position_list = $department_group_position_list->filter(function ($item) use ($department_group_position_id) {
                        return $item->id === $department_group_position_id;
                    });
                }
        }
        return [
            'platform_list' => $platform_list->values(),
            'department_list' => $department_list->values(),
            'department_group_list' => $department_group_list->values(),
            'department_group_position_list' => $department_group_position_list->values(),
        ];
    }

    /**
     * @param $level
     * @param $rank_id
     *
     * @return array
     */
    public function getRankRouterCascader($level, $rank_id)
    {
        $route_model = new RouteModel();

        // level 就是超管级别 拿所有
        if ($level === UserService::LEVEL_SUPER) {
            $route_item = $route_model->getAll();
        } else {
            $route_str = (new RankRouteListModel())->getData($level, $rank_id);
            $route_item = $route_model->getAllByRouteStr($route_str);
        }
        $route_cascader = $route_model->getCascader($route_item);
        return [
            'route_cascader' => $route_cascader,
        ];
    }

    public function getSubordinateInfo($param, $level, $rank_id)
    {
        $users = $this->getSubordinate($param, $level, $rank_id);
        $user_list = [];

        foreach ($users['list'] as $item) {
            $groups = [];
            if ($item->platform_id) {
                $groups[] = [
                    'id' => $item->platform_id,
                    'name' => $item->platform_name
                ];
            }
            if ($item->department_id) {
                $groups[] = [
                    'id' => $item->department_id,
                    'name' => $item->department_name
                ];
            }
            if ($item->department_group_id) {
                $groups[] = [
                    'id' => $item->department_group_id,
                    'name' => $item->department_group_name
                ];
            }
            if ($item->department_group_position_id) {
                $groups[] = [
                    'id' => $item->department_group_position_id,
                    'name' => $item->department_group_position_name
                ];
            }
            if ($item->department_group_position_worker_id) {
                $groups[] = [
                    'id' => $item->department_group_position_worker_id,
                    'name' => $item->department_group_position_worker_name
                ];
            }
            if ($item->department_six_id) {
                $groups[] = [
                    'id' => $item->department_six_id,
                    'name' => $item->department_six_name
                ];
            }
            if ($item->department_seven_id) {
                $groups[] = [
                    'id' => $item->department_seven_id,
                    'name' => $item->department_seven_name
                ];
            }
            if ($item->department_eight_id) {
                $groups[] = [
                    'id' => $item->department_eight_id,
                    'name' => $item->department_eight_name
                ];
            }
            unset($item->platform_name);
            unset($item->department_name);
            unset($item->department_group_name);
            unset($item->department_group_position_name);
            unset($item->department_group_position_worker_name);
            unset($item->department_six_name);
            unset($item->department_seven_name);
            unset($item->department_eight_name);
            $item->group_name = $groups;
            $user_list[] = $item;
        }

        return [
            'list' => $user_list,
            'total' => $users['total']
        ];
    }

    public function getSubordinate($param, $level, $rank_id)
    {
        $model = new UserModel();
        switch ($level) {
            case UserService::LEVEL_SUPER:
                $list = $model->getListByZeda($param);
                break;
            case UserService::LEVEL_EIGHT:
                $list = ['total' => 0, 'list' => []];
                break;
            default:
                $list = $model->getListByRankId($rank_id, $level, $param);
        }

        return $list;
    }

    /**
     * @param SyncPositionPermissionParam $param
     *
     * @throws Exception
     */
    public function syncPositionPermission(SyncPositionPermissionParam $param)
    {
        // 开启一下事务
        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            $this->syncAddRoute($param->position_list, $param->route_ids);
            $this->syncAddRoutePermission($param->position_list, $param->route_permission_ids);
            MysqlConnection::getConnection('default')->commit();
        } catch (\Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage());
        }
    }

    /**
     * 同步路由
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $route_list [1,2,3,4]
     */
    public function syncAddRoute(array $position_list, array $route_list)
    {
        /******* 处理路由 *******/
        $rank_route_model = new RankRouteListModel();
        $rank_route_list = $rank_route_model->getAllInPositionList($position_list);

        $replace_route_list = [];
        foreach ($rank_route_list as $item) {
            $route_ids = explode(',', $item->route_list);
            // 合并、去重
            $route_ids = array_unique(array_merge($route_ids, $route_list));
            sort($route_ids);
            // 再次变成字符串更新权限
            $replace_route_list[] = [
                'id' => $item->id,
                'rank_id' => $item->rank_id,
                'level' => $item->level,
                'route_list' => trim(implode(',', $route_ids), ','),
            ];
        }

        $rank_route_model->replace($replace_route_list);
    }

    /**
     * 同步路由
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param string $route_list '1,2,3,4'
     */
    public function syncDeleteRoute(array $position_list, $route_list)
    {
        /******* 处理路由 *******/
        $rank_route_model = new RankRouteListModel();
        $rank_route_list = $rank_route_model->getAllInPositionList($position_list);
        $sync_route_ids = explode(',', $route_list);

        $replace_route_list = [];
        foreach ($rank_route_list as $item) {
            $route_ids = explode(',', $item->route_list);
            // 合并、去重
            $route_ids = array_diff($route_ids, $sync_route_ids);

            // 再次变成字符串更新权限
            $replace_route_list[] = [
                'id' => $item->id,
                'rank_id' => $item->rank_id,
                'level' => $item->level,
                'route_list' => trim(implode(',', $route_ids), ','),
            ];
        }
        $rank_route_model->replace($replace_route_list);
    }

    /**
     * 同步路由权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $route_permission_ids [1,2,3,4]
     */
    public function syncAddRoutePermission(array $position_list, array $route_permission_ids)
    {
        $replace_route_permission_list = [];
        foreach ($position_list as $item) {
            foreach ($route_permission_ids as $route_permission_id) {
                $replace_route_permission_list[] = [
                    'rank_id' => $item['rank_id'],
                    'level' => $item['level'],
                    'route_permission_id' => $route_permission_id,
                    'create_time' => time(),
                    'update_time' => time()
                ];
            }
        }
        /******* 处理路由权限 *******/
        $rank_route_permission_model = new RankRoutePermissionModel();
        $rank_route_permission_model->replace($replace_route_permission_list);
    }

    /**
     * 同步路由权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $route_permission_ids [1,2,3,4]
     */
    public function syncDeleteRoutePermission(array $position_list, array $route_permission_ids)
    {
        $delete_route_permission_list = [];
        foreach ($position_list as $item) {
            foreach ($route_permission_ids as $route_permission_id) {
                $delete_route_permission_list[] = [
                    'rank_id' => $item['rank_id'],
                    'level' => $item['level'],
                    'route_permission_id' => $route_permission_id,
                ];
            }
        }
        /******* 处理路由权限 *******/
        if (!empty($delete_route_permission_list)) {
            $rank_route_permission_model = new RankRoutePermissionModel();
            // 表太大 需要分批操作
            foreach (array_chunk($delete_route_permission_list, 1000) as $chunk) {
                $rank_route_permission_model->deleteByList($chunk);
            }
        }
    }

    /**
     * 同步新增平台权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddPlatform(array $position_list, array $platform_list)
    {
        $rank_platform_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform_item) {
                $rank_platform_list[] = [
                    'rank_id' => $item['rank_id'],
                    'level' => $item['level'],
                    'platform' => $platform_item['platform'],
                ];
            }
        }

        if (!empty($rank_platform_list)) {
            $rank_platform_model = new RankPlatformModel();
            $rank_platform_model->replace($rank_platform_list);
        }
    }

    /**
     * 同步新增游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddGame(array $position_list, array $platform_list)
    {
        $root_game_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['game_list']) && is_array($platform['game_list'])) {
                    foreach ($platform['game_list'] as $game) {
                        $root_game_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'plat_id' => $game['plat_id'] ?? 0,
                            'root_game_id' => $game['root_game_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }

        }

        if (!empty($root_game_list)) {
            $rank_root_game_model = new RankRootGameModel();
            $rank_root_game_model->replace($root_game_list);
            (new RankMainGameModel())->deleteByRootGameList($root_game_list);
            (new RankPermissionAllModel())->deleteGameByPositionList($position_list);
            $all_root_game_list = (new V2DimGameIdModel())->getAllRootGameByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $root_game_list = $rank_root_game_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleGameAllPermission($item['level'], $item['rank_id'], $root_game_list, $all_root_game_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }


    /**
     * 同步新增发行的游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddLYGame(array $position_list, array $platform_list)
    {
        $game_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['game_list']) && is_array($platform['game_list'])) {
                    foreach ($platform['game_list'] as $game) {
                        $game_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'game_id' => $game['game_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }

        }

        if (!empty($game_list)) {
            $rank_game_model = new RankLYGameModel();
            $rank_game_model->replace($game_list);
            (new RankLYAgentModel())->deleteByGameList($game_list);
            (new RankPermissionAllModel())->deleteLYGameByPositionList($position_list);
            $all_game_list = (new ViewV2DimSiteIdModel())->getAllGameByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $game_list = $rank_game_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleLYGameAllPermission($item['level'], $item['rank_id'], $game_list, $all_game_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }


    /**
     * 同步新增负责人权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddLYAgentGroup(array $position_list, array $platform_list)
    {
        $agent_group_list = [];
        foreach ($position_list as $item) {
            foreach ($platform_list as $platform) {
                if (isset($platform['agent_group_list']) && is_array($platform['agent_group_list'])) {
                    foreach ($platform['agent_group_list'] as $agent_group_id) {
                        $agent_group_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'agent_group_id' => $agent_group_id,
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($agent_group_list)) {
            $rank_agent_group_model = new RankLYAgentGroupModel();
            $rank_agent_group_model->replace($agent_group_list);
            (new RankPermissionAllModel())->deleteLYAgentGroupByPositionList($position_list);
            $rank_permission_list = [];
            $all_agent_group_list = (new ViewV2DimSiteIdModel())->getAllAgentGroupByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            foreach ($position_list as $item) {
                $agent_group_list = $rank_agent_group_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleLYAgentGroupAllPermission($item['level'], $item['rank_id'], $agent_group_list, $all_agent_group_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }


    /**
     * 同步新增游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddSettleCompany(array $position_list, array $platform_list)
    {
        $settle_company_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['settle_company_name_list']) && is_array($platform['settle_company_name_list'])) {
                    foreach ($platform['settle_company_name_list'] as $settle_company_name) {
                        $settle_company_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'settle_company_name' => $settle_company_name,
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($settle_company_list)) {
            $rank_settle_company_model = new RankSettleCompanyModel();
            $rank_settle_company_model->replace($settle_company_list);
        }
    }


    /**
     * 同步删除游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteGame(array $position_list, array $platform_list)
    {
        $root_game_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['game_list']) && is_array($platform['game_list'])) {
                    foreach ($platform['game_list'] as $game) {
                        $root_game_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'root_game_id' => $game['root_game_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }

        }

        if (!empty($root_game_list)) {
            $rank_root_game_model = new RankRootGameModel();
            $rank_root_game_model->deleteByList($root_game_list);
            (new RankMainGameModel())->deleteByRootGameList($root_game_list);
            (new RankPermissionAllModel())->deleteGameByPositionList($position_list);
            $all_root_game_list = (new V2DimGameIdModel())->getAllRootGameByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $root_game_list = $rank_root_game_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleGameAllPermission($item['level'], $item['rank_id'], $root_game_list, $all_root_game_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }

    }

    /**
     * 同步删除游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteLYGame(array $position_list, array $platform_list)
    {
        $game_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['game_list']) && is_array($platform['game_list'])) {
                    foreach ($platform['game_list'] as $game) {
                        $game_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'game_id' => $game['game_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }

        }

        if (!empty($game_list)) {
            $rank_game_model = new RankLYGameModel();
            $rank_game_model->deleteByList($game_list);
            (new RankLYAgentModel())->deleteByGameList($game_list);
            (new RankPermissionAllModel())->deleteLYGameByPositionList($position_list);
            $all_game_list = (new ViewV2DimSiteIdModel())->getAllGameByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $game_list = $rank_game_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleLYGameAllPermission($item['level'], $item['rank_id'], $game_list, $all_game_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }

    /**
     * 同步删除游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteLYAgentGroup(array $position_list, array $platform_list)
    {
        $agent_group_list = [];
        foreach ($position_list as $item) {
            foreach ($platform_list as $platform) {
                if (isset($platform['agent_group_list']) && is_array($platform['agent_group_list'])) {
                    foreach ($platform['agent_group_list'] as $agent_group_id) {
                        $agent_group_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'agent_group_id' => $agent_group_id,
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }


        if (!empty($agent_group_list)) {
            $rank_agent_group_model = new RankLYAgentGroupModel();
            $rank_agent_group_model->deleteByList($agent_group_list);
            (new RankPermissionAllModel())->deleteLYAgentGroupByPositionList($position_list);
            $all_agent_group_list = (new ViewV2DimSiteIdModel())->getAllAgentGroupByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $agent_group_list = $rank_agent_group_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleLYAgentGroupAllPermission($item['level'], $item['rank_id'], $agent_group_list, $all_agent_group_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }


    /**
     * 同步删除结算主体权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteSettleCompany(array $position_list, array $platform_list)
    {
        $settle_company_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['settle_company_name_list']) && is_array($platform['settle_company_name_list'])) {
                    foreach ($platform['settle_company_name_list'] as $settle_company_name) {
                        $settle_company_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'settle_company_name' => $settle_company_name,
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }

        }

        if (!empty($settle_company_list)) {
            $rank_settle_company_model = new RankSettleCompanyModel();
            $rank_settle_company_model->deleteByList($settle_company_list);
        }
    }

    /**
     * 同步新增负责人权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddLeader(array $position_list, array $platform_list)
    {
        $leader_list = [];
        foreach ($position_list as $item) {
            foreach ($platform_list as $platform) {
                if (isset($platform['leader_list']) && is_array($platform['leader_list'])) {
                    foreach ($platform['leader_list'] as $leader) {
                        $leader_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'agent_leader' => $leader['agent_leader'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($leader_list)) {
            $rank_leader_model = new RankLeaderModel();
            $rank_leader_model->replace($leader_list);
            (new RankLeaderAgentModel())->deleteByLeaderList($leader_list);
            (new RankPermissionAllModel())->deleteLeaderByPositionList($position_list);
            $all_leader_list = (new V2DimAgentIdModel())->getAllAgentLeaderInPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $leader_list = $rank_leader_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleLeaderAllPermission($item['level'], $item['rank_id'], $leader_list, $all_leader_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }

    /**
     * 同步删除游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteLeader(array $position_list, array $platform_list)
    {
        $leader_list = [];
        foreach ($position_list as $item) {
            foreach ($platform_list as $platform) {
                if (isset($platform['leader_list']) && is_array($platform['leader_list'])) {
                    foreach ($platform['leader_list'] as $leader) {
                        $leader_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'agent_leader' => $leader['agent_leader'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($leader_list)) {
            $rank_leader_model = new RankLeaderModel();
            $rank_leader_model->deleteByList($leader_list);
            (new RankLeaderAgentModel())->deleteByLeaderList($leader_list);
            (new RankPermissionAllModel())->deleteLeaderByPositionList($position_list);
            $all_leader_list = (new V2DimAgentIdModel())->getAllAgentLeaderInPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $leader_list = $rank_leader_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleLeaderAllPermission($item['level'], $item['rank_id'], $leader_list, $all_leader_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }


    /**
     * 同步新增负责人权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddAgentGroup(array $position_list, array $platform_list)
    {
        $agent_group_list = [];
        foreach ($position_list as $item) {
            foreach ($platform_list as $platform) {
                if (isset($platform['agent_list']) && is_array($platform['agent_list'])) {
                    foreach ($platform['agent_list'] as $leader) {
                        $agent_group_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'agent_group_id' => $leader['agent_group_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($agent_group_list)) {
            $rank_agent_group_model = new RankAgentGroupModel();
            $rank_agent_group_model->replace($agent_group_list);
            (new RankAgentModel())->deleteByAgentgroupList($agent_group_list);
            (new RankPermissionAllModel())->deleteAgentGroupByPositionList($position_list);
            $all_agent_group_list = (new V2DimAgentIdModel())->getAllAgentGroupByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $agent_group_list = $rank_agent_group_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleAgentGroupAllPermission($item['level'], $item['rank_id'], $agent_group_list, $all_agent_group_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }

    /**
     * 同步删除游戏权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteAgentGroup(array $position_list, array $platform_list)
    {
        $agent_group_list = [];
        foreach ($position_list as $item) {
            foreach ($platform_list as $platform) {
                if (isset($platform['agent_list']) && is_array($platform['agent_list'])) {
                    foreach ($platform['agent_list'] as $leader) {
                        $agent_group_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'agent_group_id' => $leader['agent_group_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($agent_group_list)) {
            $rank_agent_group_model = new RankAgentGroupModel();
            foreach (array_chunk($agent_group_list, 1000) as $chunk) {
                $rank_agent_group_model->deleteByList($chunk);
                (new RankAgentModel())->deleteByAgentgroupList($chunk);
            }
            (new RankPermissionAllModel())->deleteAgentGroupByPositionList($position_list);
            $all_agent_group_list = (new V2DimAgentIdModel())->getAllAgentGroupByPlatformList(array_keys(EnvConfig::PLATFORM_MAP));
            $rank_permission_list = [];
            foreach ($position_list as $item) {
                $agent_group_list = $rank_agent_group_model->getAll($item['level'], $item['rank_id']);
                $rank_permission_list = array_merge(
                    $rank_permission_list,
                    self::handleAgentGroupAllPermission($item['level'], $item['rank_id'], $agent_group_list, $all_agent_group_list)
                );
            }
            if (!empty($rank_permission_list)) {
                (new RankPermissionAllModel())->replace($rank_permission_list);
            }
        }
    }

    /**
     *
     * @return array
     */
    public function getLoginUserAllRouterCascader()
    {
        $route_model = new RouteModel();

        // level 就是超管级别 拿所有
        if (UserService::isSuperManager()) {
            $route_item = $route_model->getAll();
        } else {
            // 非超管 拿用户所有的route_id
            $user_level = Container::getSession()->get('user_level');
            $route_res_list = (new RankRouteListModel())->getAllByUserLevel($user_level);
            $route_str = $route_res_list->reduce(function ($carry, $item) {
                return $carry . $item->route_list . ',';
            });
            $route_item = $route_model->getAllByRouteStr($route_str);
        }
        $route_cascader = $route_model->getCascader($route_item);
        return [
            'route_cascader' => $route_cascader,
        ];
    }

    /**
     * @param $level
     * @param $rank_id
     * @param $module
     *
     * @return array
     */
    public function getRankPermissionDetail($level, $rank_id, $module)
    {
        $route_model = new RouteModel();
        // level 就是超管级别 拿所有
        if ($level === UserService::LEVEL_SUPER) {
            $route_item = $route_model->getAllByModule($module);
            $route_permission_list = (new RoutePermissionModel())->getAll();
            $platform = (new V2DimGameIdModel())->getPlatformList();
            $sbu_list = (new SBULogic())->getSBUListWithoutTw()->keys();
        } else {
            $platform = (new RankPlatformModel())->getAllByRank($level, $rank_id);
            $route_str = (new RankRouteListModel())->getData($level, $rank_id);
            $route_item = $route_model->getAllByRouteStr($route_str);
            $route_permission_list = (new RankRoutePermissionModel())->getAll($level, $rank_id);
            $sbu_list = (new RankSbuModel())->getAllByRank($level, $rank_id);
        }
        $route_cascader = $route_model->getCascader($route_item);
        return [
            'route_cascader' => $route_cascader,
            'route_permission_list' => FrontendTool::formatRoutePermission($route_permission_list),
            'position_cascader' => $this->getCoPositionList($level, $rank_id, $module),
            'platform_list' => $platform->map(function ($platform) {
                return [
                    'platform_id' => $platform,
                    'platform_name' => EnvConfig::PLATFORM_MAP[$platform]
                ];
            }),
            'sbu_list' => $sbu_list,
        ];
    }

    /**
     * 获取某个等级的可协作岗位权限
     *
     * @param $level
     * @param $rank_id
     * @param $module
     *
     * @return array
     */
    public function getCoPositionList($level, $rank_id, $module)
    {
        $level = intval($level);
        $rank_id = intval($rank_id);
        $position_list = (new ViewPositionModel())->getAllByModule($module);
        if ($level !== UserService::LEVEL_SUPER) {
            $enable_position_list = collect();
            foreach ($position_list as $item) {
                $item->disabled = true;
                switch ($level) {
                    case UserService::LEVEL_PLATFORM:
                        if ($rank_id === $item->platform_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_DEPARTMENT:
                        if ($rank_id === $item->department_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_DEPARTMENT_GROUP:
                        if ($rank_id === $item->department_group_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                        if ($rank_id === $item->department_group_position_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                        if ($rank_id === $item->department_group_position_worker_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_SIX:
                        if ($rank_id === $item->department_six_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_SEVEN:
                        if ($rank_id === $item->department_seven_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                    case UserService::LEVEL_EIGHT:
                        if ($rank_id === $item->department_eight_id) {
                            $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                            $item->disabled = false;
                        }
                        break;
                }
            }

            $rank_co_position_list = (new RankCoPositionModel())->getAllByRank($level, $rank_id);
            foreach ($rank_co_position_list as $co_position) {
                foreach ($position_list as $item) {
                    if ($item->level === $co_position->co_level && $item->rank_id === $co_position->co_rank_id) {
                        $enable_position_list["{$item->level}-{$item->rank_id}"] = $item;
                        $item->disabled = false;
                    }
                }
            }

            $co_position_list = [
                'platform_list' => $position_list
                    ->where('level', UserService::LEVEL_PLATFORM)
                    ->whereIn('platform_id', $enable_position_list->pluck('platform_id'))
                    ->values(),
                'department_list' => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT)
                    ->whereIn('department_id', $enable_position_list->pluck('department_id'))
                    ->values(),
                'department_group_list' => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT_GROUP)
                    ->whereIn('department_group_id', $enable_position_list->pluck('department_group_id'))
                    ->values(),
                'department_group_position_list' => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION)
                    ->whereIn('department_group_position_id', $enable_position_list->pluck('department_group_position_id'))
                    ->values(),
                'department_group_position_worker_list' => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER)
                    ->whereIn('department_group_position_worker_id', $enable_position_list->pluck('department_group_position_worker_id'))
                    ->values(),
                'department_six_list' => $position_list
                    ->where('level', UserService::LEVEL_SIX)
                    ->whereIn('department_six_id', $enable_position_list->pluck('department_six_id'))
                    ->values(),
                'department_seven_list' => $position_list
                    ->where('level', UserService::LEVEL_SEVEN)
                    ->whereIn('department_seven_id', $enable_position_list->pluck('department_seven_id'))
                    ->values(),
                'department_eight_list' => $position_list
                    ->where('level', UserService::LEVEL_EIGHT)
                    ->whereIn('department_eight_id', $enable_position_list->pluck('department_eight_id'))
                    ->values(),
            ];
        } else {
            $co_position_list = [
                'platform_list' => $position_list->where('level', UserService::LEVEL_PLATFORM)->values(),
                'department_list' => $position_list->where('level', UserService::LEVEL_DEPARTMENT)->values(),
                'department_group_list' => $position_list->where('level', UserService::LEVEL_DEPARTMENT_GROUP)->values(),
                'department_group_position_list' => $position_list->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION)->values(),
                'department_group_position_worker_list' => $position_list->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER)->values(),
                'department_six_list' => $position_list->where('level', UserService::LEVEL_SIX)->values(),
                'department_seven_list' => $position_list->where('level', UserService::LEVEL_SEVEN)->values(),
                'department_eight_list' => $position_list->where('level', UserService::LEVEL_EIGHT)->values(),
            ];
        }

        return $co_position_list;
    }

    /**
     * 获取某个等级的可协作权限
     *
     * @param $level
     * @param $rank_id
     * @param $module
     *
     * @return array
     */
    public function getCoUserList($level, $rank_id, $module)
    {
        $co_position_list = $this->getCoPositionList($level, $rank_id, $module);

        $position_list = collect();
        /** @var Collection $co_position */
        foreach ($co_position_list as $co_position) {
            $position_list = $position_list->merge(
                $co_position
                    ->filter(function ($item) {
                        return !isset($item->disabled) || $item->disabled === false;
                    })
                    ->map(function ($item) {
                        return "($item->platform_id, $item->department_id, $item->department_group_id,
                         $item->department_group_position_id, $item->department_group_position_worker_id,
                         $item->department_six_id,$item->department_seven_id,$item->department_eight_id)";
                    })
            );
        }
        if ($position_list->isNotEmpty()) {
            $user_list = (new UserLevelModel())->getAllInPosition($position_list->toArray(), $module);
            $co_position_list['user_list'] = $user_list;
        } else {
            $co_position_list['user_list'] = collect();
        }

        return $co_position_list;
    }

    /**
     * 添加路由权限
     *
     * @param $level
     * @param $rank_id
     * @param $route_permission_ids
     *
     * @return bool
     */
    public function addRankRoutePermission($level, $rank_id, $route_permission_ids)
    {
        return (new RankRoutePermissionModel())->addMultiple($level, $rank_id, $route_permission_ids);
    }

    /**
     * 添加平台权限
     *
     * @param $level
     * @param $rank_id
     * @param $platform_list
     *
     * @return bool
     */
    public function addRankPlatform($level, $rank_id, $platform_list)
    {
        $model = new RankPlatformModel();
        $rank_platform_list = [];
        foreach ($platform_list as $item) {
            $rank_platform_list[] = $item['platform'];
        }

        return $model->addMultiple($level, $rank_id, $rank_platform_list);
    }

    /**
     * 添加事业部权限
     *
     * @param $level
     * @param $rank_id
     * @param $sbu_list
     *
     * @return bool
     */
    public function addRankSbu($level, $rank_id, $sbu_list)
    {
        $model = new RankSbuModel();
        $rank_sbu_list = [];
        foreach ($sbu_list as $item) {
            $rank_sbu_list[] = $item;
        }

        return $model->addMultiple($level, $rank_id, $rank_sbu_list);
    }

    /**
     * 获取某个等级的所有路由权限（返回前端需要的结构）
     *
     * @param $level
     * @param $rank_id
     *
     * @return array
     */
    public function getRankRoutePermissionList($level, $rank_id)
    {
        $route_permission_list = FrontendTool::formatRoutePermission((new RankRoutePermissionModel())->getAll($level, $rank_id));
        $route_id_list = (new RankRouteListModel())->getData($level, $rank_id);

        $route_model = new RouteModel();
        $route_tree = $route_model->getTree($route_model->getAllByRouteStr($route_id_list));

        $route_permission_ids = [];
        foreach ($route_tree as $key => $father) {
            // 下级无选择
            if (!isset($father['children'])) {
                continue;
            }
            foreach ($father['children'] as $children) {
                $route_permission_ids[$children['id']] = [$father['id'], $children['id']];
                $origin_length = count($route_permission_ids);
                foreach ($route_permission_list as $route_permission) {
                    if ($route_permission['route_id'] === $children['id']) {
                        if (isset($route_permission['type']) && $route_permission['type']) {
                            $route_permission_ids[$children['id'] . '-' . $route_permission['id']] = [$father['id'], $children['id'], $route_permission['cate_name'], $route_permission['type'], $route_permission['id']];
                        } else {
                            $route_permission_ids[$children['id'] . '-' . $route_permission['id']] = [$father['id'], $children['id'], $route_permission['cate_name'], $route_permission['id']];
                        }
                    }
                }
                if ($origin_length !== count($route_permission_ids)) {
                    unset($route_permission_ids[$children['id']]);
                }

            }
        }

        return array_values($route_permission_ids);
    }

    /**
     * 是否能删除路由 要判断时候下一级是否有该渠道
     * 如果下一级有该权限 本级无法删除
     *
     * @param            $next_level
     * @param            $next_rank_ids
     * @param            $route_list
     * @param Collection $rank_list
     *
     * <AUTHOR>
     */
    public function canDelRouteList($next_level, $next_rank_ids, $route_list, $rank_list, $module = 'dms')
    {
        $route_arr = explode(',', $route_list);
        $rank_route_list = (new RankRouteListModel())->getAllInRank($next_level, $next_rank_ids, $module);
        foreach ($rank_route_list as $item) {
            $rank_route_arr = explode(',', $item->route_list);
            if (empty($item->route_list)) {
                continue;
            }
            if (!empty(array_diff($rank_route_arr, $route_arr))) {
                $rank_item = $rank_list->where('id', $item->rank_id)->first();
                throw new AppException("下一级{$rank_item->name}导航权限较高，无法删除(ps:多出的route_id:" . implode(',', array_diff($rank_route_arr, $route_arr)));
            }
        }
    }

    /**
     * 是否能删除路由 要判断时候下一级是否有该渠道
     * 如果下一级有该权限 本级无法删除
     *
     * @param            $next_level
     * @param            $next_rank_ids
     * @param            $route_permission_ids
     * @param Collection $rank_list
     *
     * <AUTHOR>
     */
    public function canDelRoutePermission($next_level, $next_rank_ids, $route_permission_ids, $rank_list, $module = 'dms')
    {
        $rank_route_list = (new RankRoutePermissionModel())->getRankRoutePermissionIdInRank($next_level, $next_rank_ids, $module);
        foreach ($rank_route_list as $item) {
            $rank_route_arr = explode(',', trim($item->route_permission_list, ','));
            if (!empty(array_diff($rank_route_arr, $route_permission_ids))) {
                $rank_item = $rank_list->where('id', $item->rank_id)->first();
                throw new AppException("下一级{$rank_item->name}路由权限较高，无法删除(ps:多出的route_permission_id:" . implode(',', array_diff($rank_route_arr, $route_permission_ids)));
            }
        }
    }

    /**
     * 处理全选的游戏权限
     *
     * @param $level
     * @param $rank_id
     * @param $root_game_list
     * @param $all_root_game_list
     *
     * @return array
     */
    static public function handleGameAllPermission($level, $rank_id, $root_game_list, $all_root_game_list = null)
    {
        // 把root_game_list转成集合好处理  (按平台分组)
        $root_game_list = collect($root_game_list)->groupBy('platform');


        // 获取所有的root_game_id 然后比对
        if (is_null($all_root_game_list)) {
            // 获取所有的平台
            $platform_list = $root_game_list->keys();
            $all_root_game_list = (new V2DimGameIdModel())->getAllRootGameByPlatformList($platform_list);
        }
        $rank_permission_list = [];

        foreach ($root_game_list as $platform => $list) {
            /*** @var $list Collection */
            $my_root_game_list = $list;
            $all_platform_root_game_list = $all_root_game_list->where('platform', $platform);
            $add_list = $all_platform_root_game_list->keyBy('root_game_id')->diffKeys($my_root_game_list->keyBy('root_game_id'));
            // 判断是不是全选有所根游戏  是的话入表。 否则要分开三个判断
            if ($add_list->isEmpty()) {
                $rank_permission_list [] = [
                    'level' => $level, 'rank_id' => $rank_id, 'platform' => $platform, 'type' => RankPermissionAllModel::TYPE_GAME
                ];
            }
//            游戏类型已经干掉了
//            else {
//                // 判断多个 plat_id否全选
//                foreach (PlatId::MAP as $plat_id => $plat_name) {
//                    $plat_all_list = $all_platform_root_game_list->where('plat_id', $plat_id)->keyBy('root_game_id');
//                    if ($plat_all_list->isNotEmpty()) {
//                        $add_list = $plat_all_list->diffKeys($my_root_game_list->where('plat_id', $plat_id)->keyBy('root_game_id'));
//                        if ($add_list->isEmpty()) {
//                            $rank_permission_list[] = [
//                                'level' => $level, 'rank_id' => $rank_id, 'platform' => $platform, 'type' => PlatId::platIDtoAllTypeMap($plat_id)
//                            ];
//                        }
//                    }
//                }
//            }
        }

        return $rank_permission_list;
    }


    static public function handleLYGameAllPermission($level, $rank_id, $game_list, $all_game_list = null)
    {
        // 把game_list转成集合好处理  (按平台分组)
        $game_list = collect($game_list)->groupBy('platform');

        if (is_null($all_game_list)) {
            // 获取所有的平台
            $platform_list = $game_list->keys();
            $all_game_list = (new ViewV2DimSiteIdModel())->getAllGameByPlatformList($platform_list);
        }

        $rank_permission_list = [];
        foreach ($game_list as $platform => $list) {
            // 判断是不是全选  是的话入表。
            /*** @var $list Collection */
            $add_list = $all_game_list->where('platform', $platform)->keyBy('game_id')->diffKeys($list->keyBy('game_id'));
            if ($add_list->isEmpty()) {
                $rank_permission_list [] = [
                    'level' => $level, 'rank_id' => $rank_id, 'platform' => $platform, 'type' => RankPermissionAllModel::TYPE_LY_GAME
                ];
            }
        }
        return $rank_permission_list;
    }

    /**
     * 处理全选的渠道组
     *
     * @param      $level
     * @param      $rank_id
     * @param      $agent_group_list
     * @param null $all_agent_group_list
     *
     * @return array
     */
    static public function handleLYAgentGroupAllPermission($level, $rank_id, $agent_group_list, $all_agent_group_list = null)
    {
        // 把ad_platform_list转成集合好处理  (按平台分组)
        $agent_group_list = collect($agent_group_list)->groupBy('platform');


        if (is_null($all_agent_group_list)) {
            // 获取所有的平台
            $platform_list = $agent_group_list->keys();
            $all_agent_group_list = (new ViewV2DimSiteIdModel())->getAllAgentGroupByPlatformList($platform_list);
        }

        $rank_permission_list = [];
        foreach ($agent_group_list as $platform => $list) {
            // 判断是不是全选  是的话入表。
            /*** @var $list Collection */
            $add_list = $all_agent_group_list->where('platform', $platform)->keyBy('agent_group_id')->diffKeys($list->keyBy('agent_group_id'));

            if ($add_list->isEmpty()) {
                $rank_permission_list [] = [
                    'level' => $level, 'rank_id' => $rank_id, 'platform' => $platform, 'type' => RankPermissionAllModel::TYPE_LY_AGENT_GROUP
                ];
            }
        }

        return $rank_permission_list;
    }

    /**
     * 处理全选的负责人权限
     *
     * @param      $level
     * @param      $rank_id
     * @param      $leader_list
     * @param null $all_leader_list
     *
     * @return array
     */
    static public function handleLeaderAllPermission($level, $rank_id, $leader_list, $all_leader_list = null)
    {
        // 把leader_list转成集合好处理  (按平台分组)
        $leader_list = collect($leader_list)->groupBy('platform');

        if (is_null($all_leader_list)) {
            // 获取所有的平台
            $platform_list = $leader_list->keys();
            $all_leader_list = (new V2DimAgentIdModel())->getAllAgentLeaderInPlatformList($platform_list);
        }

        $rank_permission_list = [];
        foreach ($leader_list as $platform => $list) {
            // 判断是不是全选  是的话入表。
            /*** @var $list Collection */
            $add_list = $all_leader_list->where('platform', $platform)->keyBy('agent_leader')->diffKeys($list->keyBy('agent_leader'));
            if ($add_list->isEmpty()) {
                $rank_permission_list [] = [
                    'level' => $level, 'rank_id' => $rank_id, 'platform' => $platform, 'type' => RankPermissionAllModel::TYPE_LEADER
                ];
            }
        }
        return $rank_permission_list;
    }

    /**
     * 处理全选的渠道组
     *
     * @param      $level
     * @param      $rank_id
     * @param      $agent_group_list
     * @param null $all_agent_group_list
     *
     * @return array
     */
    static public function handleAgentGroupAllPermission($level, $rank_id, $agent_group_list, $all_agent_group_list = null)
    {
        // 把ad_platform_list转成集合好处理  (按平台分组)
        $agent_group_list = collect($agent_group_list)->groupBy('platform');


        if (is_null($all_agent_group_list)) {
            // 获取所有的平台
            $platform_list = $agent_group_list->keys();
            $all_agent_group_list = (new V2DimAgentIdModel())->getAllAgentGroupByPlatformList($platform_list);
        }

        $rank_permission_list = [];
        foreach ($agent_group_list as $platform => $list) {
            // 判断是不是全选  是的话入表。
            /*** @var $list Collection */
            $add_list = $all_agent_group_list->where('platform', $platform)->keyBy('agent_group_id')->diffKeys($list->keyBy('agent_group_id'));

            if ($add_list->isEmpty()) {
                $rank_permission_list [] = [
                    'level' => $level, 'rank_id' => $rank_id, 'platform' => $platform, 'type' => RankPermissionAllModel::TYPE_AGENT_GROUP
                ];
            }
        }

        return $rank_permission_list;
    }

    /**
     * route uri 映射到中文路由名
     *
     * @return array
     */
    static public function routeUriNameMap()
    {
        $all_router = (new RouteModel())->getAll();
        $map = [];
        foreach ($all_router as $item) {
            $map[$item->uri] = $item->title;
        }

        return $map;
    }

    /**
     * 同步负责人组权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddLeaderGroup(array $position_list, array $platform_list)
    {
        $leader_group_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['leader_group_list']) && is_array($platform['leader_group_list'])) {
                    foreach ($platform['leader_group_list'] as $leader_group) {
                        $leader_group_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'leader_group_id' => $leader_group['leader_group_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }
        }

        if (!empty($leader_group_list)) {
            $rank_leader_group_model = new RankLeaderGroupModel();
            $rank_leader_group_model->replace($leader_group_list);
        }
    }

    /**
     * 同步负责人组权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteLeaderGroup(array $position_list, array $platform_list)
    {
        $leader_group_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {
                if (isset($platform['leader_group_list']) && is_array($platform['leader_group_list'])) {
                    foreach ($platform['leader_group_list'] as $leader_group) {
                        $leader_group_list[] = [
                            'rank_id' => $item['rank_id'],
                            'level' => $item['level'],
                            'leader_group_id' => $leader_group['leader_group_id'],
                            'platform' => $platform['platform'],
                        ];
                    }
                }
            }

        }

        if (!empty($leader_group_list)) {
            $rank_leader_group_model = new RankLeaderGroupModel();
            $rank_leader_group_model->deleteByList($leader_group_list);
        }
    }

    /**
     * 同步新增交集权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddIntersectLeaderAndAgent(array $position_list, array $platform_list)
    {
        /**
         * 新增交集权限的逻辑
         * 注意: 这里的$position_list选中的职位,仅仅是选中的
         * 前置条件：
         *      1.选中交集后，所有下级都要交集
         *      2.需要确保每个rank_id都有选 （负责人或负责人组）和（渠道）
         */
        $leader_group_model = new RankLeaderGroupModel();
        $leader_model = new RankLeaderModel();
        $agent_group_model = new RankAgentGroupModel();
        $intersect_leader_and_agent_list = [];
        $selected_level_and_rank_ids = Collection::make($position_list);
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {

                if (isset($platform['is_intersect_leader_and_agent']) && $platform['is_intersect_leader_and_agent'] == 1) {

                    // 为了防止下级的权限大过上级，所以选中的职位有下级职位的时候，下级都要勾选上，因此要判断操作者是否有选择其所有下级
                    switch ($item['level']) {
                        case UserService::LEVEL_PLATFORM:
                            $rank_data = (new PlatformModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentModel())->getAllByPlatformId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_DEPARTMENT:
                            $rank_data = (new DepartmentModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentGroupModel())->getAllByDepartmentId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP:
                            $rank_data = (new DepartmentGroupModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentGroupPositionModel())->getAllByDepartmentGroupId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                            $rank_data = (new DepartmentGroupPositionModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentGroupPositionWorkerModel)->getAllByDepartmentGroupPositionId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                            $rank_data = (new DepartmentGroupPositionWorkerModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentSixLevelModel())->getAllByLastLevelID($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_SIX:
                            $rank_data = (new DepartmentSixLevelModel())->getData($item['rank_id']);
                            $next_rank_list = ((new DepartmentSevenLevelModel()))->getAllByLastLevelID($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_SEVEN:
                            $rank_data = (new DepartmentSevenLevelModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentEightLevelModel)->getAllByLastLevelID($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            break;
                        case UserService::LEVEL_EIGHT:
                            $rank_data = (new DepartmentEightLevelModel())->getData($item['rank_id']);
                            $next_rank_list = Collection::make();
                            $next_rank_ids = Collection::make();// 这个职位无下级咯，搞个空
                            break;
                        default:
                            throw new AppException('等级出错');
                    }

                    $next_level = $item['level'] + 1;
                    $diff_rank_ids = $next_rank_ids->diff($selected_level_and_rank_ids->where('level', $next_level)->pluck('rank_id'));
                    if ($diff_rank_ids->isNotEmpty()) {
                        $name_list = $next_rank_list->whereIn('id', $diff_rank_ids)->pluck('name')->join(',');
                        throw new AppException("$name_list 的上级被选中，为了防止权限错误，<$name_list>也应该被选中，请重新操作后提交");
                    }

                    // 负责组和负责人
                    $leader_group_result = $leader_group_model->getAll($item['level'], $item['rank_id'], $platform['platform']);
                    $leader_result = $leader_model->getAll($item['level'], $item['rank_id'], $platform['platform']);
                    // 渠道组
                    $agent_group_result = $agent_group_model->getAll($item['level'], $item['rank_id'], $platform['platform']);
                    // 判断该职位有没有选 （负责人或负责人组）和（渠道组），无则不符合要求
                    if (($leader_group_result->isEmpty() && $leader_result->isEmpty()) || $agent_group_result->isEmpty()) {
                        throw new AppException("职位 <{$rank_data['name']}> 存在以下失败原因：勾选取交集按钮时，负责人或负责人组必选，渠道也必选，请检阅所选职位是否符合要求");
                    }

                    $intersect_leader_and_agent_list[] = [
                        'level' => $item['level'],
                        'rank_id' => $item['rank_id'],
                        'platform' => $platform['platform'],
                        'intersect_leader_and_agent' => 1
                    ];
                }

            }
        }

        if (!empty($intersect_leader_and_agent_list)) {
            $rank_intersect_leader_and_agent_model = new RankIntersectLeaderAndAgentModel();
            $rank_intersect_leader_and_agent_model->replace($intersect_leader_and_agent_list);
        }
    }

    /**
     * 同步删除交集权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteIntersectLeaderAndAgent(array $position_list, array $platform_list)
    {
        $intersect_leader_and_agent_list = [];
        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {

                if (isset($platform['is_intersect_leader_and_agent']) && $platform['is_intersect_leader_and_agent'] == 1) {
                    // 删除交集只需要校验上级是否为交集,如果上级为交集,则不能删除
                    switch ($item['level']) {
                        case UserService::LEVEL_PLATFORM:
                            $rank_data = [];// 平台级别没有上级了,可以随便删
                            $pre_rank_id = 0;
                            break;
                        case UserService::LEVEL_DEPARTMENT:
                            $rank_data = (new DepartmentModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['platform_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP:
                            $rank_data = (new DepartmentGroupModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                            $rank_data = (new DepartmentGroupPositionModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_group_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                            $rank_data = (new DepartmentGroupPositionWorkerModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_group_position_id'];
                            break;
                        case UserService::LEVEL_SIX:
                            $rank_data = (new DepartmentSixLevelModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_group_position_worker_id'];
                            break;
                        case UserService::LEVEL_SEVEN:
                            $rank_data = (new DepartmentSevenLevelModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_six_id'];
                            break;
                        case UserService::LEVEL_EIGHT:
                            $rank_data = (new DepartmentEightLevelModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_seven_id'];
                            break;
                        default:
                            throw new AppException('等级出错');
                    }

                    if ($item['level'] != UserService::LEVEL_PLATFORM) {
                        $pre_rank_is_intersect_data = (new RankIntersectLeaderAndAgentModel())->getAll($item['level'] - 1, $pre_rank_id, $platform['platform']);
                        if ($pre_rank_is_intersect_data->isNotEmpty()) {
                            throw new AppException("职位 <{$rank_data['name']}> 的上级为交集权限, 无法删除<{$rank_data['name']}>权限, 请删除其上级交集权限后再操作");
                        }
                    }

                    $intersect_leader_and_agent_list[] = [
                        'level' => $item['level'],
                        'rank_id' => $item['rank_id'],
                        'platform' => $platform['platform'],
                        'intersect_leader_and_agent' => 1
                    ];
                }

            }
        }

        if (!empty($intersect_leader_and_agent_list)) {
            $rank_intersect_leader_and_agent_model = new RankIntersectLeaderAndAgentModel();
            $rank_intersect_leader_and_agent_model->deleteByList($intersect_leader_and_agent_list);
        }
    }

    /**
     * 同步买量/发行权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncAddProxyType(array $position_list, array $platform_list)
    {
        $proxy_type_list = [];

        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {

                // 0：不操作，1：买量，2：发行，3：不限
                if (isset($platform['proxy_type']) && $platform['proxy_type'] != 0) {

                    switch ($item['level']) {
                        case UserService::LEVEL_PLATFORM:
                            $rank_data = (new PlatformModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentModel())->getAllByPlatformId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = 0;
                            break;
                        case UserService::LEVEL_DEPARTMENT:
                            $rank_data = (new DepartmentModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentGroupModel())->getAllByDepartmentId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = $rank_data['platform_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP:
                            $rank_data = (new DepartmentGroupModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentGroupPositionModel())->getAllByDepartmentGroupId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = $rank_data['department_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                            $rank_data = (new DepartmentGroupPositionModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentGroupPositionWorkerModel)->getAllByDepartmentGroupPositionId($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = $rank_data['department_group_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                            $rank_data = (new DepartmentGroupPositionWorkerModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentSixLevelModel())->getAllByLastLevelID($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = $rank_data['department_group_position_id'];
                            break;
                        case UserService::LEVEL_SIX:
                            $rank_data = (new DepartmentSixLevelModel())->getData($item['rank_id']);
                            $next_rank_list = ((new DepartmentSevenLevelModel()))->getAllByLastLevelID($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = $rank_data['department_group_position_worker_id'];
                            break;
                        case UserService::LEVEL_SEVEN:
                            $rank_data = (new DepartmentSevenLevelModel())->getData($item['rank_id']);
                            $next_rank_list = (new DepartmentEightLevelModel)->getAllByLastLevelID($item['rank_id']);
                            $next_rank_ids = $next_rank_list->pluck('id');
                            $pre_rank_id = $rank_data['department_six_id'];
                            break;
                        case UserService::LEVEL_EIGHT:
                            $rank_data = (new DepartmentEightLevelModel())->getData($item['rank_id']);
                            $next_rank_list = Collection::make();
                            $next_rank_ids = Collection::make();// 这个职位无下级咯，搞个空
                            $pre_rank_id = $rank_data['department_seven_id'];
                            break;
                        default:
                            throw new AppException('等级出错');
                    }

                    $pre_level = $item['level'] - 1;
                    $next_level = $item['level'] + 1;

                    if ($platform['proxy_type'] == 1 || $platform['proxy_type'] == 2) {

                        // 判断上级是否为不限or和当前选中的类型一致or空
                        if ($item['level'] != UserService::LEVEL_PLATFORM) {
                            $pre_proxy_type = (new RankProxyTypeModel())->getAll($pre_level, $pre_rank_id, $platform['platform']);
                            if ($pre_proxy_type->isNotEmpty() && $pre_proxy_type->first()->proxy_type != 3 && $pre_proxy_type->first()->proxy_type != $platform['proxy_type']) {
                                throw new AppException("职位 <{$rank_data['name']}> - {$platform['platform']} 的上级买量/发行权限错误，确保上级也为不限或者和当前选中的权限一致");
                            }
                        }

                        // 下级判断麻烦咯
                        if ($next_rank_ids->isNotEmpty()) {

                            // 判断下级是否都有配置proxy_type，理应一个职位一条记录，判断依据：职位数 == 记录数
                            $next_proxy_type_list = (new RankProxyTypeModel())->getAllInRank($next_level, $next_rank_ids);
                            $next_proxy_type = $next_proxy_type_list->where('platform', $platform['platform']);
                            if ($next_proxy_type->count() != $next_rank_ids->count()) {
                                throw new AppException("职位 <{$rank_data['name']}> - {$platform['platform']} 的下级买量/发行权限错误，请检查其下级是否有配置买量/发行权限");
                            }

                            // 轮训每条记录是否和选中的proxy_type一致
                            foreach ($next_proxy_type as $next_proxy_type_item) {
                                if ($next_proxy_type_item->proxy_type != $platform['proxy_type']) {
                                    throw new AppException("您当前选中买量或发行，职位 <{$rank_data['name']}> - {$platform['platform']} 的下级里，包含和您当前选中的权限不一致的选项，为了防止权限越级，请确保下级权限等于或小于当前职位");
                                }
                            }
                        }

                    } elseif ($platform['proxy_type'] == 3) {

                        // 上级可以是空或者是不限
                        if ($item['level'] != UserService::LEVEL_PLATFORM) {
                            $pre_proxy_type = (new RankProxyTypeModel())->getAll($pre_level, $pre_rank_id, $platform['platform']);
                            if ($pre_proxy_type->isNotEmpty() && $pre_proxy_type->first()->proxy_type != 3) {
                                throw new AppException("职位 <{$rank_data['name']}> - {$platform['platform']} 的上级买量/发行权限错误，当前选中不限，确保上级也为不限");
                            }
                        }

                        // 因为选的是不限，下级就不用判断了

                    } else {
                        throw new AppException("买量/发行类型错误");
                    }

                    $now = time();
                    $proxy_type_list[] = [
                        'level' => $item['level'],
                        'rank_id' => $item['rank_id'],
                        'platform' => $platform['platform'],
                        'proxy_type' => $platform['proxy_type'],
                        'create_time' => $now,
                        'update_time' => $now
                    ];
                }

            }
        }

        if (!empty($proxy_type_list)) {
            $rank_proxy_type_model = new RankProxyTypeModel();
            $rank_proxy_type_model->replace($proxy_type_list);
        }
    }

    /**
     * 删除买量/发行权限
     *
     * @param array $position_list [[level => 1, rank_id => 1], [level => 2, rank_id => 4]...]
     * @param array $platform_list
     */
    public function syncDeleteProxyType(array $position_list, array $platform_list)
    {
        $proxy_type_list = [];

        foreach ($position_list as $item) {
            // 组装数据
            foreach ($platform_list as $platform) {

                // 0：不操作，1：买量，2：发行，3：不限
                if (isset($platform['proxy_type']) && $platform['proxy_type'] != 0) {

                    switch ($item['level']) {
                        case UserService::LEVEL_PLATFORM:
                            $rank_data = (new PlatformModel())->getData($item['rank_id']);
                            $pre_rank_id = 0;
                            break;
                        case UserService::LEVEL_DEPARTMENT:
                            $rank_data = (new DepartmentModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['platform_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP:
                            $rank_data = (new DepartmentGroupModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                            $rank_data = (new DepartmentGroupPositionModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_group_id'];
                            break;
                        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                            $rank_data = (new DepartmentGroupPositionWorkerModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_group_position_id'];
                            break;
                        case UserService::LEVEL_SIX:
                            $rank_data = (new DepartmentSixLevelModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_group_position_worker_id'];
                            break;
                        case UserService::LEVEL_SEVEN:
                            $rank_data = (new DepartmentSevenLevelModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_six_id'];
                            break;
                        case UserService::LEVEL_EIGHT:
                            $rank_data = (new DepartmentEightLevelModel())->getData($item['rank_id']);
                            $pre_rank_id = $rank_data['department_seven_id'];
                            break;
                        default:
                            throw new AppException('等级出错');
                    }

                    $pre_level = $item['level'] - 1;

                    if ($platform['proxy_type'] == 1 || $platform['proxy_type'] == 2 || $platform['proxy_type'] == 3) {

                        // 删除当前发行/买量权限，要判断上级是否为空或者为不限
                        if ($item['level'] != UserService::LEVEL_PLATFORM) {
                            $pre_proxy_type = (new RankProxyTypeModel())->getAll($pre_level, $pre_rank_id, $platform['platform']);
                            if ($pre_proxy_type->isNotEmpty() && $pre_proxy_type->first()->proxy_type != 3) {
                                throw new AppException("删除权限错误，职位 <{$rank_data['name']}> - {$platform['platform']} 的上级买量/发行权限错误，确保上级也为不限");
                            }
                        }

                        // 删除当前发行/买量权限，那么下级必定已经是发行/买量权限，删除当前职位的权限，相当于把当前设为不限，下级可以是空or买量or发行or不限，所以下级不用判断了

                    } else {
                        throw new AppException("买量/发行类型错误");
                    }

                    $proxy_type_list[] = [
                        'level' => $item['level'],
                        'rank_id' => $item['rank_id'],
                        'platform' => $platform['platform'],
                        'proxy_type' => 3,// 同步删除买量/发行权限，相当于把它们设为不限
                    ];
                }

            }
        }

        if (!empty($proxy_type_list)) {
            $rank_proxy_type_model = new RankProxyTypeModel();
            // 注意：此处与交集权限有所不同，交集权限是删除对应条件的记录，而对于proxy_type，它有一个不限的选项（相当于不设权限），那这里就不删除记录了，而是保存一条权限为不限的记录就好了
            $rank_proxy_type_model->replace($proxy_type_list);
        }
    }
}
