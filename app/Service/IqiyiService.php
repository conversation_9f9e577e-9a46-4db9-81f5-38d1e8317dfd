<?php


namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Model\SqlModel\DataMedia\OdsAQYAccountLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use App\Utils\Helpers;
use ZipArchive;

class IqiyiService implements AccountLeader, DMP
{
    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsAQYAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }

    /**
     * 新增人群包推送
     * @param $user_id
     * @param $username
     * @param $device_task_id
     * @param $audience_name
     * @param $audience_desc
     * @param $companies
     * @param $data_source_type
     * @return array
     */
    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type): array
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_EXPORT);
    }

    /**
     * 新增人群包推送 （从文件取数据）
     * @param $user_id
     * @param $username
     * @param $device_task_id
     * @param $audience_name
     * @param $audience_desc
     * @param $companies
     * @param $data_source_type
     * @return array
     */
    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type): array
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_FILE_ADDITION);
    }

    public function editPushAccountList($id, $account_ids)
    {
        //TODO
    }

    /**
     * @param $user_id
     * @param $username
     * @param $device_task_id
     * @param $audience_name
     * @param $audience_desc
     * @param $companies
     * @param $data_source_type
     * @param $addition_type
     * @return array
     */
    private function addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, $addition_type): array
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $device_task_info = (new DeviceTaskModel())->getData($device_task_id);
        $file_list = json_decode($device_task_info->file_list, true);
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::IQIYI);

            foreach ($file_list as $file) {
                if ($file['row'] <= 0) {
                    continue;
                }

                // 爱奇艺无OPEN_ID类型，过滤不处理
                if ($file['data_type'] == UserIDType::OPEN_ID) {
                    continue;
                }

                $device_audience_name = "{$audience_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']];
                $table_audience_id = $audience_model->add(
                    $user_id,
                    $username,
                    $device_task_id,
                    MediaType::IQIYI,
                    $company['name'],
                    $seed_account,
                    $device_audience_name,
                    $audience_desc,
                    $push_account_list,
                    $addition_type,
                    0,
                    '',
                    $media_account_info->platform,
                    $data_source_type ?: 'UID'
                );
                if (!($table_audience_id > 0)) {
                    Helpers::getLogger('audience')->error("add audience record fail", [
                        'device_task_id' => $device_task_id,
                        'account_id' => $seed_account,
                        'company' => $company,
                        'audience_name' => $audience_name,
                        'audience_desc' => $audience_desc,
                        'data_source_type' => $data_source_type,
                        'media_type' => MediaType::IQIYI,
                        'creator_id' => $user_id,
                        'creator' => $username,
                    ]);
                    continue;
                }
                $table_audience_ids[] = $table_audience_id;
                // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
                ///** @uses \App\Task\IqiyiTask::createDataSource() */
                //Container::getServer()->task(['action' => 'IqiyiTask.createDataSource', 'data' => ['table_audience_id' => $table_audience_id]]);
            }
        }
        return $table_audience_ids;
    }

    /**
     * 创建数据源文件
     *
     * @param $table_audience_id
     * @param array $file_list
     * @return array
     */
    public function createDataSourceFile($table_audience_id, $file): array
    {
        $row_limit = 4000000;
        $file_count = 0;
        $zip_paths = [];

        $filename = $file['name'];
        $user_id_type = $file['data_type'];
        $need_md5 = $file['need_md5'];
        $platform = $file['platform'];
        $device_id_types = $file['device_id_types'];
        if (!file_exists($filename)) {
            Helpers::getLogger('audience')->warning("file not exists", [
                'audience_id' => $table_audience_id,
                'filename' => $filename,
                'media_type' => MediaType::IQIYI,
                'media_type_name' => '爱奇艺',
            ]);
            return [];
        }
        $handle = fopen($filename, 'r');
        while (!feof($handle)) {
            $content = Helpers::getFileContent($handle, $row_limit, $need_md5);
            if (empty($content)) {
                break;
            }
            $basename = "$table_audience_id-{$file_count}";
            $zip_path = AudienceService::getAudienceDir(MediaType::IQIYI) . "$basename.zip";
            $sub_file = AudienceService::getAudienceDir(MediaType::IQIYI) . "$basename.txt";
            file_put_contents($sub_file, $content);
            $zip = new ZipArchive;
            $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
            if (true !== $code) {
                Helpers::getLogger('audience')->error("create data source zip fail", [
                    'table_audience_id' => $table_audience_id,
                    'zip_error_code' => $code,
                    'media_type' => MediaType::IQIYI,
                    'media_type_name' => '爱奇艺',
                ]);
                continue;
            }
            $zip->addFile($sub_file, "$basename.txt");
            $zip->close();
            unlink($sub_file);
            $zip_paths[] = [
                'file' => $zip_path,
                'user_id_type' => $user_id_type,
                'platform' => $platform,
                'device_id_types' => $device_id_types
            ];

            $file_count++;
        }
        fclose($handle);

        return $zip_paths;
    }

}