<?php

namespace App\Service;

use App\Logic\DSP\MaterialToolLogic;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\ToutiaoStar\TaskUpdateParam;
use App\Param\Material\VideoClipComposeParam;
use App\Param\Material\VideoClipProjectParam;
use App\Param\Material\VideoClipTaskParam;
use App\Utils\Helpers;
use Common\EnvConfig;

class ScriptMsgService
{
    const MATERIAL_CREATE_URL = EnvConfig::DOMAIN . '/dsp/MaterialStore/message?x-token=material_create';
    const AD_TASK_UPDATE_URL = EnvConfig::DOMAIN . '/dsp/ADServing/message?x-token=ad_task';
    const TOUTIAO_STAR_TASK_UPDATE_URL = EnvConfig::DOMAIN . '/dsp/ToutiaoStar/message?x-token=toutiao_star_task';
    const MATERIAL_FILE_EXPAND_TASK_UPDATE_URL = EnvConfig::DOMAIN . '/dsp/MaterialFileExpandTask/message?x-token=material_file_expand';
    const VIDEO_CLIP_TASK_UPDATE_URL = EnvConfig::DOMAIN . '/dsp/MaterialTool/message?x-token=video_clip_task'; // TODO 修改域名
    const VIDEO_CLIP_COMPOSE_UPDATE_URL = EnvConfig::DOMAIN . '/dsp/MaterialTool/message?x-token=video_clip_compose'; // TODO 修改域名


    static public function MaterialCreate($data)
    {
        $platform = '';
        $material_id = 0;
        $file = [];
        foreach ($data as $item) {
            if ($item['file_type'] === MaterialFileModel::FILE_TYPE_COVER) {
                continue;
            }
            $platform = $item['platform'];
            $material_id = $item['material_id'];
            $file[] = [
                "url" => $item['url'],
                "duration" => $item['duration'],
                "height" => $item['height'],
                "width" => $item['width'],
                "file_type" => $item['file_type']
            ];
        }
        $message = [
            'platform' => $platform,
            'material_id' => $material_id,
            'file' => $file,
        ];
        return self::send(self::MATERIAL_CREATE_URL, json_encode($message));
    }

    /**
     * ws更新广告任务的状态
     * @param $id
     * @param ADTaskUpdateParam $param
     * @return mixed
     */
    static public function ADTaskUpdate($id, ADTaskUpdateParam $param)
    {
        $message = array_merge(['id' => $id], $param->toArray());
        return self::send(self::AD_TASK_UPDATE_URL, json_encode($message));
    }

    /**
     * ws更新广告任务的状态
     * @param $id
     * @param ADTaskUpdateParam $param
     */
    static public function ADTaskUpdateByParam(ADTaskUpdateParam $param)
    {
        self::ADTaskUpdate($param->id, new ADTaskUpdateParam([
            'id' => $param->id,
            'agent_id' => $param->agent_id,
            'convert_id' => $param->convert_id,
            'ad1_name_text' => $param->ad1_name_text,
            'ad2_name_text' => $param->ad2_name_text,
            'site_id' => $param->site_id,
            'ad1_id' => $param->ad1_id,
            'ad2_id' => $param->ad2_id,
            'state_code' => $param->state_code,
            'task_state' => $param->task_state,
            'error_msg' => $param->error_msg,
            'creator_id' => $param->creator_id,
            'update_time' => time(),
        ]));
    }

    /**
     * ws更新星图发布任务的状态
     * @param $id
     * @param ADTaskUpdateParam $param
     */
    static public function ToutiaoStarTaskUpdateByParam($id, TaskUpdateParam $param)
    {
        $message = array_merge(['id' => $id], $param->toParam());
        return self::send(self::TOUTIAO_STAR_TASK_UPDATE_URL, json_encode($message));
    }


    /**
     * ws更新素材文件扩展任务的状态
     * @param $id
     * @param array $param
     * @return mixed
     */
    static public function materialFileExpandTaskUpdate($id, array $param)
    {
        $message = array_merge(['id' => $id], $param);
        return self::send(self::MATERIAL_FILE_EXPAND_TASK_UPDATE_URL, json_encode($message));
    }

    /**
     * ws更新视频混剪任务的状态
     * @param $id
     * @param VideoClipProjectParam $param
     * @return bool|string
     */
    static public function VideoClipTaskUpdateByParam($id, VideoClipProjectParam $param)
    {
        $message = array_merge(['id' => $id, 'message_type' => 'task'], $param->toParam());
        return self::send(self::VIDEO_CLIP_TASK_UPDATE_URL, json_encode($message));
    }

    /**
     * ws更新视频混剪组合的状态
     * @param $id
     * @param VideoClipComposeParam $param
     * @return bool|string
     */
    static public function VideoClipComposeUpdateByParam($id, VideoClipComposeParam $param)
    {
        $message = array_merge(['id' => $id, 'message_type' => 'compose'], $param->toParam());
        return self::send(self::VIDEO_CLIP_COMPOSE_UPDATE_URL, json_encode($message));
    }

    static public function send($url, $data)
    {
        $options = [
            'header' => [
                'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36',
                'Content-Type: application/json',
            ],
        ];
        return Helpers::postCurl($url, $data, $options);
    }
}
