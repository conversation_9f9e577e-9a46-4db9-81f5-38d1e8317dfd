<?php

namespace App\Service\SiteConfig;

use App\Constant\MediaType;
use App\Param\SiteOptionParam;
use App\Utils\Helpers;

class SiteConfig
{
    /**
     * 初始化媒体的对应 SiteConfig
     * @param SiteOptionParam $param
     * @return AbstractSiteConfig
     */
    static public function init(SiteOptionParam $param): AbstractSiteConfig
    {
        $media_name = Helpers::pascal(ucfirst(strtolower(MediaType::CONST_NAME_LIST[$param->media_type] ?? '')));
        $media_service_class = __NAMESPACE__ . '\\SiteConfig' . $media_name;
        if (class_exists($media_service_class) && !empty($media_name)) {
            return new $media_service_class($param);
        } else {
            return new SiteConfigCommon($param);
        }
    }
}
