<?php

namespace App\Service\SiteConfig;

use App\Constant\ActionTrackType;
use App\Constant\ConvertSourceType;
use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\ActionTrackTypeItem;
use App\Service\SiteConfig\SiteComponent\AkeyItem;
use App\Service\SiteConfig\SiteComponent\ConvertDataTypeItem;
use App\Service\SiteConfig\SiteComponent\ConvertSourceTypeItem;
use App\Service\SiteConfig\SiteComponent\ConvertToolkitItem;
use App\Service\SiteConfig\SiteComponent\ConvertTypeItem;
use App\Service\SiteConfig\SiteComponent\DeepExternalActionItem;
use App\Service\SiteConfig\SiteComponent\InputItem;
use App\Service\SiteConfig\SiteComponent\IsThirdItem;

abstract class AbstractSiteConfig
{
    /**
     * @var SiteOptionParam
     */
    protected $option_param;
    private $options = [];

    public function __construct(SiteOptionParam $param)
    {
        $this->option_param = $param;
    }

     public function getSiteOptions(): array
     {
         return $this->filterOptions($this->getOptions());
     }

    /**
     * @return mixed
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @param mixed $options
     */
    public function addOption($options): void
    {
        $this->options[] = $options;
    }

    /**
     * @param mixed $options
     */
    public function setOptions($options)
    {
        $this->options = array_merge($this->options, $options);
    }

    protected function filterOptions($options): array
    {
        return array_values(array_filter($options));
    }

    protected function genConvertSourceType(): array
    {
        $option_type = [
            ConvertSourceTypeItem::OPTION_API,
            ConvertSourceTypeItem::OPTION_SDK
        ];

        return $this->genConvertSourceTypeOptions($option_type);
    }

    protected function genConvertType(): array
    {
        return (new ConvertTypeItem($this->option_param->media_type, $this->option_param->plat_id, $this->option_param->platform))
            ->setDisabled($this->option_param->action)
            ->make();
    }

    protected function genDeepExternalAction(): array
    {
        return (new DeepExternalActionItem($this->option_param->media_type, $this->option_param->platform))
            ->setDisabled($this->option_param->action)
            ->make();
    }

    protected function genConvertDataType(): array
    {
        return (new ConvertDataTypeItem($this->option_param->media_type))
            ->setDisabled($this->option_param->action)
            ->make();
    }

    protected function genAkey(): array
    {
        return (new AkeyItem($this->option_param->media_type))->make();
    }

    /**
     * 生成落地页链接 input 框
     * @return array
     */
    protected function genExternalUrl(): array
    {
        if (
            $this->option_param->plat_id == PlatId::MINI
            && $this->option_param->mask !== 'compose'
            && $this->option_param->action !== 'edit'
        ) {
            return (new InputItem())
                ->setLabel('落地页链接')
                ->setProps('ext.external_url')
                ->setRequired(true)
                ->setTips(" 渠道：头条/UC \n 游戏：小游戏 \n 转化来源：落地页API(H5) \n 开启落地页链接")
                ->setValidator('^(#|(http(s?)):\/\/|\/\/)[^\s]+\\.[^\s]+$', '请输入以http://或https://开头的落地页链接')
                ->setCondition(['convert_source_type' => [ConvertSourceType::H5_API]])
                ->make();
        }

        return [];
    }

    protected function genIsThird(): array
    {
        if (in_array($this->option_param->platform, ['TW'])) {
            return (new IsThirdItem($this->option_param->platform))->make();
        }
        return [];
    }

    protected function genConvertToolkit(): array
    {
        return (new ConvertToolkitItem($this->option_param->action))
            ->make();
    }

    /**
     * 特殊媒体使用，只返回API转化来源，用于上报数据
     * @return array
     */
    public function genApiConvertSourceType(): array
    {
        $option_type = [
            ConvertSourceTypeItem::OPTION_API
        ];

        return $this->genConvertSourceTypeOptions($option_type, ConvertSourceType::API);
    }

    /**
     * 只返回落地页H5，媒体小游戏只可投落地页H5使用
     * @return array
     */
    public function genH5ConvertSourceType()
    {
        $option_type = [
            ConvertSourceTypeItem::OPTION_H5_API
        ];

        return $this->genConvertSourceTypeOptions($option_type, ConvertSourceType::H5_API);
    }

    /**
     * 返回所有可用转化来源，可投小游戏的媒体使用
     * @return array
     */
    public function genAllConvertSourceType()
    {
        $option_type = [
            ConvertSourceTypeItem::OPTION_API,
            ConvertSourceTypeItem::OPTION_SDK,
            ConvertSourceTypeItem::OPTION_H5_API
        ];

        return $this->genConvertSourceTypeOptions($option_type);
    }

    private function genConvertSourceTypeOptions($option_type, $default = '')
    {
        return (new ConvertSourceTypeItem($this->option_param->media_type, $this->option_param->game_type, $this->option_param->action, $option_type))
            ->setDisabled($this->option_param->action)
            ->setDefault($default)
            ->make();
    }

    /**
     * 生成点击监测类型
     * @param $option_type
     * @param int $default
     * @return array
     */
    public function genActionTrackType($option_type, $default = ActionTrackType::DEFAULT): array
    {
        if ($this->option_param->mask !== 'compose') {
            $option_type = array_merge([ActionTrackType::DEFAULT], $option_type);
            return (new ActionTrackTypeItem($option_type, $this->option_param->action))
                ->setDefault($this->option_param->action === 'add' ? $default : ActionTrackType::DEFAULT) // 防止前端没刷新编辑广告位回显错误
                ->make();
        }
        return [];
    }
}
