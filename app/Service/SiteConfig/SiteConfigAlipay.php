<?php

namespace App\Service\SiteConfig;

use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\ConvertSourceTypeItem;

class SiteConfigAlipay extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $option_type = [
            ConvertSourceTypeItem::OPTION_API,
            ConvertSourceTypeItem::OPTION_H5_API
        ];

        $this->setOptions([
            (new ConvertSourceTypeItem($this->option_param->media_type, $this->option_param->game_type, $this->option_param->action, $option_type))
                ->setDisabled($this->option_param->action)
                ->setDefault("")
                ->make(),
            $this->genAkey()
        ]);
    }
}
