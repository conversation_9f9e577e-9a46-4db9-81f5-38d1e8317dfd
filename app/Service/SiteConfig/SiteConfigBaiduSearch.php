<?php

namespace App\Service\SiteConfig;


use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\AppAndroidChannelPackageIdItem;

class SiteConfigBaiduSearch extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genAkey()
        ]);

        // 百度新建渠道包 app_android_channel_package_id
        if ($this->option_param->mask !== 'compose' && $this->option_param->game_type === '安卓') {
            $this->addOption((new AppAndroidChannelPackageIdItem())->make());
        }
    }
}
