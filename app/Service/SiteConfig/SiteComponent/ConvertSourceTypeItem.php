<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ConvertSourceType;
use App\Constant\MediaType;
use App\Constant\PlatId;

class ConvertSourceTypeItem extends AbstractSiteComponent
{
    const OPTION_API = 'api';
    const OPTION_SDK = 'sdk';
    const OPTION_H5_API = 'h5';

    private $media_type;
    private $game_type;
    private $action;
    private $disabled;
    private $option_type;
    private $default;

    public function __construct($media_type, $game_type, $action, $option_type = [])
    {
        $this->media_type = $media_type;
        $this->game_type = $game_type;
        $this->action = $action;
        $this->option_type = $option_type;
    }

    public function getLabel(): string
    {
        return '转化来源';
    }

    public function getProps(): string
    {
        return 'convert_source_type';
    }

    public function getType(): string
    {
        return self::TYPE_RADIO;
    }

    public function getOptions(): array
    {
        $all_options = [
            self::OPTION_API => [
                'label' => ConvertSourceType::MAP[ConvertSourceType::API],
                'value' => ConvertSourceType::API,
            ],
            self::OPTION_SDK => [
                'label' => ConvertSourceType::MAP[ConvertSourceType::SDK],
                'value' => ConvertSourceType::SDK,
            ],
            self::OPTION_H5_API => [
                'label' => ConvertSourceType::MAP[ConvertSourceType::H5_API],
                'value' => ConvertSourceType::H5_API,
                'has' => [
                    'plat_id' => [(string)PlatId::MINI, (string)PlatId::DY_MINI, (string)PlatId::DY_MINI_PROGRAM],
                ],
            ]
        ];

        $options = [];
        foreach ($this->option_type as $type) {
            $options[] = $all_options[$type];
        }

        return $options;
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        if ($this->default) {
            return $this->default;
        }
        if (in_array($this->action, ['add', 'compose']) && in_array($this->media_type, [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR,
                MediaType::TENCENT, MediaType::KUAISHOU, MediaType::BAIDU, MediaType::CHENZHONG, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR,
                MediaType::KUAISHOU_STAR, MediaType::DOUYIN_ENTERPRISE])
        ) {
            if ($this->game_type === '安卓') {
                return ConvertSourceType::SDK;
            } else {
                return ConvertSourceType::API;
            }
        }
        return ConvertSourceType::API;
    }

    public function setDefault($value): ConvertSourceTypeItem
    {
        $this->default = $value;
        return $this;
    }

    public function getTips(): string
    {
        return "游戏：小游戏 开启落地页API(H5)";
    }

    public function getDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled($action): ConvertSourceTypeItem
    {
        $this->disabled = $action === 'edit';
        return $this;
    }

    public function getResetFields(): array
    {
        return ['convert_type', 'deep_external_action' ,'convert_data_type', 'akey', 'ext.external_url', 'convert_toolkit'];
    }
}
