<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Constant\PlatId;

class ConvertTypeItem extends AbstractSiteComponent
{
    private $media_type;
    private $plat_id;
    private $platform;
    private $disabled;

    public function __construct($media_type, $plat_id, $platform)
    {
        $this->media_type = $media_type;
        $this->plat_id = $plat_id;
        $this->platform = $platform;
    }

    public function getLabel(): string
    {
        return '转化类型';
    }

    public function getProps(): string
    {
        return 'convert_type';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $options = [
            MediaType::TOUTIAO => [
                [
                    'label' => '激活',
                    'value' => '1',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI, (string)PlatId::DY_MINI, (string)PlatId::DY_MINI_PROGRAM],
                            'convert_toolkit' => [ConvertToolkit::TOUTIAO_ASSET]
                        ],
                        $this->getToutiaoCommonHas()
                    ]
                ],
                [
                    'label' => '注册',
                    'value' => '2',
                    'has' => $this->getToutiaoCommonHas()
                ],
                [
                    'label' => '关键行为',
                    'value' => '3',
                    'has' => $this->getToutiaoCommonHas()
                ],
                [
                    'label' => '付费',
                    'value' => '4',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI, (string)PlatId::DY_MINI, (string)PlatId::DY_MINI_PROGRAM]
                        ],
                        $this->getToutiaoCommonHas()
                    ]
                ],
                [
                    'label' => 'APP内付费',
                    'value' => '5',
                    'has' => array_merge(
                        [
                            'media_type' => [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE],
                            'platform' => array_merge(PlatformAbility::INSIDE_AD_API, ['TW'])
                        ],
                        $this->getToutiaoCommonHas()
                    )
                ],
                [
                    'label' => '预约表单',
                    'value' => '6',
                    'has' => array_merge(
                        [
                            'media_type' => [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE],
                            'platform' => array_merge(PlatformAbility::INSIDE_AD_API, ['TW', 'GR', 'GRBB'])
                        ],
                        $this->getToutiaoCommonHas()
                    )
                ],
            ],
            MediaType::TENCENT => [
                ['value' => 'ACTIVATE_APP', 'label' => '激活'],
                ['value' => 'OPTIMIZATIONGOAL_APP_REGISTER', 'label' => '注册'],
                ['value' => 'OPTIMIZATIONGOAL_APP_PURCHASE', 'label' => '付费'],
                ['value' => 'PURCHASE', 'label' => '首次付费'],
                ['value' => 'OPTIMIZATIONGOAL_MOBILE_APP_CREATE_ROLE', 'label' => '小游戏创角'],
                ['value' => 'OPTIMIZATIONGOAL_MOBILE_APP_AD_INCOME', 'label' => '广告变现'],
                ['value' => 'OPTIMIZATIONGOAL_PROMOTION_VIEW_KEY_PAGE', 'label' => '关键页面访问'],
                ['value' => 'OPTIMIZATIONGOAL_CANVAS_CLICK', 'label' => '跳转按钮点击'],
                ['value' => 'OPTIMIZATIONGOAL_PAGE_RESERVATION', 'label' => '表单预约'],
                ['value' => 'OPTIMIZATIONGOAL_BACK_FLOW', 'label' => '沉默唤起'],
            ],
            MediaType::KUAISHOU => [
                ['value' => '1', 'label' => '单注册匹配'],
                ['value' => '2', 'label' => '注册充值匹配'],
            ],
            MediaType::BAIDU => [
                [
                    'value' => '4',
                    'label' => '激活',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK],
                        ]
                    ]
                ],
                [
                    'value' => '25',
                    'label' => '注册'
                ],
                [
                    'value' => '26',
                    'label' => '付费'
                ],
                [
                    'value' => '49',
                    'label' => '登录',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
            ],
            MediaType::MP => [
                ['value' => 'ACTIVATE_APP', 'label' => '激活'],
                ['value' => 'PURCHASE', 'label' => '首次付费'],
            ],
            MediaType::UC => [
                ['value' => '1', 'label' => '激活'],
                ['value' => '27', 'label' => '注册'],
                ['value' => '1000', 'label' => '付费']
            ],
            MediaType::BAIDU_SEARCH => [
                ['value' => '1', 'label' => '激活']
            ],
            MediaType::CHENZHONG => [
                ['value' => '1', 'label' => '单注册匹配'],
                ['value' => '2', 'label' => '注册充值匹配'],
            ],
            MediaType::KUAISHOU_PINPAI => [
                ['value' => '1', 'label' => '单注册匹配'],
                ['value' => '2', 'label' => '注册充值匹配'],
            ],
            MediaType::KUAISHOU_STAR => [
                ['value' => '1', 'label' => '单注册匹配'],
                ['value' => '2', 'label' => '注册充值匹配'],
            ],
            MediaType::KUAISHOU_JUXING => [
                ['value' => '1', 'label' => '单注册匹配'],
                ['value' => '2', 'label' => '注册充值匹配'],
            ],
            MediaType::BILIBILI => [
                [
                    'value' => '2',
                    'label' => '应用激活',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK],
                        ]
                    ]
                ],
                [
                    'value' => '7',
                    'label' => '用户注册',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK],
                        ]
                    ]
                ],
                [
                    'value' => '11',
                    'label' => '应用内付费(首次)',
                ],
                [
                    'value' => '32',
                    'label' => '24小时付费ROI',
                ],
                [
                    'value' => '33',
                    'label' => '每次付费',
                ],
                [
                    'value' => '4',
                    'label' => '表单提交',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '5',
                    'label' => '订单提交',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '9',
                    'label' => '稿件播放',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '13',
                    'label' => '有效线索',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '18',
                    'label' => '表单付费',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '30',
                    'label' => '评论链接点击',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '34',
                    'label' => '微信复制',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '35',
                    'label' => '完件',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '38',
                    'label' => '关键行为',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '50',
                    'label' => '组件点击',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
                [
                    'value' => '54',
                    'label' => '24小时变现ROI',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                        ]
                    ]
                ],
            ]
        ];

        if (in_array($this->media_type, [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE])) {
            $this->media_type = MediaType::TOUTIAO;

            if (!in_array($this->platform, array_merge(PlatformAbility::INSIDE_AD_API, ['TW']))) {
                $options[$this->media_type] = $this->optionsFilter($options[$this->media_type], ['5'], false);
            }
        }

        if (in_array($this->media_type, [MediaType::TENCENT, MediaType::CHANNELS_LIVE])) {
            $this->media_type = MediaType::TENCENT;

            // 这个特殊 因plat_id众多 不宜写在has里面
            if ($this->plat_id == PlatId::MINI) {
                $options[$this->media_type] = $this->optionsFilter($options[$this->media_type], [
                    'OPTIMIZATIONGOAL_APP_REGISTER',
                    'OPTIMIZATIONGOAL_APP_PURCHASE',
                    'OPTIMIZATIONGOAL_MOBILE_APP_CREATE_ROLE',
                    'OPTIMIZATIONGOAL_MOBILE_APP_AD_INCOME',
                    'PURCHASE',
                    'OPTIMIZATIONGOAL_PROMOTION_VIEW_KEY_PAGE',
                    'OPTIMIZATIONGOAL_BACK_FLOW'
                ]);
            } else {
                $options[$this->media_type] = $this->optionsFilter($options[$this->media_type], [
                    'ACTIVATE_APP',
                    'OPTIMIZATIONGOAL_APP_REGISTER',
                    'OPTIMIZATIONGOAL_APP_PURCHASE',
                    'PURCHASE',
                    'OPTIMIZATIONGOAL_CANVAS_CLICK',
                    'OPTIMIZATIONGOAL_PAGE_RESERVATION',
                ]);
            }
        }

        return $options[$this->media_type];
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled($action): ConvertTypeItem
    {
        $this->disabled = $action === 'edit';
        return $this;
    }

    public function getResetFields(): array
    {
        return ['convert_data_type', 'deep_external_action', 'ext.conversion_template_id', 'ext.adgroup_type'];
    }

    private function getToutiaoCommonHas()
    {
        return [
            'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK],
        ];
    }
}
