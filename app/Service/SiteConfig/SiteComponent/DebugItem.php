<?php

namespace App\Service\SiteConfig\SiteComponent;


class DebugItem extends AbstractSiteComponent
{
    public function __construct()
    {

    }

    public function getLabel(): string
    {
        return '是否联调';
    }

    public function getProps(): string
    {
        return 'ext.debug';
    }

    public function getType(): string
    {
        return self::TYPE_RADIO;
    }

    public function getOptions(): array
    {
        $options = [
            [
                'label' => '否',
                'value' => 0,
            ],
            [
                'label' => '是',
                'value' => 1,
            ],
        ];

        return $options;
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): int
    {
        return 0;
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }
}
