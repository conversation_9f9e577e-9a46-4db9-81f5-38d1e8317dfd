<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\MediaType;

class AkeyItem extends AbstractSiteComponent
{
    private $media_type;
    private $condition = [];

    public function __construct($media_type)
    {
        $this->media_type = $media_type;
    }

    public function getLabel(): string
    {
        return 'akey|token';
    }

    public function getProps(): string
    {
        return 'akey';
    }

    public function getType(): string
    {
        return self::TYPE_INPUT;
    }

    public function getOptions(): array
    {
        return [];
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        if (in_array($this->media_type, [MediaType::DOUYIN_STAR, MediaType::TOUTIAO_APP])) {
            return '826d7fde89ae720afe7036713c6edbc9cb951480eec0b42380e16ff67bd80a77';
        } else if (in_array($this->media_type, [MediaType::UC])) {
            return '0f5e9da71b3214238a84fbbbf49578a7';
        } else {
            return '';
        }
    }

    public function getTips(): string
    {
        return "百度akey\n百度搜索token\n百度 应用下载API 开启";
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }

    public function setCondition($condition): AkeyItem
    {
        $this->condition = $condition;
        return $this;
    }

    public function getCondition(): array
    {
        return $this->condition;
    }
}
