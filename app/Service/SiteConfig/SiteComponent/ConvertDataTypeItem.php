<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\MediaType;

class ConvertDataTypeItem extends AbstractSiteComponent
{
    private $disabled;
    private $media_type;

    public function __construct($media_type)
    {
        $this->media_type = $media_type;
    }

    public function getLabel(): string
    {
        return '转化统计方式';
    }

    public function getProps(): string
    {
        return 'convert_data_type';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $options = [
            MediaType::TOUTIAO => [
                [
                    'label' => '每一次',
                    'value' => 'EVERY_ONE',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::API],
                            'convert_type' => ['3', '4', '5']
                        ],
                        [
                            'convert_source_type' => [ConvertSourceType::SDK],
                            'convert_type' => ['4', '5']
                        ],
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'convert_type' => ['4'],
                            'convert_toolkit' => [ConvertToolkit::TOUTIAO_ASSET]
                        ]
                    ]
                ],
                [
                    'label' => '仅一次',
                    'value' => 'ONLY_ONE',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::API],
                            'convert_type' => ['3', '4', '5']
                        ],
                        [
                            'convert_source_type' => [ConvertSourceType::SDK],
                            'convert_type' => ['4', '5']
                        ],
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'convert_type' => ['4'],
                            'convert_toolkit' => [ConvertToolkit::TOUTIAO_ASSET]
                        ]
                    ]
                ]
            ],
            MediaType::UC => [
                [
                    'label' => '不回传真实付费数据',
                    'value' => '0',
                    'multi_has' => [
                        [
                            'deep_external_action' => ['1000']
                        ],
                        [
                            'convert_type' => ['1', '27', '1000']
                        ]
                    ],
                ],
                [
                    'label' => '回传真实付费数据',
                    'value' => '1',
                    'multi_has' => [
                        [
                            'deep_external_action' => ['1000']
                        ],
                        [
                            'convert_type' => ['1', '27', '1000']
                        ]
                    ],
                ]
            ],
        ];

        if (in_array($this->media_type, [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE])) {
            $this->media_type = MediaType::TOUTIAO;
        }

        return $options[$this->media_type];
    }

    public function getRequired(): bool
    {
        return false;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getTips(): string
    {
        if (in_array($this->media_type, [MediaType::UC])) {
            return "转化类型: 激活\n深度转化类型: 付费\n应用下载：API|SDK";
        } else {
            return "转化类型: 付费\n应用下载：API|SDK";
        }
    }

    public function getDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled($action): ConvertDataTypeItem
    {
        $this->disabled = $action === 'edit';
        return $this;
    }

    public function getResetFields(): array
    {
        return [];
    }
}
