<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ConvertSourceType;
use App\Constant\MediaType;

class ConvertToolkitItem extends AbstractSiteComponent
{
    private $action;

    public function __construct($action)
    {
        $this->action = $action;
    }

    public function getLabel(): string
    {
        return '转化工具类型';
    }

    public function getProps(): string
    {
        return 'convert_toolkit';
    }

    public function getType(): string
    {
       return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        return [
            [
                'label' => '头条事件管理',
                'value' => 'TOUTIAO_ASSET',
                'has' => [
                    'media_type' => [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE]
                ],
            ],
        ];
    }

    public function getRequired(): bool
    {
        return !($this->action === 'edit');
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getTips(): string
    {
        return "";
    }

    public function getDisabled(): bool
    {
        return $this->action === 'edit';
    }

    public function getResetFields(): array
    {
        return ['convert_type'];
    }
}
