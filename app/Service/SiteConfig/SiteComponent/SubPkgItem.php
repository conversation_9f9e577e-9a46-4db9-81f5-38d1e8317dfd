<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Model\SqlModel\DataMedia\OdsBilibiliGameListLogModel;

class SubPkgItem extends AbstractSiteComponent
{
    private $platform;
    private $game_id;
    private $action;

    public function __construct($platform, $game_id, $action)
    {
        $this->platform = $platform;
        $this->game_id = $game_id;
        $this->action = $action;
    }

    public function getLabel(): string
    {
        return '游戏分包类型';
    }

    public function getProps(): string
    {
        return 'ext.sub_pkg';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        // 读取表
        $list = (new OdsBilibiliGameListLogModel())->getListByPlatformGame($this->platform, $this->game_id);
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'label' => $item->game_name,
                'value' => $item->sub_pkg
            ];
        }

        return $data;
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getTips(): string
    {
        return '';
    }

    public function getDisabled(): bool
    {
        return $this->action === 'edit';
    }

    public function getResetFields(): array
    {
        return [];
    }
}
