<?php

namespace App\Service\SiteConfig\SiteComponent;

class InputItem extends AbstractSiteComponent
{
    private $label = '';
    private $props = '';
    private $required = false;
    private $tips = '';
    private $options = [];
    private $validator = [];
    private $default = '';
    private $condition = [];

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getProps(): string
    {
        return $this->props;
    }

    public function getType(): string
    {
        return self::TYPE_INPUT;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getRequired(): bool
    {
        return $this->required;
    }

    public function getDefault(): string
    {
        return $this->default;
    }

    public function getTips(): string
    {
        return $this->tips;
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getValidator(): array
    {
        return $this->validator;
    }

    public function getCondition(): array
    {
        return $this->condition;
    }

    /**
     * @param string $label
     * @return InputItem
     */
    public function setLabel(string $label): InputItem
    {
        $this->label = $label;
        return $this;
    }

    /**
     * @param string $props
     * @return InputItem
     */
    public function setProps(string $props): InputItem
    {
        $this->props = $props;
        return $this;
    }

    /**
     * @param array $options
     * @return InputItem
     */
    public function setOptions(array $options): InputItem
    {
        $this->options = $options;
        return $this;
    }

    /**
     * @param bool $required
     * @return InputItem
     */
    public function setRequired(bool $required): InputItem
    {
        $this->required = $required;
        return $this;
    }

    /**
     * @param string $tips
     * @return InputItem
     */
    public function setTips(string $tips): InputItem
    {
        $this->tips = $tips;
        return $this;
    }

    public function setValidator($rule, $error)
    {
        $this->validator = [
            'rule' => $rule,
            'error' => $error
        ];
        return $this;
    }

    public function setDefault($data)
    {
        $this->default = $data;
        return $this;
    }

    public function setCondition($condition): InputItem
    {
        $this->condition = $condition;
        return $this;
    }

    public function getResetFields()
    {
        return [];
    }
}
