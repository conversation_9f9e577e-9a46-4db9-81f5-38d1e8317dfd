<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ConvertSourceType;
use App\Constant\PlatId;
use App\Constant\TencentEum;

class AdgroupTypeItem extends AbstractSiteComponent
{
    private $plat_id;
    private $disabled;

    public function __construct($plat_id)
    {
        $this->plat_id = $plat_id;
    }

    public function getLabel(): string
    {
        return '广告组类型';
    }

    public function getProps(): string
    {
        return 'ext.adgroup_type';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $options = [];
        if ($this->plat_id == PlatId::MINI) {
            $options = [
                [
                    'label' => '场景化投放',
                    'value' => TencentEum::SMART_DELIVERY_PLATFORM_EDITION_SCENE,
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                            'convert_type' => ["OPTIMIZATIONGOAL_APP_REGISTER"],
                            'deep_external_action' => ["GOAL_1DAY_PURCHASE_ROAS"],
                        ]
                    ]
                ],
            ];
        }

        return $options;
    }

    public function getRequired(): bool
    {
        return false;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled($action): AdgroupTypeItem
    {
        $this->disabled = $action === 'edit';
        return $this;
    }

    public function getResetFields(): array
    {
        return ['convert_data_type'];
    }
}
