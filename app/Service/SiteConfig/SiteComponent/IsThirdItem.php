<?php

namespace App\Service\SiteConfig\SiteComponent;


class IsThirdItem extends AbstractSiteComponent
{
    private $platform;

    public function __construct($platform)
    {
        $this->platform = $platform;
    }

    public function getLabel(): string
    {
        return '点击监测域名';
    }

    public function getProps(): string
    {
        return 'is_third';
    }

    public function getType(): string
    {
        return self::TYPE_RADIO;
    }

    public function getOptions(): array
    {
        $options = [
            'TW' => [
                [
                    'label' => '贪玩',
                    'value' => 0,
                ],
                [
                    'label' => '其他',
                    'value' => 1,
                ],
            ]
        ];

        return $options[$this->platform];
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): int
    {
        if ($this->platform === 'TW') {
            return 0;
        } else {
            return 1;
        }
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }
}
