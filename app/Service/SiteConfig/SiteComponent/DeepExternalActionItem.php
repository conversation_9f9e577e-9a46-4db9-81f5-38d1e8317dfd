<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ConvertSourceType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use Common\EnvConfig;

class DeepExternalActionItem extends AbstractSiteComponent
{
    private $media_type;
    private $platform;
    private $disabled;

    public function __construct($media_type, $platform)
    {
        $this->media_type = $media_type;
        $this->platform = $platform;
    }

    public function getLabel(): string
    {
        return '深度转化类型';
    }

    public function getProps(): string
    {
        return 'deep_external_action';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $options = [
            MediaType::TOUTIAO => [
                [
                    'label' => '注册',
                    'value' => 'AD_CONVERT_TYPE_ACTIVE_REGISTER',
                    'has' => [
                        'convert_type' => ['1']
                    ]
                ],
                [
                    'label' => '付费',
                    'value' => 'AD_CONVERT_TYPE_PAY',
                    'has' => [
                        'convert_type' => ['1']
                    ]
                ],
                [
                    'label' => '次留',
                    'value' => 'AD_CONVERT_TYPE_NEXT_DAY_OPEN',
                    'has' => [
                        'convert_type' => ['1']
                    ]
                ],
                [
                    'label' => '付费ROI',
                    'value' => 'AD_CONVERT_TYPE_PURCHASE_ROI',
                    'multi_has' => [
                        [
                            'convert_type' => ['1']
                        ],
                        [
                            'convert_type' => ['4'],
                            'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK, ConvertSourceType::H5_API]
                        ],
                    ]
                ],
                [
                    'label' => '7日付费ROI',
                    'value' => 'AD_CONVERT_TYPE_PURCHASE_ROI_7D',
                    'has' => [
                        'convert_type' => ['4'],
                        'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK, ConvertSourceType::H5_API],
                        'platform' => ['TW', 'ZW', 'GR', 'GRBB']
                    ]
                ],
            ],
            MediaType::TENCENT => [
                [
                    'label' => '付费',
                    'value' => 'OPTIMIZATIONGOAL_APP_PURCHASE',
                    'has' => [
                        'convert_type' => ['ACTIVATE_APP', 'OPTIMIZATIONGOAL_APP_REGISTER']
                    ]
                ],
                [
                    'label' => '首次付费',
                    'value' => 'OPTIMIZATIONGOAL_FIRST_PURCHASE',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER', 'OPTIMIZATIONGOAL_MOBILE_APP_CREATE_ROLE'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                        [
                            'convert_type' => ['ACTIVATE_APP', 'OPTIMIZATIONGOAL_APP_REGISTER']
                        ]
                    ]
                ],
                [
                    'label' => '首日付费ROI',
                    'value' => 'GOAL_1DAY_PURCHASE_ROAS',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER', 'PURCHASE', 'OPTIMIZATIONGOAL_BACK_FLOW'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                        [
                            'convert_type' => ['ACTIVATE_APP', 'OPTIMIZATIONGOAL_APP_REGISTER', 'PURCHASE', 'OPTIMIZATIONGOAL_APP_PURCHASE'],
                            'plat_id' => [(string)PlatId::YY, (string)PlatId::SY, (string)PlatId::H5, (string)PlatId::PC]
                        ]
                    ]
                ],
                [
                    'label' => '次日留存',
                    'value' => 'OPTIMIZATIONGOAL_ONE_DAY_RETENTION',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                    ]
                ],
                [
                    'label' => '首日变现ROI',
                    'value' => 'GOAL_1DAY_MONETIZATION_ROAS',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER', 'OPTIMIZATIONGOAL_MOBILE_APP_AD_INCOME'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                    ]
                ],
                [
                    'label' => '强化-首日付费ROI',
                    'value' => 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER', 'PURCHASE'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                        [
                            'convert_type' => ['ACTIVATE_APP', 'OPTIMIZATIONGOAL_APP_REGISTER', 'PURCHASE', 'OPTIMIZATIONGOAL_APP_PURCHASE'],
                            'plat_id' => [(string)PlatId::YY, (string)PlatId::SY, (string)PlatId::H5, (string)PlatId::PC]
                        ],
                    ]
                ],
                [
                    'label' => '7日付费ROI',
                    'value' => 'GOAL_7DAY_PURCHASE_ROAS',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                    ]
                ],
                [
                    'label' => '7日长效ROI',
                    'value' => 'GOAL_7DAY_LONGTERM_PURCHASE_ROAS',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                    ]
                ],
                [
                    'label' => '首日混合变现ROI',
                    'value' => 'GOAL_1DAY_PURCHASE_MONETIZATION_ROAS',
                    'multi_has' => [
                        [
                            'convert_type' => ['OPTIMIZATIONGOAL_APP_REGISTER'],
                            'plat_id' => [(string)PlatId::MINI]
                        ],
                    ]
                ],
            ],
            MediaType::BAIDU => [
                ['value' => '25', 'label' => '注册', 'has' => ['convert_type' => '4']],
                ['value' => '26', 'label' => '付费', 'has' => ['convert_type' => ['4', '25', '49']]],
                ['value' => '27', 'label' => '客户自定义(小流量)', 'has' => ['convert_type' => '4']],
                ['value' => '28', 'label' => '次日留存(小流量)', 'has' => ['convert_type' => '4']],
                ['value' => '29', 'label' => '商品下单成功(小流量)', 'has' => ['convert_type' => '4']],
            ],
            MediaType::UC => [
                [
                    'value' => '1000',
                    'label' => '付费',
                    'has' => [
                        'convert_type' => ['1', '27']
                    ]
                ],
            ],
            MediaType::BILIBILI => [
                [
                    'label' => '应用内付费(首次)',
                    'value' => '11',
                    'has' => [
                        'convert_type' => ['2', '4'],
                    ]
                ],
                [
                    'label' => '次日留存',
                    'value' => '16',
                    'has' => [
                        'convert_type' => ['2'],
                        'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::SDK],
                    ]
                ],
                [
                    'label' => '24小时付费ROI',
                    'value' => '32',
                    'has' => [
                        'convert_type' => ['2', '4'],
                    ]
                ],
                [
                    'label' => '每次付费',
                    'value' => '33',
                    'has' => [
                        'convert_type' => ['2', '4'],
                    ]
                ],
                [
                    'label' => '有效线索',
                    'value' => '13',
                    'has' => [
                        'convert_type' => ['4'],
                        'convert_source_type' => [ConvertSourceType::H5_API],
                    ]
                ],
                [
                    'label' => '表单付费',
                    'value' => '18',
                    'has' => [
                        'convert_type' => ['4'],
                        'convert_source_type' => [ConvertSourceType::H5_API],
                    ]
                ],
                [
                    'label' => '授信',
                    'value' => '36',
                    'has' => [
                        'convert_type' => ['35'],
                        'convert_source_type' => [ConvertSourceType::H5_API],
                    ]
                ],
            ]
        ];

        if (in_array($this->media_type, [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE])) {
            $this->media_type = MediaType::TOUTIAO;

            if (!in_array($this->platform, ['TW', 'ZW', 'GR', 'GRBB'])) {
                $options[$this->media_type] = $this->optionsFilter($options[$this->media_type], ['AD_CONVERT_TYPE_PURCHASE_ROI_7D'], false);
            }
        }

        if (in_array($this->media_type, [MediaType::BAIDU_SEARCH])) {
            $this->media_type = MediaType::BAIDU;
        }

        if (in_array($this->media_type, [MediaType::CHANNELS_LIVE])) {
            $this->media_type = MediaType::TENCENT;
        }

        return $options[$this->media_type];
    }

    public function getRequired(): bool
    {
        return false;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled($action): DeepExternalActionItem
    {
        $this->disabled = $action === 'edit';
        return $this;
    }

    public function getResetFields(): array
    {
        return ['convert_data_type', 'ext.conversion_template_id', 'ext.adgroup_type'];
    }
}
