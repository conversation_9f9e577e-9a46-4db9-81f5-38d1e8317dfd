<?php

namespace App\Service\SiteConfig\SiteComponent;

abstract class AbstractSiteComponent
{
    const TYPE_INPUT = 'input';
    const TYPE_SELECT = 'select';
    const TYPE_RADIO = 'radio';
    const TYPE_PLAIN = 'plain';
    const TYPE_CHECKBOX = 'checkbox';
    const TYPE_TEXTAREA = 'textarea';

    /**
     * form label
     * @return string
     */
    abstract function getLabel(): string;

    /**
     * 传给后端的key
     * @return string
     */
    abstract function getProps(): string;

    /**
     * 组件的类型one of TYPE CONSTANT
     * @return string
     */
    abstract public function getType(): string;

    /**
     * 下拉选项
     * 筛选配置：has：完全符合条件才可选 multi_has：符合其中一个条件则可选 具体看已有例子
     * @return array
     */
    abstract public function getOptions(): array;

    abstract public function getRequired(): bool;

    abstract public function getDefault();

    abstract public function getDisabled(): bool;

    protected function getTips(): string
    {
        return '';
    }

    protected function getValidator(): array
    {
        return [];
    }

    /**
     * 该选项变更后，需要重置哪些字段
     * @return mixed
     */
    abstract public function getResetFields();

    /**
     * 返回是否为选项组件，是则前端会渲染组件，否则前端只使用options 和 default 字段
     * @return bool
     */
    protected function getComponent(): bool
    {
        return true;
    }

    /**
     * 返回该组件可选可填的条件 具体可看百度的akey生成
     * @return array
     */
    protected function getCondition(): array
    {
        return [];
    }

    /**
     * 筛选选项值
     * @param      $options
     * @param      $filters
     * @param bool $in true 则筛选出 filters 值，false 反之
     * @return array
     */
    public function optionsFilter($options, $filters, $in = true): array
    {
        return array_values(array_filter($options, function ($item) use ($filters, $in) {
            return $in ? in_array($item['value'], $filters) : !in_array($item['value'], $filters);
        }));
    }

    public function make(): array
    {
        $result =  [
            'label' => $this->getLabel(),
            'props' => $this->getProps(),
            'type' => $this->getType(),
            'options' => $this->getOptions(),
            'required' => $this->getRequired(),
            'default' => $this->getDefault(),
            'tips' => $this->getTips(),
            'disabled' => $this->getDisabled(),
            'reset' => $this->getResetFields(),
            'component' => $this->getComponent(),
            'condition' => (object)$this->getCondition()
        ];
        $this->getType() === self::TYPE_INPUT && $result['validator'] = $this->getValidator();
        return $result;
    }
}
