<?php

namespace App\Service\SiteConfig\SiteComponent;


use App\Constant\ActionTrackType;

class ActionTrackTypeItem extends AbstractSiteComponent
{
    private $option_type;
    private $action;
    private $default;

    public function __construct($option_type, $action)
    {
        $this->option_type = $option_type;
        $this->action = $action;
    }

    public function getLabel(): string
    {
        return '点击监测类型';
    }

    public function getProps(): string
    {
        return 'action_track_type';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $all_options = [
            ActionTrackType::DEFAULT => [
                'label' => ActionTrackType::MAP[ActionTrackType::DEFAULT],
                'value' => ActionTrackType::DEFAULT,
            ],
            ActionTrackType::TOUTIAO_V2 => [
                'label' => ActionTrackType::MAP[ActionTrackType::TOUTIAO_V2],
                'value' => ActionTrackType::TOUTIAO_V2,
            ],
            ActionTrackType::TENCENT_ADA => [
                'label' => ActionTrackType::MAP[ActionTrackType::TENCENT_ADA],
                'value' => ActionTrackType::TENCENT_ADA,
            ],
            ActionTrackType::TENCENT_V3 => [
                'label' => ActionTrackType::MAP[ActionTrackType::TENCENT_V3],
                'value' => ActionTrackType::TENCENT_V3,
            ],
            ActionTrackType::UC_V2 => [
                'label' => ActionTrackType::MAP[ActionTrackType::UC_V2],
                'value' => ActionTrackType::UC_V2,
            ],
        ];

        $options = [];
        foreach ($this->option_type as $type) {
            $options[] = $all_options[$type];
        }

        return $options;
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): int
    {
        return $this->default;
    }

    public function getDisabled(): bool
    {
        return $this->action === 'edit';
    }

    public function getResetFields(): array
    {
        return ['ext.conversion_template_id'];
    }

    public function setDefault($value): ActionTrackTypeItem
    {
        $this->default = $value;
        return $this;
    }
}
