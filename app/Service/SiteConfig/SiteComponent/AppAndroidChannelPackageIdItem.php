<?php

namespace App\Service\SiteConfig\SiteComponent;


class AppAndroidChannelPackageIdItem extends AbstractSiteComponent
{
    public function __construct()
    {

    }

    public function getLabel(): string
    {
        return '新建渠道包';
    }

    public function getProps(): string
    {
        return 'app_android_channel_package_id';
    }

    public function getType(): string
    {
        return self::TYPE_RADIO;
    }

    public function getOptions(): array
    {
        $options = [
            [
                'label' => '否',
                'value' => 'NO_NEED',
            ],
            [
                'label' => '是',
                'value' => 'WAITING',
            ],
        ];

        return $options;
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        return 'NO_NEED';
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }
}
