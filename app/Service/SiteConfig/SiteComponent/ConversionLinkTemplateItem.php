<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Constant\ActionTrackType;
use App\Constant\ConvertSourceType;
use App\Constant\PlatId;

class ConversionLinkTemplateItem extends AbstractSiteComponent
{
    private $plat_id;
    private $game_type;
    private $disabled;

    public function __construct($plat_id, $game_type)
    {
        $this->plat_id = $plat_id;
        $this->game_type = $game_type;
    }

    public function getLabel(): string
    {
        return '营销链路模板';
    }

    public function getProps(): string
    {
        return 'ext.conversion_template_id';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $options = [];
        if ($this->plat_id == PlatId::MINI) {
            $options = [
                [
                    'label' => '微信小游戏:启动-注册-创建角色-完成新手指引-广告变现-付费',
                    'value' => '592',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                            'convert_type' => ["OPTIMIZATIONGOAL_APP_REGISTER"],
                            'deep_external_action' => ["GOAL_1DAY_PURCHASE_ROAS", "GOAL_7DAY_PURCHASE_ROAS", "GOAL_1DAY_PURCHASE_MONETIZATION_ROAS"],
                        ],
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                            'convert_type' => ["PURCHASE"],
                            'deep_external_action' => [""],
                        ]
                    ]
                ],
                [
                    'label' => '微信小游戏:启动-注册-创建角色-完成新手指引-付费',
                    'value' => '606',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                            'convert_type' => ["OPTIMIZATIONGOAL_APP_REGISTER"],
                            'deep_external_action' => ["GOAL_1DAY_PURCHASE_ROAS", "GOAL_7DAY_PURCHASE_ROAS"],
                        ],
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                            'convert_type' => ["PURCHASE"],
                            'deep_external_action' => [""],
                        ]
                    ]
                ],
                [
                    'label' => '微信小游戏:沉默唤起-付费',
                    'value' => '593',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::H5_API],
                            'plat_id' => [(string)PlatId::MINI],
                            'convert_type' => ["OPTIMIZATIONGOAL_BACK_FLOW"],
                            'deep_external_action' => ["GOAL_1DAY_PURCHASE_ROAS"],
                        ]
                    ]
                ],
            ];
        } elseif ($this->game_type == "IOS") {
            $options = [
                [
                    'label' => '【苹果AppStore:下载应用-安装应用】-【IOS_App:激活应用-注册-创建角色-完成新手指引-付费】',
                    'value' => '605',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::API],
                            'convert_type' => ["ACTIVATE_APP"],
                            'deep_external_action' => ["GOAL_1DAY_PURCHASE_ROAS"],
                        ]
                    ]
                ],
            ];
        } elseif ($this->game_type == "安卓") {
            $options = [
                [
                    'label' => '安卓_App:激活应用-注册-创建角色-完成新手指引-付费',
                    'value' => '604',
                    'multi_has' => [
                        [
                            'convert_source_type' => [ConvertSourceType::SDK],
                            'convert_type' => ["ACTIVATE_APP"],
                            'deep_external_action' => ["GOAL_1DAY_PURCHASE_ROAS"],
                        ]
                    ]
                ],
            ];
        }

        return $options;
    }

    public function getRequired(): bool
    {
        return false;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled($action): ConversionLinkTemplateItem
    {
        $this->disabled = $action === 'edit';
        return $this;
    }

    public function getResetFields(): array
    {
        return ['convert_data_type'];
    }
}
