<?php

namespace App\Service\SiteConfig\SiteComponent;

use App\Container;
use App\Service\PlatformAD\PlatformAD;

class TemplateTypeItem extends AbstractSiteComponent
{
    private $platform;

    public function __construct($platform)
    {
        $this->platform = $platform;
    }

    public function getLabel(): string
    {
        return '落地页模板';
    }

    public function getProps(): string
    {
        return 'template_type';
    }

    public function getType(): string
    {
        return self::TYPE_SELECT;
    }

    public function getOptions(): array
    {
        $ads_platform = PlatformAD::create($this->platform);
        return $ads_platform->getTemplateList(1, 9999, Container::getSession()->name);
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        if (in_array($this->platform, ['ZW'])) {
            return '';
        } else {
            return 'nojump.html';
        }
    }

    public function getTips(): string
    {
        return "";
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }

    public function getComponent(): bool
    {
        return false;
    }
}
