<?php

namespace App\Service\SiteConfig\SiteComponent;


class UptStateItem extends AbstractSiteComponent
{
    private $default;

    public function getLabel(): string
    {
        return '上报状态';
    }

    public function getProps(): string
    {
        return 'upt_state';
    }

    public function getType(): string
    {
        return self::TYPE_RADIO;
    }

    public function getOptions(): array
    {
        return [];
    }

    public function getRequired(): bool
    {
        return false;
    }

    public function getDefault(): int
    {
        return $this->default;
    }

    public function getTips(): string
    {
        return "";
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }

    public function getComponent(): bool
    {
        return false;
    }

    public function setDefault($value): UptStateItem
    {
        $this->default = $value;
        return $this;
    }
}
