<?php

namespace App\Service\SiteConfig\SiteComponent;


class TemplateAddressItem extends AbstractSiteComponent
{
    public function __construct()
    {

    }

    public function getLabel(): string
    {
        return '临时模板地址';
    }

    public function getProps(): string
    {
        return 'template_address';
    }

    public function getType(): string
    {
        return self::TYPE_TEXTAREA;
    }

    public function getOptions(): array
    {
        return [];
    }

    public function getRequired(): bool
    {
        return true;
    }

    public function getDefault(): string
    {
        return '';
    }

    public function getTips(): string
    {
        return "";
    }

    public function getDisabled(): bool
    {
        return false;
    }

    public function getResetFields(): array
    {
        return [];
    }

    public function getComponent(): bool
    {
        return false;
    }
}
