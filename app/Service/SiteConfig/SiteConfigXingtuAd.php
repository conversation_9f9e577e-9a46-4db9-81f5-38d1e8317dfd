<?php

namespace App\Service\SiteConfig;

use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\InputItem;

class SiteConfigXingtuAd extends AbstractSiteConfigToutiaoBase
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            (new InputItem())
                ->setLabel('资产账户ID')
                ->setProps('ext.convert_account_id')
                ->setRequired(true)
                ->setDefault('')
                ->make(),
        ]);
    }
}
