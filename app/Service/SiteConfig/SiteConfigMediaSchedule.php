<?php

namespace App\Service\SiteConfig;


use App\Constant\ActionTrackType;
use App\Constant\PlatId;
use App\Param\SiteOptionParam;

class SiteConfigMediaSchedule extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genActionTrackType([ActionTrackType::TENCENT_ADA, ActionTrackType::TENCENT_V3])
        ]);
    }

    protected function genGamePack(): array
    {
        return $this->genToutiaoGamePack();
    }
}
