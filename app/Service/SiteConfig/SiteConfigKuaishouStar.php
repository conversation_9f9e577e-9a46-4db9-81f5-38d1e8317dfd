<?php

namespace App\Service\SiteConfig;


use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\InputItem;
use App\Service\SiteConfig\SiteComponent\SkipChannelConvertItem;

class SiteConfigKuaishouStar extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genConvertType()
        ]);

        if ($this->option_param->action !== 'edit') {
           $this->addOption(
               (new InputItem())
                   ->setLabel('小铃铛下载链接')
                   ->setProps('ext.jingle_bell_download_url')
                   ->setRequired(false)
                   ->setValidator('^(#|(http(s?)):\/\/|\/\/)[^\s]+\\.[^\s]+$', '请输入以http://或https://开头的小铃铛下载链接')
                   ->setCondition(['game_pack' => [0, 1]])
                   ->make()
           );
        }

        // 是否跳过创建渠道包与转化步骤
        if ($this->option_param->mask !== 'compose' && $this->option_param->game_type === '安卓') {
            $this->addOption((new SkipChannelConvertItem())->make());
        }
    }

    protected function genGamePack(): array
    {
        return $this->genToutiaoGamePack(2);
    }
}
