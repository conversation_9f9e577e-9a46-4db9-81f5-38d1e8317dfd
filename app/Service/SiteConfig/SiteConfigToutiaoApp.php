<?php

namespace App\Service\SiteConfig;


use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\InputItem;
use App\Service\SiteConfig\SiteComponent\UptStateItem;

class SiteConfigToutiaoApp extends AbstractSiteConfigToutiaoBase
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->genAkey(),
            (new InputItem())
                ->setLabel('转化账户id')
                ->setProps('ext.convert_account_id')
                ->setRequired(true)
                ->setDefault('***********')
                ->make(),
            (new UptStateItem())->setDefault(0)->make(),
            (new InputItem())
                ->setLabel('抖音视频id')
                ->setProps('ext.item_id')
                ->make()
        ]);
    }
}
