<?php

namespace App\Service\SiteConfig;


use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\InputItem;

class SiteConfigHuaweiOcpd extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->addOption(
            (new InputItem())
                ->setLabel('clientId')
                ->setProps('ext.client_id')
                ->setRequired(true)
                ->make()
        );

        $this->addOption(
            (new InputItem())
                ->setLabel('clientSecret')
                ->setProps('ext.client_secret')
                ->setRequired(true)
                ->make()
        );
    }
}
