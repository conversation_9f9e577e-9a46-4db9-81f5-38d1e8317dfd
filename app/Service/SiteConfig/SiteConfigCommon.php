<?php
/**
 * 通用广告位选项配置 以防无配置媒体出错
 */

namespace App\Service\SiteConfig;


use App\Constant\PlatId;
use App\Param\SiteOptionParam;

class SiteConfigCommon extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);
        // 页游没有转化来源
        if ($this->option_param->plat_id != PlatId::YY) {
            $this->setOptions([
                $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genApiConvertSourceType(),
            ]);
        }
    }
}
