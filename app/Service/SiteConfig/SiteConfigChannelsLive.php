<?php

namespace App\Service\SiteConfig;


use App\Constant\ActionTrackType;
use App\Constant\AgentGroup;
use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\InputItem;

class SiteConfigChannelsLive extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genActionTrackType([ActionTrackType::TENCENT_ADA, ActionTrackType::TENCENT_V3], ActionTrackType::TENCENT_V3),
            $this->genConvertType(),
            $this->genDeepExternalAction(),
        ]);

        // 视频号加热订单ID和视频ID
        if ($this->option_param->agent_group == AgentGroup::WX_CHANNEL_HOT) {
            $this->setOptions([
                (new InputItem())
                    ->setLabel('订单ID')
                    ->setProps('ext.channels_promotion_id')
                    ->setTips('请填写视频号加热订单ID')
                    ->setRequired(false)
                    ->make(),
                (new InputItem())
                    ->setLabel('视频ID')
                    ->setProps('ext.channels_export_id')
                    ->setTips('请填写视频号加热视频ID')
                    ->setRequired(true)
                    ->make()
            ]);
        }
    }

    protected function genGamePack(): array
    {
        return $this->genToutiaoGamePack();
    }
}
