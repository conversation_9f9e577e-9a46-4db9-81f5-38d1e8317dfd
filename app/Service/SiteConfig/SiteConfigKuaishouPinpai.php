<?php

namespace App\Service\SiteConfig;


use App\Constant\PlatId;
use App\Param\SiteOptionParam;

class SiteConfigKuaishouPinpai extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genAkey(),
            $this->genConvertType()
        ]);
    }

    protected function genGamePack(): array
    {
        return $this->genToutiaoGamePack(1);
    }
}
