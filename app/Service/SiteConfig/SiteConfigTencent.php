<?php

namespace App\Service\SiteConfig;


use App\Constant\ActionTrackType;
use App\Constant\AgentGroup;
use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\AdgroupTypeItem;
use App\Service\SiteConfig\SiteComponent\ConversionLinkTemplateItem;
use App\Service\SiteConfig\SiteComponent\InputItem;
use App\Service\SiteConfig\SiteComponent\SkipChannelConvertItem;

class SiteConfigTencent extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : ($this->option_param->game_type === 'IOS' ? $this->genApiConvertSourceType() : $this->genConvertSourceType()),
            $this->genConvertType(),
            $this->genDeepExternalAction(),
            $this->genActionTrackType([ActionTrackType::TENCENT_ADA, ActionTrackType::TENCENT_V3], ActionTrackType::TENCENT_V3)
        ]);

        // 是否跳过创建渠道包与转化步骤
        if ($this->option_param->mask !== 'compose' && $this->option_param->game_type === '安卓') {
            $this->addOption((new SkipChannelConvertItem())->make());
        }

        // 提供小游戏分包渠道号输入框
        if ($this->option_param->plat_id == PlatId::MINI) {
            $this->addOption(
                (new InputItem())
                    ->setLabel('分包渠道号')
                    ->setProps('app_android_channel_package_id')
                    ->setTips('请填写小游戏分包渠道号，请不要输入带分号的渠道号')
                    ->setRequired(false)
                    ->make()
            );

            if ($this->option_param->mask !== 'compose') {
                $this->addOption(
                    (new AdgroupTypeItem($this->option_param->plat_id))
                        ->setDisabled($this->option_param->action)
                        ->make()
                );
            }
        }

        $this->addOption(
            (new ConversionLinkTemplateItem($this->option_param->plat_id, $this->option_param->game_type))
                ->setDisabled($this->option_param->action)
                ->make()
        );
    }

    protected function genGamePack(): array
    {
        return $this->genToutiaoGamePack();
    }
}
