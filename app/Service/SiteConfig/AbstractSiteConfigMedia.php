<?php

namespace App\Service\SiteConfig;


use App\Constant\PlatformAbility;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\GamePackItem;
use App\Service\SiteConfig\SiteComponent\TemplateAddressItem;
use App\Service\SiteConfig\SiteComponent\TemplateTypeItem;

abstract class AbstractSiteConfigMedia extends AbstractSiteConfig
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->genGamePack(),
            $this->genTemplateType(),
            $this->genTemplateAddress(),
        ]);
    }

    protected function genGamePack(): array
    {
        $options = [
            [
                'label' => '打包',
                'value' => 1
            ],
            [
                'label' => '不打包',
                'value' => 0
            ],
        ];
        return (new GamePackItem($options))->setDefault(1)->make();
    }

    protected function genTemplateType(): array
    {
        return (new TemplateTypeItem($this->option_param->platform))->make();
    }

    protected function genTemplateAddress(): array
    {
        if (!in_array($this->option_param->platform, PlatformAbility::SITE_CONFIG_TEMPLATE_ADDRESS)) {
            return (new TemplateAddressItem())->make();
        }

        return [];
    }

    protected function genToutiaoGamePack($default = 2): array
    {
        $options = [
            [
                'label' => '媒体分包',
                'value' => 2
            ],
            [
                'label' => '打包',
                'value' => 1
            ],
            [
                'label' => '不打包',
                'value' => 0
            ],
        ];

        // IOS 只选不分包
        if ($this->option_param->game_type === 'IOS') {
            $options = [
                array_pop($options)
            ];
            $default = 0;
        }

        return (new GamePackItem($options))->setDefault($default)->make();
    }
}
