<?php

namespace App\Service\SiteConfig;

use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\SubPkgItem;

class SiteConfigBilibili extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genConvertType(),
            $this->genDeepExternalAction()
        ]);

        if ($this->option_param->game_id == 18180 &&
            $this->option_param->mask === 'compose' && $this->option_param->game_type === '安卓' && $this->option_param->plat_id != PlatId::MINI) {
            $this->setOptions([
                (new SubPkgItem(
                    $this->option_param->platform, $this->option_param->game_id, $this->option_param->action
                ))->make()
            ]);
        }
    }
}
