<?php

namespace App\Service\SiteConfig;


use App\Constant\ActionTrackType;
use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\InputItem;

class SiteConfigUc extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genConvertType(),
            $this->genDeepExternalAction(),
            $this->genConvertDataType(),
            $this->genActionTrackType([ActionTrackType::UC_V2]),
            $this->genIsThird(),
            $this->genExternalUrl()
        ]);

        $this->addOption(
            (new InputItem())
                ->setLabel('上报比例(%)')
                ->setProps('upt_rate')
                ->setValidator('^([1-9][0-9]{0,1}|100)$', '请输入1-100内的整数')
                ->make()
        );

        // 该token用于UC媒体调用贪玩生成微信小游戏URL scheme
        // https://www.yuque.com/yzsaga/ryopix/nntekx
        if ($this->option_param->plat_id == PlatId::MINI) {
            $this->addOption($this->genAkey());
        }
    }
}
