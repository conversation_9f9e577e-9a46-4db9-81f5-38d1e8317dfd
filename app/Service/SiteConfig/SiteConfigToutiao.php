<?php

namespace App\Service\SiteConfig;



use App\Constant\ActionTrackType;
use App\Param\SiteOptionParam;

class SiteConfigToutiao extends AbstractSiteConfigToutiaoBase
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->genActionTrackType([ActionTrackType::TOUTIAO_V2], ActionTrackType::TOUTIAO_V2)
        ]);
    }
}
