<?php

namespace App\Service\SiteConfig;

use App\Constant\ConvertSourceType;
use App\Constant\PlatId;
use App\Param\SiteOptionParam;
use App\Service\SiteConfig\SiteComponent\AkeyItem;
use App\Service\SiteConfig\SiteComponent\AppAndroidChannelPackageIdItem;

class SiteConfigBaidu extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            $this->option_param->plat_id == PlatId::MINI ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genConvertType(),
            $this->genDeepExternalAction()
        ]);

        $this->addOption(
            (new AkeyItem($this->option_param->media_type))
                ->setCondition([
                    'convert_source_type' => [ConvertSourceType::API, ConvertSourceType::H5_API]
                ])->make()
        );

        // 百度新建渠道包 app_android_channel_package_id
        if ($this->option_param->mask !== 'compose' && $this->option_param->game_type === '安卓') {
            $this->addOption((new AppAndroidChannelPackageIdItem())->make());
        }
    }
}
