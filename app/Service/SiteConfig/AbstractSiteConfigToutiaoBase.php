<?php

namespace App\Service\SiteConfig;


use App\Constant\PlatId;
use App\Param\SiteOptionParam;

abstract class AbstractSiteConfigToutiaoBase extends AbstractSiteConfigMedia
{
    public function __construct(SiteOptionParam $param)
    {
        parent::__construct($param);

        $this->setOptions([
            in_array($this->option_param->plat_id, PlatId::getMiniPlatIDArray()) ? $this->genH5ConvertSourceType() : $this->genConvertSourceType(),
            $this->genConvertToolkit(),
            $this->genConvertType(),
            $this->genDeepExternalAction(),
            $this->genConvertDataType(),
            $this->genExternalUrl(),
            $this->genIsThird(),
        ]);
    }

    protected function genGamePack(): array
    {
        return $this->genToutiaoGamePack();
    }
}
