<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\Twitter\Account\AccountModel;
use App\Param\MediaAccountInfoParam;

class TwitterService
{
    public function addAccount($platform, $consumer_key, $access_token, $consumer_secret, $access_token_secret, $company)
    {
        if (empty($consumer_key) || empty($access_token) || empty($consumer_secret) || empty($access_token_secret) || empty($company)) {
            throw new AppException('请输入所有参数');
        }

        $message = "";
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;

        $service = new MediaService();

        /**
         * media_account表字段对应，可查询后调用接口
         * 'majordomo_name' => $consumer_key
         * 'access_token' => $access_token
         * 'wechat_account_name' => $consumer_secret
         * 'refresh_token' => $access_token_secret
         *
         * 账号ID为字符串，需要转成数字入库
         * 原账号字符串存在 account_password 字段
         *
         */
        /**
         *  请求接口可这样请求
         *  $account_model = new MediaAccountModel();
         *  $data = $account_model->getDataByAccountId(869379446245425152, MediaType::TWITTER);
         *
         *  $model = new \App\Model\HttpModel\Twitter\Account\AccountModel(
         *  $data->majordomo_name,
         *  $data->access_token,
         *  $data->wechat_account_name,
         *  $data->refresh_token
         *  );
         */
        $model = new AccountModel($consumer_key, $access_token, $consumer_secret, $access_token_secret);
        $account_list = $model->getList();

        foreach ($account_list['data'] as $item) {
            $tmp_message = $service->saveAuthAccount(new MediaAccountInfoParam([
                'media_type' => MediaType::TWITTER,
                'platform' => $platform,
                'account_id' => abs(intval(hexdec(substr(hash('sha256', $item['id']), 0, 18)))),
                'account_password' =>  $item['id'],
                'account_name' =>  $item['name'],
                'access_token' => $access_token,
                'access_token_expires' => 0,
                'refresh_token' => $access_token_secret,
                'refresh_token_expires' => 0,
                'majordomo_name' => $consumer_key,
                'wechat_account_name' => $consumer_secret,
                'company' => $company,
            ]), $creator_id, $creator_name);
            $message .= "$tmp_message;";
        }

        return $message;
    }
}
