<?php


namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Model\HttpModel\Toutiao\DMP\CustomAudienceModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAccountLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use App\Utils\Helpers;
use Toutiao\DMP\DmpData;
use Toutiao\DMP\IdItem;
use Toutiao\DMP\IdItem\DataType;
use ZipArchive;

class ToutiaoService implements AccountLeader, DMP
{

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_EXPORT);
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_FILE_ADDITION);
    }

    /**
     * 创建数据源文件
     * @param $file_uuid
     * @param array $file_list
     * @param int $addition_type
     * @return array|bool
     * <AUTHOR>
     */
    public function createDataSourceFile($file_uuid, array $file_list, $addition_type = 1)
    {
        $dmp_data = new DmpData();
        $id_item_list = [];
        $id_item_limit = 1;
        $row_limit = 1;
        $file_count = 0;
        foreach ($file_list as $file) {
            if (!file_exists($file['name'])) {
                Helpers::getLogger('audience')->warning("file not exists", [
                    'file_uuid' => $file_uuid,
                    'filename' => $file['name'],
                    'media_type' => MediaType::TOUTIAO,
                ]);
                continue;
            }
            $handle = fopen($file['name'], 'r');
            $data_type = UserIDType::TOUTIAO_TYPE_MAP[$file['data_type']];
            $need_to_hash = false;
            if ($addition_type === AudienceModel::TYPE_EXPORT && $data_type === DataType::OAID) {
                $data_type = DataType::OAID_MD5;
                $need_to_hash = true;
            }
            while (!feof($handle)) {
                $md5 = trim(fgets($handle));
                if (empty($md5)) {
                    break;
                }
                if (++$row_limit > 1000000) {
                    $file_count++;
                    $row_limit = 1;
                }
                $id_item = new IdItem();
                $id_item->setDataType($data_type);
                if ($need_to_hash) {
                    $md5 = md5($md5);
                }
                $id_item->setId($md5);
                $id_item_list[] = $id_item;
                // id_item_list 最多1000个
                if (++$id_item_limit > 1000) {
                    $dmp_data->setIdList($id_item_list);
                    $dmp_string = $dmp_data->serializeToString();
                    $dmp_bs4 = base64_encode($dmp_string);
                    file_put_contents(AudienceService::getAudienceDir(MediaType::TOUTIAO) . "$file_uuid-{$file_count}.txt", $dmp_bs4 . PHP_EOL, FILE_APPEND);
                    $id_item_limit = 0;
                    $id_item_list = [];
                }
            }
            fclose($handle);
        }
        // 把剩余的id item 加回去最后一个文件
        // 因为id_item_list 最多1000个
        // 加到最后一个文件 也不会超出50M (100W行靠近50M)
        // 如果刚好做到50M大小, 头条服务器会超时
        if (!empty($id_item_list)) {
            $dmp_data->setIdList($id_item_list);
            $dmp_string = $dmp_data->serializeToString();
            $dmp_bs4 = base64_encode($dmp_string);
            file_put_contents(AudienceService::getAudienceDir(MediaType::TOUTIAO) . "$file_uuid-{$file_count}.txt", $dmp_bs4 . PHP_EOL, FILE_APPEND);
        }

        $zip_paths = [];
        if ($file_count === 0 && $row_limit === 1) {
            // 文件无内容
            return $zip_paths;
        }
        for ($i = 0; $i <= $file_count; $i++) {
            $zip_path = AudienceService::getAudienceDir(MediaType::TOUTIAO) . "$file_uuid-{$i}.zip";
            $zip = new ZipArchive;
            $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
            if ($code === TRUE) {
                $zip->addFile(AudienceService::getAudienceDir(MediaType::TOUTIAO) . "$file_uuid-{$i}.txt", "$file_uuid-{$i}.txt");
                $zip->close();
                unlink(AudienceService::getAudienceDir(MediaType::TOUTIAO) . "$file_uuid-{$i}.txt");
                $zip_paths[] = $zip_path;
            } else {
                Helpers::getLogger('audience')->error("create data source zip fail", [
                    'file_uuid' => $file_uuid,
                    'zip_error_code' => $code,
                    'media_type' => MediaType::TOUTIAO,
                ]);
            }
        }
        return $zip_paths;
    }

    public function editPushAccountList($id, $account_ids)
    {
        $audience_model = new AudienceModel();
        $audience_info = $audience_model->getData($id);
        $media_model = new MediaAccountModel();
        $account_info = $media_model->getDataByAccountId($audience_info->account_id);
        $custom_audience = new CustomAudienceModel();
        $custom_audience->push($audience_info->account_id, $account_info->access_token, $audience_info->audience_id, $account_ids);
        $already_push_account_list = json_decode($audience_info->push_account_list, true);
        return $audience_model->editPushAccountList($id, json_encode(array_merge($already_push_account_list, $account_ids)));
    }

    private function addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, $addition_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];
        foreach ($companies as $company) {
            // 挑选种子广告主 生成人群包
            $seed_account = array_pop($company['account']);
            // 除种子广告主外，同主体的其他广告主加入待推送列表
            $push_account_list = json_encode($company['account']);
            $media_account_info = $media_model->getDataWithMediaType($seed_account, MediaType::TOUTIAO);
            $table_audience_id = $audience_model->add(
                $user_id,
                $username,
                $device_task_id,
                MediaType::TOUTIAO,
                $company['name'],
                $seed_account,
                $audience_name,
                $audience_desc,
                $push_account_list,
                $addition_type,
                0,
                '',
                $media_account_info->platform,
                $data_source_type ?: 'UID'
            );
            if (!($table_audience_id > 0)) {
                Helpers::getLogger('audience')->error("add audience record fail", [
                    'device_task_id' => $device_task_id,
                    'account_id' => $seed_account,
                    'company' => $company,
                    'audience_name' => $audience_name,
                    'audience_desc' => $audience_desc,
                    'data_source_type' => $data_source_type,
                    'media_type' => MediaType::TOUTIAO,
                    'creator_id' => $user_id,
                    'creator' => $username,
                ]);
                continue;
            }
            $table_audience_ids[] = $table_audience_id;
            // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
            ///** @uses \App\Task\ToutiaoTask::createDataSource() */
            //Container::getServer()->task(['action' => 'ToutiaoTask.createDataSource', 'data' => ['table_audience_id' => $table_audience_id]]);
        }
        return $table_audience_ids;
    }

    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsToutiaoAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }

}