<?php

namespace App\Service;

use App\Model\HttpModel\Qihu\Account\AccountModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Container;
use App\Exception\AppException;
use App\MysqlConnection;
use App\Param\MediaAccountInfoParam;
use App\Param\MediaMajordomoAccountInfoParam;
use Throwable;

class QihuService
{
    /**
     * @param $platform
     * @param $username
     * @param $passwd
     * @param $media_type
     * @return string
     * @throws Throwable
     */
    public function addAccount($platform, $username, $passwd, $media_type): string
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $developer_info = (new MediaDeveloperAccountModel())->getDataByPlatformMediaCompany($platform, $media_type, '');
        if (!$developer_info) {
            $msg = "找不到360开发者，组织: $platform, 联系管理员配置";
            throw new AppException($msg);
        }

        //获取token
        $account_model = new AccountModel();
        $login_info = $account_model->clientLogin($developer_info->app_id, $developer_info->app_secret, $username, $passwd);
        if (!isset($login_info['accessToken'])) throw new AppException("获取token失败，请重新授权");

        // 获取子账号列表，接口报错则是单独授权子账号
        try {
            $sub_account_list = $account_model->getMccList($developer_info->app_id, $login_info['accessToken']);
        } catch (AppException $e) {
            if (strpos($e->getMessage(), "该账户不是超级账户") === false) {
                throw new AppException($e->getMessage());
            }
        }

        $is_majordomo = false;
        $accounts = [];
        $account_info = $account_model->getInfo($developer_info->app_id, $login_info['accessToken'], '');

        if (isset($sub_account_list['mccList']) && !empty($sub_account_list['mccList'])) {
            $accounts = $this->getSubAccountInfo($developer_info->app_id, $login_info['accessToken'], $sub_account_list['mccList']);
            $is_majordomo = true;
            $majordomo_id = $account_info['uid'];
            $access_token_expires = 0;
            $account_password = '';
        } else {
            $accounts[] = $account_info;
            $majordomo_id = 0;
            $access_token_expires = time() + 36000;
            $account_password = $passwd;
        }

        $message = '';
        $media_service = new MediaService();
        try {
            MysqlConnection::getConnection('default')->beginTransaction();
            if ($is_majordomo) {
                $media_service->saveAuthMajordomoAccount(
                    new MediaMajordomoAccountInfoParam([
                        'platform' => $platform,
                        'media_type' => $media_type,
                        'account' => $account_info['userName'],
                        'account_id' => $account_info['uid'],
                        'password' => $passwd,
                        'access_token' => $login_info['accessToken'],
                        'access_token_expires' => time() + 36000,
                        'refresh_token' => '',
                        'refresh_token_expires' => 0,
                        'developer_app_id' => $developer_info->app_id,
                        'company' => $account_info['companyName']
                    ]), $creator_id, $creator_name
                );
            }

            foreach ($accounts as $account) {
                $media_account_param = new MediaAccountInfoParam([
                    'media_type' => $media_type,
                    'platform' => $platform,
                    'account_id' => $account['uid'],
                    'account_name' => $account['userName'],
                    'account_password' => $account_password,
                    'access_token' => $login_info['accessToken'],
                    'access_token_expires' => $access_token_expires,
                    'refresh_token' => '',
                    'refresh_token_expires' => 0,
                    'agent' => $developer_info->app_id,
                    'company' => $account['companyName'],
                    'toutiao_majordomo_id' => $majordomo_id
                ]);
                $tmp_message = $media_service->saveAuthAccount($media_account_param, $creator_id, $creator_name);
                $message .= "{$account['userName']}-$tmp_message;";
            }

            MysqlConnection::getConnection('default')->commit();
        } catch (Throwable $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw $e;
        }

        return $message;
    }

    /**
     * 筛选出正常使用的子账号并获取账号信息
     * @param $app_id
     * @param $access_token
     * @param $account_list
     * @return array
     */
    public function getSubAccountInfo($app_id, $access_token, $account_list)
    {
        $accounts = [];
        $account_model = new AccountModel();

        foreach ($account_list as $item) {
            /**
             * ptype: 0-等待绑定 1-通过绑定
             * status:  0等待审核,1账户正常生效,2余额不足
             *          -1帐户信息未填写,-2注册未充值,-3审核拒绝,-4账户关闭,-5客户类型待确认,-6帐户已注销,-7待销售确认
             */
            if ($item['ptype'] == 1 && ((int)$item['status'] > 0 || $item['status'] == -3)) {
                $accounts[] = $account_model->getInfo($app_id, $access_token, $item['userName']);
            }
        }

        return $accounts;
    }
}
