<?php
/**
 * 后台消息
 * User: hejs
 * Date: 2019/11/12
 * Time: 11:34
 */

namespace App\Service;

use App\Container;
use App\Struct\RedisCache;
use App\Task\NoticeTask;

class NoticeService
{

    const NOTICE_BELL = 1;
    const NOTICE_DMP_TASK = 2;
    const NOTICE_DMP_BATCH = 3;
    const NOTICE_AUDIENCE_TASK = 4;
    const NOTICE_ALERT_TASK = 5;       // 超管弹窗预警
    const NOTICE_AUTH = 6;             // 媒体授权
    const NOTICE_LOGOUT = 7;           // 退出登录
    const NOTICE_MATERIAL_TASK = 8;    // 素材创建
    const NOTICE_AD_TASK_UPDATE = 9;   // 广告任务创建
    const NOTICE_MATERIAL_FILE_EXPAND_TASK_UPDATE = 10;   // 素材文件扩展任务
    const NOTICE_TOUTIAO_STAR_TASK_UPDATE = 11;   // 星图任务创建
    const NOTICE_VIDEO_CLIP_TASK_UPDATE = 12;   // 视频混剪任务
    const NOTICE_VIDEO_CLIP_COMPOSE_UPDATE = 13;   // 视频混剪组合

    const LEVEL_NOTICE = 0;
    const LEVEL_WARNING = 1;
    const LEVEL_DANGER = 2;

    const STATE_UNREAD = 0;
    const STATE_READ = 1;

    public function addNotice($user_id, $level, $title, $type, array $message_args, $route_id = 0, $param = [])
    {
        $sms_data = [
            'user_id' => $user_id,
            'type' => $type,
            'message_args' => $message_args,
        ];

        $system_data = [
            'user_id' => $user_id,
            'level' => $level,
            'title' => $title,
            'type' => $type,
            'message_args' => $message_args,
            'route_id' => $route_id,
            'param' => $param
        ];

        $server = Container::getServer();
        if (!$server || $server->taskworker) {
            $task = new NoticeTask();
            $task->pushSMS($sms_data);
            $task->pushSystemNotice($system_data);
            return true;
        }

        $server->task([
            /** @uses NoticeTask::pushSMS */
            'action' => 'NoticeTask.pushSMS',
            'data' => $sms_data
        ]);

        $server->task([
            /** @uses NoticeTask::pushSystemNotice */
            'action' => 'NoticeTask.pushSystemNotice',
            'data' => $system_data
        ]);
        return true;
    }

    public function addBellNotice($user_id, $level, $title, $message, $route_id, $param = [])
    {
        $system_data = [
            'user_id' => $user_id,
            'level' => $level,
            'title' => $title,
            'message' => $message,
            'route_id' => $route_id,
            'param' => $param
        ];
        $server = Container::getServer();
        $server->task([
            /** @uses NoticeTask::pushSystemNoticeWithoutTemplate */
            'action' => 'NoticeTask.pushSystemNoticeWithoutTemplate',
            'data' => $system_data
        ]);
    }

    /**
     * 推送websocket消息，不入库。
     *
     * @param [int] $user_id
     * @param [int] $level
     * @param [int] $notice_type
     * @param [string] $title
     * @param [mixed] $message
     *
     * @return bool
     */
    public function pushMessage($user_id, $level, $notice_type, $title, $message)
    {
        $data = [
            'user_id' => $user_id,
            'level' => $level,
            'title' => $title,
            'message' => $message,
            'notice_type' => $notice_type
        ];
        $server = Container::getServer();
        if (!$server) {
            return false;
        }
        return $this->unicast($user_id, $notice_type, $data);
    }

    /**
     * 单独推送给用户
     *
     * @param int $user_id
     * @param $notice_type
     * @param $data
     * @return boolean true|false
     */
    public function unicast($user_id, $notice_type, $data)
    {
        $fd = Container::getWSManager()->getFDByUserID($user_id);
        $server = Container::getServer();
        if (empty($server) || !$server->exist($fd)) {
            return false;
        }
        $data['notice_type'] = $notice_type;
        $data['create_time'] = time();
        $retData = [
            'code' => 0,
            'message' => '您有一条新的消息',
            'data' => $data,
        ];
        $server->push($fd, json_encode($retData, JSON_UNESCAPED_UNICODE));
        return true;
    }

    /**
     * 广播
     *
     * @param $notice_type
     * @param $data
     * @return boolean true|false
     */
    public function broadcast($notice_type, $data)
    {
        $data['notice_type'] = $notice_type;
        $data['create_time'] = time();
        $server = Container::getServer();
        foreach ($server->connections as $fd) {
            $info = $server->getClientInfo($fd);
            // 过滤非websocket连接
            if (!$info || !isset($info["websocket_status"]) || intval($info["websocket_status"]) <= 0) {
                continue;
            }
            $server->push($fd, json_encode([
                'code' => 0,
                'message' => '消息通知',
                'data' => $data
            ], JSON_UNESCAPED_UNICODE));
        }

        return true;
    }
}
