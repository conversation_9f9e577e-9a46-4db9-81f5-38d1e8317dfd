<?php
/**
 * 广点通创意规格校验
 * User: Lin
 * Date: 2022/3/24
 * Time: 17:51
 */

namespace App\Service;

use App\Constant\BatchAD;
use App\Constant\MediaType;
use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\AdcreativeTemplate\AdcreativeTemplateModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\AbstractParam;
use App\Param\ADServing\Tencent\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\Tencent\Basics\ADSettingContentParam;
use App\Param\Tencent\AdCreativeTemplateParam;
use App\Param\Tencent\TemplateVerifyParam;
use App\Utils\Helpers;
use Common\EnvConfig;

class TencentVerifyService extends AbstractParam
{
    public $request_id;
    public $adcreative_elements_rule;
    public $adcreative_attributes_rule;
    public $landing_page_config;
    public $link_name_type_config;
    public $link_page_type_config;

    public function __construct($property = [])
    {
        parent::__construct($property);
    }

    public function init(
        $account_id, array $site_set,
        string $promoted_object_type,
        string $automatic_site_enabled,
        string $is_dynamic_creative,
        $adcreative_template_id,
        $dynamic_creative_type = TencentEum::DYNAMIC_CREATIVE_TYPE_COMMON)
    {
        $acc_info = (new MediaAccountModel())->getDataByAccountId($account_id, MediaType::TENCENT);


        if($automatic_site_enabled == 'true') {
            $site_set = [];
        }
        try {
            $request_param = new AdCreativeTemplateParam([
                'account_id' => $account_id,
                'site_set' => $site_set,
                'promoted_object_type' => $promoted_object_type,
                'automatic_site_enabled' => $automatic_site_enabled,
                'is_dynamic_creative' => $is_dynamic_creative,
                'adcreative_template_id' => $adcreative_template_id,
                'dynamic_creative_type' => $dynamic_creative_type,
            ]);

            $rule = (new AdcreativeTemplateModel())->info($request_param, $acc_info->access_token);
            $this->request_id = $rule['request_id'] ?? 0;

        } catch (AppException $e) {
            throw new AppException("获取创意规格详情错误，请重试----{$e->getMessage()}");
        }

        if (!$rule['list']) {
            return $this;
        }

        $rule = $rule['list'][0];

        $adcreative_elements_rule = $rule['adcreative_elements'];
        $landing_page_config_rule = $rule['landing_page_config'];
        $adcreative_attributes_rule = $rule['adcreative_attributes'];

        foreach ($adcreative_attributes_rule as $info) {
            $this->adcreative_attributes_rule[$info['name']] = [
                'required' => $info['required'],
                'content' => $info, // 输出整个结构
            ];
        }

        foreach ($adcreative_elements_rule as $info) {

            $this->adcreative_elements_rule[$info['name']] = [
                'required' => $info['required'],
                'element_type' => $info['element_type'],
                'field_type' => $info['field_type'],
            ];
            if($info['parent_name']) {
                $this->adcreative_elements_rule[$info['name']]['parent_name'] = $info['parent_name'];
            }
            if ($info['element_type'] === 'ELEMENT_TYPE_TEXT') {
                // 文本类型规格
                if (isset($info['restriction']['text_restriction'])) {
                    $this->adcreative_elements_rule[$info['name']]['max_length'] = $info['restriction']['text_restriction']['max_length'];
                    $this->adcreative_elements_rule[$info['name']]['min_length'] = $info['restriction']['text_restriction']['min_length'];
                }
                if (isset($info['enum_property']['enumeration'])) {
                    $this->adcreative_elements_rule[$info['name']]['require_text'] = array_column($info['enum_property']['enumeration'], 'value');
                }
            }
            if ($info['element_type'] === 'ELEMENT_TYPE_IMAGE') {
                if (isset($info['restriction']['image_restriction'])) {
                    $this->adcreative_elements_rule[$info['name']]['file_format'] = $info['restriction']['image_restriction']['file_format'];
                    $this->adcreative_elements_rule[$info['name']]['file_size'] = $info['restriction']['image_restriction']['file_size'];
                    $this->adcreative_elements_rule[$info['name']]['height'] = $info['restriction']['image_restriction']['height'];
                    $this->adcreative_elements_rule[$info['name']]['width'] = $info['restriction']['image_restriction']['width'];
                }
            }
            if ($info['element_type'] === 'ELEMENT_TYPE_VIDEO') {
                if (isset($info['restriction']['video_restriction'])) {
                    $this->adcreative_elements_rule[$info['name']]['file_format'] = $info['restriction']['video_restriction']['file_format'];
                    $this->adcreative_elements_rule[$info['name']]['file_size'] = $info['restriction']['video_restriction']['file_size'];
                    $this->adcreative_elements_rule[$info['name']]['height'] = $info['restriction']['video_restriction']['height'];
                    $this->adcreative_elements_rule[$info['name']]['width'] = $info['restriction']['video_restriction']['width'];
                    $this->adcreative_elements_rule[$info['name']]['max_duration'] = $info['restriction']['video_restriction']['max_duration'];
                    $this->adcreative_elements_rule[$info['name']]['min_duration'] = $info['restriction']['video_restriction']['min_duration'];
                    $this->adcreative_elements_rule[$info['name']]['min_height'] = $info['restriction']['video_restriction']['min_height'];
                    $this->adcreative_elements_rule[$info['name']]['min_width'] = $info['restriction']['video_restriction']['min_width'];
                    $this->adcreative_elements_rule[$info['name']]['ratio_height'] = $info['restriction']['video_restriction']['ratio_height'];
                    $this->adcreative_elements_rule[$info['name']]['ratio_width'] = $info['restriction']['video_restriction']['ratio_width'];
                }
            }
            if ($info['element_type'] === 'ELEMENT_TYPE_STRUCT') {
                // 数组结构
                if ($info['field_type'] === 'FIELD_TYPE_STRUCT_ARRAY') {
                    if (isset($info['array_property'])) {
                        $this->adcreative_elements_rule[$info['name']]['max_number'] = $info['array_property']['max_number'];
                        $this->adcreative_elements_rule[$info['name']]['min_number'] = $info['array_property']['min_number'];
                    }
                }
            }
        }

        if ($landing_page_config_rule && isset($landing_page_config_rule['support_page_type_list'])) {

            // 落地页规格
            $this->landing_page_config = [
                'required' => $landing_page_config_rule['required'],
                'support_page_type' => array_column($landing_page_config_rule['support_page_type_list'], 'page_type')
            ];

            // 文字链名称规格
            foreach ($landing_page_config_rule['support_page_type_list'] as $support_page_type_info) {
                if (isset($support_page_type_info['support_link_name_type'])) {
                    $this->link_name_type_config[$support_page_type_info['page_type']] = [
                        'required' => $support_page_type_info['support_link_name_type']['required'],
                    ];
                    foreach ($support_page_type_info['support_link_name_type']['list'] as $item) {
                        $this->link_name_type_config[$support_page_type_info['page_type']]['require_text'][] = $item['link_name_type'];
                        $this->link_name_type_config[$support_page_type_info['page_type']]['link_name_type_map'][$item['link_name_type']] = $item['description'];
                    }
                }
            }

            // 文字链类型规格
            foreach ($landing_page_config_rule['support_page_type_list'] as $support_page_type_info) {
                if (isset($support_page_type_info['support_link_page_type'])) {
                    $this->link_page_type_config[$support_page_type_info['page_type']] = [
                        'required' => $support_page_type_info['support_link_page_type']['required'],
                        'require_text' => array_column($support_page_type_info['support_link_page_type']['list'], 'link_page_type')
                    ];
                }
            }
        }

        return $this;
    }

    /**
     * @return array
     */
    public function getRules()
    {
        return [
            'adcreative_elements_rule' => $this->adcreative_elements_rule,
            'adcreative_attributes_rule' => $this->adcreative_attributes_rule,
            'landing_page_config' => $this->landing_page_config,
            'link_name_type_config' => $this->link_name_type_config,
            'link_page_type_config' => $this->link_page_type_config,
            'request_id' => $this->request_id,
        ];
    }

    /**
     * 参数校验
     * @param $field
     * @param $item
     * @param bool $is_serious
     * @return TemplateVerifyParam
     */
    public function verify($field, $item,$is_serious = false)
    {
        $verify = new TemplateVerifyParam();

        if ($rule = $this->adcreative_attributes_rule[$field] ?? []) {
            if ($rule['required'] && !$item) {
                return $verify->error('参数必填');
            }
        } elseif ($rule = $this->adcreative_elements_rule[$field] ?? []) {
            if ($rule['required'] && !$item) {
                return $verify->error('参数必填!');
            }
            if ($item) {
                switch ($rule['element_type']) {
                    case 'ELEMENT_TYPE_TEXT':
                        $len = Helpers::ADServingStrLen($item);
                        $min_len = $rule['min_length'] ?? 0;
                        $max_len = $rule['max_length'] ?? 0;
                        $require_text = $rule['require_text'] ?? [];
                        if ($min_len && $len < $min_len * 2) {
                            return $verify->error("文本参数不能少于{$min_len}位");
                        }
                        if ($max_len && $len > $max_len * 2) {
                            return $verify->error("文本参数不能超过{$max_len}位");
                        }
                        if ($require_text && !in_array($item, $require_text)) {
                            return $verify->error("参数不符合要求");
                        }
                        break;
                    case 'ELEMENT_TYPE_IMAGE':
                        break;
                    case 'ELEMENT_TYPE_STRUCT':
                        $min_num = $rule['min_number'] ?? 0;
                        $max_num = $rule['max_number'] ?? 0;
                        $count = count($item);
                        if ($min_num && $count < $min_num) {
                            return $verify->error("参数数量不能小于{$min_num}");
                        }
                        if ($max_num && $count > $max_num) {
                            return $verify->error( "参数数量不能大于{$max_num}");
                        }
                        break;
                }
            }
        }elseif ($is_serious && $item) {
            return $verify->error('功能不支持，请不要使用');
        }
        if ($field === 'page_type' && $this->landing_page_config) {
            if (!$item && $this->landing_page_config['required']) {
                return $verify->error( '落地页必选');
            }
//            if ($item && !in_array($item, $this->landing_page_config['support_page_type'])) {
//                return $verify->error( '不支持该类型落地页或未选择落地页');
//            }
        }

        return $verify;
    }

    /**
     * 专门校验落地页文字链接
     * @param $field
     * @param string $item
     * @param string $page_type
     * @return TemplateVerifyParam
     */
    public function verifyPageLink($field, $item = '', $page_type = '')
    {
        $verify = new TemplateVerifyParam();
        if ($field === 'link_name_type' && $this->link_name_type_config) {
            if (isset($this->link_name_type_config[$page_type]['required'])) {
                if (!$item && $this->link_name_type_config[$page_type]['required']) {
                    return $verify->error('文字链接名称必选');
                }
            }
//            if (isset($this->link_name_type_config[$page_type]['require_text'])) {
//                if ($item && !in_array($item, $this->link_name_type_config[$page_type]['require_text'])) {
//                    return $verify->error('不支持该类型文字链接名称');
//                }
//            }
        }
        if ($field === 'link_page_type' && $item && $this->link_page_type_config) {
            if (isset($this->link_page_type_config[$page_type]['required'])) {
                if (!$item && $this->link_page_type_config[$page_type]['required']) {
                    return $verify->error('文字链必选');
                }
            }
            if (isset($this->link_page_type_config[$page_type]['require_text'])) {
                if ($item && !in_array($item, $this->link_page_type_config[$page_type]['require_text'])) {
                    return $verify->error('不支持该类型文字链类型');
                }
            }
        }
        return $verify;
    }

    /**
     * 专门校验素材
     * @param $field
     * @param $struct
     * @return TemplateVerifyParam
     */
    public function verifyMaterial($field, $struct = [])
    {
        $verify = new TemplateVerifyParam();
        if ($rule = $this->adcreative_elements_rule[$field] ?? []) {
            if ($field === 'video') {
                $cover_rule = $this->adcreative_elements_rule['image'] ?? [];
                if ($rule['required'] && !$struct) {
                    return $verify->error('必须使用视频素材');
                }
                foreach ($struct as $video_key => $video_info) {
                    if ($video_info['width'] < $rule['min_width']) {
                        return $verify->error( "视频尺寸要求至少为{$rule['min_width']}x{$rule['min_height']}");
                    }
                    if ($video_info['height'] < $rule['min_height']) {
                        return $verify->error( "视频尺寸要求至少为{$rule['min_width']}x{$rule['min_height']}");
                    }
                    if ($video_info['duration'] < $rule['min_duration'] || $video_info['duration'] > $rule['max_duration']) {
                        return $verify->error( "视频素材时长要求为{$rule['min_duration']}~{$rule['max_duration']}");
                    }
                    if (floor($video_info['size'] / 1024) > $rule['file_size']) {
                        return $verify->error( "视频素材大小不得超过{$rule['file_size']}KB");
                    }
                    if ($cover_rule) {
                        foreach ($video_info['cover_list'] as $image_key => $image_info) {
                            if ($image_info['width'] != $cover_rule['width']) {
                                return $verify->error( "封面图片尺寸要求至少为{$cover_rule['width']}x{$cover_rule['height']}");
                            }
                            if ($image_info['height'] != $cover_rule['height']) {
                                return $verify->error( "封面图片尺寸要求至少为{$cover_rule['width']}x{$cover_rule['height']}");
                            }
                        }
                    }
                }
            }
            // 组图
            if ($field === 'image_list') {
                if ($rule['required'] && !$struct) {
                    return $verify->error('必须使用组图素材');
                }
                foreach ($struct as $image_key => $images) {
                    foreach($images['image_list'] as $image_info) {
                        if ($image_info['width'] != $rule['width']) {
                            return $verify->error("组图图片尺寸要求至少为{$rule['width']}x{$rule['height']}");
                        }
                        if ($image_info['height'] != $rule['height']) {
                            return $verify->error("组图图片尺寸要求至少为{$rule['width']}x{$rule['height']}");
                        }
                    }
                }
            }
            // 图片
            if ($field === 'image') {
                // 如果有视频就不校验图片素材了(封面)
                if ($this->adcreative_elements_rule['video']['required'] ?? false) {
                    return $verify;
                }
                if ($rule['required'] && !$struct) {
                    return $verify->error( '必须使用图片素材');
                }
                foreach ($struct as $image_key => $image_info) {
                    if ($image_info['width'] != $rule['width']) {
                        return $verify->error("图片尺寸要求至少为{$rule['width']}x{$rule['height']}");
                    }
                    if ($image_info['height'] != $rule['height']) {
                        return $verify->error("图片尺寸要求至少为{$rule['width']}x{$rule['height']}");
                    }
                }
            }
        }
        return $verify;
    }


    /**
     * 媒体api创意规格校验
     * @return array
     */
//    public function verifyParamUnify()
//    {
//        $error_msg_list = [];
//
//        /**
//         * @var ADOtherSettingContentParam $other_setting
//         */
//        $other_setting = $this->other_setting;
//
//        if (!$other_setting->template_id) {
//            return $error_msg_list;
//        }
//
//        $account_id = $this->account_list->account_list[0]['account_id'];
//        $site_set = $other_setting->site_set;
//        if ($this->site_config->plat_id == EnvConfig::PLAT_ID_MINI) {
//            $promoted_object_type = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
//        } else {
//            $promoted_object_type = $this->site_config->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;
//        }
//        $automatic_site_enabled = $other_setting->site_set_none == 'auto' ? 'true' : 'false';
//        $is_dynamic_creative = $this->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE ? 'true' : 'false';
//
//        $service = TencentVerifyService::init(
//            $account_id, $site_set, $promoted_object_type, $automatic_site_enabled, $is_dynamic_creative, $other_setting->template_id);
//
//        // ------------------------------------参数包校验-------------------------------
//
//        foreach ($this->setting_list->getContentList() as $setting_id => $setting) {
//
//            /* @var ADSettingContentParam $setting */
//
//            // 底部文案
//            $bottom_text_verify = $service->verify('bottom_text', $setting->bottom_text);
//            if (!$bottom_text_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'bottom_text',
//                    'source_id' => $setting_id,
//                    'value' => $setting->bottom_text,
//                    'msg' => $bottom_text_verify['error_msg']
//                ];
//            }
//
//            // 按钮文案
//            $button_text_verify = $service->verify('button_text', $other_setting->button_text);
//            if (!$button_text_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'button_text',
//                    'source_id' => $setting_id,
//                    'value' => $setting->button_text,
//                    'msg' => $button_text_verify['error_msg']
//                ];
//            }
//
//            // 品牌名称
//            $brand_name_verify = $service->verify('brand_name', $setting->brand_name);
//            if (!$brand_name_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'brand_name',
//                    'source_id' => $setting_id,
//                    'value' => $setting->brand_name,
//                    'msg' => $brand_name_verify['error_msg']
//                ];
//            }
//
//            // 首部文案
//            $head_line_verify = $service->verify('head_line', $setting->head_line);
//            if (!$head_line_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'head_line',
//                    'source_id' => $setting_id,
//                    'value' => $setting->head_line,
//                    'msg' => $head_line_verify['error_msg']
//                ];
//            }
//
//            // 标签
//            $label_verify = $service->verify('label', $setting->label);
//            if (!$label_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_SETTING,
//                    'key' => 'label',
//                    'source_id' => $setting_id,
//                    'value' => $setting->label,
//                    'msg' => $label_verify['error_msg']
//                ];
//            }
//        }
//
//        // ------------------------------------其他参数包校验-------------------------------
//
//        // 落地页&文字链接名称
//        foreach ($other_setting->page_map as $account_id => $page_info) {
//            $page_info = json_decode($page_info);
//            if ($page_type_verify = $service->verify('page_type', $page_info->page_type ?? '')) {
//                if (!$page_type_verify['state']) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                        'key' => 'is_xijing',
//                        'value' => 0,
//                        'msg' => $page_type_verify['error_msg']
//                    ];
//                } else {
//                    $link_name_type_verify = $service->verifyPageLink('link_name_type', $other_setting->link_name_type, $page_info->page_type);
//                    if (!$link_name_type_verify['state']) {
//                        $error_msg_list[] = [
//                            'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                            'key' => 'link_name_type',
//                            'value' => $other_setting->link_name_type,
//                            'msg' => $link_name_type_verify['error_msg']
//                        ];
//                    }
//                }
//            }
//        }
//
//        // 小游戏可以不使用落地页 另外校验
//        if ((int)$other_setting->is_xijing === 4) {
//            $link_name_type_verify = $service->verifyPageLink('link_name_type', $other_setting->link_name_type, 'PAGE_TYPE_MINI_GAME_WECHAT');
//            if (!$link_name_type_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                    'key' => 'link_name_type',
//                    'value' => $other_setting->link_name_type,
//                    'msg' => $link_name_type_verify['error_msg']
//                ];
//            }
//        }
//
//        // 朋友圈头像
//        foreach ($other_setting->profile_map as $account_id => $profile_info) {
//            $profile_info = json_decode($profile_info);
//            if ($profile_id_verify = $service->verify('profile_id', $profile_info->profile_id ?? '')) {
//                if (!$profile_id_verify['state']) {
//                    $error_msg_list[] = [
//                        'type' => BatchAD::COMPOSE_ERROR_TYPE_OTHER_SETTING,
//                        'key' => 'profile_map',
//                        'value' => 0,
//                        'msg' => $profile_id_verify['error_msg']
//                    ];
//                }
//            }
//        }
//
//        // ------------------------------------文案校验-------------------------------
//
//        // 判断是哪个字段取的文案包
//        $word_field = 'description';
//        if (in_array($other_setting->template_id, [927, 311, 641, 642, 643, 1465, 1480, 1064, 618, 559, 560, 1529, 720])) {
//            $word_field = 'title';
//        }
//        if (in_array($other_setting->template_id, [721])) {
//            $word_field = 'description';
//            if($other_setting->site_set_none === 'datongtou') {
//                $word_field = 'description';
//            } else {
//                if(in_array('SITE_SET_MOMENTS', $other_setting->site_set)) {
//                    $word_field = 'title';
//                }
//                if(in_array('SITE_SET_WECHAT', $other_setting->site_set)) {
//                    $word_field = 'title';
//                }
//            }
//        }
//        foreach ($this->word_list->getWordContent() as $key => $value) {
//            $word_verify = $service->verify($word_field, $value);
//            if (!$word_verify['state']) {
//                $error_msg_list[] = [
//                    'type' => BatchAD::COMPOSE_ERROR_TYPE_WORD,
//                    'key' => $value,
//                    'value' => 0,
//                    'msg' => $word_verify['error_msg']
//                ];
//            }
//        }
//
//        // ------------------------------------素材校验-------------------------------
//
//        // 视频
//        $video_verify = $service->verifyMaterial('video', $this->material_list->all_video_list);
//        if (!$video_verify['state']) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                'value' => 0,
//                'msg' => $video_verify['error_msg']
//            ];
//        }
//
//        // 图片&组图
//        $image_verify = $service->verifyMaterial('image', $this->material_list->all_image_list);
//        if (!$image_verify['state']) {
//            $error_msg_list[] = [
//                'type' => BatchAD::COMPOSE_ERROR_TYPE_MATERIAL,
//                'value' => 0,
//                'msg' => $image_verify['error_msg']
//            ];
//        }
//
//        return $error_msg_list;
//    }

}
