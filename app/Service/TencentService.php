<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\AndroidUnionChannelPackages\AndroidUnionChannelPackagesModel;
use App\Model\HttpModel\Tencent\AsyncTasks\AsyncTasksModel;
use App\Model\HttpModel\Tencent\CustomAudienceGrantRelations\CustomAudienceGrantModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAccountLogModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\AudienceUploadFileModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Service\MediaServiceInterface\AccountLeader;
use App\Service\MediaServiceInterface\DMP;
use App\Utils\Helpers;
use SplFileObject;
use ZipArchive;

class TencentService implements AccountLeader, DMP
{
    const RECORD_PATH = AudienceService::AUDIENCE_DIR . '/tencent';
    const ERROR_RECORD_SUFFIX = '_reduce_error.txt';

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_EXPORT);
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        return $this->addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, AudienceModel::TYPE_FILE_ADDITION);
    }

    /**
     * 创建数据源文件
     *
     * @param int $table_audience_id
     * @param array $file_list
     * @return array|bool
     * <AUTHOR>
     */
    public function createDataSourceFile($table_audience_id, array $file_list)
    {
        $row_limit = 3000000;
        $file_count = 0;
        $zip_paths = [];
        foreach ($file_list as $file) {
            $filename = $file['name'];
            $user_id_type = $file['data_type'];
            if (!file_exists($filename)) {
                Helpers::getLogger('audience')->warning("file not exists", [
                    'audience_id' => $table_audience_id,
                    'filename' => $filename,
                    'media_type' => MediaType::TENCENT,
                    'media_type_name' => '广点通',
                ]);
                continue;
            }
            $handle = fopen($filename, 'r');
            $zip = new ZipArchive;
            if ($user_id_type == UserIDType::getTencentType(UserIDType::OPEN_ID)) {
                $sub_file_arr = [];
                while (!feof($handle)) {
                    $line = fgets($handle);
                    if (empty($line)) {
                        break;
                    }

                    $explode_data = explode('@', $line);
                    if (count($explode_data) != 2) continue;
                    $app_id = $explode_data[0];
                    $open_id = $explode_data[1];

                    if (!isset($app_id_line_count_map[$app_id])) {
                        $line_row = $app_id_line_count_map[$app_id] = 0;
                    }else {
                        $line_row = $app_id_line_count_map[$app_id];
                    }
                    $shang = intval($line_row / $row_limit);//文件行数限制。商值想下取整得到的值作为文件名的顺序索引
                    $basename = "$table_audience_id-{$app_id}-{$shang}";
                    $sub_file = AudienceService::getAudienceDir(MediaType::TENCENT) . "$basename.txt";
                    if (!isset($sub_file_arr[$app_id])) {
                        $sub_file_arr[$app_id][] = $sub_file;
                    } elseif (!in_array($sub_file, $sub_file_arr[$app_id])) {
                        $sub_file_arr[$app_id][] = $sub_file;
                    }

                    file_put_contents($sub_file, $open_id, FILE_APPEND);
                    $app_id_line_count_map[$app_id]++;
                }

                foreach ($sub_file_arr as $app_id => $sub_file_path_arr) {
                    foreach ($sub_file_path_arr as $sub_file_path) {
                        $basename = "$table_audience_id-{$file_count}";
                        $zip_path = AudienceService::getAudienceDir(MediaType::TENCENT) . "$basename.zip";
                        $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
                        if (true !== $code) {
                            Helpers::getLogger('audience')->error("create data source zip fail", [
                                'table_audience_id' => $table_audience_id,
                                'zip_error_code' => $code,
                                'media_type' => MediaType::TENCENT,
                                'media_type_name' => '广点通',
                            ]);
                            continue;
                        }
                        $zip->addFile($sub_file_path, "$basename.txt");
                        $zip->close();
                        unlink($sub_file_path);
                        $zip_paths[] = [
                            'file' => $zip_path,
                            'user_id_type' => $user_id_type,
                            'app_id' => $app_id
                        ];

                        $file_count++;
                    }
                }

            } else {
                while (!feof($handle)) {
                    $content = Helpers::getFileContent($handle, $row_limit);
                    if (empty($content)) {
                        break;
                    }
                    $basename = "$table_audience_id-{$file_count}";
                    $zip_path = AudienceService::getAudienceDir(MediaType::TENCENT) . "$basename.zip";
                    $sub_file = AudienceService::getAudienceDir(MediaType::TENCENT) . "$basename.txt";
                    file_put_contents($sub_file, $content);
                    $code = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);
                    if (true !== $code) {
                        Helpers::getLogger('audience')->error("create data source zip fail", [
                            'table_audience_id' => $table_audience_id,
                            'zip_error_code' => $code,
                            'media_type' => MediaType::TENCENT,
                            'media_type_name' => '广点通',
                        ]);
                        continue;
                    }
                    $zip->addFile($sub_file, "$basename.txt");
                    $zip->close();
                    unlink($sub_file);
                    $zip_paths[] = [
                        'file' => $zip_path,
                        'user_id_type' => $user_id_type,
                        'app_id' => ''
                    ];

                    $file_count++;
                }
            }
            fclose($handle);
        }

        return $zip_paths;
    }

    /**
     * 添加增量记录
     *
     * @param int $audience_id
     * @param array $zip_path
     * @return void
     */
    public function appendRecord($audience_id, $zip_path)
    {
        $filename = self::RECORD_PATH . '/' . $audience_id . '.txt';
        $fp = fopen($filename, 'ab+');
        foreach ($zip_path as $value) {
            fwrite($fp, serialize($value) . PHP_EOL);
        }
        fclose($fp);
    }

    public function getRecordForPage($audience_id, $page, $rows = 1000)
    {
        $data = [];
        $filename = self::RECORD_PATH . '/' . $audience_id . '.txt';
        if (!file_exists($filename)) {
            return $data;
        }

        $fp = new SplFileObject($filename);
        $fp->seek(($page - 1) * $rows);
        for ($i = 0; $i < $rows && $fp->valid(); ++$i, $fp->next()) {
            $line = $fp->current();
            if (!$line) {
                continue;
            }
            $data[] = unserialize($line);
        }
        return $data;
    }

    public function cleanRecord($audience_id)
    {
        $filename = self::RECORD_PATH . '/' . $audience_id . '.txt';
        $fp = fopen($filename, 'w');
        if (!$fp) {
            return;
        }
        ftruncate($fp, 0);
        fclose($fp);
    }

    public function writeErrorRecord($info)
    {
        $audience_id = $info['audience_id'];
        $list = $info['list'];
        $filename = self::RECORD_PATH . '/' . $audience_id . self::ERROR_RECORD_SUFFIX;
        $fp = fopen($filename, 'wb');

        foreach ($list as $value) {
            fwrite($fp, serialize($value) . PHP_EOL);
        }

        fclose($fp);
    }

    public function getErrorRecordList()
    {
        $matches = [];
        $path = self::RECORD_PATH;
        $suffix = self::ERROR_RECORD_SUFFIX;
        $files = scandir($path);
        $list = [];
        foreach ($files as $filename) {
            $pattern = "/(\S+)?(?={$suffix})/";
            preg_match($pattern, $filename, $matches);

            if (empty($matches)) {
                continue;
            }
            $list[$matches[0]] = [];
            $fullname = self::RECORD_PATH . '/' . $filename;
            $filesize = filesize($fullname);
            if ($filesize == 0) {
                continue;
            }
            $fp = fopen($fullname, 'rb');
            $buffer = fread($fp, $filesize);
            $buffer_list = explode(PHP_EOL, $buffer);
            foreach ($buffer_list as $value) {
                if (!$value) {
                    continue;
                }
                $info = unserialize($value);
                $list[$matches[0]][] = $info;
            }
            fclose($fp);
        }
        return $list;
    }

    public function editPushAccountList($id, $account_ids)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $custom_audience = new CustomAudienceGrantModel();

        $audience_info = $audience_model->getData($id);
        $account_info = $media_model->getDataByAccountId($audience_info->account_id);

        $majordomo_id = $account_info->toutiao_majordomo_id;
        $account_id = $account_info->account_id;
        $access_token = $account_info->access_token;
        if ($majordomo_id === 0) {
            throw new AppException('非BM账户不可添加人群授权');
        }

        $custom_audience->push($account_id, $access_token, [$audience_info->audience_id], $majordomo_id, $account_ids);
        $already_push_account_list = json_decode($audience_info->push_account_list, true);
        try {
            $audience_model->editPushAccountList($id, json_encode(array_merge($already_push_account_list, $account_ids)));
        } catch (\Exception $e) {
            Helpers::getLogger('tencent')->error('save push account list fail', [
                'table_audience_id' => $id,
                'account_ids' => $account_ids,
                'error_message' => $e->getMessage(),
            ]);
        }
        return true;
    }

    public function updateAndroidChannelPackage($platform, $site_id)
    {
        $site_info = (new SiteModel())->getDataByPlatformSiteId($platform, $site_id);

        if ($site_info->game_id !== $site_info->ori_game_id) {
            throw new AppException("切换过游戏，无法更新渠道包");
        }

        if (empty($site_info->app_android_channel_package_id)) {
            throw new AppException("不存在渠道包id");
        }

        $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($platform, MediaType::TENCENT, $site_info->game_id);
        if (empty($game_sdk_info) || $game_sdk_info->account_id == 0) {
            throw new AppException('不存在主线包账户');
        }

        $media_account_info = (new MediaAccountModel())->getDataByAccountId($game_sdk_info->account_id);
        if (empty($media_account_info)) {
            throw new AppException("获取账户信息失败");
        }

        return (new AndroidUnionChannelPackagesModel())->update($media_account_info->account_id, $media_account_info->access_token, $site_info->appid, $site_info->app_android_channel_package_id);
    }


    public function getAndroidChannelPackageTask($platform, $site_id, $task_id)
    {
        $site_info = (new SiteModel())->getDataByPlatformSiteId($platform, $site_id);

        if ($site_info->game_type !== '安卓') {
            throw new AppException("不是安卓游戏");
        }

        if (empty($site_info->app_android_channel_package_id)) {
            throw new AppException("不存在渠道包id");
        }

        $game_sdk_info = (new OdsMediaSDKModel())->getDataByGame($platform, MediaType::TENCENT, $site_info->game_id);
        if (empty($game_sdk_info)) {
            throw new AppException('不存在主线包账户');
        }

        $media_account_info = (new MediaAccountModel())->getDataByAccountId($game_sdk_info->account_id);
        if (empty($media_account_info)) {
            throw new AppException("获取账户信息失败");
        }
        $fields = ['task_name', 'status', 'created_time'];
        $filtering = [[
            'field' => 'task_id',
            'operator' => 'EQUALS',
            'values' => [$task_id]
        ]];

        return (new AsyncTasksModel())->info($media_account_info->account_id, $media_account_info->access_token, $fields, $filtering, 1, 1);
    }

    private function addAudienceByType($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type, $addition_type)
    {
        $audience_model = new AudienceModel();
        $media_model = new MediaAccountModel();
        $table_audience_ids = [];
        $bm_list = [];
        foreach ($companies as $company) {
            foreach ($company['account'] as $account) {
                $media_account_info = $media_model->getDataWithMediaType($account, MediaType::TENCENT);
                $acc_info = [
                    'company' => $company['name'],
                    'platform' => $media_account_info->platform,
                    'account_id' => $media_account_info->account_id
                ];
                $majordomo_id = $media_account_info->toutiao_majordomo_id;
                if (!isset($bm_list[$majordomo_id])) {
                    $bm_list[$majordomo_id] = [
                        'leader' => $acc_info,
                        'acc_ids' => [],
                        'accounts' => []
                    ];
                    continue;
                }
                $bm_list[$majordomo_id]['acc_ids'][] = $account;
                $bm_list[$majordomo_id]['accounts'][] = $acc_info;
            }
        }
        $device_task_info = (new DeviceTaskModel())->getData($device_task_id);

        foreach ($bm_list as $majordomo_id => $item) {
            $leader = $item['leader'];
            $acc_ids = $item['acc_ids'];
            $accounts = $item['accounts'];
            $leaders = [$leader];

            if ($majordomo_id == 0) {
                $leaders = array_merge($leaders, $accounts);
                $push_list = '[]';
            } else {
                $push_list = json_encode($acc_ids, JSON_UNESCAPED_UNICODE);
            }

            foreach ($leaders as $leader) {
                $acc_id = $leader['account_id'];
                $platform = $leader['platform'];
                $company = $leader['company'];

                $table_audience_id = $audience_model->add(
                    $user_id,
                    $username,
                    $device_task_id,
                    MediaType::TENCENT,
                    $company,
                    $acc_id,
                    $audience_name,
                    $audience_desc,
                    $push_list,
                    $addition_type,
                    $majordomo_id,
                    '',
                    $platform,
                    $data_source_type
                );

                $table_audience_ids[] = $table_audience_id;
                // 因Polar MySQL会话读写不一致问题，导致createAudience()读取不到刚刚写入的数据，改用retryErrorAudience()任务异步触发
                ///** @uses \App\Task\TencentTask::createAudience() */
                //Container::getServer()->task(['action' => 'TencentTask.createAudience', 'data' => ['table_audience_id' => $table_audience_id]]);
            }
        }
        return $table_audience_ids;
    }

    public function updateAccountLeader($account_leader, array $account_ids)
    {
        return (new OdsToutiaoAccountLogModel())->updateAccountLeader($account_leader, $account_ids);
    }

}
