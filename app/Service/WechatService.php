<?php
/**
 * 微信管理服务
 * User: zz
 */

namespace App\Service;

use App\Logic\WechatLogic;
use App\Model\HttpModel\Aliyun\DashScope\ParaformerModel;
use App\Model\HttpModel\Wechat\Media\MediaModel;
use App\Model\RedisModel\UserWechatModel;
use App\Model\RedisModel\WechatAccessTokenModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Service\DataBot\DataBotSession;
use App\Utils\Helpers;
use App\Utils\Wechat\WechatMessage;

class WechatService
{
    /**
     * 扫描绑定二维码
     */
    const SCAN_BINDING = 1;

    /**
     * 扫描登录验证二维码
     */
    const SCAN_LOGIN = 2;

    /**
     * 关注事件
     */
    const EVENT_SUBSCRIBE = 'subscribe';

    /**
     * 关注事件
     */
    const EVENT_UNSUBSCRIBE = 'unsubscribe';

    /**
     * 关注事件
     */
    const EVENT_SCAN = 'SCAN';

    /**
     * 自定义菜单：开启新会话
     */
    const EVENT_KEY_CLEAN_SESSION = 'cleanSession';

    /**
     * 数据助手的视频的 media id
     */
    const MEDIA_ID = 'Xsu5QfTxQPdtQuMrmsu7wDCz7MPMpmO7yvZGwfwMkiQ5BoH33mVsfVnW5EcuAZAQ';

    public function handleEvent($data)
    {
        $logger = Helpers::getLogger("wechat_event");
        $logger->info('接收到事件', ['data' => $data]);
        $method = 'handle' . ucfirst(strtolower($data['Event'])) . 'Event';
        if (method_exists($this, $method)) {
            return $this->$method($data);
        } else {
            // 没有处理的事件返回success
            return 'success';
        }

    }

    public function handleText($data)
    {
        $user_model = new UserModel();
        $wechat_redis_model = new UserWechatModel();
        $user_info = $user_model->getDataByOpenId($data['FromUserName']);
        if (!empty($user_info)) {
            file_put_contents('/tmp/test.log', "开始写消息\n", FILE_APPEND);
            // 把消息写进消息队列
            $queue = [
                'user_id'  => $user_info->id,
                'content'  => $data['Content'],
                'msg_type' => 'text',
                'openid'   => $data['FromUserName'],
            ];
            $wechat_redis_model->addUserMessage($queue);
        }

        return 'success';
    }


    public function handleVoice($data)
    {
        $logger = Helpers::getLogger("paraformer");
        $logger->info('处理语音消息');

        $user_model = new UserModel();
        $wechat_redis_model = new UserWechatModel();
        $user_info = $user_model->getDataByOpenId($data['FromUserName']);

        if (empty($user_info)) {
            $logger->info('获取用户信息失败');
            return 'success';
        }

//        // 获取语音链接
//        $wechat_access_token_model = new WechatAccessTokenModel();
//        $access_token_info = $wechat_access_token_model->get('zx_qbj');
//        $access_token = $access_token_info['access_token'] ?? '';
//        $file_url = MediaModel::GET_MEDIA . '?access_token=' . $access_token . "&media_id={$data['MediaId16K']}";
//
//        // 语音转文本
//        $text = (new ParaformerModel())->transcriptionToText($file_url);
//        if ($text === false) {
//            $logger->info('文本转换失败');
//            return WechatMessage::text($data['FromUserName'], $data['ToUserName'], '语音转文本失败，请重试。');
//        }


        // 把消息写进消息队列
        $queue = [
            'user_id'  => $user_info->id,
            'content'  => $data['MediaId16K'],
            'msg_type' => 'voice',
            'openid'   => $data['FromUserName'],
        ];
        $wechat_redis_model->addUserMessage($queue);
        return 'success';
    }

    /**
     * 处理菜单点击事件
     *
     * @param $data
     * @return string
     */
    private function handleClickEvent($data)
    {
        $logger = Helpers::getLogger("wechat_click");
        $logger->info('接收到点击事件', ['data' => $data]);
        if (isset($data['EventKey']) && $data['EventKey'] == self::EVENT_KEY_CLEAN_SESSION) {
            $user_model = new UserModel();
            $user_info = $user_model->getDataByOpenId($data['FromUserName']);

            if (empty($user_info)) {
                $logger->info('获取用户信息失败');
                return WechatMessage::text($data['FromUserName'], $data['ToUserName'], '获取用户信息失败，请先绑定后台账号');
            }

            // 清除会话消息
            $session = new DataBotSession($user_info->id, 0);
            $session->cleanSession();

            return WechatMessage::text($data['FromUserName'], $data['ToUserName'], '新会话开启成功');
        }

        return WechatMessage::text($data['FromUserName'], $data['ToUserName'], '新会话开启异常');
    }

    private function handleSubscribeEvent($data)
    {
        if (isset($data['EventKey'])) {
            $delete_qrscene = str_replace('qrscene_', '', $data['EventKey']);
            $data['EventKey'] = $delete_qrscene;
            $response_xml = $this->handleScanEvent($data);
        } else {
            $response_xml = WechatMessage::text($data['FromUserName'], $data['ToUserName'], '欢迎关注情报局');
        }

        return $response_xml;
    }

    private function handleUnsubscribeEvent($data)
    {
        if ($this->unbindOpenID($data['FromUserName'])) {
            $response_xml = WechatMessage::text($data['FromUserName'], $data['ToUserName'], '解除绑定成功，再见啦');
        } else {
            $response_xml = WechatMessage::text($data['FromUserName'], $data['ToUserName'], '再见来不及挥手');
        }
        return $response_xml;
    }

    private function handleScanEvent($data)
    {
        $json_data = json_decode($data['EventKey'], true);
        switch ($json_data['st']) {
            case self::SCAN_BINDING:
                if ($this->bindOpenID($json_data['uid'], $data['FromUserName'])) {
                    // 要回复视频消息
                    $response_xml = WechatMessage::video($data['FromUserName'], $data['ToUserName'], self::MEDIA_ID);
                } else {
                    $response_xml = WechatMessage::text($data['FromUserName'], $data['ToUserName'], '绑定失败，请再扫一次呗');
                }
                break;
            case self::SCAN_LOGIN:
                $info = $this->loginDmsByScan($json_data['uid'], $data['FromUserName']);
                $response_xml = WechatMessage::text($data['FromUserName'], $data['ToUserName'], $info['message']);
                break;
            default:
                $response_xml = WechatMessage::text($data['FromUserName'], $data['ToUserName'], '无效扫码，请通过官方后台扫码关注');
        }
        return $response_xml;
    }

    private function bindOpenID($user_id, $open_id)
    {
        $user_model = new UserModel();
        try {
            // 解绑原来的open_id
            $user_model->cleanOpenID($open_id);
            $user_model->updateOpenID($user_id, $open_id);
            return true;
        } catch (\Exception $e) {
            Helpers::getLogger('wechat')->error('bind fail', [
                'user_id'       => $user_id,
                'open_id'       => $open_id,
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
    }

    private function unbindOpenID($open_id)
    {
        $user_model = new UserModel();
        try {
            $user_model->cleanOpenID($open_id);
            return true;
        } catch (\Exception $e) {
            Helpers::getLogger('wechat')->error('unbind fail', [
                'open_id'       => $open_id,
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
    }

    private function loginDmsByScan($user_id, $open_id)
    {
        $user_wechat_redis_model = new UserWechatModel();
        $user_model = new UserModel();
        $user_info = $user_model->getData($user_id);
        if (!empty($user_info) && $user_info->open_id == $open_id) {
            // 判断一下是否是泽达情报局的open_id，如果是，则不给登录
            if (in_array($user_info->open_id, WechatLogic::OPEN_ID)) {
                $info = ['state' => UserWechatModel::WECHAT_LOGIN_FAIL, 'message' => '泽达情报局已弃用，相关功能迁移至中旭情报局，请通过后台个人中心重新绑定微信'];
            } else {
                $info = ['state' => UserWechatModel::WECHAT_LOGIN_SUCCESS, 'message' => '登录成功'];
            }

        } else {
            $info = ['state' => UserWechatModel::WECHAT_LOGIN_FAIL, 'message' => '登录微信和绑定微信不一致，登录失败'];
        }

        $user_wechat_redis_model->setInfo($user_id, $info);

        return $info;
    }

    private function handleTemplatesendjobfinishEvent($data)
    {
        $status = empty($data['Status']) ? 'success' : $data['Status'];
        if ($status === 'success') {
            return 'success';
        }

        $mp_id = $data['ToUserName'] ?? '';
        $open_id = $data['FromUserName'] ?? '';
        $create_time = $data['CreateTime'] ?? '';
        $msg_id = $data['MsgID'] ?? '';
        Helpers::getLogger('wechat')->error('send template message callback', [
            'mp'          => $mp_id,
            'open_id'     => $open_id,
            'create_time' => $create_time,
            'msg_id'      => $msg_id,
            'status'      => $status
        ]);

        return WechatMessage::text($data['FromUserName'], $data['ToUserName'], '推送消息错误，请联系管理员。');
    }
}
