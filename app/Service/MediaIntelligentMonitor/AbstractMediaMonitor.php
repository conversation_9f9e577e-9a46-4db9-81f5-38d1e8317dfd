<?php

namespace App\Service\MediaIntelligentMonitor;

use App\Exception\AppException;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorBindModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorRobotModel;
use App\Param\ADServing\ADIntelligentMonitorBindParam;
use App\Param\ADServing\ADIntelligentMonitorRobotLogParam;

abstract class AbstractMediaMonitor
{
    public $dim_value_map = [];

    public $dim_value_platform = '';

    protected $level_target_map;

    /**
     * @var ADIntelligentMonitorRobotLogParam[] $log_param_map
     */
    public $log_param_map;

    public $already_invalid_action_list = [];

    public $already_effective_action_list = [];

    public function getDimValueMap()
    {
        return $this->dim_value_map;
    }

    public function getDimAccountList()
    {
        return array_values(array_unique(array_map(function ($item) {
            return $item['account'];
        }, $this->dim_value_map)));
    }

    public function getDateRange($time_range, $time_range_type, $time_unit): array
    {
        switch ($time_range_type) {
            case 'ago':
                if ($time_unit == 'day') {
                    return [date('Y-m-d 00:00:00', strtotime("-$time_range days")), date('Y-m-d 23:59:59', strtotime("-1 days"))];
                } else {
                    return [date('Y-m-d H:i:s', strtotime("-$time_range hours")), date('Y-m-d H:i:s', strtotime("-1 hours"))];
                }
            case 'recently':
                if ($time_unit == 'day') {
                    return [date('Y-m-d 00:00:00', strtotime("-$time_range days")), date('Y-m-d 23:59:59')];
                } else {
                    return [date('Y-m-d H:i:s', strtotime("-$time_range hours")), date('Y-m-d H:i:s')];
                }
        }
        throw new AppException('获取时间范围传参有误');
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $condition_body
     * @return bool[]
     */
    public function execConditionBody(array $bind_target_param_list, $condition_body, $exec_body_name): array
    {
        $condition_group_result_map = [];
        $last_result_map = [];

        $condition_group_relation = $condition_body['condition_group_relation'];
        $condition_group_list = $condition_body['condition_group_list'];

        foreach ($condition_group_list as $key => $condition_group) {

            // 过滤已知执行事件类型的绑定记录
            $bind_target_param_list = array_values(array_filter($bind_target_param_list, function ($ele) use ($last_result_map) {
                return !isset($last_result_map[$ele->target_value]);
            }));

            if (!$bind_target_param_list) {
                break;
            }

            foreach ($bind_target_param_list as $bind_target_param) {
                $this->log_param_map[$bind_target_param->target_value]->addConditionLogicGroup($exec_body_name, $key + 1);
            }

            $target_result_map = $this->executeConditionGroup($bind_target_param_list, $condition_group);

            // 过滤没有查出来信息的绑定记录
            $bind_target_param_list = array_filter($bind_target_param_list, function ($ele) use ($target_result_map) {
                return isset($target_result_map[$ele->target_value]);
            });

            foreach ($bind_target_param_list as $bind_target_param) {
                if (!isset($condition_group_result_map[$bind_target_param->target_value])) {
                    $condition_group_result_map[$bind_target_param->target_value] = (bool)$target_result_map[$bind_target_param->target_value];
                    if ($condition_group_relation == 'and') {
                        if (!$condition_group_result_map[$bind_target_param->target_value]) {
                            $last_result_map[$bind_target_param->target_value] = false;
                        }
                    } else {
                        if ($condition_group_result_map[$bind_target_param->target_value]) {
                            $last_result_map[$bind_target_param->target_value] = true;
                        }
                    }
                } else if ($condition_group_relation == 'and') {
                    $condition_group_result_map[$bind_target_param->target_value] = ($condition_group_result_map[$bind_target_param->target_value] && ((bool)$target_result_map[$bind_target_param->target_value]));
                    if (!$condition_group_result_map[$bind_target_param->target_value]) {
                        $last_result_map[$bind_target_param->target_value] = false;
                    }
                } else {
                    $condition_group_result_map[$bind_target_param->target_value] = ($condition_group_result_map[$bind_target_param->target_value] || ((bool)$target_result_map[$bind_target_param->target_value]));
                    if ($condition_group_result_map[$bind_target_param->target_value]) {
                        $last_result_map[$bind_target_param->target_value] = true;
                    }
                }
            }
        }

        foreach ($condition_group_result_map as $target_value => $last_condition_group_result) {
            if (!isset($last_result_map[$target_value])) {
                $last_result_map[$target_value] = $last_condition_group_result;
            }
            $this->log_param_map[$target_value]->condition_result = $last_result_map[$target_value];
        }

        return $last_result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $condition_group
     * @return bool[]
     */
    protected function executeConditionGroup(array $bind_target_param_list, $condition_group): array
    {
        $condition_result_map = [];
        $last_result_map = [];

        $condition_list = $condition_group['condition_list'];
        $condition_relation = $condition_group['condition_relation'];

        foreach ($condition_list as $target) {
            // 过滤已知执行事件类型的绑定记录
            $bind_target_param_list = array_values(array_filter($bind_target_param_list, function ($ele) use ($last_result_map) {
                return !isset($last_result_map[$ele->target_value]);
            }));

            if (!$bind_target_param_list) {
                break;
            }

            $target_result_map = $this->executeCondition($bind_target_param_list, $target);

            // 过滤没有查出来信息的绑定记录
            $bind_target_param_list = array_filter($bind_target_param_list, function ($ele) use ($target_result_map) {
                return isset($target_result_map[$ele->target_value]);
            });

            foreach ($bind_target_param_list as $bind_target_param) {
                if (!isset($condition_result_map[$bind_target_param->target_value])) {
                    $condition_result_map[$bind_target_param->target_value] = (bool)$target_result_map[$bind_target_param->target_value];
                    if ($condition_relation == 'and') {
                        if (!$condition_result_map[$bind_target_param->target_value]) {
                            $last_result_map[$bind_target_param->target_value] = false;
                        }
                    } else {
                        if ($condition_result_map[$bind_target_param->target_value]) {
                            $last_result_map[$bind_target_param->target_value] = true;
                        }
                    }
                } else if ($condition_relation == 'and') {
                    $condition_result_map[$bind_target_param->target_value] = ($condition_result_map[$bind_target_param->target_value] && ((bool)$target_result_map[$bind_target_param->target_value]));
                    if (!$condition_result_map[$bind_target_param->target_value]) {
                        $last_result_map[$bind_target_param->target_value] = false;
                    }
                } else {
                    $condition_result_map[$bind_target_param->target_value] = ($condition_result_map[$bind_target_param->target_value] || ((bool)$target_result_map[$bind_target_param->target_value]));
                    if ($condition_result_map[$bind_target_param->target_value]) {
                        $last_result_map[$bind_target_param->target_value] = true;
                    }
                }
            }
        }

        foreach ($condition_result_map as $target_value => $last_condition_result) {
            if (!isset($last_result_map[$target_value])) {
                $last_result_map[$target_value] = $last_condition_result;
            }
        }

        return $last_result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @return mixed
     */
    abstract function initDimValueMap(array $bind_target_param_list);

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return bool|null
     */
    abstract function executeCondition(array $bind_target_param_list, $target): array;

    abstract function execAction(ADIntelligentMonitorBindParam $bind_target_param, $action);

    public function bind(string $ad_level, $target_id, $robot_id, $creator)
    {
        $target_type = $this->level_target_map[$ad_level] ?? false;
        if (!$target_type) {
            throw new AppException('非法的ad_level');
        }

        $robot_info = (new ADIntelligentMonitorRobotModel())->getDataById($robot_id);

        if (!$robot_info) {
            throw new AppException('错误的机器人');
        }

        $bind_param = new ADIntelligentMonitorBindParam([
            'media_type' => $robot_info->media_type,
            'media_agent_type' => $robot_info->media_agent_type,
            'target_type' => $target_type,
            'target_value' => $target_id,
            'monitor_robot_id' => $robot_id,
            'first_exec_time' => date("Y-m-d H:i:s", strtotime("+$robot_info->delay_exec_hour hours")),
            'creator' => $creator
        ]);

        (new ADIntelligentMonitorBindModel())->bind($bind_param);
    }
}
