<?php

namespace App\Service\MediaIntelligentMonitor;


use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Advertiser\AdvertiserModel;
use App\Model\HttpModel\Toutiao\V2\Material\MaterialModel;
use App\Model\HttpModel\Toutiao\V2\Project\ProjectModel;
use App\Model\HttpModel\Toutiao\V2\Promotion\PromotionModel;
use App\Model\SqlModel\DataMedia\ADIntelligentMonitorToutiaoV2Model;
use App\Model\SqlModel\DataMedia\OdsToutiaoAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoADHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCampaignLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCreativeLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\ADIntelligentMonitorBindParam;
use Illuminate\Support\Collection;
use Throwable;

class IntelligentMonitorToutiaoV2 extends AbstractMediaMonitor
{
    protected $level_target_map = [
        'ad1' => 'ad1',
        'ad2' => 'ad2',
        'ad3' => 'ad2',
        'ad4' => 'ad2',
    ];

    function isRestartAction($error_msg): bool
    {
        $restart_like_msg_list = [
            'Too many requests',
            'too many requests',
            '系统繁忙，请稍后再试',
            '请稍后刷新重试',
            'internal error',
            'Internal Error',
            'internal service error',
            'Internal service timed out',
            '502 Bad Gateway',
            '504 Gateway Time-out',
            'upstream connect error or disconnect',
            '请稍后重试',
            'System timeout',
        ];

        foreach ($restart_like_msg_list as $msg) {
            if (strpos($error_msg, $msg) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return bool[]
     */
    function executeCondition(array $bind_target_param_list, $target): array
    {
        $result = [];
        switch ($target['condition_target_dim']) {
            case 'account':
                $result = $this->conditionAccount($bind_target_param_list, $target);
                break;
            case 'ad1':
                $result = $this->conditionAd1($bind_target_param_list, $target);
                break;
            case 'ad2':
                $result = $this->conditionAd2($bind_target_param_list, $target);
                break;
            case 'ad3':
                $result = $this->conditionAd3($bind_target_param_list, $target);
                break;
            default:
                throw new AppException('错误的条件数据维度' . $target['condition_target_dim']);
        }

        $data_map = $result['data'];

        $condition_result_map = [];
        foreach ($data_map as $target_value => $data) {
            $condition_result = $data && ($data->where('result', '>', 0)->count());
            $this->log_param_map[$target_value]->addConditionLogicResult(
                $target['condition_target'],
                $target['condition_target_action'],
                $target['condition_target_value'],
                $data ? $data->pluck($target['condition_target']) : [],
                $result['sql'],
                $condition_result
            );
            $condition_result_map[$target_value] = $condition_result;
        }

        return $condition_result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    private function conditionAccount(array $bind_target_param_list, $target)
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            $copy_bind_target_param->target_type = 'account';
            $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['account'];
            $copy_bind_target_param_list[] = $copy_bind_target_param;
        }

        $result = (new ADIntelligentMonitorToutiaoV2Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );

        $result_map = [
            'data' => [],
            'sql' => $result['sql'],
        ];

        foreach ($bind_target_param_list as $bind_target_param) {
            $result_map['data'][$bind_target_param->target_value] = $result['data'][$this->dim_value_map[$bind_target_param->target_value]['account']];
        }

        return $result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    private function conditionAd1(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            switch ($bind_target_param->target_type) {
                case 'ad3':
                case 'ad2':
                case 'ad1':
                    $copy_bind_target_param->target_type = 'ad1';
                    $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['ad1'];
                    break;
            }
            $copy_bind_target_param_list[] = $copy_bind_target_param;
        }

        $result = (new ADIntelligentMonitorToutiaoV2Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );

        $result_map = [
            'data' => [],
            'sql' => $result['sql'],
        ];

        foreach ($bind_target_param_list as $bind_target_param) {
            $result_map['data'][$bind_target_param->target_value] = $result['data'][$this->dim_value_map[$bind_target_param->target_value]['ad1']];
        }

        return $result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    private function conditionAd2(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            switch ($bind_target_param->target_type) {
                case 'ad3':
                case 'ad2':
                    $copy_bind_target_param->target_type = 'ad2';
                    $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['ad2'];
                    break;
            }
            $copy_bind_target_param_list[] = $copy_bind_target_param;
        }

        return (new ADIntelligentMonitorToutiaoV2Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    private function conditionAd3(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        return (new ADIntelligentMonitorToutiaoV2Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );
    }

    /**
     * @throws Throwable
     */
    public function execAction(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        switch ($bind_target_param->target_type) {
            case 'ad1':
                $this->actionAd1($bind_target_param, $action);
                break;
            case 'ad2':
                $this->actionAd2($bind_target_param, $action);
                break;
            case 'ad3':
                $this->actionAd3($bind_target_param, $action);
                break;
            case 'account':
                $this->actionAccount($bind_target_param, $action);
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd1(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        $modify_time = date('Y-m-d H:i:s');

        switch ($action['target']) {
            case 'budget':
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);
                try {
                    (new ProjectModel())->updateBudget([
                        'advertiser_id' => $this->dim_value_map[$bind_target_param->target_value]['account'],
                        'data' => [
                            [
                                'project_id ' => $this->dim_value_map[$bind_target_param->target_value]['ad1'],
                                'budget_mode' => 'BUDGET_MODE_DAY',
                                'budget' => intval($action['target_value']),
                            ]
                        ]
                    ], $access_token_info->access_token);
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd1($bind_target_param, $action);
                    } else {
                        throw $e;
                    }
                }
                break;
            case 'opt_status':
                $info = (new odsToutiaoCampaignLogModel())->getDataByCampaignId($this->dim_value_map[$bind_target_param->target_value]['ad1']);
                if (!$info) {
                    throw new AppException('找不到opt_status数据');
                }
                if ($info->status == "PROJECT_STATUS_{$action['target_value']}") {
                    $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                    $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是PROJECT_STATUS_{$action['target_value']};");
                    return;
                }
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);

                try {
                    (new ProjectModel())->updateProjectStatus([(int)$this->dim_value_map[$bind_target_param->target_value]['ad1']], $action['target_value'], $this->dim_value_map[$bind_target_param->target_value]['account'], $access_token_info->access_token);
                    (new OdsToutiaoCampaignHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['ad1']],
                        'status',
                        "CAMPAIGN_STATUS_{$action['target_value']}",
                        $modify_time
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd1($bind_target_param, $action);
                    } else {
                        throw $e;
                    }
                }

                break;
            case 'delete':
                $info = (new odsToutiaoCampaignLogModel())->getDataByCampaignId($this->dim_value_map[$bind_target_param->target_value]['ad1']);
                if (!$info) {
                    throw new AppException('找不到status数据');
                }
                if ($info->status == 'PROJECT_STATUS_DELETE') {
                    $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                    $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是PROJECT_STATUS_DELETE;");
                    return;
                }

                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);

                try {
                    (new ProjectModel())->deleteProject([(int)$this->dim_value_map[$bind_target_param->target_value]['ad1']], $this->dim_value_map[$bind_target_param->target_value]['account'], $access_token_info->access_token);
                    (new OdsToutiaoCampaignHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['ad1']],
                        'status',
                        "CAMPAIGN_STATUS_DELETE",
                        $modify_time
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd1($bind_target_param, $action);
                    } else {
                        throw $e;
                    }
                }

                break;
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd2(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        $modify_time = date('Y-m-d H:i:s');

        switch ($action['target']) {
            case 'opt_status':
                $info = (new odsToutiaoAdLogModel())->getDataByAdId($this->dim_value_map[$bind_target_param->target_value]['ad2']);
                if (!$info) {
                    throw new AppException('找不到opt_status数据');
                }
                if ($info->opt_status == "AD_STATUS_{$action['target_value']}") {
                    $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                    $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是AD_STATUS_{$action['target_value']};");
                    return;
                }
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);

                try {
                    (new PromotionModel())->updatePromotionStatus([(int)$this->dim_value_map[$bind_target_param->target_value]['ad2']], $action['target_value'], $this->dim_value_map[$bind_target_param->target_value]['account'], $access_token_info->access_token);
                    (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['ad2']],
                        'opt_status',
                        "AD_STATUS_{$action['target_value']}",
                        $modify_time
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd2($bind_target_param, $action);
                    } else {
                        throw $e;
                    }

                }

                break;
            case 'delete':
                $info = (new odsToutiaoAdLogModel())->getDataByAdId($this->dim_value_map[$bind_target_param->target_value]['ad2']);
                if (!$info) {
                    throw new AppException('找不到status数据');
                }
                if ($info->status == 'AD_STATUS_DELETE') {
                    $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                    $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是AD_STATUS_DELETE;");
                    return;
                }
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);

                try {
                    (new PromotionModel())->deletePromotion([(int)$this->dim_value_map[$bind_target_param->target_value]['ad2']], $this->dim_value_map[$bind_target_param->target_value]['account'], $access_token_info->access_token);
                    (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['ad2']],
                        'opt_status',
                        "AD_STATUS_DELETE",
                        $modify_time
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd2($bind_target_param, $action);
                    } else {
                        throw $e;
                    }
                }

                break;
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd3(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        switch ($action['target']) {
            case 'opt_status':
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);

                try {
                    (new MaterialModel())->updateMaterialStatus(
                        (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        (int)$this->dim_value_map[$bind_target_param->target_value]['ad2'],
                        [
                            [
                                'material_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad3_name'],
                                'opt_status' => $action['target_value']
                            ],
                        ],
                        $access_token_info->access_token
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd3($bind_target_param, $action);
                    } else {
                        throw $e;
                    }

                }
                break;
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAccount(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        switch ($action['target']) {
            case 'budget':
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TOUTIAO);
                try {
                    (new AdvertiserModel())->updateAdvertiserBudget([
                        'advertiser_id' => $this->dim_value_map[$bind_target_param->target_value]['account'],
                        'budget_mode' => 'BUDGET_MODE_DAY',
                        'budget' => intval($action['target_value']),
                    ], $access_token_info->access_token);
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAccount($bind_target_param, $action);
                    } else {
                        throw $e;
                    }
                }
                break;
        }
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @return mixed
     */
    public function initDimValueMap(array $bind_target_param_list)
    {
        $target_type = $bind_target_param_list[0]->target_type;
        switch ($target_type) {
            case 'ad1':
                $campaign_log_result = (new OdsToutiaoCampaignLogModel())->builder
                    ->whereIn('campaign_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('campaign_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($campaign_log_result[$bind_target_param->target_value]->campaign_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $campaign_log_result[$bind_target_param->target_value]->account_id,
                            'ad1' => $campaign_log_result[$bind_target_param->target_value]->campaign_id,
                            'ad1_name' => $campaign_log_result[$bind_target_param->target_value]->campaign_name,
                            'ad2' => null,
                            'ad2_name' => '',
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $campaign_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'ad2':
                $ad_log_result = (new OdsToutiaoAdLogModel())->builder
                    ->select(['ods_toutiao_ad_log.platform', 'ods_toutiao_ad_log.account_id', 'ods_toutiao_ad_log.ad_id', 'ods_toutiao_ad_log.ad_name', 'ods_toutiao_ad_log.campaign_id', 'ods_toutiao_campaign_log.campaign_name'])
                    ->leftJoin('ods_toutiao_campaign_log', 'ods_toutiao_campaign_log.campaign_id', '=', 'ods_toutiao_ad_log.campaign_id')
                    ->whereIn('ad_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('ad_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($ad_log_result[$bind_target_param->target_value]->ad_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $ad_log_result[$bind_target_param->target_value]->account_id,
                            'ad1' => $ad_log_result[$bind_target_param->target_value]->campaign_id,
                            'ad1_name' => $ad_log_result[$bind_target_param->target_value]->campaign_name,
                            'ad2' => $ad_log_result[$bind_target_param->target_value]->ad_id,
                            'ad2_name' => $ad_log_result[$bind_target_param->target_value]->ad_name,
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $ad_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'ad3':
                $creative_log_result = (new OdsToutiaoCreativeLogModel())->builder
                    ->select([
                        'ods_toutiao_ad_log.platform',
                        'ods_toutiao_ad_log.account_id',
                        'ods_toutiao_ad_log.ad_id',
                        'ods_toutiao_ad_log.ad_name',
                        'ods_toutiao_ad_log.campaign_id',
                        'ods_toutiao_campaign_log.campaign_name',
                        'ods_toutiao_creative_log.creative_id',
                        'ods_toutiao_creative_log.material_id',
                    ])
                    ->leftJoin('ods_toutiao_ad_log', 'ods_toutiao_ad_log.ad_id', '=', 'ods_toutiao_creative_log.ad_id')
                    ->leftJoin('ods_toutiao_campaign_log', 'ods_toutiao_campaign_log.campaign_id', '=', 'ods_toutiao_ad_log.campaign_id')
                    ->whereIn('ods_toutiao_creative_log.creative_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('creative_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($creative_log_result[$bind_target_param->target_value]->ad_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $creative_log_result[$bind_target_param->target_value]->account_id,
                            'ad1' => $creative_log_result[$bind_target_param->target_value]->campaign_id,
                            'ad1_name' => $creative_log_result[$bind_target_param->target_value]->campaign_name,
                            'ad2' => $creative_log_result[$bind_target_param->target_value]->ad_id,
                            'ad2_name' => $creative_log_result[$bind_target_param->target_value]->ad_name,
                            'ad3' => $creative_log_result[$bind_target_param->target_value]->creative_id,
                            'ad3_name' => $creative_log_result[$bind_target_param->target_value]->material_id,
                        ];
                        $this->dim_value_platform = $creative_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'account':
                $account_result = (new OdsToutiaoAccountLogModel())->builder
                    ->selectRaw('account_id')
                    ->selectRaw('platform')
                    ->whereIn('account_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('account_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($account_result[$bind_target_param->target_value]->account_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $account_result[$bind_target_param->target_value]->account_id,
                            'ad1' => null,
                            'ad1_name' => '',
                            'ad2' => null,
                            'ad2_name' => '',
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $account_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }
    }
}
