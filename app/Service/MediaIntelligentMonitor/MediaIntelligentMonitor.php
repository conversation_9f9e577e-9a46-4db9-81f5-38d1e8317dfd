<?php

namespace App\Service\MediaIntelligentMonitor;

use App\Constant\BatchADClassMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Utils\Helpers;

class MediaIntelligentMonitor
{
    static public function create($media_type, $media_agent_type): AbstractMediaMonitor
    {
        $media_name = Helpers::pascal(ucfirst(strtolower(MediaType::CONST_NAME_LIST[$media_type])));

        $media_service_class = __NAMESPACE__ . '\\IntelligentMonitor' . self::fillNamespace($media_type, ucfirst($media_name), $media_agent_type);
        if (class_exists($media_service_class)) {
            return new $media_service_class();
        } else {
            throw new AppException("错误的MediaIntelligentMonitor类名,$media_service_class");
        }
    }

    static private function fillNamespace(int $media_type, string $class_name, int $media_agent_type = 0)
    {
        $suffix = BatchADClassMap::SERVICE_MAP[$media_type][$media_agent_type];

        return "$class_name$suffix";
    }
}
