<?php

namespace App\Service\MediaIntelligentMonitor;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\Advertiser\AdvertiserModel;
use App\Model\HttpModel\Tencent\V3\AdGroup\AdGroupModel;
use App\Model\HttpModel\Tencent\V3\DynamicCreatives\DynamicCreativeModel;
use App\Model\SqlModel\DataMedia\ADIntelligentMonitorTencentV3Model;
use App\Model\SqlModel\DataMedia\OdsTencentAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentCampaignLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\ADIntelligentMonitorBindParam;
use Illuminate\Support\Collection;
use Throwable;

class IntelligentMonitorTencentV3 extends AbstractMediaMonitor
{
    protected $level_target_map = [
        'ad1' => 'ad1',
        'ad2' => 'ad1',
        'ad3' => 'ad3',
        'ad4' => 'ad3',
    ];

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    public function executeCondition(array $bind_target_param_list, $target): array
    {
        switch ($target['condition_target_dim']) {
            case 'account':
                $result = $this->conditionAccount($bind_target_param_list, $target);
                break;
            case 'ad1':
                $result = $this->conditionAd1($bind_target_param_list, $target);
                break;
            case 'ad3':
                $result = $this->conditionAd3($bind_target_param_list, $target);
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }

        $data_map = $result['data'];

        $condition_result_map = [];
        foreach ($data_map as $target_value => $data) {
            $condition_result = $data && ($data->where('result', '>', 0)->count());
            $this->log_param_map[$target_value]->addConditionLogicResult(
                $target['condition_target'],
                $target['condition_target_action'],
                $target['condition_target_value'],
                $data ? $data->pluck($target['condition_target']) : [],
                $result['sql'],
                $condition_result
            );
            $condition_result_map[$target_value] = $condition_result;
        }

        return $condition_result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return bool
     */
    private function conditionAccount(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            $copy_bind_target_param->target_type = 'account';
            $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['account'];
            $copy_bind_target_param_list[] = $copy_bind_target_param;
        }

        $result = (new ADIntelligentMonitorTencentV3Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );

        $result_map = [
            'data' => [],
            'sql' => $result['sql'],
        ];

        foreach ($bind_target_param_list as $bind_target_param) {
            $result_map['data'][$bind_target_param->target_value] = $result['data'][$this->dim_value_map[$bind_target_param->target_value]['account']];
        }

        return $result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    private function conditionAd1(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            switch ($bind_target_param->target_type) {
                case 'ad3':
                case 'ad1':
                    $copy_bind_target_param->target_type = 'ad1';
                    $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['ad1'];
                    break;
            }
            if (!empty($this->dim_value_map[$bind_target_param->target_value]['ad1'])) {
                $copy_bind_target_param_list[] = $copy_bind_target_param;
            } else {
                echo date('Y-m-d H:i:s') . '空数据' . json_encode($this->dim_value_map, JSON_UNESCAPED_UNICODE) . '需要:' . $bind_target_param->target_value . PHP_EOL;
            }
        }

        $result = (new ADIntelligentMonitorTencentV3Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );

        $result_map = [
            'data' => [],
            'sql' => $result['sql'],
        ];

        foreach ($bind_target_param_list as $bind_target_param) {
            $result_map['data'][$bind_target_param->target_value] = $result['data'][$this->dim_value_map[$bind_target_param->target_value]['ad1']];
        }

        return $result_map;
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @param $target
     * @return array
     */
    private function conditionAd3(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        return (new ADIntelligentMonitorTencentV3Model())->getConditionSqlData(
            $target,
            $condition_time_range,
            $bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );
    }


    /**
     * @throws Throwable
     */
    public function execAction(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        switch ($bind_target_param->target_type) {
            case 'account':
                $this->actionAccount($bind_target_param, $action);
                break;
            case 'ad1':
                $this->actionAd1($bind_target_param, $action);
                break;
            case 'ad3':
                $this->actionAd3($bind_target_param, $action);
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }
    }

    private function actionAccount(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        try {
            switch ($action['target']) {
                case 'daily_budget':
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);
                    (new AdvertiserModel())->update(
                        [
                            'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                            'daily_budget' => intval($action['target_value'] * 100),
                        ],
                        $access_token_info->access_token
                    );
                    break;
                default:
                    throw new AppException('错误的执行指标');
            }
        } catch (Throwable $e) {
            if ($this->isRestartAction($e->getMessage())) {
                sleep(5);
                $this->actionAccount($bind_target_param, $action);
            }
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd1(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        $modify_time = date('Y-m-d H:i:s');

        try {
            switch ($action['target']) {
                case 'daily_budget':
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);

                    $request_data = [
                        'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        'adgroup_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'],
                        'daily_budget' => intval($action['target_value'] * 100)
                    ];

                    (new AdGroupModel())->update($request_data, $access_token_info->access_token);
                    break;
                case 'begin_date':
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);

                    $adgroup_model = new AdGroupModel();

                    $result = $adgroup_model->info(
                        $this->dim_value_map[$bind_target_param->target_value]['account'],
                        $access_token_info->access_token,
                        [
                            [
                                'field' => 'adgroup_id',
                                'operator' => 'EQUALS',
                                'values' => [$this->dim_value_map[$bind_target_param->target_value]['ad1']],
                            ]
                        ],
                        ['adgroup_id', 'adgroup_name', 'begin_date', 'end_date']
                    );

                    if (count($result['list'] ?? []) > 0) {

                        $begin_date = $result['list'][0]['begin_date'] ?? '';
                        $end_date = $result['list'][0]['end_date'] ?? '';
                        $today_date = date('Y-m-d');

                        if ($begin_date && $end_date) {

                            $delay_foundation = $begin_date;

                            if (strtotime($begin_date) <= strtotime($today_date)) {
                                $delay_foundation = $today_date;
                            }

                            $new_begin_date = date("Y-m-d", strtotime("$delay_foundation +{$action['target_value']} days"));

                            $request_data = [
                                'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                                'adgroup_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'],
                                'begin_date' => $new_begin_date,
                            ];

                            if (strtotime($new_begin_date) >= strtotime($end_date)) {
                                $request_data['end_date'] = date("Y-m-d", strtotime("$delay_foundation +1 year"));
                            }

                            $adgroup_model->update($request_data, $access_token_info->access_token);
                        } else {
                            throw new AppException('查询广告组信息无返回 begin_date 与 end_date ');
                        }
                    } else {
                        throw new AppException('查询广告组信息失败');
                    }

                    break;
                case 'delete':
                    $info = (new odsTencentAdGroupLogModel())->getDataByAdGroupId($this->dim_value_map[$bind_target_param->target_value]['ad1']);
                    if (!$info) {
                        throw new AppException('找不到system_status数据');
                    }
                    if ($info->system_status == 'AD_GROUP_STATUS_DELETED') {
                        $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                        $this->log_param_map[$bind_target_param->target_value]->msg .= "无效操作-原状态就是AD_GROUP_STATUS_DELETED;";
                        return;
                    }
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);
                    (new AdGroupModel())->delete([
                        'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        'adgroup_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'],
                    ], $access_token_info->access_token);

                    (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['account'] => [$this->dim_value_map[$bind_target_param->target_value]['ad1']]],
                        'system_status',
                        "AD_GROUP_STATUS_DELETED",
                        $modify_time
                    );

                    break;
                case 'configured_status':
                    $info = (new odsTencentCampaignLogModel())->getDataByCampaignId($this->dim_value_map[$bind_target_param->target_value]['ad1']);
                    if (!$info) {
                        throw new AppException('找不到configured_status数据');
                    }
                    if ($info->configured_status == "AD_STATUS_{$action['target_value']}") {
                        $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                        $this->log_param_map[$bind_target_param->target_value]->msg .= "无效操作-原状态就是AD_STATUS_{$action['target_value']};";
                        return;
                    }
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);

                    (new AdGroupModel())->update([
                        'configured_status' => "AD_STATUS_{$action['target_value']}",
                        'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        'adgroup_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'],
                    ], $access_token_info->access_token);
                    (new OdsTencentCampaignHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['account'] => [$this->dim_value_map[$bind_target_param->target_value]['ad1']]],
                        'configured_status',
                        "AD_STATUS_{$action['target_value']}",
                        $modify_time
                    );
                    break;
                case 'auto_acquisition_budget':

                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);

                    $adgroup_model = new AdGroupModel();

                    $result = $adgroup_model->info(
                        $this->dim_value_map[$bind_target_param->target_value]['account'],
                        $access_token_info->access_token,
                        [
                            [
                                'field' => 'adgroup_id',
                                'operator' => 'EQUALS',
                                'values' => [$this->dim_value_map[$bind_target_param->target_value]['ad1']],
                            ]
                        ],
                        ['adgroup_id', 'adgroup_name', 'auto_acquisition_enabled', 'auto_acquisition_status']
                    );

                    if (count($result['list'] ?? []) > 0) {
                        $auto_acquisition_enabled = $result['list'][0]['auto_acquisition_enabled'] ?? '';
                        if ($auto_acquisition_enabled === true) {
                            $auto_acquisition_status = $result['list'][0]['auto_acquisition_status'] ?? '';
                            if (!$auto_acquisition_status) {
                                $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                                $this->log_param_map[$bind_target_param->target_value]->msg .= '一键起量状态未知';
                                return;
                            }
                            if (!in_array($auto_acquisition_status, [
                                'AUTO_ACQUISTION_STATUS_UNKNOW',
                                'AUTO_ACQUISTION_STATUS_END_LESS_THAN_24H',
                                'AUTO_ACQUISTION_STATUS_END_MORE_THAN_24H',
                                'AUTO_ACQUISTION_STATUS_COMPLETED',
                            ])) {
                                $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                                $this->log_param_map[$bind_target_param->target_value]->msg .= ('auto_acquisition_status:' . $auto_acquisition_status . '-广告本身已开启一键起量,并且"探索中"、"探索过程中，因广告起量情况太差，从而探索中止" 或者 "探索过程中，因广告无法播放，从而起量中止（包括广告主动或被动下线或 timeset 不连续）"跳过');
                                return;
                            }

                            $request_data = [
                                'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                                'adgroup_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'],
                                'auto_acquisition_enabled' => false,
                            ];
                            $adgroup_model->update($request_data, $access_token_info->access_token);
                        }
                    } else {
                        throw new AppException('查询广告组信息失败');
                    }

                    $request_data = [
                        'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        'adgroup_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'],
                        'auto_acquisition_enabled' => true,
                        'auto_acquisition_budget' => intval($action['target_value'] * 100)
                    ];
                    $adgroup_model->update($request_data, $access_token_info->access_token);
                    break;
                case 'auto_acquisition_enabled':
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);

                    $result = (new AdGroupModel())->info(
                        $this->dim_value_map[$bind_target_param->target_value]['account'],
                        $access_token_info->access_token,
                        [
                            [
                                'field' => 'adgroup_id',
                                'operator' => 'EQUALS',
                                'values' => [$this->dim_value_map[$bind_target_param->target_value]['ad1']],
                            ]
                        ],
                        ['adgroup_id', 'adgroup_name', 'auto_acquisition_enabled', 'auto_acquisition_status']
                    );

                    if (count($result['list'] ?? []) > 0) {
                        $auto_acquisition_enabled = $result['list'][0]['auto_acquisition_enabled'] ?? '';
                        if ($auto_acquisition_enabled === false) {
                            $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                            $this->log_param_map[$bind_target_param->target_value]->msg .= ('广告本身已关闭一键起量，跳过');
                            return;
                        }
                    } else {
                        throw new AppException('查询广告组信息失败');
                    }

                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);
                    $adgroup_model = new AdGroupModel();
                    $request_data['account_id'] = (int)$this->dim_value_map[$bind_target_param->target_value]['account'];
                    $request_data['adgroup_id'] = (int)$this->dim_value_map[$bind_target_param->target_value]['ad1'];
                    $request_data['auto_acquisition_enabled'] = false;
                    $adgroup_model->update($request_data, $access_token_info->access_token);
                    break;
                default:
                    throw new AppException('错误的执行指标');
            }
        } catch (Throwable $e) {
            if ($this->isRestartAction($e->getMessage())) {
                sleep(5);
                $this->actionAd1($bind_target_param, $action);
            } else {
                if (strpos($e->getMessage(), 'already deleted') !== false) {
                    (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['account'] => [$this->dim_value_map[$bind_target_param->target_value]['ad1']]],
                        'system_status',
                        "AD_GROUP_STATUS_DELETED",
                        $modify_time
                    );
                    return;
                } else {
                    throw $e;
                }
            }
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd3(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        $modify_time = date('Y-m-d H:i:s');

        try {
            switch ($action['target']) {
                case 'delete':
                    $info = (new odsTencentAdLogModel())->getDataByAdId($this->dim_value_map[$bind_target_param->target_value]['ad3']);
                    if (!$info) {
                        throw new AppException('找不到system_status数据');
                    }
                    if (in_array($info->system_status, ['SMART_DYNAMIC_CREATIVE_STATUS_DELETED', 'DYNAMIC_CREATIVE_STATUS_DELETED'])) {
                        $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                        $this->log_param_map[$bind_target_param->target_value]->msg .= "无效操作-原状态就是DYNAMIC_CREATIVE_STATUS_DELETED;";
                        return;
                    }
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);
                    (new DynamicCreativeModel())->delete([
                        'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        'dynamic_creative_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad3'],
                    ], $access_token_info->access_token);

                    (new OdsTencentAdHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['account'] => [$this->dim_value_map[$bind_target_param->target_value]['ad3']]],
                        'system_status',
                        "DYNAMIC_CREATIVE_STATUS_DELETED",
                        $modify_time
                    );
                    break;
                case 'configured_status':
                    $info = (new odsTencentAdLogModel())->getDataByAdId($this->dim_value_map[$bind_target_param->target_value]['ad3']);
                    if (!$info) {
                        throw new AppException('找不到configured_status数据');
                    }
                    if ($info->configured_status == "AD_STATUS_{$action['target_value']}") {
                        $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                        $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是AD_STATUS_{$action['target_value']};");
                        return;
                    }
                    $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::TENCENT);

                    (new DynamicCreativeModel())->update([
                        'configured_status' => "AD_STATUS_{$action['target_value']}",
                        'account_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['account'],
                        'dynamic_creative_id' => (int)$this->dim_value_map[$bind_target_param->target_value]['ad3'],
                    ], $access_token_info->access_token);
                    (new OdsTencentAdHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['account'] => [$this->dim_value_map[$bind_target_param->target_value]['ad3']]],
                        'configured_status',
                        "AD_STATUS_{$action['target_value']}",
                        $modify_time
                    );
                    break;
                default:
                    throw new AppException('错误的执行指标');
            }
        } catch (Throwable $e) {
            if ($this->isRestartAction($e->getMessage())) {
                sleep(5);
                $this->actionAd3($bind_target_param, $action);
            } else {
                if (strpos($e->getMessage(), 'already deleted') !== false) {
                    (new OdsTencentAdHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['account'] => [$this->dim_value_map[$bind_target_param->target_value]['ad3']]],
                        'system_status',
                        "DYNAMIC_CREATIVE_STATUS_DELETED",
                        $modify_time
                    );
                    return;
                } else {
                    throw $e;
                }
            }
        }
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @return void
     */
    function initDimValueMap(array $bind_target_param_list)
    {
        $target_type = $bind_target_param_list[0]->target_type;
        switch ($target_type) {
            case 'ad1':
                $ad_group_log_result = (new OdsTencentCampaignLogModel())->builder
                    ->where(['port_version' => '3.0'])
                    ->whereIn('campaign_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('campaign_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($ad_group_log_result[$bind_target_param->target_value]->campaign_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $ad_group_log_result[$bind_target_param->target_value]->account_id,
                            'ad1' => $ad_group_log_result[$bind_target_param->target_value]->campaign_id,
                            'ad1_name' => $ad_group_log_result[$bind_target_param->target_value]->campaign_name,
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $ad_group_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'ad3':
                $ad3_log_result = (new OdsTencentAdLogModel())->builder
                    ->select(['ods_tencent_ad_log.platform', 'ods_tencent_ad_log.account_id', 'ods_tencent_ad_log.ad_id', 'ods_tencent_ad_log.ad_name', 'ods_tencent_ad_log.campaign_id', 'ods_tencent_campaign_log.campaign_name'])
                    ->leftJoin('ods_tencent_campaign_log', 'ods_tencent_campaign_log.campaign_id', '=', 'ods_tencent_ad_log.campaign_id')
                    ->where(['ods_tencent_campaign_log.port_version' => '3.0'])
                    ->where(['ods_tencent_ad_log.port_version' => '3.0'])
                    ->where('ods_tencent_campaign_log.campaign_id', '!=', '')
                    ->whereIn('ad_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('ad_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($ad3_log_result[$bind_target_param->target_value]->ad_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $ad3_log_result[$bind_target_param->target_value]->account_id,
                            'ad1' => $ad3_log_result[$bind_target_param->target_value]->campaign_id,
                            'ad1_name' => $ad3_log_result[$bind_target_param->target_value]->campaign_name,
                            'ad3' => $ad3_log_result[$bind_target_param->target_value]->ad_id,
                            'ad3_name' => $ad3_log_result[$bind_target_param->target_value]->ad_name,
                        ];
                        $this->dim_value_platform = $ad3_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'account':
                $account_result = (new OdsTencentAccountLogModel())->builder
                    ->selectRaw('account_id')
                    ->selectRaw('platform')
                    ->whereIn('account_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('account_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($account_result[$bind_target_param->target_value]->account_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $account_result[$bind_target_param->target_value]->account_id,
                            'ad1' => null,
                            'ad1_name' => '',
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $account_result[$bind_target_param->target_value]->platform;
                    }
                }

                break;
            default:
                throw new AppException('错误的条件数据维度');
        }
    }

    private function isRestartAction(string $error_msg): bool
    {
        $restart_like_msg_list = [
            'Your request is much too frequent',
        ];

        foreach ($restart_like_msg_list as $msg) {
            if (strpos($error_msg, $msg) !== false) {
                return true;
            }
        }
        return false;
    }
}
