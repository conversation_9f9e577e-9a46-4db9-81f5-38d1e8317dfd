<?php

namespace App\Service\MediaIntelligentMonitor;


use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Kuaishou\ADUnit\ADUnitModel;
use App\Model\HttpModel\Kuaishou\Creative\CreativeModel;
use App\Model\SqlModel\DataMedia\ADIntelligentMonitorKuaishouModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouCreativeHisLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouCreativeLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouUnitHisLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouUnitLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\ADIntelligentMonitorBindParam;
use Illuminate\Support\Collection;
use Throwable;

class IntelligentMonitorKuaishou extends AbstractMediaMonitor
{
    protected $level_target_map = [
        'ad1' => 'ad1',
        'ad2' => 'ad2',
        'ad3' => 'ad3',
        'ad4' => 'ad3',
    ];

    function isRestartAction($error_msg): bool
    {
        $restart_like_msg_list = [
            '该应用请求过于频繁'
        ];

        foreach ($restart_like_msg_list as $msg) {
            if (strpos($error_msg, $msg) !== false) {
                return true;
            }
        }
        return false;
    }

    function executeCondition(array $bind_target_param_list, $target): array
    {
        switch ($target['condition_target_dim']) {
            case 'account':
                $result = $this->conditionAccount($bind_target_param_list, $target);
                break;
            case 'ad2':
                $result = $this->conditionAd2($bind_target_param_list, $target);
                break;
            case 'ad3':
                $result = $this->conditionAd3($bind_target_param_list, $target);
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }

        $data_map = $result['data'];

        $condition_result_map = [];
        foreach ($data_map as $target_value => $data) {
            $condition_result = $data && ($data->where('result', '>', 0)->count());
            $this->log_param_map[$target_value]->addConditionLogicResult(
                $target['condition_target'],
                $target['condition_target_action'],
                $target['condition_target_value'],
                $data ? $data->pluck($target['condition_target']) : [],
                $result['sql'],
                $condition_result
            );
            $condition_result_map[$target_value] = $condition_result;
        }

        return $condition_result_map;
    }

    private function conditionAccount(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            $copy_bind_target_param->target_type = 'account';
            $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['account'];
            $copy_bind_target_param_list[] = $copy_bind_target_param;
        }

        $result = (new ADIntelligentMonitorKuaishouModel())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );

        $result_map = [
            'data' => [],
            'sql' => $result['sql'],
        ];

        foreach ($bind_target_param_list as $bind_target_param) {
            $result_map['data'][$bind_target_param->target_value] = $result['data'][$this->dim_value_map[$bind_target_param->target_value]['account']];
        }

        return $result_map;
    }

    private function conditionAd2(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        $copy_bind_target_param_list = [];
        foreach ($bind_target_param_list as $bind_target_param) {
            $copy_bind_target_param = new ADIntelligentMonitorBindParam($bind_target_param->toArray());
            switch ($bind_target_param->target_type) {
                case 'ad2':
                case 'ad1':
                    $copy_bind_target_param->target_type = 'ad2';
                    $copy_bind_target_param->target_value = $this->dim_value_map[$bind_target_param->target_value]['ad2'];
                    break;
            }
            $copy_bind_target_param_list[] = $copy_bind_target_param;
        }

        $result = (new ADIntelligentMonitorKuaishouModel())->getConditionSqlData(
            $target,
            $condition_time_range,
            $copy_bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );

        $result_map = [
            'data' => [],
            'sql' => $result['sql'],
        ];

        foreach ($bind_target_param_list as $bind_target_param) {
            $result_map['data'][$bind_target_param->target_value] = $result['data'][$this->dim_value_map[$bind_target_param->target_value]['ad2']];
        }

        return $result_map;
    }

    private function conditionAd3(array $bind_target_param_list, $target): array
    {
        $condition_time_range = $this->getDateRange($target['time_range'], $target['time_range_type'], $target['time_range_unit']);

        return (new ADIntelligentMonitorKuaishouModel())->getConditionSqlData(
            $target,
            $condition_time_range,
            $bind_target_param_list,
            $this->dim_value_platform,
            $this->getDimAccountList()
        );
    }

    /**
     * @throws Throwable
     */
    public function execAction(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        switch ($bind_target_param->target_type) {
            case 'ad2':
                $this->actionAd2($bind_target_param, $action);
                break;
            case 'ad3':
                $this->actionAd3($bind_target_param, $action);
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd2(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        $modify_time = date('Y-m-d H:i:s');

        switch ($action['target']) {
            case 'put_status':
                $info = (new odsKuaishouUnitLogModel())->getInfoByAdId($this->dim_value_map[$bind_target_param->target_value]['ad2']);
                if (!$info) {
                    throw new AppException('找不到put_status数据');
                }
                if ($info->ad_put_status == $action['target_value']) {
                    $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                    $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是{$action['target_value']};");
                    return;
                }
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::KUAISHOU);

                try {
                    (new ADUnitModel())->updateStatus((int)$this->dim_value_map[$bind_target_param->target_value]['account'], $access_token_info->access_token, (int)$this->dim_value_map[$bind_target_param->target_value]['ad2'], (int)$action['target_value']);
                    (new OdsKuaishouUnitHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['ad2']],
                        'ad_put_status',
                        $action['target_value'],
                        $modify_time
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd2($bind_target_param, $action);
                    } else {
                        throw $e;
                    }
                }

                break;
        }
    }

    /**
     * @throws Throwable
     */
    private function actionAd3(ADIntelligentMonitorBindParam $bind_target_param, $action)
    {
        $modify_time = date('Y-m-d H:i:s');

        switch ($action['target']) {
            case 'put_status':
                $info = (new odsKuaishouCreativeLogModel())->getDataByCreativeID($this->dim_value_map[$bind_target_param->target_value]['ad3']);
                if (!$info) {
                    throw new AppException('找不到put_status数据');
                }
                if ($info->opt_status == $action['target_value']) {
                    $this->log_param_map[$bind_target_param->target_value]->is_effective = 'invalid';
                    $this->log_param_map[$bind_target_param->target_value]->msg .= ("无效操作-原状态就是{$action['target_value']};");
                    return;
                }
                $access_token_info = (new MediaAccountModel())->getDataByAccountId($this->dim_value_map[$bind_target_param->target_value]['account'], MediaType::KUAISHOU);

                try {
                    (new CreativeModel())->updateStatus((int)$this->dim_value_map[$bind_target_param->target_value]['account'], $access_token_info->access_token, (int)$this->dim_value_map[$bind_target_param->target_value]['ad3'], (int)$action['target_value']);
                    (new OdsKuaishouCreativeHisLogModel())->getLatestByADInfoAndInsert(
                        [$this->dim_value_map[$bind_target_param->target_value]['ad3']],
                        'put_status',
                        $action['target_value'],
                        $modify_time
                    );
                } catch (Throwable $e) {
                    if ($this->isRestartAction($e->getMessage())) {
                        sleep(5);
                        $this->actionAd3($bind_target_param, $action);
                    } else {
                        throw $e;
                    }

                }

                break;
        }
    }

    /**
     * @param ADIntelligentMonitorBindParam[] $bind_target_param_list
     * @return mixed
     */
    public function initDimValueMap(array $bind_target_param_list)
    {
        $target_type = $bind_target_param_list[0]->target_type;
        switch ($target_type) {
            case 'ad2':
                $unit_log_result = (new OdskuaishouUnitLogModel())->builder
                    ->whereIn('ad_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('ad_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($unit_log_result[$bind_target_param->target_value]->ad_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $unit_log_result[$bind_target_param->target_value]->account_id,
                            'ad2' => $unit_log_result[$bind_target_param->target_value]->ad_id,
                            'ad2_name' => $unit_log_result[$bind_target_param->target_value]->ad_name,
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $unit_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'ad3':
                $creative_log_result = (new OdsKuaishouCreativeLogModel())->builder
                    ->select([
                        'ods_kuaishou_creative_log.platform',
                        'ods_kuaishou_creative_log.account_id',
                        'ods_kuaishou_creative_log.ad_id',
                        'ods_kuaishou_unit_log.ad_name',
                        'ods_kuaishou_creative_log.creative_id',
                        'ods_kuaishou_creative_log.creative_name'
                    ])
                    ->leftJoin('ods_kuaishou_unit_log', 'ods_kuaishou_unit_log.ad_id', '=', 'ods_kuaishou_creative_log.ad_id')
                    ->whereIn('creative_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('creative_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($creative_log_result[$bind_target_param->target_value]->creative_id)) {
                        $this->dim_value_map = [
                            'account' => $creative_log_result[$bind_target_param->target_value]->account_id,
                            'ad2' => $creative_log_result[$bind_target_param->target_value]->ad_id,
                            'ad2_name' => $creative_log_result[$bind_target_param->target_value]->ad_name,
                            'ad3' => $creative_log_result[$bind_target_param->target_value]->creative_id,
                            'ad3_name' => $creative_log_result[$bind_target_param->target_value]->creative_name,
                        ];
                        $this->dim_value_platform = $creative_log_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            case 'account':
                $account_result = (new OdsKuaishouAccountLogModel())->builder
                    ->selectRaw('account_id')
                    ->selectRaw('platform')
                    ->whereIn('account_id', array_map(function ($bind_target_param) {
                        return $bind_target_param->target_value;
                    }, $bind_target_param_list))
                    ->get()->keyBy('account_id');

                foreach ($bind_target_param_list as $bind_target_param) {
                    if (isset($account_result[$bind_target_param->target_value]->account_id)) {
                        $this->dim_value_map[$bind_target_param->target_value] = [
                            'account' => $account_result[$bind_target_param->target_value]->account_id,
                            'ad2' => null,
                            'ad2_name' => '',
                            'ad3' => null,
                            'ad3_name' => '',
                        ];
                        $this->dim_value_platform = $account_result[$bind_target_param->target_value]->platform;
                    }
                }
                break;
            default:
                throw new AppException('错误的条件数据维度');
        }
    }
}
