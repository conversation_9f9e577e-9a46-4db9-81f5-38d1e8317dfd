<?php

namespace App\Service\DataBot;


use App\Exception\AppException;
use App\Model\HttpModel\Feishu\Aily\ChatModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Monolog\Logger;
use RedisException;

/**
 * Aily智能伙伴创建平台
 * Class AilyService
 */
class AilyService
{

    const SKILL_LIST = [
        'xhs'      => 'skill_a0d70166553f',// 小红书
        'qa'       => 'skill_4a2bf652d957',// 企业知识问答
        'deepseek' => 'skill_c5f8af0dee1d',// deepseek联网搜索
    ];

    const AILY_SESSION_LIST_KEY = 'aily_session_list_';

    /**
     * @var ChatModel
     */
    protected $chat_model;

    public function __construct()
    {
        $this->chat_model = new ChatModel();
    }


    public function handlerMessage($user_query, $union_id, $access_token, Logger $logger)
    {
        $chat_model = $this->chat_model;
        $logger->info('处理aily消息', ['user_query' => $user_query]);
        try {

            // 先发一个正在思考
            $msg_id = 0; // 消息id，用来更新消息卡片
            $this->streamOutput($msg_id, $access_token, $union_id, '正在思考...', $logger);


            // 1. 获取会话
            $session_id = $this->getSessionID($union_id);
            $logger->info("获取到会话id:$session_id");

// session_4fvgn7dsj6krz
            // on_b8ef743bfe285ce29e8079b882739aa3
            // 2. 创建用户问题消息
            $message_data = $chat_model->createMessage($session_id, $user_query);
            $logger->info("问题消息创建成功", ['message_data' => $message_data]);

            // 3.创建运行, 判断要不要调用技能id 判断需不需要获取原来的运行
            $scene = DataBotService::getScene($union_id);
            $skill_id = self::SKILL_LIST[$scene] ?? '';

            $run_info = $this->getRunInfo($union_id, $session_id);
            if ($run_info) {
                $run_id = $run_info['run_id'];
                $logger->info("获取原来的运行成功,run_id是$run_id,skill_id:$skill_id");
            } else {
                $run_data = $chat_model->createRun($session_id, $skill_id);
                $run_id = $run_data['data']['run']['id'];
                $logger->info("运行创建成功,run_id是$run_id,skill_id:$skill_id");
            }

            // 4. 轮询
            $start_time = time();
            $max_total_wait_time = 3600; // 最大等待时间一小时
            while (true) {
                $status_res = $chat_model->getRun($session_id, $run_id);
                $run_status = $status_res['data']['run']['status'] ?? null;

                if ($run_status === 'IN_PROGRESS') {
                    // 获取消息
                    $message_res = $chat_model->listMessage($session_id, $run_id);

                    $message_list = $message_res['data']['messages'] ?? [];

                    foreach ($message_list as $msg) {
                        $send_type = $msg['sender']['sender_type'] ?? '';
                        if ($send_type === 'ASSISTANT') {
                            $msg_id_list = $this->getMsgList($session_id, $run_id);
                            if (!in_array($msg['id'], $msg_id_list)) {
                                $this->streamOutput($msg_id, $access_token, $union_id, $msg['content'], $logger);
                            }
                            if ($msg['status'] === 'COMPLETED' && !in_array($msg['id'], $msg_id_list)) {
                                $this->saveMsgList($session_id, $run_id, $msg['id']);
                            }

                        }
                    }


                } elseif ($run_status === 'COMPLETED') {
                    // 获取消息
                    $message_res = $chat_model->listMessage($session_id, $run_id);
                    $message_list = $message_res['data']['messages'] ?? [];

                    foreach ($message_list as $msg) {
                        $send_type = $msg['sender']['sender_type'] ?? '';
                        if ($send_type === 'ASSISTANT') {
                            $msg_id_list = $this->getMsgList($session_id, $run_id);
                            if (!in_array($msg['id'], $msg_id_list)) {
                                $this->streamOutput($msg_id, $access_token, $union_id, $msg['content'], $logger);
                            }
                            if ($msg['status'] === 'COMPLETED' && !in_array($msg['id'], $msg_id_list)) {
                                $this->saveMsgList($session_id, $run_id, $msg['id']);
                            }
                        }
                    }
                    $this->deleteRunInfo($union_id, $session_id);
                    break;
                } elseif ($run_status === 'REQUIRES_MESSAGE') {
                    // 获取消息
                    $message_res = $chat_model->listMessage($session_id, $run_id);
                    $message_list = $message_res['data']['messages'] ?? [];
                    foreach ($message_list as $msg) {
                        $send_type = $msg['sender']['sender_type'] ?? '';
                        if ($send_type === 'ASSISTANT') {
                            $msg_id_list = $this->getMsgList($session_id, $run_id);
                            if (!in_array($msg['id'], $msg_id_list)) {
                                $this->streamOutput($msg_id, $access_token, $union_id, $msg['content'], $logger);
                            }
                            if ($msg['status'] === 'COMPLETED' && !in_array($msg['id'], $msg_id_list)) {
                                $this->saveMsgList($session_id, $run_id, $msg['id']);
                            }
                        }
                    }
                    // 需要把run_id存起来
                    $this->saveRunInfo($union_id, $session_id, $run_id);
                    break;
                } elseif ($run_status === 'FAILED') {
                    // 失败状态
                    $message_res = $chat_model->listMessage($session_id, $run_id);
                }

                usleep(500000); // 0.5秒
                // 检查总等待时间
                if (time() - $start_time > $max_total_wait_time) {
                    $logger->error("超出最大等待时间，任务停止。");
                    break;
                }
            }


        } catch (\Throwable $e) {
            $logger->error('出现未知错误', ['message' => $e->getMessage(), 'strace' => $e->getTraceAsString()]);
        }

    }


    /**
     * 获取会话id
     *
     * @throws RedisException
     */
    public function getSessionID($union_id)
    {
        $redis = RedisCache::getInstance();
        // 判断有没有历史会话，有的话用会原来的session
        try {
            $session_id = $redis->get(self::AILY_SESSION_LIST_KEY . $union_id) ?? 0;
        } catch (RedisException $e) {
            $session_id = 0;
        }
        if (!$session_id) {
            try {
                $session_data = $this->chat_model->createSession($union_id);
            } catch (\Throwable $e) {
                throw new AppException('会话创建失败:' . $e->getMessage());
            }

            $session_id = $session_data['data']['session']['id'];
            $redis->setex(self::AILY_SESSION_LIST_KEY . $union_id, 7200, $session_id);
        }

        return $session_id;
    }


    /**
     * 开启新话题，清空原来的session_id
     *
     * @param $union_id
     * @return void
     * @throws RedisException
     */
    public function cleanSession($union_id)
    {
        $redis = RedisCache::getInstance();
        $redis->del(self::AILY_SESSION_LIST_KEY . $union_id);
    }

    public function getMsgCard($content)
    {
        $card = [
            "schema" => "2.0",
            "config" => [
                "update_multi" => true,
                "style"        => [
                    "text_size" => [
                        "normal_v2" => [
                            "default" => "normal",
                            "pc"      => "normal",
                            "mobile"  => "heading"
                        ]
                    ]
                ]
            ],
            "body"   => [
                "direction" => "vertical",
                "padding"   => "12px 12px 12px 12px",
                "elements"  => [
                    [
                        "tag"        => "markdown",
                        "content"    => $content,
                        "text_align" => "left",
                        "text_size"  => "normal_v2",
                        "margin"     => "0px 0px 0px 0px",
                        "element_id" => "msg_1"
                    ]
                ]
            ]
        ];

        return json_encode($card, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    }

    private function streamOutput(&$msg_id, $access_token, $union_id, $content, $logger)
    {
        // 消息内容为空的时候，不需要发送
        if (!$content) {
            return;
        }
        $pattern = '/\[(.*?)\]\(file_[a-z0-9]+\)/i';
        $replacement = '';

        // 暂时先去掉图片。到时候看看怎么解决图片问题 TODO
        $content = preg_replace($pattern, $replacement, $content);
        $message_model = new MessageModel();
        if ($msg_id === 0) {
            // 第一次是发送消息
            $msg_ret_data = $message_model->message($access_token, 'union_id', $union_id, $this->getMsgCard($content), 'interactive');
            $msg_id = $msg_ret_data['data']['message_id'] ?? 0; // 更新消息id
            $logger->info("首次消息发送成功", $msg_ret_data);
        } else {
            // 更新消息
            $ret = $message_model->updateCard($access_token, $msg_id, $this->getMsgCard($content));
            $logger->info("卡片更新成功", $ret);
        }
    }

    protected function saveMsgList($session_id, $run_id, $msg_id)
    {
        $msg_id_list = $this->getMsgList($session_id, $run_id);
        $msg_id_list[] = $msg_id;

        $redis = RedisCache::getInstance();
        $key = $this->genMsgListKey($session_id, $run_id);
        $redis->setex($key, 7200, serialize($msg_id_list));
    }

    protected function getMsgList($session_id, $run_id)
    {
        $redis = RedisCache::getInstance();
        $key = $this->genMsgListKey($session_id, $run_id);
        $res = $redis->get($key);
        if ($res) {
            $ret = unserialize($res);
        } else {
            $ret = [];
        }
        return $ret;
    }



    protected function saveRunInfo($union_id, $session_id, $run_id)
    {
        $redis = RedisCache::getInstance();
        $key = $this->genRunInfoKey($union_id, $session_id);
        $redis->set($key, serialize([
            'union_id'   => $union_id,
            'run_id'     => $run_id,
            'session_id' => $session_id,
        ]));
    }

    protected function getRunInfo($union_id, $session_id)
    {
        $redis = RedisCache::getInstance();
        $key = $this->genRunInfoKey($union_id, $session_id);
        $res = $redis->get($key);
        if ($res) {
            $ret = unserialize($res);
        } else {
            $ret = [];
        }
        return $ret;
    }

    protected function deleteRunInfo($union_id, $session_id)
    {
        $redis = RedisCache::getInstance();
        $key = $this->genRunInfoKey($union_id, $session_id);
        $redis->del($key);
    }

    protected function genRunInfoKey($union_id, $session_id)
    {
        return 'run_info_' . $union_id . $session_id;
    }

    protected function genMsgListKey($session_id, $run_id)
    {
        return 'mgs_list_' . $session_id . '_' . $run_id;
    }
}