<?php

namespace App\Service\DataBot;

class DataBotConfig
{

    const MODEL = 'gpt-4o';

    const PROJECT = '数据分析助手';

    const ROUTE_NAME_OVERVIEW = '数据总览';
    const ROUTE_NAME_PAYMENT = '付费情况';
    const ROUTE_NAME_RETAIN = '留存情况';

    /**
     * 统计口径的 map
     */
    const DIMENSION_TYPE_MAP = ["按子" => 1, "按根" => 2, "按回流" => 3];
    const DIMENSION_NAME_MAP = [1 => "按子", 2 => "按根", 3 => "按回流"];


    // 搜索特殊处理的维度
    const SEARCH_SPECIFY = ['联运渠道', '渠道组', '渠道', '广告位', '集团游戏', '根游戏', '主游戏', '子游戏', '媒体账户'];

    /**
     * 精确搜索的维度。
     *
     */
    const EXACT_SPECIFY = ['平台'];

    /**
     * 指标的映射，搜索到的指标映射一下
     * 比如问某个游戏的注册，就是要映射成注册数。
     */
    const TARGET_MAP = [
        '流水'           => '总付费金额',
        '收入'           => '总付费金额',
        '投入'           => '消耗',
        '总流水'         => '总付费金额',
        '充值金额'       => '总付费金额',
        '创角'           => '创角数',
        '角色数'         => '创角数',
        '实名'           => '实名率',
        '注册'           => '注册数',
        '新增'           => '注册数',
        '新增用户'       => '注册数',
        '激活'           => '激活数',
        '次留'           => '次留数',
        '用户数'         => '总用户数',
        '新增用户总充值' => '累计付费总额',
        '新增用户总付费' => '累计付费总额',
        '累计总付费金额' => '累计付费总额',
        '累计付费'       => '累计付费总额',
    ];

    /**
     * 会话历史记录最大值
     */
    const SESSION_MAX_LENGTH = 4;

    /**
     * 用字符串数量来判断
     */
    const SESSION_MAX_LENGTH_STR = 100;


    /**
     * 会话过期时间 单位 s
     */
    const SESSION_EXPIRE_TIME = 7200;

    /**
     * 付费情况超过一定数量的情况下，默认给这几个指标
     */
    const PAYMENT_DAY_LIST = [
        'day_1', 'day_2', 'day_3', 'day_7', 'day_14', 'day_15',
        'day_21', 'day_30', 'day_45', 'day_60', 'day_90', 'day_120',
        'day_150', 'day_180', 'day_210', 'day_240', 'day_270', 'day_300',
        'day_330', 'day_360', 'day_390', 'day_420', 'day_450', 'day_480',
        'day_510', 'day_540', 'day_570', 'day_600', 'day_630', 'day_660',
        'day_690', 'day_720', 'day_780', 'day_810', 'day_840', 'day_870',
        'day_900', 'day_930', 'day_960', 'day_990', 'day_1020', 'day_1050',
    ];

    /**
     * 留存情况超过一定数量的情况下，默认给这几个指标
     */
    const RETAIN_DAY_LIST = [
        'rate_day_stay_2', 'rate_day_stay_3', 'rate_day_stay_7', 'rate_day_stay_14', 'rate_day_stay_15',
        'rate_day_stay_21', 'rate_day_stay_30', 'rate_day_stay_45', 'rate_day_stay_60', 'rate_day_stay_90', 'rate_day_stay_120',
        'rate_day_stay_150', 'rate_day_stay_180', 'rate_day_stay_210', 'rate_day_stay_240', 'rate_day_stay_270', 'rate_day_stay_300',
        'rate_day_stay_330', 'rate_day_stay_360', 'rate_day_stay_390', 'rate_day_stay_420', 'rate_day_stay_450', 'rate_day_stay_480',
        'rate_day_stay_510', 'rate_day_stay_540', 'rate_day_stay_570', 'rate_day_stay_600', 'rate_day_stay_630', 'rate_day_stay_660',
        'rate_day_stay_690', 'rate_day_stay_720', 'rate_day_stay_780', 'rate_day_stay_810', 'rate_day_stay_840', 'rate_day_stay_870',
        'rate_day_stay_900', 'rate_day_stay_930', 'rate_day_stay_960', 'rate_day_stay_990', 'rate_day_stay_1020', 'rate_day_stay_1050',
    ];

}