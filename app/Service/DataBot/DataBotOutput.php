<?php

namespace App\Service\DataBot;

use App\Constant\RouteID;
use App\Constant\RoutePermission;
use App\Exception\AppException;
use App\Logic\DMS\DataAnalysis\PaymentLogic;
use App\Logic\DMS\MarketLogic;
use App\Model\SqlModel\Tanwan\DataAnalysis\DataAnalysisModel;
use App\Param\DMS\DataAnalysis\OverviewListFilterParam;
use App\Param\DMS\DataAnalysis\PaymentListFilterParam;
use App\Param\DMS\DataAnalysis\RetainListFilterParam;
use App\Task\ExportFileTask;
use App\Utils\DimensionTool;
use App\Utils\ExportHelper;
use App\Utils\Helpers;
use Illuminate\Support\Collection;

/**
 * 处理data bot 的输出
 */
class DataBotOutput
{


    /**
     * 用户名
     *
     * @var string
     */
    protected $username = '';

    public function __construct($username)
    {
        $this->username = $username;
    }

    /**
     * 格式化输出条件等信息
     *
     * @param $mapping
     * @param $dimension_filter
     * @param $query_param
     * @param $dimension_names
     * @param $route_name
     * @param string $hint
     * @return string
     */
    public function filterOutputString($mapping, $dimension_filter, $query_param, $dimension_names, $route_name, string $hint = '')
    {
        // 做个条件映射
        $filter_map = [];
        $target_map = [];
        foreach ($mapping[RoutePermission::DIMENSION_FILTER] as $key => $value) {
            $filter_map[$value['key']] = $key;
        }
        // $mapping[RoutePermission::TARGET][$item['target']]
        foreach ($mapping[RoutePermission::TARGET] as $target_map_name => $column_name) {
            $target_map[$column_name] = $target_map_name;

            // 特殊处理一下，实际消耗
            if ($column_name === 'cost_money') {
                if (isset($query_param['true_cost']) && $query_param['true_cost']) {
                    $target_map['cost_money'] = '实际消耗';
                } else {
                    $target_map['cost_money'] = '消耗';
                }
            }
            // 特殊处理一下，实际消耗
            if ($column_name === 'day_cost_money') {
                if (isset($query_param['true_cost']) && $query_param['true_cost']) {
                    $target_map['day_cost_money'] = '实际消耗';
                } else {
                    $target_map['day_cost_money'] = '消耗';
                }
            }
        }


        // 做一个分摊的显示
        $apportion = [];
        if (isset($query_param['official_apportion']) && $query_param['official_apportion'] == 1) {
            $apportion[] = '自然量分摊';
        }
        if (isset($query_param['flow_apportion']) && $query_param['flow_apportion'] == 1) {
            $apportion[] = '直播间分摊';
        }
        if (isset($query_param['preregister_apportion']) && $query_param['preregister_apportion'] == 1) {
            $apportion[] = '预约分摊';
        }

        // 返回条件显示，每个条件最多显示5个value
        $filter_str = '';
        if ($hint) {
            $filter_str .= "$hint\n";
        }
        $filter_str .= "查询报表：$route_name\n查询条件\n";
        foreach ($dimension_filter as $key => $item) {
            $suffix = count($item['value']) > 5 ? "等\n" : "\n";
            $item['value'] = array_slice($item['value'], 0, 5);
            $filter_str .= "{$filter_map[$key]}: " . implode(',', $item['value']) . "$suffix";
        }
        $filter_str .= "日期范围：{$query_param['start_time']}  -  {$query_param['end_time']}\n";
        $filter_str .= "时间聚合：{$query_param['aggregation_time']}\n";
        $filter_str .= "维度聚合：" . implode(',', $dimension_names) . "\n";
        $dimension_type_name = DataBotConfig::DIMENSION_NAME_MAP[$query_param['dimension_type']];
        $filter_str .= "统计口径：{$dimension_type_name}\n";
        if ($apportion) {
            $filter_str .= "分摊参数：" . implode(',', $apportion) . "\n";
        }

        if ($query_param['order_by']) {
            $filter_str .= "排序字段：";
            $sort_map = ["DESC" => '降序', "ASC" => '升序'];
            foreach ($query_param['order_by'] as $item) {
                $target_name = $target_map[$item['target']] ?? $item['target'];
                $filter_str .= "{$target_name}{$sort_map[$item['direction']]} ";
            }
        }

        return $filter_str;
    }


    /**
     * 查询参数反映射，作用于chatGPT的历史会话
     *
     * @param $mapping
     * @param $dimension_filter
     * @param $query_param
     * @return array
     */
    public function queryParamFormat($mapping, $query_param, $dimension_filter)
    {
        // 如果是标签的话，dimension_filter的处理要特殊一点。
        if (isset($query_param['pag']) && $query_param['pag']) {
            // 标签的处理，把所有条件都丢到dimension_filter_name里面
            $dimension_filter_name = [];

            // 先构建索引
            $reverse_index = [];
            foreach ($mapping[RoutePermission::DIMENSION_FILTER] as $chinese_name => $attributes) {
                $reverse_index[$attributes['key']] = $chinese_name;
            }
            foreach ($dimension_filter as $column_name => $value) {
                if (isset($reverse_index[$column_name])) {
                    $dimension_filter_name[$reverse_index[$column_name]] = $value['value'];
                }
            }

            unset($query_param['dimension_filter']);
            $query_param['dimension_filter_name'] = $dimension_filter_name;

        } else {
            // 没有标签的情况,全都放 dimension_filter_name
            $dimension_filter_name = $query_param['dimension_filter_name'] ?? [];
            foreach ($dimension_filter_name as $key => &$item) {
                $item = $dimension_filter[$mapping[RoutePermission::DIMENSION_FILTER][$key]['key']]['value'];
            }
            $dimension_filter_id = $query_param['dimension_filter_id'] ?? [];
            foreach ($dimension_filter_id as $key => &$item) {
                $item = $dimension_filter[$mapping[RoutePermission::DIMENSION_FILTER][$key]['key']]['value'];
            }

            $query_param['dimension_filter_name'] = $dimension_filter_name;
            $query_param['dimension_filter_id'] = $dimension_filter_id;
        }


        return $query_param;
    }

    public function overviewOutputString($data, $mapping_column)
    {
        $mapping_column['cost_money'] = '消耗';
        $mapping_column['date'] = '日期';
        $mapping_column['game_reg_date'] = '日期';
        foreach (DataAnalysisModel::ID_TO_NAME as $item) {
            $name = DimensionTool::idToNameKey($item);
            if (isset($mapping_column[$item])) {
                $mapping_column[$name] = $mapping_column[$item] . '名';
                $mapping_column[$item] .= 'ID';
            }
        }

        // 指标翻译
        $out_data = [];

        // 需要过滤的指标
        $filter_target = ['total_standard_value', 'seventh_standard_value', 'thirty_standard_value', 'standard_value', 'max_first_day_pay_money'];
        foreach ($data['list'] as $item) {
            $tmp = [];
            foreach ($item as $key => $value) {
                // 过滤指标
                if (in_array($key, $filter_target)) {
                    continue;
                }
                $name_key = $mapping_column[$key] ?? $key;
                $tmp[$name_key] = $value;
            }
            $out_data[] = $tmp;
        }

        // 直接输入一句话：
        $str = '';
        foreach ($out_data as $index => $item) {
            $row = $index + 1;
            $str .= "第 $row 行数据：\n";
            foreach ($item as $key => $value) {
                if ($key === 'cost_days') continue;

                $str .= "{$key} ：$value\n";
            }

        }

        return $str;
    }

    public function paymentOutputTable($data, PaymentListFilterParam $param, $gpt_param, $watermark, $order_by = [])
    {
        $raw_dimension = $param->dimension;
        $payment_date = 'game_reg_date';

        // 格式化一下原始维度
        if (in_array('game_reg_date', $raw_dimension)) {
            $key = array_search('game_reg_date', $raw_dimension);
            array_splice($raw_dimension, $key, 1);
        }
        // 格式化一下原始维度
        if (in_array('create_date', $raw_dimension)) {
            $key = array_search('create_date', $raw_dimension);
            array_splice($raw_dimension, $key, 1);
        }

        if (DimensionTool::needPushPlatform($raw_dimension)) {
            array_unshift($raw_dimension, 'platform');
        }
        // 去重
        $raw_dimension = array_unique($raw_dimension);

        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }

        $list = $data['list'];
        // 判断数据是否只有一行，一行的话，不需要合计列
        if (count($list) > 1) {
            // 不存在 date 的情况下，没有合计这两个字，要去最后一个维度添加合计
            if ($param->aggregation_time === '聚合') {
                // 最后一个维度，固定合计
                foreach ($param->dimension as $index => $item) {
                    if ($index === count($param->dimension) - 1) {
                        $data['sum'][$item] = '合计';
                    }
                }
            }

            // 判断是否有排序, 有的话需要排序
            if ($order_by) {
                $sorts = [];
                foreach ($order_by as $item) {
                    $sorts[$item['target']] = $item['direction'] === "DESC" ? SORT_DESC : SORT_ASC;
                }
                Helpers::multipleSort($list, $sorts, true);
            }


            (new ExportFileTask())->unshift($list, $data['sum']); // 合计塞上来
        }


        // 只保留50行
        $overflow = false;
        if (count($list) > 50) {
            $list = array_slice($list, 0, 50);
            $overflow = true;
        }


        $header_map = ExportHelper::exportPaymentHeadMap();
        // 换成实际消耗
        if ($param->true_cost) {
            $header_map['day_cost_money'] = '实际消耗';
        }

        $content_list = [];

        foreach ($gpt_param['type'] as $type) {
            // 重置type
            $param->type = $type;
            try {
                $key_prefix = $param->switchColumnKey();
            } catch (AppException $exception) {
                // 遇到错误的类型直接返回回本率
                $param->type = '回本率';
                $key_prefix = 'roi_';
            }

            // 表头 要根据type来决定
            $header = array_merge($raw_dimension, $param->drawFixedHeader(), $param->header_day_list);
            $header_format = (new ExportFileTask())->getHeaderFormat($param, $header, $header_map, 1, '日');

            $content = [];
            $now = strtotime(date("Ymd"));

            foreach ($list as $index => $item) {
                $item = (array)$item;
                $tmp = [];
                // xx日
                $date = $param->aggregation_time === '聚合' || $index === 0 ? $param->start_time : strtotime(date("Ymd", strtotime($item[$payment_date])));
                $in_current_month = Helpers::inCurrentMonth($date);
                foreach ($header as $column) {
                    // 普通表头
                    if (isset($item[$column])) {
                        $tmp[] = ExportHelper::itemFormat($column, $item);
                    } else {
                        // 这里主要是处理合计的情况。
                        if (!is_numeric($column)) {
                            $tmp[] = '';
                            continue;
                        }
                        // xx日表头
                        $column_key = $key_prefix . $column;
                        // 屏蔽当日
                        if ($param->blocked) {
                            // 判断是否是按月
                            if ($param->aggregation_time === '按月') {
                                // 判断是否包含当月
                                if ($in_current_month) {
                                    $tmp[] = '';
                                    continue;
                                } else {
                                    // 拿到当月的最后一天
                                    $last_day_of_month = strtotime(date('Y-m-t', $date));
                                    $days = floor(($now - $last_day_of_month) / 86400);
                                    if ($column > $days) {
                                        $tmp[] = '';
                                        continue;
                                    }
                                }

                            } else {
                                // 按日、聚合、按周都是同一个逻辑
                                $days = floor(($now - $date) / 86400) + 1;
                                if ($column >= $days) {
                                    $tmp[] = '';
                                    continue;
                                }
                            }
                            if (isset($item[$column_key])) {
                                $tmp[] = $item[$column_key];
                            } else {
                                $tmp[] = '0';
                            }
                        } else {
                            $days = floor(($now - $date) / 86400) + 1;
                            if ($column > $days) {
                                $tmp[] = '';
                            } else {
                                if (isset($item[$column_key])) {
                                    $tmp[] = $item[$column_key];
                                } else {
                                    $tmp[] = '0';
                                }
                            }
                        }

                    }
                }
                $content[] = $tmp;
            }

            $content_list[] = ['column_list' => $header_format, 'data_list' => $content, 'draw' => ['type' => '表格', 'title' => $type, 'watermark' => $watermark]];
        }

        return [
            'draw_data' => $content_list,
            'overflow'  => $overflow,
        ];


    }


    public function retainOutputTable($data, RetainListFilterParam $param, $gpt_param, $watermark, $order_by = [])
    {
        // 游戏留存和注册留存字段名有点不一样
        $retain_count = 'reg_uid_count';
        $retain_date = 'game_reg_date';
        // 表头
        if ($param->aggregation_time !== '聚合' && $param->export_type != 2) {
            $other[] = 'game_reg_date';
        }
        if ((in_array($param->aggregation_retain, $param->pay_retain) || in_array($param->server_type, $param->pay_retain)) && $param->pay_type_filter) {
            $other[] = 'pay_type';
        }
        $other[] = $retain_count;
        $has_platform_belong = in_array('is_old_root_game_muid', $param->dimension);  // 是否选中了平台归属
        $has_group_belong = in_array('is_old_clique_game_muid', $param->dimension);   // 是否选中了集团归属
        if ($has_group_belong) {
            $param->dimension[] = 'group_belong';
        }
        if ($has_platform_belong) {
            $param->dimension[] = 'platform_belong';
        }
        if ($has_group_belong || $has_platform_belong) {
            foreach ($param->dimension as $key => $item) {
                if (in_array($item, ['is_old_root_game_muid', 'is_old_root_pay_muid', 'is_old_clique_game_muid', 'is_old_clique_pay_muid'])) {
                    unset($param->dimension[$key]);
                }
            }
        }
        // 判断是否需要消耗
        if ($param->export_type == 1 && $param->needCostMoney()) {
            $other[] = 'cost_money';
        }
        $header = array_merge($param->dimension, $other, $param->export_retain_list);
        $header_map = ExportHelper::exportRetainHeadMap();
        $header_format = (new ExportFileTask())->getHeaderFormat($param, $header, $header_map, 1, '日');


        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }

        $list = $data['list'];
        // 判断数据是否只有一行，一行的话，不需要合计列
        if (count($list) > 1) {
            // 不存在 date 的情况下，没有合计这两个字，要去最后一个维度添加合计
            if ($param->aggregation_time === '聚合') {
                // 最后一个维度，固定合计
                foreach ($param->dimension as $index => $item) {
                    if ($index === count($param->dimension) - 1) {
                        $data['sum'][$item] = '合计';
                    }
                }
            }

            // 判断是否有排序, 有的话需要排序
            if ($order_by) {
                $sorts = [];
                foreach ($order_by as $item) {
                    $sorts[$item['target']] = $item['direction'] === "DESC" ? SORT_DESC : SORT_ASC;
                }
                Helpers::multipleSort($list, $sorts, true);
            }


            (new ExportFileTask())->unshift($list, $data['sum']); // 合计塞上来
        }


        // 只保留50行
        $overflow = false;
        if (count($list) > 50) {
            $list = array_slice($list, 0, 50);
            $overflow = true;
        }

        $content_list = [];
        $data_type_map = [
            "留存数"      => 0,
            "留存率"      => 1,
            "有效留存率"  => 2,
            "留存成本"    => 3,
            "2日有效留存" => 4,
        ];
        foreach ($gpt_param['data_type'] as $type) {
            // 重置is_rate
            $param->is_rate = $data_type_map[$type] ?? 1;

            $content = [];
            $now = strtotime(date("Ymd"));

            foreach ($list as $index => $item) {
                $item = (array)$item;
                $tmp = [];
                $date = $param->aggregation_time === '聚合' || $index === 0 ? $param->start_time : strtotime(date("Ymd", strtotime($item[$retain_date])));
                $in_current_month = Helpers::inCurrentMonth($date);
                foreach ($header as $column) {
                    if (isset($item[$column])) {
                        $tmp[] = ExportHelper::itemFormat($column, $item);
                    } else {
                        // 这里主要是处理合计的情况。
                        if (!is_numeric($column)) {
                            $tmp[] = '';
                            continue;
                        }

                        // xx留
                        $num_stay_key = 'num_day_stay_' . $column;
                        $rate_stay_key = 'rate_day_stay_' . $column;
                        $effective_day_stay_key = 'effective_day_stay_' . $column;
                        $stay_cost_key = 'stay_cost_' . $column;
                        $te_stay_key = 'te_day_stay_' . $column;

                        // 屏蔽当日
                        if ($param->blocked) {
                            // 判断是否是按月
                            if ($param->aggregation_time === '按月') {
                                // 判断是否包含当月
                                if ($in_current_month) {
                                    $tmp[] = '';
                                    continue;
                                } else {
                                    // 拿到当月的最后一天
                                    $last_day_of_month = strtotime(date('Y-m-t', $date));
                                    $days = floor(($now - $last_day_of_month) / 86400);
                                    if ($column > $days) {
                                        $tmp[] = '';
                                        continue;
                                    }
                                }

                            } else {
                                // 按日、聚合、按周都是同一个逻辑
                                $days = floor(($now - $date) / 86400) + 1;
                                if ($column >= $days) {
                                    $tmp[] = '';
                                    continue;
                                }
                            }
                        }
                        if (isset($item[$num_stay_key])) {
                            switch ($param->is_rate) {
                                case 0:
                                    $tmp[] = $item[$num_stay_key];
                                    break;
                                case 1:
                                    $tmp[] = $item[$rate_stay_key] . '%';
                                    break;
                                case 2:
                                    $tmp[] = $item[$effective_day_stay_key] . '%';
                                    break;
                                case 3:
                                    $tmp[] = $item[$stay_cost_key];
                                    break;
                                case 4:
                                    $tmp[] = $item[$te_stay_key] . '%';
                                    break;
                            }
                        } else {
                            $days = floor(($now - $date) / 86400);
                            if ($column > $days + 1) {
                                $tmp[] = '';
                            } else {
                                $tmp[] = in_array($param->is_rate, [1, 2, 4]) ? '0%' : '0';
                            }
                        }
                    }
                }
                $content[] = $tmp;
            }

            // 表格标题加上留存类型
            $type = $param->aggregation_retain . ": $type";
            $content_list[] = ['column_list' => $header_format, 'data_list' => $content, 'draw' => ['type' => '表格', 'title' => $type, 'watermark' => $watermark]];
        }

        return [
            'draw_data' => $content_list,
            'overflow'  => $overflow,
        ];


    }

    public function overviewOutputTable($data, OverviewListFilterParam $param, $watermark, $order_by = [])
    {
        // 去掉自动添加的标准值
        if (in_array('max_first_day_pay_money', $param->export_target)) {
            $key = array_search('max_first_day_pay_money', $param->export_target);
            array_splice($param->export_target, $key, 1);
        }
        $param->export_target = array_unique($param->export_target);

        // 表头
        if ($param->aggregation_time !== '聚合') {
            $header = array_merge($param->dimension, ['date'], $param->export_target);
        } else {
            $header = array_merge($param->dimension, $param->export_target);
        }

        $header_map = ExportHelper::exportOverviewHeadMap();

        // 换成实际消耗
        if ($param->true_cost) {
            $header_map['cost_money'] = '实际消耗';
        }
        $header_format = (new ExportFileTask())->getHeaderFormat($param, $header, $header_map);

        // 自定义指标
        /* @var Collection $customized_target */
        $customized_target = $param->customized_target;
        if ($param->customized_target) {
            $header = array_merge($header, $customized_target->pluck('name')->toArray());
            $header_format = array_merge($header_format, $customized_target->pluck('name')->toArray());
        }

        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }

        // 统一成数组
        $list = $data['list']->toArray();

        // 判断数据是否只有一行，一行的话，不需要合计列
        if (count($list) > 1) {

            // 不存在 date 的情况下，没有合计这两个字，要去最后一个维度添加合计
            if ($param->aggregation_time === '聚合') {
                // 最后一个维度，固定合计
                foreach ($param->dimension as $index => $item) {
                    if ($index === count($param->dimension) - 1) {
                        $data['sum'][$item] = '合计';
                    }
                }
            }


            // 判断是否有排序, 有的话需要排序
            if ($order_by) {
                $sorts = [];
                foreach ($order_by as $item) {
                    $sorts[$item['target']] = $item['direction'] === "DESC" ? SORT_DESC : SORT_ASC;
                }
                Helpers::multipleSort($list, $sorts, true);
            }


            (new ExportFileTask())->unshift($list, $data['sum']); // 合计塞上来
        }


        // 只保留50行
        $overflow = false;
        if (count($list) > 50) {
            $list = array_slice($list, 0, 50);
            $overflow = true;
        }


        $content = [];
        foreach ($list as $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content[] = $tmp;
        }


        return [
            'draw_data' => [
                // 数组格式输出，有些报表需要画多个图
                ['column_list' => $header_format, 'data_list' => $content, 'draw' => ['type' => '表格', 'watermark' => $watermark]]
            ],
            'overflow'  => $overflow,
        ];
    }


    /**
     * 分析画图
     *
     * @param $user_query
     * @param $data
     * @param OverviewListFilterParam $param
     * @return array
     */
    public function analysisDraw($user_query, $data, OverviewListFilterParam $param)
    {
        // 先处理一下维度和指标
        // 去掉自动添加的标准值
        if (in_array('max_first_day_pay_money', $param->export_target)) {
            $key = array_search('max_first_day_pay_money', $param->export_target);
            array_splice($param->export_target, $key, 1);
        }

        // 指标
        $target_list = array_unique($param->export_target);

        // 维度
        $dimension_list = $param->dimension;
        if ($param->aggregation_time !== '聚合') {
            $dimension_list = array_merge($param->dimension, ['date']);
        }

        // 把指标翻译成中文
        $header_map = ExportHelper::exportOverviewHeadMap();

        $target_list_cn = array_map(function ($item) use ($header_map) {
            return $header_map[$item] ?? '';
        }, $target_list);

        $dimension_list_cn = array_map(function ($item) use ($header_map) {
            return $header_map[$item] ?? '';
        }, $dimension_list);

        // 自定义指标
        /* @var Collection $customized_target */
        $customized_target = $param->customized_target;
        if ($param->customized_target) {
            $name_list = $customized_target->pluck('name')->toArray();
            $header_map = array_merge($header_map, array_combine($name_list, $name_list));
            $target_list_cn = array_merge($target_list_cn, $name_list);
        }

        // 先去GPT提问，到底怎么画图。
        $gpt_content = $this->getAnalysisDrawPrompt($user_query, $dimension_list_cn, $target_list_cn);
        $gpt_param = $this->requestGPT($gpt_content, '画图分析', 'GPT画图分析结果');

        // 提取画图需要的字段
        /**
         * ```
         * {"type":"折线图","draw_column":{"x":"日期","y":"总付费金额"}}
         * {"type":"折线图","draw_column": {"x": "根游戏", "y": {"line":"消耗","bar":"总付费金额"}}}
         * ```
         */
        // 验证一下参数
        if (!in_array($gpt_param['type'], ['折线图', '饼图', '柱状图', '组合图'])) {
            throw new AppException('gpt画图分析出错，请联系管理员');
        }
        $reverse_map = array_flip($header_map);
        if ($gpt_param['type'] != '组合图') {
            $header = [$reverse_map[$gpt_param['draw_column']['x']], $reverse_map[$gpt_param['draw_column']['y']]];
            $header_format = [$gpt_param['draw_column']['x'], $gpt_param['draw_column']['y']];
        } else {
            // 组合图数据结构比较不一样
            $header = [$reverse_map[$gpt_param['draw_column']['x']], $reverse_map[$gpt_param['draw_column']['y']['line']], $reverse_map[$gpt_param['draw_column']['y']['bar']]];
            $header_format = [$gpt_param['draw_column']['x'], $gpt_param['draw_column']['y']['line'], $gpt_param['draw_column']['y']['bar']];
        }


        // 统一成数组
        $list = $data['list']->toArray();

        // 只保留5w行
        if (count($list) > 50000) {
            $list = array_slice($list, 0, 50000);
        }


        $content = [];
        foreach ($list as $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content[] = $tmp;
        }

        return ['column_list' => $header_format, 'data_list' => $content, 'draw' => $gpt_param];
    }
}