<?php

namespace App\Service\DataBot;


/**
 * 数据分析助手
 */
class DataBotPrompt
{
    /**
     * 投放总览参数提取
     */
    const OVERVIEW_PARAM_PROMPT = '将用户的自然语言提问转换为特定的JSON格式。
        
        - 从用户输入中提取与查询相关的元素，并结构化地填充如下格式的JSON。
        - 确保提取的维度、时间、指标等信息遵循指定的枚举和规则。
        - 处理中文分词，以确保各个字段被正确解析。
        - 判断用户是否需要画图
        
        ## 任务详细规范
        - **当用户在多轮对话中添加或者覆盖维度值时:**
            - 严格使用用户提供的字面名称进行附加或者覆盖
            - 禁止基于历史对话补充ID/后缀（如用户说"原始传奇"时不要自动添加为"贪玩-XXXX-原始传奇",正确的应该是“原始传奇”）
            - 禁止自动拆解带分隔符的名称（如"原始传奇"不要拆解为"贪玩-XXXX-原始传奇-XX",正确的应该是“原始传奇”）
            
        
        - **维度筛选与聚合**：依据用户的问题，识别并区分维度筛选和维度聚合。支持的枚举值见下：
          - 维度筛选和聚合：平台、买量/发行、联运渠道、负责人、负责人分组、渠道组、渠道、广告位、游戏类型、合同游戏名、集团游戏、根游戏、主游戏、子游戏、团队、系统、媒体账户、广告1级、广告2级、广告3级、媒体、直播间、直播间对接人、直播间对接人分组、转化类型、深度转化类型、消耗主体
          - 平台的枚举值：贪玩、八九游、掌玩、香港贪玩、菲凡、欢乐时光、AX、香港欢娱
          - 分摊方式枚举值：自然量分摊、直播间分摊、预约分摊
          - 统计口径枚举值：按回流、按根、按子
          - 时间粒度枚举值：聚合、按日、按月、按周。
          - 判断用户的提问是否需要画图，当用户提到“分析”、“趋势”、“占比”、“分布”、“可视化”或者直接指定图表类型时等关键词时，一般都需要画图.在多轮对话中，保持对用户画图需求的上下文追踪。
        
        - **JSON字段说明**：
          - `dimension_filter_name` 和 `dimension_filter_id`: 描述用户的WHERE条件，未明确指定使用维度ID筛选的情况下默认为使用维度名筛选，未明确指定游戏名使用哪个维度进行筛选时默认为使用根游戏名筛选。
          - `dimension`: 描述用户的GROUP BY条件。
          - `time_range`: 时间范围。默认值为“今天”，格式为`YYYY-MM-DD`。
          - `aggregation_time`: 时间粒度。默认“聚合”。
          - `dimension_type`: 统计口径。默认值为“按回流”。
          - `target`: 查询的指标。
          - `apportion`: 分摊方式。可多选。
          - `order_by`: 指定排序字段及方式。
          - `pag`: 标签名字。
          - `draw`字段：仅在需要绘制图表时设置为`true`，否则为`false`。
         
        - **时间与统计**：
          - 当前日期为{{today}}，{{day_of_week_cn}}。
          - 默认时间区间为当天，如未指定以今天为准。
        
        ## 输出格式
        
        结果应为如下的JSON对象：
        
        ```json
        {
          "dimension_filter_name": {"{维度A}": ["{维度名a}"], "{维度B}": ["{维度名b}", "{维度名c}"]},
          "dimension_filter_id": {"{维度A}": ["{维度ID1}", "{维度ID2}"]},
          "dimension": ["{维度D}", "{维度E}"],
          "time_range": ["{开始日期}", "{结束日期}"],
          "aggregation_time": "{时间粒度}",
          "target": ["{指标A}", "{指标B}"],
          "dimension_type": "{统计口径}",
          "apportion": ["{分摊方式}"],
          "order_by": {"{排序字段}": "{DESC/ASC}", "{排序字段}": "{DESC/ASC}"},
          "pag": "{标签名}",
          "draw": {true/false}
        }
        ```
        
        ## 示例
        
        ### 输入示例
        
        用户输入: "我要查询平台的用户注册量和充值金额，并按日拆分，100今日头条渠道，子游戏2，子游戏ID3，广告位名cps和100，广告位ID1，按首日注册数降序排列。"
        
        ### 输入解析
        
        维度筛选描述为“100今日头条渠道，子游戏2，子游戏ID3，广告位名cps和100，广告位ID1”:
        - 因为“100今日头条渠道”和“子游戏2”未指定是维度名字还是ID，默认放在dimension_filter_name；
        - 因为“子游戏ID3”和“广告位ID1”指定了是ID，所以放在dimension_filter_id；
        - 因为“广告位名cps和100”指定了是名字，所以放在dimension_filter_name；
        
        ### 输出示例
        
        ```json
        {
          "dimension_filter_name": {"渠道": ["100今日头条"],"子游戏": ["2"],"广告位": ["cps", "100"]},
          "dimension_filter_id": {"广告位": ["1"],"子游戏": ["3"]},
          "dimension": ["平台"],
          "time_range": ["{{today}}", "{{today}}"],
          "aggregation_time": "按日",
          "target": ["用户注册量", "充值金额"],
          "dimension_type": "按回流",
          "apportion": [],
          "order_by": {"首日注册数": "DESC"},
          "pag": "",
          "draw": false
        }
        ```
        
        ## 注意事项
        
        - 特别注意：维度值的处理必须保持原始形态。即使系统知道"原始传奇"存在多个子版本，只要用户未明确指定，JSON中应原样保留用户提供的名称
        - 确保所有字段遵循给定规则和背景信息的限制。
        - 确保提问包含的信息都被转化为json参数。
        
        下面是提问内容：';


    /**
     * 分析用户的图表类型
     */
    const ANALYSIS_DRAW_PROMPT = '分析用户的提问，判断用户需要绘制的图表类型，并以指定的JSON格式输出结果。
        补充信息：
        维度枚举值：{{dimension_list}}
        指标枚举值：{{target_list}}
        图表枚举值：["饼图", "折线图", "柱状图", "组合图"]
        任务详细规范：
        - 提取信息：确保提取的维度和指标符合给定的枚举值。任何不在枚举值中的词都应被忽略。
        图表判断优先级：
        1. 优先识别用户明确指定的图表类型
        2. 如果未指定图表类型，再根据关键词（如“趋势”）推断。
        3. 始终使用用户指定的图表类型生成JSON输出。
        
        参数提取：
        x轴必须是维度枚举值之一。
        y轴是指标枚举值之一。
        对于组合图，确保一个指标用于柱状图，另一个用于折线图。
        输出格式：
        
        示例1（单指标）：
        {
          "type": "折线图",
          "draw_column": {"x": "日期", "y": "消耗"}
        }
        示例2（多指标）：
        {
          "type": "组合图",
          "draw_column": {"x": "根游戏", "y": {"line":"消耗","bar":"总付费金额"}}
        }
        错误检查：确保最终选择的图表类型和提取的维度、指标符合规则，并提供相应的JSON输出。
        特别注意：请严格按照要求的json格式返回数据。即使是饼图也需要x的维度数据
       
        下面是提问内容：';


    /**
     * 投放总览指标匹配
     */
    const TARGET_PROMPT = '
        给出指定的指标完全匹配，严格按照要求输出 json。
        - 背景：1.ROI表示的是回本率。 2. 流水表示的是总付费金额。
        - 系统指标：{{system_target}}
        - 查询指标：{{query_target}}
        - 排序指标：{{order_target}}
        
        匹配规则：
            查询指标：从系统指标中找出最相关的指标。当且仅当需求中所有指标都无法匹配，则输出空数组
            排序指标：从系统指标中找出与排序指标最相关的指标，必须一对一匹配。当且仅当需求中所有指标都无法匹配，则输出空数组
        输出规则：
            输出格式用json格式。
            对于每个查询指标，列出所有相关的系统指标。
            对于每个排序指标，列出一个最相关的系统指标。
            如果没有找到相关指标，输出空数组。
        
        示例
            系统指标：["消耗","原始消耗","日均消耗","实际消耗","总付费金额","首日回本率","累计回本率"]
            查询指标：["累积消耗", "总消耗", "总流水"]
            排序指标：["累计ROI"]
        
            输出：
            {
                "查询指标": ["消耗","总付费金额"],
                "排序指标": {
                    "累计ROI" : "累计回本率"
                }
        
            }
        
            如果都无法匹配，则输出：
            {
                "查询指标": [],
                "排序指标": []
        
            }
        
        补充：其中"查询指标"输出一个一维数组。"排序指标"输出键值对。
        请严格按照我以上的要求格式输出匹配完成的json';


    /**
     * 路由分发
     */
    const DISPATCH_ROUTE = '请根据用户的提问识别他们想要查看的报表类型。报表包括：
        1.数据总览：包含投放和运营总览，涵盖各类广告投放和运营分析的统计指标。涉及“流水”或“总收入”的查询属于此类。提到“总览”、“注册数”、“用户数”等
        2.付费情况：关注广告投放后的每日付费数据，如回本率、LTV、ARPU、注册数、消耗等。
        3.留存情况：关注每日留存效果，仅当用户提到“留存”、“用户活跃”等才选择此报表。如新增账号留存、付费账号留存。
        4.素材效果：关注广告素材的投放效果。关键字：提到“素材”、“广告效果”等
        
        要求：
        优先级：当指标在多个报表中出现时，选择数据总览，即使提到“消耗”或“回本”等词。
        用户指定“投放总览”、“运营总览”或“数据总览”时，均指“数据总览”。
        用户明确指定报表时，以用户为准。
        用户未明确指定报表时，根据查询指标合理推测。
        根据之前的上下文进行推断
        
        特别注意：
        1.数据总览、付费情况、留存情况都含有注册数和消耗指标。
        2.在连续对话中，若用户未改变查询对象或指标，保持上一次识别的报表类型。
        
        描述不清晰，无法确定时，标记为“未明确报表”。
        输出：严格以 JSON 格式输出，例如：
        {
           "router": "数据总览"
        }
        ';

    const PAYMENT_PARAM_PROMPT = '将用户的自然语言提问转换为特定的JSON格式。
        
        - 从用户输入中提取与查询相关的元素，并结构化地填充如下格式的JSON。
        - 确保提取的维度、时间、指标等信息遵循指定的枚举和规则。
        - 处理中文分词，以确保各个字段被正确解析。
        - 判断用户是否需要画图
        - 对于多轮对话，用户所添加的维度应直接附加在现有列表中，不需推断或修改。
        
        ## 任务详细规范
        -**维度筛选与聚合**：依据用户的问题，识别并区分维度筛选和维度聚合。支持的枚举值见下：
          - 维度筛选和聚合：平台、买量/发行、联运渠道、负责人、负责人分组、渠道组、渠道、广告位、游戏类型、合同游戏名、集团游戏、根游戏、主游戏、子游戏、团队、系统、媒体账户、广告1级、广告2级、广告3级、媒体、直播间、直播间对接人、直播间对接人分组、转化类型、深度转化类型、消耗主体
          - 平台的枚举值：贪玩、八九游、掌玩、香港贪玩、菲凡、欢乐时光、AX、香港欢娱
          - 分摊方式枚举值：自然量分摊、直播间分摊、预约分摊
          - 统计口径枚举值：按回流、按根、按子
          - 时间粒度枚举值：聚合、按日、按月、按周。
          - money_type枚举值：总付费、游戏充值、激励广告。
          - type字段的枚举值: 回本率、LTV、累计付费、每日回本率、每日LTV、每日付费、每日付费人数、每日ARPU、每日付费ARPU、累计付费人数、累计付费率、每日付费率、累计付费ARPU、付费成本、每日付费次数、累计付费次数、累计付费次数成本。
          - 指标类型：仅使用用户明确提到的指标类型。如果用户未指定指标类型，则使用默认值“回本率”。忽略不在枚举中的类型。
          - 判断用户的提问是否需要画图，当用户提到“分析”、“趋势”、“占比”、“分布”、“可视化”或者直接指定图表类型时等关键词时，一般都需要画图.在多轮对话中，保持对用户画图需求的上下文追踪。
        
        - **JSON字段说明**：
          - `dimension_filter_name` 和 `dimension_filter_id`: 描述用户的WHERE条件，未明确指定使用维度ID筛选的情况下默认为使用维度名筛选，未明确指定游戏名使用哪个维度进行筛选时默认为使用根游戏名筛选。
          - `dimension`: 描述用户的GROUP BY条件。
          - `time_range`: 时间范围。默认值为“今天”，格式为`YYYY-MM-DD`。
          - `day_reg_uid_count`: 注册数筛选，默认是0。
          - `rm_divide`: 是否去除分成。0或者1，默认0。
          - `blocked`: 是否屏蔽当日。0或者1，默认0。
          - `day_target`: 天数的指标。格式是：特指某一天则day_{number}；描述一个范围天数则： day_{number1}_{number2}，其中{number1}<{number2}。默认值是“day_1_7”
          - `money_type`: 使用money_type枚举值，默认总付费。
          - `type`: 描述指标的类型，可多选，type的值必须在指标类型枚举值里面,任何不在此枚举值中的类型将被忽略。默认值为“回本率”。
          - `aggregation_time`: 时间粒度。默认“聚合”。
          - `dimension_type`: 统计口径。默认值为“按回流”。
          - `apportion`: 分摊方式。可多选。
          - `order_by`: 指定排序字段及方式。
          - `pag`: 标签名字。
          - `true_cost`: 0或者1，仅当用户提到“实际消耗”时，true_cost才需要设置为1。默认0。
          - `draw`字段：仅在需要绘制图表时设置为`true`，否则为`false`。
        
        - **时间与统计**：
          - 当前日期为{{today}}，{{day_of_week_cn}}。
          - 默认时间区间为当天，如未指定以今天为准。
        
        ## 输出格式
        
        结果应为如下的JSON对象：
        
        ```json
        {
          "dimension_filter_name": {"{维度A}": ["{维度名a}"], "{维度B}": ["{维度名b}", "{维度名c}"]},
          "dimension_filter_id": {"{维度A}": ["{维度ID1}", "{维度ID2}"]},
          "dimension": ["{维度D}", "{维度E}"],
          "time_range": ["{开始日期}", "{结束日期}"],
          "day_reg_uid_count":0,
          "rm_divide":"{0/1}",
          "blocked":"{0/1}",
          "day_target":["{单天指标A}", "{范围天指标B}"],
          "money_type": "{money_typeA}"
          "aggregation_time": "{时间粒度}",
          "type":["{指标类型A}","{指标类型B}","{指标类型C}"]
          "dimension_type": "{统计口径}",
          "apportion": ["{分摊方式}"],
          "order_by": {"{排序字段}": "{DESC/ASC}", "{排序字段}": "{DESC/ASC}"},
          "pag": "{标签名}",
          "true_cost":"{0/1}",
          "draw": {true/false}
        }
        ```
        
        ## 示例
        
        ### 输入示例
        
        用户输入: "我要查询平台今天的1到30天的LTV、累计付费、回本率，并按日拆分，100今日头条渠道，子游戏2，子游戏ID3，广告位名cps和100，广告位ID1，按注册数降序排列。"
        
        ### 输入解析

        指标类型描述为：LTV、累计付费、回本率。
        天指标为：1到30天。
        排序要按注册数排序。
        维度筛选描述为“100今日头条渠道，子游戏2，子游戏ID3，广告位名cps和100，广告位ID1”:
        - 因为“100今日头条渠道”和“子游戏2”未指定是维度名字还是ID，默认放在dimension_filter_name；
        - 因为“子游戏ID3”和“广告位ID1”指定了是ID，所以放在dimension_filter_id；
        - 因为“广告位名cps和100”指定了是名字，所以放在dimension_filter_name；
        
        ### 输出示例

        
        ```json
        {
          "dimension_filter_name": {"渠道": ["100今日头条"],"子游戏": ["2"],"广告位": ["cps", "100"]},
          "dimension_filter_id": {"广告位": ["1"],"子游戏": ["3"]},
          "dimension": ["平台"],
          "time_range": ["{{today}}", "{{today}}"],
          "day_reg_uid_count": 0,
          "rm_divide":0,
          "blocked":0,
          "day_target":["day_1_30"],
          "money_type": "总付费"
          "aggregation_time": "按日",
          "type":["LTV","累计付费","回本率"],
          "dimension_type": "按回流",
          "apportion": [],
          "order_by": {"注册数": "DESC"},
          "pag": "",
          "true_cost": 0,
          "draw": false
        }
        ```
        
        ## 注意事项
        
        - 确保所有字段遵循给定规则和背景信息的限制。
        - 确保提问包含的信息都被转化为json参数。
        
        下面是提问内容：';

    const RETAIN_PARAM_PROMPT = '将用户的自然语言提问转换为特定的JSON格式。
        
        - 从用户输入中提取与查询相关的元素，并结构化地填充如下格式的JSON。
        - 确保提取的维度、时间、指标等信息遵循指定的枚举和规则。
        - 处理中文分词，以确保各个字段被正确解析。
        - 判断用户是否需要画图
        - 对于多轮对话，用户所添加的维度应直接附加在现有列表中，不需推断或修改。
        
        ## 任务详细规范
          -**维度筛选与聚合**：依据用户的问题，识别并区分维度筛选和维度聚合。支持的枚举值见下：
          - 维度筛选和聚合：平台、买量/发行、平台归属、集团归属、联运渠道、负责人、负责人分组、渠道组、渠道、广告位、游戏类型、合同游戏名、集团游戏、根游戏、主游戏、子游戏、团队、系统、媒体账户、广告1级、广告2级、广告3级、媒体、直播间、直播间对接人、直播间对接人分组、转化类型、深度转化类型、消耗主体
          - 平台的枚举值：贪玩、八九游、掌玩、香港贪玩、菲凡、欢乐时光、AX、香港欢娱
          - 统计口径枚举值：按回流、按根、按子
          - 时间粒度枚举值：聚合、按日、按月、按周。
          - 判断用户的提问是否需要画图，当用户提到“分析”、“趋势”、“占比”、“分布”、“可视化”或者直接指定图表类型时等关键词时，一般都需要画图.在多轮对话中，保持对用户画图需求的上下文追踪。
        
        - **JSON字段说明**：
          - `dimension_filter_name` 和 `dimension_filter_id`: 描述用户的WHERE条件，未明确指定使用维度ID筛选的情况下默认为使用维度名筛选，未明确指定游戏名使用哪个维度进行筛选时默认为使用根游戏名筛选。
          - `dimension`: 描述用户的GROUP BY条件。
          - `time_range`: 时间范围。默认值为“今天”，格式为`YYYY-MM-DD`。
          - `reg_uid_count`: 注册数筛选，默认是0。
          - `blocked`: 是否屏蔽当日。0或者1，默认0。
          - `day_target`: 天数的指标。格式是：特指某一天则rate_day_stay_{number}；描述一个范围天数则： rate_day_stay_{number1}_{number2}，其中{number1}<{number2}。默认值是“rate_day_stay_2_30”
          - `data_type`: 数据类型。可多选。必须在数据类型枚举值里面，任何不在此枚举值中的类型将被忽略。枚举值：留存率、留存数、有效留存率、留存成本、2日有效留存。默认值：留存率。
          - `retain_type`: 描述留存的类型，retain_type的值必须在枚举值里面,任何不在此枚举值中的类型将被忽略。留存类型的枚举值：
                新增账号登录留存、付费账号登录留存、付费账号付费留存、非付费账号登录留存、付费账号留存(注册)、付费留存/注册、首日新增新付费用户留存、
                新增设备登录留存、付费设备登录留存、付费设备付费留存、非付费设备登录留存、付费设备留存(注册)、付费设备留存/注册设备、首日新增新付费设备留存。
                默认值：新增账号登录留存
          - `aggregation_time`: 时间粒度。默认“聚合”。
          - `dimension_type`: 统计口径。默认值为“按回流”。
          - `public_beta_date_amend`: 是否公测日期修正，`true` or `false` 默认`false`。 
          - `order_by`: 指定排序字段及方式。
          - `pag`: 标签名字。
          - `draw`字段：仅在需要绘制图表时设置为`true`，否则为`false`。
        
        - **时间与统计**：
          - 当前日期为{{today}}，{{day_of_week_cn}}。
          - 默认时间区间为当天，如未指定以今天为准。
        
        ## 输出格式
        
        结果应为如下的JSON对象：
        
        ```json
        {
          "dimension_filter_name": {"{维度A}": ["{维度名a}"], "{维度B}": ["{维度名b}", "{维度名c}"]},
          "dimension_filter_id": {"{维度A}": ["{维度ID1}", "{维度ID2}"]},
          "dimension": ["{维度D}", "{维度E}"],
          "time_range": ["{开始日期}", "{结束日期}"],
          "reg_uid_count":"{注册数筛选}",
          "blocked":"{0/1}",
          "day_target":["{单天指标A}", "{范围天指标B}"],
          "data_type": ["{数据类型A}","{数据类型B}"]
          "aggregation_time": "{时间粒度}",
          "retain_type":"{留存类型}"
          "dimension_type": "{统计口径}",
          "order_by": {"{排序字段}": "{DESC/ASC}", "{排序字段}": "{DESC/ASC}"},
          "public_beta_date_amend": {true/false},
          "pag": "{标签名}",
          "draw": {true/false}
        }
        ```
        
        ## 示例
        
        ### 输入示例
        
        用户输入: "我要查询平台今天的1到30天的新增账号登录留存，并按日拆分，注册数要大于150，100今日头条渠道，子游戏2，子游戏ID3，广告位名cps和100，广告位ID1，按注册数降序排列。"
        
        ### 输入解析

        留存类型描述为：新增账号登录留存
        天指标为：1到30天
        注册数过滤是大于100
        排序要按注册数排序
        数据类型没有指定，默认留存率
        维度筛选描述为“100今日头条渠道，子游戏2，子游戏ID3，广告位名cps和100，广告位ID1”:
        - 因为“100今日头条渠道”和“子游戏2”未指定是维度名字还是ID，默认放在dimension_filter_name；
        - 因为“子游戏ID3”和“广告位ID1”指定了是ID，所以放在dimension_filter_id；
        - 因为“广告位名cps和100”指定了是名字，所以放在dimension_filter_name；
        
        ### 输出示例

        
        ```json
        {
          "dimension_filter_name": {"渠道": ["100今日头条"],"子游戏": ["2"],"广告位": ["cps", "100"]},
          "dimension_filter_id": {"广告位": ["1"],"子游戏": ["3"]},
          "dimension": ["平台"],
          "time_range": ["{{today}}", "{{today}}"],
          "reg_uid_count": 150,
          "blocked":0,
          "day_target":["rate_day_stay_1_30"],
          "data_type": ["留存率"],
          "aggregation_time": "按日",
          "retain_type":"新增账号登录留存"
          "dimension_type": "按回流",
          "order_by": {"注册数": "DESC"},
          "public_beta_date_amend": false,
          "pag": "",
          "draw": false
        }
        ```
        
        ## 注意事项
        
        - 确保所有字段遵循给定规则和背景信息的限制。
        - 确保提问包含的信息都被转化为json参数。
        
        下面是提问内容：';



    const INTENTION = '你的任务是：
        1. 根据当前及历史对话内容，识别用户最新问题的意图。
        2. 判断是否需要进行数据分析（如数据比较、计算、模式识别或图表制作），若需要则在输出的 JSON 中将 `analyse` 字段设为 `"是"`，否则为 `"否"`。
        3. 判断是否需要查询或检索数据（如“按天、按周、按月查看”或“某某游戏渠道数据如何”等），若需要则在输出的 JSON 中将 `query` 字段设为 `"是"`，否则为 `"否"`。
        4. 将结果以固定格式 JSON 返回，包含以下字段：
           ```json
           {
             "analyse": "是/否",
             "query": "是/否",
             "summary": "{对用户最新问题的简要概述与目标说明}"
           }
           ```
        
        **关于时间与统计：**
        - 当前日期为 `{{today}}`（`{{day_of_week_cn}}`）
        - 用户如果使用相对时间提问（如：上周，最近两个月等），请在 `summary` 中翻译为绝对时间：xx年xx月xx日-xx年xx月xx日。
        
        **特别说明（若 `analyse` = `"是"`）：**
        - 在 `summary` 中必须说明需要绘制的图表类型（如“折线图”、“柱状图”、“饼图”等）。
        - 请提供图表属性配置的建议，如图例名称、图表标题、横纵坐标对应字段等。
        - 示例：
          - 图表类型：折线图
          - 图例名称：注册用户趋势
          - 图表标题：某某时间段注册用户数对比
          - 横坐标字段：日期
          - 纵坐标字段：注册用户数
        
        **示例参考：**
        - 若用户请求“按天/按周/按月查看数据”，通常只需从数据中提取信息，故 `query` = `"是"`，`analyse` = `"否"`。
        - 若用户请求“查看数据趋势”或“比较近期数据并画图”，通常涉及分析，故 `analyse` = `"是"`。
        
        **输出格式要求：**
        - 只返回下述 JSON 结构，不要添加多余文字或说明：
          ```json
          {
            "analyse": "是/否",
            "query": "是/否",
            "summary": "..."
          }
          ```
            ';

}