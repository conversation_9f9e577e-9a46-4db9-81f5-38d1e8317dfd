<?php

namespace App\Service\DataBot;

use App\Struct\RedisCache;
use RedisException;

/**
 * 处理data bot 的session信息。
 */
class DataBotSession
{

    /**
     * 用户 id
     *
     * @var integer
     */
    protected $user_id;


    /**
     * 来源类型。0=微信，1=飞书
     *
     * @var int
     */
    protected $type = 0;

    public function __construct($user_id, $type = 0)
    {
        $this->user_id = $user_id;
        $this->type = $type;
    }


    public function genDataBotSessionKey($route_name, $type, $user_id)
    {
        return 'wechat_session_list_overview_' . $type . $user_id;
        switch ($route_name) {
            case DataBotConfig::ROUTE_NAME_RETAIN:
                return 'wechat_session_list_retain_' . $type . $user_id;
            case DataBotConfig::ROUTE_NAME_PAYMENT:
                return 'wechat_session_list_payment_' . $type . $user_id;
            case DataBotConfig::ROUTE_NAME_OVERVIEW:
            default:
                return 'wechat_session_list_overview_' . $type . $user_id;
        }
    }

    public function genRouterNameSessionKey($type, $user_id)
    {
        return "data_bot_router_name_$type-" . $user_id;
    }

    public function genDispatchRouteSessionKey($type, $user_id)
    {
        return "data_bot_dispatch_route_$type-" . $user_id;
    }


    /**
     * 历史记录入队列
     *
     * @param $list_key
     * @param $value
     * @return void
     * @throws RedisException
     */
    public function addToSessionList($list_key, $value)
    {
        $redis = RedisCache::getInstance();
        // 将元素推入列表的右侧

        $value = serialize($value);
        $redis->rPush($list_key, $value);

        // 设置列表的过期时间
        $redis->expire($list_key, DataBotConfig::SESSION_EXPIRE_TIME);

        // 获取列表中的所有元素
        $list = $redis->lRange($list_key, 0, -1);

        $session_length = count($list);
        $length = 0;
        foreach ($list as &$item) {
            $item = unserialize($item);
            $length += strlen($item[0]['content']);
            // 检查列表长度是否超过最大值
            if ($length > DataBotConfig::SESSION_MAX_LENGTH_STR && $session_length > 1) {
                // 移除列表左侧的第一个元素
                $redis->lPop($list_key);
                break;
            }
        }
    }

    /**
     * 出队 这里要把队列的所有元素拿出来
     *
     * @param $list_key
     * @return array
     * @throws RedisException
     */
    public function getSessionList($list_key)
    {
        $redis = RedisCache::getInstance();

        // 获取列表中的所有元素
        $list = $redis->lRange($list_key, 0, -1);

        foreach ($list as &$item) {
            $item = unserialize($item);
        }

        return $list;
    }

    /**
     *
     * 添加历史记录
     *
     * @param $route_name
     * @param $user_query
     * @param $assistant
     * @param array $tag_data
     * @param int $type
     * @param mixed $user_id
     * @return void
     * @throws RedisException
     */
    public function addHistory($route_name, $user_query, $assistant, array $tag_data = [], int $type = 0, $user_id = 0)
    {
        $user_query .= "\n查询的报表：$route_name";
        //判断是否有标签，有的话需要加入标签内容
        if ($tag_data) {
            $json_data = json_encode($tag_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $user_query .= "\n标签'{$tag_data['name']}'内容如下：\n$json_data";
        }
        $list_key = $this->genDataBotSessionKey($route_name, $type, $user_id);
        $this->addToSessionList($list_key, [
            [
                'role'    => 'user',
                'content' => $user_query
            ],
            [
                'role'    => 'assistant',
                'content' => "```json\n" . json_encode($assistant, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n``` ",
            ]
        ]);
    }


    /**
     * @param $user_query
     * @param $assistant
     * @param $type
     * @param $user_id
     * @return void
     * @throws RedisException
     */
    public function addDispatchRouteHistory($user_query, $assistant, $type, $user_id)
    {
        $list_key = $this->genDispatchRouteSessionKey($type, $user_id);
        $this->addToSessionList($list_key, [
            [
                'role'    => 'user',
                'content' => $user_query
            ],
            [
                'role'    => 'assistant',
                'content' => "```json\n" . json_encode($assistant, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n``` ",
            ]
        ]);
    }


    public function cleanSession()
    {
        $user_id = $this->user_id;
        $type = $this->type;

        $key1 = $this->genDataBotSessionKey('', $type, $user_id);
        $key2 = $this->genDispatchRouteSessionKey($type, $user_id);
        $key3 = self::genPandasAISessionKey($type, $user_id);
        $key4 = self::genAllConversationKey($type, $user_id);
        $key5 = $this->genRouterNameSessionKey($type, $user_id);
        RedisCache::getInstance()->del([$key1, $key2, $key3, $key4, $key5]);

    }


    static public function genPandasAISessionKey($type, $user_id)
    {
        return 'pandas_ai_' . $type . $user_id;
    }

    /**
     *
     * 添加PandasAI的会话历史
     *
     * @param $type
     * @param $user_id
     * @param $question
     * @param $answer
     * @return void
     * @throws RedisException
     */
    static public function addPandasAIHistory($type, $user_id, $question, $answer)
    {
        $key = self::genPandasAISessionKey($type, $user_id);
        $redis = RedisCache::getInstance();
        $session_list = [];
        $list_str = $redis->hGet($key, 'session_list');
        if ($list_str) {
            $session_list = unserialize($list_str);
        }

        // 检查列表长度是否超过最大值
        if (strlen($list_str) > DataBotConfig::SESSION_MAX_LENGTH_STR) {
            array_shift($session_list); // 出队一个元素
        }
        // 入队
        $session_list[] = ['question' => $question, 'answer' => $answer];
        $redis->hSet($key, 'session_list', serialize($session_list));

        // 会话时长保持两个小时
        $redis->expire($key, DataBotConfig::SESSION_EXPIRE_TIME);
    }

    /**
     * pandasAI的数据文件
     *
     * @param $type
     * @param $user_id
     * @param $filename
     * @param string $header_str
     * @return void
     * @throws RedisException
     */
    static public function addSessionFile($type, $user_id, $filename, string $header_str = '')
    {
        if ($header_str) {
            $assistant = "数据：\n[表头：$header_str]\n[数据：省略]\n";
            DataBotSession::addAllConversationHistory($type, $user_id, '用户从后台导出一份数据', $assistant);
        }


        $key = self::genPandasAISessionKey($type, $user_id);
        $redis = RedisCache::getInstance();

        $old_filename = $redis->hGet($key, 'filename');
        // 把旧会话内容(pandasAI)删了
        $redis->del($key);
        // 先把旧文件删了
        try {
            @unlink($old_filename);
        } catch (\Exception $exception) {

        }
        $redis->hSet($key, 'filename', $filename);
        // 会话时长保持两个小时
        $redis->expire($key, DataBotConfig::SESSION_EXPIRE_TIME);
    }

    /**
     * @param $type
     * @param $user_id
     * @return false|mixed|\Redis|string
     * @throws RedisException
     */
    static function getSessionFilename($type, $user_id)
    {
        $key = self::genPandasAISessionKey($type, $user_id);
        $redis = RedisCache::getInstance();
        return $redis->hGet($key, 'filename');
    }

    static function getPandasAIHistory($type, $user_id)
    {
        $key = self::genPandasAISessionKey($type, $user_id);
        $redis = RedisCache::getInstance();
        $session_list = [];
        $list_str = $redis->hGet($key, 'session_list');
        if ($list_str) {
            $session_list = unserialize($list_str);
        }

        return $session_list;
    }

    static public function formatPandasAISessionList($session_list)
    {
        $message_list = [];
        foreach ($session_list as $item) {
            $user_tmp = [
                'role'    => "user",
                'content' => $item['question']
                //                'content' => [
                //                    ['type' => 'text', 'text' => $item['question']],
                //                ]
            ];
            $message_list[] = $user_tmp;

            $assistant_tmp = [
                'role'    => "assistant",
                'content' => $item['answer'],
            ];
            $message_list[] = $assistant_tmp;
        }

        return $message_list;
    }

    static public function genAllConversationKey($type, $user_id)
    {
        return 'data_bot_conversation_' . $type . $user_id;
    }

    /**
     * 存储所有查询会话和pandasAI会话的所有历史。
     * 用来做意图判断
     *
     * @param $type
     * @param $user_id
     * @param $question
     * @param $answer
     * @return void
     * @throws RedisException
     */
    static public function addAllConversationHistory($type, $user_id, $question, $answer)
    {
        $key = self::genAllConversationKey($type, $user_id);
        $redis = RedisCache::getInstance();
        $session_list = [];
        $list_str = $redis->get($key);
        if ($list_str) {
            $session_list = unserialize($list_str);
        }

        // 检查列表长度是否超过最大值
        if (strlen($list_str) > DataBotConfig::SESSION_MAX_LENGTH_STR) {
            array_shift($session_list); // 出队一个元素
        }
        // 入队
        $session_list[] = ['question' => $question, 'answer' => $answer];
        $redis->set($key, serialize($session_list));

        // 会话时长保持两个小时
        $redis->expire($key, DataBotConfig::SESSION_EXPIRE_TIME);
    }

    /**
     * 查询所有查数会话和pandasAI会话的所有历史。
     * @param $type
     * @param $user_id
     * @return array|mixed
     * @throws RedisException
     */
    static function getAllConversationHistory($type, $user_id)
    {
        $key = self::genAllConversationKey($type, $user_id);
        $redis = RedisCache::getInstance();
        $session_list = [];
        $list_str = $redis->get($key);
        if ($list_str) {
            $session_list = unserialize($list_str);
        }

        return $session_list;
    }

    static public function formatAllConversationList($session_list)
    {
        $str = "";
        foreach ($session_list as $item) {
            $str .= "user: {$item['question']}\n";
            $str .= "assistant: {$item['answer']}\n";
        }

        return $str;
    }

    static public function formatAllConversationListDataQueryAnswer($output)
    {
        $filter_str = $output['dimension_filter'];
        $draw_data = $output['draw_data'];

        $assistant = $filter_str . "\n";
        $assistant .= "数据：\n";

        if (!$draw_data[0]['data_list']) {
            $assistant .= "[表头：空]";
            $assistant .= "[数据：空]";
        } else {
            $table_list = $draw_data[0];
            $header = implode(',', $table_list['column_list']);
            if (count($table_list['data_list']) > 1) {
                $data = implode(',', $table_list['data_list'][1]);
            } else {
                $data = implode(',', $table_list['data_list'][0]);
            }
            $assistant .= "[表头：$header]\n";
            $assistant .= "[数据：$data]\n";
        }

        return $assistant;
    }


    /**
     * 添加参数提取的上下文。只有user的，没有回复的消息
     *
     * @param $user_id
     * @param $channel_type
     * @param $user_query
     * @return void
     * @throws RedisException
     */
    static public function addParamSessionByUser($user_id, $channel_type, $user_query)
    {
        $data_bot_session = new DataBotSession($user_id, $channel_type);
        $list_key = $data_bot_session->genDataBotSessionKey('', $channel_type, $user_id);
        $data_bot_session->addToSessionList($list_key, [['role' => 'user', 'content' => $user_query]]);
    }


}