<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\Honor\Oauth2Model;
use App\Param\MediaAccountInfoParam;

class HonorService
{
    public function addAccount($platform, $account_id, $account_name, $app_key, $secret, $company)
    {
        if (empty($platform) || empty($account_id) || empty($account_name) || empty($app_key) || empty($secret) || empty($company)) {
            throw new AppException('请输入所有参数');
        }

        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;

        $service = new MediaService();

        /**
         * media_account表字段对应，可查询后调用接口
         * 'account_id' => $account_id
         * 'account_name' => $account_name
         * 'majordomo_name' => $app_key 客户端ID 即client_id
         * 'wechat_account_name' => $secret 密钥 即client_secret
         */
        $model = new Oauth2Model();
        $access_token_info = $model->accessToken($app_key, $secret);

        return $service->saveAuthAccount(new MediaAccountInfoParam([
            'media_type' => MediaType::HONOR,
            'platform' => $platform,
            'account_id' => $account_id,
            'account_name' => $account_name,
            'access_token' => $access_token_info['access_token'],
            'access_token_expires' => time() + $access_token_info['expires_in'],
            'refresh_token' => '',
            'refresh_token_expires' => 0,
            'majordomo_name' => $app_key,
            'wechat_account_name' => $secret,
            'company' => $company,
        ]), $creator_id, $creator_name);
    }
}
