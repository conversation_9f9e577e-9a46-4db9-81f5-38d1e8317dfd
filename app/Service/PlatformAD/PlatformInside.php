<?php
/**
 * Created by Php<PERSON>torm.
 * Time: 10:52
 */

namespace App\Service\PlatformAD;

use App\Constant\AliOssMap;
use App\Constant\ConvertSourceType;
use App\Constant\MediaType;
use App\Constant\MixType;
use App\Constant\TencentOSS;
use App\Exception\AppException;
use App\Model\RedisModel\AutoIncrementIdModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Zeda\GameAPKModel;
use App\Model\SqlModel\Zeda\GamePackModel;
use App\Model\SqlModel\Zeda\LDYFileModel;
use App\Model\SqlModel\Zeda\LDYModel;
use App\Model\SqlModel\Zeda\SiteLdyLogModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\ADMonetization\ADMonetizationGroupParam;
use App\Param\ADMonetization\ADMonetizationPlanParam;
use App\Param\LDYListParam;
use App\Param\AgentParam;
use App\Param\APKListParam;
use App\Param\SiteConfigParam;
use App\Param\SiteGroupEditParam;
use App\Param\SiteLogParam;
use App\Utils\ALIOSSTool;
use App\Utils\TencentCdnTool;
use App\Utils\TencentCOSTool;
use Common\EnvConfig;

class PlatformInside extends AbstractPlatform
{
    public function addAgent(AgentParam $param, $username)
    {
        $agent_id_model = new AutoIncrementIdModel();
        $agent_id = $agent_id_model->getNextId(AutoIncrementIdModel::AGENT_ID, $param->platform);
        // 自然量渠道 跳过
        if ($agent_id === 10000) {
            $agent_id = $agent_id_model->getNextId(AutoIncrementIdModel::AGENT_ID, $param->platform);
        }

        return ['agent_id' => $agent_id];
    }

    public function editAgent(AgentParam $param, $username)
    {
        return ['agent_id' => $param->agent_id];
    }

    public function addSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        if (!$this->checkGameApkIsUpload($param)) {
            throw new AppException('不存在该游戏的母包，请先上传母包');
        }

        $site_id_model = new AutoIncrementIdModel();
        $site_id = $site_id_model->getNextId(AutoIncrementIdModel::SITE_ID, $param->platform);
        // 自然量渠道 跳过
        if ($site_id === 10000) {
            $site_id = $site_id_model->getNextId(AutoIncrementIdModel::SITE_ID, $param->platform);
        }

        $download_url = '';
        if ($param->game_type === '安卓' && $param->game_pack === 1) {
            (new GamePackModel())->addMany([
                'platform' => $this->platform,
                'media_type' => $param->media_type,
                'game_id' => $param->game_id,
                'agent_id' => $param->agent_id,
                'site_id' => $site_id,
                'convert_source_type' => $param->convert_source_type,
                'creator' => $username,
                'create_time' => time(),
            ]);
            $download_url = $this->getAPKURL(
                $param->platform,
                $param->game_id,
                $param->media_type,
                $site_id
            );
        } else if ($param->game_type === 'IOS') {
            $download_url = $this->getIOSURL($param->appid);
        }
        return [
            'site_id' => $site_id,
            'call_back' => $this->getActionTrackURL($param, ['site_id' => $site_id]),
            'download_url' => $download_url,
            'display_track_url' => $this->getDisplayTrackURL($param, ['site_id' => $site_id]),
            'media_site_id' => $site_id,
        ];
    }

    public function editSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        $download_url = '';
        if ($param->game_type === '安卓') {
            // 切换过游戏 重新打包 或 切换过打包选项
            if ($param->game_pack === 1
                && (($param->ori_game_id > 0 && $param->game_id !== $param->ori_game_id)
                || ($param->ori_game_pack === 0 && $param->ori_game_pack !== $param->game_pack))
            ) {
                $this->setAPK(['site_id'], $param->game_id, '', 'site', $param->media_type, $username);
            }
            $download_url = $this->getAPKURL(
                $param->platform,
                $param->game_id,
                $param->media_type,
                $param->site_id
            );
        } else if ($param->game_type === 'IOS') {
            $download_url = $this->getIOSURL($param->appid);
        }
        return [
            'site_id' => $param->site_id,
            'call_back' => $this->getActionTrackURL($param, ['site_id' => $param->site_id]),
            'download_url' => $download_url,
            'display_track_url' => $this->getDisplayTrackURL($param, ['site_id' => $param->site_id]),
            'media_site_id' => $param->site_id,
        ];
    }

    public function groupEditSite(SiteGroupEditParam $param, $username)
    {
        // 外面已编辑数据库
    }

    public function updateUptState($agent_id, $site_ids, $upt_state, $username)
    {
        (new V2DimSiteIdModel())->editInSiteId($this->platform, $site_ids, ['upt_state' => $upt_state]);
    }

    public function getDataDetail($agent_id, $site_id, $start_date, $end_date, $username)
    {
    }

    public function detailExport($agent_id, $start_date, $end_date, $username)
    {
    }

    public function getAPKList(APKListParam $param, $username)
    {
        $data = (new GamePackModel())->getList($param);
        foreach ($data['list'] as $apk) {
            $apk->download_url = $this->getAPKURL($apk->platform, $apk->game_id, $apk->media_type, $apk->site_id);
            $apk->addtime = date('Y-m-d H:i:s', $apk->create_time);
            $apk->edittime = date('Y-m-d H:i:s', $apk->update_time);
        }
        return $data;
    }

    public function getAPKStateList($site_ids, $username)
    {
        $list = (new GamePackModel())->getAllInSiteId($this->platform, $site_ids);
        return array_map(function ($data) {
            return (array)$data;
        }, $list->toArray());
    }

    public function setAPK($site_ids, $game_id, $version, $type, $media_type, $username)
    {
        $data = [];
        if ($type === 'site') {
            $site_list = (new SiteModel())->getAllInSite($this->platform, $site_ids);
            $cdn_uuid = uniqid($type, true);
            $cdn_type = 'FILE';
        } else if ($type === 'game') {
            $site_list = (new SiteModel())->getAllByGameId($this->platform, $game_id);
            $cdn_uuid = uniqid("game-$game_id", true);
            $cdn_type = 'FOLDER';
        } else {
            throw new AppException('类型错误');
        }

        foreach ($site_list as $site) {
            $data[] = [
                'platform' => $this->platform,
                'media_type' => $media_type,
                'game_id' => $game_id,
                'agent_id' => $site->agent_id,
                'site_id' => $site->site_id,
                'convert_source_type' => $site->convert_source_type,
                'cdn_uuid' => $cdn_uuid,
                'cdn_type' => $cdn_type,
                'state' => $media_type === 0 ? 1 : 0,
                'ver' => $version,
                'creator' => $username,
                'create_time' => time(),
            ];
        }
        return (new GamePackModel())->addMany($data);
    }

    public function editAPK($ids, $data, $username)
    {
        $update_data = [
            'editor' => $username,
            'update_time' => time(),
        ];
        if (isset($data['ver']) && !empty($data['ver'])) {
            $update_data['ver'] = $data['ver'];
        }

        if (isset($data['top']) && $data['top'] > 0) {
            $update_data['top'] = $data['top'];
        }

        return (new GamePackModel())->editInID($ids, $update_data);
    }

    public function switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username)
    {
        $site_list = (new SiteModel())->getAllInSite($this->platform, $site_ids);
        if ($is_game !== 1 || $is_adid !== 1) {
            if (empty($option)) {
                throw new AppException('option内容不能为空');
            }
            foreach ($option as $key => $item) {
                $item = json_decode($item, true);
                $option[$key] = $item;
                if ($is_game !== 1) {
                    if (!isset($item['game_id']) || !$item['game_id']) {
                        throw new AppException('game_id不能为空');
                    }
                }

                if ($is_adid !== 1) {
                    if (!isset($item['adid']) || !$item['adid']) {
                        throw new AppException('adid不能为空');
                    }
                }
            }
        }

        $error_msg = '';
        $data = [];
        $game_cache = [];
        $site_ldy_log_model = new SiteLdyLogModel();
        foreach ($site_list as $site) {
            $site_id = $site->site_id;
            if ($is_game === 1 || $is_adid === 1) {
                $old_data = $site_ldy_log_model->getLastByPlatformSite($this->platform, $site_id);
                if (empty($old_data)) {
                    $error_msg .= "site_id:{$site_id}:历史找不到该广告的记录，无法空切;";
                    continue;
                }
                if ($is_game === 1 && $is_adid === 1) {
                    $new_ad_list = $old_data->ldy_ad_list;
                } else {
                    $old_ad_list = json_decode($old_data->ldy_ad_list, true);
                    $old_count = count($old_ad_list['href']);
                    $new_count = count($option);
                    if ($old_count > $new_count) {
                        $error_msg .= "site_id:{$site_id}:历史轮数为{$old_count}，当前轮数为{$new_count};历史轮数大于当前轮数，无法空切;";
                        continue;
                    }
                    if ($new_count > 2) {
                        $error_msg .= "site_id:{$site_id}:当前轮数为{$new_count};当前轮数大于2，无法空切;";
                        continue;
                    }
                    if ($old_count < 1) {
                        $error_msg .= "site_id:{$site_id}:历史轮数为{$old_count};历史轮数小于1，无法空切;";
                        continue;
                    }
                    if ($is_game === 1) {
                        // 只空切游戏，用上一次的游戏，这次的创意
                        foreach ($option as $k => $v) {
                            $option[$k]['game_id'] = $old_ad_list['href'][$k]['game_id'];
                        }

                    }
                    if ($is_adid === 1) {
                        foreach ($option as $k => $v) {
                            $option[$k]['adid'] = $old_ad_list['href'][$k]['adid'];
                        }
                    }
                    $new_ad_list = json_encode(['href' => $option]);
                }
            } else {
                //正常切
                //检查安卓游戏的当前广告位是否有打包。没有的报错返回,当site_id下有一个game_id没打包，则这个site_id不会去save落地页
                foreach ($option as $item) {
                    if (!isset($game_cache["$this->platform-{$item['game_id']}"])) {
                        $game_cache["$this->platform-{$item['game_id']}"] = (new V2DimGameIdModel())->getDataByGameId($this->platform, $item['game_id']);
                    }
                    $game_info = $game_cache["$this->platform-{$item['game_id']}"];
                    if ($game_info->os === '安卓') {
                        $game_pack_info = (new GamePackModel())->getDataByPlatformSiteGameId($this->platform, $site_id, $item['game_id']);
                        if (empty($game_pack_info)) {
                            $error_msg .= "game_id:{$item['game_id']},site_id:{$site_id}:请先完成打包;";
                            // 调出两层 运行下一个site
                            continue 2;
                        }
                    }
                }
                $new_ad_list = json_encode(['href' => $option]);
            }
            $data[] = [
                'platform' => $this->platform,
                'agent_id' => $site->agent_id,
                'site_id' => $site_id,
                'ldy_ad_list' => $new_ad_list,
                'create_time' => date('Y-m-d H:i:s'),
                'rsync_time' => $rsync_time,
                'creator' => $username
            ];
        }

        $site_ldy_log_model->addMany($data);
        if (!empty($error_msg)) {
            throw new AppException($error_msg);
        }
    }

    public function switchGame($agent_ids, $ori_game_id, $game_id, $username)
    {
    }

    public function getCode($site_id, $game_id, $username)
    {
        $site_info = (new SiteModel())->getDataByPlatformSiteId($this->platform, $site_id);
        return [
            'codeinfo' => [
                'site_id' => $site_id,
                'adtype' => 1,
            ],
            'domainArr0' => [
                'name' => '默认落地页推广链接',
                'domain' => [
                    $this->getSiteLDYUrl($site_info->agent_id, $site_info->site_id),
                ],
            ],
        ];
    }

    public function getSiteLDYUrl($agent_id, $site_id, $base_url = '')
    {
        return AliOssMap::CDN_LDY_DOMAIN . AliOssMap::LDY_DIR . "/code/{$this->platform}/$agent_id/$site_id.html";
    }

    public function getSiteLogs(SiteLogParam $param, $username)
    {
        $data = (new SiteLdyLogModel())->getList($param);

        foreach ($data['list'] as $log) {
            $log->edit_time = $log->create_time;
            $log->author = $log->creator;
            $log->preview = $this->getSiteLDYUrl($log->agent_id, $log->site_id);
            $ldy_ad_list = json_decode($log->ldy_ad_list, true);
            $log->adidshow = $ldy_ad_list['href'];
            foreach ($log->adidshow as &$adidshow) {
                $adidshow['adname'] = "落地页id-{$adidshow['adid']}";
                $adidshow['game_name'] = "游戏id-{$adidshow['game_id']}";
                $adidshow['url'] = AliOssMap::CDN_LDY_DOMAIN . AliOssMap::LDY_DIR . "/static/{$adidshow['adid']}/index.html";
                unset($adidshow['adid']);
                unset($adidshow['game_id']);
            }
        }
        return $data;
    }

    public function refreshCDN($url, $username)
    {
        TencentCdnTool::purge([$url]);
        return true;
    }

    public function getLDYList(LDYListParam $param, $username)
    {
        $data = (new LDYModel())->getList($param);
        foreach ($data['list'] as $ldy) {
            $ldy->adid = $ldy->id;
            $ldy->memo = $ldy->name;
            $ldy->author = $ldy->creator;
            $ldy->addtime = date('Y-m-d H:i:s', $ldy->create_time);
            $ldy->preview_url = AliOssMap::CDN_LDY_DOMAIN . AliOssMap::LDY_DIR . "/static/$ldy->id/index.html";
        }
        return $data;
    }

    public function getLDYFileList($id, $username)
    {
        $list = (new LDYFileModel())->getAllByLDYId($id);
        foreach ($list as $ldy) {
            $ldy->savename = $ldy->filename;
            $ldy->uploadtime = date('Y-m-d H:i:s', $ldy->create_time);
            $ldy->show_url = EnvConfig::DOMAIN . "/ldy/static/$ldy->ldy_id/$ldy->filename";
        }
        return $list;
    }

    public function getTemplateList($page, $rows, $username)
    {
        return [
            ['value' => 'nojump.html', 'label' => '直链模板'],
            ['value' => 'nojump_https.html', 'label' => '直链模板(https)'],
            ['value' => 'shuangduan.html', 'label' => '双端跳转(两轮创意)'],
            ['value' => 'weixin.html', 'label' => '微信公众号模板'],
            ['value' => 'ios_link.html', 'label' => 'IOS直跳模板']
        ];
    }

    public function addLDY($memo, $voice, $copyfrom, $theme, $username)
    {
        $data = [
            'platform' => $this->platform,
            'name' => $memo,
            'voice' => $voice,
            'copyfrom' => $copyfrom,
            'creator' => $username,
            'create_time' => time()
        ];
        return (new LDYModel())->add($data);
    }

    public function editLDY($id, $memo, $voice, $copyfrom, $theme, $username)
    {
        $data = [
            'platform' => $this->platform,
            'name' => $memo,
            'voice' => $voice,
            'copyfrom' => $copyfrom,
            'editor' => $username,
            'update_time' => time()
        ];
        return (new LDYModel())->edit($id, $data);
    }

    public function editLDYState($ids, $sid, $username)
    {
        $data = [
            'state' => $sid,
            'editor' => $username,
            'update_time' => time()
        ];
        return (new LDYModel())->editInID($ids, $data);
    }

    public function fwsh($ids, $ismark, $username)
    {
        $data = [
            'fwsh' => $ismark,
            'editor' => $username,
            'update_time' => time()
        ];
        return (new LDYModel())->editInID($ids, $data);
    }

    public function score($id, $score, $username)
    {
    }

    public function deleteLDY($ids, $username)
    {
        $data = [
            'state' => LDYModel::STATE_DELETE,
            'editor' => $username,
            'update_time' => time()
        ];
        return (new LDYModel())->editInID($ids, $data);
    }

    public function uploadLDY($id, array $files, $username)
    {
        if (empty($files)) {
            return;
        }
        $data = [];
        foreach ($files as $file) {
//            TencentCOSTool::multipartUpload($file['filename'], "/static/$id/{$file['postname']}", TencentOSS::OSS_LDY_BUCKET);
            ALIOSSTool::multiuploadFile(AliOssMap::LDY_DIR . "/static/$id/{$file['postname']}", $file['filename']);
            $data[] = [
                'platform' => $this->platform,
                'ldy_id' => $id,
                'filesize' => $file['size'],
                'filename' => $file['postname'],
                'creator' => $username,
                'create_time' => time()
            ];
        }
        (new LDYFileModel())->addMany($data);
    }

    public function getHTML($id, $username)
    {
//        if (TencentCOSTool::headObject("/static/$id/index.html", TencentOSS::OSS_LDY_BUCKET)) {
        if (ALIOSSTool::getObject(AliOssMap::LDY_DIR . '/static/$id/index.html')) {
            /** @var \GuzzleHttp\Psr7\Stream $body */
//            $response = TencentCOSTool::getObject("/static/$id/index.html", TencentOSS::OSS_LDY_BUCKET);
            $response = ALIOSSTool::getObjectMeta(AliOssMap::LDY_DIR . '/static/$id/index.html');
            $body = $response['Body'];
            return ['content' => $body->getContents()];
        }
        throw new AppException('请先上传index.html');
    }

    public function editHTML($id, $content, $username)
    {
        $stream = fopen('data://text/plain,' . $content, 'r');
//        TencentCOSTool::multipartUpload($stream, "/static/$id/index.html", TencentOSS::OSS_LDY_BUCKET);
        ALIOSSTool::multiuploadFile(AliOssMap::LDY_DIR . "/static/$id/index.html", $stream);
    }

    public function deleteLDYFile($id, $username)
    {
        return (new LDYFileModel())->remove($id, $username);
    }

    public function addROIGame($media_type, $game_ids, $username)
    {
        $data = (new V2DimGameIdModel())->getListInGameId($this->platform, $game_ids);
        if ($data->isEmpty()) {
            throw new AppException('游戏ID未找到');
        }

        return $data->map(function ($item) use ($media_type) {
            return [
                'game_id' => $item->game_id,
                'game_type' => $item->os,
                'media_type' => $media_type,
            ];
        })->toArray();
    }

    public function deleteROIGame($media_type, $game_id, $username)
    {
        // TODO: Implement deleteROIGame() method.
    }

    public function getAPKURL($platform, $game_id, $media_type, $site_id)
    {
        if ($media_type > 0) {
            $media_path = MediaType::CONST_NAME_LIST[$media_type];
            return AliOssMap::CDN_APK_DOMAIN .AliOssMap::PACK_APK_DIR . "/$platform/$game_id/$media_path/$site_id.apk";
        } else {
            return AliOssMap::CDN_APK_DOMAIN .AliOssMap::PACK_APK_DIR . "/$platform/$game_id/$game_id.apk";
        }
    }

    private function getIOSURL($appid)
    {
        return "https://itunes.apple.com/cn/app/id$appid?mt=8";
    }

    /**
     * @param SiteConfigParam $param
     * @return bool
     */
    private function checkGameApkIsUpload(SiteConfigParam $param)
    {
        if ($param->game_type != '安卓' && $param->game_pack !== 1) return true;

        $media_type = $param->media_type;
        // 头条APP当做头条处理 by 嘉裕
        if ($media_type == MediaType::TOUTIAO_APP) $media_type = MediaType::TOUTIAO;

        //当game_id的mix_type为SYSTEM_MIX（系统融合）时，需要校验母包(media_type=0)是否已经上传
        $mix_data = (new V2DimGameIdModel())->getDataByGameId($param->platform, $param->game_id);
        $ext = json_decode($mix_data->ext, true);
        if ($ext['mix_type'] == MixType::SYSTEM) $media_type = 0;

        return  (new GameAPKModel())->checkGameAPK($param->platform, $param->game_id, $media_type);
    }

    /**
     * 获取微信公众号token
     * @param $app_id
     */
    public function getWechatAppToken($app_id)
    {
        throw new AppException('尚未对接获取微信公众号token');
    }

    public function addADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function editADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function AddADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function editADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function batchUPADMonetizationPlan($planIds, $state, $user_name)
    {
        throw new AppException('尚未对接流量变现接口');
    }
}
