<?php
/**
 * Created by <PERSON>p<PERSON>torm.
 * Time: 10:52
 */

namespace App\Service\PlatformAD;

use App\Exception\AppException;
use App\Model\HttpModel\Zhangwan\ADS\LDY\LDYModel;
use App\Model\HttpModel\Zhangwan\ADS\Site\SiteModel;
use App\Model\HttpModel\Zhangwan\ADS\APK\APKModel;
use App\Model\RedisModel\AutoIncrementIdModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Param\ADMonetization\ADMonetizationGroupParam;
use App\Param\ADMonetization\ADMonetizationPlanParam;
use App\Param\LDYListParam;
use App\Param\AgentParam;
use App\Param\APKListParam;
use App\Param\SiteConfigParam;
use App\Param\SiteGroupEditParam;
use App\Param\SiteLogParam;

class PlatformZW extends AbstractPlatform
{
    public function addAgent(AgentParam $param, $username)
    {
        $agent_id_model = new AutoIncrementIdModel();
        $agent_id = $agent_id_model->getNextId(AutoIncrementIdModel::AGENT_ID, $param->platform);
        // 自然量渠道 跳过
        if ($agent_id === 10000) {
            $agent_id = $agent_id_model->getNextId(AutoIncrementIdModel::AGENT_ID, $param->platform);
        }

        return ['agent_id' => $agent_id];
    }

    public function editAgent(AgentParam $param, $username)
    {
        return ['agent_id' => $param->agent_id];
    }

    public function addSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        $data = (new SiteModel())->add($param, $agent_leader, $user_name, $username, $options);
        $site_data = [
            'site_id' => $data[0]['id'],
            'download_url' => $data[0]['download_url'],
            'call_back' => $data[0]['click_url'],
            'landing_url' => $data[0]['landing_url'],
            'media_site_id' => $data[0]['media_site_id'],
            'mini_game_tracking_parameter' => $data[0]['mini_game_tracking_parameter'] ?? '',
        ];
        $this->setTrackDomain($param);
        $site_data['display_track_url'] = $this->getDisplayTrackURL($param, $site_data);
        return $site_data;
    }

    public function editSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        // 无需编辑广告位
    }

    public function setTrackDomain(SiteConfigParam $param)
    {
        if (in_array($param->audit_type, [0, 2])) {
            $this->setActionTrackDomain(self::ACTION_TRACK_DOMAIN_ZXZT);
            $this->setDisplayTrackDomain(self::DISPLAY_TRACK_DOMAIN_ZXZT);
        }
    }

    public function groupEditSite(SiteGroupEditParam $param, $username)
    {
        // 无需编辑广告位
    }

    public function updateUptState($agent_id, $site_ids, $upt_state, $username)
    {
        // 无需编辑上包状态
    }

    public function reportTest($table_site_data, $username)
    {
        throw new AppException("掌玩尚未对接测试上报");
    }

    public function getDataDetail($agent_id, $site_id, $start_date, $end_date, $username)
    {
        throw new AppException("掌玩尚未对接上报明细");
    }

    public function detailExport($agent_id, $start_date, $end_date, $username)
    {
        throw new AppException("掌玩尚未对接明细导出");
    }

    public function getAPKList(APKListParam $param, $username)
    {
        if (empty($param->site_id)) {
            throw new AppException('掌玩尚未对接apk列表');
        }
        $site_info = (new \App\Model\SqlModel\Zeda\SiteModel())->getDataByPlatformSiteId($this->platform, $param->site_id);
        $apk_state_list = $this->getAPKStateList([$param->site_id], $username);
        $item = [
            'addtime' => date('Y-m-d H:i:s', $site_info->create_time),
            'edittime' => empty($apk_state_list) ? '' : $apk_state_list[0]['finish_time'],
            'download_url' => $site_info->download_url,
            'game_id' => $site_info->game_id,
            'game_name' => $site_info->app_name,
            'platform' => $this->platform,
            'state' => empty($apk_state_list) ? 0 : $apk_state_list[0]['state'],
            'top' => '-',
            'ver' => '-',
        ];

        return ['list' => [$item], 'total' => 1];
    }

    public function getAPKStateList($site_ids, $username)
    {
        $data = (new APKModel())->getStateList($site_ids, $username);
        foreach ($data as &$item) {
            $item['site_id'] = $item['id'];
            switch ($item['package_status']) {
                case '打包中':
                    $item['state'] = 5;
                    break;
                case '打包完成':
                    $item['state'] = 1;
                    break;
                default:
                    $item['state'] = 0;
                    break;
            }
        }
        return $data;
    }

    public function setAPK($site_ids, $game_id, $version, $type, $media_type, $username)
    {
        if ($type === 'game') {
            $site_list = (new \App\Model\SqlModel\Zeda\SiteModel())->getAllByGameId($this->platform, $game_id);
            $site_ids = $site_list->pluck('site_id')->toArray();
        }
        return (new APKModel())->setAPK($site_ids, $username);
    }

    public function editAPK($ids, $data, $username)
    {
        // 无需编辑apk
    }

    public function switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username)
    {
        // 无需编辑切换广告
    }

    public function switchGame($agent_ids, $ori_game_id, $game_id, $username)
    {
        throw new AppException('尚未对接切换切换游戏');
    }

    public function getCode($site_id, $game_id, $username)
    {
        $site_info = (new \App\Model\SqlModel\Zeda\SiteModel())->getDataByPlatformSiteId($this->platform, 122412);
        return [
            'codeinfo' => [
                'site_id' => $site_id,
                'adtype' => 1,
            ],
            'domainArr0' => [
                'name' => '默认落地页推广链接',
                'domain' => [
                    $site_info->template_address
                ],
            ],
        ];
    }

    public function getSiteLogs(SiteLogParam $param, $username)
    {
        if (empty($param->site_id)) {
            throw new AppException('掌玩尚未对接apk列表');
        }
        $site_info = (new \App\Model\SqlModel\Zeda\SiteModel())->getDataByPlatformSiteId($this->platform, $param->site_id);
        $item = [
            'edit_time' => date('Y-m-d H:i:s', $site_info->create_time),
            'author' => $site_info->creator,
            'preview' => $site_info->template_address,
            'state' => 1,
            'adturn' => 1,
            'rsync_time' => date('Y-m-d H:i:s', $site_info->create_time),
            'site_id' => $param->site_id,
            'adidshow' => [
                [
                    'adname' => "落地页id-{$site_info->template_type}",
                    'game_name' => "游戏id-{$site_info->game_id}",
                    'url' => $site_info->template_address,
                ]
            ],
        ];

        return ['list' => [$item], 'total' => 1];
    }

    public function getSiteLDYUrl($agent_id, $site_id, $base_url)
    {
        $site_info = (new \App\Model\SqlModel\Zeda\SiteModel())->getDataByPlatformSiteId($this->platform, $site_id);
        return $site_info->template_address;
    }

    public function refreshCDN($url, $username)
    {
        throw new AppException('尚未对接刷新CDN');
    }

    public function getLDYList(LDYListParam $param, $username)
    {
        $ldy_data = (new LDYModel())->getList($param, $username);
        foreach ($ldy_data as &$ldy) {
            $ldy['platform'] = $this->platform;
            $ldy['adid'] = $ldy['id'];
            $ldy['memo'] = $ldy['title'];
            $ldy['preview_url'] = $ldy['tmphtml'];
        }
        return [
            'list' => $ldy_data,
            'total' => count($ldy_data),
        ];
    }

    public function getLDYFileList($id, $username)
    {
        throw new AppException('尚未对接落地页文件列表');
    }

    public function getTemplateList($page, $rows, $username)
    {
        $list = $this->getLDYList(new LDYListParam([
            'page' => $page,
            'rows' => $rows
        ]), $username);

        $template_list = [];
        foreach ($list['list'] as $item) {
            $template_list[] = [
                'value' => $item['id'],
                'label' => $item['title'],
                'tmphtml' => $item['tmphtml']
            ];
        }

        return $template_list;
    }

    public function addLDY($memo, $voice, $copyfrom, $theme, $username)
    {
        throw new AppException('尚未对接新增落地页');
    }

    public function editLDY($id, $memo, $voice, $copyfrom, $theme, $username)
    {
        throw new AppException('尚未对接编辑落地页');
    }

    public function editLDYState($ids, $sid, $username)
    {
        throw new AppException('尚未对接编辑落地页状态');
    }

    public function fwsh($ids, $ismark, $username)
    {
        throw new AppException('尚未对接落地页法务审核');
    }

    public function score($id, $score, $username)
    {
        throw new AppException('尚未对接落地页评分');
    }

    public function deleteLDY($ids, $username)
    {
        throw new AppException('尚未对接删除落地页');
    }

    public function uploadLDY($id, array $files, $username)
    {
        throw new AppException('尚未对接上传落地页文件');
    }

    public function getHTML($id, $username)
    {
        throw new AppException('尚未对接查看落地页代码');
    }

    public function editHTML($id, $content, $username)
    {
        throw new AppException('尚未对接编辑落地页代码');
    }

    public function deleteLDYFile($id, $username)
    {
        throw new AppException('尚未对接删除落地页文件');
    }

    public function addROIGame($media_type, $game_ids, $username)
    {
        throw new AppException('尚未对接媒体上报策略');
    }

    public function deleteROIGame($media_type, $game_id, $username)
    {
        throw new AppException('尚未对接媒体上报策略删除功能');
    }

    /**
     * 获取微信公众号token
     * @param $app_id
     */
    public function getWechatAppToken($app_id)
    {
        throw new AppException('尚未对接获取微信公众号token');
    }

    public function addADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function editADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function AddADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function editADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function batchUPADMonetizationPlan($planIds, $state, $user_name)
    {
        throw new AppException('尚未对接流量变现接口');
    }
}
