<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * Time: 10:52
 */

namespace App\Service\PlatformAD;

use App\Constant\ActionTrackType;
use App\Constant\AgentGroup;
use App\Constant\ConvertToolkit;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Tanwan\ADS\ADMonetization\ADMonetizationModel;
use App\Model\HttpModel\Tanwan\ADS\Agent\AgentModel;
use App\Model\HttpModel\Tanwan\ADS\APK\APKModel;
use App\Model\HttpModel\Tanwan\ADS\LDY\LDYModel;
use App\Model\HttpModel\Tanwan\ADS\ROI\ROIModel;
use App\Model\HttpModel\Tanwan\ADS\Site\SiteModel;
use App\Model\HttpModel\Tanwan\Api\WxToken\WxTokenModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Param\ADMonetization\ADMonetizationGroupParam;
use App\Param\ADMonetization\ADMonetizationPlanParam;
use App\Param\LDYListParam;
use App\Param\AgentParam;
use App\Param\APKListParam;
use App\Param\SiteConfigParam;
use App\Param\SiteGroupEditParam;
use App\Param\SiteLogParam;

class PlatformTW extends AbstractPlatform
{
    private $inside_callback_media = [
        MediaType::QUHUAN, MediaType::BUPET, MediaType::DAHENG,
        MediaType::TIANCHENG, MediaType::WEISHI, MediaType::AIPU,
        MediaType::LINDONG, MediaType::BILIBILI, MediaType::QUTOUTIAO,
        MediaType::XINMEI, MediaType::XIANGRONG, MediaType::MITUTU,
        MediaType::YANGLE, MediaType::KUAIKANMANHUA, MediaType::XIMALAYA,
        MediaType::TAPTAP, MediaType::WEIXING, MediaType::XIAOQIANQIAN,
        MediaType::SINA, MediaType::LEME, MediaType::WEIBO,
        MediaType::IQIYI, MediaType::QIHU_SEARCH, MediaType::KUAISHOU_PINPAI,
        MediaType::BAITE, MediaType::YOUJU, MediaType::JUGAO,
        MediaType::YUCHI, MediaType::HUOBANYUN, MediaType::YOUKU,
        MediaType::UC, MediaType::YUNXIANG
    ];

    private $inside_callback_agent_group = [
        AgentGroup::UC_LIVE_DIVERSION, AgentGroup::DOUYIN_MINI_GAME, AgentGroup::TOUTIAO_ORIGIN,
        AgentGroup::DOUYIN_WINDMILL_FANS, AgentGroup::DOUYIN_UOP_FANS, AgentGroup::BAIDU_SEARCH_MINI_GAME
    ];

    private $platform_callback_media = [

    ];

    public function addAgent(AgentParam $param, $username)
    {
        return (new AgentModel())->add($param, $username);
    }

    public function editAgent(AgentParam $param, $username)
    {
        return (new AgentModel())->edit($param, $username);
    }

    public function addSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        $site_data = (new SiteModel())->add($param, $agent_leader, $user_name, $username, $options);
//        if (in_array($param->media_type, $this->inside_callback_media)
//            || $this->isInsideReport($param)
//            || (in_array($param->media_type, [MediaType::TOUTIAO]) && $param->action_track_type === ActionTrackType::TOUTIAO_V2)
//            || in_array($param->agent_group, $this->inside_callback_agent_group)
//        ) {
        if (!in_array($param->media_type, $this->platform_callback_media)) {
            $this->setTrackDomain($param);
            $site_data['call_back'] = $this->getActionTrackURL($param, $site_data);
            $site_data['display_track_url'] = $this->getDisplayTrackURL($param, $site_data);
            if (in_array($param->media_type, [MediaType::FANZHUO, MediaType::YUNSI, MediaType::YUCHE, MediaType::WEILAIYOU])) {
                $site_data['subscribe_track_url'] = $this->getSubscribeTrackUrl($param, $site_data);
            }
        }
//        }

        return $site_data;
    }

    public function editSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        $site_data = (new SiteModel())->edit($param, $agent_leader, $user_name, $username, $options);
//        if (in_array($param->media_type, $this->inside_callback_media)
//            || $this->isInsideReport($param)
//            || (in_array($param->media_type, [MediaType::TOUTIAO]) && $param->action_track_type === ActionTrackType::TOUTIAO_V2)
//            || in_array($param->agent_group, $this->inside_callback_agent_group)
//        ) {
        if (!in_array($param->media_type, $this->platform_callback_media)) {
            $this->setTrackDomain($param);
            $site_data['call_back'] = $this->getActionTrackURL($param, $site_data);
            $site_data['display_track_url'] = $this->getDisplayTrackURL($param, $site_data);
            if (in_array($param->media_type, [MediaType::FANZHUO, MediaType::YUNSI, MediaType::YUCHE, MediaType::WEILAIYOU])) {
                $site_data['subscribe_track_url'] = $this->getSubscribeTrackUrl($param, $site_data);
            }
        }
//        }

        return $site_data;
    }

    public function setTrackDomain(SiteConfigParam $param)
    {
        if (in_array($param->audit_type, [0, 2])) {
            $this->setActionTrackDomain(self::ACTION_TRACK_DOMAIN_ZXZT);
            $this->setDisplayTrackDomain(self::DISPLAY_TRACK_DOMAIN_ZXZT);
        }
    }

    public function groupEditSite(SiteGroupEditParam $param, $username)
    {
        return (new SiteModel())->groupEdit($param, $username);
    }

    public function updateUptState($agent_id, $site_ids, $upt_state, $username)
    {
        return (new SiteModel())->updateUptState($agent_id, $site_ids, $upt_state, $username);
    }

    public function switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username)
    {
        return (new SiteModel())->switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username);
    }

    public function switchGame($agent_ids, $ori_game_id, $game_id, $username)
    {
        return (new SiteModel())->updateGameId($agent_ids, $ori_game_id, $game_id, $username);
    }

    public function getCode($site_id, $game_id, $username)
    {
        return (new SiteModel())->getCode($site_id, $game_id, $username);
    }

    public function reportTest($table_site_data, $username)
    {
        // UC 快接单 头条事件管理 虎牙小程序 百度系切集团上报
        if (in_array($table_site_data->media_type, [
                MediaType::UC, MediaType::CHENZHONG, MediaType::QIWU_CPS, MediaType::KUAISHOU,
                MediaType::BAIDU, MediaType::BAIDU_SEARCH, MediaType::XINGTU, MediaType::IQIYI,
                MediaType::ZHIHU, MediaType::HUYA, MediaType::BILIBILI_LY, MediaType::BILIBILI,
                MediaType::NETEASE_NEWS, MediaType::YOUKU, MediaType::XINGTU_STAR, MediaType::DOUBAN,
                MediaType::XINGTU_AD
            ])
            || $table_site_data->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET
            || in_array($table_site_data->agent_group, [AgentGroup::TOUTIAO_LIVE, AgentGroup::HONOR])
        ) {
            return parent::reportTest($table_site_data, $username);
        } else {
            return (new SiteModel())->test($table_site_data->site_id, $table_site_data->account_id, $table_site_data->agent_id, $table_site_data->game_id, $username);
        }
    }

    public function getDataDetail($agent_id, $site_id, $start_date, $end_date, $username)
    {
        return (new SiteModel())->getDataDetail($agent_id, $site_id, $start_date, $end_date, $username);
    }

    public function detailExport($agent_id, $start_date, $end_date, $username)
    {
        return (new SiteModel())->detailExport($agent_id, $start_date, $end_date, $username);
    }

    public function getSiteLogs(SiteLogParam $param, $username)
    {
        $site_logs = (new SiteModel())->getSiteLogs($param, $username);
        $site_logs['total'] = $site_logs['total_number'];
        unset($site_logs['total_number']);
        return $site_logs;
    }

    public function getSiteLDYUrl($agent_id, $site_id, $base_url)
    {
        return "$base_url/sycode/$site_id.html";
    }

    public function getAPKList(APKListParam $param, $username)
    {
        $apk_list = (new APKModel())->getList($param, $username);

        foreach ($apk_list['list'] as &$apk) {
            $apk['platform'] = $this->platform;
            $apk['download_url'] = "{$apk['base_url']}/{$apk['game_name']}/{$apk['game_name']}_{$apk['site_id']}.apk";
        }
        $apk_list['total'] = $apk_list['total_number'];
        unset($apk_list['total_number']);
        return $apk_list;
    }

    public function getAPKStateList($site_ids, $username)
    {
        return (new APKModel())->getStateList($site_ids, $username);
    }

    public function setAPK($site_ids, $game_id, $version, $type, $media_type, $username)
    {
        return (new APKModel())->setAPK($site_ids, $game_id, $version, $type, $media_type, $username);
    }

    public function editAPK($ids, $data, $username)
    {
        return (new APKModel())->editAPK($ids, $data, $username);
    }

    public function refreshCDN($url, $username)
    {
        return (new SiteModel())->refreshCDN($url, $username);
    }

    public function getLDYList(LDYListParam $param, $username)
    {
        $ldy_list = (new LDYModel())->getList($param, $username);
        foreach ($ldy_list['list'] as &$ldy) {
            $ldy['platform'] = $this->platform;
        }
        $ldy_list['total'] = $ldy_list['total_number'];
        return $ldy_list;
    }

    public function getLDYFileList($id, $username)
    {
        $ldy_file_list = (new LDYModel())->getLDYFileList($id, $username);
        foreach ($ldy_file_list as &$ldy_file) {
            $ldy_file['platform'] = $this->platform;
            $ldy_file['show_url'] = $ldy_file['savepath'];
        }
        return array_values($ldy_file_list);
    }

    public function getTemplateList($page, $rows, $username)
    {
        return [
            ['value' => 'ios_new.html', 'label' => 'IOS模板'],
            ['value' => 'ios_auto.html', 'label' => 'IOS模板(创意)'],
            ['value' => 'ios301.html', 'label' => 'IOS301模板'],
            ['value' => 'ios302.html', 'label' => 'IOS302模板'],
            ['value' => 'weixin.html', 'label' => '微信公众号模板'],
            ['value' => 'shuangduan.html', 'label' => '双端跳转(两轮创意)'],
            ['value' => 'shuangduan_ios_302.html', 'label' => '双端跳转测试(IOS直跳)'],
            ['value' => 'shuangduan_qy.html', 'label' => '双端_IOS企业(两轮创意)'],
            ['value' => 'shuangduan302.html', 'label' => '双端跳转(直跳)'],
            ['value' => 'shuangduan2.html', 'label' => '双端跳转(四轮创意分地区)'],
            ['value' => 'php.html', 'label' => '动态模板'],
            ['value' => 'nojump.html', 'label' => '直链模板'],
            ['value' => 'nojump_https.html', 'label' => '直链模板(https)'],
            ['value' => 'adturn2.html', 'label' => '客户端区分地区模板'],
            ['value' => 'suijiyuming.html', 'label' => '那些你不知道的域名'],
            ['value' => 'bai_tanwan_pz.html', 'label' => '百度品专跳个人域名'],
            ['value' => 'h5_mobile_config.html', 'label' => 'H5网页证书安装'],
            ['value' => 'ios_qy.html', 'label' => 'Ios企业证书'],
            ['value' => 'temp_iframe.html', 'label' => '临时审批模板'],
            ['value' => '404.html', 'label' => '404模板'],
            ['value' => 'test.html', 'label' => '测试模板（勿用）'],
            ['value' => 'ios_test.html', 'label' => 'IOS测试模板（勿用）'],
            ['value' => 'jssm_17zhaoyx_com.html', 'label' => 'jssm.17zhaoyx.com'],
            ['value' => 'twpop_xyxy03_com.html', 'label' => 'twpop.xyxy03.com'],
            ['value' => 'bd_tanwan_cn.html', 'label' => 'bd_tanwan.cn'],
            ['value' => 'bd_mytanwan_com.html', 'label' => 'bd_mytanwan.com'],
            ['value' => 'bd_xuxiyx_com.html', 'label' => 'bd_xuxiyx.com'],
            ['value' => 'bd_jjyx_com.html', 'label' => 'bd_jjyx.com'],
            ['value' => 'baidu_xydhl.html', 'label' => 'baidu_xydhl.html'],
            ['value' => 'baidu_tanwan_com.html', 'label' => 'baidu_tanwan_com.html'],
            ['value' => '14days.html', 'label' => '14天模板'],
            ['value' => 'shuangduan302_js.html', 'label' => '双端跳转(直跳+js)'],
            ['value' => 'shuangduan_fy.html', 'label' => '双端跳转(飞鹰)'],
            ['value' => 'wx_mini_game.html', 'label' => '微信小游戏模板'],
            ['value' => 'forbidden_down.html', 'label' => '下载禁用'],
            ['value' => 'douyinzhibo.html', 'label' => '直播引导注册'],
            ['value' => 'weixin_ts.html', 'label' => '微信公众号引导弹窗'],
        ];
    }

    public function addLDY($memo, $voice, $copyfrom, $theme, $username)
    {
        return (new LDYModel())->add($memo, $voice, $copyfrom, $theme, $username);
    }

    public function editLDY($id, $memo, $voice, $copyfrom, $theme, $username)
    {
        return (new LDYModel())->edit($id, $memo, $voice, $copyfrom, $theme, $username);
    }

    public function editLDYState($ids, $sid, $username)
    {
        return (new LDYModel())->editState($ids, $sid, $username);
    }

    public function deleteLDY($ids, $username)
    {
        return (new LDYModel())->delete($ids, $username);
    }

    public function fwsh($ids, $ismark, $username)
    {
        return (new LDYModel())->fwsh($ids, $ismark, $username);
    }

    public function score($id, $score, $username)
    {
        return (new LDYModel())->score($id, $score, $username);
    }

    public function uploadLDY($id, array $files, $username)
    {
        return (new LDYModel())->upload($id, $files, $username);
    }

    public function getHTML($id, $username)
    {
        return ['content' => (new LDYModel())->getHTML($id, $username)];
    }

    public function editHTML($id, $content, $username)
    {
        return (new LDYModel())->editHTML($id, $content, $username);
    }

    public function deleteLDYFile($id, $username)
    {
        return (new LDYModel())->deleteFile($id, $username);
    }

    /**
     * 新增媒体上报策略
     * @param $media_type
     * @param $game_ids
     * @param $username
     * @return array
     */
    public function addROIGame($media_type, $game_ids, $username)
    {
        // 快手贪玩没有策略，集团配策略以便小游戏上报sql能通用
        if (in_array($media_type, [MediaType::KUAISHOU, MediaType::UC])) {
            $data = (new V2DimGameIdModel())->getListInGameId($this->platform, $game_ids);
            if ($data->isEmpty()) {
                throw new AppException('游戏ID未找到');
            }

            return $data->map(function ($item) use ($media_type) {
                return [
                    'game_id' => $item->game_id,
                    'game_type' => $item->os,
                    'media_type' => $media_type,
                ];
            })->toArray();
        } else {
            return (new ROIModel())->addROIGame($media_type, $game_ids, $username);
        }
    }

    public function deleteROIGame($media_type, $game_id, $username)
    {
        return (new ROIModel())->deleteROIGame($media_type, $game_id, $username);
    }

    /**
     * 获取微信公众号token
     * @param $app_id
     * @return array
     */
    public function getWechatAppToken($app_id)
    {
        $data = (new WxTokenModel())->getWxAppToken([$app_id]);
        $result = [];
        foreach ($data as $item) {
            if ($item['appid'] === $app_id) {
                $result = [
                    'app_id' => $item['appid'],
                    'access_token' => $item['access_token'],
                    'access_token_expires' => intval($item['access_token_time'] + 1800),// 因TW有些小游戏只缓存半小时后强制刷新，则有效期只有半小时
                    'expires_in' => abs($item['access_token_time'] + 1800 - time())
                ];
                break;
            }
        }

        return $result;
    }

    public function addADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        return (new ADMonetizationModel())->addGroup($param);
    }

    public function editADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        return (new ADMonetizationModel())->editGroup($param);
    }

    public function addADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        return (new ADMonetizationModel())->addPlan($param);
    }

    public function editADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        return (new ADMonetizationModel())->editPlan($param);
    }

    public function batchUPADMonetizationPlan($planIds, $state, $user_name)
    {
        return (new ADMonetizationModel())->batchUPPlan($planIds, $state, $user_name);
    }
}
