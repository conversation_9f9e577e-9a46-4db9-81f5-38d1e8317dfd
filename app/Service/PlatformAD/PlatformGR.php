<?php
/**
 * Created by PhpStorm.
 * Time: 10:52
 */

namespace App\Service\PlatformAD;

use App\Exception\AppException;
use App\Model\HttpModel\GR\ADS\Agent\AgentModel;
use App\Model\HttpModel\GR\ADS\APK\APKModel;
use App\Model\HttpModel\GR\ADS\LDY\LDYModel;
use App\Model\HttpModel\GR\ADS\Site\SiteModel;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Param\ADMonetization\ADMonetizationGroupParam;
use App\Param\ADMonetization\ADMonetizationPlanParam;
use App\Param\LDYListParam;
use App\Param\AgentParam;
use App\Param\APKListParam;
use App\Param\SiteConfigParam;
use App\Param\SiteGroupEditParam;
use App\Param\SiteLogParam;

class PlatformGR extends AbstractPlatform
{
    public function addAgent(AgentParam $param, $username)
    {
        try {
            return (new AgentModel())->add($param, $username);
        } catch (AppException $appException) {
            if (strpos($appException->getMessage(), '该账号已绑定其它渠道') !== false) {
                $agent_id_data = (new V2DimAgentIdModel())->getDataByAgentGroupAccountId($param->platform, $param->agent_group, $param->account_id, $param->agent_leader);
                if (!$agent_id_data) throw $appException;
                return ['agent_id' => $agent_id_data->agent_id, 'is_created' => true];
            } else {
                throw $appException;
            }
        }
    }

    public function editAgent(AgentParam $param, $username)
    {
        return (new AgentModel())->edit($param, $username);
    }

    public function addSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        $site_data = (new SiteModel())->add($param, $agent_leader, $user_name, $username, $options);
//        if ($this->isInsideReport($param)) {
//            $this->setTrackDomain($param);
//            $site_data['call_back'] = $this->getActionTrackURL($param, $site_data);
//        }
        return $site_data;
    }

    public function editSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options)
    {
        $site_data = (new SiteModel())->edit($param, $agent_leader, $user_name, $username, $options);
//        if ($this->isInsideReport($param)) {
//            $this->setTrackDomain($param);
//            $site_data['call_back'] = $this->getActionTrackURL($param, $site_data);
//        }
        return $site_data;
    }

    public function setTrackDomain(SiteConfigParam $param)
    {
        if (in_array($param->audit_type, [0, 2])) {
            $this->setActionTrackDomain(self::ACTION_TRACK_DOMAIN_ZXZT);
            $this->setDisplayTrackDomain(self::DISPLAY_TRACK_DOMAIN_ZXZT);
        }
    }

    public function groupEditSite(SiteGroupEditParam $param, $username)
    {
        return (new SiteModel())->groupEdit($param, $username);
    }

    public function updateUptState($agent_id, $site_ids, $upt_state, $username)
    {
        return (new SiteModel())->updateUptState($agent_id, $site_ids, $upt_state, $username);
    }

    public function reportTest($table_site_data, $username)
    {
        return (new SiteModel())->test($table_site_data->site_id, $table_site_data->account_id, $table_site_data->agent_id, $table_site_data->game_id, $username);
    }

    public function getDataDetail($agent_id, $site_id, $start_date, $end_date, $username)
    {
        throw new AppException('尚未对接上报明细');
        //return (new SiteModel())->getDataDetail($agent_id, $site_id, $start_date, $end_date, $username);
    }

    public function detailExport($agent_id, $start_date, $end_date, $username)
    {
        throw new AppException('尚未对接明细导出');
        //return (new SiteModel())->detailExport($agent_id, $start_date, $end_date, $username);
    }

    public function getAPKList(APKListParam $param, $username)
    {
        $apk_list = (new APKModel())->getList($param, $username);
        $apk_list['list'] = $apk_list['rows'];
        foreach ($apk_list['list'] as &$apk) {
            $apk['platform'] = $this->platform;
        }
        unset($apk_list['rows']);
        return $apk_list;
    }

    public function getAPKStateList($site_ids, $username)
    {
        return (new APKModel())->getStateList($site_ids, $username);
    }

    public function setAPK($site_ids, $game_id, $version, $type, $media_type, $username)
    {
        return (new APKModel())->setAPK($site_ids, $game_id, $version, $type, $media_type, $username);
    }

    public function editAPK($ids, $data, $username)
    {
        return (new APKModel())->editAPK($ids, $data, $username);
    }

    public function switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username)
    {
        throw new AppException('尚未对接');

        return (new SiteModel())->switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username);
    }

    public function switchGame($agent_ids, $ori_game_id, $game_id, $username)
    {
        throw new AppException('尚未对接切换切换游戏');
    }

    public function getCode($site_id, $game_id, $username)
    {
        throw new AppException('尚未对接');

        return (new SiteModel())->getCode($site_id, $game_id, $username);
    }

    public function getSiteLogs(SiteLogParam $param, $username)
    {
        throw new AppException('尚未对接');

        $site_logs = (new SiteModel())->getSiteLogs($param, $username);
        $site_logs['list'] = $site_logs['rows'];
        unset($site_logs['rows']);
        return $site_logs;
    }

    public function refreshCDN($url, $username)
    {
        throw new AppException('尚未对接');
        return (new SiteModel())->refreshCDN($url, $username);
    }

    public function getLDYList(LDYListParam $param, $username)
    {
        throw new AppException('尚未对接');

        $ldy_list = (new LDYModel())->getList($param, $username);
        $ldy_list['list'] = $ldy_list['rows'];
        unset($ldy_list['rows']);
        foreach ($ldy_list['list'] as &$ldy) {
            $ldy['platform'] = $this->platform;
        }
        return $ldy_list;
    }

    public function getLDYFileList($id, $username)
    {
        throw new AppException('尚未对接');

        $ldy_file_list = (new LDYModel())->getLDYFileList($id, $username);
        $ldy_file_list['list'] = $ldy_file_list['rows'];
        unset($ldy_file_list['rows']);
        foreach ($ldy_file_list['list'] as &$ldy_file) {
            $ldy_file['platform'] = $this->platform;
        }
        return $ldy_file_list['list'];
    }

    public function getTemplateList($page, $rows, $username)
    {
        return [
            ['value' => 'nojump.html', 'label' => '直链模板' ]
        ];
    }

    public function addLDY($memo, $voice, $copyfrom, $theme, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->add($memo, $voice, $copyfrom, $username);
    }

    public function editLDY($id, $memo, $voice, $copyfrom, $theme, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->edit($id, $memo, $voice, $copyfrom, $username);
    }

    public function editLDYState($ids, $sid, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->editState($ids, $sid, $username);
    }

    public function fwsh($ids, $ismark, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->fwsh($ids, $ismark, $username);
    }

    public function score($id, $score, $username)
    {
        throw new AppException('尚未对接切换评分');
    }

    public function deleteLDY($ids, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->delete($ids, $username);
    }

    public function uploadLDY($id, array $files, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->upload($id, $files, $username);
    }

    public function getHTML($id, $username)
    {
        throw new AppException('尚未对接');

        $data = (new LDYModel())->getHTML($id, $username);
        return $data;
    }

    public function editHTML($id, $content, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->editHTML($id, $content, $username);
    }

    public function deleteLDYFile($id, $username)
    {
        throw new AppException('尚未对接');

        return (new LDYModel())->deleteFile($id, $username);
    }

    public function addROIGame($media_type, $game_ids, $username)
    {
        $data = (new V2DimGameIdModel())->getListInGameId($this->platform, $game_ids);
        if ($data->isEmpty()) {
            throw new AppException('游戏ID未找到');
        }

        return $data->map(function ($item) use ($media_type) {
            return [
                'game_id' => $item->game_id,
                'game_type' => $item->os,
                'media_type' => $media_type,
            ];
        })->toArray();
    }

    public function deleteROIGame($media_type, $game_id, $username)
    {
        // TODO: Implement deleteROIGame() method.
    }

    /**
     * 获取微信公众号token
     * @param $app_id
     */
    public function getWechatAppToken($app_id)
    {
        throw new AppException('尚未对接获取微信公众号token');
    }

    public function addADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function editADMonetizationGroup(ADMonetizationGroupParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function AddADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function editADMonetizationPlan(ADMonetizationPlanParam $param)
    {
        throw new AppException('尚未对接流量变现接口');
    }

    public function batchUPADMonetizationPlan($planIds, $state, $user_name)
    {
        throw new AppException('尚未对接流量变现接口');
    }
}
