<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\Platform;
use App\Constant\UserIDType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\Huawei\Connect\Oauth2Model;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\Param\MediaAccountInfoParam;
use App\Service\MediaServiceInterface\DMP;

class HuaweiService implements DMP
{
    // media_account.agent 字段
    const HUAWEI_CONNECT_AGENT = 1; // 华为商店
    const HUAWEI_ADS_AGENT = 2; // 鲸鸿动能

    public function addAccount($platform, $account_id, $account_name, $app_key, $secret, $company)
    {
        if (empty($platform) || empty($account_id) || empty($account_name) || empty($app_key) || empty($secret) || empty($company)) {
            throw new AppException('请输入所有参数');
        }

        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;

        $service = new MediaService();

        /**
         * media_account表字段对应，可查询后调用接口
         * 'account_id' => $account_id
         * 'account_name' => $account_name
         * 'majordomo_name' => $app_key 客户端ID 即client_id
         * 'wechat_account_name' => $secret 密钥 即client_secret
         */
        $model = new Oauth2Model();
        $access_token_info = $model->accessToken($app_key, $secret);

        return $service->saveAuthAccount(new MediaAccountInfoParam([
            'media_type' => MediaType::HUAWEI,
            'platform' => $platform,
            'account_id' => $account_id,
            'account_name' => $account_name,
            'access_token' => $access_token_info['access_token'],
            'access_token_expires' => time() + $access_token_info['expires_in'],
            'refresh_token' => '',
            'refresh_token_expires' => 0,
            'majordomo_name' => $app_key,
            'wechat_account_name' => $secret,
            'company' => $company,
            'agent' => self::HUAWEI_CONNECT_AGENT // 华为商店的账号为1，鲸鸿动能的账号为2
        ]), $creator_id, $creator_name);
    }

    public function addAudience($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        $audience_model = new AudienceModel();
        $device_task_model = new DeviceTaskModel();
        $device_task_info = $device_task_model->getData($device_task_id);
        $file_list = json_decode($device_task_info->file_list, true);
        if ($device_task_info->auto_update !== DeviceTaskModel::NOT_AUTO_UPDATE) {
            throw new AppException("华为Marketing API暂不支持更新人群包，请关闭任务的《自动更新》状态后再操作");
        }

        foreach ($companies as $company) {
            foreach ($company['account'] as $account_id) {

                foreach ($file_list as $file) {
                    if ($file['row'] <= 0) {
                        continue;
                    }

                    // 华为只支持imei，oaid
                    if (!in_array($file['data_type'], [UserIDType::IMEI, UserIDType::IMEI_MD5, UserIDType::OAID, UserIDType::OAID_MD5])) {
                        continue;
                    }

                    $device_audience_name = "{$audience_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']];
                    $table_audience_id = $audience_model->add(
                        $user_id,
                        $username,
                        $device_task_id,
                        MediaType::HUAWEI,
                        $company['name'],
                        $account_id,
                        $device_audience_name,
                        $audience_desc,
                        '{}',
                        AudienceModel::TYPE_EXPORT,
                        0,
                        '',
                        Platform::TW,
                        $data_source_type
                    );
                    $table_audience_ids[] = $table_audience_id;
                }

            }
        }

        return $table_audience_ids ?? [];
    }

    public function addAudienceFromFile($user_id, $username, $device_task_id, $audience_name, $audience_desc, $companies, $data_source_type)
    {
        // TODO: Implement addAudienceFromFile() method.
    }

    public function editPushAccountList($id, $account_ids)
    {
        // TODO: Implement editPushAccountList() method.
    }
}
