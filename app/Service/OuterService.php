<?php


namespace App\Service;

use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\Agency\RankAgentModel;
use App\Model\SqlModel\Agency\RankAwemeModel;
use App\Model\SqlModel\Agency\RankRoutePermissionModel;
use App\Model\SqlModel\Agency\UserModel;
use App\Model\SqlModel\Zeda\SiteDateModel;
use App\Param\AgentParam;
use App\Param\DMS\AgencyUserParam;
use App\Param\DMS\OuterSiteListParam;
use App\Param\SiteConfigParam;

class OuterService
{
    /**
     * @param AgentParam $param
     *
     * @throws \Exception
     */
    public function addAccount(AgentParam $param, $creator)
    {
        // 如果选中了cps或者cpa 需要添加对外后台的账号
        if ($param->pay_type == 6 || $param->pay_type == 3) {
            if (!$param->statistic_caliber || !$param->route_permission_ids || !$param->route_list) {
                new AppException('缺失对外账户的必要字段');
            }
            $user_param = [
                'account' => $param->user_name,
                'password' => $param->user_pwd,
                'pub_password' => $param->user_pwd,
                'statistic_caliber' => $param->statistic_caliber,
                'platform' => $param->platform,
                'creator' => $creator,
                'route_list' => $param->route_list,
                'agent_group_id' => $param->agent_group,
                'agent_id' => $param->agent_id,
                'route_permission_ids' => $param->route_permission_ids,
            ];
            $this->addUserPermission(new AgencyUserParam($user_param));
        }
    }

    public function switchSettlementPayType($settlement_type = '', $pay_type = 0)
    {
        if ($settlement_type) {
            switch ($settlement_type) {
                case 'cps':
                    return 6;
                case 'cpa':
                    return 3;
                case '免费':
                    return 5;
                case 'cpt':
                    return 7;
                default:
                    return 0;
            }
        } elseif ($pay_type > 0) {
            switch ($pay_type) {
                case 6:
                    return 'cps';
                case 3:
                    return 'cpa';
                case 5:
                    return '免费';
                case 7:
                    return 'cpt';
                default:
                    return '';
            }
        }
        throw new AppException('错误的参数');
    }

    /**
     * @param AgencyUserParam $param
     *
     * @throws \Exception
     */
    public function addUserPermission(AgencyUserParam $param)
    {
        // 添加用户账号
        $user_id = $this->addUser($param);
        // 处理路由权限
        $result = (new RankRoutePermissionModel())->addMultiple($user_id, $param->route_permission_ids);
        if (!$result) {
            throw new AppException('添加失败！失败原因：路由权限处理失败');
        }
        // 处理渠道权限
        if ($param->agent_group_id && $param->agent_id) {
            $agent_model = new RankAgentModel();
            $agent_list = [
                [
                    'platform' => $param->platform,
                    'agent_group_id' => $param->agent_group_id,
                    'agent_id' => $param->agent_id,
                ]
            ];
            $agent_add_result = $agent_model->addMultiple($user_id, $agent_list);
            if (!$agent_add_result) {
                throw new AppException('添加失败！失败原因：添加渠道权限失败');
            }
        }
        if ($param->aweme_accounts) {
            // 处理直播间的权限
            $result = (new RankAwemeModel())->addMultiple($user_id, $param->aweme_accounts, $param->platform);
            if (!$result) {
                throw new AppException('添加失败！失败原因：添加直播间权限失败');
            }
        }

    }


    /**
     * 添加一个账号
     *
     * @param AgencyUserParam $user_param
     *
     * @return int
     * @throws \Exception
     */
    private function addUser(AgencyUserParam $user_param)
    {
        $user_model = new UserModel();

//        // 老后台屏蔽TW的对外账号创建
//        if ($user_param->platform === "TW") {
//            throw new AppException('请勿添加贪玩平台的账号。');
//        }

        // 判断账号是否存在
        $user_info = $user_model->getDataByAccount($user_param->account);
        if ($user_info) {
            throw new AppException('对外账号已存在, 请勿重复添加');
        }

        $user_param->password = UserService::genHashPwd($user_param->password);

        // 加入用户表
        return $user_model->add($user_param);

    }

    /**
     * 获取直播账号名 （同 aweme_account 账号名后缀自增）
     *
     * @param $aweme_account
     *
     * @return mixed|string
     */
    public function getAccountByAwemeAccount($aweme_account)
    {
        $user_model = new UserModel();
        $account_num = $user_model->getCountLikeAccount($aweme_account);
        return $account_num > 0 ? "{$aweme_account}-" . $account_num : $aweme_account;
    }

    /**
     * 添加结算周期
     *
     * @param SiteConfigParam $param
     * @param                 $settlement_type
     * @param                 $username
     */
    public function addSiteDate(SiteConfigParam $param, $settlement_type, $username)
    {
        $site_date_param = new OuterSiteListParam([
            'platform' => $param->platform,
            'site_id' => [$param->site_id],
            'agent_id' => $param->agent_id,
            'game_id' => $param->game_id,
            'settlement_type' => $settlement_type,
            'start_date' => date("Y-m-d"),
            'ad_price' => 0.00,
            'cps_divide_rate' => 0.00,
            'editor' => $username
        ]);
        (new SiteDateModel())->addSiteDate($site_date_param);
    }
}
