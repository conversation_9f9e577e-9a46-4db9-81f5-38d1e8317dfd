<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/2/25
 * Time: 14:18
 */

namespace App\Service\MediaAD;

use App\Constant\ConvertType;
use App\Constant\DeepExternalAction;
use App\Constant\KuaishouEnum;
use App\Exception\AppException;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Model\HttpModel\Kuaishou\Hosting\ADCreatorModel;
use App\Model\HttpModel\Kuaishou\Hosting\ADPutStatusModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Kuaishou\ADTargetingContentParam;
use App\Param\ADServing\Kuaishou\project\ADOtherSettingContentParam;
use App\Param\ADServing\Kuaishou\project\ADSettingContentParam;
use App\Param\MediaAccountInfoParam;
use App\Service\ScriptMsgService;
use App\Utils\Helpers;
use Throwable;

class MediaKuaishouADLab extends MediaKuaishou
{

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
        $ad_task_model = new ADTaskModel();
        // 已经创建托管项目
        if (!$param->ad1_id) {
            try {
                $param->ad1_id = $this->createProject($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_AD;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException("新建智能托管项目失败：{$e->getMessage()}");
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
    }

    /**
     * 生成广告名称
     * @param ADTaskParam $param
     * @param $name_array
     * @return string
     */
    public function getADNameForLab(ADTaskParam $param, $name_array)
    {
        $name = '';
        foreach ($name_array as $key => $value) {
            switch ($value) {
                case 'site_id':
                    $name .= $param->site_id;
                    break;
                case 'random':
                    $code = '';
                    for ($i = 1; $i <= 4; $i++) {
                        $code .= chr(rand(97, 122));
                    }
                    $name .= $code;
                    break;
                case 'game_id':
                    $name .= $param->getSiteConfig()->game_id;
                    break;
                case 'game_name':
                    $name .= $param->getSiteConfig()->game_name;
                    break;
                case 'convert_type':
                    $convert_type = $param->getSiteConfig()->convert_type;
                    $convert_type_name = $convert_type ? (ConvertType::MAP[$param->media_type][$convert_type] ?? $convert_type) : '无转化类型';
                    $name .= $convert_type_name;
                    break;
                case 'deep_convert_type':
                    $deep_convert_type = $param->getSiteConfig()->deep_external_action;
                    $deep_convert_type_name = $deep_convert_type ? (DeepExternalAction::MAP[$param->media_type][$deep_convert_type] ?? $deep_convert_type) : '无深度转化类型';
                    $name .= $deep_convert_type_name;
                    break;
                case 'date':
                    $name .= date('Ymd');
                    break;
                case 'month_date':
                    $name .= date('md');
                    break;
                case 'time':
                    $name .= date('H:i:s');
                    break;
                case 'date_time':
                    $name .= date('Ymd H:i:s');
                    break;
                case 'targeting_name':
                    $name .= $param->targeting_name;
                    break;
                case 'setting_name':
                    $name .= $param->setting_name;
                    break;
                case 'material_author':
                    $creative_list = $param->creative_list;
                    $creative_value = $creative_list[0];
                    if (isset($creative_value['cover_info'])) {
                        $id = $creative_value['video_info']['id'] ?? '';
                    } else {
                        $id = $creative_value['image_info']['id'] ?? '';
                    }
                    if ($id) {
                        $material_file_info = (new MaterialFileModel())->getData($id);
                        if ($material_file_info) {
                            $material_info = (new MaterialModel())->get($material_file_info->material_id, $material_file_info->platform);
                            if ($material_info) {
                                $name .= $material_info->author;
                            }
                        }
                    }
                    break;
                case 'material_size':
                    $creative_list = $param->creative_list;
                    $creative_value = $creative_list[0];
                    if (isset($creative_value['cover_info'])) {
                        $size = "{$creative_value['video_info']['width']}x{$creative_value['video_info']['height']}";
                    } else {
                        $size = "{$creative_value['image_info']['width']}x{$creative_value['image_info']['height']}";
                    }
                    $name .= $size;
                    break;
                case 'material_name':
                    $creative_list = $param->creative_list;
                    $creative_value = $creative_list[0];
                    if (isset($creative_value['cover_info'])) {
                        $id = $creative_value['video_info']['id'] ?? '';
                    } else {
                        $id = $creative_value['image_info']['id'] ?? '';
                    }
                    if ($id) {
                        $material_file_info = (new MaterialFileModel())->getData($id);
                        if ($material_file_info) {
                            $material_info = (new MaterialModel())->get($material_file_info->material_id, $material_file_info->platform);
                            if ($material_info) {
                                $name .= $material_info->name;
                            }
                        }
                    }
                    break;
                case 'material_id':
                    $creative_list = $param->creative_list;
                    $creative_value = $creative_list[0];
                    if (isset($creative_value['cover_info'])) {
                        $id = $creative_value['video_info']['id'] ?? '';
                    } else {
                        $id = $creative_value['image_info']['id'] ?? '';
                    }
                    if ($id) {
                        $material_file_info = (new MaterialFileModel())->getData($id);
                        if ($material_file_info) {
                            $name .= $material_file_info->material_id;
                        }
                    }
                    break;
                case 'material_file_name':
                    $creative_list = $param->creative_list;
                    if (count($creative_list) > 0) {
                        $creative_value = $creative_list[0];
                        if (isset($creative_value['cover_info'])) {
                            $file_url = $creative_value['video_info']['url'] ?? '';
                        } else {
                            $file_url = $creative_value['image_info']['url'] ?? '';
                        }
                        $file_name = basename($file_url);
                        if ($file_name = substr($file_name, 0, strrpos($file_name, '.'))) {
                            $name .= $file_name;
                        }
                    }
                    break;
                default:
                    $name .= $value;
                    if ($value == '-') {
                        $name = str_replace('_-', '-', $name);
                    }
                    break;
            }


            $name .= '_';
        }
        $name = trim($name, '_');
        if (!$name) {
            throw new AppException('组成空的广告名称');
        }
        foreach (['/', '@'] as $char) {
            $name = str_replace($char, '', $name);
        }

        return trim($name);
    }

    /**
     * 创建项目
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array|int|mixed
     */
    private function createProject(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {

        /* @var ADTargetingContentParam $targeting ; */
        $targeting = $param->targeting;

        /* @var ADSettingContentParam $setting ; */
        $setting = $param->setting;

        /** @var ADOtherSettingContentParam $other_setting ; */
        $other_setting = $param->other_setting;

        //定向内容
        if ($targeting->ages_range) {
            $ages_range_lists = [
                18 => ['min' => 18, 'max' => 23],
                24 => ['min' => 24, 'max' => 30],
                31 => ['min' => 31, 'max' => 40],
                41 => ['min' => 41, 'max' => 49],
                50 => ['min' => 50, 'max' => 100],
            ];
            $age = [];
            foreach ($targeting->ages_range as $ages) {
                $age[] = $ages_range_lists[$ages];
            }
        } elseif ($targeting->age['min'] && $targeting->age['max']) {
            $age = [['min' => $targeting->age['min'], 'max' => $targeting->age['max']]];
        } else {
            $age = [];
        }

        $intelli_extend = [
            'isOpen' => (int)$targeting->intelli_extend['is_open'],
            'no_age_break' => (int)$targeting->intelli_extend['no_age_break'],
            'no_gender_break' => (int)$targeting->intelli_extend['no_gender_break'],
            'no_area_break' => (int)$targeting->intelli_extend['no_area_break'],
        ];
        if (!$targeting->intelli_extend['is_open']) {
            unset($intelli_extend['isOpen']);
        }

        $platform = [];

        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            if ($other_setting->os == KuaishouEnum::PLATFORM_OS_ANDROID) {
                $platform = ['android' => ['min' => $targeting->android_osv]];
            }
            if ($other_setting->os == KuaishouEnum::PLATFORM_OS_IOS) {
                $platform = ['ios' => ['min' => $targeting->ios_osv]];
            }
        } else {
            $platform = $param->getSiteConfig()->game_type == '安卓' ? ['android' => ['min' => $targeting->android_osv]] : ['ios' => ['min' => $targeting->ios_osv]];
        }

        $target = [
            'age' => $age,
            'population' => $targeting->population,
            'exclude_population' => $targeting->exclude_population,
            'gender' => $targeting->gender ? ($targeting->gender == 1 ? "F" : "M") : "",
            'region_ids' => $targeting->region,
            'intelli_extend' => $intelli_extend,
            'filter_converted_level' => $targeting->filter_converted_level,
            'platform' => $platform
        ];

        //图片素材
        $photo_infos = [];
        foreach ($param->creative_list as $value) {
            $photo_id = isset($value['video_info']['id']) ? $material_media_id_map[$value['video_info']['id']]['id'] : $material_media_id_map[$value['video_info']['video_id']];

            if (!isset($photo_infos[$photo_id])) {
                $photo_infos[$photo_id] = [
                    'photo_id' => $photo_id,
                    'cover_image_token' => isset($value['cover_info']['id']) ? $material_media_id_map[$value['cover_info']['id']]['id'] : $material_media_id_map[$value['cover_info']['image_id']],
                    'photo_orientation' => (int)$value['video_info']['width'] > (int)$value['video_info']['height'] ? 2 : 1,
                ];
            }
        }

        $photo_infos = array_values($photo_infos);

        //命名规则
        $name_rule = [
            'rule_enable' => $setting->rule_enable
        ];

        //开启了命名
        if ($name_rule['rule_enable']) {

            $material_name = '';
            $material_id = '';
            $material_author = '';

            if ($setting->campaign_name_rule) {
                $name_rule['campaign_name_rule'] = $this->getADNameForLab($param, $setting->campaign_name_rule);
                if (Helpers::ADServingStrLen($name_rule['campaign_name_rule']) > 100) {
                    $name_rule['campaign_name_rule'] = "广告计划_" . date('Y-m-d:H:i:s');
                }
            }
            if ($setting->unit_name_rule) {
                $name_rule['unit_name_rule'] = $this->getADNameForLab($param, $setting->unit_name_rule);
                if (Helpers::ADServingStrLen($name_rule['unit_name_rule']) > 100) {
                    $name_rule['unit_name_rule'] = "广告组_" . date('Y-m-d:H:i:s');
                }
            }
            if ($setting->campaign_name_rule) {
                $name_rule['creative_name_rule'] = $this->getADNameForLab($param, $setting->creative_name_rule);
                if (Helpers::ADServingStrLen($name_rule['creative_name_rule']) > 100) {
                    $name_rule['creative_name_rule'] = "创意_" . date('Y-m-d:H:i:s');
                }
            }
        }

        //提交roi但是不提交深度转化出价
        if (in_array($setting->ocpx_action_type,
                [KuaishouEnum::OCPX_ACTION_TYPE_PAY, KuaishouEnum::OCPX_ACTION_TYPE_ACTIVE])
            && $setting->deep_conversion_type == KuaishouEnum::DEEP_CONVERSION_TYPE_PAY_ROI) {
            $setting->deep_conversion_bid = [];
        }

        $data = [
            'name' => $param->ad1_name_text,
            'hosting_scene' => $setting->hosting_scene ?? 0,
            'campaign_type' => $other_setting->campaign_type,
            'app_id' => $param->convert_id ?: 0,
            'app_store' => $setting->app_store,
            'action_bar' => $other_setting->action_bar_text,
            'captions' => $param->getWordList(),
            'click_url' => $param->getSiteConfig()->action_track_url,
            'begin_time' => empty($setting->begin_time) ? time() * 1000 : $setting->begin_time * 1000,
            'end_time' => $setting->end_time * 1000,
            'schedule' => $this->makeWeekTime($setting->schedule_time),
            'day_budget' => $setting->day_budget * 1000,
            'cpa_bid' => ($setting->getCpaBid() ?: 0) * 1000,
            'smart_cover' => $setting->smart_cover,
            'roi_ratio' => $setting->getRoiRatio(),
            'asset_mining' => $setting->asset_mining,
            'web_uri_type' => 0,
            'ad_dsp_target' => $target,//定向包
            'photo_infos' => $photo_infos,
            'name_rule' => $name_rule,
            'use_app_market' => $setting->use_app_market,
            'ocpx_action_type' => $setting->ocpx_action_type,
            'deep_conversion_type' => $setting->deep_conversion_type,
            'deep_conversion_bid' => ($setting->deep_conversion_bid[rand(0, (count($setting->deep_conversion_bid) - 1))]) * 1000 ?? 0,
            'scene_ids' => $other_setting->scene_id
        ];

        if ((int)$data['hosting_scene'] === 5) {
            $data['bid_type'] = 12;
        } else {
            $data['bid_type'] = $setting->bid_type;
        }

        //销售线索
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            if ($other_setting->page_mode == 1) {
                $data['web_uri_type'] = KuaishouEnum::WEB_URI_TYPE_DOWNLOAD_PAGE;
                $data['web_uri'] = $other_setting->page_url;
            }
            if ($other_setting->page_mode == 2) {
                $data['web_uri_type'] = KuaishouEnum::WEB_URI_TYPE_DOWNLOAD_PAGE;
                $data['site_id'] = $other_setting->getPageInfo($param->account_id)->id;
            }
        }

        $ad_create_result = (new ADCreatorModel())->create($account_param->account_id, $account_param->access_token, $data);

        //把project_id作为一级id更新
        $param->ad1_id = $ad_create_result['project_id'] ?? 0;
        if (!$param->ad1_id) return [];

        //项目默认开启,如果配置关闭则创建完项目后关闭项目
        if ($setting->ad_operation == 'disable') {
            $this->updateHostingProjectStatus($account_param->account_id, $account_param->access_token, [$param->ad1_id], false);
        }

        return $param->ad1_id;
    }

    /**
     * 更新智能托管状态
     * @param $advertiser_id
     * @param $access_token
     * @param $project_ids
     * @param $put_status
     * @return array
     */
    private function updateHostingProjectStatus($advertiser_id, $access_token, $project_ids, $put_status)
    {
        $param = [
            'project_ids' => $project_ids,
            'put_status' => $put_status ? 1 : 2
        ];
        return (new ADPutStatusModel())->updatePutStatus($advertiser_id, $access_token, $param);
    }

}
