<?php

namespace App\Service\MediaAD;

use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\MediaAccountInfoParam;
use App\Struct\Input;
use Illuminate\Support\Collection;

abstract class AbstractMedia
{
    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return mixed
     */
    abstract public function getInterestActionCategoryList(Input $input);

    /**
     * 根据输入获取行为关键词
     * @param Input $input
     * @return mixed
     */
    abstract public function getInterestActionKeywordList(Input $input);

    /**
     * 根据输入获取兴趣类目
     * @param Input $input
     * @return mixed
     */
    abstract public function getInterestInterestCategoryList(Input $input);

    /**
     * 根据输入获取兴趣关键词
     * @param Input $input
     * @return mixed
     */
    abstract public function getInterestInterestKeywordList(Input $input);

    /**
     * 已创建好的广告组列表
     * @param Input $input
     * @return array
     */
    abstract public function campaignList(Input $input);

    /***
     * 判断是否需要新建广告转化
     * @param ADTaskParam $param
     * @return mixed
     */
    abstract public function isCreateConvert(ADTaskParam $param);

    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    abstract public function convertList(Input $input);

    /**
     * @param ConvertCreateParam $param
     * @return array
     */
    abstract public function createConvert(ConvertCreateParam $param);

    /**
     * @param ChannelPackageParam $param
     * @return array
     */
    abstract public function createChannelPackage(ChannelPackageParam $param);

    /**
     * 获取广告计划可用的定向包列表
     * @param Input $input
     * @return array
     */
    abstract public function audiencePackageList(Input $input);

    /**
     * 创建广告计划-返回创建的广告计划id
     * @param ADTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return string
     */

    /**
     * 修改广告计划-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    abstract public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token);

    /**
     * 修改广告计划的开关-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    abstract public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token);

    /**
     * 修改广告创意的开关-返回修改的广告创意id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    abstract public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token);

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    abstract public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token);

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    abstract public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token);

    /**
     * 上传图片到媒体-返回媒体图片id
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     * 返回的数据格式必须是
     * [
     *  'id'=id,
     *  'url'=url
     * ]
     */
    abstract public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token);

    /**
     * 上传视频到媒体-返回媒体视频id
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     * 返回的数据格式必须是
     * [
     *  'id'=id,
     *  'url'=url
     * ]
     */
    abstract public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token);

    /**
     * 获取创意行业列表
     * @param Input $input
     * @return array
     */
    abstract public function getIndustryList(Input $input);

    /**
     * 根据id获取word
     * @param array $input
     * @param $media_type
     * @return mixed
     */
    abstract public function getWordById($input, $media_type);

    /**
     * 根据ids获取人群包内容
     * @param array $ids
     * @param int $status
     * @return mixed
     */
    abstract public function getAudienceListByIds(array $ids, int $status = 2);

    /**
     * 推送人群包
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    abstract public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids);


    /**
     * 获取人群包列表
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    abstract public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = []);

    /**
     * 获取流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    abstract public function flowList($name = '',$account_id = '', $page = 1, $row = 15);

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    abstract public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);

    /**
     * 获取定型包数据
     * @param $company
     * @param $audience_md5
     * @return mixed
     */
    abstract public function getTargetingDataByAD2($company, $audience_md5);

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    abstract public function getTargetingName(array $audience_info);

    /**
     * 补充定向包信息
     * @param array $targeting_info
     * @param bool $is_return
     * @return array
     */
    abstract public function fillTargetingInfo(array $targeting_info, $is_return = false);

    /**
     * 获取计划行为徕卡
     * @param array $condition
     * @return array
     */
    abstract public function getAdActionList(array $condition);

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    abstract public function getActionWord(array $data);


    /**
     * 获取计划兴趣徕卡
     * @param array $condition
     * @return array
     */
    abstract public function getAdInterestList(array $condition);

    /**
     * 获取计划徕卡兴趣id转词汇
     * @param array $data
     * @return array
     */
    abstract public function getInterestWord(array $data);

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    abstract public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target);

    /**
     * 修改基本报表一级广告
     * @param array $data
     * @return mixed
     */
    abstract public function updateADAnalysisFirstClass(array $data);

    /**
     * 修改基本报表二级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    abstract public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target);

    /**
     * 修改基本报表预算和出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    abstract public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field);

    /**
     * 处理基本报表定时修改出价预算RabbitMQ
     * @param int $media_type
     * @param array $data
     * @return mixed
     */
    abstract public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data);

    /**
     * 修改基本报表定向
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    abstract public function updateADAnalysisTargeting(array $data, Collection $account_info);

    /**
     * 使用接口生成广告任务
     * @param array $input
     * @return int
     */
    abstract public function addADTaskByApi(array $input);

    /**
     * 使用接口修改广告任务
     * @param array $input
     * @return mixed
     */
    abstract public function updateADTaskByApi(array $input);

    /**
     * 更新媒体渠道包
     * @param array $input
     * @return mixed
     */
    abstract public function updateAndroidChannelPackage(array $input);

    /**
     * 修改基本报表深度转化出价或ROI
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    abstract public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field);

    /**
     * 删除基本报表广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    abstract public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target);

    /**
     * 修改基本报表一级预算
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    abstract public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field);

    /**
     * 修改基本报表一级投放时段
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    abstract public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target);

    /**
     * 修改基本报表二级投放时段
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    abstract public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target);


    /**
     * 修改基本报表一级出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    abstract public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field);


    /**
     * 修改基本报表账户预算
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    abstract public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field);

    /**
     * 基本报表广告绑定RTA
     * @param array $input
     * @return mixed
     */
    abstract function bindADAnalysisADRTA(array $input);

    /**
     * 基本报表广告解绑RTA
     * @param array $data
     * @param array $input
     * @return mixed
     */
    abstract function unbindADAnalysisADRTA(array $data, array $input);

    /**
     * 人群预估
     * @param array $target
     * @param $account_info
     * @return mixed
     */
    abstract function getTargetAudienceEstimate(array $target, $account_info);

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    abstract function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param);

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    abstract function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param);

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed
     */
    abstract function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map);

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    abstract function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param);

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    abstract function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param);

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    abstract function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param);

    /**
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    abstract function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param);

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    abstract function isRestartADTask(ADTaskParam $task_param);

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    abstract function prepareCreativeList(ADTaskParam $task_param);

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    abstract function getAgentGroup(ADTaskParam $task_param);

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    abstract public function getDiffMediaStatus();

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    abstract public function getDiffMediaInventoryList($keyword, $condition): Collection;


    /**
     * 获取媒体特殊取名内容
     * @param $task_param
     * @param $name_norms
     * @return mixed
     */
    abstract public function getADName(ADTaskParam $task_param, $name_norms);

    /**
     * 获取转账集合
     * @param array $data
     * @return mixed
     */
    abstract public function getTransferSet(array $data);

    /**
     * 根据transferable_key获取可转账号列表
     * @param $data
     * @return mixed
     */
    abstract public function getAccountIdListByTransferableKey($data);

    /**
     * 刷新账号余额信息
     * @param $data
     * @return mixed
     */
    abstract public function refreshAccountBalance($data);

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    abstract public function getDiffMediaAD3Status();

    /**
     * 广告一键起量
     * @param array $data
     */
    abstract public function updateADAnalysisADRaise(array $data);

    /**
     * 修改基本报表ROI系数
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    abstract public function updateFirstClassRoiGoal(array $data, array $ad_format_target);
}
