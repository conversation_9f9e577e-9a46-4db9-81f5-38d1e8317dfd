<?php

namespace App\Service\MediaAD;

use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Logic\DSP\PermissionLogic;
use App\Model\HttpModel\Toutiao\ADLab\ADLabModel;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Toutiao\ADTargetingContentParam;
use App\Param\ADServing\Toutiao\Project\ADOtherSettingContentParam;
use App\Param\ADServing\Toutiao\Project\ADSettingContentParam;
use App\Param\MediaAccountInfoParam;
use App\Param\ToutiaoADLab\ADLabAdParam;
use App\Param\ToutiaoADLab\ADLabAudienceInfoParam;
use App\Param\ToutiaoADLab\ADLabDeliveryRangeParam;
use App\Param\ToutiaoADLab\CreateADLabParam;
use App\Service\ScriptMsgService;
use Throwable;

class MediaToutiaoADLab extends MediaToutiao
{

    public function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if ($param->site_config['convert_source_type'] == ConvertSourceType::H5_API) {
            $site_ext = $param->site_config['ext'];
            $site_ext['external_url'] = $other_setting->external_url;
            $param->setSiteConfigProp('ext', $site_ext);
        }
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->createADLab($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

//        // 新建广告二级
//        if (!$param->ad2_id) {
//            try {
//                $param->ad2_id = $this->createAD2($param, $account_param);
//                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
//                $ad_task_model->updateTask($param, $param->id);
//            } catch (Throwable $e) {
//                $ad_task_model->updateTask($param, $param->id);
//                throw new AppException('新建广告二级错误:' . $e->getMessage());
//            }
//        }
//
//        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
//        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
//
//        // 推送试玩素材
//        $this->pushPlayableMaterial($param, $account_param);
//
//        // 新建广告三级
//        if (!$param->ad3_ids) {
//            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
//            try {
//                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
//                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
//                $param->ad3_ids = $creative_ids;
//                $ad_task_model->updateTask($param, $param->id);
//            } catch (Throwable $e) {
//                $ad_task_model->updateTask($param, $param->id);
//                throw new AppException('新建广告三级错误:' . $e->getMessage());
//            }
//        }
//
//        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
//        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
    }

    /**
     * 创建头条管家项目
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createADLab(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $othersetting */
        $other_setting = $param->other_setting;
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        // 人群包年龄设置
        $targeting->age = $targeting->audienceAge();
        // 初始化
        $campaign_info = [];
        // 固定行动转化
        $campaign_info['marketing_purpose'] = ToutiaoEnum::MARKETING_PURPOSE_CONVERSION;
        $campaign_info['landing_type'] = $other_setting->promotion_type;
        $campaign_info['budget'] = floatval($setting->budget);
        $campaign_info['name'] = $param->ad1_name_text;

        //todo 二级 ad_info
        $ad_param = new ADLabAdParam([
            'ad_target' => $setting->ad_target,
        ]);

        $ad_param->setDeliveryRange(new ADLabDeliveryRangeParam([
            'delivery_range' => $setting->delivery_range,
            'inventory_type' => $setting->inventory_type,
            'smart_inventory' => $setting->delivery_range == 'UNIVERSAL' ? 'UNIVERSAL_ALL' : 'NORMAL',
            'union_video_type' => $setting->union_video_type
        ]));

        $ad_param->setAudienceInfo(new ADLabAudienceInfoParam([
            'city' => $targeting->city,
            'platform' => [$param->getSiteConfig()->game_type == '安卓' ? 'ANDROID' : 'IOS'],
            'location_type' => $targeting->location_type,
            'age' => $targeting->age,
            'gender' => $targeting->gender,
            'district' => $targeting->district == 'NONE' ? 'NONE' : $targeting->district,
            'superior_popularity_type' => $targeting->superior_popularity_type,
            'flow_package' => $targeting->flow_package,
            'exclude_flow_package' => $targeting->exclude_flow_package,
            'retargeting_tags_include' => $targeting->retargeting_tags_include,
            'retargeting_tags_exclude' => $targeting->retargeting_tags_exclude,
            'auto_extend_enabled' => $targeting->auto_extend_enabled ? 1 : 0,
            'auto_extend_targets' => $targeting->auto_extend_targets,
            "hide_if_converted" => $setting->hide_if_converted,
            "converted_time_duration" => in_array($setting->hide_if_converted, ["APP", "CUSTOMER"]) ? $setting->converted_time_duration : "",
        ]));

        $ad_param->setTrackUrlSetting([
            'track_url_type' => 'CUSTOM',
            'track_url' => [$param->getSiteConfig()->display_track_url],
            'action_track_url' => [$param->getSiteConfig()->action_track_url],
        ]);

        $download_url = str_replace('itunes.apple.com', 'apps.apple.com', $param->getSiteConfig()->download_url);
        $ad_info = $ad_param->toBody();
        $ad_info['download_type'] = $other_setting->download_type;
        $ad_info['download_url'] = $download_url;
        $ad_info['external_action'] = ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type];
        $param->getSiteConfig()->deep_external_action && $ad_info['deep_external_action'] = $param->getSiteConfig()->deep_external_action;
        $ad_info['package'] = $param->getSiteConfig()->package;
        $ad_info['week_schedule'] = $this->makeWeekTime($setting->schedule_time);
        // 目标转化出价
        $ad_info['cpa_bid'] = floatval($setting->getCpaBid());
        $ad_info['deep_bid_type'] = $setting->deep_bid_type;
        $setting->deep_cpabid && $ad_info['deep_cpa_bid'] = floatval($setting->deep_cpabid);
        $ad_info['schedule_type'] = $setting->schedule_type;
        if ($setting->schedule_type === ToutiaoEnum::SCHEDULE_TYPE_SCHEDULE_START_END) {
            $ad_info['start_time'] = date('Y-m-d', $setting->start_time);
            $ad_info['end_time'] = date('Y-m-d', $setting->end_time);
        }

        $ad_info['roi_goal'] = floatval($setting->roi_goal[rand(0, (count($setting->roi_goal) - 1))] ?? 0);

        //下载的应用类型，当推广目的选择「应用推广」时必填
        if ($other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
            $ad_info['app_type'] = $param->getSiteConfig()->game_type == '安卓' ? 'APP_ANDROID' : 'APP_IOS';
        }
        if ($param->getSiteConfig()->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            // 事件管理asset_ids
            $ext_data = $param->getSiteConfig()->ext ?: [];
            $ad_info['assets_ids'] = $ext_data['asset_ids'] ?? [];
        }

        //todo 三级 creative_info
        $creative_info = [
            'app_name' => $other_setting->app_name ? $other_setting->app_name : $param->getSiteConfig()->app_name,
            'source' => $other_setting->source,
            'is_follow_material' => empty($setting->is_follow_material) ? 0 : 1,
            'creative_auto_generate_switch' => $setting->creative_auto_generate_switch,
            'web_url' => trim($param->getSiteConfig()->game_type == '安卓' ? $other_setting->web_url : ''),
            'external_url' => $other_setting->external_url,
            'is_comment_disable' => (int)$setting->is_comment_disable,
        ];
        if ($setting->third_industry_id) {
            $creative_info['industry_v3'] = (int)$setting->third_industry_id;
        }
        if ($setting->ad_keywords) {
            $creative_info['ad_keywords'] = array_values(array_unique(array_filter($setting->ad_keywords)));
        }
        // 设置素材
        foreach ($param->creative_list as $creative_data) {
            if (!isset($creative_data['video_info'])) {
                $creative_info['image_materials'][] = [
                    'image_info' => isset($creative_data['image_info']['id']) ? [['image_id' => $material_media_id_map[$creative_data['image_info']['id']]['id']]] : [['image_id' => $material_media_id_map[$creative_data['image_info']['image_id']]]],
                    'image_mode' => $this->getMaterialFileType(1, $creative_data['image_info']['width'], $creative_data['image_info']['height'],$setting->isUnionSplash())
                ];
            } else {
                $creative_info['video_materials'][] = [
                    'image_info' => isset($creative_data['cover_info']['id']) ? [['image_id' => $material_media_id_map[$creative_data['cover_info']['id']]['id']]] : [['image_id' => $material_media_id_map[$creative_data['cover_info']['image_id']]]],
                    'video_info' => ['video_id' => isset($creative_data['video_info']['id']) ? $material_media_id_map[$creative_data['video_info']['id']]['id'] : $material_media_id_map[$creative_data['video_info']['video_id']]],
                    'image_mode' => $this->getMaterialFileType(2, $creative_data['video_info']['width'], $creative_data['video_info']['height'])
                ];
            }
        }

        // 设置文案
        $creative_info['title_materials'] = [];
        foreach ($param->getWordList() as $title) {
            $material = [
                'title' => $title
            ];
            if ($word_ids = $this->getWordIds($title)) {
                foreach ($word_ids as $word_id) {
                    $material['word_list'][] = ['word_id' => $word_id];
                }
            }
            $creative_info['title_materials'][] = $material;
        }

        $res = (new ADLabModel())->createV3(new CreateADLabParam([
            'campaign_info' => $campaign_info,
            'ad_info' => $ad_info,
            'creative_info' => $creative_info,
        ]), $account_param->account_id, $account_param->access_token);

        $this->addAD1Log($param->account_id, $res['id'], $param->ad1_name_text);
        return $res['id'];
    }

//    /**
//     * 设置管家计划
//     * @param ADTaskParam $param
//     * @param MediaAccountInfoParam $account_param
//     * @return int
//     */
//    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
//    {
//        /* @var ADSettingContentParam $setting */
//        $setting = $param->setting;
//
//        /* @var ADTargetingContentParam $targeting */
//        $targeting = $param->targeting;
//
//        /* @var ADOtherSettingContentParam $other_setting */
//        $other_setting = $param->other_setting;
//
//        $ad_param = new ADLabAdParam([
//            'ad_group_id' => $param->ad1_id,
//            'ad_target' => $setting->ad_target,
//        ]);
//
//        $ad_param->setInventoryInfo(new ADLabInventoryInfoParam([
//            'delivery_range' => $setting->delivery_range,
//            'inventory_type' => $setting->inventory_type,
//            'smart_inventory' => $setting->delivery_range == 'UNIVERSAL' ? 'UNIVERSAL_ALL' : 'NORMAL',
//            'union_video_type' => $setting->union_video_type,
//        ]));
//
//        $download_url = str_replace('itunes.apple.com', 'apps.apple.com', $param->getSiteConfig()->download_url);
//
//        if ($param->getSiteConfig()->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
//            // 事件管理asset_ids
//            $ext_data = $param->getSiteConfig()->ext ?: [];
//            $asset_ids = $ext_data['asset_ids'] ?? [];
//        }
//
//        $ad_param->setConvertInfo(new ADLabConvertInfoParam([
//            'download_type' => $other_setting->download_type,
//            'convert_id' => $param->convert_id ?: 1,
//            'asset_ids' => $asset_ids ?? [],
//            'download_url' => $download_url,
//            'package' => $param->getSiteConfig()->package,
//            'app_type' => $param->getSiteConfig()->game_type == '安卓' ? 'APP_ANDROID' : 'APP_IOS',
//        ]));
//
//        if ((int)$param->getSiteConfig()->plat_id === PlatId::MINI) {
//            $ad_param->setConvertInfo(new ADLabConvertInfoParam([
//                'download_type' => $other_setting->download_type,
//                'convert_id' => $param->convert_id ?: 1,
//                'asset_ids' => $asset_ids ?? [],
//            ]));
//        }
//
//        $ad_param->setBudgetInfo(new ADLabBudgetInfoParam([
//            'bid' => $setting->getCpaBid(),
//            'week_time' => $this->makeWeekTime($setting->schedule_time),
//            'deep_bid_type' => $setting->deep_bid_type,
//            'deep_bid' => $setting->deep_cpabid,
//            'schedule_type' => $setting->schedule_type,
//            'start_time' => date('Y-m-d', $setting->start_time),
//            'end_time' => date('Y-m-d', $setting->end_time),
//            'roi_goal' => floatval($setting->roi_goal[rand(0, (count($setting->roi_goal) - 1))] ?? 0),
//            'auto_stop' => $setting->auto_stop,
//        ]));
//
//        $ad_param->setAudienceInfo(new ADLabAudienceInfoParam([
//            'city' => $targeting->city,
//            'platform' => $param->getSiteConfig()->game_type == '安卓' ? 'ANDROID' : 'IOS',
//            'location_type' => $targeting->location_type,
//            'age' => $targeting->age,
//            'gender' => $targeting->gender,
//            'district' => $targeting->district == 'NONE' ? 'ALL' : $targeting->district,
//            'superior_popularity_type' => $targeting->superior_popularity_type,
//            'flow_package' => $targeting->flow_package,
//            'exclude_flow_package' => $targeting->exclude_flow_package,
//            'retargeting_tags_include' => $targeting->retargeting_tags_include,
//            'retargeting_tags_exclude' => $targeting->retargeting_tags_exclude,
//            'auto_extend_enabled' => $targeting->auto_extend_enabled ? 1 : 0,
//            'auto_extend_targets' => $targeting->auto_extend_targets,
//            "hide_if_converted" => $setting->hide_if_converted,
//            "converted_time_duration" => in_array($setting->hide_if_converted, ["APP", "CUSTOMER"]) ? $setting->converted_time_duration : "",
//        ]));
//
//        $ad_param->setTrackUrlSetting([
//            'track_url_type' => 'CUSTOM',
//            'track_url' => [$param->getSiteConfig()->display_track_url],
//            'action_track_url' => [$param->getSiteConfig()->action_track_url],
//        ]);
//
//        (new ADLabModel())->setAD($ad_param, $account_param->account_id, $account_param->access_token);
//
//        return $param->ad1_id;
//    }

    /**
     * @param $schedule_time
     * @return array[]
     */
    private function makeWeekTime($schedule_time)
    {
        $time = [[], [], [], [], [], [], []];

        foreach ($schedule_time as $index => $t) {
            $time_index = (int)($index / 48);
            if ($t == 1) {
                $time[$time_index][] = $index - ($time_index * 48);
            }
        }

        $all_num = 0;
        foreach ($time as $tt) {
            if (count($tt) == 0) {
                $all_num++;
            }
        }

        if ($all_num >= 7) {
            return [[], [], [], [], [], [], []];
        } else {
            return $time;
        }
    }

//    /**
//     * @param ADTaskParam $param
//     * @param MediaAccountInfoParam $account_param
//     * @param array $material_media_id_map
//     * @return array
//     */
//    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
//    {
//        /* @var ADSettingContentParam $setting */
//        $setting = $param->setting;
//
//        /* @var ADOtherSettingContentParam $other_setting */
//        $other_setting = $param->other_setting;
//
//        $create_param = new ADLabCreativeParam([
//            'ad_group_id' => $param->ad1_id
//        ]);
//
//        $create_data = [
//            'app_name' => $other_setting->app_name ? $other_setting->app_name : $param->getSiteConfig()->app_name,
//            'source' => $other_setting->source,
//            'is_follow_material' => $setting->is_follow_material,
//            'creative_auto_generate_switch' => $setting->creative_auto_generate_switch,
//            'web_url' => trim($param->getSiteConfig()->game_type == '安卓' ? $other_setting->web_url : ''),
//            'external_url' => $other_setting->external_url,
//            'is_comment_disable' => $setting->is_comment_disable,
//        ];
//
//        if ($setting->third_industry_id) {
//            $create_data['third_industry_id'] = $setting->third_industry_id;
//        }
//
//        if ($setting->ad_keywords) {
//            $create_data['ad_keywords'] = array_values(array_unique(array_filter($setting->ad_keywords)));
//        }
//
//        $create_info_param = new ADLabCreativeInfoParam($create_data);
//
//        foreach ($param->getWordList() as $word) {
//            $create_info_param->addTitle($word);
//        }
//
//        foreach ($param->creative_list as $creative_info) {
//            if (isset($creative_info['video_info'])) {
//                $create_info_param->addVideo(isset($creative_info['video_info']['id']) ? $material_media_id_map[$creative_info['video_info']['id']]['id'] : $material_media_id_map[$creative_info['video_info']['video_id']]);
//            } else {
//                $create_info_param->addImage(isset($creative_info['image_info']['id']) ? $material_media_id_map[$creative_info['image_info']['id']]['id'] : $material_media_id_map[$creative_info['image_info']['image_id']]);
//            }
//        }
//
//        $create_param->setCreativeInfo($create_info_param);
//
//        (new ADLabModel())->setCreative($create_param, $account_param->account_id, $account_param->access_token);
//
//        return [$param->ad1_id];
//    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return array|mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $toutiao_format_target)
    {
        $model = new ADLabModel();
        $batch_limit = 50;
        $close = 'CAMPAIGN_STATUS_DISABLE';
        $open = 'CAMPAIGN_STATUS_ENABLE';
        $delete = 'CAMPAIGN_STATUS_DELETE';

        //存储到操作日志的单条数据
        $single_record_operate = [];
        if (0 === (int)$data['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_OFF;
            $opt_status = 'DISABLED';
            $switch_option = $close;
            $edit_detail = '头条投放管家暂停组';
        } elseif (1 === (int)$data['switch']) {
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_ON;
            $opt_status = 'ENABLED';
            $switch_option = $open;
            $edit_detail = '头条投放管家重启组';
        } else {
            $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;
            $opt_status = 'DELETED';
            $switch_option = $delete;
            $edit_detail = '头条投放管家删除';

        }

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        $all_project_ids = [];
        foreach ($toutiao_format_target as $ad_infos) {
            $all_project_ids = array_merge($all_project_ids, $ad_infos);
        }

        $account_ids = array_keys($toutiao_format_target);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');
        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的project_id集合
        $success_project_ids = [];
        //向媒体发起请求后失败的project_id集合
        $failed_project_ids = [];
        //无权限操作的广告id集合
        $no_permission_project_ids = [];
        //操作成功的账号ID
        $success_account_ids = [];

        foreach ($toutiao_format_target as $acc_id => $project_ids) {
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$acc_id])) {
                $no_permission_project_ids = array_merge(
                    $no_permission_project_ids,
                    $project_ids
                );
                continue;
            }
            //解决同一账号中project_id可能超出的问题
            $ids_num = count($project_ids);
            $loop_times = (int)ceil($ids_num / $batch_limit);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-99、100-199、200-299....
                $part_of_project_ids = array_slice($project_ids, ($i - 1) * $batch_limit, $batch_limit);
                //请求头条修改开关状态
                try {
                    $request_group = [];
                    foreach ($part_of_project_ids as $single_project_id) {
                        $request_group[] = ['id' => $single_project_id, 'opt_status' => $opt_status];
                    }
                    $response = $model->setStatus((int)$acc_id, $access_token_info[$acc_id]->access_token, $request_group);
                    if (!in_array($acc_id, $success_account_ids)) {
                        $success_account_ids[] = $acc_id;
                    }
                    if (isset($response['data']['ids']) && $response['data']['ids']) {
                        $success_project_ids = array_merge($success_project_ids, $response['data']['ids']);
                    }
                    if (isset($response['data']['errors']) && $response['data']['errors']) {
                        foreach ($response['data']['errors'] as $one_error) {
                            $failed_project_ids[$one_error['failed_reason']][] = $one_error['id'];
                        }
                    }
                    //因为account_id相同的project_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
//                    $success_project_ids = array_merge($success_project_ids, $part_of_project_ids);
                } catch (AppException $e) {
                    $resp['message'] = $e->getMessage();
                    $resp['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($resp, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的project_id之后一起处理
                    if (isset($failed_project_ids[$failed_info])) {
                        $failed_project_ids[$failed_info] = array_merge($part_of_project_ids, $failed_project_ids[$failed_info]);
                    } else {
                        $failed_project_ids[$failed_info] = $part_of_project_ids;
                    }
                }
            }
        }
        unset($item);

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['project_id']] = $item;
        }, $data['update']);

        //处理成功的
        if ($success_project_ids) {
            foreach ($success_project_ids as $project_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $risk_operate = '';
                if (isset($opt_data[$project_id]['risk_operate']) && 1 === (int)$opt_data[$project_id]['risk_operate']) {
                    $risk_operate = '本次为风险操作';
                    $success_code = ADAnalysisModel::SUCCESS_WITH_RISK;
                }
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$project_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$project_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$opt_data[$project_id]['ad_id'];
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;

                $single_record_operate['edit_detail'] = $edit_detail . '成功 ' . $risk_operate;

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($project_id);
        }

        //处理失败的
        if ($failed_project_ids) {
            foreach ($failed_project_ids as $key => $project_ids) {
                foreach ($project_ids as $one_project_id) {
                    $risk_operate = '';
                    $failed_code = ADAnalysisModel::FAILED;
                    if (isset($opt_data[$one_project_id]['risk_operate']) && 1 === (int)$opt_data[$one_project_id]['risk_operate']) {
                        $risk_operate = '本次为风险操作';
                        $failed_code = ADAnalysisModel::FAILED_WITH_RISK;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_project_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_project_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$opt_data[$one_project_id]['ad_id'];
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = $edit_detail . '失败，' . $risk_operate . ' 错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_project_id);
            }
            unset($project_ids);
        }

        //处理没有权限的
        if ($no_permission_project_ids) {
            foreach ($no_permission_project_ids as $project_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$project_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$project_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$opt_data[$project_id]['ad_id'];
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($project_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        //请求大数据刷新数据
        if ($success_account_ids) {
            $task_model = new ToutiaoTaskModel();
            $task_model->toutiaoADLabTask($success_account_ids);
            $task_model->toutiaoProjectAd1SyncTask($success_account_ids);
        }

        return $return_data;
    }
}
