<?php

namespace App\Service\MediaAD;

use App\Constant\ActionTrackType;
use App\Constant\BatchAD;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Logic\DSP\ADAnalysisMQLogic;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\HttpModel\Tencent\BidWord\BidWordModel;
use App\Model\HttpModel\Tencent\V3\DynamicCreatives\DynamicCreativeModel;
use App\Model\HttpModel\Tencent\V3\AdGroup\AdGroupModel;
use App\Model\HttpModel\Tencent\V3\Programmed\ProgrammedModel;
use App\Model\RedisModel\ADTaskTencentFilterModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentHuxuanVideoLogModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Tencent\ADTargetingContentParam;
use App\Param\ADServing\Tencent\v3\ADOtherSettingContentParam;
use App\Param\ADServing\Tencent\v3\ADSettingContentParam;
use App\Param\MediaAccountInfoParam;
use App\Param\Tencent\V3\AdDynamicCreativeCreateParam;
use App\Param\Tencent\V3\AdGroupCreateParam;
use App\Service\ScriptMsgService;
use App\Utils\Math;
use RedisException;
use Throwable;

class MediaTencentV3 extends MediaTencent
{
    /**
     * 已创建好的广告组列表
     * @param ADTaskParam $param
     * @param $account_id
     * @param $access_token
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, $account_id, $access_token)
    {
        $result = (new AdGroupModel())->info(
            $account_id,
            $access_token,
            [
                [
                    'field' => 'adgroup_name',
                    'operator' => 'EQUALS',
                    'values' => [$param->ad1_name_text],
                ]
            ],
        );

        if (count($result['list']) > 0) {
            $adgroup_id = $result['list'][0]['adgroup_id'] ?? 0;
        } else {
            $adgroup_id = 0;
        }
        return $adgroup_id;
    }

    public function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var  ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ($other_setting->site_set_none === 'search') {
            $param->setSiteConfigProp('action_track_type', ActionTrackType::DEFAULT);
        }
    }

    /**
     * @param $param
     */
    public function formatParamBeforeCreateAD2($param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $setting->end_date = !$setting->end_date ? '' : date('Y-m-d', $setting->end_date);
        in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) && !$setting->end_date && date('Y-m-d', strtotime('+89 day'));

        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $setting->promoted_object_type = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
        } else {
            $setting->promoted_object_type = $param->getSiteConfig()->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;
        }

        if (!$param->getSiteConfig()->deep_external_action) {
            unset($setting->deep_conversion_spec['deep_conversion_worth_spec']);
        }

        if (!in_array('SITE_SET_MOBILE_UNION', $other_setting->site_set) && $other_setting->site_set_none !== 'auto'
        ) {
            unset($setting->flow_optimization_enabled);
        }

        if (!empty($setting->bid_adjustment['site_set_package'])) {
            $setting->bid_adjustment['site_set_package'] = array_values($setting->bid_adjustment['site_set_package']);
        } else {
            if (isset($setting->bid_adjustment['site_set_package'])) {
                unset($setting->bid_adjustment['site_set_package']);
            }
        }

        if (in_array($param->getSiteConfig()->deep_external_action, [
            'GOAL_1DAY_MONETIZATION_ROAS', 'GOAL_1DAY_PURCHASE_ROAS', 'GOAL_7DAY_PURCHASE_ROAS',
            'GOAL_7DAY_LONGTERM_PURCHASE_ROAS', 'GOAL_1DAY_PURCHASE_MONETIZATION_ROAS'
        ])) {
            unset($setting->deep_conversion_spec['deep_conversion_behavior_spec']);
            if ($param->convert_id) {
                $setting->deep_conversion_worth_advanced_rate = '';
                $setting->deep_conversion_worth_rate = (float)trim($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']);
                $setting->deep_conversion_spec = [];
            }
        }

        if ($param->getSiteConfig()->deep_external_action == 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS') {
            unset($setting->deep_conversion_spec['deep_conversion_behavior_spec']);
            if ($param->convert_id) {
                $setting->deep_conversion_worth_rate = '';
                $setting->deep_conversion_worth_advanced_rate = (float)trim($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']);
                $setting->deep_conversion_spec = [];
            }
        }

        if (in_array($param->getSiteConfig()->deep_external_action, ['OPTIMIZATIONGOAL_FIRST_PURCHASE', 'OPTIMIZATIONGOAL_APP_PURCHASE'])) {
            unset($setting->deep_conversion_spec['deep_conversion_worth_spec']);
            if ($param->convert_id) {
                $setting->deep_conversion_behavior_bid = $setting->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'];
                $setting->deep_conversion_spec = [];
            }
        }

        if ($setting->smart_bid_type == 'SMART_BID_TYPE_SYSTEMATIC') {
            $setting->deep_conversion_worth_rate = $setting->deep_conversion_worth_advanced_rate = '';
            $setting->bid_adjustment = [];
        }

        if ($setting->bid_strategy == TencentEum::BID_STRATEGY_MAX_COST) {
            $setting->smart_bid_type = '';
            $setting->bid_strategy = '';
            $setting->bid_scene = TencentEum::BID_SCENE_NORMAL_MAX;
        } else {
            if ($other_setting->smart_delivery) {
                $setting->bid_scene = '';
            } else {
                $setting->bid_strategy = '';
                $setting->smart_bid_type = '';
                $setting->bid_scene = TencentEum::BID_SCENE_NORMAL_AVERAGE;
            }
        }
    }

    public function getMarketingSubGoal(ADTaskParam $param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ($param->getSiteConfig()->convert_type == 'OPTIMIZATIONGOAL_BACK_FLOW') {
            return 'MARKETING_SUB_GOAL_MINI_GAME_RETURN_CUSTOMER_ENGAGEMENT';
        }

        if ($other_setting->marketing_scene == 'GAME_RESERVATION') {
            return 'MARKETING_SUB_GOAL_NEW_GAME_RESERVE';
        }

        if ($other_setting->marketing_scene == 'DEFAULT') {
            if ($param->getSiteConfig()->plat_id != PlatId::MINI) {
                return 'MARKETING_SUB_GOAL_PLATEAU_PHASE_LAUNCH';
            } else {
                return 'MARKETING_SUB_GOAL_MINI_GAME_NEW_CUSTOMER_GROWTH';
            }
        }

        return $other_setting->marketing_scene;
    }

    /**
     * 创建广告组-返回创建的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param): string
    {
        // 准备参数
        $this->formatParamBeforeCreateAD2($param);

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $marketing_carrier_type = 'MARKETING_CARRIER_TYPE_WECHAT_MINI_GAME';
            $marketing_target_type = 'MARKETING_TARGET_TYPE_WECHAT_MINI_GAME';
        } else {
            $marketing_carrier_type = $param->getSiteConfig()->game_type == 'IOS' ? 'MARKETING_CARRIER_TYPE_APP_IOS' : 'MARKETING_CARRIER_TYPE_APP_ANDROID';
            $marketing_target_type = $param->getSiteConfig()->game_type == 'IOS' ? 'MARKETING_TARGET_TYPE_APP_IOS' : 'MARKETING_TARGET_TYPE_APP_ANDROID';
        }

        $data = array_merge(
            $setting->toArray(),
            $other_setting->toArray(),
            [
                'marketing_goal' => 'MARKETING_GOAL_USER_GROWTH',
                'marketing_sub_goal' => $this->getMarketingSubGoal($param),
                'daily_budget' => $setting->ad_daily_budget,
                'configured_status' => $setting->ad_configured_status,
                'marketing_carrier_type' => $marketing_carrier_type,
                'marketing_carrier_detail' => [
                    'marketing_carrier_id' => $param->getSiteConfig()->appid
                ],
                'marketing_asset_outer_spec' => [
                    'marketing_target_type' => $marketing_target_type,
                    'marketing_asset_outer_id' => $param->getSiteConfig()->appid,
                ],
                'begin_date' => $setting->begin_date ? date('Y-m-d', $setting->begin_date) : date('Y-m-d'),
                'end_date' => $setting->end_date,
                'account_id' => $account_param->account_id,
                'access_token' => $account_param->access_token,
                'adgroup_name' => $param->ad1_name_text,
                'conversion_id' => $param->convert_id ? (int)$param->convert_id : '',
                'bid_amount' => $setting->getBidAmount(),
                'time_series' => implode('', $setting->time_series),
                'auto_audience' => $targeting->auto_audience,
                'expand_enabled' => $targeting->expand_enabled,
                'expand_targeting' => $targeting->expand_targeting,
                'cold_start_audience' => $targeting->cold_start_audience,
                'optimization_goal' => ConvertType::MEDIA[MediaType::TENCENT][$param->getSiteConfig()->convert_type],
                'bid_mode' => $setting->billing_event == 'BILLINGEVENT_IMPRESSION' ? 'BID_MODE_OCPM' : 'BID_MODE_OCPC',
            ]
        );
        if ($setting->promoted_object_type == TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID) {
            $data['marketing_asset_outer_spec']['marketing_asset_outer_sub_id'] = (string)($param->getSiteConfig()->app_android_channel_package_id);
        }

        if (in_array('SITE_SET_PCQQ', $other_setting->site_set) && $other_setting->pc_scene) {
            $data['scene_spec']['pc_scene'] = $other_setting->pc_scene;
        }

        if ($setting->cost_constraint_scene == TencentEum::COST_CONSTRAINT_SCENE_OPEN) {
            if (
                $setting->deep_conversion_spec_none === 'true' &&
                in_array($param->getSiteConfig()->deep_external_action, [
                    'GOAL_1DAY_PURCHASE_ROAS',
                    'GOAL_7DAY_PURCHASE_ROAS',
                    'GOAL_7DAY_LONGTERM_PURCHASE_ROAS',
                    'ADVANCED_GOAL_1DAY_PURCHASE_ROAS'
                ])) {
                // roi的时候不给custom_cost_cap
                unset($data['custom_cost_cap']);
            } else {
                // 出价的时候不给custom_cost_roi_cap
                unset($data['custom_cost_roi_cap']);
            }
        }

        $data['site_set'] = $other_setting->site_set;

        if ($targeting->search_scene_switch && $other_setting->site_set_none != 'auto') {
            $data['site_set'][] = TencentEum::SITE_SET_SEARCH_SCENE;
        }

        if ($targeting->search_expand_targeting_switch == 'SEARCH_EXPAND_TARGETING_SWITCH_OPEN') {
            $data['search_expand_targeting_switch'] = $targeting->search_expand_targeting_switch;
        } else {
            $data['search_expand_targeting_switch'] = 'SEARCH_EXPAND_TARGETING_SWITCH_CLOSE';
        }
//        if ($other_setting->live_video_mode == 'LIVE_VIDEO_MODE_LIVE') {
//            $data['marketing_carrier_type'] = 'MARKETING_CARRIER_TYPE_WECHAT_CHANNELS_LIVE';
//            $data['live_video_mode'] = "LIVE_VIDEO_MODE_LIVE";
//            $data['live_video_sub_mode'] = "LIVE_VIDEO_SUBMODE_LIVE_ROOM";
//            $data['promoted_object_id'] = $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id;
//        }

//        if ($other_setting->live_video_mode !== 'LIVE_VIDEO_MODE_LIVE') {
//            try {
//                // 新建渠道object id
//                $this->addPromotedObjects($param, $account_param, $data['promoted_object_type']);
//            } catch (Throwable $e) {
//                if (strpos($e->getMessage(), '该应用封面图为空') !== false) {
//                    throw new AppException('该应用封面图为空，请补充封面图信息');
//                }
//            }
//        }

//        try {
//            // 更新渠道object id
//            $this->updatePromotedObjects($param, $account_param);
//        } catch (Throwable $e) {
//            $error_msg = $e->getMessage();
//            echo "updatePromotedObjects出错:{$error_msg}";
//        }

        if ($param->getSiteConfig()->plat_id == PlatId::MINI &&
            $other_setting->smart_delivery
        ) {
            unset($data['conversion_id']);
            unset($data['deep_conversion_worth_rate']);
            unset($data['bid_strategy']);
            unset($data['smart_bid_type']);
            unset($data['marketing_scene']);
        }

        $agcp = new AdGroupCreateParam($data);

        // 场景化投放
        if (
            $param->getSiteConfig()->plat_id == PlatId::MINI &&
            $other_setting->smart_delivery
        ) {
            $smart_delivery_scene_spec = [
                "smart_delivery_goal" => TencentEum::SMART_DELIVERY_GOAL_MAP[$other_setting->smart_delivery][(string)($param->getSiteConfig()->deep_external_action)]
            ];

            if ($other_setting->smart_delivery == 'IAAP') {
                $smart_delivery_scene_spec['smart_delivery_goal_spec']["mix_iaap_in_one_spec"] = [
                    "iaap_mix_in_one_factor" => (double)$other_setting->iaap_mix_factor,
                    "iaap_roi" => (double)trim($setting->deep_conversion_worth_rate)
                ];

                $smart_delivery_scene_spec["conversion_id_list"] = [$param->getSiteConfig()->convert_id];
            }

            if ($other_setting->smart_delivery == 'MINI_GAME') {
                $smart_delivery_scene_spec['smart_delivery_goal_spec']["mini_game_promotion_spec"] = [
                    "register_cost" => ((int)$setting->getBidAmount()) * 100,
                ];

                switch ($param->getSiteConfig()->deep_external_action) {
                    case TencentEum::GOAL_1DAY_PURCHASE_ROAS:
                        $smart_delivery_scene_spec['smart_delivery_goal_spec']["mini_game_promotion_spec"]['first_day_purchase_roi'] = (double)trim($setting->deep_conversion_worth_rate);
                        break;
                    case TencentEum::GOAL_7DAY_PURCHASE_ROAS:
                        $smart_delivery_scene_spec['smart_delivery_goal_spec']["mini_game_promotion_spec"]['seven_day_purchase_roi'] = (double)trim($setting->deep_conversion_worth_rate);
                        break;
                    case TencentEum::GOAL_1DAY_MONETIZATION_ROAS:
                        $smart_delivery_scene_spec['smart_delivery_goal_spec']["mini_game_promotion_spec"]['first_day_monetization_roi'] = (double)trim($setting->deep_conversion_worth_rate);
                        break;
                    case TencentEum::GOAL_7DAY_MONETIZATION_ROAS:
                        $smart_delivery_scene_spec['smart_delivery_goal_spec']["mini_game_promotion_spec"]['seven_day_monetization_roi'] = (double)trim($setting->deep_conversion_worth_rate);
                        break;
                    default:
                        return false;
                }

                $smart_delivery_scene_spec["conversion_id_list"] = [$param->getSiteConfig()->convert_id];
            }

            $agcp->setSmartDeliveryScene(
                TencentEum::SMART_DELIVERY_PLATFORM_MAP[$other_setting->smart_delivery],
                $smart_delivery_scene_spec
            );
        }

        // 长效ROI定向字段的额外处理
        if (
            $targeting->wechat_ad_behavior_behavior &&
            $param->getSiteConfig()->plat_id == PlatId::MINI &&
            $param->getSiteConfig()->convert_type == 'OPTIMIZATIONGOAL_APP_REGISTER' &&
            $param->getSiteConfig()->deep_external_action == 'GOAL_7DAY_LONGTERM_PURCHASE_ROAS'
        ) {
            $exp = [];
            $exp['wechat_ad_behavior']['minigame_app_id'] = [$param->getSiteConfig()->appid];
            if ($targeting->wechat_ad_behavior_status == 'include') {
                $exp['wechat_ad_behavior']['actions'] = $targeting->wechat_ad_behavior_behavior;
            } elseif ($targeting->wechat_ad_behavior_status == 'exclude') {
                $exp['wechat_ad_behavior']['excluded_actions'] = $targeting->wechat_ad_behavior_behavior;
            }
        }


        $agcp->setTargeting($targeting, $exp ?? []);

        if ($setting->excluded_dimension_mode == 'zidingyi') {
            $agcp->targeting['excluded_converted_audience']['excluded_dimension'] = $setting->excluded_dimension;
            if ($setting->excluded_conversion_behavior_mode == 'zidingyi' && $setting->excluded_conversion_behavior) {
                $agcp->targeting['excluded_converted_audience']['conversion_behavior_list'] = is_array($setting->excluded_conversion_behavior) ? $setting->excluded_conversion_behavior : [$setting->excluded_conversion_behavior];
            }

            if ($setting->excluded_day_mode) {
                $agcp->targeting['excluded_converted_audience']['excluded_day'] = $setting->excluded_day;
            }
        }


        $result = (new AdGroupModel())->add($agcp);
        return $result['adgroup_id'];
    }

    /**
     * 获取账号落地页信息
     * @param ADTaskParam $param
     * @return array|mixed
     */
    public function getPageInfo(ADTaskParam $param)
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        if ($other_setting->is_xijing == 4) {
            return [
                "page_type" => "PAGE_TYPE_WECHAT_MINI_GAME",
                "page_spec" => [
                    "backup_option" => "BACKUP_OPTION_DEFAULT_OFF",
                    "wechat_mini_game_spec" => [
                        'mini_game_tracking_parameter' =>
                            $param->getSiteConfig()->ext['mini_game_tracking_parameter'] ??
                            "?{$param->getSiteConfig()->game_id}&agent_id=$param->agent_id&site_id=$param->site_id",
                        'mini_game_id' => $param->getSiteConfig()->appid
                    ]
                ]
            ];
        }

        // 一键下载
        if ($other_setting->isWechatSingleSite() && $other_setting->click_download) {
            $data = [];
            $data["page_type"] = "PAGE_TYPE_ANDROID_DIRECT_DOWNLOAD";
            return $data;
        }

        if ($page_info = $other_setting->page_map[$param->account_id] ?? '') {
            $page_info = json_decode($page_info);
            if ($page_info->page_type == 'PAGE_TYPE_XIJING_QUICK') {
                return [
                    "page_type" => "PAGE_TYPE_XJ_QUICK",
                    "page_spec" => [
                        "xj_quick_spec" => [
                            'page_id' => (int)$page_info->page_id
                        ]
                    ]
                ];
            } else if ($page_info->page_type == 'PAGE_TYPE_CANVAS_WECHAT') {
                return [
                    "page_type" => "PAGE_TYPE_WECHAT_CANVAS",
                    "page_spec" => [
                        "wechat_canvas_spec" => [
                            'page_id' => (int)$page_info->page_id,
                            "override_canvas_head_option" => "OPTION_CREATIVE_OVERRIDE_CANVAS"
                        ]
                    ]
                ];
            } elseif ($page_info->page_type == 'PAGE_TYPE_TSA_APP') {
                $data = [];
                $game_type = $param->getSiteConfig()->game_type;
                if ($game_type == 'IOS') {
                    $data["page_type"] = "PAGE_TYPE_XJ_IOS_APP_H5";
                    $data['page_spec']['xj_ios_app_h5_spec'] = [
                        'page_id' => (int)$page_info->page_id
                    ];
                } else {
                    $data["page_type"] = "PAGE_TYPE_XJ_ANDROID_APP_H5";
                    $data['page_spec']['xj_android_app_h5_spec'] = [
                        'page_id' => (int)$page_info->page_id
                    ];
                }
                return $data;
            } elseif ($page_info->page_type == 'PAGE_TYPE_OFFICIAL') {
                $data = [];
                $data["page_type"] = "PAGE_TYPE_OFFICIAL";
                $data['page_spec']['official_spec'] = [
                    'page_id' => (int)$page_info->page_id
                ];
                return $data;
            }
            return $page_info;
        }

        return [
            "page_type" => $param->getSiteConfig()->game_type == 'IOS' ? "PAGE_TYPE_IOS_APP" : "PAGE_TYPE_ANDROID_APP",
        ];
    }

    /**
     * 获取动态广告创意结构
     * @param $template_id
     * @param $site_set
     * @param array $creative_info_list
     * @param array $title_list
     * @param array $material_media_id_map
     * @param ADTaskParam $param
     * @return array
     */
    protected function getCreativeElement($template_id, $site_set, array $creative_info_list, array $title_list, array $material_media_id_map, ADTaskParam $param): array
    {
        /** @var ADSettingContentParam $setting * */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $description_options = [];
        foreach ($title_list as $description) {
            $description_options[] = ['value' => ['content' => trim(str_replace("\t", '', $description))]];
        }

        $video_options = [];
        $image_options = [];
        $image_list_options = [];

        if ($other_setting->isHuxuanMaterialMode()) {
            foreach ($other_setting->huxuan_material_map[$param->account_id] as $huxuan_material) {
                $video_options[] = [
                    'value' => [
                        'video_id' => $huxuan_material['video_id'],
                    ]
                ];
            }
        } else {
            $already_make_list = [];
            foreach ($creative_info_list as $creative) {
                if (isset($creative['video_info'])) {
                    if (
//                    (in_array($template_id, [720]) && in_array('SITE_SET_CHANNELS', $site_set) && count($site_set) == 1) ||
                    (in_array($template_id, [721, 720, 1708, 1707]) && in_array('SITE_SET_MOMENTS', $site_set) && count($site_set) == 1)
                    ) {
                        if (!in_array((string)($material_media_id_map[$creative['video_info']['id']]['id']), $already_make_list)) {
                            $video_options[] = [
                                'value' => [
                                    'video_id' => (string)($material_media_id_map[$creative['video_info']['id']]['id']),
                                ]
                            ];
                            $already_make_list[] = (string)($material_media_id_map[$creative['video_info']['id']]['id']);
                        }
                    } else {
                        if (!in_array((string)($material_media_id_map[$creative['video_info']['id']]['id']), $already_make_list)) {
                            $video_options[] = [
                                'value' => [
                                    'video_id' => (string)($material_media_id_map[$creative['video_info']['id']]['id']),
                                    'cover_id' => (string)($material_media_id_map[$creative['cover_info']['id']]['id'])
                                ]
                            ];
                            $already_make_list[] = (string)($material_media_id_map[$creative['video_info']['id']]['id']);
                        }
                    }
                }

                if (isset($creative['image_info'])) {
                    if (isset($creative['image_info']['image_list']) && $creative['image_info']['image_list']) {
                        foreach ($creative['image_info']['image_list'] as $image) {
                            $image_list_options[] = (string)($material_media_id_map[$image['id']]['id']);
                        }
                    } else {
                        if (!in_array((string)($material_media_id_map[$creative['image_info']['id']]['id']), $already_make_list)) {
                            $image_options[] = ['value' => ['image_id' => (string)($material_media_id_map[$creative['image_info']['id']]['id'])]];
                            $already_make_list[] = (string)($material_media_id_map[$creative['image_info']['id']]['id']);
                        }
                    }
                }
            }
        }

        switch ($template_id) {
            case '910':
                $data = [
                    "wxgame_playable_page" => [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $other_setting->getPlayablePageInfo($param->account_id)['playable_page_path'],
                                "wxgame_playable_page_end_cover_img" => $image_options[0]['value']['image_id'],
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                            ]
                        ]
                    ]
                ];

                $data['main_jump_info'] = [
                    [
                        'value' => [
                            "page_type" => "PAGE_TYPE_WECHAT_MINI_GAME",
                            "page_spec" => [
                                "wechat_mini_game_spec" => [
                                    "backup_option" => "BACKUP_OPTION_DEFAULT_OFF",
                                    "mini_game_tracking_parameter" =>
                                        $param->getSiteConfig()->ext['mini_game_tracking_parameter']
                                        ??
                                        "?{$param->getSiteConfig()->game_id}&agent_id=$param->agent_id&site_id=$param->site_id",
                                ]
                            ]
                        ]
                    ]
                ];

                $data["brand"] = [
                    [
                        "value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]
                    ]
                ];

                break;
            case '450':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "description" => $description_options,
                    "image_list_options" => []
                ];
                foreach ($image_options as $image_id) {
                    $data['image_list_options'][] = [$image_id];
                }
                break;
            case '876':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "description" => $description_options,
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "image" => []
                ];
                foreach ($image_options as $image_id) {
                    $data['image'][] = [$image_id];
                }
                break;
            case '877':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "description" => $description_options,
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "video" => $video_options[0],
                ];
                break;
            case '1708':
                $data = [
                    "description" => $description_options,
                    "video" => $video_options,
                ];
                if ($setting->shop_img) {
                    $data['shop_image_struct']['shop_image_id'] = $setting->shop_img;
                    $data['shop_image_struct']['shop_image_switch'] = true;
                }
                if ($setting->label) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                    }
                }
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                if ($other_setting->choose_button_switch) {
                    $data['chosen_button'] = [
                        [
                            'value' => [
                                "left_button" => ['text' => $other_setting->chosen_button_text1, 'jump_info' => $this->getPageInfo($param)],
                                "right_button" => ['text' => $other_setting->chosen_button_text2, 'jump_info' => $this->getPageInfo($param)]
                            ]
                        ]
                    ];
                }
                break;
            case '1707':
                $data = [
                    "description" => $description_options,
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                ];
                $data['image_list'] = [];
                foreach ($image_options as $image_id) {
                    $data['image_list'][] = [
                        'value' => [
                            'list' => [$image_id['value']]
                        ]
                    ];
                }
                if ($setting->shop_img) {
                    $data['shop_image_struct']['shop_image_id'] = $setting->shop_img;
                    $data['shop_image_struct']['shop_image_switch'] = true;
                }
                if ($setting->label) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];

                    }
                }
                if ($other_setting->choose_button_switch) {
                    $data['chosen_button'] = [
                        [
                            'value' => [
                                "left_button" => ['text' => $other_setting->chosen_button_text1, 'jump_info' => $this->getPageInfo($param)],
                                "right_button" => ['text' => $other_setting->chosen_button_text2, 'jump_info' => $this->getPageInfo($param)]
                            ]
                        ]
                    ];
                }
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                break;
            case '452':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "description" => $description_options,
                    "short_video_struct_options" => []
                ];
                foreach ($video_options as $video_id) {
                    $data['short_video_struct_options'][] = ['short_video2' => $video_id];
                }
                break;
            case '588':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "description" => $description_options,
                ];
                $data['image_list'] = [
                    ["value" => ['list' => []]]
                ];
                foreach ($image_options as $image_id) {
                    $data['image_list'][0]['value']['list'][] = $image_id['value'];
                }
                if ($setting->label) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                    }
                }
                break;
            case '311':
                $data = [
                    "description" => $description_options,
                ];

                foreach ($image_options as $index => $image_id) {
                    if (!isset($data['image_list'][$index])) {
                        $data['image_list'][$index] = [
                            "value" => [
                                'list' => []
                            ]
                        ];
                    }
                    $data['image_list'][$index]['value']['list'][] = $image_id['value'];
                }
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data["brand"] = [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ];
                }
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                // 试玩页
                if ($playable_page = $other_setting->getPlayablePageInfo($param->account_id)) {
                    $data["wxgame_playable_page"] = [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $playable_page['playable_page_path'],
                                "wxgame_playable_page_end_cover_img" => $other_setting->wxgame_playable_cover,
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_trigger_types" => ['WXGAME_PLAYABLE_PAGE_TRIGGER_TYPE_CARD_LINK'],
                                "wxgame_playable_page_trigger_text" => $other_setting->wxgame_playable_button_text ?: '立即试玩',
                                "wxgame_playable_page_card_link_image" => $setting->brand_img,
                                "wxgame_playable_page_card_link_description" => $other_setting->wxgame_playable_page_end_desc,
                            ]
                        ]
                    ];
                }
                break;
            case '641': // 3组图
                $data = [
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "description" => $description_options,
                ];
                $data["image_list"] = [];
                for ($i = 0; $i < count($image_list_options); $i = $i + 3)
                    $data["image_list"][] = [
                        "value" => [
                            'list' => [
                                ['image_id' => $image_list_options[$i]],
                                ['image_id' => $image_list_options[$i + 1]],
                                ['image_id' => $image_list_options[$i + 2]],
                            ],
                        ],
                    ];
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                break;
            case '642':
                $data = [
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "description" => $description_options,
                ];
                $data["image_list"] = [];
                for ($i = 0; $i < count($image_list_options); $i = $i + 4) {
                    $data["image_list"][] = [
                        "value" => [
                            'list' => [
                                ['image_id' => $image_list_options[$i]],
                                ['image_id' => $image_list_options[$i + 1]],
                                ['image_id' => $image_list_options[$i + 2]],
                                ['image_id' => $image_list_options[$i + 3]],
                            ],
                        ],
                    ];
                }
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                break;
            case '643':
                $data = [
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "description" => $description_options,
                ];
                $data["image_list"] = [];
                for ($i = 0; $i < count($image_list_options); $i = $i + 6) {
                    $data["image_list"][] = [
                        "value" => [
                            'list' => [
                                ['image_id' => $image_list_options[$i]],
                                ['image_id' => $image_list_options[$i + 1]],
                                ['image_id' => $image_list_options[$i + 2]],
                                ['image_id' => $image_list_options[$i + 3]],
                                ['image_id' => $image_list_options[$i + 4]],
                                ['image_id' => $image_list_options[$i + 5]],
                            ],
                        ],
                    ];
                }
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                break;
            case '2277':
                $data = [
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "description" => $description_options,
                ];
                $data["image_list"] = [];
                for ($i = 0; $i < count($image_list_options); $i = $i + 9) {
                    $data["image_list"][] = [
                        "value" => [
                            'list' => [
                                ['image_id' => $image_list_options[$i]],
                                ['image_id' => $image_list_options[$i + 1]],
                                ['image_id' => $image_list_options[$i + 2]],
                                ['image_id' => $image_list_options[$i + 3]],
                                ['image_id' => $image_list_options[$i + 4]],
                                ['image_id' => $image_list_options[$i + 5]],
                                ['image_id' => $image_list_options[$i + 6]],
                                ['image_id' => $image_list_options[$i + 7]],
                                ['image_id' => $image_list_options[$i + 8]],
                            ],
                        ],
                    ];
                }
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                // 试玩页
                if ($playable_page = $other_setting->getPlayablePageInfo($param->account_id)) {
                    $data["wxgame_playable_page"] = [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $playable_page['playable_page_path'],
                                "wxgame_playable_page_end_cover_img" => $other_setting->wxgame_playable_cover,
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_trigger_types" => ['WXGAME_PLAYABLE_PAGE_TRIGGER_TYPE_CARD_LINK'],
                                "wxgame_playable_page_trigger_text" => $other_setting->wxgame_playable_button_text ?: '立即试玩',
                                "wxgame_playable_page_card_link_image" => $setting->brand_img,
                                "wxgame_playable_page_card_link_description" => $other_setting->wxgame_playable_page_end_desc,
                            ]
                        ]
                    ];
                }
                break;
            case '618':
                $data = [
                    "short_video_struct_options" => [],
                    "title" => $description_options,
                ];
                $data['short_video'] = $video_options;
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                break;
            case '589':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "video" => $video_options,
                    "description" => $description_options
                ];
                if ($setting->label) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                    }
                }
                break;
            case '957':
                $data = [
                    "title" => [['value' => ['content' => trim($setting->title)]]],
                    "short_video_struct_options" => [],
                    "description" => $description_options
                ];
                foreach ($video_options as $video_id) {
                    $data['short_video_struct_options'][] = ['short_video2' => $video_id];
                }
                break;
            case '711':
                $data = [
                    "brand" => [
                        [
                            "value" => [
                                "brand_name" => $setting->brand_name,
                                "brand_image_id" => $setting->brand_img
                            ]
                        ]
                    ],
                    "description" => $description_options,
                    "image" => $image_options
                ];
                if ($other_setting->smart_delivery) {
                    $data['main_jump_info'] = [['value' => $this->getPageInfo($param)]];
                } else {
                    $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                }
                break;
            case '712':
                $data = [
                    "brand" => [
                        ["value" => [
                            "brand_image_id" => $setting->brand_img,
                            "brand_name" => $setting->brand_name,
                        ]]
                    ],
                    "description" => $description_options,
                    "image" => $image_options,
                ];
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                if ($setting->label) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                    }
                }
                // 试玩页
                if ($playable_page = $other_setting->getPlayablePageInfo($param->account_id)) {
                    $data["wxgame_playable_page"] = [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $playable_page['playable_page_path'],
                                "wxgame_playable_page_end_cover_img" => $other_setting->wxgame_playable_cover,
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_trigger_types" => ['WXGAME_PLAYABLE_PAGE_TRIGGER_TYPE_CARD_LINK'],
                                "wxgame_playable_page_trigger_text" => $other_setting->wxgame_playable_button_text ?: '立即试玩',
                                "wxgame_playable_page_card_link_image" => $setting->brand_img,
                                "wxgame_playable_page_card_link_description" => $other_setting->wxgame_playable_page_end_desc,
                            ]
                        ]
                    ];
                }
                break;
            case '713':
                $data = [
                    "brand" => [
                        ["value" => [
                            "brand_name" => $setting->brand_name,
                            "brand_image_id" => $setting->brand_img
                        ]]
                    ],
                    "description" => $description_options,
                    "image" => $image_options,
                ];
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                if ($setting->label) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                    }
                }
                break;
            case '720':
            case '721':
                $data = [
                    "brand" => [
                        [
                            "value" => [
                                "brand_name" => $setting->brand_name,
                                "brand_image_id" => $setting->brand_img,
                            ]
                        ]
                    ],
                    "description" => $description_options,
                    "video" => $video_options,
                    'jump_info' => [['value' => $this->getPageInfo($param)]],
                ];
                if ($other_setting->site_set_none == 'shipinghao') {
                    unset($data['image']);
                }
                if ($setting->label && !(count($other_setting->site_set) == 1 && in_array('SITE_SET_WECHAT', $other_setting->site_set))) {
                    $data['label'] = [];
                    $data['label'][0] = [
                        'value' => [
                            'list' => []
                        ]
                    ];
                    foreach ($setting->label as $value) {
                        $data['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                    }
                }
                if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                    $data['floating_zone'] = [
                        [
                            'value' => [
                                'floating_zone_name' => $other_setting->promotion_title,
                                'floating_zone_button_text' => $other_setting->button_text,
                                'floating_zone_desc' => $other_setting->promotion_desc,
                                'floating_zone_image_id' => $other_setting->promotion_card_id,
                                'floating_zone_type' => 'FLOATING_ZONE_TYPE_IMAGE_TEXT',
                                'floating_zone_switch' => true,
                            ]
                        ]
                    ];
                    $data["wechat_channels"] = [
                        [
                            'value' => [
                                "username" => $other_setting->getVideoNumberId($param->account_id),
                                "finder_object_visibility" => $other_setting->finder_object_visibility
                            ]
                        ]
                    ];
                }
                // 试玩页
                if ($playable_page = $other_setting->getPlayablePageInfo($param->account_id)) {
                    $data["wxgame_playable_page"] = [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $playable_page['playable_page_path'],
                                "wxgame_playable_page_end_cover_img" => $other_setting->wxgame_playable_cover,
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_trigger_types" => ['WXGAME_PLAYABLE_PAGE_TRIGGER_TYPE_CARD_LINK'],
                                "wxgame_playable_page_trigger_text" => $other_setting->wxgame_playable_button_text ?: '立即试玩',
                                "wxgame_playable_page_card_link_image" => $setting->brand_img,
                                "wxgame_playable_page_card_link_description" => $other_setting->wxgame_playable_page_end_desc,
                            ]
                        ]
                    ];
                }
                break;
            case '925':
                $data = [
                    'image' => $image_options,
                ];
                break;
            case '2106':
                $data = [
                    'image' => $image_options,
                    'jump_info' => [['value' => $this->getPageInfo($param)]],
                    "description" => $description_options,
                ];
                if ($playable_page = $other_setting->getPlayablePageInfo($param->account_id)) {
                    $data["wxgame_playable_page"] = [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $playable_page['playable_page_path'],
                                "wxgame_playable_page_end_cover_img" => $other_setting->wxgame_playable_cover,
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_trigger_types" => ['WXGAME_PLAYABLE_PAGE_TRIGGER_TYPE_CARD_LINK'],
                                "wxgame_playable_page_trigger_text" => $other_setting->wxgame_playable_button_text ?: '立即试玩',
                                "wxgame_playable_page_card_link_image" => $setting->brand_img,
                                "wxgame_playable_page_card_link_description" => $other_setting->wxgame_playable_page_end_desc,
                            ]
                        ]
                    ];
                }
                break;
            case '1529':
                $data = [
                    "video" => $video_options,
                ];
                $data['jump_info'] = [['value' => $this->getPageInfo($param)]];
                break;
            default:
                throw new AppException('没有传入正确的创意模型id');
        }

        $other_setting->button_text && $data["action_button"] = [
            [
                "value" => [
                    "button_text" => trim($other_setting->button_text),
                    "jump_info" => $this->getPageInfo($param),
                ]
            ]
        ];

        // 文字链类型 (包含朋友圈)

        if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {
            if (!$other_setting->mini_card_link_switch && $other_setting->link_name_type && !$other_setting->getPlayablePageInfo($param->account_id)) {
                $data['text_link'] = [
                    [
                        "value" => [
                            "link_name_type" => trim($other_setting->link_name_type),
                            "jump_info" => $this->getPageInfo($param),
                            "link_name_text" => $other_setting->link_name_type == 'LINK_NAME_TEXT_TEMPLATE' ? $other_setting->link_name_type_text : ''
                        ]
                    ]
                ];
            }
            if ($other_setting->mini_card_link_switch && !$other_setting->getPlayablePageInfo($param->account_id)) {
                $data['mini_card_link'] = [
                    [
                        'value' => [
                            'jump_info' => $this->getPageInfo($param),
                            'mini_card_link_description' => $other_setting->mini_card_link_spec['desc'],
                            'mini_card_link_image' => $other_setting->mini_card_link_spec['image'],
                            'mini_card_link_button_text' => $other_setting->button_text,
                            'mini_card_link_switch' => true,
                        ],
                    ]
                ];
            }
        }

//        if (in_array($template_id, [1708, 1707, 450, 588, 957]) && $other_setting->button_text) {
//            $data['action_button'][0]['value']["jump_info"] = [
//                "page_type" =>
//                    $param->getSiteConfig()->plat_id == PlatId::MINI ?
//                        'PAGE_TYPE_WECHAT_MINI_GAME'
//                        :
//                        ($param->getSiteConfig()->game_type == 'IOS' ? 'PAGE_TYPE_IOS_APP' : "PAGE_TYPE_ANDROID_APP")
//            ];
//        }

        if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
            if (isset($data['brand'][0])) {
                $data['brand'][0]['value']['jump_info'] = [
                    "page_type" => $other_setting->getProfileInfo($param->account_id)->page_type == 'PAGE_TYPE_OFFICIAL' ? 'PAGE_TYPE_OFFICIAL' : "PAGE_TYPE_H5_PROFILE",
                    "page_spec" => [
                        'h5_profile_spec' => [
                            'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                        ],
                        'official_spec' => [
                            'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                        ]
                    ],
                ];

            } else {
                $data['brand'] = [
                    [
                        'value' => [
                            'jump_info' => [
                                "page_type" => $other_setting->getProfileInfo($param->account_id)->page_type == 'PAGE_TYPE_OFFICIAL' ? 'PAGE_TYPE_OFFICIAL' : "PAGE_TYPE_H5_PROFILE",
                                "page_spec" => [
                                    'h5_profile_spec' => [
                                        'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                                    ],
                                    'official_spec' => [
                                        'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                                    ]
                                ],
                            ]
                        ]
                    ]
                ];
            }
        }

        if ($other_setting->site_set_none == 'auto' && !$template_id) {
            $data["wechat_channels"] = [
                [
                    "value" => [
                        "username" => $other_setting->getVideoNumberId($param->account_id),
                        "finder_object_visibility" => false
                    ]
                ]
            ];
        }


        if ($other_setting->site_set_none == 'auto' && $other_setting->smart_delivery) {
            $data["wechat_channels"] = [
                [
                    "value" => [
                        "username" => $other_setting->getVideoNumberId($param->account_id),
                        "finder_object_visibility" => $other_setting->finder_object_visibility
                    ]
                ]
            ];
        }

        // 视频号一定要在下面因为优先级最高
        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {

            if (isset($data['brand'][0])) {
                $data['brand'] = [
                    [
                        'value' => [
                            'jump_info' => [
                                "page_type" => "PAGE_TYPE_WECHAT_CHANNELS_PROFILE",
                                "page_spec" => [
                                    'wechat_channels_profile_spec' => [
                                        'username' => $other_setting->getVideoNumberId($param->account_id),
                                    ]
                                ],
                            ]
                        ]
                    ]
                ];
            }
        }

        // 一键下载
        if ($other_setting->isWechatSingleSite() && $other_setting->click_download) {
            unset($data['jump_info']);
            $data['main_jump_info'][] = [
                'value' => $this->getPageInfo($param),
            ];
        }

        // 朋友圈首条评论
        if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {
            if ($other_setting->moment_first_comment) {
                $data['social_skill'] = [
                    [
                        'value' => [
                            'social_skill_first_comment_switch' => true,
                            'social_skill_first_comment' => $other_setting->moment_first_comment,
                        ]
                    ]
                ];

                if ($other_setting->moment_first_comment_account_type == 'official') {
                    $data['brand'][0]['value']['jump_info']['page_type'] = 'PAGE_TYPE_WECHAT_OFFICIAL_ACCOUNT_DETAIL';
                    $data['brand'][0]['value']['jump_info']['page_spec'] = [
                        'wechat_official_account_detail_spec' => [
                            "app_id" => $other_setting->getFirstCommentOfficialAccountInfo($param->account_id)->promoted_object_id ?? ''
                        ]
                    ];
                } elseif ($other_setting->moment_first_comment_account_type == 'video') {
                    $data['brand'][0]['value']['jump_info']['page_type'] = 'PAGE_TYPE_WECHAT_CHANNELS_PROFILE';
                    $data['brand'][0]['value']['jump_info']['page_spec'] = [
                        'wechat_channels_profile_spec' => [
                            "username" => $other_setting->getFirstCommentVideoNumberInfo($param->account_id)->promoted_object_id ?? ''
                        ]
                    ];
                } elseif ($other_setting->moment_first_comment_account_type == 'custom') {
                    $data['brand'][0]['value'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_image_id" => $setting->brand_img
                    ];
                }
            }
        }

        if (
            (in_array($template_id, [721]) && in_array('SITE_SET_CHANNELS', $site_set) && count($site_set) == 1)
        ) {
            unset($data['action_button']);
        }

        return $data;
    }

    /**
     * 构造创意请求参数
     * @param ADTaskParam $param
     * @param $creative_info
     * @return string
     */
    protected function getTemplateId(ADTaskParam $param, $creative_info)
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        // 自定义版位和自动版位创意规格
        if (!(in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set))) {
            if (in_array($other_setting->site_set_none, ['zidingyi', 'auto'])) {
                $other_setting->template_id = '';
            }
            if (isset($creative_info['cover_info'])) {
                if ((int)$creative_info['video_info']['width'] > (int)$creative_info['video_info']['height']) {
                    return !empty($other_setting->template_id) ? $other_setting->template_id : 720;
                } else {
                    if ((int)$creative_info['video_info']['width'] == 1080) {
                        return !empty($other_setting->template_id) ? $other_setting->template_id : 1529;
                    } else {
                        return !empty($other_setting->template_id) ? $other_setting->template_id : 721;
                    }
                }
            } else {
                if ((int)$creative_info['image_info']['width'] > (int)$creative_info['image_info']['height']) {
                    if ((int)$creative_info['image_info']['width'] == 960 && (int)$creative_info['image_info']['height'] == 274) {
                        return !empty($other_setting->template_id) ? $other_setting->template_id : 713;
                    } else {
                        return !empty($other_setting->template_id) ? $other_setting->template_id : 711;
                    }
                } else {
                    return !empty($other_setting->template_id) ? $other_setting->template_id : 712;
                }
            }
        }
        return $other_setting->template_id;
    }

    /**
     * 创建自定义创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     * @throws Throwable
     */
    public function createCommonCreative(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map): array
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        /** @var ADSettingContentParam $setting * */
        $setting = $param->setting;

        $create_model = new DynamicCreativeModel();

        $data = [
            'account_id' => (int)$account_param->account_id,
            'access_token' => $account_param->access_token,
            'adgroup_id' => (int)$param->ad1_id,
            'delivery_mode' => 'DELIVERY_MODE_CUSTOMIZE',
            'dynamic_creative_type' => 'DYNAMIC_CREATIVE_TYPE_COMMON',
            'configured_status' => $setting->creative_configured_status
        ];

        if ($other_setting->site_set_none == 'auto') {
            $data['automatic_site_enabled'] = true;
            $data['site_set'] = [];
        }

        $result_ids = [];

        foreach ($param->creative_list as $creative) {

            $data['creative_template_id'] = (int)$this->getTemplateId($param, $creative);

            if (count($other_setting->ad3_name) > 2) {
                $data['dynamic_creative_name'] = $this->getCustomAd3Name($param, $creative);
            } else {
                if (isset($creative['cover_info'])) {
                    $c_name = basename($creative['video_info']['url']);
                } else {
                    $c_name = basename($creative['image_info']['url']);
                }
                $data['dynamic_creative_name'] = "{$param->ad1_id}_" . $c_name . "_" . date('Ymd');
            }
            // 初始化动态广告的创意结构体
            $data['creative_components'] = $this->getCreativeElement(
                $data['creative_template_id'],
                $other_setting->site_set,
                [$creative],
                [$creative['title']],
                $material_media_id_map,
                $param
            );

            try {
                $result_ids[] = $create_model->add(new AdDynamicCreativeCreateParam($data));
            } catch (Throwable $e) {
                if (
                    (strpos($e->getMessage(), '创意与创意id') !== false && strpos($e->getMessage(), '重复，请修改') !== false) ||
                    (strpos($e->getMessage(), '组件与组件id') !== false && strpos($e->getMessage(), '相似度较高') !== false && strpos($e->getMessage(), '建议使用已有组件') !== false)
                ) {
                    continue;
                } else {
                    throw $e;
                }
            }
        }


        return $result_ids;
    }

    public function createComponentCreative(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map): array
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        /** @var ADSettingContentParam $setting * */
        $setting = $param->setting;

        $create_model = new DynamicCreativeModel();

        $data = [
            'account_id' => (int)$account_param->account_id,
            'access_token' => $account_param->access_token,
            'adgroup_id' => (int)$param->ad1_id,
            'delivery_mode' => 'DELIVERY_MODE_COMPONENT',
            'dynamic_creative_type' => $other_setting->dynamic_creative_type,
            'configured_status' => $setting->creative_configured_status
        ];

        if ($other_setting->site_set_none == 'auto') {
            $data['automatic_site_enabled'] = true;
            $data['site_set'] = [];
        }

        $result_ids = [];

        if (count($other_setting->ad3_name) > 2) {
            $data['dynamic_creative_name'] = $this->getCustomAd3Name($param);
        } else {
            $data['dynamic_creative_name'] = "{$param->ad1_id}_" . "组件化创意_" . date('Y-m-d H:i:s');
        }

        if ($other_setting->dynamic_creative_type == 'DYNAMIC_CREATIVE_TYPE_COMMON' && $other_setting->site_set_none != 'auto') {
            $data['creative_template_id'] = (int)$this->getTemplateId($param, $param->creative_list[0]);

            $data['creative_components'] = $this->getCreativeElement(
                $data['creative_template_id'],
                $other_setting->site_set,
                $param->creative_list,
                $param->word_list,
                $material_media_id_map,
                $param
            );
        } else {
//            $p_data = $this->makeProgramModelAndGetID($param, $account_param, $material_media_id_map);
//            $data['program_creative_info']['material_derive_id'] = $p_data['material_derive_id'];
//            $data['program_creative_info']['material_derive_info'] = $p_data['material_derive_info'];
            $data["creative_template_id"] = 721;
            $data["auto_derived_program_creative_switch"] = false;

            if ($other_setting->smart_delivery == 'MINI_GAME') {
                unset($data["auto_derived_program_creative_switch"]);
            }

            $data['creative_components'] = [];


            $data['creative_components']['jump_info'] = [['value' => $this->getPageInfo($param)]];

            if ($setting->label) {
                $data['creative_components']['label'] = [];
                $data['creative_components']['label'][0] = [
                    'value' => [
                        'list' => []
                    ]
                ];
                foreach ($setting->label as $value) {
                    $data['creative_components']['label'][0]['value']['list'][] = ["content" => $value, 'type' => 'LABEL_TYPE_CUSTOMIZETEXT'];
                }
            }

            $setting->brand_img && $setting->brand_name && $data['creative_components']["brand"] = [
                [
                    "value" => [
                        "brand_name" => $setting->brand_name,
                        "brand_image_id" => $setting->brand_img
                    ]
                ]
            ];

            if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                if (isset($data['creative_components']['brand'][0])) {
                    $data['creative_components']['brand'][0]['value']['jump_info'] = [
                        "page_type" => $other_setting->getProfileInfo($param->account_id)->page_type == 'PAGE_TYPE_OFFICIAL' ? 'PAGE_TYPE_OFFICIAL' : "PAGE_TYPE_H5_PROFILE",
                        "page_spec" => [
                            'h5_profile_spec' => [
                                'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                            ],
                            'official_spec' => [
                                'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                            ]
                        ],
                    ];
                } else {
                    $data['creative_components']['brand'] = [
                        [
                            'value' => [
                                'jump_info' => [
                                    "page_type" => $other_setting->getProfileInfo($param->account_id)->page_type == 'PAGE_TYPE_OFFICIAL' ? 'PAGE_TYPE_OFFICIAL' : "PAGE_TYPE_H5_PROFILE",
                                    "page_spec" => [
                                        'h5_profile_spec' => [
                                            'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                                        ],
                                        'official_spec' => [
                                            'page_id' => (int)$other_setting->getProfileInfo($param->account_id)->profile_id,
                                        ]
                                    ],
                                ]
                            ]
                        ]
                    ];
                }
            }

            foreach ($param->word_list as $description) {
                $data['creative_components']['description'][] = ['value' => ['content' => trim(str_replace("\t", '', $description))]];
            }

            $other_setting->button_text && $data['creative_components']["action_button"] = [
                [
                    "value" => [
                        "button_text" => trim($other_setting->button_text),
                        "jump_info" => $this->getPageInfo($param)
                    ]
                ]
            ];

            if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {
                if ((!$other_setting->mini_card_link_switch && !$other_setting->getPlayablePageInfo($param->account_id)['playable_page_path']) && $other_setting->link_name_type) {
                    $data['creative_components']["text_link"] = [
                        [
                            "value" => [
                                "link_name_type" => trim($other_setting->link_name_type),
                                "jump_info" => $this->getPageInfo($param),
                                "link_name_text" => $other_setting->link_name_type == 'LINK_NAME_TEXT_TEMPLATE' ? $other_setting->link_name_type_text : ''
                            ]
                        ]
                    ];
                }
                if ($other_setting->mini_card_link_switch) {
                    $data['creative_components']['mini_card_link'] = [
                        [
                            'value' => [
                                'jump_info' => $this->getPageInfo($param),
                                'mini_card_link_description' => $other_setting->mini_card_link_spec['desc'],
                                'mini_card_link_image' => $other_setting->mini_card_link_spec['image'],
                                'mini_card_link_button_text' => $other_setting->button_text,
                                'mini_card_link_switch' => true,
                            ]
                        ]
                    ];
                }
            }

            if ($other_setting->promotion_card_id && $other_setting->button_text && in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                $data['creative_components']['floating_zone'] = [
                    [
                        'value' => [
                            "floating_zone_switch" => true,
                            "floating_zone_image_id" => $other_setting->promotion_card_id,
                            "floating_zone_name" => $other_setting->promotion_title,
                            "floating_zone_desc" => $other_setting->promotion_desc,
                            "floating_zone_type" => "FLOATING_ZONE_TYPE_IMAGE_TEXT",
                            "floating_zone_button_text" => $other_setting->button_text
                        ]
                    ]
                ];
            }

            if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                $data['creative_components']["wechat_channels"] = [
                    [
                        'value' => [
                            "username" => $other_setting->getVideoNumberId($param->account_id),
                            "finder_object_visibility" => $other_setting->finder_object_visibility
                        ]
                    ]
                ];
            }

            if ($other_setting->site_set_none == 'auto') {

                $playable_path = $other_setting->getPlayablePageInfo($param->account_id)['playable_page_path'];
                if ($playable_path) {

                    $image = '';
                    foreach ($param->creative_list as $creative) {
                        if (isset($creative['image_info'])) {
                            $image = (string)($material_media_id_map[$creative['image_info']['id']]['id']);
                        }
                    }

                    $data['creative_components']['wxgame_playable_page'] = [
                        [
                            "value" => [
                                "wxgame_playable_page_switch" => true,
                                "wxgame_playable_page_path" => $playable_path,
                                "wxgame_playable_page_end_cover_img" => $image,
                                "wxgame_playable_page_end_desc" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_trigger_types" => ['WXGAME_PLAYABLE_PAGE_TRIGGER_TYPE_CARD_LINK'],
                                "wxgame_playable_page_trigger_text" => $other_setting->wxgame_playable_page_end_desc,
                                "wxgame_playable_page_card_link_image" => $image,
                                "wxgame_playable_page_card_link_description" => $other_setting->wxgame_playable_page_end_desc,
                            ]

                        ]
                    ];
                }

                $data['creative_components']["wechat_channels"] = [
                    [
                        "value" => [
                            "username" => $other_setting->getVideoNumberId($param->account_id),
                            "finder_object_visibility" => $other_setting->finder_object_visibility
                        ]
                    ]
                ];

                if ($other_setting->promotion_card_id && $other_setting->button_text) {
                    $data['creative_components']['floating_zone'] = [
                        [
                            'value' => [
                                "floating_zone_switch" => true,
                                "floating_zone_image_id" => $other_setting->promotion_card_id,
                                "floating_zone_name" => $other_setting->promotion_title,
                                "floating_zone_desc" => $other_setting->promotion_desc,
                                "floating_zone_type" => "FLOATING_ZONE_TYPE_IMAGE_TEXT",
                                "floating_zone_button_text" => $other_setting->button_text
                            ]
                        ]
                    ];
                }
            }

            // 视频号一定要在下面因为优先级最高
            if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {

                if (isset($data['creative_components']['brand'][0])) {
                    $data['creative_components']['brand'][0]['value']['jump_info'] = [
                        "page_type" => "PAGE_TYPE_WECHAT_CHANNELS_PROFILE",
                        "page_spec" => [
                            'wechat_channels_profile_spec' => [
                                'username' => $other_setting->getVideoNumberId($param->account_id),
                            ]
                        ],
                    ];
                } else {
                    $data['creative_components']['brand'] = [
                        [
                            'value' => [
                                'jump_info' => [
                                    "page_type" => "PAGE_TYPE_WECHAT_CHANNELS_PROFILE",
                                    "page_spec" => [
                                        'wechat_channels_profile_spec' => [
                                            'username' => $other_setting->getVideoNumberId($param->account_id),
                                        ]
                                    ],
                                ]
                            ]
                        ]
                    ];
                }
            }

            $already_make_list = [];

            if ($other_setting->isHuxuanMaterialMode()) {
                // 文档不支持 但媒体后台可以 尝试一手
                foreach ($other_setting->huxuan_material_map[$param->account_id] as $huxuan_material) {
                    $data['creative_components']['video'][] = [
                        'value' => [
                            'video_id' => $huxuan_material['video_id'],
                        ]
                    ];
                }
            } else {
                foreach ($param->creative_list as $creative) {
                    if (isset($creative['video_info'])) {
                        if (!in_array((string)($material_media_id_map[$creative['video_info']['id']]['id']), $already_make_list)) {
                            $data['creative_components']['video'][] = [
                                'value' => [
                                    'video_id' => (string)($material_media_id_map[$creative['video_info']['id']]['id']),
                                    'cover_id' => (string)($material_media_id_map[$creative['cover_info']['id']]['id']),
                                ]
                            ];
                            $already_make_list[] = (string)($material_media_id_map[$creative['video_info']['id']]['id']);
                        }
                    } elseif (isset($creative['image_info'])) {
                        if (isset($creative['image_info']['image_list']) && $creative['image_info']['image_list']) {
                            $value = ['value' => ['list' => []]];
                            if (!in_array(md5(json_encode($creative['image_info']['image_list'])), $already_make_list)) {
                                foreach ($creative['image_info']['image_list'] as $image) {

                                    $value['value']['list'][] = [
                                        'image_id' => (string)($material_media_id_map[$image['id']]['id']),
                                    ];
                                }
                                $already_make_list[] = md5(json_encode($creative['image_info']['image_list']));
                                $data['creative_components']['image_list'][] = $value;
                            }
                        } else {
                            if (!in_array((string)($material_media_id_map[$creative['image_info']['id']]['id']), $already_make_list)) {
                                $data['creative_components']['image'][] = [
                                    'value' => [
                                        'image_id' => (string)($material_media_id_map[$creative['image_info']['id']]['id'])
                                    ]
                                ];
                                $already_make_list[] = (string)($material_media_id_map[$creative['image_info']['id']]['id']);
                            }
                        }
                    }
                }
            }

            // 一键下载
            if ($other_setting->isWechatSingleSite() && $other_setting->click_download) {
                unset($data['creative_components']['jump_info']);
                $data['creative_components']['main_jump_info'][] = [
                    'value' => $this->getPageInfo($param),
                ];
            }
        }

        if ($other_setting->site_set_none == 'auto') {
            $data['creative_components']["wechat_channels"] = [
                [
                    "value" => [
                        "username" => $other_setting->getVideoNumberId($param->account_id),
                        "finder_object_visibility" => $other_setting->finder_object_visibility
                    ]
                ]
            ];

            if ($other_setting->promotion_card_id && $other_setting->button_text) {
                $data['creative_components']['floating_zone'] = [
                    [
                        'value' => [
                            "floating_zone_switch" => true,
                            "floating_zone_image_id" => $other_setting->promotion_card_id,
                            "floating_zone_name" => $other_setting->promotion_title,
                            "floating_zone_desc" => $other_setting->promotion_desc,
                            "floating_zone_type" => "FLOATING_ZONE_TYPE_IMAGE_TEXT",
                            "floating_zone_button_text" => $other_setting->button_text
                        ]
                    ]
                ];
            }
        }

        // 朋友圈首条评论
        if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {
            if ($other_setting->moment_first_comment) {
                $data['creative_components']['social_skill'] = [
                    [
                        'value' => [
                            'social_skill_first_comment_switch' => true,
                            'social_skill_first_comment' => $other_setting->moment_first_comment,
                        ]
                    ]
                ];

                if ($other_setting->moment_first_comment_account_type == 'official') {
                    $data['creative_components']['brand'][0]['value']['jump_info']['page_type'] = 'PAGE_TYPE_WECHAT_OFFICIAL_ACCOUNT_DETAIL';
                    $data['creative_components']['brand'][0]['value']['jump_info']['page_spec'] = [
                        'wechat_official_account_detail_spec' => [
                            "app_id" => $other_setting->getFirstCommentOfficialAccountInfo($param->account_id)->promoted_object_id ?? ''
                        ]
                    ];
                } elseif ($other_setting->moment_first_comment_account_type == 'video') {
                    $data['creative_components']['brand'][0]['value']['jump_info']['page_type'] = 'PAGE_TYPE_WECHAT_CHANNELS_PROFILE';
                    $data['creative_components']['brand'][0]['value']['jump_info']['page_spec'] = [
                        'wechat_channels_profile_spec' => [
                            "username" => $other_setting->getFirstCommentVideoNumberInfo($param->account_id)->promoted_object_id ?? ''
                        ]
                    ];
                } elseif ($other_setting->moment_first_comment_account_type == 'custom') {
                    $data['creative_components']['brand'][0]['value'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_image_id" => $setting->brand_img
                    ];
                }
            }
        }

        if (
            (in_array($data['creative_template_id'], [721]) && in_array('SITE_SET_CHANNELS', $other_setting->site_set) && count($other_setting->site_set) == 1)
        ) {
            unset($data['creative_components']['action_button']);
        }

//        try {
        $result_ids[] = $create_model->add(new AdDynamicCreativeCreateParam($data));
//        } catch (Throwable $e) {
//            if (
//                !(
//                    (strpos($e->getMessage(), '创意与创意id') !== false && strpos($e->getMessage(), '重复，请修改') !== false) ||
//                    (strpos($e->getMessage(), '组件与组件id') !== false && strpos($e->getMessage(), '相似度较高') !== false && strpos($e->getMessage(), '建议使用已有组件') !== false)
//                )
//            ) {
//                throw $e;
//            }
//        }

        return $result_ids;
    }

    /**
     * 自定义创意名称
     * @param $param
     * @param $creative
     * @return string
     */
    public function getCustomAd3Name($param, $creative = [])
    {
        /** @var \App\Param\ADServing\Tencent\V3\ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $creative_list = $creative ? [$creative] : $param->creative_list;

        $name = '';
        foreach ($other_setting->ad3_name as $ad3_name) {
            switch ($ad3_name) {
                case 'date_time':
                    $name .= date('Y-m-d H:i:s');
                    break;
                case 'random':
                    $code = '';
                    for ($i = 1; $i <= 4; $i++) {
                        $code .= chr(rand(97, 122));
                    }
                    $name .= $code;
                    break;
                case 'material_name':
                    $creative_value = $creative_list[0];
                    if (isset($creative_value['cover_info'])) {
                        $id = $creative_value['video_info']['id'] ?? '';
                    } else {
                        $id = $creative_value['image_info']['id'] ?? '';
                    }
                    if ($id) {
                        $material_file_info = (new MaterialFileModel())->getData($id);
                        if ($material_file_info) {
                            $material_info = (new MaterialModel())->get($material_file_info->material_id, $material_file_info->platform);
                            if ($material_info) {
                                $name .= $material_info->name;
                            }
                        }
                    }
                    break;
                case 'material_id':
                    $creative_value = $creative_list[0];
                    if (isset($creative_value['cover_info'])) {
                        $id = $creative_value['video_info']['id'] ?? '';
                    } else {
                        $id = $creative_value['image_info']['id'] ?? '';
                    }
                    if ($id) {
                        $material_file_info = (new MaterialFileModel())->getData($id);
                        if ($material_file_info) {
                            $name .= $material_file_info->material_id;
                        }
                    }
                    break;
                default:
                    $name .= $ad3_name;
            }
        }
        return $name;
    }

    public function makeProgramModelAndGetID(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $already_make_list = [];

        $create_material_groups = [];
        foreach ($param->creative_list as $creative) {
            if (isset($creative['video_info'])) {
                if (!in_array($material_media_id_map[$creative['video_info']['id']]['id'], $already_make_list)) {
                    $create_material_groups[] = [
                        'materials' => [[
                            'type' => 'VIDEO',
                            'media_id' => "{$material_media_id_map[$creative['video_info']['id']]['id']}"
                        ]]
                    ];
                    $already_make_list[] = $material_media_id_map[$creative['video_info']['id']]['id'];
                }
            } else if (isset($creative['image_info'])) {
                if (isset($creative['image_info']['image_list']) && $creative['image_info']['image_list']) {
                    $m_s = '';
                    $m = [
                        'materials' => []
                    ];
                    foreach ($creative['image_info']['image_list'] as $image) {
                        $m['materials'][] = [
                            'type' => 'IMAGE',
                            'media_id' => $material_media_id_map[$image['id']]['id']
                        ];
                        $m_s .= $material_media_id_map[$image['id']]['id'];
                    }

                    if (!in_array($m_s, $already_make_list)) {
                        $create_material_groups[] = $m;
                        $already_make_list[] = $m_s;
                    }
                } else {
                    if (!in_array($material_media_id_map[$creative['image_info']['id']]['id'], $already_make_list)) {
                        $create_material_groups[] = [
                            'materials' => [[
                                'type' => 'IMAGE',
                                'media_id' => $material_media_id_map[$creative['image_info']['id']]['id']
                            ]]
                        ];
                        $already_make_list[] = $material_media_id_map[$creative['image_info']['id']]['id'];
                    }
                }
            }
        }

        $make_param = [
            'account_id' => $account_param->account_id,
            'adgroup_id' => $param->ad1_id,
            'auto_derived_program_creative_switch' => false,
            'standard_switch' => false,
            'create_material_groups' => $create_material_groups
        ];

        $resp = (new ProgrammedModel())->addProgrammed($account_param->access_token, $make_param);

        $material_derive_id = $resp['material_derive_id'];

        $programmed_info = (new ProgrammedModel())->getProgrammed($account_param->account_id, $account_param->access_token, $material_derive_id);

        $programmed_data = [];
        if ($programmed_info) {
            foreach ($programmed_info['material_groups'] as $pg) {
                if ($pg['ratio'] == '1:1') {
                    $programmed_data[] = [
                        'original_material_id_list' => array_column($pg['materials'], 'media_id'),
                        'original_adcreative_template_id_list' => $pg['original_creative_templates'][0]['creative_template_ids'],
                    ];
                } else {
                    $programmed_data[] = [
                        'original_material_id_list' => [$pg['materials'][0]['media_id']],
//                        "original_cover_image_id" => "**********",
                        'original_adcreative_template_id_list' => $pg['original_creative_templates'][0]['creative_template_ids'],
                    ];
                }
            }
        }

        return ['material_derive_id' => $material_derive_id, 'material_derive_info' => $programmed_data];
    }

    /**
     * 创建创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     * @throws Throwable
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var \App\Param\ADServing\Tencent\V3\ADSettingContentParam $setting */
        $setting = $param->setting;
        /** @var \App\Param\ADServing\Tencent\V3\ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        // icon上传
        if ($setting->brand_img) {
            $setting->brand_img = $this->uploadImageByUrl($param, $account_param, $setting->brand_img);
        }
        // 卖点图上传
        if ($setting->shop_img) {
            $setting->shop_img = $this->uploadImageByUrl($param, $account_param, $setting->shop_img);
        }
        // 主播卡
        if ($other_setting->promotion_card_id) {
            $other_setting->promotion_card_id = $this->uploadImageByUrl($param, $account_param, $other_setting->promotion_card_id);
        }
        // 图文图片上传
        if ($other_setting->mini_card_link_switch && $other_setting->mini_card_link_spec['image']) {
            $other_setting->mini_card_link_spec['image'] = $this->uploadImageByUrl($param, $account_param, $other_setting->mini_card_link_spec['image']);
        }
        // 试玩结束页图片上传
        if ($other_setting->wxgame_playable_cover) {
            $other_setting->wxgame_playable_cover = $this->uploadImageByUrl($param, $account_param, $other_setting->wxgame_playable_cover);
        }

        if ($param->creative_mode === BatchAD::CREATIVE_CUSTOM_MODE) {
            return $this->createCommonCreative($param, $account_param, $material_media_id_map);
        } else {
            return $this->createComponentCreative($param, $account_param, $material_media_id_map) ?: [];
        }

    }


    /**
     * 新建广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return void
     * @throws RedisException
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        /* @var \App\Param\ADServing\Tencent\V3\ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ($other_setting->site_set_none === 'search') {
            (new MediaTencent())->createAD($param, $account_param, $material_media_id_map);
            return;
        }

        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName(
                    $param,
                    $account_param->account_id,
                    $account_param->access_token
                ) ?: $this->createAD1($param, $account_param);
                $param->ad2_id = $param->ad1_id;
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {

                if (strpos($e->getMessage(), '该广告存在相似广告') !== false || strpos($e->getMessage(), '前往查看已有广告')) {
                    $ad1_id = 0;

                    if (strpos($e->getMessage(), '该广告存在相似广告') !== false) {
                        $matches = [];
                        $pattern = "/该广告存在相似广告(.*?)，/";
                        preg_match($pattern, $e->getMessage(), $matches);

                        $ad1_id = $matches[1] ?? null;
                    }

                    if (strpos($e->getMessage(), '前往查看已有广告') !== false) {
                        $matches = [];
                        $pattern = "/前往查看已有广告（(.*?)）/";
                        preg_match($pattern, $e->getMessage(), $matches);

                        $ad1_id = $matches[1] ?? null;
                    }

                    if ($ad1_id) {
                        $ad1_id = trim($ad1_id, " \n\r\t\v\0[]");
                        $result = (new AdGroupModel())->info(
                            $account_param->account_id,
                            $account_param->access_token,
                            [
                                [
                                    'field' => 'adgroup_id',
                                    'operator' => 'EQUALS',
                                    'values' => [$ad1_id],
                                ]
                            ],
                        );

                        if (count($result['list']) > 0) {
                            $adgroup_name = $result['list'][0]['adgroup_name'] ?? '';
                            $param->ad1_id = $ad1_id;
                            $param->ad2_id = $ad1_id;
                            $param->ad1_name_text = $adgroup_name;
                            $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                            $ad_task_model->updateTask($param, $param->id);
                        } else {
                            $ad_task_model->updateTask($param, $param->id);
                            throw new AppException("新建广告一级错误:账户下找不到的广告一级($ad1_id)的信息-" . $e->getMessage());
                        }
                    } else {
                        $ad_task_model->updateTask($param, $param->id);
                        throw new AppException('新建广告一级错误:' . $e->getMessage());
                    }
                } else {
                    $ad_task_model->updateTask($param, $param->id);
                    throw new AppException('新建广告一级错误:' . $e->getMessage());
                }
            }
        }

        if ($targeting->search_scene_switch == '1' && $targeting->search_expand_bidword_switch == 'on' && count($targeting->search_expand_bidword_list) > 0) {
            $list = [];
            foreach ($targeting->search_expand_bidword_list as $word) {
                $list[] = [
                    'adgroup_id' => $param->ad1_id,
                    'bidword' => $word['word'],
                    'match_type' => $word['match_type'],
                ];
            }

            (new BidWordModel())->add(
                $account_param->account_id,
                $account_param->access_token,
                $list
            );

        }


        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {

            if ($param->creative_mode === BatchAD::CREATIVE_CUSTOM_MODE) {
                $c_count = count($param->creative_list);
            } else {
                $c_count = 1;
            }

            if (
                !(new ADTaskTencentFilterModel())->incrBy(
                    $other_setting->getVideoNumberId($param->account_id),
                    $c_count
                )
            ) {
                throw new AppException('视频号频次控制', ADTaskDigestLogic::RESTART_TASK_CODE);
            }
        }

        // 新建动态广告
        if (!$param->ad3_ids) {
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建动态广告错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
    }

    /**
     * 修改腾讯3.0 二、三级广告开关
     * @param $input
     * @param $format_target
     * @param $media_type
     * @return array
     */
    public function updateADAnalysisStatus($media_type, $input, $format_target)
    {
        if (3 === $input['ad_level']) {
            //腾讯系三级
//            if (MediaType::TENCENT === $media_type) {
            $ad_model = new OdsTencentAdLogModel();
            $ad_his_log_model = new OdsTencentADHisLogModel();
//            } else {
//                $ad_model = new OdsWeChatCampaignLogModel();
//                $ad_his_log_model = new OdsWechatCampaignHisLogModel();
//            }
            $http_model = new DynamicCreativeModel();
            $ad_id_type = 'ad_id';
            $request_ad_type = 'dynamic_creative_id';
            $ad_name_type = 'ad_name';

        } elseif (2 === $input['ad_level']) {
            //腾讯系二级
//            if (MediaType::TENCENT === $media_type) {
            $ad_model = new OdsTencentAdGroupLogModel();
            $ad_his_log_model = new OdsTencentAdGroupHisLogModel();
//            } else {
//                $ad_model = new OdsWeChatAdGroupLogModel();
//                $ad_his_log_model = new OdsWechatAdGroupHisLogModel();
//            }
            $http_model = new AdGroupModel();
            $ad_id_type = 'adgroup_id';
            $request_ad_type = 'adgroup_id';
            $ad_name_type = 'adgroup_name';
        } else {
            //腾讯三级
            throw new AppException('腾讯3.0 一级禁止开关');
        }
        $close = 'AD_STATUS_SUSPEND';
        $open = 'AD_STATUS_NORMAL';

        $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        $result = $ad_model->getAccountId($format_target, $column);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];
        if (0 === $input['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = 2;
            $request_data['configured_status'] = $close;
            $switch_option = $close;
            $edit_detail = '关闭';
        } else {
            $single_record_operate['edit_type'] = 1;
            $request_data['configured_status'] = $open;
            $switch_option = $open;
            $edit_detail = '开启';
        }

        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($result as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['ad_level'] = $input['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update_switch = [];
        foreach ($result as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->$ad_id_type;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data[$request_ad_type] = (int)$v->$ad_id_type;
            try {
                $http_model->update($request_data, $access_tokens[$v->account_id]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;
                $update_switch[$v->account_id][] = $request_data[$request_ad_type];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "失败，错误信息：" .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }

            $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->$ad_name_type;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_type;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update_switch && $media_type === MediaType::TENCENT) {
            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                'configured_status',
                $switch_option,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表腾讯3.0预算和出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $update_field = 'budget' === $update_field ? 'daily_budget' : 'bid_amount';
        $column = ['adgroup_id', 'adgroup_name', 'account_id', 'platform', $update_field];
        $account_info = (new OdsTencentAdGroupLogModel())->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $opt_cn_name = '预算';
        if ('bid_amount' === $update_field) {
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BID;
            $opt_cn_name = '出价';
        }
        $http_model = new AdGroupModel();
        //即时修改到数据库的数据
        $update_data = [];
        $request_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_return_data['value'] = 0;
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->adgroup_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            //保存原始值
            $original_value = $v->$update_field;
            if (1 === (int)$data['change_type']) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === (int)$data['change_type']) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } elseif (3 === (int)$data['change_type']) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } else {
                throw new AppException('参数错误');
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data['adgroup_id'] = (int)$v->adgroup_id;
            $request_data[$update_field] = $v->$update_field * 100;

            if (1 === (int)$data['execute_type']) {
                //即时修改，直接请求腾讯修改
                try {
                    $http_model->update($request_data, $access_tokens[$v->account_id]);
                    //修改成功
                    $update_data[$v->account_id][] = $v->adgroup_id;
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = (string)$v->$update_field;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name . '修改成功，由[' . $original_value . ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    //修改失败
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //操作失败
                    $single_return_data['message'] = $e->getMessage();
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name . "修改失败，错误信息：" . $failed_info;
                }
            } elseif (0 === (int)$data['execute_type']) {
                //定时修改，将请求数据汇总之后放入RabbitMQ
                $single_return_data['status'] = $single_record_operate['status'] = 1;
                $single_return_data['value'] = $original_value;
                $single_return_data['message'] = $single_record_operate['edit_detail'] =
                    $opt_cn_name . '修改准备' . $data['execute_time'] . '定时执行，由[' . $original_value .
                    ']修改为[' . (string)$v->$update_field . ']';
                //$request_data存入session信息,access_token消费队列时重新查
                $request_data['editor_id'] = $single_record_operate['editor_id'];
                $request_data['editor_name'] = $single_record_operate['editor_name'];
                $request_data['media_type'] = $media_type;
                $request_data['port_version'] = '3.0';
                $request_data['request_type'] = $update_field;
                $timing_execute_data[] = $request_data;
            }
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->adgroup_id;
            $single_record_operate['ad_name'] = $v->adgroup_name;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update_data && $media_type === MediaType::TENCENT) {
            //组合要update的sql
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, 2 ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        if ($timing_execute_data) {
            $time = strtotime($data['execute_time']) - time();
            if ('bid_amount' === $update_field) {
                (new ADAnalysisMQLogic())->produceDelayChangeBidTask($timing_execute_data, $time);
            } elseif ('daily_budget' === $update_field) {
                (new ADAnalysisMQLogic())->produceDelayChangeBudgetTask($timing_execute_data, $time);
            }
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 处理基本报表定时修改出价预算RabbitMQ
     * @param int $media_type
     * @param array $data
     * @return mixed|void
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        //腾讯二级表查询条件
        $condition = [];
        foreach ($data as $v) {
            $condition[$v['account_id']][] = $v['adgroup_id'];
        }
        unset($v);
        if (!$condition) {
            return;
        }
        $account_ids = array_keys($condition);
        $ad_info = (new OdsTencentAdGroupLogModel())->getADInfoByAccountIdAndADId($condition);
        $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids);
        if ($ad_info->isEmpty() || $access_token_info->isEmpty()) {
            return;
        }
        //整合access_token方便后面使用
        $access_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
        }
        unset($v);

        $http_model = new AdGroupModel();
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];

        $opt_cn_name = ['bid_amount' => '出价', 'daily_budget' => '预算'];
        foreach ($ad_info as $value) {
            foreach ($data as $v) {
                if ((int)$value->adgroup_id !== (int)$v['adgroup_id'] ||
                    (int)$value->account_id !== (int)$v['account_id']) {
                    continue;
                }
                $request_type = $v['request_type'];
                $single_record_operate['edit_type'] = $request_type === 'bid_amount' ? 7 : 3;
                $request_data = [];
                $request_data['account_id'] = (int)$v['account_id'];
                $request_data['adgroup_id'] = (int)$v['adgroup_id'];
                $request_data[$request_type] = $v[$request_type];

                try {
                    $http_model->update($request_data, $access_token[$v['account_id']]);
                    //修改成功
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = Math::div($v[$request_type], 100, 2);
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$request_type] . '定时修改成功，由[' . $value->$request_type . ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //修改失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //操作失败
                    $single_return_data['message'] = $e->getMessage();
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$request_type] . "定时修改失败，错误信息：" . $failed_info;
                }
                $single_record_operate['platform'] = $value->platform;
                $single_record_operate['account_id'] = (int)$value->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$value->adgroup_id;
                $single_record_operate['ad_name'] = $value->adgroup_name;
                $single_record_operate['editor_id'] = $v['editor_id'];
                $single_record_operate['editor_name'] = $v['editor_name'];
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($v);
        }
        unset($value);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
    }

    /**
     * 修改基本报表腾讯3.0二级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        $column = ['adgroup_id', 'adgroup_name', 'account_id', 'platform'];
        $account_info = (new OdsTencentAdGroupLogModel())->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];
        //避免循环查库，手动来组合广告数据
        if (isset($data['ad_name'])) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SECOND_CLASS_AD_NAME;
            $update_value = $request_data['adgroup_name'] = $data['ad_name'];
            $edit_detail = '广告二级名';
            $update_field = 'adgroup_name';
        } else {
            //修改schedule_time
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SCHEDULE_TIME;
            $update_value = $request_data['time_series'] = $data['schedule_time'];
            $edit_detail = '广告投放时段';
            $update_field = 'time_series';
        }

        $http_model = new AdGroupModel();
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->adgroup_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data['adgroup_id'] = (int)$v->adgroup_id;
            try {
                $http_model->update($request_data, $access_tokens[$v->account_id]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $update_value;
                $update[$v->account_id][] = $request_data['adgroup_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "修改成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->adgroup_name;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->adgroup_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update && $media_type === MediaType::TENCENT) {
            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_value,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表-批量修改腾讯3.0深度优化出价或ROI
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $column = ['adgroup_id', 'adgroup_name', 'account_id', 'platform', 'deep_conversion_behavior_goal',
            'deep_conversion_behavior_bid_amount', 'deep_conversion_worth_expected_roi', 'smart_delivery_platform', 'smart_delivery_scene_spec'];
        $account_info = (new OdsTencentAdGroupLogModel())->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];

        if ('deep_bid' === $update_field) {
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_DEEP_BID;
            $update_field = 'deep_conversion_behavior_bid_amount';
            $media_field = 'deep_conversion_behavior_bid';
            $edit_detail = '深度优化行为的出价';
            $decimal = 2;
        } else {
            //修改ROI
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_ROI;
            $update_field = 'deep_conversion_worth_expected_roi';
            $media_field = 'deep_conversion_worth_rate';
            $edit_detail = '深度优化价值的期望ROI';
            $decimal = 3;
        }

        $http_model = new AdGroupModel();
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->adgroup_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            //向媒体发起请求的数据
            $request_data = [];
            $request_data['account_id'] = (int)$v->account_id;
            $request_data['adgroup_id'] = (int)$v->adgroup_id;
            $original_value = $v->$update_field;
            if (1 === intval($data['change_type'])) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === intval($data['change_type'])) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    $decimal, '.', '');
            } elseif (3 === intval($data['change_type'])) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    $decimal, '.', '');
            } else {
                throw new AppException('参数错误');
            }

            if ('deep_conversion_behavior_bid' === $media_field) {
                if ($v->$update_field < 1 || $v->$update_field > 500000) {
                    throw new AppException('深度优化出价允许范围为：1-500000');
                }
                $request_data[$media_field] = $v->$update_field * 100;
            }
            if ('deep_conversion_worth_rate' === $media_field) {
                if ($v->smart_delivery_scene_spec) {
                    //如果是智投
                    $smart_delivery_scene_spec = json_decode($v->smart_delivery_scene_spec, true);
                    if (isset($smart_delivery_scene_spec['smart_delivery_goal'])) {
                        if (TencentEum::SMART_DELIVERY_GOAL_MAP['MINI_GAME']['GOAL_1DAY_PURCHASE_ROAS'] === $smart_delivery_scene_spec['smart_delivery_goal']) {
                            $roi_key = 'first_day_purchase_roi';
                        } else {
                            $roi_key = 'seven_day_purchase_roi';
                        }
                        $smart_delivery_scene_spec['smart_delivery_goal_spec']['mini_game_promotion_spec'][$roi_key] = (double)$v->$update_field;
                        $request_data['smart_delivery_scene_spec'] = $smart_delivery_scene_spec;
                    }
                } else {
                    //普通3.0
                    if ($v->$update_field < 0.01 || $v->$update_field > 1000) {
                        throw new AppException('深度转化ROI系数允许范围为：0.01-1000');
                    }
                    $request_data[$media_field] = (float)$v->$update_field;
                }

            }

            try {
                $http_model->update($request_data, $access_tokens[$v->account_id]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->$update_field;
                $update[$v->account_id][] = $request_data['adgroup_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "由[" . $original_value . ']修改为[' . $v->$update_field . ']' . "，修改成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "由[" . $original_value . ']修改为[' . $v->$update_field . ']' . "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->adgroup_name;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->adgroup_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update && $media_type === MediaType::TENCENT) {
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 删除基本报表二三级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return array|mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
//        if (MediaType::TENCENT === $media_type) {
//            $class_half_name = 'App\\Model\\SqlModel\\DataMedia\\OdsTencent';
//        } else {
//            $class_half_name = 'App\\Model\\SqlModel\\DataMedia\\OdsWechat';
//        }
        if (3 === $data['ad_level']) {
            //腾讯三级
            $ad_model = new OdsTencentAdLogModel;
            $ad_his_log_model = new OdsTencentAdHisLogModel;
            $http_model = new DynamicCreativeModel();
            $request_id_type = 'dynamic_creative_id';
            $ad_id_type = 'ad_id';
            $ad_name_type = 'ad_name';
        } elseif (2 === $data['ad_level']) {
            //腾讯系二级
            $ad_model = new OdsTencentAdGroupLogModel;
            $ad_his_log_model = new OdsTencentAdGroupHisLogModel;
            $http_model = new AdGroupModel();
            $request_id_type = 'adgroup_id';
            $ad_id_type = 'adgroup_id';
            $ad_name_type = 'adgroup_name';
        } else {
            //腾讯系一级
            throw new AppException('腾讯一级广告禁止删除');
        }

        $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        $result = $ad_model->getAccountId($ad_format_target, $column);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;

        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($result as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update_switch = [];
        foreach ($result as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->$ad_id_type;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            if (isset($data['risk_operate'][$media_type][$v->$ad_id_type]) &&
                $data['risk_operate'][$media_type][$v->$ad_id_type]) {
                $risk_operate = '本次为风险操作';
                $risk_code = 1;
            } else {
                $risk_operate = '';
                $risk_code = 0;
            }
            $request_data['account_id'] = (int)$v->account_id;
            $request_data[$request_id_type] = (int)$v->$ad_id_type;
            $access_token = $access_tokens[$v->account_id];
            try {
                $http_model->delete($request_data, $access_token);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = 'AD_STATUS_SUSPEND';
                $update_switch[$v->account_id][] = $request_data[$ad_id_type];
                $single_record_operate['status'] = $single_return_data['status'] = $risk_code ? ADAnalysisModel::SUCCESS_WITH_RISK : ADAnalysisModel::SUCCESS;
                $single_record_operate['edit_detail'] = "删除成功 " . $risk_operate;
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = $risk_code ? ADAnalysisModel::FAILED_WITH_RISK : ADAnalysisModel::FAILED;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = "删除失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }

            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->$ad_name_type;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_type;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update_switch) {
            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                'system_status',
                'DYNAMIC_CREATIVE_STATUS_DELETED',
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表腾讯3.0三级名
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisThirdClass(array $data, array $ad_format_target)
    {
        $column = ['ad_id', 'ad_name', 'account_id', 'platform'];
        $account_info = (new OdsTencentAdLogModel())->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_info = $account_info[0];
        $tencent_http_model = new DynamicCreativeModel();
        //获取access_token并注入权限
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$account_info->account_id], null, $leader_permission);
        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 3;
        $record_operate['media_type'] = $data['media_type'];
        $record_operate['editor_id'] = $data['editor_id'];
        $record_operate['edit_type'] = ADAnalysisModel::CHANGE_THIRD_CLASS_AS_NAME;
        $record_operate['platform'] = $account_info->platform;
        $record_operate['editor_name'] = $data['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        $record_operate['account_id'] = (int)$account_info->account_id;
        $record_operate['ad_id'] = $return_data['ad_id'] = (int)$data['ad3_id'];

        if ($access_token->isNotEmpty()) {
            $ad_info = [];
            $access_token = $access_token->pop()->access_token;
            $request_data['account_id'] = (int)$account_info->account_id;
            $request_data['dynamic_creative_id'] = (int)$data['ad3_id'];
            $request_data['dynamic_creative_name'] = $data['ad3_name'];
            try {
                $tencent_http_model->update($request_data, $access_token);
                //修改成功
                $return_data['message'] = 'success';
                $record_operate['ad_name'] = $return_data['value'] = $data['ad3_name'];
                $record_operate['status'] = $return_data['status'] = 1;
                $record_operate['edit_detail'] = "腾讯3.0三级名称由[ {$account_info->ad_name} ] " . "修改为 [ {$data['ad3_name']} ]";
                $ad_info[$request_data['account_id']] = [$request_data['dynamic_creative_id']];
            } catch (AppException $e) {
                //修改失败
                $return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $record_operate['edit_detail'] = "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
                $record_operate['status'] = $return_data['status'] = 2;
                $record_operate['ad_name'] = $account_info->ad_name;
                $return_data['value'] = 0;
            }

            if ($ad_info) {
                (new OdsTencentAdHisLogModel())->getLatestByADInfoAndInsert(
                    $ad_info,
                    'ad_name',
                    $data['ad3_name'],
                    $record_operate['insert_time']
                );
            }
        } else {
            //没有权限操作
            $return_data['message'] = $record_operate['edit_detail'] = '当前用户无操作权限';
            $return_data['value'] = 0;
            $record_operate['status'] = $return_data['status'] = 2;
            $record_operate['ad_name'] = $account_info->ad_name;
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [$return_data];

    }

    /**
     * 修改腾讯一键起量(包含开启和关闭)
     * @param array $data
     * @return array|bool
     */
    public function updateADAnalysisADRaise($data)
    {
        $account_ids = [];
//        $open_all_request_data = [];
//        $open_request_data['auto_acquisition_enabled'] = false;
//        if (1 === (int)$data['raise_switch']) {
        foreach ($data['raise_info'] as $item) {
            if (!in_array($item['account_id'], $account_ids)) {
                $account_ids[] = $item['account_id'];
            }
//                $open_request_data['account_id'] = $item['account_id'];
//                $open_request_data['auto_acquisition_budget'] = $item['auto_acquisition_budget'];
//                $open_request_data['auto_acquisition_enabled'] = true;
//                $open_all_request_data[] = $open_request_data;
        }
        unset($item);

//        }

        //获取access_token
        $leader_permission = $data['leader_permission'];
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }

        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = MediaType::TENCENT;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = ADAnalysisModel::STOP_AD_RAISE;
        if (1 === (int)$data['auto_acquisition_enabled']) {
            $single_record_operate['edit_type'] = ADAnalysisModel::START_AD_RAISE;
        }

        $http_model = new AdGroupModel();
        $update_data = [];
        foreach ($data['raise_info'] as $v) {
            if (!isset($access_tokens[$v['account_id']])) {
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_return_data['value'] = 0;
                $single_record_operate['platform'] = $v['platform'];
                $single_record_operate['account_id'] = (int)$v['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v['adgroup_id'];
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '修改失败：access_token错误';
//                $single_record_operate['ad_name']    = $v['adgroup_name'];
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            try {
                $close_request_data['account_id'] = (int)$v['account_id'];
                $close_request_data['adgroup_id'] = (int)$v['adgroup_id'];
                $close_request_data['auto_acquisition_enabled'] = false;
                $http_model->update($close_request_data, $access_tokens[$v['account_id']]);
                if (1 === (int)$data['auto_acquisition_enabled']) {
                    $open_request_data['account_id'] = (int)$v['account_id'];
                    $open_request_data['adgroup_id'] = (int)$v['adgroup_id'];
                    $open_request_data['auto_acquisition_enabled'] = true;
                    $open_request_data['auto_acquisition_budget'] = intval($data['auto_acquisition_budget'] * 100);
                    $http_model->update($open_request_data, $access_tokens[$v['account_id']]);
                    $single_return_data['message'] = 'success';
                    $single_return_data['value']['gdt_auto_acquisition_enabled'] = 1;
                    $single_return_data['value']['gdt_auto_acquisition_budget'] = $data['auto_acquisition_budget'];
                } else {
                    $single_return_data['message'] = 'success';
                    $single_return_data['value']['gdt_auto_acquisition_enabled'] = 0;
                    $single_return_data['value']['gdt_auto_acquisition_budget'] = $data['auto_acquisition_budget'];
                }
                //修改成功
                $update_data[$v['account_id']][] = $v['adgroup_id'];
                $single_record_operate['status'] = 1;
                $single_record_operate['edit_detail'] = '修改成功';

            } catch (AppException $e) {
                //修改失败
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                //操作失败
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = "修改失败，错误信息：" . $failed_info;

                //操作失败
                $single_return_data['value'] = 0;
//                $single_record_operate['ad_name'] = $v->adgroup_name;
            }
            $single_record_operate['platform'] = $v['platform'];
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_return_data['adgroup_id'] = $single_record_operate['ad_id'] = (int)$v['adgroup_id'];
            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        if ($update_data && count($data['raise_info']) === 1) {
            //只有单条修改插入his表
            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                ['auto_acquisition_enabled', 'auto_acquisition_budget'],
                [
                    $data['auto_acquisition_enabled'],
                    "CAST ( (  {$data['auto_acquisition_budget']} ) AS DECIMAL ( 12, 2 ) )"

                ],
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        if (count($data['raise_info']) > 1) {
            return true;
        }

        return $return_data;
    }

    /**
     * 互选素材
     * @param $condition
     * @return array
     */
    public function getHuxuanMaterialFileList($condition)
    {
        return (new OdsTencentHuxuanVideoLogModel())->getListByCondition(
            $condition,
            $condition['page'] ?? 1,
            $condition['rows'] ?? 20,
        );
    }
}
