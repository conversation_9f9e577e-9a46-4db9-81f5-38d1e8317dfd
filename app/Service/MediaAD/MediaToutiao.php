<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: 张中昊
 * Date: 2020/02/25
 * Time: 10:44
 */

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ADFieldsENToCNMap;
use App\Constant\CityMap;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\Platform;
use App\Constant\PlatId;
use App\Constant\ToutiaoEnum;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\ADAnalysisMQLogic;
use App\Logic\DSP\ADServingLogic;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\ADTaskMasterMQLogic;
use App\Logic\DSP\MaterialPushMQLogic;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\Toutiao\Assets\CreativeComponentModel;
use App\Model\HttpModel\Toutiao\Creative\CustomCreativeModel;
use App\Model\HttpModel\Toutiao\Creative\ProceduralCreativeModel;
use App\Model\HttpModel\Toutiao\CustomerCenter\AdvertiserModel;
use App\Model\HttpModel\Toutiao\Advertiser\AdvertiserModel as AccountHttpModel;
use App\Model\HttpModel\Toutiao\DMP\CustomAudienceModel;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\HttpModel\Toutiao\AudiencePackage\AudiencePackageModel;
use App\Model\HttpModel\Toutiao\Campaign\CampaignModel;
use App\Model\HttpModel\Toutiao\Convert\ConvertModel;
use App\Model\HttpModel\Toutiao\Creative\CreativeModel;
use App\Model\HttpModel\Toutiao\Estimation\EstimationModel;
use App\Model\HttpModel\Toutiao\EventManager\AssetModel;
use App\Model\HttpModel\Toutiao\EventManager\EventModel;
use App\Model\HttpModel\Toutiao\EventManager\TrackUrlModel;
use App\Model\HttpModel\Toutiao\File\FileAudioModel;
use App\Model\HttpModel\Toutiao\File\FileImageModel;
use App\Model\HttpModel\Toutiao\File\FileVideoModel;
use App\Model\HttpModel\Toutiao\Industry\IndustryModel;
use App\Model\HttpModel\Toutiao\InterestAction\InterestActionModel;
use App\Model\HttpModel\Toutiao\InterestInterest\InterestInterestModel;
use App\Model\HttpModel\Toutiao\Keyword\KeywordModel;
use App\Model\HttpModel\Toutiao\Tools\AdRaiseModel;
use App\Model\HttpModel\Toutiao\Tools\AppManagementModel;
use App\Model\HttpModel\Toutiao\Tools\AwemeAuthorInfoModel;
use App\Model\HttpModel\Toutiao\Tools\AwemeCategoryTopAuthorModel;
use App\Model\HttpModel\Toutiao\Tools\AwemeMultiLevelCategoryModel;
use App\Model\HttpModel\Toutiao\Tools\EventConvertModel;
use App\Model\HttpModel\Toutiao\Tools\FlowPackageModel;
use App\Model\HttpModel\Toutiao\Tools\MicroGameModel;
use App\Model\HttpModel\Toutiao\Tools\PlayableModel;
use App\Model\HttpModel\Toutiao\Tools\RTAModel;
use App\Model\HttpModel\Toutiao\Tools\TaskRaiseModel;
use App\Model\HttpModel\Toutiao\Tools\ToolsEventModel;
use App\Model\HttpModel\Toutiao\Tools\WechatGameModel;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\DataMedia\OdsADAnalysisToutiaoRTATargetBindLogModel;
use App\Model\SqlModel\DataMedia\OdsLianShanYunMaterialFileLog;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoADHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAwemeAuthDetailLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCampaignLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCloudGamePlayableLog;
use App\Model\SqlModel\DataMedia\OdsToutiaoCreativeComponentLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCreativeHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCreativeLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCustomAudienceLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoPlayableLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoTransferOperateLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoUnionFlowPackageLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoVideoLogModel;
use App\Model\SqlModel\Tanwan\V2DimAgentLeaderGroupModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\CampaignCreateMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialPushMediaTaskModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADAnalysisToutiaoTransferParam;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADComposeContent\ADComposeConfigParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeImageParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeListParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoCoverParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\ADServing\ADTaskCreateParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Toutiao\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\Toutiao\V2\ADOtherSettingContentParam as V2ADOtherSettingContentParam;
use App\Param\ADServing\Toutiao\ADTargetingContentParam;
use App\Param\ADServing\Toutiao\Basics\ADSettingContentParam;
use App\Param\ADServing\Toutiao\V2\ADSettingContentParam as V2ADSettingContentParam;
use App\Param\AssetCreateParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\EventCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialPushMediaTaskParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Param\SiteConfigParam;
use App\Param\Toutiao\AdCreateMarketParam;
use App\Param\Toutiao\AudiencePackageSearchParam;
use App\Param\Toutiao\CampaignCreateParam;
use App\Param\Toutiao\ConvertSearchCreateParam;
use App\Param\Toutiao\Creative\AdDataParam;
use App\Param\Toutiao\Creative\CreativeCreateMarketParam;
use App\Param\Toutiao\Creative\CreativeListsParam;
use App\Param\Toutiao\Creative\CreativeParam;
use App\Param\Toutiao\Creative\ProceduralCreativeMarketParam;
use App\Param\Toutiao\EventOptimizedGoalParam;
use App\Param\Toutiao\FileAudioUploadParam;
use App\Param\Toutiao\FileImageUploadParam;
use App\Param\Toutiao\FileVideoUploadParam;
use App\Param\Toutiao\IdToWordParam;
use App\Param\Toutiao\IndustryGetParam;
use App\Param\Toutiao\InterestActionCategorySearchParam;
use App\Param\Toutiao\InterestActionKeywordSearchParam;
use App\Param\Toutiao\InterestActionKeywordSuggestionSearchParam;
use App\Param\Toutiao\InterestInterestCategorySearchParam;
use App\Param\Toutiao\InterestInterestKeywordSearchParam;
use App\Param\Toutiao\PlayableSaveParam;
use App\Param\Toutiao\PlayableUploadParam;
use App\Param\Toutiao\PlayableValidateParam;
use App\Param\TrackUrlGroupCreateParam;
use App\Service\PlatformAD\PlatformAD;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Helpers;
use App\Utils\Math;
use Exception;
use Illuminate\Support\Collection;
use Throwable;

class MediaToutiao extends AbstractMedia
{
    /**
     *  获取搜索快投推荐词
     * @param int $ad_id
     * @return array
     */
    public function getFeedDeliverySearchSuggestKeywordList(int $ad_id)
    {
        $ad_info = (new OdsToutiaoAdLogModel())->getDataByAdId($ad_id);
        if (!$ad_info) {
            throw new AppException('找不到对应广告计划下的信息');
        }
        $account_info = (new MediaAccountModel())->getDataByAccountId($ad_info->account_id);
        if (!$account_info) {
            throw new AppException("找不到账号{$ad_info->account_id}的信息");
        }
        return (new KeywordModel())->getFeedDeliverySearchSuggestKeywordList(
            $account_info->account_id,
            $account_info->access_token,
            $ad_id
        );
    }

    /**
     * 上传试玩素材到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadZip(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        $data_param = new PlayableUploadParam($param, $advertiser_id, $access_token);
        $result = (new PlayableModel())->upload($data_param);
        return [
            'id' => $result['playable_id'],
            'url' => ''
        ];
    }

    /**
     * 试玩素材上传校验结果
     * @param $playable_id
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function validateZip($playable_id, $advertiser_id, $access_token)
    {
        $data_param = new PlayableValidateParam($playable_id, $advertiser_id, $access_token);
        $result = (new PlayableModel())->validate($data_param);
        return [
            'id' => $playable_id,
            'url' => $result['playable_url'],
            'status' => $result['status']
        ];
    }

    /**
     * 保存试玩素材
     * @param $playable_id
     * @param $playable_name
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function saveZip($playable_id, $playable_name, $advertiser_id, $access_token)
    {
        $data_param = new PlayableSaveParam($playable_id, $playable_name, $advertiser_id, $access_token);
        $result = (new PlayableModel())->save($data_param);
        return [
            'id' => $result['playable_id'],
            'name' => $result['playable_name'],
            'url' => $result['playable_url'],
            'status' => $result['status']
        ];
    }

    /**
     * 已创建好的广告组列表
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        $campaign_model = new CampaignModel();
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getDataByPlatformAgentId($input['platform'], $input['agent_id']);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $filters = [];
        $input['campaign_name'] && $filters['campaign_name'] = $input['campaign_name'];
        return $campaign_model->info($account_data->account_id, $account_data->access_token, [], $filters, $input['page'], $input['page_size']);
    }

    /**
     * 已创建好的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $ad1_log_model = new CampaignCreateMediaLogModel();
        $campaign_info = $ad1_log_model->getCreateLog(MediaType::TOUTIAO, $account_param->account_id, $param->ad1_name_text);
        $campaign_id = $campaign_info ? $campaign_info->campaign_id : 0;

        if ($campaign_id) {
            $filters = ['ids' => [$campaign_id]];
            $response_data = (new CampaignModel())->info($account_param->account_id, $account_param->access_token, ['id'], $filters, 1, 1);
            $campaign_id = $response_data['list'][0]['id'] ?? 0;
            if (!$campaign_id) {
                $ad1_log_model->deleteLogByID($campaign_info->id);
            }
        }
        return $campaign_id;
    }

    /**
     * 创建广告组-返回创建的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $campaign_data = [
            'operation' => $setting->campaign_operation,
//            'marketing_scene' => ($other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_FORM' || $other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE') ? $other_setting->marketing_scene : '',
            'landing_type' => ToutiaoEnum::PROMOTION_TYPE_APP,
            'budget_mode' => $setting->campaign_budget_mode,
            'campaign_name' => $param->ad1_name_text,
            'campaign_budget_optimization' => $setting->campaign_budget_optimization
        ];

        if ($setting->campaign_budget_mode == 'BUDGET_MODE_DAY') {
            $campaign_data['budget'] = $setting->campaign_budget;
        }

        if ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_LIVE || $other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_GAME) {
            $campaign_data['landing_type'] = ToutiaoEnum::PROMOTION_TYPE_LIVE;
            $campaign_data['marketing_purpose'] = 'CONVERSION';
        }

        if ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_LINK) {
            $campaign_data['landing_type'] = ToutiaoEnum::PROMOTION_TYPE_LINK;
            $campaign_data['marketing_purpose'] = 'CONVERSION';
        }

        if ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT && $param->getSiteConfig()->convert_source_type == ConvertSourceType::H5_API) {
            $campaign_data['landing_type'] = ToutiaoEnum::PROMOTION_TYPE_LINK;
            $campaign_data['marketing_purpose'] = 'CONVERSION';
        }

        if ($param->getSiteConfig()->plat_id == PlatId::DY_MINI) {
            $campaign_data['landing_type'] = ToutiaoEnum::PROMOTION_TYPE_APP;
            $campaign_data['marketing_purpose'] = 'CONVERSION';
        }

        $campaign_data['access_token'] = $account_param->access_token;
        $campaign_data['advertiser_id'] = $account_param->account_id;
        $campaign_data['creator'] = $param->creator;
        $ccp = new CampaignCreateParam($campaign_data);
        $campaign_model = new CampaignModel();
        try {
            $response_data = $campaign_model->create($ccp);
            $campaign_id = $response_data['campaign_id'];
        } catch (AppException $e) {
//            广告组名称不能重复, campaign_id为: ****************
            $msg = $e->getMessage();
            $campaign_id = '';
            if (strpos($msg, '广告组名称不能重复') !== false) {
                $campaign_id = trim(str_replace('广告组名称不能重复, campaign_id为: ', '', $msg));
                $cut_index = strpos($campaign_id, ',');
                $campaign_id = trim(substr($campaign_id, 0, $cut_index));
            }
            if (!is_numeric($campaign_id)) {
                throw $e;
            }
        }
        $this->addAD1Log($param->account_id, $campaign_id, $param->ad1_name_text);
        return $campaign_id;
    }

    /**
     * 一级广告添加日志
     * @param $account_id
     * @param $ad1_id
     * @param $ad1_name
     * @return int
     */
    protected function addAD1Log($account_id, $ad1_id, $ad1_name)
    {
        return (new CampaignCreateMediaLogModel())->addCreateLog(MediaType::TOUTIAO, $account_id, $ad1_id, $ad1_name);
    }

    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        $convert_model = new ConvertModel();
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getDataByPlatformAgentId($input['platform'], $input['agent_id']);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        $csp = new ConvertSearchCreateParam($input);
        return $convert_model->info($csp);
    }

    /**
     * @inheritDoc
     */
    public function createConvert(ConvertCreateParam $param)
    {
        $convert_data = (new ConvertModel())->create($param);
        $convert_data['convert_id'] = $convert_data['id'];
        return $convert_data;
    }

    /**
     * 新建渠道包
     * @param ChannelPackageParam $param
     * @return array
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        try {
            $data = (new AppManagementModel())->createExtendPackage($param);
        } catch (AppException $e) {
            if (strpos($e->getMessage(), '渠道号信息错误') === false) {
                throw $e;
            }
        }

        $data['app_android_channel_package_id'] = $param->site_id;
        $data['download_url'] = "https://apps.bytesfield.com/download/extend/cur/$param->app_id/$param->site_id";
        return $data;
    }

    /**
     * 获取广告计划可用的定向包列表
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        $audience_package_model = new AudiencePackageModel();
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getDataByPlatformAgentId($input['platform'], $input['agent_id']);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        $apsp = new AudiencePackageSearchParam($input);
        return $audience_package_model->info($apsp);
    }

    /**
     * 上传游戏封面图标
     * @param ADTaskParam $param
     * @param $access_token
     * @param array $game_package_thumbnails
     * @return array
     */
//    public function uploadGamePackagePic(ADTaskParam $param, $access_token, array $game_package_thumbnails)
//    {
//        $return_game_package_thumbnails = [];
//        $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
//        $material_file_model = new MaterialFileModel();
//        foreach ($game_package_thumbnails as $key => $value) {
//            // 判断icon是否有上传到媒体
//            $name = basename($value);
//            $data = $material_file_model->getDataByName($name);
//            if ($data) {
//                $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
//                    [$data->id],
//                    $param->platform,
//                    MediaType::TOUTIAO,
//                    $param->account_id
//                );
//
//                $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
//                if ($upload_info_list) {
//                    $return_game_package_thumbnails[] = $upload_info_list[$data->id]->media_material_file_id;
//                } else {
//                    $data = (array)$data;
//
//                    $insert = [];
//                    $insert['platform'] = $param->platform;
//                    $insert['media_type'] = MediaType::TOUTIAO;
//                    $insert['account_id'] = $param->account_id;
//                    $insert['material_id'] = $data['material_id'];
//                    $insert['file_type'] = $data['file_type'];
//                    $insert['file_id'] = $data['id'];
//                    $insert['filename'] = $data['filename'];
//                    $insert['uploader'] = $param->creator;
//                    $insert['signature'] = $data['signature'];
//                    $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
//                    $media_file_info = $this->uploadImage(
//                        new MaterialFileParam($data),
//                        $param->account_id,
//                        $access_token
//                    );
//                    if (!$media_file_info['id']) {
//                        throw new AppException('上传游戏码图片失败,请尝试重启任务');
//                    }
//                    $media_file_id = $media_file_info['id'];
//                    $media_file_url = $media_file_info['url'];
//                    $insert['media_material_file_id'] = $media_file_id;
//                    $insert['url'] = $media_file_url;
//                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
//                    $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
//                    $return_game_package_thumbnails[] = $media_file_id;
//                }
//            } else {
//                throw new AppException("找不到{$name}-游戏礼包图片的信息");
//            }
//        }
//        return $return_game_package_thumbnails;
//    }

    /**
     * 上传应用图标
     * @param ADTaskParam $param
     * @param $access_token
     * @param array $app_thumbnails
     * @return array
     */
    private function uploadAppPic(ADTaskParam $param, $access_token, array $app_thumbnails)
    {
        $return_app_package_thumbnails = [];
        $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
        $material_file_model = new MaterialFileModel();
        foreach ($app_thumbnails as $key => $value) {
            if (!$value) {
                continue;
            }
            // 判断icon是否有上传到媒体
            $name = basename($value);
            $data = $material_file_model->getDataByName($name);
            if ($data) {
                $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
                    [$data->id],
                    $param->platform,
                    MediaType::TOUTIAO,
                    $param->account_id
                );

                $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
                if ($upload_info_list) {
                    $return_app_package_thumbnails[] = $upload_info_list[$data->id]->media_material_file_id;
                } else {
                    $data = (array)$data;

                    $insert = [];
                    $insert['platform'] = $param->platform;
                    $insert['media_type'] = MediaType::TOUTIAO;
                    $insert['account_id'] = $param->account_id;
                    $insert['material_id'] = $data['material_id'];
                    $insert['file_type'] = $data['file_type'];
                    $insert['file_id'] = $data['id'];
                    $insert['filename'] = $data['filename'];
                    $insert['uploader'] = $param->creator;
                    $insert['signature'] = $data['signature'];
                    $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                    $media_file_info = $this->uploadImage(
                        new MaterialFileParam($data),
                        $param->account_id,
                        $access_token
                    );
                    if (!$media_file_info['id']) {
                        throw new AppException('上传应用图片失败,请尝试重启任务');
                    }
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                    $return_app_package_thumbnails[] = $media_file_id;
                }
            } else {
                throw new AppException("找不到{$name}-应用图片的信息");
            }
        }
        return $return_app_package_thumbnails;
    }


    /**
     * 创建营销链路广告计划
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string 返回广告计划ID
     */
    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $site_config = $param->getSiteConfig();
        // 人群包年龄设置
        $targeting->age = $targeting->audienceAge();

        //开启状态
        $setting->ad_operation = $this->getOperation($param);
        $setting_data = $setting->toArray();
        $targeting_data = $targeting->toArray();
        $other_setting_data = $other_setting->toArray();
        $site_config_data = $site_config->toArray();
        $ad_data = array_merge($param->toArray(), $setting_data, $targeting_data, $other_setting_data, $site_config_data);
        $ad_data['app_type'] = $site_config->game_type === 'IOS' ? 'APP_IOS' : 'APP_ANDROID';
        $ad_data['campaign_id'] = $param->ad1_id;
        $ad_data['name'] = $param->ad2_name_text;
        $ad_data['ad_name_text'] = $param->ad2_name_text;
        $ad_data['cpa_bid'] = $setting->getCpaBid();
        $ad_data['roi_goal'] = $setting->getRoiGoal();
        if ($ad_data['deep_bid_type'] && $ad_data['deep_bid_type'] === ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT_7) {
            $ad_data['deep_bid_type'] = ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT;
        }

        if ($param->getSiteConfig()->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
            // 事件管理asset_ids
            $ext_data = $param->getSiteConfig()->ext ?: [];
            $ad_data['asset_ids'] = $ext_data['asset_ids'] ?? [];
            $ad_data['external_actions'] = ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type];
            $ad_data['deep_external_action'] = $param->getSiteConfig()->deep_external_action;
            $ad_data['value_optimized_type'] = 0;
        } else {
            if (
                in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_LIVE, ToutiaoEnum::PROMOTION_TYPE_GAME]) &&
                $param->getSiteConfig()->convert_type == 4
            ) {
                $ad_data['external_actions'] = 'AD_CONVERT_TYPE_PAY';
            }
        }

        $ad_result = (new AdModel())->createAdMarketing(new AdCreateMarketParam($ad_data), $account_param->account_id, $account_param->access_token);
        return (string)($ad_result['ad_id'] ?? 0);
    }

    /**
     * 创建营销链路的广告创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array|mixed
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        if ($param->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            //程序化创意
            return $this->createProceduralCreative($param, $account_param, $material_media_id_map);
        } else {
            //自定义创意
            return $this->createCustomCreative($param, $account_param, $material_media_id_map);
        }
    }

    /**
     * 自定义创意-营销链路
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed
     */
    private function createCustomCreative(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        //自定义创意
        $creative_list = [];

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_APP, ToutiaoEnum::PROMOTION_TYPE_LINK])) {
            foreach ($param->creative_list as $creative) {
                $creative_list_param = new CreativeListsParam();
                //设置标题
                $creative_list_param->setTitleMaterial($creative['title']);
                //设置素材
                $creative_list_param->setImageOrVideoMaterials($creative, $material_media_id_map, $setting->isUnionSplash());
                //设置试玩素材
                $creative_list_param->setPlayableUrl($this->getPlayableUrl($param));
                //设置组件id
                $creative_list_param->setComponentMaterials($this->getComponentId($param, $account_param));

                if ($other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE') {
                    $creative_list_param->setComponentMaterials($this->getComponentId($param, $account_param, 'game_subscribe'));
                }

                if ($other_setting->is_union_component == 1) {
                    $creative_list_param->setComponentMaterials($other_setting->union_component_map[$param->account_id]);
                }

                $creative_list[] = ($creative_list_param->toFilterArray());
            }
            $ccmp = $param->toArray();
            $ccmp['ad_data'] = (new AdDataParam(array_merge(
                $param->toArray(),
                $param->setting->toArray(),
                $param->other_setting->toArray(),
                [
                    'web_url' => $other_setting->getPageWebUrlMapInfo($account_param->account_id),
                    'external_url' => $other_setting->getPageExternalUrlMapInfo($account_param->account_id)
                ]
            )))->toFilterArray();

            if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN) {
                $ccmp['ad_data']['ies_core_user_id'] = $other_setting->ies_map[$account_param->account_id];

                if ($param->platform == 'TW' && $param->creator != '陈林沛') {
                    $ccmp['ad_data']['is_feed_and_fav_see'] = '1';
                } else {
                    $ccmp['ad_data']['is_feed_and_fav_see'] = $other_setting->is_feed_and_fav_see;
                }
            }

            $ccmp['creative_list'] = $creative_list;
            $ccmp['ad_id'] = $param->ad2_id;
        } elseif (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_HOT])) {
            if ($other_setting->hot_material_map[$param->account_id] ?? false) {
                $ies_core_user_id = $other_setting->hot_material_map[$param->account_id][0]['aweme_id'];
                foreach ($other_setting->hot_material_map[$param->account_id] as $creative) {
                    $creative_list_param = new CreativeListsParam();
                    //设置标题
                    $creative_list_param->setTitleMaterial($creative['title']);
                    //设置素材
                    $creative_list_param->setAwemeMaterials(
                        $creative['item_id'],
                        $creative['video_cover_id'],
                        $creative['image_mode'] ?? CreativeModel::CREATIVE_IMAGE_MODE_VIDEO_VERTICAL
                    );
                    //设置试玩素材
                    $creative_list_param->setPlayableUrl($this->getPlayableUrl($param));
                    //设置组件id
                    $creative_list_param->setComponentMaterials($this->getComponentId($param, $account_param));

                    if ($other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE') {
                        $creative_list_param->setComponentMaterials($this->getComponentId($param, $account_param, 'game_subscribe'));
                    }

                    if ($other_setting->is_union_component == 1) {
                        $creative_list_param->setComponentMaterials($other_setting->union_component_map[$param->account_id]);
                    }

                    $creative_list[] = ($creative_list_param->toFilterArray());
                }
                $ccmp = $param->toArray();

                $ccmp['ad_data'] = (new AdDataParam(array_merge(
                    $param->toArray(),
                    $param->setting->toArray(),
                    $param->other_setting->toArray(),
                    [
                        'ies_core_user_id' => $ies_core_user_id,
                        'web_url' => $other_setting->getPageWebUrlMapInfo($account_param->account_id),
                        'external_url' => $other_setting->getPageExternalUrlMapInfo($account_param->account_id)
                    ]
                )))->toFilterArray();
                $ccmp['creative_list'] = $creative_list;
                $ccmp['ad_id'] = $param->ad2_id;
            } else {
                throw new AppException("账号:{$param->account_id}无选择原生加热素材");
            }
        } else {
            $ccmp = $param->toArray();

            if ($param->getSiteConfig()->game_type == '安卓') {
                $ccmp['web_url'] = $other_setting->getPageWebUrlMapInfo($account_param->account_id);
            } else {
                $ccmp['external_url'] = $other_setting->getPageExternalUrlMapInfo($account_param->account_id);
            }

            $ccmp['ad_data'] = [
                'ies_core_user_id' => $other_setting->ies_map[$account_param->account_id],
                'is_feed_and_fav_see' => $other_setting->is_feed_and_fav_see,
            ];

            if ($setting->third_industry_id) {
                $ccmp['ad_data']['third_industry_id'] = $setting->third_industry_id;
            }

            if ($setting->ad_keywords) {
                $ccmp['ad_data']['ad_keywords'] = array_values(array_unique(array_filter($setting->ad_keywords)));
            }

            if ($other_setting->is_live_creative == 0) {

                if ($other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
                    $ccmp['ad_data']['source'] = $other_setting->app_name;
                } else {
                    $ccmp['ad_data']['source'] = $other_setting->source;
                }

                foreach ($param->creative_list as $creative) {
                    $creative_list_param = new CreativeListsParam();
                    //设置标题
                    $creative_list_param->setTitleMaterial($creative['title']);
                    //设置素材
                    $creative_list_param->setImageOrVideoMaterials($creative, $material_media_id_map, $setting->isUnionSplash());
                    $creative_list[] = ($creative_list_param->toFilterArray());
                }
                $ccmp['creative_list'] = $creative_list;
            } else {
                if ($other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
                    $ccmp['ad_data']['app_name'] = $other_setting->app_name;
                } else {
                    $ccmp['ad_data']['app_name'] = $other_setting->source;
                }
                $ccmp['creative_list'] = [['image_mode' => 'CREATIVE_IMAGE_MODE_AWEME_LIVE']];
            }

            $ccmp['ad_id'] = $param->ad2_id;
        }

        // 原生锚点
        if ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT
            || (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_APP, ToutiaoEnum::PROMOTION_TYPE_LINK]) && $other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN)) {
            $ccmp['ad_data']['anchor_related_type'] = $other_setting->anchor_related_type;
            // 手动时必填
            if ($other_setting->anchor_related_type == ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                $ccmp['ad_data']['anchor_type'] = ToutiaoEnum::ANCHOR_TYPE_APP_GAME;
                $ccmp['ad_data']['anchor_id'] = $other_setting->anchor_related_map[$param->account_id];
            }
        }


        $creative_model = new CustomCreativeModel();
        $response_data = $creative_model->create((new CreativeCreateMarketParam($ccmp)), $account_param->account_id, $account_param->access_token);

        if (isset($response_data['creative_ids'])) {
            if (isset($response_data['errors']) && $response_data['errors']) {
                throw new AppException($response_data['errors'][0]['message']);
            } else {
                return $response_data['creative_ids'];
            }
        } else {
            throw new AppException('新建广告创意失败');
        }
    }

    /**
     * 程序化创意-营销链路
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    private function createProceduralCreative(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        //程序化创意
        $creative_list_param = new CreativeParam();
        //设置标题
        $creative_list_param->setTitleMaterial($param->word_list);
        //设置素材
        $creative_list_param->setImageOrVideoMaterials($param->creative_list, $material_media_id_map, $setting->isUnionSplash());
        //设置组件id
        $creative_list_param->setComponentMaterials($this->getComponentId($param, $account_param));

        if ($other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE') {
            $creative_list_param->setComponentMaterials($this->getComponentId($param, $account_param, 'game_subscribe'));
        }

        if ($other_setting->is_union_component == 1) {
            $creative_list_param->setComponentMaterials($other_setting->union_component_map[$param->account_id]);
        }

        $ccmp = $param->toArray();
        $ccmp['ad_data'] = (new AdDataParam(array_merge(
            $param->toArray(),
            $param->setting->toArray(),
            $param->other_setting->toArray(),
            [
                'web_url' => $other_setting->getPageWebUrlMapInfo($account_param->account_id),
                'external_url' => $other_setting->getPageExternalUrlMapInfo($account_param->account_id)
            ]
        )))->toFilterArray();

        if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN) {
            $ccmp['ad_data']['ies_core_user_id'] = $other_setting->ies_map[$account_param->account_id];
            if ($param->platform == 'TW' && $param->creator != '陈林沛') {
                $ccmp['ad_data']['is_feed_and_fav_see'] = '1';
            } else {
                $ccmp['ad_data']['is_feed_and_fav_see'] = $other_setting->is_feed_and_fav_see;
            }
        }

        if (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_LIVE, ToutiaoEnum::PROMOTION_TYPE_GAME]) && $other_setting->is_live_creative == 0) {
            $ccmp['ad_data']['is_feed_and_fav_see'] = $other_setting->is_feed_and_fav_see;
        }

        if ($other_setting->is_live_creative == 0) {
            if ($other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
                $ccmp['ad_data']['source'] = $other_setting->app_name;
            } else {
                $ccmp['ad_data']['source'] = $other_setting->source;
            }
        }

        // 原生锚点
        if (
            $other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT ||
            (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_APP, ToutiaoEnum::PROMOTION_TYPE_LINK]) && $other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN)
        ) {
            $ccmp['ad_data']['anchor_related_type'] = $other_setting->anchor_related_type;
            // 手动时必填
            if ($other_setting->anchor_related_type == ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                $ccmp['ad_data']['anchor_type'] = ToutiaoEnum::ANCHOR_TYPE_APP_GAME;
                $ccmp['ad_data']['anchor_id'] = $other_setting->anchor_related_map[$param->account_id];
            }
        }

        $ccmp['creative'] = (object)($creative_list_param->toFilterArray());
        $ccmp['ad_id'] = $param->ad2_id;

        $creative_model = new ProceduralCreativeModel();
        $creative_model->create((new ProceduralCreativeMarketParam($ccmp)), $account_param->account_id, $account_param->access_token);
        return [1];
    }

    /**
     * 检查推广卡片图片上传
     * @param ADTaskParam $param
     * @param $image_id
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function checkCardImageUpload(ADTaskParam $param, $image_id, MediaAccountInfoParam $account_param)
    {
        if (strpos($image_id, 'web.business.image') === false) {

            $name = basename($image_id);
            $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
            $data = (new MaterialFileModel())->getDataByName($name);
            if ($data) {
                $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
                    [$data->id],
                    $param->platform,
                    $param->media_type,
                    $account_param->account_id
                );

                $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
                if ($upload_info_list) {
                    $image_id = $upload_info_list[$data->id]->media_material_file_id;
                } else {
                    $data = (array)$data;

                    $insert = [];
                    $insert['platform'] = $param->platform;
                    $insert['media_type'] = $param->media_type;
                    $insert['account_id'] = $account_param->account_id;
                    $insert['material_id'] = $data['material_id'];
                    $insert['file_type'] = $data['file_type'];
                    $insert['file_id'] = $data['id'];
                    $insert['filename'] = $data['filename'];
                    $insert['uploader'] = $param->creator;
                    $insert['signature'] = $data['signature'];
                    $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                    $media_file_info = $this->uploadImage(
                        new MaterialFileParam($data),
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    if (!$media_file_info['id']) {
                        throw new AppException('上传推广卡片图片失败');
                    }
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                    $image_id = $media_file_id;
                }
            } else {
                throw new AppException('找不到推广卡片图片信息');
            }
        }
        return $image_id;
    }

    /**
     * 根据媒体获取素材类型
     * @param $file_type
     * @param $width
     * @param $height
     * @param bool $is_union_splash
     * @return string
     */
    public function getMaterialFileType($file_type, $width, $height, $is_union_splash = false)
    {
        if ((int)($file_type) === 1) {
            if ($is_union_splash) {
                return CreativeModel::CREATIVE_IMAGE_MODE_UNION_SPLASH;
            }
            // 大图横版列表
            if ($width >= 690 && $width <= 2560 && $height >= 150 && $height <= 900 && $width > $height) {
                return CreativeModel::CREATIVE_IMAGE_MODE_LARGE;
            }
            // 小图横版列表
            if ($width >= 228 && $width <= 1368 && $height >= 150 && $height <= 900 && $width > $height) {
                return CreativeModel::CREATIVE_IMAGE_MODE_SMALL;
            }
            // 大图竖版列表
            if ($width >= 720 && $height <= 1280 && $width < $height) {
                return CreativeModel::CREATIVE_IMAGE_MODE_LARGE_VERTICAL;
            }
            throw new AppException('错误的图片像素');
        } else {
            // 视频竖版列表
            if ($width < $height) {
                return CreativeModel::CREATIVE_IMAGE_MODE_VIDEO_VERTICAL;
            }
            // 视频横版列表
            if ($width > $height) {
                return CreativeModel::CREATIVE_IMAGE_MODE_VIDEO;
            }
            throw new AppException('错误的视频像素');
        }
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionCategoryList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $iasp = new InterestActionCategorySearchParam($input);
        $interest_action_model = new InterestActionModel();
        $response_data = $interest_action_model->categoryInfo($iasp);
        unset($response_data['request_id']);
        $response_data = $this->interestActionCategoryFormat($response_data);
        return array_values($response_data);
    }

    private function interestActionCategoryFormat($data)
    {
        foreach ($data as $key => &$value) {
            if (isset($value['children'])) {
                $value['children'] = $this->interestActionCategoryFormat($value['children']);
            }
            $value['value'] = ['id' => $value['id'] ?? '', 'name' => $value['name'] ?? ''];
        }
        return $data;
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionKeywordList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $iasp = new InterestActionKeywordSearchParam($input);
        $interest_action_model = new InterestActionModel();
        $response_data_search = $interest_action_model->keywordInfo($iasp);
        if (count($response_data_search['list']) > 0) {
            $iassp = $iasp->toArray();
            $iassp['id'] = $response_data_search['list'][0]['id'];
            $iassp['targeting_type'] = 'ACTION';
            $response_data_suggestion = $interest_action_model->keyWordInfoSuggestion(new InterestActionKeywordSuggestionSearchParam($iassp));
            $response_data = [];
//        $response_data['list'] = array_merge($response_data_search['list'],$response_data_suggestion['keywords']);
            $response_data['list'] = $response_data_suggestion['keywords'];
        } else {
            $response_data['list'] = [];
        }
        return $response_data;
    }

    /**
     * 根据输入获取兴趣类目
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType($input['media_type']);
        if (!$account_data) {
            throw new AppException('getInterestInterestCategoryList找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $iasp = new InterestInterestCategorySearchParam($input);
        $interest_model = new InterestInterestModel();
        $response_data = $interest_model->categoryInfo($iasp);
        unset($response_data['request_id']);
        $response_data = $this->interestActionCategoryFormat($response_data);
        return array_values($response_data);
    }

    /**
     * 根据输入获取兴趣关键词
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType($input['media_type']);
        if (!$account_data) {
            throw new AppException('getInterestInterestKeywordList找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $iasp = new InterestInterestKeywordSearchParam($input);
        $interest_model = new InterestInterestModel();
        $response_data_search = $interest_model->keywordInfo($iasp);
        if (count($response_data_search['list']) > 0) {
            $iassp = $iasp->toArray();
            $iassp['id'] = $response_data_search['list'][0]['id'];
            $iassp['targeting_type'] = 'INTEREST';
            $interest_action_model = new InterestActionModel();
            $response_data_suggestion = $interest_action_model->keyWordInfoSuggestion(new InterestActionKeywordSuggestionSearchParam($iassp));
            $response_data = [];
//        $response_data['list'] = array_merge($response_data_search['list'],$response_data_suggestion['keywords']);
            $response_data['list'] = $response_data_suggestion['keywords'];
        } else {
            $response_data['list'] = [];
        }
        return $response_data;
    }

    /**
     * 获取头条多关键词兴趣列表
     * @param array $input
     * @return array[]
     */
    public function getInterestInterestKeywordMultiList(array $input)
    {
        if (count($input['query_words_addr']) > 300)
            throw new AppException('关键词组不得超过300条');

        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('getInterestInterestKeywordMultiList找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $interest_model = new InterestInterestModel();
        $result = ['list' => []];
        // 循环发起请求
        foreach ($input['query_words_addr'] as $query_words) {
            $input['query_words'] = $query_words;
            $keyword_data = $interest_model->keywordInfo(new InterestInterestKeywordSearchParam($input));
            if (count($keyword_data['list']) > 0) {
                $result['list'][] = $keyword_data['list'][0];
            }
        }
        return $result;
    }

    /**
     * 获取头条多关键词行为列表
     * @param array $input
     * @return array[]
     */
    public function getInterestActionKeywordMultiList(array $input)
    {
        if (count($input['query_words_addr']) > 300)
            throw new AppException('关键词组不得超过300条');

        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('getInterestActionKeywordMultiList找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $interest_model = new InterestActionModel();
        $result = ['list' => []];
        // 循环发起请求
        foreach ($input['query_words_addr'] as $query_words) {
            $input['query_words'] = $query_words;
            $param = new InterestActionKeywordSearchParam($input);
            $keyword_data = $interest_model->keywordInfo($param);
            if (count($keyword_data['list']) > 0) {
                $result['list'][] = $keyword_data['list'][0];
            }
        }
        return $result;
    }


    /**
     * 上传图片到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $data_param = new FileImageUploadParam($param, $advertiser_id, $access_token);
            $result = (new FileImageModel())->upload($data_param);
            return [
                'id' => $result['id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传视频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $lsy_data = (new OdsLianShanYunMaterialFileLog())->getDataByFileId($param->id);
            if ($lsy_data) {
                $param->url = "https://$lsy_data->bucket_name.tos-cn-beijing.volces.com/$lsy_data->signature.mp4";
            }

            $data_param = new FileVideoUploadParam($param, $advertiser_id, $access_token);

            $result = (new FileVideoModel())->upload($data_param);
            return [
                'id' => $result['video_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传音频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadAudio(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $data_param = new FileAudioUploadParam($param, $advertiser_id, $access_token);
            $result = (new FileAudioModel())->upload($data_param);
            return [
                'id' => $result['audio_info']['audio_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 推送人群包
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        $media_account_model = new MediaAccountModel();
        $audience_account_id_map = $media_account_model->getDataInAccountIds($audience_account_id_list)->keyBy('account_id');
        if (!$audience_account_id_map) {
            throw new AppException('找不到' . implode(',', $audience_account_id_list) . '的账号信息');
        }
        $custom_audience_model = new CustomAudienceModel();
        foreach ($need_push_audience_list_data as $audience_key => $audience_data) {
            try {
                $custom_audience_model->push(
                    // {"code":40000,"message":"advertiser_id: Field must be set to integer or not be present","request_id":"202403291707061F73366B1BF768404888"}
                    (int)$audience_data->account_id,
                    $audience_account_id_map[$audience_data->account_id]->access_token,
                    (int)$audience_data->custom_audience_id,
                    $target_account_ids
                );
            } catch (Throwable $e) {
                throw new AppException(
                    "人群包{$audience_data->custom_audience_id}上传错误," .
                    "推送的管家ID:{$audience_account_id_map[$audience_data->account_id]->toutiao_majordomo_id}," .
                    $e->getMessage()
                );
            }
        }
        return [];
    }

    /**
     * 获取创意行业列表
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType($input['media_type']);
        if (!$account_data) {
            throw new AppException('getIndustryList找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $igp = new IndustryGetParam($input);
        $industry_model = new IndustryModel();
        return $industry_model->industryInfo($igp);
    }

    /**
     * id转词汇
     * @param array $input
     * @param $media_type
     * @return array
     */
    public function getWordById($input, $media_type)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType($media_type);
        if (!$account_data) {
            throw new AppException('getIndustryList找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        $itwp = new IdToWordParam($input);
        $interest_action_model = new InterestActionModel();
        return $interest_action_model->idToWord($itwp);
    }

    /**
     * 根据ids获取人群包内容
     * @param array $ids
     * @param int $status
     * @return Collection
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        $model = new OdsToutiaoCustomAudienceLogModel();
        return $model->getListByAudienceIds($ids, $status);
    }

    /**
     * 获取人群包列表
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = "", $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        return (new OdsToutiaoCustomAudienceLogModel())->getListByCompany($company, $page, $rows, $id, $name, $tag, $source, $account_id);
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        $tcal_model = new OdsToutiaoCustomAudienceLogModel();
        return $tcal_model->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    /**
     * @param $flow_md5_list
     * @param $account_id
     * @return Collection
     */
    public function getAlreadyExistFlowMd5List($flow_md5_list, $account_id)
    {
        return ((new OdsToutiaoUnionFlowPackageLogModel())->getAlreadyExistFlowList($flow_md5_list, $account_id))->keyBy('rit_md5');
    }

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        $targeting_name = '';

        // 性别
        switch ($audience_info['gender']) {
            case 'GENDER_MALE':
                $targeting_name .= '男';
                break;
            case 'GENDER_FEMALE':
                $targeting_name .= '女';
                break;
        }

        // 年龄
        $age_min = 99;
        $age_max = 0;
        foreach ($audience_info['age'] as $age) {
            switch ($age) {
                case 'AGE_BETWEEN_18_19':
                    $age_min > 18 && $age_min = 18;
                    $age_max < 19 && $age_max = 19;
                    break;
                case 'AGE_BETWEEN_20_23':
                    $age_min > 20 && $age_min = 20;
                    $age_max < 23 && $age_max = 23;
                    break;
                case 'AGE_BETWEEN_24_30':
                    $age_min > 24 && $age_min = 24;
                    $age_max < 30 && $age_max = 30;
                    break;
                case 'AGE_BETWEEN_31_35':
                    $age_min > 31 && $age_min = 31;
                    $age_max < 35 && $age_max = 35;
                    break;
                case 'AGE_BETWEEN_36_40':
                    $age_min > 36 && $age_min = 36;
                    $age_max < 40 && $age_max = 40;
                    break;
                case 'AGE_BETWEEN_41_45':
                    $age_min > 41 && $age_min = 41;
                    $age_max < 45 && $age_max = 45;
                    break;
                case 'AGE_BETWEEN_46_50':
                    $age_min > 46 && $age_min = 46;
                    $age_max < 50 && $age_max = 50;
                    break;
                case 'AGE_BETWEEN_51_55':
                    $age_min > 51 && $age_min = 51;
                    $age_max < 55 && $age_max = 55;
                    break;
                case 'AGE_BETWEEN_56_59':
                    $age_min > 56 && $age_min = 56;
                    $age_max < 59 && $age_max = 59;
                    break;
                case 'AGE_ABOVE_60':
                    $age_min > 60 && $age_min = 60;
                    $age_max < 99 && $age_max = 99;
                    break;
            }
        }
        if ($age_min != 99 && $age_max != 0) {
            mb_strlen($targeting_name) > 0 && $targeting_name .= '-';
            $targeting_name .= "{$age_min}~{$age_max}";
        }

        // 人群包
        $re_targeting_text = '';
        if (isset($audience_info['retargeting_tags_include']) && $audience_info['retargeting_tags_include'] && count($audience_info['retargeting_tags_include']) > 0) {
            $re_targeting_text .= "定向";
        }
        if (isset($audience_info['retargeting_tags_exclude']) && $audience_info['retargeting_tags_exclude'] && count($audience_info['retargeting_tags_exclude']) > 0) {
            mb_strlen($re_targeting_text) > 0 && $re_targeting_text .= '+';
            $re_targeting_text .= "排除";
        }
        $re_targeting_text && mb_strlen($re_targeting_text) > 0 && $targeting_name .= '-';
        $re_targeting_text && $targeting_name .= "{$re_targeting_text}";

        // 行为
        $lai_ka_name = '';
        $action_interest_text = '';
        if ($audience_info['interest_action_mode'] == 'RECOMMEND') {
            $action_interest_text .= "系统推荐";
        } elseif ($audience_info['interest_action_mode'] == 'CUSTOM') {
            if ((isset($audience_info['action_categories']) && $audience_info['action_categories'] && count($audience_info['action_categories']) > 0) ||
                (isset($audience_info['action_words']) && $audience_info['action_words'] && count($audience_info['action_words']) > 0)) {
                $action_interest_text .= "行为";
            }
            if ((isset($audience_info['interest_categories']) && $audience_info['interest_categories'] && count($audience_info['interest_categories']) > 0) ||
                (isset($audience_info['interest_words']) && $audience_info['interest_words'] && count($audience_info['interest_words']) > 0)) {
                mb_strlen($action_interest_text) > 0 && $action_interest_text .= '+';
                $action_interest_text .= "兴趣";
            }
        }
        $action_interest_text && $action_interest_text != '系统推荐' && $lai_ka_name .= '徕卡';
        $lai_ka_name .= "{$action_interest_text}";

        $lai_ka_name && mb_strlen($targeting_name) > 0 && $targeting_name .= '-';
        $lai_ka_name && $targeting_name .= "{$lai_ka_name}";

        // 智能放量
        mb_strlen($targeting_name) > 0 && $targeting_name .= '-';
        if ((int)$audience_info['auto_extend_enabled'] === 0) {
            $targeting_name .= '无智能放量';
        } else {
            $targeting_name .= '智能放量';
        }

        return $targeting_name;
    }

    private function array_key_by(array $array, string $key)
    {
        $return = [];
        foreach ($array as $k => $v) {
            $return[$v[$key]] = $v;
        }
        return $return;
    }

    private function make_interest_structure(array $id_array, array $data_json)
    {
        $return = [];
        foreach ($id_array as $key => $value) {
            isset($data_json[$value]) && $return[] = [
                'id' => $data_json[$value]['id'],
                'name' => $data_json[$value]['name'],
            ];
        }
        return $return;
    }

    private function make_audience_structure(array $id_array, $data_json)
    {
        $return = [];
        foreach ($id_array as $key => $value) {
            isset($data_json[$value]) && $return[] = json_encode([
                'custom_audience_id' => $data_json[$value]->custom_audience_id,
                'name' => $data_json[$value]->name
            ], JSON_UNESCAPED_UNICODE);
        }
        return $return;
    }

    private function make_city_structure(array $id_array)
    {
        $return = [];
        foreach ($id_array as $key => $value) {
            $return[] = [
                'id' => $value,
                'name' => CityMap::map[$value] ?? ''
            ];
        }
        return $return;
    }

    /**
     * 补充定向包信息
     * @param array $targeting_info
     * @param bool $is_return
     * @return array
     */
    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        $targeting = json_decode($targeting_info['targeting'], true);

        // 补充map数据
        if (isset($targeting['action_categories']) && $targeting['action_categories']) {
            $targeting['action_days'] = $targeting['action_days'] ?: 365;
            $targeting['action_scene'] = $targeting['action_scene'] ?: ['E-COMMERCE', 'NEWS', 'APP'];
            $action_categories_data = $this->getWordById([
                'ids' => $targeting['action_categories'],
                'tag_type' => 'CATEGORY',
                'targeting_type' => 'ACTION',
                'action_scene' => $targeting['action_scene'],
                'action_days' => $targeting['action_days'],
            ], MediaType::TOUTIAO);
            $action_categories_data = $this->array_key_by($action_categories_data['categories'], 'id');
            $targeting['action_categories_map'] = $this->make_interest_structure($targeting['action_categories'], $action_categories_data);
        }

        if (isset($targeting['action_words']) && $targeting['action_words']) {
            $targeting['action_days'] = $targeting['action_days'] ?: 365;
            $targeting['action_scene'] = $targeting['action_scene'] ?: ['E-COMMERCE', 'NEWS', 'APP'];
            $action_words_data = $this->getWordById([
                'ids' => $targeting['action_words'],
                'tag_type' => 'KEYWORD',
                'targeting_type' => 'ACTION',
                'action_scene' => $targeting['action_scene'],
                'action_days' => $targeting['action_days'],
            ], MediaType::TOUTIAO);
            $action_words_data = $this->array_key_by($action_words_data['keywords'], 'id');
            $targeting['action_words_map'] = $this->make_interest_structure($targeting['action_words'], $action_words_data);
        }

        if (isset($targeting['interest_categories']) && $targeting['interest_categories']) {
            $interest_categories_data = $this->getWordById([
                'ids' => $targeting['interest_categories'],
                'tag_type' => 'CATEGORY',
                'targeting_type' => 'INTEREST'
            ], MediaType::TOUTIAO);
            $interest_categories_data = $this->array_key_by($interest_categories_data['categories'], 'id');
            $targeting['interest_categories_map'] = $this->make_interest_structure($targeting['interest_categories'], $interest_categories_data);
        }

        if (isset($targeting['interest_words']) && $targeting['interest_words']) {
            $interest_words_data = $this->getWordById([
                'ids' => $targeting['interest_words'],
                'tag_type' => 'KEYWORD',
                'targeting_type' => 'INTEREST',
            ], MediaType::TOUTIAO);
            $interest_words_data = $this->array_key_by($interest_words_data['keywords'], 'id');
            $targeting['interest_words_map'] = $this->make_interest_structure($targeting['interest_words'], $interest_words_data);
        }

        $audience_id_list = array_merge($targeting['retargeting_tags_include'], $targeting['retargeting_tags_exclude']);
        $audience_list = $this->getAudienceListByIds($audience_id_list, 0);
        $audience_list = $audience_list->keyBy('custom_audience_id');

        if (isset($targeting['retargeting_tags_include']) && $targeting['retargeting_tags_include']) {
            $targeting['retargeting_tags_include_map'] = $this->make_audience_structure($targeting['retargeting_tags_include'], $audience_list);
        }

        if (isset($targeting['retargeting_tags_exclude']) && $targeting['retargeting_tags_exclude']) {
            $targeting['retargeting_tags_exclude_map'] = $this->make_audience_structure($targeting['retargeting_tags_exclude'], $audience_list);
        }

        if ($targeting['city']) {
            $targeting['city_map'] = $this->make_city_structure($targeting['city']);
        }

        // 补充none数据
        if (!$targeting['carrier']) {
            $targeting['carrier_none'] = 'buxian';
        } else {
            $targeting['carrier_none'] = 'zidingyi';
        }
        if (!$targeting['age']) {
            $targeting['age_none'] = 'buxian';
        } else {
            $targeting['age_none'] = 'zidingyi';
        }
        if (!$targeting['ac']) {
            $targeting['ac_none'] = 'buxian';
        } else {
            $targeting['ac_none'] = 'zidingyi';
        }
        if (!$targeting['activate_type']) {
            $targeting['activate_type_none'] = 'buxian';
        } else {
            $targeting['activate_type_none'] = 'zidingyi';
        }
        if (!$targeting['device_brand']) {
            $targeting['device_brand_none'] = 'buxian';
        } else {
            $targeting['device_brand_none'] = 'zidingyi';
        }
        if (!isset($targeting['launch_price']) || !$targeting['launch_price']) {
            $targeting['launch_price'] = [];
            $targeting['launch_price_none'] = 'buxian';
        } else {
            $targeting['launch_price_none'] = 'zidingyi';
        }

        if (!$targeting['retargeting_tags_include'] && !$targeting['retargeting_tags_exclude']) {
            $targeting['zidingyirenqun'] = 'buxian';
        } else {
            $targeting['zidingyirenqun'] = 'zidingyi';
        }

        $targeting_info['targeting'] = $targeting;

        return $targeting_info;
    }

    /**
     * 获取计划行为徕卡
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        $tt_ad_log_model = new OdsToutiaoAdLogModel();
        $list = $tt_ad_log_model->getAdList($condition['ad_name'] ?? '');
        $return_list = [];
        foreach ($list as $key => $value) {
            $audience = json_decode($value->audience, true);
            if ($audience['action']) {
                $return_list[] = [
                    'ad_id' => $value->ad_id,
                    'ad_name' => $value->ad_name,
                    'action_scene' => $audience['action']['action_scene'],
                    'action_days' => $audience['action']['action_days'],
                    'action_categories' => $audience['action']['action_categories'],
                    'action_words' => $audience['action']['action_words']
                ];
            }
        }
        return $return_list;
    }

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        $result = [];
        if (isset($data['action_categories']) && $data['action_categories']) {
            $data['action_days'] = $data['action_days'] ?? 365;
            $data['action_scene'] = $data['action_scene'] ?? ['E-COMMERCE', 'NEWS', 'APP'];
            $action_categories_data = $this->getWordById([
                'ids' => $data['action_categories'],
                'tag_type' => 'CATEGORY',
                'targeting_type' => 'ACTION',
                'action_scene' => $data['action_scene'],
                'action_days' => $data['action_days'],
            ], MediaType::TOUTIAO);
            $action_categories_data = $this->array_key_by($action_categories_data['categories'], 'id');
            $result['action_categories_map'] = $this->make_interest_structure($data['action_categories'], $action_categories_data);
        } else {
            $result['action_categories_map'] = [];
        }

        if (isset($data['action_words']) && $data['action_words']) {
            if (!isset($data['action_scene']) || !isset($data['action_days'])) {
                throw new AppException('必传行为场景与行为天数');
            }
            $action_words_data = $this->getWordById([
                'ids' => $data['action_words'],
                'tag_type' => 'KEYWORD',
                'targeting_type' => 'ACTION',
                'action_scene' => $data['action_scene'],
                'action_days' => $data['action_days'],
            ], MediaType::TOUTIAO);
            $action_words_data = $this->array_key_by($action_words_data['keywords'], 'id');
            $result['action_words_map'] = $this->make_interest_structure($data['action_words'], $action_words_data);
        } else {
            $result['action_words_map'] = [];
        }
        return $result;
    }


    /**
     * 获取计划兴趣徕卡
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        $tt_ad_log_model = new OdsToutiaoAdLogModel();
        $list = $tt_ad_log_model->getAdList($condition['ad_name'] ?? '');
        $return_list = [];
        foreach ($list as $key => $value) {
            $audience = json_decode($value->audience, true);
            if ($audience['interest_categories'] || $audience['interest_words']) {
                $return_list[] = [
                    'ad_id' => $value->ad_id,
                    'ad_name' => $value->ad_name,
                    'interest_categories' => $audience['interest_categories'],
                    'interest_words' => $audience['interest_words']
                ];
            }
        }
        return $return_list;
    }

    /**
     * 获取计划徕卡兴趣id转词汇
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        $result = [];
        if (isset($data['interest_categories']) && $data['interest_categories']) {
            $interest_categories_data = $this->getWordById([
                'ids' => $data['interest_categories'],
                'tag_type' => 'CATEGORY',
                'targeting_type' => 'INTEREST'
            ], MediaType::TOUTIAO);
            $interest_categories_data = $this->array_key_by($interest_categories_data['categories'], 'id');
            $result['interest_categories_map'] = $this->make_interest_structure($data['interest_categories'], $interest_categories_data);
        } else {
            $result['interest_categories_map'] = [];
        }

        if (isset($data['interest_words']) && $data['interest_words']) {
            $interest_words_data = $this->getWordById([
                'ids' => $data['interest_words'],
                'tag_type' => 'KEYWORD',
                'targeting_type' => 'INTEREST',
            ], MediaType::TOUTIAO);
            $interest_words_data = $this->array_key_by($interest_words_data['keywords'], 'id');
            $result['interest_words_map'] = $this->make_interest_structure($data['interest_words'], $interest_words_data);
        } else {
            $result['interest_words_map'] = [];
        }
        return $result;
    }

    /**
     * 获取流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        $tcal_model = new OdsToutiaoUnionFlowPackageLogModel();
        return $tcal_model->getList($name, $account_id, $page, $row);
    }

    /**
     * @param $advertiser_id
     * @param $access_token
     * @param $package_params
     * @return array|mixed
     */
    public function uploadFlow($advertiser_id, $access_token, $package_params)
    {
        if (!$package_params['rit']) {
            return [];
        }
        if (count($package_params['rit']) > 500) {
            throw new AppException("{$package_params['name']}流量包穿山甲广告位数量大于500，无法创建");
        }
        $flow_package_model = new FlowPackageModel();
        $name = $package_params['name'];
        $flow_rit_ids = $package_params['rit'];
        return $flow_package_model->create($advertiser_id, $access_token, $name, $flow_rit_ids);
    }

    /**
     * 更新流量包上传成功的重置参数
     * @param int $flow_package_id
     * @param array $flow_rit_id_list
     * @return mixed
     */
    public function getSuccessFlowMap($flow_package_id, $flow_rit_id_list)
    {
        sort($flow_rit_id_list['rit']);
        $rit_md5 = $flow_rit_id_list['rit'] ? md5(json_encode($flow_rit_id_list['rit'])) : md5($flow_rit_id_list['name']);
        $reset_flow_map[$rit_md5] = [
            'flow_package_id' => $flow_package_id,
            'md5' => $rit_md5,
            'name' => $flow_rit_id_list['name'],
        ];
        return $reset_flow_map;
    }

    /**
     * 请求结果提取流量包ID
     * @param $request
     * @return int
     */
    public function getFlowPackageIdByRequest($request)
    {
        return $request['flow_package_id'] ?? 0;
    }

    /**
     * 同步流量包
     * @param $account_id
     * @return array
     */
    public function syncFlowPackage($account_id)
    {
        return (new ToutiaoTaskModel)->unionFlowPackage($account_id);
    }

    /**
     * 修改广告计划-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        $request_data = json_decode($param->request, true);
        try {
            $response_data = (new AdModel())->update($request_data, $access_token);
            return [
                'ad_id' => $response_data['data']['ad_id'] ?? 0,
                'result' => [
                    'code' => 0,
                    'data' => $response_data
                ]
            ];
        } catch (Throwable $e) {
            return [
                'ad_id' => 0,
                'result' => [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage()
                ],
            ];
        }
    }

    /**
     * 修改广告计划的开关-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        $request_data = json_decode($param->request, true);
        try {
            $response_data = (new AdModel())->updateOptStatus($request_data, $access_token);
            return [
                'ad_id' => $response_data['data']['ad_ids'][0] ?? 0,
                'result' => [
                    'code' => 0,
                    'data' => $response_data
                ]
            ];
        } catch (Throwable $e) {
            return [
                'ad_id' => 0,
                'result' => [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage()
                ],
            ];
        }
    }

    /**
     * 修改广告创意的开关-返回修改的广告创意id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        $request_data = json_decode($param->request, true);
        try {
            $response_data = (new CreativeModel())->updateOptStatus($request_data, $access_token);
            return [
                'creative_id' => $response_data['data']['creative_ids'][0],
                'result' => [
                    'code' => 0,
                    'data' => $response_data
                ]
            ];
        } catch (Throwable $e) {
            return [
                'creative_id' => 0,
                'result' => [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage()
                ],
            ];
        }
    }

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        $request_data = json_decode($param->request, true);
        $response_data = (new CustomAudienceModel())->expand(
            $account_id,
            (int)$request_data['custom_audience_id'],
            $access_token,
            $request_data['tag'],
            $request_data['name'],
            (int)$request_data['lookalike_num'],
            $request_data['platform'],
            (int)$request_data['is_contain_seed'],
            $request_data['app_platform']
        );
        return [
            'custom_audience_id' => $response_data['data']['custom_audience_id'],
            'result' => $response_data,
        ];
    }

    /**
     * 头条开关操作前检查打包状态
     * @param $column
     * @param OdsToutiaoAdLogModel $toutiao_model
     * @param $toutiao_format_target
     * @param $editor_id
     * @param $editor_name
     * @return array|array[]
     */
    public function checkPackageStatus($column, OdsToutiaoAdLogModel $toutiao_model, $toutiao_format_target, $editor_id, $editor_name)
    {
        $column = array_merge($column, ['ad_log.app_type', 'ad_log.download_url', 'game.app_id']);
        $result = $toutiao_model->getAccountId($toutiao_format_target, $column, true);
        $original_result = $result->toArray();
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }
        //检查是否打包完成
        $package_unfinished_info = [];
        $android_site_info = [];
        $ios_site_info = [];
        $app_store_url = 'https://apps.apple.com/cn/app/id';
        foreach ($result as $key => $item) {
            if ('APP_IOS' === $item->app_type) {
                if ($item->download_url) {
                    if (!isset($ios_site_info[$item->download_url]) ||
                        !in_array(['platform' => $item->platform, 'site_id' => $item->site_id],
                            $ios_site_info[$item->download_url])) {

                        $ios_site_info[$item->download_url][] =
                            ['platform' => $item->platform, 'site_id' => $item->site_id];
                    }
                } else {
                    if (!$item->app_id) {
                        $package_unfinished_info[] = [
                            'ad_id' => $item->ad_id,
                            'platform' => $item->platform,
                            'ad_name' => $item->ad_name,
                            'account_id' => $item->account_id
                        ];
                        unset($result[$key]);
                    }
                    $ios_site_info[$app_store_url . $item->app_id][] =
                        ['platform' => $item->platform, 'site_id' => $item->site_id];
                }

            } else {
                if (!isset($android_site_info[$item->platform]) ||
                    !in_array($item->site_id, $android_site_info[$item->platform])) {

                    $android_site_info[$item->platform][] = $item->site_id;
                }
            }
        }
        unset($item);

        //iOS包curl请求判断状态码
        if ($ios_site_info) {
            $ios_package_unfinished = [];
            foreach ($ios_site_info as $download_url => $value) {
                $http_code = Helpers::getHttpStatus($download_url, ['proxy' => true, 'timeout' => 10]);
                if ((int)$http_code < 200 || (int)$http_code >= 400) {
                    $ios_package_unfinished = array_merge($ios_package_unfinished, $value);
                }
            }
            unset($value);

            if ($ios_package_unfinished) {
                foreach ($result as $key => $value) {
                    foreach ($ios_package_unfinished as $v) {
                        if ($value->platform === $v['platform'] && (int)$value->site_id === (int)$v['site_id']) {
                            $package_unfinished_info[] = [
                                'ad_id' => $value->ad_id,
                                'platform' => $value->platform,
                                'ad_name' => $value->ad_name,
                                'account_id' => $value->account_id
                            ];
                            unset($result[$key]);
                        }
                    }
                    unset($v);
                }
                unset($value);
            }
        }

        //Android包请求子平台获取打包状态并删除result中打包未完成的ad_id
//        if ($android_site_info) {
//            foreach ($android_site_info as $platform => $site_ids) {
//                $ads_platform = PlatformAD::create($platform);
//                $site_state = $ads_platform->getAPKStateList($site_ids, $editor_name);
//                if (!$site_state) {
//                    throw new AppException('根据site_id获取子平台打包状态失败');
//                }
//                foreach ($site_state as $v) {
//                    foreach ($result as $key => $value) {
//                        if (1 !== (int)$v['state'] &&
//                            $value->platform === $platform &&
//                            (int)$value->site_id === (int)$v['site_id']) {
//                            $package_unfinished_info[] = [
//                                'ad_id' => $value->ad_id,
//                                'platform' => $value->platform,
//                                'ad_name' => $value->ad_name,
//                                'account_id' => $value->account_id
//                            ];
//                            unset($result[$key]);
//                        }
//                    }
//                }
//            }
//            unset($site_ids);
//        }

        $return_data = [];
        $record_operate = [];
        $single_record_operate = [];
        $single_return_data = [];
        if ($package_unfinished_info) {
            $single_record_operate['ad_level'] = 2;
            $single_record_operate['media_type'] = MediaType::TOUTIAO;
            $single_record_operate['editor_id'] = $editor_id;
            $single_record_operate['editor_name'] = $editor_name;
            $single_record_operate['edit_type'] = 1;
            //处理没有打包完成的
            foreach ($package_unfinished_info as $one_ad_info) {
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_record_operate['platform'] = $one_ad_info['platform'];
                $single_record_operate['account_id'] = (int)$one_ad_info['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$one_ad_info['ad_id'];
                $single_record_operate['ad_name'] = $one_ad_info['ad_name'];
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '未打包完成，禁止开关';
                $single_return_data['value'] = 0;
                $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($one_ad_info);
        }
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [
            'return_data' => $return_data,
            'record_operate' => $record_operate,
            'result' => $result,
            'original_result' => $original_result
        ];

    }

    public function checkResultAction($ad_data, $editor_id, $editor_name)
    {
        $single_record_operate = [];
        $single_return_data = [];
        $record_operate = [];
        $return_data = [];

        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $editor_id;
        $single_record_operate['editor_name'] = $editor_name;
        $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_OFF;
        foreach ($ad_data as $key => $ad_info) {
            if (1 === (int)$ad_info->result_action) {
                unset($ad_data[$key]);
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_record_operate['platform'] = $ad_info->platform;
                $single_record_operate['account_id'] = $ad_info->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $ad_info->ad_id;
                $single_record_operate['ad_name'] = $ad_info->ad_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前广告由智投接管, 不可关闭';
                $single_return_data['value'] = 0;
                $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
        }

        (new ADAnalysisOperateLogModel())->add($record_operate);
        return [
            'return_data' => $return_data,
            'record_operate' => $record_operate,
            'result' => $ad_data,
        ];
    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return array|mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $toutiao_format_target)
    {
        if (3 === $data['ad_level']) {
            //头条三级
            $model = new CreativeModel();
            $toutiao_ad_model = new OdsToutiaoCreativeLogModel();
            $toutiao_his_log_model = new OdsToutiaoCreativeHisLogModel();
            $ad_id_type = 'creative_id';
            $ad_name_type = 'title';
            $request_toutiao_ids_name = 'creative_ids';
            $history_update_field = 'opt_status';
            $close = 'CREATIVE_STATUS_DISABLE';
            $open = 'CREATIVE_STATUS_ENABLE';
            $delete = 'CREATIVE_STATUS_DELETE';
            $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        } elseif (2 === $data['ad_level']) {
            //头条二级
            $model = new AdModel();
            $ad_id_type = 'ad_id';
            $ad_name_type = 'ad_name';
            $toutiao_ad_model = new OdsToutiaoAdLogModel();
            $toutiao_his_log_model = new OdsToutiaoADHisLogModel();
            $request_toutiao_ids_name = 'ad_ids';
            $history_update_field = 'opt_status';
            $close = 'AD_STATUS_DISABLE';
            $open = 'AD_STATUS_ENABLE';
            $delete = 'AD_STATUS_DELETE';
            $column = ['ad_log.' . $ad_id_type, 'ad_log.' . $ad_name_type, 'ad_log.account_id', 'ad_log.platform', 'ad_log.site_id'];
        } else {
            //头条一级
            $model = new CampaignModel();
            $ad_id_type = 'campaign_id';
            $ad_name_type = 'campaign_name';
            $toutiao_ad_model = new OdsToutiaoCampaignLogModel();
            $toutiao_his_log_model = new OdsToutiaoCampaignHisLogModel();
            $request_toutiao_ids_name = 'campaign_ids';
            $history_update_field = 'status';
            $close = 'CAMPAIGN_STATUS_DISABLE';
            $open = 'CAMPAIGN_STATUS_ENABLE';
            $delete = 'CAMPAIGN_STATUS_DELETE';
            $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        }

        //向媒体发起请求的数据
        $request_data = [];
        //存储到操作日志的单条数据
        $single_record_operate = [];
        if (0 === (int)$data['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_OFF;
            $request_data['opt_status'] = 'disable';
            $switch_option = $close;
            $edit_detail = '关闭';
        } elseif (1 === (int)$data['switch']) {
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_ON;
            $request_data['opt_status'] = 'enable';
            $switch_option = $open;
            $edit_detail = '开启';
        } else {
            $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;
            $request_data['opt_status'] = 'delete';
            $switch_option = $delete;
            $edit_detail = '删除';

        }

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        //操作计划开启开关的时候校验打包状态
        if (in_array('ad_log.site_id', $column) && 1 === $data['switch']) {
            $check = $this->checkPackageStatus($column, $toutiao_ad_model, $toutiao_format_target, $data['editor_id'], $data['editor_name']);
            $result = $check['result'];

            /* @var Collection $result */
            if ($result->isEmpty()) {
                return $check['return_data'];
            }
            $return_data = $check['return_data'];
            $record_operate = $check['record_operate'];
            $original_result = $check['original_result'];
        } else {
            $result = $toutiao_ad_model->getAccountId($toutiao_format_target, $column);
            //检查是否被智投接管
//            if ((!isset($data['is_machine']) || 1 !== (int)$data['is_machine']) &&
//                2 === $data['ad_level'] && 0 === (int)$data['switch']) {
//                $check_result = $this->checkResultAction($result, $data['editor_id'], $data['editor_name']);
//                $return_data = $check_result['return_data'];
//                $record_operate = $check_result['record_operate'];
//                $result = $check_result['result'];
//            }
            $original_result = $result->toArray();
            if ($result->isEmpty()) {
                return $return_data;
//                throw new AppException('修改失败, 所选所有广告都已被智投接管');
            }
        }
        //将所有account_id放在一起，去重
        $account_ids = [];
        $all_ad_ids = [];
        foreach ($result as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
            $all_ad_ids[] = $value->$ad_id_type;
        }
        unset($value);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //如果是机器学习来操作且是开启广告， 则标记result_action=1
//        if (isset($data['is_machine']) && 1 === (int)$data['is_machine'] && 1 === (int)$data['switch']) {
//            $single_record_operate['result_action'] = 1;
//        }
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //要直接update到数据库的数据
//        $update_switch = [];
        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //有权限操作的广告id集合
        $permission_ad_ids = [];
        foreach ($access_token as $item) {
            $ad_ids = [];
            //access_token和account_id找到对应关系
            foreach ($result as $v) {
                if (intval($v->account_id) === intval($item->account_id)) {
                    $ad_ids[] = intval($v->$ad_id_type);
                    $permission_ad_ids[] = intval($v->$ad_id_type);
                }
            }
            unset($v);
            if (!$ad_ids) {
                continue;
            }
            $request_data['advertiser_id'] = intval($item->account_id);
            //解决同一账号中ad_id可能超过100个的问题，头条单次请求上限为100个ad_id集合
            $ids_num = count($ad_ids);
            $loop_times = (int)ceil($ids_num / 100);
            //循环请求，每个account_id最多带100个二级广告ID
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-99、100-199、200-299....
                $part_of_ad_ids = array_slice($ad_ids, ($i - 1) * 100, 100);
                $request_data[$request_toutiao_ids_name] = $part_of_ad_ids;
                //请求头条修改开关状态
                try {
                    $response = $model->updateOptStatus($request_data, $item->access_token);
                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response['errors']) && $response['errors']) {
                        foreach ($response['errors'] as $one_error) {
                            $failed_ad_ids[$one_error['error_message']][] = $one_error[$ad_id_type];
                            $error_ad_ids[] = $one_error[$ad_id_type];
                        }
                    }
                    $part_of_success = array_diff($part_of_ad_ids, $error_ad_ids);
                    //操作成功
                    //因为account_id相同的ad_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
                    $success_ad_ids = array_merge($success_ad_ids, $part_of_success);
//                    $update_switch[$request_data['advertiser_id']] = $part_of_success;
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_ad_ids[$failed_info])) {
                        $failed_ad_ids[$failed_info] = array_merge($part_of_ad_ids, $failed_ad_ids[$failed_info]);
                    } else {
                        $failed_ad_ids[$failed_info] = $part_of_ad_ids;
                    }
                }
            }
        }
        unset($item);

        //插入his表
        if ($success_ad_ids) {
            $toutiao_his_log_model->getLatestByADInfoAndInsert(
                $success_ad_ids,
                $history_update_field,
                $switch_option,
                $single_record_operate['insert_time']
            );
        }
        //无操作权限的账号
        $not_permission = array_diff($all_ad_ids, $permission_ad_ids);

        //将发起请求之后失败或成功的ad_id的其它信息对应出来
        foreach ($original_result as $v) {
            if (isset($data['risk_operate'][MediaType::TOUTIAO][$v->$ad_id_type]) &&
                $data['risk_operate'][MediaType::TOUTIAO][$v->$ad_id_type]) {
                $risk_operate = '本次为风险操作';
                $risk_code = 1;
            } else {
                $risk_operate = '';
                $risk_code = 0;
            }

            //处理成功的
            if ($success_ad_ids && in_array($v->$ad_id_type, $success_ad_ids)) {
//                foreach ($success_ad_ids as $ad_id) {
//                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
//                        continue;
//                    }
                $single_return_data['status'] = $single_record_operate['status'] = $risk_code ? ADAnalysisModel::SUCCESS_WITH_RISK : ADAnalysisModel::SUCCESS;
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->$ad_id_type;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;

                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_record_operate['edit_detail'] = $edit_detail . '成功 ' . $risk_operate;

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
//                }
//                unset($ad_id);
            }

            //处理失败的
            if ($failed_ad_ids) {
                foreach ($failed_ad_ids as $key => $ad_ids) {
                    if (in_array($v->$ad_id_type, $ad_ids)) {
//                    foreach ($ad_ids as $one_ad_id) {
//                        if ((int)$one_ad_id !== (int)$v->$ad_id_type) {
//                            continue;
//                        }
                        $single_return_data['status'] = $single_record_operate['status'] = $risk_code ? ADAnalysisModel::FAILED_WITH_RISK : ADAnalysisModel::FAILED;
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->$ad_id_type;
                        $single_record_operate['ad_name'] = $v->$ad_name_type;
                        $err_info = json_decode($key, true);
                        $single_return_data['message'] = $err_info['message'] ?? $key;
                        $single_return_data['value'] = 0;

                        $single_record_operate['edit_detail'] = $edit_detail . '失败，' . $risk_operate . ' 错误信息：' . $key;
                        $record_operate[] = $single_record_operate;
                        $return_data[] = $single_return_data;
//                    }
//                    unset($one_ad_id);
                    }
                }
                unset($ad_ids);
            }

            //处理没有权限的
            if ($not_permission && in_array($v->$ad_id_type, $not_permission)) {
//                foreach ($not_permission as $ad_id) {
//                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
//                        continue;
//                    }
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->$ad_id_type;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
//                }
//                unset($ad_id);
            }
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改头条广告组数据
     * @param array $input
     * @return array
     */
    public function updateADAnalysisFirstClass(array $input)
    {
        $toutiao_model = new OdsToutiaoCampaignLogModel();
        $account_info = $toutiao_model->getAccountIdAndPlatform($input);
        if (!$account_info) {
            throw new AppException('修改失败');
        }
        $toutiao_http_model = new CampaignModel();
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$account_info->account_id], null, $leader_permission);

        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 1;
        $record_operate['media_type'] = MediaType::TOUTIAO;
        $record_operate['editor_id'] = $input['editor_id'];
        $record_operate['editor_name'] = $input['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $record_operate['edit_type'] = 5;
        $record_operate['platform'] = $account_info->platform;
        $record_operate['account_id'] = (int)$account_info->account_id;
        $record_operate['ad_id'] = $return_data['ad_id'] = (int)$input['ad_id'];
        $record_operate['ad_name'] = $return_data['value'] = $account_info->campaign_name;
        $record_operate['status'] = $return_data['status'] = 2;

        if ($access_token->isNotEmpty()) {
            $access_token = $access_token->pop()->access_token;
            $request_data['advertiser_id'] = intval($account_info->account_id);
            $request_data['campaign_id'] = intval($input['ad_id']);
            $request_data['campaign_name'] = $input['ad_group_name'];

            //获取一级广告最新modify_time
            $filtering = ['ids' => [(int)$request_data['campaign_id']]];
            try {
                $ad1_info = (new CampaignModel())->info($request_data['advertiser_id'], $access_token, [], $filtering);
                //判断modify_time拉取情况
                if (!isset($ad1_info['list'][0]['modify_time'])) {
                    throw new AppException('修改广告组名称时未拉取到一级广告modify_time');
                } else {
                    $update_info = [];
                    $request_data['modify_time'] = $ad1_info['list'][0]['modify_time'];
                    $toutiao_http_model->update($request_data, $access_token);
                    //修改成功
                    $return_data['message'] = 'success';
                    $record_operate['ad_name'] = $return_data['value'] = $input['ad_group_name'];
                    $record_operate['status'] = $return_data['status'] = 1;
                    $record_operate['edit_detail'] = "广告组名称由[ {$account_info->campaign_name} ] " .
                        "修改为 [ {$input['ad_group_name']} ]";
//                    $update_info[$request_data['advertiser_id']] = [$request_data['campaign_id']];
                    $update_info[] = $request_data['campaign_id'];

                    if ($update_info) {
                        (new OdsToutiaoCampaignHisLogModel())->getLatestByADInfoAndInsert(
                            $update_info,
                            'campaign_name',
                            $input['ad_group_name'],
                            $record_operate['insert_time']
                        );
                    }
                }
            } catch (AppException $e) {
                $return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $record_operate['edit_detail'] = "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }
        } else {
            //没有权限操作
            $return_data['message'] = $record_operate['edit_detail'] = '当前用户无操作权限';
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [$return_data];
    }

    /**
     * 修改头条广告计划数据
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @return array
     */
    public function updateADAnalysisSecondClass(int $media_type, array $input, array $toutiao_format_target)
    {
        $toutiao_model = new OdsToutiaoAdLogModel();
        $column = ['ad_id', 'account_id', 'platform', 'ad_name'];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new AdModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        if (isset($input['ad_name'])) {
            $update_value = $request_data['name'] = $input['ad_name'];
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = 6;
            $update_field = 'ad_name';
            $opt_detail = '广告二级名';
        } else {
            //修改schedule_time
            $single_record_operate['edit_type'] = 4;
            $update_value = $request_data['schedule_time'] = $input['schedule_time'];
            $update_field = 'schedule_time';
            $opt_detail = '广告投放时段';
        }

        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->ad_id;
                $single_record_operate['ad_name'] = $v->ad_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['ad_id'] = (int)$v->ad_id;
            //获取二级广告最新modify_time
            $get_ad2_request['advertiser_id'] = $request_data['advertiser_id'] = (int)$v->account_id;
            $get_ad2_request['filtering'] = ['ids' => [(int)$v->ad_id]];
            try {
                $ad2_info = $toutiao_http_model->getAD2Info($get_ad2_request, $access_tokens[$v->account_id]);
                //检查是否拉取到modify_time
                if (!isset($ad2_info['list'][0]['modify_time'])) {
                    throw new AppException('修改二级广告时未拉取到二级广告modify_time');
                } else {
                    $request_data['modify_time'] = $ad2_info['list'][0]['modify_time'];
                    //向头条发起请求
                    $toutiao_http_model->update($request_data, $access_tokens[$v->account_id]);
                    //修改成功
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = $update_value;
                    $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->ad_name;
//                    $update_data[$v->account_id][] = (int)$v->ad_id;
                    $update_data[] = (int)$v->ad_id;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_detail . "修改成功";
                }
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->ad_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->ad_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_value,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoAdLogModel();
        $column = ['ad_id', 'ad_name', 'account_id', 'platform', $update_field];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        $all_ad_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }

            $all_ad_ids[] = $item->ad_id;
        }
        unset($item);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $request_type = 'budget';
        $opt_cn_name = '预算';
        if ('cpa_bid' === $update_field) {
            $request_type = 'bid';
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BID;
            $opt_cn_name = '出价';
        }
        $toutiao_http_model = new AdModel();
        //有操作权限的ad_id集合
        $permission_ad_ids = [];
        //修改成功的数据集合
        $success_data = [];
        //定时执行的数据集合
        $timed_execution_data = [];
        //修改失败的数据集合
        $failed_data = [];
        //存储所有需要定时修改的数据
        $all_data = [];
        //即时修改到数据库的数据
        $all_update = [];

        foreach ($access_token as $value) {
            $request_data = [];
            $data = [];
            $ad_ids = [];
            $all_change_info = [];
            foreach ($account_info as $v) {
                if (intval($value->account_id) === intval($v->account_id)) {
                    //记录有权限的ad_id
                    $permission_ad_ids[] = $v->ad_id;
                    //保存原始值
                    $original_value = $v->$update_field;
                    if (1 === intval($input['change_type'])) {
                        $v->$update_field = $input['change_value'];
                    } elseif (2 === intval($input['change_type'])) {
                        $v->$update_field += number_format($v->$update_field * ($input['change_value'] / 100),
                            2, '.', '');
                    } elseif (3 === intval($input['change_type'])) {
                        $v->$update_field -= number_format($v->$update_field * ($input['change_value'] / 100),
                            2, '.', '');
                    } else {
                        throw new AppException('参数错误');
                    }
                    //组合请求的数据
                    $ad_ids[] = $single_data['ad_id'] = (int)$v->ad_id;
                    $single_data[$request_type] = (string)$v->$update_field;
                    $data[] = $single_data;
                    //记录每一个ad_id的原始值和修改后的值，用于之后组装操作日志数据
                    $single_change_info['ad_id'] = (int)$v->ad_id;
                    $single_change_info['account_id'] = $v->account_id;
                    $single_change_info['value'] = $v->$update_field;
                    $single_change_info['original_value'] = $original_value;
                    $all_change_info[(int)$v->ad_id] = $single_change_info;
                }
            }
            unset($v);
            if (!$data) {
                continue;
            }
            $request_data['advertiser_id'] = intval($value->account_id);
            $request_data['data'] = $data;
            //即时修改，直接进行修改
            if (1 === intval($input['execute_type'])) {
                try {
                    $response_data = $toutiao_http_model->updateBudgetOrBid($request_data, $value->access_token, $request_type);
                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response_data['errors']) && $response_data['errors']) {
                        foreach ($response_data['errors'] as $item) {
                            $failed_data[$item['error_message']][] = $all_change_info[$item['ad_id']];
                            $error_ad_ids[] = $item['ad_id'];
                            unset($all_change_info[$item['ad_id']]);
                        }
                    }
                    //修改成功
                    $success_ad_ids = array_diff($ad_ids, $error_ad_ids);
                    if ($success_ad_ids) {
//                        $all_update[$request_data['advertiser_id']] = $success_ad_ids;
                        $all_update = array_merge($all_update, $success_ad_ids);
                    }
                    $success_data = array_merge($success_data, $all_change_info);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //修改失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的信息之后一起处理
                    if (isset($failed_data[$failed_info])) {
                        $failed_data[$failed_info] = array_merge($all_change_info, $failed_data[$failed_info]);
                    } else {
                        $failed_data[$failed_info] = $all_change_info;
                    }
                }
            } elseif (0 === intval($input['execute_type'])) {
                //定时修改，将请求数据汇总之后放入RabbitMQ
                $timed_execution_data = array_merge($timed_execution_data, $all_change_info);
                //$request_data存入session信息,access_token消费队列时重新查
                $request_data['editor_id'] = $single_record_operate['editor_id'];
                $request_data['editor_name'] = $single_record_operate['editor_name'];
                $request_data['request_type'] = $request_type;
                $request_data['media_type'] = MediaType::TOUTIAO;
                $all_data[] = $request_data;
            }
        }

        if ($all_data) {
            $time = strtotime($input['execute_time']) - time();
            if ('bid' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBidTask($all_data, $time);
            } elseif ('budget' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBudgetTask($all_data, $time);
            }
        }

        //无权限的ad2_id
        $not_permission = array_diff($all_ad_ids, $permission_ad_ids);
        foreach ($account_info as $v) {
            //处理成功的
            if ($success_data) {
                foreach ($success_data as $change_info) {
                    if ((int)$change_info['ad_id'] !== (int)$v->ad_id ||
                        (int)$change_info['account_id'] !== (int)$v->account_id) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 1;
                    $single_return_data['value'] = (string)$change_info['value'];
                    $single_return_data['message'] = 'success';
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->ad_id;
                    $single_record_operate['ad_name'] = $v->ad_name;
                    $single_record_operate['edit_detail'] = $opt_cn_name . '修改成功，由[' .
                        $change_info['original_value'] . ']修改为[' . $change_info['value'] . ']';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($change_info);
            }

            //处理定时执行的
            if ($timed_execution_data) {
                foreach ($timed_execution_data as $one_data) {
                    if ((int)$one_data['ad_id'] !== (int)$v->ad_id ||
                        (int)$one_data['account_id'] !== (int)$v->account_id) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 1;
                    $single_return_data['value'] = $one_data['original_value'];
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->ad_id;
                    $single_record_operate['ad_name'] = $v->ad_name;
                    $single_return_data['message'] = $single_record_operate['edit_detail'] =
                        $opt_cn_name . '修改准备' . $input['execute_time'] . '定时执行，由[' . $one_data['original_value'] .
                        ']修改为[' . $one_data['value'] . ']';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_data);
            }

            //处理失败的
            if ($failed_data) {
                foreach ($failed_data as $key => $change_info) {
                    foreach ($change_info as $one_info) {
                        if ((int)$one_info['ad_id'] !== (int)$v->ad_id ||
                            (int)$one_info['account_id'] !== (int)$v->account_id) {
                            continue;
                        }
                        $single_return_data['status'] = $single_record_operate['status'] = 2;
                        $single_return_data['value'] = 0;
                        $err_info = json_decode($key, true);
                        $single_return_data['message'] = $err_info['message'] ?? $key;
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->ad_id;
                        $single_record_operate['ad_name'] = $v->ad_name;
                        $single_record_operate['edit_detail'] = $opt_cn_name . '由[' . $one_info['original_value'] .
                            ']修改为[' . $one_info['value'] . ']修改失败，错误信息：' . $key;
                        $record_operate[] = $single_record_operate;
                        $return_data[] = $single_return_data;
                    }
                    unset($one_info);
                }
                unset($change_info);
            }

            //处理没有权限的
            if ($not_permission) {
                foreach ($not_permission as $ad_id) {
                    if ((int)$ad_id !== (int)$v->ad_id) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 2;
                    $single_return_data['value'] = 0;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                    $single_record_operate['ad_name'] = $v->ad_name;
                    $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($ad_id);
            }
        }

        if ($all_update) {
            //组合要update的sql
            if (1 === (int)$input['change_type']) {
                $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, 2 ) )";
            } elseif (2 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } elseif (3 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } else {
                throw new AppException('参数错误');
            }
            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $all_update,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 处理队列中头条定时修改预算或出价的数据
     * @param int $media_type
     * @param array $all_toutiao_mq_data
     * @return mixed|void
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $all_toutiao_mq_data)
    {
        $toutiao_http_model = new AdModel();
        //头条二级表查询条件
        $condition = [];
        foreach ($all_toutiao_mq_data as $v) {
            foreach ($v['data'] as $item) {
                $condition[$v['advertiser_id']][] = $item['ad_id'];
            }
            unset($item);
        }
        unset($v);
        if (!$condition) {
            return;
        }
        $account_ids = array_keys($condition);
        $ad_info = (new OdsToutiaoAdLogModel())->getADInfoByAccountIdAndADId($condition);
        $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids);
        if ($ad_info->isEmpty() || $access_token_info->isEmpty()) {
            return;
        }
        //整合access_token方便后面使用
        $access_tokens = [];
        foreach ($access_token_info as $v) {
            $access_tokens[$v->account_id] = $v->access_token;
        }
        unset($v);

        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];

        $success_data = [];
        $failed_data = [];
        foreach ($all_toutiao_mq_data as $v) {
            $request['advertiser_id'] = $v['advertiser_id'];
            $request['data'] = $v['data'];
            $access_token = $access_tokens[$v['advertiser_id']];
            $request_type = $v['request_type'];

            //把操作者和account_id信息塞进去
            $all_change_info = [];
            $datum = [];
            foreach ($v['data'] as $datum) {
                $datum['editor_id'] = $v['editor_id'];
                $datum['editor_name'] = $v['editor_name'];
                $datum['account_id'] = $v['advertiser_id'];
                $all_change_info[$datum['ad_id']] = $datum;
            }
            unset($datum);
            try {
                $response_data = $toutiao_http_model->updateBudgetOrBid($request, $access_token, $request_type);

                //部分失败
                if (isset($response_data['errors'])) {
                    foreach ($response_data['errors'] as $item) {
                        $failed_data[$item['error_message']][] = $all_change_info[$item['ad_id']];
                        $error_ad_ids[] = $item['ad_id'];
                        unset($all_change_info[$item['ad_id']]);
                    }
                }
                //修改成功
                $success_data = array_merge($success_data, $all_change_info);
            } catch (AppException $e) {
                //修改失败
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                if (isset($failed_data[$failed_info])) {
                    $failed_data[$failed_info] = array_merge($all_change_info, $failed_data[$failed_info]);
                } else {
                    $failed_data[$failed_info] = $all_change_info;
                }
            }
        }
        $opt_cn_name = ['cpa_bid' => '出价', 'budget' => '预算'];
        //组合操作记录数据
        foreach ($ad_info as $v) {
            //处理成功的
            if ($success_data) {
                foreach ($success_data as $change_info) {
                    if ((int)$change_info['ad_id'] !== (int)$v->ad_id ||
                        (int)$change_info['account_id'] !== (int)$v->account_id) {
                        continue;
                    }

                    if (isset($change_info['budget'])) {
                        $single_record_operate['edit_type'] = 3;
                        $update_field = $change_info_index = 'budget';
                    } else {
                        $single_record_operate['edit_type'] = 7;
                        $update_field = 'cpa_bid';
                        $change_info_index = 'bid';
                    }
                    $single_record_operate['status'] = 1;
                    $single_record_operate['editor_name'] = $change_info['editor_name'];
                    $single_record_operate['editor_id'] = $change_info['editor_id'];
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_record_operate['ad_id'] = (int)$v->ad_id;
                    $single_record_operate['ad_name'] = $v->ad_name;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$update_field] . '定时修改成功，由[' .
                        $v->$update_field . ']修改为[' . $change_info[$change_info_index] . ']';
                    $record_operate[] = $single_record_operate;

                }
                unset($change_info);
            }

            //处理失败的
            if ($failed_data) {
                foreach ($failed_data as $key => $change_info) {
                    foreach ($change_info as $one_info) {
                        if ((int)$one_info['ad_id'] !== (int)$v->ad_id ||
                            (int)$one_info['account_id'] !== (int)$v->account_id) {
                            continue;
                        }

                        if (isset($one_info['budget'])) {
                            $single_record_operate['edit_type'] = 3;
                            $update_field = $change_info_index = 'budget';
                        } else {
                            $single_record_operate['edit_type'] = 7;
                            $update_field = 'cpa_bid';
                            $change_info_index = 'bid';
                        }
                        $single_record_operate['status'] = 2;
                        $single_record_operate['editor_name'] = $one_info['editor_name'];
                        $single_record_operate['editor_id'] = $one_info['editor_id'];
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_record_operate['ad_id'] = (int)$v->ad_id;
                        $single_record_operate['ad_name'] = $v->ad_name;
                        $error_info = json_decode($key, true);
                        $error_msg = $error_info['message'] ?? $key;
                        $single_record_operate['edit_detail'] = $opt_cn_name[$update_field] . '由[' . $v->$update_field .
                            ']修改为[' . $one_info[$change_info_index] . '] 定时修改失败，失败信息：' . $error_msg;
                        $record_operate[] = $single_record_operate;
                    }
                    unset($one_info);
                }
                unset($change_info);
            }
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
    }

    /**
     * 批量修改头条定向包数据
     * @param array $input
     * @param Collection $account_info
     * @return array
     */
    public function updateADAnalysisTargeting(array $input, Collection $account_info)
    {
        $access_token = [];
        $platform_for_targeting = '';
        $account_ids_for_targeting = [];
        foreach ($account_info as $item) {
            $access_token[$item->account_id] = $item->access_token;
            if (!$platform_for_targeting) {
                $platform_for_targeting = $item->platform;
            }
            $account_ids_for_targeting[] = $item->account_id;
        }
        unset($item);

        if ($platform_for_targeting) {
            //推送人群包并获取定向包内容
            $request_data = (new ADServingLogic())->pushAudienceForAnalysis(
                (int)$input['targeting_id'],
                (string)$platform_for_targeting,
                $account_ids_for_targeting
            )['targeting'];
        }
        $toutiao_http_model = new AdModel();
        //单条操作记录
        $single_record_operate = [];

        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价,
        // 9: 修改定向
        $single_record_operate['edit_type'] = 9;
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        $single_return_data['value'] = 0;
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($input['ad_info'] as $v) {
            $single_record_operate['ad_name'] = $v['ad_name'];
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_record_operate['platform'] = $v['platform'];
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v['ad_id'];

            if (!isset($access_token[$v['account_id']])) {
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $this_access_token = $access_token[$v['account_id']];
            $request_data['ad_id'] = (int)$v['ad_id'];

            //获取二级广告最新modify_time
            $get_ad2_request['advertiser_id'] = $request_data['advertiser_id'] = (int)$v['account_id'];
            $get_ad2_request['filtering'] = ['ids' => [(int)$v['ad_id']]];
            try {
                $ad2_info = $toutiao_http_model->getAD2Info($get_ad2_request, $this_access_token);
                //检查是否拉取到modify_time
                if (!isset($ad2_info['list'][0]['modify_time'])) {
                    throw new AppException('修改定向时未拉取到广告modify_time');
                } else {
                    $request_data['modify_time'] = $ad2_info['list'][0]['modify_time'];
                    //向头条发起请求
                    $response = $toutiao_http_model->update($request_data, $this_access_token);
                    //修改成功
                    $single_return_data['message'] = 'success';
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = "定向修改成功";
                }
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '定向修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /*
    * @param $company
    * @param $audience_md5
    * @return array
    */
    public function getTargetingDataByAD2($company, $audience_md5)
    {
        $data = (new OdsToutiaoAdLogModel())->getTargetingDataByAD2($audience_md5);
        if ($data) {
            $param = new ADTargetingPacketParam([
                'company' => $company,
                'media_type' => MediaType::TOUTIAO,
                'state' => ADTargetingPacketParam::UN_FINISH_STATE
            ]);
            $audience = json_decode($data->audience, true);
            $audience['action_categories'] = $audience['action']['action_categories'] ?? [];
            $audience['action_days'] = $audience['action']['action_days'] ?? '';
            $audience['action_scene'] = $audience['action']['action_scene'] ?? [];
            $audience['action_words'] = $audience['action']['action_words'] ?? [];
            $param->setTargetingByArray($audience);
            $param->state = 1;
            $data = $param->toArray();
            $data['targeting'] = json_encode($data['targeting']->toArray());
            return $this->fillTargetingInfo($data, true);
        } else {
            throw new AppException('找不到对应的定向包数据');
        }
    }

    /**
     * 转账
     * @param ADAnalysisToutiaoTransferParam $param
     * @return array
     */
    public function transfer(ADAnalysisToutiaoTransferParam $param)
    {
        //获取所有account_id
        $account_ids = array_column($param->transfer_info, 'operate_account_id');

        foreach ($param->transfer_info as $item) {
            $account_ids = array_merge(array_column($item['account_info'], 'account_id'), $account_ids);
        }

        $leader_permission = $param->leader_permission ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission);

        $access_token_format = [];
        $platform = [];
        $account_name = [];
        foreach ($access_token_info as $item) {
            $access_token_format[$item->account_id] = $item->access_token;
            $platform[$item->account_id] = $item->platform;
            $account_name[$item->account_id] = $item->account_name;
        }
        unset($item);

        $http_model = new AdvertiserModel();
        $one_record = [];
        $one_record['operator'] = $param->operator;
        $one_record['operate_time'] = date('Y-m-d H:i:s');
        $all_record = [];
        $update_account_ids = [];
        $failed_account_infos = [];
        foreach ($param->transfer_info as $one_transfer_info) {
            if (!$one_transfer_info['account_info']) {
                continue;
            }
            foreach ($one_transfer_info['account_info'] as $item) {
                if (0.0 === (float)$item['amount']) {
                    continue;
                }
                $failed_account_info = [];
                $error = [];
                if ('RECHARGE' === $param->operate_type) {
                    //充值
                    $advertiser_id = (int)$one_transfer_info['operate_account_id'];
                    $target_advertiser_id = (int)$item['account_id'];
                } else {
                    //退款
                    $advertiser_id = (int)$item['account_id'];
                    $target_advertiser_id = (int)$one_transfer_info['operate_account_id'];
                }

                $amount = (float)Math::decimal($item['amount'], 2);
                $transfer_type = $item['transfer_type'];

                $one_record['platform'] = $platform[$advertiser_id] ?? '';
                $one_record['account_id'] = $advertiser_id;
                $one_record['account_name'] = $account_name[$advertiser_id] ?? '';
                $one_record['target_account_id'] = $target_advertiser_id;
                $one_record['target_account_name'] = $account_name[$target_advertiser_id] ?? '';
                $one_record['amount'] = $amount;
                $one_record['transfer_type'] = $param::TRANSFER_TYPE[$transfer_type];
                //如果没有拿到access_token，充值跳出两层循环，退款跳出一层
                if (!isset($access_token_format[$advertiser_id]) || !isset($access_token_format[$target_advertiser_id])) {
                    $one_record['transaction_seq'] = 0;
                    $one_record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id 转账失败，没有转账权限";
                    $one_record['status'] = 2;
                    $all_record[] = $one_record;
                    $failed_account_info['account_id'] = $target_advertiser_id;
                    $failed_account_info['operate_account_id'] = $advertiser_id;
                    $failed_account_info['transfer_type'] = $transfer_type;
                    $failed_account_info['msg'] = "没有转账权限";
                    $failed_account_info['amount'] = $amount;
                    $failed_account_infos[] = $failed_account_info;
                    //对于充值，来源账号没权限直接跳出两层，对于退款，目标账号没有权限也跳出两层
                    if (('RECHARGE' === $param->operate_type && !isset($access_token_format[$advertiser_id])) ||
                        ('REFUND' === $param->operate_type && !isset($access_token_format[$target_advertiser_id]))) {
                        continue 2;
                    } else {
                        continue 1;
                    }
                }
                $access_token = $access_token_format[$advertiser_id];
                try {
                    $response = $http_model->transfer($advertiser_id, $target_advertiser_id, $amount, $transfer_type, $access_token);
                    $one_record['transaction_seq'] = $response['transaction_seq'] ?? 0;
                    $one_record['status'] = 1;
                    $one_record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账成功';
                    $update_account_ids[] = $advertiser_id;
                    $update_account_ids[] = $target_advertiser_id;
                } catch (AppException $e) {
                    $failed_account_info['account_id'] = $target_advertiser_id;
                    $failed_account_info['operate_account_id'] = $advertiser_id;
                    $failed_account_info['transfer_type'] = $transfer_type;
                    $failed_account_info['msg'] = $e->getMessage();
                    $failed_account_info['amount'] = $amount;
                    $failed_account_infos[] = $failed_account_info;

                    $one_record['transaction_seq'] = 0;
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $one_record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id 转账失败，失败原因：" . json_encode($error, JSON_UNESCAPED_UNICODE);
                    $one_record['status'] = 2;
                }
                $all_record[] = $one_record;
            }
        }
        if ($update_account_ids) {
            $update_account_ids = array_flip(array_flip($update_account_ids));
            (new ToutiaoTaskModel())->account(array_values($update_account_ids));
        }

        (new OdsToutiaoTransferOperateLogModel())->add($all_record);

        return $failed_account_infos;
    }

    /**
     * @param $create_data
     * @return ADTaskParam
     */
    private function makeADTaskData($create_data)
    {
        if ($create_data['account_id']) {
            $account_info = (new MediaAccountModel())->getDataByAccountId($create_data['account_id'], MediaType::TOUTIAO);
            if ($account_info) {
                $create_data['company'] = $account_info->company;
                $create_data['account_name'] = $account_info->account_name;
            } else {
                throw new AppException('找不到账号信息');
            }
        } else {
            throw new AppException('account_id为空');
        }

        $md5_list = [];
        foreach ($create_data['creative_list'] as $create_info) {
            if (isset($create_info['cover_info'])) {
                if (isset($create_info['cover_info']['md5']) && isset($create_info['video_info']['md5'])) {
                    $md5_list[] = $create_info['cover_info']['md5'];
                    $md5_list[] = $create_info['video_info']['md5'];
                }
            } else {
                if (isset($create_info['image_info']['md5'])) {
                    $md5_list[] = $create_info['image_info']['md5'];
                }
            }
        }
        $md5_list = array_values(array_unique($md5_list));
        $find_material_file_md5_list = [];
        if ($md5_list) {
            $find_material_file_md5_list = (new OdsMaterialFileLogModel())->getListBySignature($md5_list)->keyBy('signature')->toArray();
            if (count($find_material_file_md5_list) <= 0) {
                throw new AppException("创意素材错误:根据MD5找不到任何一个素材信息");
            }
            if (count($find_material_file_md5_list) < count($md5_list)) {
                $find_material_file_md5_list = array_keys($find_material_file_md5_list);
                $result = '';
                foreach ($md5_list as $material_file_md5) {
                    if (!in_array($material_file_md5, $find_material_file_md5_list)) {
                        $result .= ($material_file_md5 . ',');
                    }
                }
                $result = trim($result, ',');
                throw new AppException("创意素材错误:找不到对应{$result}可用的素材文件信息");
            }
        }

        $create_list = new ADComposeCreativeListParam();
        foreach ($create_data['creative_list'] as $create_info) {
            if (isset($create_info['cover_info'])) {

                if (isset($create_info['cover_info']['md5']) && isset($create_info['video_info']['md5'])) {
                    $cover_info = $find_material_file_md5_list[$create_info['cover_info']['md5']];
                    $video_info = $find_material_file_md5_list[$create_info['video_info']['md5']];
                    $cover_id = $cover_info->id;
                    $cover_url = $cover_info->url;
                    $video_id = $video_info->id;
                    $video_url = $video_info->url;
                } else {
                    $cover_id = $create_info['cover_info']['id'];
                    $cover_url = $create_info['cover_info']['url'];
                    $video_id = $create_info['video_info']['id'];
                    $video_url = $create_info['video_info']['url'];
                }

                $create_list->addVideoToList(
                    $create_info['title'] ?? '',
                    new ADComposeCreativeVideoCoverParam([
                        'id' => $cover_id,
                        'url' => $cover_url,
                        'width' => $create_info['cover_info']['width'],
                        'height' => $create_info['cover_info']['height'],
                    ]),
                    new ADComposeCreativeVideoParam([
                        'id' => $video_id,
                        'url' => $video_url,
                        'width' => $create_info['video_info']['width'],
                        'height' => $create_info['video_info']['height'],
                    ])
                );
            } else {
                if (isset($create_info['image_info']['md5'])) {
                    $image_info = $find_material_file_md5_list[$create_info['image_info']['md5']];
                    $image_id = $image_info->id;
                    $image_url = $image_info->url;
                } else {
                    $image_id = $create_info['image_info']['id'];
                    $image_url = $create_info['image_info']['url'];
                }

                $create_list->addImageToList(
                    $create_info['title'] ?? '',
                    new ADComposeCreativeImageParam([
                        'id' => $image_id,
                        'url' => $image_url,
                        'width' => $create_info['image_info']['width'],
                        'height' => $create_info['image_info']['height'],
                    ])
                );
            }
        }

        $targeting_data['targeting'] = json_encode([
            'search_keyword_type' => $create_data['search_keyword_type'] ?? '',
            'feed_search_keyword_list' => $create_data['feed_search_keyword_list'] ?? [],
            'district' => $create_data['district'] ?? '',
            'city' => $create_data['city'] ?? [],
            'location_type' => $create_data['location_type'] ?? '',
            'gender' => $create_data['gender'] ?? '',
            'age' => $create_data['age'] ?? '',
            'ios_osv' => $create_data['ios_osv'] ?? '',
            'android_osv' => $create_data['android_osv'] ?? '',
            'ac' => $create_data['ac'] ?? [],
            'app_behavior_target' => $create_data['app_behavior_target'] ?? '',
            'app_category' => $create_data['app_category'] ?? [],
            'auto_extend_enabled' => $create_data['auto_extend_enabled'] ?? [],
            'auto_extend_targets' => $create_data['auto_extend_targets'] ?? [],
            'carrier' => $create_data['carrier'] ?? [],
            'activate_type' => $create_data['activate_type'] ?? [],
            'launch_price' => $create_data['launch_price'] ?? [],
            'interest_action_mode' => $create_data['interest_action_mode'] ?? 'UNLIMITED',
            'action_scene' => $create_data['action_scene'] ?? [],
            'action_days' => $create_data['action_days'] ?? '',
            'action_categories' => $create_data['action_categories'] ?? [],
            'action_words' => $create_data['action_words'] ?? [],
            'interest_categories' => $create_data['interest_categories'] ?? [],
            'interest_words' => $create_data['interest_words'] ?? [],
            'retargeting_tags_exclude' => $create_data['retargeting_tags_exclude'] ?? [],
            'retargeting_tags_include' => $create_data['retargeting_tags_include'] ?? [],
            'device_brand' => $create_data['device_brand'] ?? [],
            'device_type' => $create_data['device_type'] ?? '',
            'superior_popularity_type' => $create_data['superior_popularity_type'] ?? 'NONE',
            'flow_package' => $create_data['flow_package'] ?? 'NONE',
            'exclude_flow_package' => $create_data['exclude_flow_package'] ?? 'NONE',
            'audience_extend' => $create_data['audience_extend'] ?? 'ON',
            'search_bid_ratio' => $create_data['search_bid_ratio'] ?? 0,
        ]);
        $targeting_data = $this->fillTargetingInfo($targeting_data, true);
        $targeting_pa = new ADTargetingContentParam($targeting_data['targeting']);
        $targeting_pa->format(ADTargetingPacketParam::FINISH_STATE);

        // 针对信息流2.0操作
        if ($create_data['media_agent_type'] == 2) {
            $create_data['delivery_range'] = 'DEFAULT';
            $create_data['smart_bid_type'] = 'CUSTOM';
        }

        $setting_data = [
            'delivery_range' => $create_data['delivery_range'] ?? 'DEFAULT',
            'union_video_type' => $create_data['union_video_type'] ?? 'ORIGINAL_VIDEO',
            'smart_inventory' => $create_data['smart_inventory'] ?? 0,
            'inventory_mode' => $create_data['inventory_mode'] ?? 'inventory_type',
            'inventory_type' => $create_data['inventory_type'] ?? [],
            'schedule_type' => $create_data['schedule_type'] ?? 'SCHEDULE_FROM_NOW',
            'smart_bid_type' => $create_data['smart_bid_type'] ?? 'SMART_BID_CUSTOM',
            'start_time' => $create_data['start_time'] ?? '',
            'end_time' => $create_data['end_time'] ?? '',
            'budget' => $create_data['budget'] ?? 0,
            'deep_bid_type' => $create_data['deep_bid_type'] ?? 'DEEP_BID_DEFAULT',
            'roi_goal' => $create_data['roi_goal'] ?? [],
            'deep_cpabid' => $create_data['deep_cpabid'] ?? 0,
            'is_comment_disable' => $create_data['is_comment_disable'] ?? 0,
            'creative_auto_generate_switch' => $create_data['creative_auto_generate_switch'] ?? 1,
            'third_industry_id' => $create_data['third_industry_id'] ?? '',
            'ad_keywords' => $create_data['ad_keywords'] ?? [],
            'schedule_time' => $create_data['schedule_time'] ?? [],
            'cpa_bid' => $create_data['cpa_bid'] ?? [],
            'campaign_operation' => $create_data['campaign_operation'] ?? 'disable',
            'ad_operation' => $create_data['ad_operation'] ?? 'disable',
            'flow_control_mode' => $create_data['flow_control_mode'] ?? 'FLOW_CONTROL_MODE_FAST',
            'budget_mode' => $create_data['budget_mode'] ?? 'BUDGET_MODE_DAY',
            'pricing' => $create_data['pricing'] ?? 'PRICING_OCPM',
            'adjust_cpa' => $create_data['adjust_cpa'] ?? 0,
            'deep_adjust_cpa' => $create_data['deep_adjust_cpa'] ?? 1,
            'hide_if_converted' => $create_data['hide_if_converted'] ?? 'NO_EXCLUDE',
            'converted_time_duration' => $create_data['converted_time_duration'] ?? 'THREE_MONTH',
            'hide_if_exists' => $create_data['hide_if_exists'] ?? 0,
            'generate_derived_ad' => $create_data['generate_derived_ad'] ?? 0,
            'is_presented_video' => $create_data['is_presented_video'] ?? 0,
            'promotion_card_mode' => $create_data['promotion_card_mode'] ?? 'none',
            'creative_display_mode' => $create_data['creative_display_mode'] ?? 'CREATIVE_DISPLAY_MODE_CTR',
            'product_selling_points' => $create_data['product_selling_points'] ?? [''],
            'product_description' => $create_data['product_description'] ?? '',
            'call_to_action' => $create_data['call_to_action'] ?? '',
            'enable_personal_action' => $create_data['enable_personal_action'] ?? 0,
            'product_image_id' => $create_data['product_image_id'] ?? '',
            'ad1_budget' => $create_data['ad1_budget'] ?? '',
            'ad2_budget' => $create_data['ad2_budget'] ?? '',
            'product_info_image_id' => $create_data['product_info_image_id'] ?? '',
            'product_info_title' => $create_data['product_info_title'] ?? '',
            'product_info_selling_points' => $create_data['product_info_selling_points'] ?? [],
            'call_to_action_buttons' => $create_data['call_to_action_buttons'] ?? [],
        ];

        if ($create_data['media_agent_type'] == 0) {
            $setting_pa = new ADSettingContentParam($setting_data);
        } else {
            $setting_data['ad1_status'] = $setting_data['campaign_operation'];
            $setting_data['ad2_status'] = $setting_data['ad_operation'];
            $setting_pa = new V2ADSettingContentParam($setting_data);
        }
        $setting_pa->format();

        $other_setting_data = [
            'promotion_type' => $create_data['promotion_type'] ?? 'APP',
            'download_type' => $create_data['download_type'] ?? 'DOWNLOAD_URL',
            'app_name' => $create_data['app_name'] ?? '',
            'source' => $create_data['source'] ?? '',
            'web_url' => $create_data['web_url'] ?? '',
            'external_url' => $create_data['external_url'] ?? '',
            'auto_inherit_switch' => $create_data['auto_inherit_switch'] ?? 'OFF',
            'inherit_type' => $create_data['inherit_type'] ?? 'INHERIT_FROM_ACCOUNT',
            'inherited_advertiser_id' => $create_data['inherited_advertiser_id'] ?? [],
            'web_url_list' => $create_data['web_url_list'] ?? [],
            'external_url_list' => $create_data['external_url_list'] ?? [],
            'os' => $create_data['os'] ?? [],
            'app_promotion_type' => $create_data['app_promotion_type'] ?? 'DOWNLOAD',
            'marketing_goal' => $create_data['marketing_goal'] ?? 'VIDEO_AND_IMAGE',
            'ad_type' => $create_data['ad_type'] ?? 'ALL',
            'download_mode' => $create_data['download_mode'] ?? 'DEFAULT',
        ];

        if (is_array($other_setting_data['inherited_advertiser_id']) && $other_setting_data['inherited_advertiser_id']) {
            $other_setting_data['inherited_advertiser_id'] = array_map(function ($ele) {
                return ['account_id' => $ele, 'account_name' => $ele];
            }, $other_setting_data['inherited_advertiser_id']);
        }

        $create_data['convert_type'] = (string)$create_data['convert_type'];
        $create_data['deep_external_action'] = (string)$create_data['deep_external_action'];
        $site_config = new SiteConfigParam($create_data);
        $site_config->convert_toolkit = ConvertToolkit::TOUTIAO_ASSET;
        if ($create_data['apple_ppid'] ?? false) {
            $site_config->ext['apple_ppid'] = $create_data['apple_ppid'];
        }

        $param = (new ADTaskParam([
            'compose_id' => 0,
            'company' => $create_data['company'],
            'origin_type' => 3,
            'platform' => $create_data['platform'],
            'media_type' => $create_data['media_type'],
            'creative_mode' => count($create_data['word_list'] ?? []) > 0 ? BatchAD::CREATIVE_PROGRAM_MODE : BatchAD::CREATIVE_CUSTOM_MODE,
            'media_agent_type' => $create_data['media_agent_type'],
            'creator' => $create_data['creator'],
            'creator_id' => $create_data['creator_id'],
        ]))->initByCompose(
            $account_info->account_id,
            $account_info->account_name,
            $create_list,
            $create_data['word_list'] ?? [],
            '智能投放定向',
            $targeting_pa,
            '智能投放参数',
            $setting_pa,
            $site_config->toArray(),
            new ADComposeConfigParam([
                'ad1_name' => $create_data['campaign_name'],
                'ad2_name' => $create_data['ad_name']
            ]),
            $create_data['media_agent_type'] == 0 ? new ADOtherSettingContentParam($other_setting_data) : new V2ADOtherSettingContentParam($other_setting_data),
            $create_data['calc_rule_list'] ?: [],
            $create_data['rta_list'] ?: []
        );

        if (!in_array($create_data['task_state'] ?? 1, [1, 2, 3, 4])) {
            throw new AppException('task_state状态错误');
        }
        $param->task_state = $create_data['task_state'] ?? 1;
        return $param;
    }

    /**
     * @param array $create_data
     * @return int|string
     */
    public function addADTaskByApi(array $create_data)
    {
        $create_data['schedule_time'] = str_split($create_data['schedule_time']);
        $create_data['schedule_time'] = array_map(function ($element) {
            return (int)$element;
        }, $create_data['schedule_time']);

        $create_data['word_type'] = (int)($create_data['word_type'] ?? 1);

        if ($create_data['product_selling_points'] && $create_data['product_description'] &&
            $create_data['call_to_action'] && $create_data['product_image_id']) {
            $create_data['promotion_card_mode'] = 'zidingyi';
        } else {
            $create_data['promotion_card_mode'] = 'none';
        }

        $create_data['media_agent_type'] = $create_data['media_agent_type'] ?? 0;

        $task_param = $this->makeADTaskData($create_data);

        $task_create_param = new ADTaskCreateParam();
        $task_create_param->addADTaskParam($task_param);
        $task_create_param->setADTaskQueueIndex();
        $task_param->queue_index = $task_create_param->getAccountQueueIndexMap()[$task_param->account_id] ?? 1;
        $task_id = (new ADTaskModel())->addTask($task_param);

        if ($task_id) {
            $task_param->id = $task_id;
            if ($task_param->task_state == 1) {
                (new ADTaskMasterMQLogic())->produceTask($task_param->toMQData());
            }

            return $task_param->id;
        } else {
            throw  new AppException('新建广告任务失败!');
        }
    }

    public function updateAndroidChannelPackage(array $input)
    {

    }

    /**
     * 基本报表-批量修改深度优化出价或ROI
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoAdLogModel();
        $column = ['ad_id', 'account_id', 'platform', 'ad_name', 'deep_bid_type', 'deep_cpabid', 'roi_goal'];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new AdModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        if ('deep_bid' === $update_field) {
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_DEEP_BID;
            $update_field = 'deep_cpabid';
            $opt_detail = '深度转化出价';
            $decimal = 2;
        } else {
            //修改ROI
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_ROI;
            $update_field = 'roi_goal';
            $opt_detail = 'ROI';
            $decimal = 4;
        }

        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->ad_id;
                $single_record_operate['ad_name'] = $v->ad_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['ad_id'] = (int)$v->ad_id;
            //获取二级广告最新modify_time
            $get_ad2_request['advertiser_id'] = $request_data['advertiser_id'] = (int)$v->account_id;
            $get_ad2_request['filtering'] = ['ids' => [(int)$v->ad_id]];
            try {
                $ad2_info = $toutiao_http_model->getAD2Info($get_ad2_request, $access_tokens[$v->account_id]);
                //检查是否拉取到modify_time
                if (!isset($ad2_info['list'][0]['modify_time'])) {
                    throw new AppException('修改二级广告时未拉取到二级广告modify_time');
                } else {
                    $request_data['modify_time'] = $ad2_info['list'][0]['modify_time'];
                    $original_value = $v->$update_field;
                    if (1 === intval($input['change_type'])) {
                        $v->$update_field = $input['change_value'];
                    } elseif (2 === intval($input['change_type'])) {
                        $v->$update_field += number_format($v->$update_field * ($input['change_value'] / 100),
                            $decimal, '.', '');
                    } elseif (3 === intval($input['change_type'])) {
                        $v->$update_field -= number_format($v->$update_field * ($input['change_value'] / 100),
                            $decimal, '.', '');
                    } else {
                        throw new AppException('参数错误');
                    }
                    if ('deep_cpabid' === $update_field) {
                        if ($v->$update_field < 0.1 || $v->$update_field > 10000) {
                            throw new AppException('深度优化出价允许范围为：0.1-10000');
                        }
                        if ($v->deep_bid_type !== 'DEEP_BID_MIN') {
                            throw new AppException('当前深度优化方式不允许修改深度优化出价');
                        }
                    }
                    if ('roi_goal' === $update_field) {
                        if (($v->$update_field <= 0 || $v->$update_field > 5)) {
                            throw new AppException('深度转化ROI系数允许范围为：0.1-10000');
                        }
                        if ($v->deep_bid_type !== 'ROI_COEFFICIENT') {
                            throw new AppException('当前深度优化方式不允许修改深度转化ROI系数');
                        }
                    }
                    $request_data[$update_field] = $v->$update_field;
                    //向头条发起请求
                    $toutiao_http_model->update($request_data, $access_tokens[$v->account_id]);
                    //修改成功
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = $v->$update_field;
                    $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->ad_name;
//                    $update_data[$v->account_id][] = (int)$v->ad_id;
                    $update_data[] = (int)$v->ad_id;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                        ']修改为[' . $v->$update_field . ']' . "，修改成功";
                }
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->ad_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->ad_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (1 === (int)$input['change_type']) {
                $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 删除基本报表二三级广告
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return mixed|void
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $toutiao_format_target)
    {
        if (1 === (int)$data['ad_level']) {
            throw new AppException('一级广告禁止删除');
        }
        return $this->updateADAnalysisStatus($media_type, $data, $toutiao_format_target);
    }

    /**
     * 基本报表-批量修改一级预算
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoCampaignLogModel();
        $column = ['campaign_id', 'account_id', 'platform', 'campaign_name', 'budget'];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new CampaignModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $opt_detail = '头条一级广告预算';
        $decimal = 2;

        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->campaign_id;
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['campaign_id'] = (int)$v->campaign_id;
            //获取一级广告最新modify_time
            $request_data['advertiser_id'] = (int)$v->account_id;
            $filtering = ['ids' => [(int)$request_data['campaign_id']]];
            try {
                $ad1_info = $toutiao_http_model->info($request_data['advertiser_id'], $access_tokens[$v->account_id], [], $filtering);
                //检查是否拉取到modify_time
                if (!isset($ad1_info['list'][0]['modify_time'])) {
                    throw new AppException('修改一级广告预算时未拉取到一级广告modify_time');
                } else {
                    $request_data['modify_time'] = $ad1_info['list'][0]['modify_time'];
                    $original_value = $v->budget;
                    if (0 === (int)$input['change_value']) {
                        $request_data['budget_mode'] = 'BUDGET_MODE_INFINITE';
                        $request_data['budget'] = $v->budget = 0;
                    } else {
                        if (1 === intval($input['change_type'])) {
                            $v->budget = $input['change_value'];
                        } elseif (2 === intval($input['change_type'])) {
                            $v->budget += number_format($v->budget * ($input['change_value'] / 100),
                                $decimal, '.', '');
                        } elseif (3 === intval($input['change_type'])) {
                            $v->budget -= number_format($v->budget * ($input['change_value'] / 100),
                                $decimal, '.', '');
                        } else {
                            throw new AppException('参数错误');
                        }
                        $request_data['budget_mode'] = 'BUDGET_MODE_DAY';
                        $request_data['budget'] = $v->budget;
                    }

                    //向头条发起请求
                    $toutiao_http_model->update($request_data, $access_tokens[$v->account_id]);
                    //修改成功
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = $v->budget;
                    $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->campaign_name;
//                    $update_data[$v->account_id][] = (int)$v->campaign_id;
                    $update_data[] = (int)$v->campaign_id;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $change_value = 0 === $v->budget ? '无限' : $v->budget;
                    $original_value = (int)$original_value ?: '无限';
                    $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                        ']修改为[' . $change_value . ']' . "，修改成功";
                }
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->campaign_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (0 === (int)$input['change_value']) {
                $update_set = 0;
            } else {
                if (1 === (int)$input['change_type']) {
                    $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
                } elseif (2 === (int)$input['change_type']) {
                    $update_set =
                        "CAST ( ( budget + budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
                } elseif (3 === (int)$input['change_type']) {
                    $update_set =
                        "CAST ( ( budget - budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
                } else {
                    throw new AppException('参数错误');
                }
            }

            (new OdsToutiaoCampaignHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                ['budget', 'budget_mode'],
                [$update_set, $request_data['budget_mode']],
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表更新账号预算
     * @param int $media_type
     * @param array $input
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $input, array $ad_format_target, string $update_field)
    {
        $toutiao_model = new OdsToutiaoAccountLogModel();
        $column = ['account_id', 'account_budget', 'account_budget_mode', 'platform'];
        $account_info = $toutiao_model->getAccountInfoInAccountIds($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new AccountHttpModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $decimal = 2;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['ad_id'] = 0;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['advertiser_id'] = (int)$v->account_id;
            try {
                $original_value = $v->account_budget;
                if (0 === (int)$input['change_value']) {
                    $request_data['budget_mode'] = 'BUDGET_MODE_INFINITE';
                    $request_data['budget'] = $v->account_budget = 0;
                } else {
                    if (1 === intval($input['change_type'])) {
                        $v->account_budget = $input['change_value'];
                    } elseif (2 === intval($input['change_type'])) {
                        $v->account_budget += number_format($v->account_budget * ($input['change_value'] / 100),
                            $decimal, '.', '');
                    } elseif (3 === intval($input['change_type'])) {
                        $v->account_budget -= number_format($v->account_budget * ($input['change_value'] / 100),
                            $decimal, '.', '');
                    } else {
                        throw new AppException('参数错误');
                    }
                    $request_data['budget_mode'] = 'BUDGET_MODE_DAY';
                    $request_data['budget'] = $v->account_budget;
                }

                //向头条发起请求
                $toutiao_http_model->updateAdvertiserBudget($request_data, $access_tokens[$v->account_id]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->account_budget;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = 0 === (int)$v->account_budget ? '无限' : $v->account_budget;
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = "头条账号预算由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";

            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '头条账号预算修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = '';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = 0;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 绑定RTA
     * @param array $input
     * @return mixed|void
     */
    public function bindADAnalysisADRTA(array $input)
    {
        $account_ids = collect($input['ad_info'])->pluck('account_id')->toArray();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, -1)->keyBy('account_id');

        if ($access_token_info->isEmpty()) {
            throw new AppException('绑定失败');
        }

        $all_bind_log = [];
        foreach ($input['ad_info'] as $item) {
            $bind_log = [];
            $bind_log['platform'] = $item['platform'];
            $bind_log['rta_id'] = $input['rta_id'];
            $bind_log['majordomo_account_id'] = $access_token_info[$item['account_id']]->toutiao_majordomo_id ?? 0;
            $bind_log['target_name'] = $input['target_name'] ?? '';
            $bind_log['remark'] = $input['remark'] ?? '';
            $bind_log['binding_target_id'] = $item['account_id'];
            $bind_log['target_type'] = $input['target_type'];
            $bind_log['account_id'] = $item['account_id'];
            $bind_log['operator'] = $input['editor_name'] ?? '';
            $bind_log['operate_type'] = 1;
            if (!isset($access_token_info[$item['account_id']])) {
                $bind_log['operate_detail'] = '绑定失败，失败原因: 无操作权限';
                $bind_log['status'] = 2;
                $all_bind_log[] = $bind_log;
                continue;
            }

            $http_model = new RTAModel();
            try {
                $access_token = $access_token_info[$item['account_id']]->access_token;
                $response = $http_model->rtaStatusUpdate((int)$item['account_id'], [(int)$input['rta_id']], 'ENABLE', $access_token);
                $bind_log['operate_detail'] = '绑定成功, request_id: ' . $response['request_id'];
                $bind_log['status'] = 1;
            } catch (AppException $e) {
                $bind_log['operate_detail'] = '绑定失败，失败原因: ' . $e->getMessage();
                $bind_log['status'] = 2;
            }
            $all_bind_log[] = $bind_log;
        }

        (new OdsADAnalysisToutiaoRTATargetBindLogModel())->add($all_bind_log);

    }

    /**
     * 解绑RTA
     * @param array $data
     * @param array $input
     * @return mixed|void
     */
    public function unbindADAnalysisADRTA(array $data, array $input)
    {
        $account_ids = collect($data)->pluck('account_id')->toArray();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, -1)->keyBy('account_id');

        if ($access_token_info->isEmpty()) {
            throw new AppException('解绑失败');
        }

        $all_bind_log = [];
        foreach ($data as $item) {
            $bind_log = [];
            $bind_log['platform'] = $item['platform'];
            $bind_log['rta_id'] = $item['rta_id'];
            $bind_log['majordomo_account_id'] = $access_token_info[$item['account_id']]->toutiao_majordomo_id ?? 0;
            $bind_log['target_name'] = $item['target_name'] ?? '';
            $bind_log['remark'] = $item['remark'] ?? '';
            $bind_log['binding_target_id'] = $item['account_id'];
            $bind_log['target_type'] = $input['target_type'];
            $bind_log['account_id'] = $item['account_id'];
            $bind_log['operator'] = $input['editor_name'];
            $bind_log['operate_type'] = 2;
            if (!isset($access_token_info[$item['account_id']])) {
                $bind_log['operate_detail'] = '解绑失败，失败原因: 无操作权限';
                $bind_log['status'] = 2;
                $all_bind_log[] = $bind_log;
                continue;
            }

            $http_model = new RTAModel();
            try {
                $access_token = $access_token_info[$item['account_id']]->access_token;
                $response = $http_model->rtaStatusUpdate((int)$item['account_id'], [(int)$item['rta_id']], 'DISABLE', $access_token);
                $bind_log['operate_detail'] = '解绑成功, request_id: ' . $response['request_id'];
                $bind_log['status'] = 1;
            } catch (AppException $e) {
                $bind_log['operate_detail'] = '解绑失败，失败原因: ' . $e->getMessage();
                $bind_log['status'] = 2;
            }
            $all_bind_log[] = $bind_log;
        }

        (new OdsADAnalysisToutiaoRTATargetBindLogModel())->add($all_bind_log);

    }

    /**
     * 根据账号获取头条抖音号id
     * @param int $account_id
     * @param string $platform
     * @return array
     */
    public function getToutiaoIesIdList(int $account_id, string $platform): array
    {
        if (in_array($platform, [Platform::TW])) {
            return (new OdsToutiaoAwemeAuthDetailLogModel())->getAccordingPlatformAwemeListRules($account_id)->toArray();
        } else {
            return (new OdsToutiaoAwemeAuthDetailLogModel())->getAwemeListByAccountId($account_id)->toArray();
        }
//        $media_account_model = new MediaAccountModel();
//        $account_data = $media_account_model->getDataByAccountId($account_id, MediaType::TOUTIAO);
//        if (!$account_data) {
//            throw new AppException("找不到{$account_id}的账号信息");
//        }
//        return (new IesAccountModel())->search($account_data->account_id, $account_data->access_token);
    }

    /**
     * 人群预估
     * @param array $target
     * @param $account_info
     * @return array
     */
    public function getTargetAudienceEstimate(array $target, $account_info)
    {
        throw new AppException('媒体当前不支持,已在排期处理');
        $body = array_merge($target, [
            'access_token' => $account_info->access_token,
            'account_id' => $account_info->account_id
        ]);
        return (new EstimationModel())->getEstimation($body);
    }

    public function updateADTaskByApi(array $input)
    {
    }

    /**
     * 获取试玩内容
     * @param $account_id
     * @param $playable_id
     * @param $platform
     * @return array
     */
    public function getPlayableContent($account_id, $playable_id, $platform)
    {
        $playable_model = new OdsToutiaoPlayableLogModel();
        $playable_content = [];
        $playable_info = $playable_model->getPlayableUrlByPlayableId($playable_id);
        if ($account_id == $playable_info->account_id) {
            if ($playable_info->status !== 'AUDIT_SUCCESS') {
                throw new AppException($playable_id . '试玩素材已推送，但审核未通过');
            }
            $playable_content = [
                'playable_id' => $playable_info->playable_id,
                'playable_url' => $playable_info->playable_url,
                'status' => 'SUCCESS',
            ];
        } else {
            $push_model = new MaterialPushMediaTaskModel();
            if ($playable_info = $push_model->getPushTaskByAccountIdAndMediaFileID(
                $playable_id, $account_id, $platform, md5_file($playable_info->playable_url))) {
                // 任务失败
                if ($playable_info->task_state == MaterialPushMediaTaskModel::TASK_STATE_FAILED) {
                    $push_model->updateById(
                        $playable_info->id, ['task_state' => MaterialPushMediaTaskModel::TASK_STATE_DELETE]); // 软删 保留任务记录
                    throw new AppException($playable_id . '试玩素材推送异常:' . $playable_info->error_msg);
                }
                // 任务成功
                $playable_content = [
                    'playable_id' => $playable_info->object_media_file_id,
                    'playable_url' => $playable_info->object_url,
                    'status' => 'SUCCESS',
                ];
                // 任务进行中
                if ($playable_info->task_state != MaterialPushMediaTaskModel::TASK_STATE_FINISH) {
                    $playable_content['status'] = 'RUNNING';
                }
            }
        }
        return $playable_content;
    }

    /**
     * 查询达人视频信息
     * @param $signature
     * @return array
     */
    public function matchingCelebrityVideo($signature)
    {
        if (!$data = (new OdsToutiaoVideoLogModel())->getAccountBySignature($signature)) {
            throw new AppException("找不到{$signature}对应达人视频信息");
        }
        return [
            'id' => $data['video_id'],
            'url' => $data['url']
        ];
    }

    /**
     * 获取抖音达人类目
     * @param array $behaviors
     * @return array
     * @throws Exception
     */
    public function getAwemeMultiLevelCategoryList(array $behaviors)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $data = (new AwemeMultiLevelCategoryModel())->getList(
            $input['advertiser_id'],
            $input['access_token'],
            $behaviors
        )['categories'];

        return $this->awemeCategoryFormat($data);
    }

    /**
     * @param $data
     * @return mixed
     */
    private function awemeCategoryFormat($data)
    {
        foreach ($data as $key => &$value) {
            $new_value = [];
            if (isset($value['children']) && $value['children']) {
                $new_value['children'] = $this->awemeCategoryFormat($value['children']);
            }
            $new_value['value'] = ['id' => $value['id'] ?? '', 'name' => $value['value'] ?? ''];
            $new_value['label'] = $value['value'];
            $new_value['id'] = $value['id'];
            $value = $new_value;
        }
        return $data;
    }

    /**
     * @param array $behaviors
     * @param int $category_id
     * @return mixed
     * @throws Exception
     */
    public function getAwemeAccountsList(array $behaviors, int $category_id)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        return (new AwemeCategoryTopAuthorModel())->getList(
            $input['advertiser_id'],
            $input['access_token'],
            $behaviors,
            $category_id
        )['authors'];
    }

    /**
     * @param array $behaviors
     * @param $label_id
     * @return mixed
     * @throws Exception
     */
    public function getAwemeAccountsInfo(array $behaviors, $label_id)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::TOUTIAO);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $data = (new AwemeAuthorInfoModel())->getList(
            $input['advertiser_id'],
            $input['access_token'],
            $behaviors,
            $label_id
        )['list'][0]['authors'] ?? [];

        return $data ? $data : [];
    }

    /**
     * 获取头条词包ID
     * @param string $word 素材文案
     * @return array 词包id数组
     */
    public function getWordIds($word = '')
    {
        preg_match_all("/(?<={)[^}]+/", $word, $result);
        $word_ids = [];
        foreach ($result[0] as $word_packet_name) {
            $word_packet_name = trim($word_packet_name, '{}');
            $word_ids[] = ToutiaoEnum::WORD_MAP[$word_packet_name] ?? '';
        }
        return $word_ids;
    }

    /**
     * 获取试玩链接
     * @param ADTaskParam $param
     * @return string
     */
    public function getPlayableUrl(ADTaskParam $param)
    {
        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $param->other_setting;
        if ($other_setting->is_playable == 1) {
            $playable_content = $this->getPlayableContent($param->account_id, $other_setting->playable_id, $param->platform);
            if (!$playable_content) {
                throw new AppException('没有找到试玩素材记录');
            }
            return $playable_content['playable_url'];
        }
        return '';
    }

    /**
     * 获取云试玩
     * @param $account_id
     * @param $playable_id
     * @return array
     */
    public function getSupplementsDesc($account_id, $playable_id)
    {
        $supplements = (new OdsToutiaoCloudGamePlayableLog())->getDataByIDANDAccountID($playable_id, $account_id);
        if (!$supplements) {
            throw new AppException('没有找到云游戏试玩素材记录');
        }
        return [
            [
                'supplement_type' => ToutiaoEnum::CLOUD_GAME,
                'games' => [
                    [
                        'id' => $supplements->game_id,
                        'orientation' => $supplements->orientation,
                    ]
                ],
            ]
        ];
    }

    /**
     * 获取组件id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param string $type
     * @return int
     */
    public function getComponentId($param, MediaAccountInfoParam $account_param, $type = "card")
    {
        /** @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /** @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        switch ($type) {
            case 'card':
                if ($setting->promotion_card_mode != 'none') {
                    $icon_name = basename($setting->product_image_id);
                    $icon_file_info = (new MaterialFileModel())->getDataByName($icon_name);
                    if ($icon_file_info) {
                        $icon_id = $this->checkCardImageUpload($param, $setting->product_image_id, $account_param);
                        if (!$icon_id) {
                            throw new AppException('获取推广卡片图片媒体信息失败');
                        }
                    } else {
                        throw new AppException('推广卡片图片信息为空');
                    }

                    $component_id_data = (new OdsToutiaoCreativeComponentLogModel())->getComponentInfo(
                        $param->account_id,
                        'PROMOTION_CARD',
                        $icon_id,
                        $setting->call_to_action,
                        $setting->product_description,
                        $setting->enable_personal_action ? 1 : 0,
                        $setting->product_selling_points
                    );

                    if ($component_id_data) {
                        return (int)$component_id_data->component_id;
                    } else {
                        $icon_create_result = (new CreativeComponentModel())->create(
                            $param->account_id,
                            $account_param->access_token,
                            'PROMOTION_CARD',
                            $setting->product_description,
                            [
                                'image_id' => $icon_id,
                                'title' => $setting->product_description,
                                'button_text' => $setting->call_to_action,
                                'product_selling_points' => $setting->product_selling_points,
                                'enable_personal_action' => $setting->enable_personal_action ? 1 : 0,

                            ]
                        );
                        (new ToutiaoTaskModel())->setAsync(false)->creativeComponent($param->account_id);
                        return $icon_create_result['component_id'];
                    }
                } else {
                    return 0;
                }
            case 'game_subscribe':
                if ($other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE') {

                    if ($other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_FORM' ||
                        $other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE'
                    ) {
                        $app_thumbnails_ids = $this->uploadAppPic(
                            $param,
                            $account_param->access_token,
                            $other_setting->app_thumbnails
                        );
                    }

                    if ($setting->promotion_card_mode != 'none') {
                        $icon_name = basename($setting->product_image_id);
                        $icon_file_info = (new MaterialFileModel())->getDataByName($icon_name);
                        if ($icon_file_info) {
                            $icon_id = $this->checkCardImageUpload($param, $setting->product_image_id, $account_param);
                            if (!$icon_id) {
                                throw new AppException('获取推广卡片图片媒体信息失败');
                            }
                        } else {
                            throw new AppException('推广卡片图片信息为空');
                        }

                        $download_url_array = explode('?', $param->getSiteConfig()->download_url);
                        $subscribe_url = str_replace("itunes.apple.com", "apps.apple.com", $download_url_array[0]);

                        $icon_create_result = (new CreativeComponentModel())->create(
                            $param->account_id,
                            $account_param->access_token,
                            'RESERVATION_BUTTON',
                            '游戏预约' . time(),
                            [
                                'app_logo' => $icon_id,
                                'app_desc' => $other_setting->app_desc,
                                'app_introduction' => $other_setting->app_introduction,
                                'app_thumbnails' => $app_thumbnails_ids ?? [],
                                'app_name' => $other_setting->app_name ? $other_setting->app_name : ($other_setting->source ?? $param->getSiteConfig()->game_name),
                                'package_name' => $param->getSiteConfig()->package,
                                'download_url' => $subscribe_url,

                            ]
                        );
                        (new ToutiaoTaskModel())->setAsync(false)->creativeComponent($param->account_id);
                        return $icon_create_result['component_id'];
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }

            default:
                return 0;
        }
    }

    /**
     * 获取广告二级开关
     * @param ADTaskParam $param
     * @return string
     */
    public function getOperation(ADTaskParam $param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        if (in_array($param->getSiteConfig()->game_pack, [0, 2])) {
            return $setting->ad_operation;
        }
        if ($param->getSiteConfig()->game_type == '安卓') {
            $ads_platform = PlatformAD::create($param->platform);
            try {
                $apk_list = $ads_platform->getAPKStateList([$param->site_id], $param->creator);
                $http_status = (int)(($apk_list[0]['state']) ?? 0);
            } catch (Throwable $e) {
                if ($e->getMessage() != '无返回信息') {
                    throw new AppException($e->getMessage(), $e->getCode());
                }
                $http_status = 0;
            }

            if ($http_status != 1) {
                $operation = ToutiaoEnum::OPERATION_DISABLE;
            }
            (new ADTaskModel())->setTaskCheckPackageCode($param->id, 'ad2_check_package_code', $http_status);
        } else {
            $download_url = str_replace('itunes.apple.com', 'apps.apple.com', $param->getSiteConfig()->download_url);
            $http_status = Helpers::getHttpStatus($download_url, ['proxy' => true]);
            if (!(200 <= $http_status && $http_status < 400)) {
                $operation = ToutiaoEnum::OPERATION_DISABLE;
            }
            (new ADTaskModel())->setTaskCheckPackageCode($param->id, 'ad2_check_package_code', $http_status);
        }

        return $operation ?? $setting->ad_operation;
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_LIVE, ToutiaoEnum::PROMOTION_TYPE_GAME]) && ($other_setting->ies_map[$param->account_id] ?? false)) {
            $site_ext = $param->site_config['ext'];
            $site_ext['aweme_account'] = $other_setting->ies_map[$param->account_id];
            $param->setSiteConfigProp('ext', $site_ext);
        }

        if ($param->site_config['convert_source_type'] == ConvertSourceType::H5_API) {

            $site_ext = $param->site_config['ext'];
            $site_ext['external_url'] = $other_setting->getPageExternalUrlMapInfo($account_param->account_id);
            $param->setSiteConfigProp('ext', $site_ext);
        }
    }

    /**
     * @inheritDoc
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
    }

    /**
     * @inheritDoc
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        /** @var ADSettingContentParam $setting */
        $setting = $param->setting;

        // 媒体指定位置 -> 火上信息流已下架 去除 INVENTORY_HOTSOON_FEED
        $setting->inventory_type = array_filter($setting->inventory_type, function ($element) {
            return !in_array($element, ['INVENTORY_HOTSOON_FEED']);
        });
        $setting->inventory_type = array_values($setting->inventory_type);

        $param->setting = $setting;

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告二级
        if (!$param->ad2_id) {
            try {
                $param->ad2_id = $this->createAD2($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);

            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 推送试玩素材
        $this->pushPlayableMaterial($param, $account_param);
        // 开启一键起量
        $this->makeADRaise($param, $account_param);
        // 创建关键词
        $this->createKeywords($param, $account_param);

        // 新建广告三级
        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
    }

    /**
     * @inheritDoc
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // 推送流量包
        $all_flow_id_md5_list = $param->targeting->getAllFlowIdMd5MapList();
        if ($all_flow_id_md5_list) {
            $flow_md5_list = array_keys($all_flow_id_md5_list);
            $already_exist_list_data = $this->getAlreadyExistFlowMd5List(
                $flow_md5_list,
                $param->account_id
            );
            $need_push_flow_list_data = [];
            $reset_flow_map = [];
            if ($already_exist_list_data) {
                foreach ($all_flow_id_md5_list as $md5 => $id) {
                    if (isset($already_exist_list_data[$md5])) {
                        $already_exist_list_data_array = (array)$already_exist_list_data[$md5];
                        $already_exist_list_data_array['md5'] = $md5; // 头条前端用的是md5字段
                        $reset_flow_map[$md5] = $already_exist_list_data_array;
                    } else {
                        $all_flow_id_md5_list[$md5]['md5'] = $md5;
                        $need_push_flow_list_data[] = $all_flow_id_md5_list[$md5];
                    }
                }
            }

            if ($need_push_flow_list_data) {
                foreach ($need_push_flow_list_data as $key => $flow_rit_id_list) {
                    try {
                        $flow_package_id_data = $this->uploadFlow(
                        // 有这个流量包的account_id
                            $account_param->account_id,
                            // 有这个流量包的account_id的access_token
                            $account_param->access_token,
                            $flow_rit_id_list
                        );
                    } catch (Throwable $e) {
                        throw new AppException("上传流量包{$flow_rit_id_list['name']}-{$flow_rit_id_list['md5']}错误:" . $e->getMessage());
                    }
                    // 上传成功后记录
                    if ($flow_package_id = $this->getFlowPackageIdByRequest($flow_package_id_data)) {
                        $reset_flow_map = array_merge($reset_flow_map, $this->getSuccessFlowMap($flow_package_id, $flow_rit_id_list));
                    }
                }
                $this->syncFlowPackage($param->account_id);
            }
            $param->targeting->resetFlowPackage($reset_flow_map);
        }
    }

    /**
     * @inheritDoc
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $all_audience_id_list = $param->targeting->getAllAudienceIdList();
        if ($all_audience_id_list) {
            $need_push_audience_list_data = $this->getNeedUploadAudienceList(
                $all_audience_id_list,
                $param->account_id,
                $param->platform,
                $account_param->toutiao_majordomo_id
            );
            if ($need_push_audience_list_data->isNotEmpty()) {
                $audience_account_id_list = $need_push_audience_list_data->pluck('account_id')->toArray();
                if ($audience_account_id_list) {
                    $this->pushAudience(
                        $audience_account_id_list,
                        $need_push_audience_list_data->toArray(),
                        [$param->account_id]
                    );
                } else {
                    throw new AppException("人群包从未上传过");
                }
            }
        }
    }

    /**
     * @inheritDoc
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $toutiao_other_setting */
        $toutiao_other_setting = $param->other_setting;

        // 游戏礼包计划类型的安卓的，需要走解析包链接
        if (
            $param->media_agent_type == BatchAD::MEDIA_AGENT_TYPE_DEFAULT &&
            $toutiao_other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_PACKAGE' &&
            $toutiao_other_setting->game_package_desc &&
            $toutiao_other_setting->advanced_creative_type &&
            $toutiao_other_setting->game_package_thumbnails &&
            $param->getSiteConfig()->game_type === "安卓"
        ) {
            $data = array_merge($param->toArray(), ['access_token' => $account_param->access_token]);
            (new SiteMQLogic)->produceTouTiaoCheckPackageTask($data, 1);
            throw new AppException('等待打包1', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }

//        if ($param->getSiteConfig()->game_type === "安卓" && $param->getSiteConfig()->game_pack == 2) {
//            $data = array_merge($param->toArray(), ['access_token' => $account_param->access_token]);
//            if ($toutiao_other_setting->promotion_type != 'APP') {
//                $data['download_url'] = '';
//            }
//            (new SiteMQLogic)->produceTouTiaoCheckPackageTask($data, 1);
//            throw new AppException('等待打包2', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
//        }

        // 游戏预约类型的，需要走解析包链接
        if (
            isset($toutiao_other_setting->advanced_creative_type) &&
            (
                $toutiao_other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_FORM' ||
                $toutiao_other_setting->advanced_creative_type == 'ATTACHED_CREATIVE_GAME_SUBSCRIBE'
            ) &&
            $toutiao_other_setting->advanced_creative_type
        ) {
            $data = array_merge($param->toArray(), ['access_token' => $account_param->access_token]);
            (new SiteMQLogic)->produceTouTiaoCheckPackageTask($data, 1);
            throw new AppException('等待打包3', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }

        // 如果是需要等待打包的任务，需要走解析包链接
        if ($param->is_wait_package == 1 && $param->getSiteConfig()->game_type == "安卓") {
            $data = array_merge($param->toArray(), ['access_token' => $account_param->access_token]);
            (new SiteMQLogic)->produceTouTiaoCheckPackageTask($data, 1);
            throw new AppException('等待打包4', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }
    }

    /**
     * @inheritDoc
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::TOUTIAO, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        $result = [];
        foreach ($material_file_list as $material_file) {
            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::TOUTIAO;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;
            if ($upload_info_list[$material_file->id] ?? '') {
                $result[$material_file->id] = [
                    'id' => $upload_info_list[$material_file->id]->media_material_file_id,
                    'url' => $upload_info_list[$material_file->id]->url,
                    'signature' => $material_file->signature,
                ];
            } else {
                if (in_array(
                    $material_file->file_type,
                    [MaterialFileModel::FILE_TYPE_IMAGE, MaterialFileModel::FILE_TYPE_COVER, MaterialFileModel::FILE_TYPE_ICON])
                ) {
                    $media_file_info = $this->uploadImage($material_file, $account_param->account_id, $account_param->access_token);
                    $insert['media_material_file_id'] = $media_file_info['id'];
                    $insert['url'] = $media_file_info['url'];
                    $result[$material_file->id] = [
                        'id' => $media_file_info['id'],
                        'signature' => $material_file->signature,
                        'url' => $media_file_info['url']
                    ];
                } else if ($material_file->file_type == MaterialFileModel::FILE_TYPE_AUDIO) {
                    $media_file_info = $this->uploadAudio($material_file, $account_param->account_id, $account_param->access_token);
                    $insert['media_material_file_id'] = $media_file_info['id'];
                    $insert['url'] = $media_file_info['url'];
                    $result[$material_file->id] = [
                        'id' => $media_file_info['id'],
                        'signature' => $material_file->signature,
                        'url' => $media_file_info['url']
                    ];
                } else {
                    $media_file_info = $this->uploadVideo($material_file, $account_param->account_id, $account_param->access_token);
                    $insert['media_material_file_id'] = $media_file_info['id'];
                    $insert['url'] = $media_file_info['url'];
                    $result[$material_file->id] = [
                        'id' => $media_file_info['id'],
                        'signature' => $material_file->signature,
                        'url' => $media_file_info['url']
                    ];
                }
                if ($media_file_info['id']) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * @inheritDoc
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        $restart_like_msg_list = [
            'Too many requests',
            'too many requests',
            '系统繁忙，请稍后再试',
            '请稍后再试',
            '请稍后刷新重试',
            '获取产品卖点ID失败',
            'internal error',
            'internal service error',
            'Internal service timed out',
            '请勿重复提交',
            '获取应用包数据失败',
//            '请前往应用管理中心上传并发布应用',
            '502 Bad Gateway',
            '504 Gateway Time-out',
            '该广告暂不支持搜索快投关键词',
            'upstream connect error or disconnect',
            '只有应用资产需要传递下载链接',   // 创建资产后，媒体接口延迟，创建监测链接组会报错
            '下载链接的包名与当前资产的包名不符，请填写当前资产的下载链接', // 应用分包创建发布中 此时会报错
            '资产不存在',    // 创建资产后，媒体接口延迟，创建事件会报错
            '包名已经存在，请不要重复创建同包名的资产',
            '请稍后重试',
            'System timeout',
            '应用类型必填',
            '同名项目正在创建中',
        ];

        if ($task_param->getSiteConfig()->game_type == 'IOS') {
            $restart_like_msg_list[] = '应用版本更新审核中';
        }

        foreach ($restart_like_msg_list as $msg) {
            if (strpos($task_param->error_msg, $msg) !== false) {
                return true;
            }
        }
        return false;
    }

    function prepareCreativeList(ADTaskParam $task_param)
    {
    }

    /**
     * 推送试玩素材
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function pushPlayableMaterial(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if ((int)$other_setting->is_playable === 1 && $other_setting->playable_id) {
            $playable_model = new OdsToutiaoPlayableLogModel();
            if ($playable_content = $this->getPlayableContent($account_param->account_id, $other_setting->playable_id, $param->platform)) {
                if ($playable_content['status'] != 'SUCCESS') {
                    throw new AppException('试玩素材推送未完成，任务自动重启', ADTaskDigestLogic::RESTART_TASK_CODE);
                }
            } else {
                // 执行推送任务
                $wait_push_data = $playable_model->getPlayableUrlByPlayableId($other_setting->playable_id);
                $data = [
                    'platform' => $param->platform,
                    'media_type' => MediaType::TOUTIAO,
                    'origin_account_id' => $wait_push_data->account_id,
                    'object_account_id' => $account_param->account_id,
                    'file_type' => 8,
                    'file_id' => 0,
                    'signature' => md5_file($wait_push_data->playable_url),
                    'filename' => $wait_push_data->playable_name,
                    'origin_media_file_id' => $wait_push_data->playable_id,
                    'origin_url' => $wait_push_data->playable_url,
                    'create_time' => time(),
                    'creator' => 'system',
                    'task_state' => MaterialPushMediaTaskModel::TASK_STATE_NO_START
                ];
                $id = (new MaterialPushMediaTaskModel())->add(new MaterialPushMediaTaskParam($data));
                if ($id) {
                    //添加到消息队列
                    $data['id'] = $id;
                    (new MaterialPushMQLogic())->produceTask((new MaterialPushMediaTaskParam($data)));
                }
                throw new AppException('试玩素材开始推送，任务自动重启', ADTaskDigestLogic::RESTART_TASK_CODE);
            }
        }
    }

    /**
     * 开启一键起量
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function makeADRaise(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        if ((int)$setting->raise_status === 1) {
            (new AdRaiseModel())->set(
                $account_param->account_id, $account_param->access_token, $param->ad2_id, 'CLICK_RAISE', $setting->modify_value
            );
        }
    }

    /**
     * 创建关键词
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function createKeywords(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        $ad2_id = $param->ad2_id;

        if (
            $targeting->search_keyword_status == 1 &&
            $targeting->search_keyword_type == 'feed_delivery_search' &&
            !in_array($setting->delivery_range, [ToutiaoEnum::DELIVERY_RANGE_UNION])
        ) {
            $keyword_list = array_map(function ($data) {
                $data['bid_type'] = 'FEED_TO_SEARCH';
                return $data;
            }, $targeting->feed_search_keyword_list);
            array_map(function ($data) use ($account_param, $ad2_id) {
                (new KeywordModel())->createV2ForAD($account_param->account_id, $account_param->access_token, $ad2_id, $data);
            }, array_chunk($keyword_list, 100));
        }
    }

    /**
     * 获取渠道组
     * @param ADTaskParam $param
     * @return int
     */
    public function getAgentGroup(ADTaskParam $param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        if ($param->platform == 'GR' && $other_setting->app_promotion_type == 'RESERVE') {
            return 340;
        }

        if (in_array($other_setting->promotion_type, [ToutiaoEnum::PROMOTION_TYPE_APP, ToutiaoEnum::PROMOTION_TYPE_LINK])) {
            $agent_group = AgentGroup::TOUTIAO;
            if ($param->other_setting->origin_ad_type == 'origin') {
                return AgentGroup::TOUTIAO_ORIGIN;
            }

            if ($setting->delivery_range == 'UNION') {
                return AgentGroup::TOUTIAO_UNION;
            }
            if ($param->site_config['plat_id'] == PlatId::DY_MINI) {
                return AgentGroup::DOUYIN_MINI_GAME;
            }
            if ($setting->delivery_range == 'DEFAULT') {
                foreach ($setting->inventory_type as $key => $value) {
                    if (in_array($value, ['INVENTORY_UNION_SLOT', 'UNION_BOUTIQUE_GAME', 'INVENTORY_UNION_SPLASH_SLOT'])) {
                        return AgentGroup::TOUTIAO_UNION;
                    }
                }
            }
        } elseif ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_LIVE) {
            $agent_group = AgentGroup::TOUTIAO_LIVE;
        } elseif ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_GAME) {
            $agent_group = AgentGroup::TOUTIAO_UOP;
        } elseif ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT) {
            $agent_group = AgentGroup::TOUTIAO_HOT;
        } else {
            if ($param->other_setting->origin_ad_type == 'origin') {
                return AgentGroup::TOUTIAO_ORIGIN;
            }
            $agent_group = AgentGroup::TOUTIAO;
        }

        return $agent_group;
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaStatus()
    {
        $list = (new OdsToutiaoAdLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::EN_TO_CN[$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 获取事件管理的优化目标（事件管理）暂无使用
     * @param EventOptimizedGoalParam $param
     * @return array
     */
    public function getOptimizedGoal(EventOptimizedGoalParam $param)
    {
        return (new EventConvertModel())->getOptimizedGoal($param);
    }

    /**
     * 获取事件管理的优化目标（橙子建站）
     * @param EventOptimizedGoalParam $param
     * @return array
     */
    public function getTetrisOptimizedGoal(EventOptimizedGoalParam $param)
    {
        return (new EventConvertModel())->getTetrisOptimizedGoal($param);
    }

    /*
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        $list = (new OdsToutiaoAdLogModel())->getInventoryList($condition);
        if ($list->isEmpty()) {
            return $list;
        }
        return $list->map(function ($item) use ($keyword) {
            $inventory_type = json_decode($item->inventory_type, true);
            $inventory_type_cn = [];
            foreach ($inventory_type as $value) {
                //手动把数据库中奇怪的null过滤掉-.-||
                if (is_null($value)) {
                    return false;
                }
                $inventory_type_cn[] = ADFieldsENToCNMap::INVENTORY_TO_CN[$value] ?? $value;
            }
            unset($value);
            $label = implode(',', $inventory_type_cn);

            if ('' === $keyword || ('' !== $keyword && false !== strpos($label, $keyword))) {
                return [
                    'label' => $label,
                    'value' => $item->inventory_type,
                ];
            } else {
                return false;
            }
        })->filter()->values();
    }

    /**
     * @param $data
     * @return array
     */
    public function getHotMaterialFileList($data): array
    {
        $res = (new V2DimAgentLeaderGroupModel())->getDataByLeaders([Container::getSession()->name]);
        if ($res->isNotEmpty()) {
            return (new OdsToutiaoAwemeAuthDetailLogModel())->getListByCondition(
                $data['page'] ?? 1,
                $data['rows'] ?? 20,
                $data,
                explode('-', $res[0]->agent_leader_group_name)[0] ?? ''
            );
        } else {
            return (new OdsToutiaoAwemeAuthDetailLogModel())->getListByCondition(
                $data['page'] ?? 1,
                $data['rows'] ?? 20,
                $data,
                ''
            );
        }
    }

    public function removeEmoji($text)
    {
        $pattern = '/[\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}]/u';
        return preg_replace($pattern, '', $text);
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        /**
         * @var ADOtherSettingContentParam $other_setting
         */
        $other_setting = $task_param->other_setting;
        if ($other_setting->promotion_type == ToutiaoEnum::PROMOTION_TYPE_HOT) {
            $first_hot_material_info = $other_setting->hot_material_map[$task_param->account_id][0];
            switch ($name_norms) {
                case 'material_author':
                    // 去除字符串中的 Emoji
                    return $this->removeEmoji($first_hot_material_info['aweme_name']);

                case 'material_size':
                    if ($first_hot_material_info['image_mode'] == 'CREATIVE_IMAGE_MODE_VIDEO_VERTICAL') {
                        return '竖版';
                    } else {
                        return '横版';
                    }
                case 'material_name':
                    $str = "{$first_hot_material_info['aweme_id']}-{$first_hot_material_info['aweme_name']}";
                    return $this->removeEmoji($str);
                case 'material_id':
                    return mb_substr($first_hot_material_info['title'] ?? '', 0, 5);
                case 'material_file_name':
                    $time = date('m-d', strtotime($first_hot_material_info['start_time']));
                    $str = "{$first_hot_material_info['aweme_id']}-{$first_hot_material_info['aweme_name']}-$time";
                    return $this->removeEmoji($str);
            }
        }

        return '';
    }

    /**
     * 创建资产
     * @param AssetCreateParam $param
     * @return array
     */
    public function createAsset(AssetCreateParam $param)
    {
        return (new AssetModel())->create($param);
    }

    /**
     * 资产下创建事件
     * @param EventCreateParam $param
     * @return array
     */
    public function createEvent(EventCreateParam $param)
    {
        return (new EventModel())->create($param);
    }

    /**
     * 事件资产下创建监测链接组
     * @param TrackUrlGroupCreateParam $param
     * @return array
     */
    public function createTrackUrlGroup(TrackUrlGroupCreateParam $param)
    {
        return (new TrackUrlModel())->create($param);
    }

    /**
     * 获取已创建资产列表
     * @param $account_id
     * @param $access_token
     * @param $filtering
     * @param int $page
     * @param int $page_size
     * @return array
     */
    public function getAssetList($account_id, $access_token, $filtering, $page = 1, $page_size = 10)
    {
        return (new ToolsEventModel())->getAllAssetList($account_id, $access_token, $filtering, $page, $page_size);
    }

    /**
     * 获取已创建资产列表
     * @param $account_id
     * @param $access_token
     * @param $asset_ids
     * @return array
     */
    public function getAssetDetail($account_id, $access_token, $asset_ids)
    {
        return (new ToolsEventModel())->getAssetDetails($account_id, $access_token, $asset_ids);
    }

    /**
     * 获取资产下已创建事件列表
     * @param $account_id
     * @param $access_token
     * @param $asset_id
     * @return array
     */
    public function getEventConfigsList($account_id, $access_token, $asset_id)
    {
        return (new EventModel())->getEventConfigsList($account_id, $access_token, $asset_id);
    }

    /**
     * 创建起量任务
     * @param $data
     * @param $format_data
     * @param $account_ids
     * @return array
     */
    public function createRaiseTask($data, $format_data, $account_ids)
    {
        $toutiao_model = new OdsToutiaoAccountLogModel();
        $column = ['account_id', 'account_status_version_one', 'account_status_version_two', 'platform'];
        $account_info = $toutiao_model->getAccountInfoInAccountIds($format_data, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('创建起量任务失败');
        }

        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, MediaType::TOUTIAO, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new TaskRaiseModel();
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CREATE_RAISE_TASK;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //所有成功的账号
        $success_account_ids = [];
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['ad_id'] = 0;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            try {
//                if ('ENABLERAISE' !== $v->account_status_version_one) {
//                    throw new AppException("当前账号的起量状态不允许创建起量任务");
//                }
                //向头条发起请求
                $toutiao_http_model->createRaiseTask(
                    (int)$v->account_id,
                    $data['budget_mode'],
                    $access_tokens[$v->account_id],
                    $data["platform_version"],
                    'NO_LIMIT' === $data['allocated_budget'] ? 0 : $data['allocated_budget'],
                    $data['end_time'] ?? ''
                );
                $success_account_ids[] = (int)$v->account_id;
                //成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = 0;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = "创建成功";

            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '创建起量任务失败，错误信息：' . json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = '';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = 0;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
        //推送给大数据刷新账号
        if ($success_account_ids) {
            (new ToutiaoTaskModel())->raiseTask($success_account_ids);
        }
        return $return_data;
    }

    /**
     * 停止起量任务
     * @param $data
     * @param $account_ids
     * @return string
     */
    public function stopRaiseTask($data, $account_ids)
    {
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, MediaType::TOUTIAO, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new TaskRaiseModel();
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::STOP_RAISE_TASK;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //所有成功的账号ID
        $success_account_ids = [];
        //返回的信息
        $return_msg = '';
        $update_data = $data['update_data'][0];

        if (!isset($access_tokens[$update_data['account_id']])) {
            $single_record_operate['platform'] = $update_data['platform'];
            $single_record_operate['account_id'] = (int)$update_data['account_id'];
            $single_record_operate['ad_id'] = 0;
            $single_record_operate['ad_name'] = '';
            $single_record_operate['status'] = 2;
            $single_record_operate['edit_detail'] = '当前用户无操作权限';

            (new ADAnalysisOperateLogModel())->add($single_record_operate);
            return '当前用户无操作权限';
        }

        try {
//            if ('RAISING' !== $update_data['status']) {
//                throw new AppException("当前账号此任务ID不处于起量中, 不可停止起量");
//            }
            //发起请求
            $toutiao_http_model->stopRaiseTask(
                (int)$update_data['account_id'],
                (int)$update_data['report_id'],
                $access_tokens[$update_data['account_id']]
            );
            $success_account_ids[] = (int)$update_data['account_id'];
            //修改成功
            $single_record_operate['ad_name'] = '';
            $single_record_operate['status'] = 1;
            $single_record_operate['edit_detail'] = "停止成功";
            $single_record_operate['account_id'] = (int)$update_data['account_id'];
            $single_record_operate['platform'] = $update_data['platform'];
            $single_record_operate['ad_id'] = 0;

        } catch (AppException $e) {
            $response['message'] = $e->getMessage();
            $response['code'] = $e->getCode();
            $single_record_operate['edit_detail'] = '停止起量任务失败，错误信息：' . json_encode($response, JSON_UNESCAPED_UNICODE);
            $single_record_operate['ad_name'] = '';
            $single_record_operate['status'] = 2;
            $single_record_operate['account_id'] = (int)$update_data['account_id'];
            $single_record_operate['platform'] = $update_data['platform'];
            $single_record_operate['ad_id'] = 0;
            $return_msg = $e->getMessage();
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($single_record_operate);

        if ($success_account_ids) {
            (new ToutiaoTaskModel())->raiseTask($success_account_ids);
        }

        return $return_msg;
    }

    /**
     * 获取转账待选集合
     * @param array $data
     * @param $agent_permission
     * @return array
     */
    public function getTransferSet(array $data)
    {
        //计算每个account_id下有几条关联记录
        $count_related_record = array_count_values($data['account_ids']);

        $account_ids = array_keys($count_related_record);
        //注入权限获取access_token
        $agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
        $account_info = (new OdsToutiaoAccountLogModel())->getAccountInfosInAccountIds($account_ids, $agent_permission);
        $account_info_ids = $account_info->pluck('account_id')->toArray();

        if ($no_permission = array_diff($account_ids, $account_info_ids)) {
            throw new AppException('账号ID：' . json_encode(array_values($no_permission)) . '无操作权限');
        }

        $account_set = [];
        foreach ($account_info as $item) {
            $item->related_record = $count_related_record[$item->account_id];
            $transferable_key = $item->transferable_key;
            unset($item->transferable_key);
            $account_set[$transferable_key][] = (array)$item;
        }

        return $account_set;
    }

    /**
     * 根据transferable_key获取可转账号列表
     * @param $data
     * @return Collection|mixed
     */
    public function getAccountIdListByTransferableKey($data)
    {
        $agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
        return (new OdsToutiaoAccountLogModel())->getAccountIdListByTransferableKey(
            $agent_permission,
            [$data['transferable_key']],
            $data['account_ids'],
            $data['keyword']
        );
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if ($param->getSiteConfig()->convert_toolkit == ConvertToolkit::TOUTIAO_ASSET) {
            if (empty($param->getSiteConfig()->ext['asset_ids'] ?? '')) {
                return true;
            } else {
                return false;
            }
        } else {
            if (!$param->convert_id) {
                return true;
            } else {
                return false;
            }
        }

    }

    /**
     * 刷新账号余额信息
     * @param $data
     */
    public function refreshAccountBalance($data)
    {
        $task_model = new ToutiaoTaskModel();
        $task_model->setAsync(false);
        $task_model->account(array_unique($data['account_ids']));
    }

    /**
     * 获取字节小游戏ID
     * @param $platform
     * @param $media_type
     * @param $game_id
     * @return array
     */
    public function getByteGameInfo($platform, $media_type, $game_id)
    {
        $sdk_info = (new OdsMediaSDKModel())->getDataByGame($platform, $media_type, $game_id);
        if (empty($sdk_info)) {
            throw new AppException("请到SDK管理配置相应的游戏参数");
        }

        $ext = json_decode($sdk_info->ext, true);
        if (empty($ext['byte_game_id'])) {
            throw new AppException("请到SDK管理配置相应的游戏参数-头条小游戏ID");
        }
        if (empty($ext['micro_game_instance_id'] ?? '')) {
            throw new AppException("请到SDK管理重新配置头条小游戏参数");
        }

        return [
            'byte_game_id' => $ext['byte_game_id'],
            'micro_game_instance_id' => $ext['micro_game_instance_id'],
            'byte_game_url' => $ext['byte_game_url'] ?? ""
        ];
    }

    /**
     * 获取微信小游戏原始ID
     * @param $platform
     * @param $media_type
     * @param $game_id
     * @return array
     */
    public function getWechatGameInfo($platform, $media_type, $game_id)
    {
        $sdk_info = (new OdsMediaSDKModel())->getDataByGame($platform, $media_type, $game_id);
        if (empty($sdk_info)) {
            throw new AppException("请到SDK管理配置相应的游戏参数-小程序原始ID");
        }

        $ext = json_decode($sdk_info->ext, true);
        if (empty($ext['mini_game_original_id'])) {
            throw new AppException("请到SDK管理配置相应的游戏参数-小程序原始ID.");
        }

        return [
            'mini_game_original_id' => $ext['mini_game_original_id'],
        ];
    }

    /**
     * 获取字节小游戏 instance_id
     * @param $account_id
     * @param $access_token
     * @param $appid
     * @param $search_type
     * @return mixed|string
     */
    public function getMicroGameInstanceId($account_id, $access_token, $appid, $search_type)
    {
        $instance_id = '';
        $data = (new MicroGameModel())->getMicroGameList($account_id, $access_token, $search_type);
        foreach ($data['list'] as $item) {
            if ($item['app_id'] == $appid) {
                $instance_id = $item['instance_id'];
                break;
            }
        }

        return $instance_id;
    }

    /**
     * 获取微信小游戏 instance_id
     * @param $account_id
     * @param $account_type
     * @param $access_token
     * @param $mini_game_original_id
     * @param $search_type
     * @return mixed|string
     */
    public function getWechatGameInstanceId($account_id, $account_type, $access_token, $mini_game_original_id, $search_type)
    {
        $instance_id = '';
        $data = (new WechatGameModel())->getWechatGameList($account_id, $account_type, $access_token, $search_type);
        foreach ($data['list'] as $item) {
            if ($item['user_name'] == $mini_game_original_id) {
                $instance_id = $item['instance_id'];
                break;
            }
        }

        return $instance_id;
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
    }

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassScheduleTime() method.
    }

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateSecondClassScheduleTime() method.
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status()
    {
        $list = (new OdsToutiaoCreativeLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::EN_TO_CN[$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 广告一键起量
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisADRaise(array $data) {
        // TODO: Implement updateADAnalysisADRaise() method.
    }

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}

