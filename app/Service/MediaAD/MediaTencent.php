<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: 张中昊
 * Date: 2020/02/25
 * Time: 10:44
 */

namespace App\Service\MediaAD;

use App\Constant\ActionTrackType;
use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ADFieldsENToCNMap;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Logic\DSP\ADAnalysisMQLogic;
use App\Logic\DSP\ADServingLogic;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\ADTaskMasterMQLogic;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\Tencent\AdcreativeTemplate\AdcreativeTemplateModel;
use App\Model\HttpModel\Tencent\AdGroup\AdGroupModel;
use App\Model\HttpModel\Tencent\Ads\AdsModel;
use App\Model\HttpModel\Tencent\Advertiser\AdvertiserModel;
use App\Model\HttpModel\Tencent\AndroidUnionChannelPackages\AndroidUnionChannelPackagesModel;
use App\Model\HttpModel\Tencent\BidWord\BidWordModel;
use App\Model\HttpModel\Tencent\Campaign\CampaignModel;
use App\Model\HttpModel\Tencent\Conversions\ConversionsModel;
use App\Model\HttpModel\Tencent\V3\Conversions\ConversionsModel as V3ConversionsModel;
use App\Model\HttpModel\Tencent\Creative\CreativeModel;
use App\Model\HttpModel\Tencent\CustomAudienceGrantRelations\CustomAudienceGrantModel;
use App\Model\HttpModel\Tencent\DynamicCreatives\DynamicCreativeModel;
use App\Model\HttpModel\Tencent\Estimation\EstimationModel;
use App\Model\HttpModel\Tencent\ExtendPackage\ExtendPackageModel;
use App\Model\HttpModel\Tencent\Images\ImagesModel;
use App\Model\HttpModel\Tencent\MergeFundTypeSubcustomerTransfer\MergeFundTypeSubcustomerTransferModel;
use App\Model\HttpModel\Tencent\Programmed\ProgrammedModel;
use App\Model\HttpModel\Tencent\PromotedObjects\PromotedObjectsModel;
use App\Model\HttpModel\Tencent\RTA\TargetModel;
use App\Model\HttpModel\Tencent\TargetingTags\TargetingTagsModel;
use App\Model\HttpModel\Tencent\UnionPositionPackages\UnionPositionPackagesModel;
use App\Model\HttpModel\Tencent\Videos\VideosModel;
use App\Model\HttpModel\TrinoTask\GDTTaskModel;
use App\Model\RedisModel\ADTaskMaterialModel;
use App\Model\RedisModel\ADTaskTencentFilterModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\DataMedia\OdsADAnalysisTencentRTATargetBindLogModel;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentCampaignLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentCustomAudienceLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentRTATargetBindListLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentTransferOperateLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentUnionPositionPackagesLogModel;
use App\Model\SqlModel\DataMedia\OdsWeChatAdGroupHisLogModel;
use App\Model\SqlModel\DataMedia\OdsWeChatAdGroupLogModel;
use App\Model\SqlModel\DataMedia\OdsWechatCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsWeChatCampaignLogModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADAnalysisTencentTransferParam;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADComposeContent\ADComposeConfigParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeImageParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeListParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoCoverParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoParam;
use App\Param\ADServing\ADComposePacketParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Tencent\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\Tencent\Basics\ADSettingContentParam;
use App\Param\ADServing\Tencent\ADTargetingContentParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Param\SiteConfigParam;
use App\Param\Tencent\AdCreateParam;
use App\Param\Tencent\AdCreativeCreateParam;
use App\Param\Tencent\AdDynamicCreativeCreateParam;
use App\Param\Tencent\AdGroupCreateParam;
use App\Param\Tencent\CampaignCreateParam;
use App\Param\Tencent\ProgrammedAddParam;
use App\Param\Tencent\V3\CreativeTemplateParam;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Image;
use App\Utils\Math;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use RedisException;
use Throwable;

class MediaTencent extends AbstractMedia
{

    /**
     * 已创建好的广告组列表
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        return [];
    }

    /**
     * 已创建好的广告组列表
     * @param ADTaskParam $param
     * @param $account_id
     * @param $access_token
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, $account_id, $access_token)
    {
        $filters[] = [
            'field' => 'campaign_name',
            'operator' => 'EQUALS',
            'values' => [$param->ad1_name_text]
        ];
        $result = (new CampaignModel())->info(
            $account_id,
            $access_token,
            $filters,
            false,
            ['campaign_id'],
            1,
            1
        );
        if (count($result['list']) > 0) {
            $campaign_id = $result['list'][0]['campaign_id'] ?? 0;
        } else {
            $campaign_id = 0;
        }
        return $campaign_id;
    }


    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        return [];
    }

    /**
     * 获取广告计划可用的定向包列表
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionCategoryList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionKeywordList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取兴趣类目
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取兴趣关键词
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        return [];
    }

    /**
     * 上传图片到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $img_path = $param->file_type == 3 ? EnvConfig::MATERIAL_VIDEO_DIR_NAME : EnvConfig::MATERIAL_IMG_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $image_model = new ImagesModel();
            $file = curl_file_create("$upload_path/$img_path/$platform/$material_id/$file_name");
            $material = (new MaterialModel())->get($material_id, $platform);
            $description = ($material ? $material->name : '') . $param->width . 'x' . $param->height;
            $signature = md5_file("$upload_path/$img_path/$platform/$material_id/$file_name");
            $result = $image_model->addImage($advertiser_id, $access_token, $file, $signature, $description);
            return [
                'id' => $result['image_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传icon到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadIcon(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $img_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $image_model = new ImagesModel();
            $file = curl_file_create("$upload_path/$img_path/$platform/$material_id/$file_name");
            $material = (new MaterialModel())->get($material_id, $platform);
            $description = str_replace('&', '', ($material ? $material->name : '') . $param->width . 'x' . $param->height);
            $result = $image_model->addImage($advertiser_id, $access_token, $file, $param->signature, $description);
            return [
                'id' => $result['image_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传视频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $video_model = new VideosModel();
            $file = curl_file_create("$upload_path/$video_path/$platform/$material_id/$file_name");
//            $material = (new MaterialModel())->get($material_id, $platform);
            $description = str_replace('&', '', basename($file_name, '.mp4'));
            $result = $video_model->addVideo($advertiser_id, $access_token, $file, $param->signature, $description);
            return [
                'id' => $result['video_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 获取创意行业列表
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        return [];
    }

    /**
     * id转词汇
     * @param array $input
     * @param $media_type
     * @return array
     */
    public function getWordById($input, $media_type)
    {
        return [];
    }


    /**
     * 推送人群包
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        $media_account_model = new MediaAccountModel();
        $audience_account_id_list[] = $target_account_ids[0];
        $audience_account_id_map = $media_account_model->getDataInAccountIds($audience_account_id_list)->keyBy('account_id');
        if (!$audience_account_id_map) {
            throw new AppException('找不到' . implode(',', $audience_account_id_list) . '的账号信息');
        }
        if (!$audience_account_id_map[$target_account_ids[0]]->toutiao_majordomo_id) {
            throw new AppException("账号:{$target_account_ids[0]},没有BM授权");
        }
        foreach ($need_push_audience_list_data as $audience_key => $audience_data) {
            try {
                if (!$audience_account_id_map[$audience_data->account_id]->access_token) {
                    $audience_info = '';
                    foreach ($need_push_audience_list_data as $audience_data_for_info) {
                        if ($audience_data_for_info->account_id == $audience_data->account_id) {
                            $audience_info .= "{$audience_data_for_info->audience_id}-{$audience_data_for_info->name},";
                        }
                    }
                    $audience_info = trim($audience_info, ',');
                    throw new AppException("授权人群包过程中，找不到账号({$audience_data->account_id})的token,涉及人群包($audience_info)");
                }
                (new CustomAudienceGrantModel())->push(
                    $audience_data->account_id,
                    $audience_account_id_map[$audience_data->account_id]->access_token,
                    [$audience_data->audience_id],
                    $audience_account_id_map[$target_account_ids[0]]->toutiao_majordomo_id,
                    $target_account_ids
                );
            } catch (Throwable $e) {
                throw new AppException("{$audience_data->audience_id}-{$audience_data->name}推送失败" . $e->getMessage());
            }
        }
        sleep(4);
        return [];
    }

    /**
     * 获取人群包列表
     * @param string $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = "", $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        $tcal_model = new OdsTencentCustomAudienceLogModel();
        return $tcal_model->getListByCompany($company, $page, $rows, $id, $name, $tag, $source, $account_id);
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        $model = new OdsTencentCustomAudienceLogModel();
        return $model->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    /**
     * @inheritDoc
     */
    public function createConvert(ConvertCreateParam $param)
    {
        if ($param->action_track_type == ActionTrackType::TENCENT_V3) {
            $convert_data = (new V3ConversionsModel())->add($param);
        } else {
            $convert_data = (new ConversionsModel())->add($param);
        }
        $convert_data['convert_id'] = $convert_data['conversion_id'];
        return $convert_data;
    }

    /**
     * 新建渠道包
     * @param ChannelPackageParam $param
     * @return array
     * @throws Throwable
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        if ($param->game_pack === 2) {
            $extend_package_model = new ExtendPackageModel();
            $data = $extend_package_model->add($param);
            if (!empty($data['success_results'])) {
                $data['app_android_channel_package_id'] = $data['success_results'][0]['channel_package_id'];
                $data['download_url'] = $data['success_results'][0]['channel_package_id'];// 用这个来判断是否已建过分包 防止重复建分包
            } elseif (!empty($data['failed_results'])) {
                $error_msg = $data['failed_results'][0]['message'];
                if (strpos($error_msg, '该应用下已存在相同渠道') !== false) {
                    // 通过列表查询channel_package_id
                    $channel_package_list = $extend_package_model->getList(
                        $param->media_account->account_id,
                        $param->media_account->access_token,
                        $param->app_id,
                        1,
                        100,
                        [
                            [
                                'field' => 'channel_name',
                                'operator' => 'CONTAINS',
                                'values' => [(string)$param->site_id]
                            ]
                        ],
                    );

                    $app_android_channel_package_id = '';
                    foreach ($channel_package_list['list'] as $item) {
                        if ((int)$item['package_id'] === (int)$param->app_id && (string)$item['channel_id'] === (string)$param->site_id) {
                            $app_android_channel_package_id = $item['channel_package_id'];
                            break;
                        }
                    }
                    if (!$app_android_channel_package_id) {
                        throw new AppException("腾讯返回渠道包id失败, 请稍后重试");
                    }
                    $data['app_android_channel_package_id'] = $app_android_channel_package_id;
                    $data['download_url'] = $app_android_channel_package_id;// 用这个来判断是否已建过分包 防止重复建分包
                } else {
                    throw new AppException($error_msg);
                }
            } else {
                throw new AppException("腾讯创建渠道包失败, 请稍后重试");
            }
        } else {
            $data = (new AndroidUnionChannelPackagesModel())->add($param);
            if ($data['app_android_channel_package_id'] === '0;') {
                throw new AppException("腾讯返回不正确渠道包id, 请稍后重试");
            }
        }
        return $data;
    }

    /**
     * 创建广告组-返回创建的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        $data = [
            'account_id' => $account_param->account_id,
            'access_token' => $account_param->access_token,
            'campaign_name' => $param->ad1_name_text,
            'campaign_type' => $setting->campaign_type,
            'promoted_object_type' => $param->getSiteConfig()->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID,
            'daily_budget' => $setting->campaign_daily_budget,
            'configured_status' => $setting->campaign_configured_status,
            'speed_mode' => $setting->speed_mode
        ];

        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $data['promoted_object_type'] = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
        }

        if ($other_setting->live_video_mode == 'LIVE_VIDEO_MODE_LIVE') {
            $data['promoted_object_type'] = TencentEum::PROMOTED_OBJECT_TYPE_WECHAT_CHANNELS;
        }
        $ccp = new CampaignCreateParam($data);
        $result = (new CampaignModel())->add($ccp);
        return $result['campaign_id'];
    }

    /**
     * 创建promotedObjects
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param $promoted_object_type
     */
    protected function addPromotedObjects(ADTaskParam $param, MediaAccountInfoParam $account_param, $promoted_object_type)
    {
        (new PromotedObjectsModel())->add($account_param->account_id, $account_param->access_token, $promoted_object_type, $param->getSiteConfig()->appid);
    }

    /**
     * 更新promotedObjects
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    protected function updatePromotedObjects(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        (new PromotedObjectsModel())->update($account_param->account_id, $account_param->access_token, $setting->promoted_object_type, $param->getSiteConfig()->appid);
    }

    /**
     * @param $param
     */
    public function formatParamBeforeCreateAD2($param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $setting->end_date = $setting->schedule_type == 'buxian' || !$setting->end_date ? '' : date('Y-m-d', $setting->end_date);
        in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) && !$setting->end_date && date('Y-m-d', strtotime('+89 day'));

        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $setting->promoted_object_type = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
        } else {
            $setting->promoted_object_type = $param->getSiteConfig()->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;
        }

        if (!$param->getSiteConfig()->deep_external_action) {
            unset($setting->deep_conversion_spec['deep_conversion_worth_spec']);
        }

        if (!in_array('SITE_SET_MOBILE_UNION', $other_setting->site_set) && $other_setting->site_set_none !== 'auto'
        ) {
            unset($setting->flow_optimization_enabled);
        }

        if ($setting->is_bid_coefficient == 1 && $setting->site_set_bid_coefficient) {
            $setting->bid_adjustment['site_set_package'] = [];
            foreach ($setting->site_set_bid_coefficient as $site_set => $value) {
                if (!in_array($site_set, $other_setting->site_set)) {
                    if ($other_setting->site_set_none != 'auto' || (in_array($site_set, [TencentEum::SITE_SET_WECHAT, TencentEum::SITE_SET_MOMENTS]) && intval($value) === 1)) {
                        continue;
                    }
                }
                $site_set_package = [];
                $site_set_package['site_set'] = [$site_set];
                $site_set_package['bid_coefficient'] = $value;
                if (isset($setting->deep_conversion_spec['deep_conversion_behavior_spec']) &&
                    $setting->deep_conversion_spec['deep_conversion_behavior_spec'] &&
                    $setting->is_deep_bid_coefficient == 1 &&
                    $setting->site_set_deep_bid_coefficient[$site_set]) {
                    $site_set_package['deep_bid_coefficient'] = $setting->site_set_deep_bid_coefficient[$site_set];
                }
                if (isset($setting->deep_conversion_spec['deep_conversion_worth_spec']) &&
                    $setting->deep_conversion_spec['deep_conversion_worth_spec'] &&
                    $setting->is_deep_bid_coefficient == 1 &&
                    $setting->site_set_deep_bid_coefficient[$site_set]) {
                    $site_set_package['deep_bid_coefficient'] = $setting->site_set_deep_bid_coefficient[$site_set];
                }
                $setting->bid_adjustment['site_set_package'][$site_set] = $site_set_package;
            }
        }

        if ($setting->is_deep_bid_coefficient == 1 && $setting->site_set_deep_bid_coefficient && $setting->deep_conversion_spec_none === 'true') {
            foreach ($setting->site_set_deep_bid_coefficient as $site_set => $value) {
                if (!in_array($site_set, $other_setting->site_set)) {
                    if ($other_setting->site_set_none != 'auto' || (in_array($site_set, [TencentEum::SITE_SET_WECHAT, TencentEum::SITE_SET_MOMENTS]) && intval($value) === 1)) {
                        continue;
                    }
                }
                if (isset($setting->bid_adjustment['site_set_package'][$site_set])) {
                    $setting->bid_adjustment['site_set_package'][$site_set]['deep_bid_coefficient'] = $value;
                } else {
                    $site_set_package = [];
                    $site_set_package['site_set'] = [$site_set];
                    $site_set_package['deep_bid_coefficient'] = $value;
                    $setting->bid_adjustment['site_set_package'][$site_set] = $site_set_package;
                }
            }
        }

        if (!empty($setting->bid_adjustment['site_set_package'])) {
            $setting->bid_adjustment['site_set_package'] = array_values($setting->bid_adjustment['site_set_package']);
        } else {
            if (isset($setting->bid_adjustment['site_set_package'])) {
                unset($setting->bid_adjustment['site_set_package']);
            }
        }

        if (in_array($param->getSiteConfig()->deep_external_action, ['GOAL_1DAY_PURCHASE_ROAS', 'GOAL_7DAY_PURCHASE_ROAS', 'GOAL_7DAY_LONGTERM_PURCHASE_ROAS'])) {
            unset($setting->deep_conversion_spec['deep_conversion_behavior_spec']);
            if ($param->convert_id) {
                $setting->deep_conversion_worth_advanced_rate = '';
                $setting->deep_conversion_worth_rate = (double)trim($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']);
                $setting->deep_conversion_spec = [];
            }
        }

        if ($param->getSiteConfig()->deep_external_action == 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS') {
            unset($setting->deep_conversion_spec['deep_conversion_behavior_spec']);
            if ($param->convert_id) {
                $setting->deep_conversion_worth_rate = '';
                $setting->deep_conversion_worth_advanced_rate = (double)trim($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi']);
                $setting->deep_conversion_spec = [];
            }
        }

        if (in_array($param->getSiteConfig()->deep_external_action, ['OPTIMIZATIONGOAL_FIRST_PURCHASE', 'OPTIMIZATIONGOAL_APP_PURCHASE'])) {
            unset($setting->deep_conversion_spec['deep_conversion_worth_spec']);
            if ($param->convert_id) {
                $setting->deep_conversion_behavior_bid = $setting->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount'];
                $setting->deep_conversion_spec = [];
            }
        }

        if ($setting->smart_bid_type == 'SMART_BID_TYPE_SYSTEMATIC') {
            $setting->deep_conversion_worth_rate = $setting->deep_conversion_worth_advanced_rate = '';
            $setting->bid_adjustment = [];
        }

        if ($setting->bid_strategy == TencentEum::BID_STRATEGY_MAX_COST) {
            $setting->smart_bid_type = '';
            $setting->bid_strategy = '';
            $setting->bid_scene = TencentEum::BID_SCENE_NORMAL_MAX;
        } else {
            $setting->bid_scene = '';
        }
    }


    /**
     * 新建广告二级
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param string $dynamic_creative_id
     * @return mixed
     */
    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param, $dynamic_creative_id = '')
    {
        // 准备参数
        $this->formatParamBeforeCreateAD2($param);

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $data = array_merge(
            $setting->toArray(),
            $other_setting->toArray(),
            [
                'daily_budget' => $setting->ad_daily_budget,
                'configured_status' => $setting->ad_configured_status,
                'promoted_object_id' => $param->getSiteConfig()->appid,
                'begin_date' => $setting->schedule_type == 'buxian' ? date('Y-m-d') : date('Y-m-d', $setting->begin_date),
                'end_date' => $setting->end_date,
                'account_id' => $account_param->account_id,
                'access_token' => $account_param->access_token,
                'campaign_id' => $param->ad1_id,
                'adgroup_name' => $param->ad1_name_text . date('-YmdHi'),
                'conversion_id' => $param->convert_id ? (int)$param->convert_id : '',
                'dynamic_creative_id' => $dynamic_creative_id,
                'bid_amount' => $setting->getBidAmount(),
                'time_series' => implode('', $setting->time_series),
                'auto_audience' => $targeting->auto_audience,
                'expand_enabled' => $targeting->expand_enabled,
                'expand_targeting' => $targeting->expand_targeting,
                'cold_start_audience' => $targeting->cold_start_audience,
                'optimization_goal' => ConvertType::MEDIA[MediaType::TENCENT][$param->getSiteConfig()->convert_type],
                'bid_mode' => $setting->billing_event == 'BILLINGEVENT_IMPRESSION' ? 'BID_MODE_OCPM' : 'BID_MODE_OCPC',
                'app_android_channel_package_id' => $setting->promoted_object_type == TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID ? $param->getSiteConfig()->app_android_channel_package_id : '',
            ]
        );

        if ($other_setting->live_video_mode == 'LIVE_VIDEO_MODE_LIVE') {
            $data['promoted_object_type'] = TencentEum::PROMOTED_OBJECT_TYPE_WECHAT_CHANNELS;
            $data['live_video_mode'] = "LIVE_VIDEO_MODE_LIVE";
            $data['live_video_sub_mode'] = "LIVE_VIDEO_SUBMODE_LIVE_ROOM";
            $data['promoted_object_id'] = $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id;
        }

        if ($other_setting->live_video_mode !== 'LIVE_VIDEO_MODE_LIVE') {
            try {
                // 新建渠道object id
                $this->addPromotedObjects($param, $account_param, $data['promoted_object_type']);
            } catch (Throwable $e) {
                if (strpos($e->getMessage(), '该应用封面图为空') !== false) {
                    throw new AppException('该应用封面图为空，请补充封面图信息');
                }
            }
        }

        try {
            // 更新渠道object id
            $this->updatePromotedObjects($param, $account_param);
        } catch (Throwable $e) {
            $error_msg = $e->getMessage();
            echo "updatePromotedObjects出错:{$error_msg}";
        }

        $agcp = new AdGroupCreateParam($data);

        // 长效ROI定向字段的额外处理
        if (
            $param->getSiteConfig()->plat_id == PlatId::MINI &&
            $param->getSiteConfig()->convert_type == 'OPTIMIZATIONGOAL_APP_REGISTER' &&
            $param->getSiteConfig()->deep_external_action == 'GOAL_7DAY_LONGTERM_PURCHASE_ROAS') {
            $exp = [];
            $exp['wechat_ad_behavior']['minigame_app_id'] = [$param->getSiteConfig()->appid];
            if ($targeting->wechat_ad_behavior_status == 'include' && $targeting->wechat_ad_behavior_behavior) {
                $exp['wechat_ad_behavior']['actions'] = $targeting->wechat_ad_behavior_behavior;
            } elseif ($targeting->wechat_ad_behavior_status == 'exclude' && $targeting->wechat_ad_behavior_behavior) {
                $exp['wechat_ad_behavior']['excluded_actions'] = $targeting->wechat_ad_behavior_behavior;
            }
        }

        $agcp->setTargeting($targeting, $exp ?? []);

        if ($setting->excluded_dimension_mode == 'zidingyi') {
            $agcp->targeting['excluded_converted_audience']['excluded_dimension'] = $setting->excluded_dimension;
            if ($setting->excluded_conversion_behavior_mode == 'zidingyi' && $setting->excluded_conversion_behavior) {
                $agcp->targeting['excluded_converted_audience']['conversion_behavior_list'] = is_array($setting->excluded_conversion_behavior) ? $setting->excluded_conversion_behavior : [$setting->excluded_conversion_behavior];
            }
        }

        $result = (new AdGroupModel())->add($agcp);

        // 搜索广告 创建关键词
        if ($other_setting->site_set_none === 'search' &&
            $targeting->search_scene_switch == '1' && $targeting->search_expand_bidword_switch == 'on' && count($targeting->search_expand_bidword_list) > 0) {
            $list = [];
            foreach ($targeting->search_expand_bidword_list as $word) {
                $list[] = [
                    'campaign_id' => $param->ad1_id,
                    'adgroup_id' => $result['adgroup_id'],
                    'bidword' => $word['word'],
                    'match_type' => $word['match_type'],
                ];
            }

            (new BidWordModel())->addV1(
                $account_param->account_id,
                $account_param->access_token,
                $list
            );
        }

        return $result['adgroup_id'];
    }

    /**
     * 获取动态广告创意结构
     * @param $template_id
     * @param array $creative_info_list
     * @param array $title_list
     * @param array $material_media_id_map
     * @param ADTaskParam $param
     * @param bool $is_ada
     * @return array
     */
    protected function getDynamicElementStructure(
        $template_id, array $creative_info_list, array $title_list,
        array $material_media_id_map, ADTaskParam $param, bool $is_ada = false
    )
    {
        /** @var ADSettingContentParam $setting * */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $title = trim($setting->title);
        $description_options = [];
        foreach ($title_list as $description) {
            $description_options[] = trim(str_replace("\t", '', $description));
        }

        $video_options = [];
        $cover_options = [];
        $image_options = [];
        $image_list_options = [];
        foreach ($creative_info_list as $creative) {
            if (isset($creative['video_info'])) {
                $video_options[] = $material_media_id_map[$creative['video_info']['id']]['id'];
                if (
                    !(
                        $is_ada &&
                        (
                            (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) && count($other_setting->site_set) == 1) ||
                            (in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set) && count($other_setting->site_set) == 1)
                        )
                    )
                ) {
                    $cover_options[] = $material_media_id_map[$creative['cover_info']['id']]['id'];
                }
            }

            if (isset($creative['image_info'])) {
                if (isset($creative['image_info']['image_list']) && $creative['image_info']['image_list']) {
                    foreach ($creative['image_info']['image_list'] as $image) {
                        $image_list_options[] = $material_media_id_map[$image['id']]['id'];
                    }
                } else {
                    $image_options[] = $material_media_id_map[$creative['image_info']['id']]['id'];
                }

            }
        }


        switch ($template_id) {
            case '450':
                $data = [
                    "title_options" => [$title],
                    "description_options" => $description_options,
                    "image_list_options" => []
                ];
                foreach ($image_options as $image_id) {
                    $data['image_list_options'][] = [$image_id];
                }
                break;
            case '876':
                $data = [
                    "title_options" => [$title],
                    "description_options" => $description_options,
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "image_options" => []
                ];
                foreach ($image_options as $image_id) {
                    $data['image_options'][] = [$image_id];
                }
                break;
            case '877':
                $data = [
                    "title_options" => [$title],
                    "description_options" => $description_options,
                    "button_text" => $other_setting->button_text,
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "image" => $cover_options[0] ?? '',
                    "video" => $video_options[0],
                ];
                break;
            case '1708':
                $data = [
                    "title" => trim($setting->title),
                    "description_options" => $description_options,
                    "video_options" => $video_options,
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                ];
                if ($setting->shop_img) {
                    $data['shop_image_struct']['shop_image_id'] = $setting->shop_img;
                    $data['shop_image_struct']['shop_image_switch'] = true;
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '1707':
                $data = [
                    "title" => trim($setting->title),
                    "description_options" => $description_options,
                    "image_list_options" => [],
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                ];
                foreach ($image_options as $image_id) {
                    $data['image_list_options'][] = [$image_id];
                }
                if ($setting->shop_img) {
                    $data['shop_image_struct']['shop_image_id'] = $setting->shop_img;
                    $data['shop_image_struct']['shop_image_switch'] = true;
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '452':
                $data = [
                    "title_options" => [$title],
                    "description_options" => $description_options,
                    "short_video_struct_options" => []
                ];
                foreach ($video_options as $video_id) {
                    $data['short_video_struct_options'][] = ['short_video2' => $video_id];
                }
                break;
            case '588':
                $data = [
                    "title_options" => [$title],
                    "description_options" => $description_options,
                    "image_list_options" => []
                ];
                foreach ($image_options as $image_id) {
                    $data['image_list_options'][] = [$image_id];
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '311':
                $data = [
                    "title_options" => $description_options,
                    "image_list_options" => []
                ];
                foreach ($image_options as $image_id) {
                    $data['image_list_options'][] = [$image_id];
                }
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '641': // 3组图
                $data = [
                    "description_options" => $description_options,
                ];
                for ($i = 0; $i < count($image_list_options); $i = $i + 3)
                    $data["image_list_options"][] = [
                        $image_list_options[$i],
                        $image_list_options[$i + 1],
                        $image_list_options[$i + 2],
                    ];
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '642':
                $data = [
                    "description_options" => $description_options,
                ];
                for ($i = 0; $i < count($image_list_options); $i = $i + 4) {
                    $data["image_list_options"][] = [
                        $image_list_options[$i],
                        $image_list_options[$i + 1],
                        $image_list_options[$i + 2],
                        $image_list_options[$i + 3],
                    ];
                }
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '643':
                $data = [
                    "description_options" => $description_options,
                ];
                for ($i = 0; $i < count($image_list_options); $i = $i + 6) {
                    $data["image_list_options"][] = [
                        $image_list_options[$i],
                        $image_list_options[$i + 1],
                        $image_list_options[$i + 2],
                        $image_list_options[$i + 3],
                        $image_list_options[$i + 4],
                        $image_list_options[$i + 5],
                    ];
                }
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '618':
                $data = [
                    "short_video_struct_options" => [],
                    "title_options" => $description_options,
                ];
                foreach ($video_options as $video_id) {
                    $data['short_video_struct_options'][] = ['short_video1' => $video_id];
                }
                break;
            case '589':
                $data = [
                    "title" => $title,
                    "video_options" => $video_options,
                    "description_options" => $description_options
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '957':
                $data = [
                    "title_options" => [$title],
                    "short_video_struct_options" => [],
                    "description_options" => $description_options
                ];
                foreach ($video_options as $video_id) {
                    $data['short_video_struct_options'][] = ['short_video2' => $video_id];
                }
                break;
            case '711':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    'button_text' => $other_setting->button_text,
                    "description_options" => $description_options,
                    "image_options" => $image_options
                ];
                break;
            case '712':
                $data = [
                    "brand" => [
                        "brand_img" => $setting->brand_img,
                        "brand_name" => $setting->brand_name,
                    ],
                    "description_options" => $description_options,
                    "image_options" => $image_options,
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '713':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "description_options" => $description_options,
                    "image_options" => $image_options,
                    "button_text" => trim($other_setting->button_text),
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '721':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "description_options" => $description_options,
                    "image_options" => $cover_options,
                    "video_options" => $video_options,
                    "button_text" => trim($other_setting->button_text),
                ];
                if ($other_setting->site_set_none == 'shipinghao' && !$is_ada) {
                    unset($data['image_options']);
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                    $data['floating_zone_struct'] = [
                        'floating_zone_name' => $other_setting->promotion_title,
                        'floating_zone_button_text' => $other_setting->button_text,
                        'floating_zone_desc' => $other_setting->promotion_desc,
                        'floating_zone_image_id' => $other_setting->promotion_card_id,
                        'floating_zone_type' => 'FLOATING_ZONE_TYPE_IMAGE_TEXT',
                        'floating_zone_switch' => true,
                    ];
                    $data['finder_object_visibility'] = $other_setting->finder_object_visibility;
                    if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE) {
                        $data['wechat_channels_spec'] = ['username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''];
                    }
                }
                if ($other_setting->choose_button_switch) {
                    $data['floating_zone_struct'] = array_merge(($data['floating_zone_struct'] ?? []), [
                        'chosen_button_text1' => $other_setting->chosen_button_text1,
                        'chosen_button_text2' => $other_setting->chosen_button_text2,
                    ]);
                }
                break;
            case '925':
                $data = [
                    'image_options' => $image_options
                ];
                break;
            case '1529':
                $data = [
                    "image_options" => $cover_options,
                    "video_options" => $video_options,
                    "title_options" => $description_options,
                ];
                break;
            case '2083':
                $data = [];
                $data['brand_component_options'] = [[
                    'value' => [
                        'brand_name' => $setting->brand_name,
                        'brand_img' => ['image_id' => $setting->brand_img]
                    ]]
                ];
                foreach ($description_options as $desc) {
                    $data['description_component_options'][] = ['value' => $desc];
                }
                foreach ($image_options as $image_id) {
                    $data['image_component_options'][] = ['value' => ['image_id' => $image_id]];
                }
                foreach ($video_options as $key => $video_id) {
                    $data['video_component_options'][] = ['value' => [
                        'video' => ['video_id' => $video_id],
                        'cover_image' => ['image_id' => $cover_options[$key]]
                    ]];
                }
                $data['title_component_options'][] = ['value'=> $setting->title];
                break;
            case '720':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "description_options" => $description_options,
                    "image_options" => $cover_options,
                    "video_options" => $video_options,
                    "button_text" => trim($other_setting->button_text),
                    "bottom_text" => trim($setting->bottom_text),
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                    $data['floating_zone_struct'] = [
                        'floating_zone_name' => $other_setting->promotion_title,
                        'floating_zone_button_text' => $other_setting->button_text,
                        'floating_zone_desc' => $other_setting->promotion_desc,
                        'floating_zone_image_id' => $other_setting->promotion_card_id,
                        'floating_zone_type' => 'FLOATING_ZONE_TYPE_IMAGE_TEXT',
                        'floating_zone_switch' => true,
                    ];
                    $data['finder_object_visibility'] = $other_setting->finder_object_visibility;
                    if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE) {
                        $data['wechat_channels_spec'] = ['username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''];
                    }
                }
                if ($other_setting->choose_button_switch) {
                    $data['floating_zone_struct'] = array_merge(($data['floating_zone_struct'] ?? []), [
                        'chosen_button_text1' => $other_setting->chosen_button_text1,
                        'chosen_button_text2' => $other_setting->chosen_button_text2,
                    ]);
                }
                break;
            default:
                throw new AppException('没有传入正确的创意模型id');
        }

        if (!$data['image_options']) {
            unset($data['image_options']);
        }
        return $data;
    }

    /**
     * 获取创意结构
     * @param $template_id
     * @param array $creative_info
     * @param array $material_media_id_map
     * @param ADTaskParam $param
     * @return array
     */
    private function getElementsStructure($template_id, array $creative_info, array $material_media_id_map, ADTaskParam $param)
    {
        /** @var ADSettingContentParam $setting * */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $creative_info['title'] = trim(str_replace("\t", '', $creative_info['title']));
        switch ($template_id) {
            case '450':
                $data = [
                    "title" => trim($setting->title),
                    "description" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['id']]['id']
                    ]
                ];
                break;
            case '876':
                $data = [
                    "title" => $setting->title,
                    "description" => trim($creative_info['title']),
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "image" => $material_media_id_map[$creative_info['image_info']['id']]['id'],
                ];
                break;
            case '877':
                $data = [
                    "title" => $setting->title,
                    "description" => trim($creative_info['title']),
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "button_text" => $other_setting->button_text,
                    "image" => $material_media_id_map[$creative_info['cover_info']['id']]['id'],
                    "video" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                ];
                break;
            case '878':
                $data = [
                    "title" => $setting->title,
                    "description" => trim($creative_info['title']),
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "button_text" => $other_setting->button_text,
                    "left_bottom_txt" => $setting->bottom_text,
                    "image" => $material_media_id_map[$creative_info['image_info']['id']]['id'],
                ];
                break;
            case '1708':
                $data = [
                    "title" => trim($setting->title),
                    "description" => trim($creative_info['title']),
                    "video" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                ];
                if ($setting->shop_img) {
                    $data['shop_image_struct']['shop_image_id'] = $setting->shop_img;
                    $data['shop_image_struct']['shop_image_switch'] = true;
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '1707':
                $data = [
                    "title" => trim($setting->title),
                    "description" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['id']]['id']
                    ],
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                ];
                if ($setting->shop_img) {
                    $data['shop_image_struct']['shop_image_id'] = $setting->shop_img;
                    $data['shop_image_struct']['shop_image_switch'] = true;
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '452':
                $data = [
                    "title" => trim($setting->title),
                    "description" => trim($creative_info['title']),
                    "short_video_struct" => [
                        'short_video2' => $material_media_id_map[$creative_info['video_info']['id']]['id']
                    ]
                ];
                break;
            case '588':
                $data = [
                    "title" => trim($setting->title),
                    "description" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['id']]['id']
                    ]
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '311':
                $data = [
                    "title" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['id']]['id']
                    ]
                ];
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '641':
                $data = [
                    "description" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['image_list'][0]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][1]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][2]['id']]['id']
                    ]
                ];
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '642':
                $data = [
                    "description" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['image_list'][0]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][1]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][2]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][3]['id']]['id']
                    ]
                ];
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '643':
                $data = [
                    "description" => trim($creative_info['title']),
                    "image_list" => [
                        $material_media_id_map[$creative_info['image_info']['image_list'][0]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][1]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][2]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][3]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][4]['id']]['id'],
                        $material_media_id_map[$creative_info['image_info']['image_list'][5]['id']]['id']
                    ]
                ];
                if (!in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['brand'] = [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ];
                    $data['button_text'] = $other_setting->button_text;
                }
                break;
            case '618':
                $data = [
                    "short_video_struct" => [
                        "short_video1" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                    ],
                    "title" => trim($creative_info['title']),
                ];
                break;
            case '589':
                $data = [
                    "title" => trim($setting->title),
                    "video" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                    "description" => trim($creative_info['title'])
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '957':
                $data = [
                    "title" => trim($setting->title),
                    "short_video_struct" => [
                        'short_video2' => $material_media_id_map[$creative_info['video_info']['id']]['id']
                    ],
                    "description" => trim($creative_info['title'])
                ];
                break;
            case '711':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    'button_text' => $other_setting->button_text,
                    "description" => trim($creative_info['title']),
                    "image" => $material_media_id_map[$creative_info['image_info']['id']]['id'],
                ];
                if ($other_setting->site_set_none === 'datongtou') {
                    if ($setting->label) {
                        foreach ($setting->label as $value) {
                            $data['label'][] = ["content" => $value];
                        }
                    }
                }
                break;
            case '712':
                $data = [
                    "brand" => [
                        "brand_img" => $setting->brand_img,
                        "brand_name" => $setting->brand_name,
                    ],
                    "description" => trim($creative_info['title']),
                    "image" => $material_media_id_map[$creative_info['image_info']['id']]['id'],
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '713':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "description" => trim($creative_info['title']),
                    "image" => $material_media_id_map[$creative_info['image_info']['id']]['id'],
                    "button_text" => trim($other_setting->button_text),
                ];
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                break;
            case '721':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "description" => trim($creative_info['title']),
                    "image" => $material_media_id_map[$creative_info['cover_info']['id']]['id'],
                    "video" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                    "button_text" => trim($other_setting->button_text),
                ];
                if ($other_setting->site_set_none == 'shipinghao') {
                    unset($data['image']);
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                    $data['floating_zone_struct'] = [
                        'floating_zone_name' => $other_setting->promotion_title,
                        'floating_zone_button_text' => $other_setting->button_text,
                        'floating_zone_desc' => $other_setting->promotion_desc,
                        'floating_zone_image_id' => $other_setting->promotion_card_id,
                        'floating_zone_type' => 'FLOATING_ZONE_TYPE_IMAGE_TEXT',
                        'floating_zone_switch' => true,
                    ];
//                    $data['finder_object_visibility'] = $other_setting->finder_object_visibility;
                    if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE) {
                        $data['wechat_channels_spec'] = ['username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''];
                    }
                    $data['finder_object_visibility'] = false;
                }
                if ($other_setting->choose_button_switch) {
                    $data['floating_zone_struct'] = array_merge(($data['floating_zone_struct'] ?? []), [
                        'chosen_button_text1' => $other_setting->chosen_button_text1,
                        'chosen_button_text2' => $other_setting->chosen_button_text2,
                    ]);
                }
                break;
            case '925':
                $data = [
                    'image' => $material_media_id_map[$creative_info['image_info']['id']]['id']
                ];
                break;
            case '1529':
                $data = [
                    "image" => $material_media_id_map[$creative_info['cover_info']['id']]['id'],
                    "video" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                    "title" => trim($creative_info['title']),
                ];
                break;
            case '720':
                $data = [
                    "brand" => [
                        "brand_name" => $setting->brand_name,
                        "brand_img" => $setting->brand_img
                    ],
                    "description" => trim($creative_info['title']),
                    "image" => $material_media_id_map[$creative_info['cover_info']['id']]['id'],
                    "video" => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                    "button_text" => trim($other_setting->button_text),
                    "bottom_text" => trim($setting->bottom_text),
                ];
                if ($other_setting->site_set_none == 'shipinghao') {
                    unset($data['image']);
                }
                if ($setting->label) {
                    foreach ($setting->label as $value) {
                        $data['label'][] = ["content" => $value];
                    }
                }
                if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
                    $data['floating_zone_struct'] = [
                        'floating_zone_name' => $other_setting->promotion_title,
                        'floating_zone_button_text' => $other_setting->button_text,
                        'floating_zone_desc' => $other_setting->promotion_desc,
                        'floating_zone_image_id' => $other_setting->promotion_card_id,
                        'floating_zone_type' => 'FLOATING_ZONE_TYPE_IMAGE_TEXT',
                        'floating_zone_switch' => true,
                    ];
                    $data['finder_object_visibility'] = $other_setting->finder_object_visibility;
                    if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE) {
                        $data['wechat_channels_spec'] = ['username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''];
                    }
                }
                if ($other_setting->choose_button_switch) {
                    $data['floating_zone_struct'] = array_merge(($data['floating_zone_struct'] ?? []), [
                        'chosen_button_text1' => $other_setting->chosen_button_text1,
                        'chosen_button_text2' => $other_setting->chosen_button_text2,
                    ]);
                }
                break;
            default:
                throw new AppException('没有传入正确的创意模型id');
        }

        return $data;
    }

    /**
     * 创建创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        // icon上传
        if ($setting->brand_img) {
            $setting->brand_img = $this->uploadImageByUrl($param, $account_param, $setting->brand_img);
        }
        // 卖点图上传
        if ($setting->shop_img) {
            $setting->shop_img = $this->uploadImageByUrl($param, $account_param, $setting->shop_img);
        }
        // 卖点图上传
        if ($other_setting->promotion_card_id && in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            $other_setting->promotion_card_id = $this->uploadImageByUrl($param, $account_param, $other_setting->promotion_card_id);
        }

        if ($param->creative_mode == BatchAD::CREATIVE_CUSTOM_MODE) {
            $media_creative_id_list = $this->createCustomCreative($param, $account_param, $material_media_id_map);
        } else {
            if ($other_setting->dynamic_creative_type == TencentEum::DYNAMIC_CREATIVE_TYPE_PROGRAM) {
                $media_creative_id_list = $this->createProgramCreative2($param, $account_param, $material_media_id_map);
            } else {
                $media_creative_id_list = $this->createProgramCreative($param, $account_param, $material_media_id_map);
            }
        }

        return $media_creative_id_list;
    }

    /**
     * 程序会创意2.0
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createProgramCreative2(
        ADTaskParam           $param,
        MediaAccountInfoParam $account_param,
        array                 $material_media_id_map
    )
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        $data = $this->structureCreativePostData($param, $param->creative_list[0]);

        $create_model = new DynamicCreativeModel();

        $ad_datetime = date('Y-m-d H:i:s');

        $data = array_merge($data, [
            'account_id' => $account_param->account_id,
            'access_token' => $account_param->access_token,
            'dynamic_creative_name' => "{$param->ad2_id}_动态创意_{$ad_datetime}",
            'dynamic_creative_type' => $other_setting->dynamic_creative_type,
//            'click_tracking_url' => $param->getSiteConfig()->action_track_url,
//            'impression_tracking_url' => $param->getSiteConfig()->display_track_url,
        ]);

        // 初始化动态广告的创意结构体
        $data['dynamic_creative_elements'] = $this->getDynamicElementStructure2($param, $material_media_id_map);

        $data['program_creative_info'] = [
            'material_derive_id' => $this->makeProgramModelAndGetID($param, $account_param, $material_media_id_map),
            'bid_mode' => $setting->billing_event == 'BILLINGEVENT_IMPRESSION' ? 'BID_MODE_OCPM' : 'BID_MODE_OCPC',
        ];

        try {
            // 新建渠道object id
            $this->addPromotedObjects($param, $account_param, $data['promoted_object_type']);
        } catch (Throwable $e) {
            if (strpos($e->getMessage(), '该应用封面图为空') !== false) {
                throw new AppException('该应用封面图为空，请补充封面图信息');
            }
        }

        $ad_creative_result = $create_model->add(new AdDynamicCreativeCreateParam($data));

        return [$ad_creative_result['dynamic_creative_id']];
    }

    /**
     * @param ADTaskParam $param
     * @param array $material_media_id_map
     * @return array
     */
    public function getDynamicElementStructure2(ADTaskParam $param, array $material_media_id_map)
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        $image_list_options = $video_options = $cover_options = $image_options = [];
        foreach ($param->creative_list as $creative) {
            if (isset($creative['video_info'])) {
                $video_options[] = $material_media_id_map[$creative['video_info']['id']]['id'];
                $image_options[] = $material_media_id_map[$creative['cover_info']['id']]['id'];
            }
            if (isset($creative['image_info'])) {
                if (isset($creative['image_info']['image_list']) && $creative['image_info']['image_list']) {
                    foreach ($creative['image_info']['image_list'] as $image) {
                        $image_list_options[] = $material_media_id_map[$image['id']]['id'];
                    }
                } else {
                    $image_options[] = $material_media_id_map[$creative['image_info']['id']]['id'];
                }
            }
        }


        $struct = [
            'brand' => [
                'brand_name' => $setting->brand_name,
                'brand_img' => $setting->brand_img
            ],
            'description_options' => $param->getWordList(),
            'button_text' => $other_setting->button_text,
            'bottom_text' => trim($setting->bottom_text),
            'caption' => trim($setting->bottom_text),
            'head_line' => trim($setting->head_line),
            'image_options' => $image_options ?? [],
            'image_list_options' => $image_list_options ? [$image_list_options] : [],
            'video_options' => $video_options ?? [],
        ];

        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            $struct['floating_zone_struct'] = [
                'floating_zone_name' => $other_setting->promotion_title,
                'floating_zone_button_text' => $other_setting->button_text,
                'floating_zone_desc' => $other_setting->promotion_desc,
                'floating_zone_image_id' => $other_setting->promotion_card_id,
                'floating_zone_type' => 'FLOATING_ZONE_TYPE_IMAGE_TEXT',
                'floating_zone_switch' => true,
            ];
            $struct['finder_object_visibility'] = $other_setting->finder_object_visibility;
            if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE) {
                $struct['wechat_channels_spec'] = ['username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''];
            }
        }
        if ($setting->label) {
            foreach ($setting->label as $value) {
                $struct['label'][] = ["content" => $value];
            }
        }
        return $struct;
    }

    /**
     * 新建创意预览并获取预览ID
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return string
     */
    public function makeProgramModelAndGetID(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        $make_param = new ProgrammedAddParam([
            'account_id' => $account_param->account_id,
            'access_token' => $account_param->access_token,
            'auto_derived_program_creative_switch' => $other_setting->auto_derived_program_creative_switch,
            'standard_switch' => 'true',
        ]);

        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $promoted_object_type = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
        } else {
            $promoted_object_type = $param->getSiteConfig()->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;
        }

        $make_param->setAdMetaData([
            'site_sets' => $other_setting->site_set,
            'automatic_site_enabled' => $other_setting->automatic_site_enabled,
            'promoted_object_type' => $promoted_object_type,
            'promoted_object_id' => $param->getSiteConfig()->appid,
            'bid_mode' => $setting->billing_event == 'BILLINGEVENT_IMPRESSION' ? 'BID_MODE_OCPM' : 'BID_MODE_OCPC',
        ]);

        foreach ($param->creative_list as $creative) {
            if (isset($creative['video_info'])) {
                $make_param->addMaterials([['type' => 'VIDEO', 'media_id' => "{$material_media_id_map[$creative['video_info']['id']]['id']}"]]);
            } else if (isset($creative['image_info'])) {
                if (isset($creative['image_info']['image_list']) && $creative['image_info']['image_list']) {
                    $materials = [];
                    foreach ($creative['image_info']['image_list'] as $image) {
                        $materials[] = ['type' => 'IMAGE', 'media_id' => $material_media_id_map[$image['id']]['id']];
                    }
                    $make_param->addMaterials($materials);
                } else {
                    $make_param->addMaterials([['type' => 'IMAGE', 'media_id' => $material_media_id_map[$creative['image_info']['id']]['id']]]);
                }
            }
        }

        $resp = (new ProgrammedModel())->add($make_param);

        return $resp['material_derive_id'];
    }

    /**
     * 上传图片
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param string $icon_url
     * @return mixed
     */
    protected function uploadImageByUrl(ADTaskParam $param, MediaAccountInfoParam $account_param, string $icon_url)
    {
        $name = basename($icon_url);
        $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
        $data = (new MaterialFileModel())->getDataByName($name);
        if ($data) {
            $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
                [$data->id],
                $param->platform,
                MediaType::TENCENT,
                $account_param->account_id
            );

            $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
            if ($upload_info_list) {
                $resp_img = $upload_info_list[$data->id]->media_material_file_id;
            } else {
                $data = (array)$data;

                $insert = [];
                $insert['platform'] = $param->platform;
                $insert['media_type'] = MediaType::TENCENT;
                $insert['account_id'] = $account_param->account_id;
                $insert['material_id'] = $data['material_id'];
                $insert['file_type'] = $data['file_type'];
                $insert['file_id'] = $data['id'];
                $insert['filename'] = $data['filename'];
                $insert['uploader'] = $param->creator;
                $insert['signature'] = $data['signature'];
                $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                if($data['file_type'] == 4) {
                    $media_file_info = $this->uploadIcon(
                        new MaterialFileParam($data),
                        $account_param->account_id,
                        $account_param->access_token
                    );
                } else {
                    $media_file_info = $this->uploadImage(
                        new MaterialFileParam($data),
                        $account_param->account_id,
                        $account_param->access_token
                    );
                }
                if (!$media_file_info['id']) {
                    throw new AppException('上传图片失败:' . $name);
                }
                $media_file_id = $media_file_info['id'];
                $media_file_url = $media_file_info['url'];
                $insert['media_material_file_id'] = $media_file_id;
                $insert['url'] = $media_file_url;
                $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                $resp_img = $media_file_id;
            }
        } else {
            throw new AppException('找不到图片信息:' . $name);
        }
        return $resp_img;
    }

    /**
     * 创建程序化创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @param bool $is_ada
     * @return array
     */
    public function createProgramCreative(
        ADTaskParam           $param,
        MediaAccountInfoParam $account_param,
        array                 $material_media_id_map,
        bool                  $is_ada = false
    )
    {

        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $data = $this->structureCreativePostData($param, $param->creative_list[0]);

        $create_model = new DynamicCreativeModel();

        $ad_datetime = date('Y-m-d H:i:s');

        $data = array_merge($data, [
            'account_id' => $account_param->account_id,
            'access_token' => $account_param->access_token,
            'campaign_id' => $param->ad1_id,
            'dynamic_creative_name' => "{$param->ad2_id}_动态创意_{$ad_datetime}",
            'dynamic_creative_template_id' => $other_setting->template_id,
//            'click_tracking_url' => $param->getSiteConfig()->action_track_url,
//            'impression_tracking_url' => $param->getSiteConfig()->display_track_url,
        ]);

        // 初始化动态广告的创意结构体
        $data['dynamic_creative_elements'] = $this->getDynamicElementStructure(
            $other_setting->template_id,
            $param->creative_list,
            $param->word_list,
            $material_media_id_map,
            $param,
            $is_ada
        );

        if ($other_setting->site_set_none === 'search') {
            $data['dynamic_creative_elements']['landing_page_component_options'] = [
                [
                    'value' => [[
                        'page_type' => $data['page_type'],
                        'page_spec' => [
                            'mini_program_spec' => [
                                'mini_program_id' => $param->getSiteConfig()->appid,
                                'mini_program_path' => "?game_id={$param->getSiteConfig()->game_id}&agent_id={$param->agent_id}&site_id={$param->site_id}",
                            ]
                        ],
                        'media_query' => ['media_type' => 'ALL']
                    ]]
                ]
            ];
        }

        try {
            // 新建渠道object id
            $this->addPromotedObjects($param, $account_param, $data['promoted_object_type']);
        } catch (Throwable $e) {
            if (strpos($e->getMessage(), '该应用封面图为空') !== false) {
                throw new AppException('该应用封面图为空，请补充封面图信息');
            }
        }

        if ($is_ada) {
            $data['smart_delivery_platform'] = 'SMART_DELIVERY_PLATFORM_EDITION_GAME';
            if (in_array($other_setting->template_id, [721, 720]) &&
                !(
                    !empty(array_intersect([TencentEum::SITE_SET_WECHAT, TencentEum::SITE_SET_MOMENTS], $other_setting->site_set)) &&
                    count($other_setting->site_set) == 1
                )
            ) {
                $data['dynamic_creative_group_used'] = 'VIDEO_IMAGE_GROUP';
            }

            if (in_array('SITE_SET_CHANNELS', $other_setting->site_set)) {
                $data['dynamic_creative_elements']['wechat_channels_spec']['username'] = $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? '';
            }
        }
        $ad_creative_result = $create_model->add(new AdDynamicCreativeCreateParam($data));

        return [$ad_creative_result['dynamic_creative_id']];
    }

    /**
     * 创建自定义创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createCustomCreative(
        ADTaskParam           $param,
        MediaAccountInfoParam $account_param,
        array                 $material_media_id_map
    )
    {
        $create_model = new CreativeModel();
        $ads_model = new AdsModel();

        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $ad3_ids = [];

        foreach ($param->creative_list as $key => $value) {

            $ad_num = $key + 1;
            $ad_datetime = date('Y-m-d H:i:s');

            $data = $this->structureCreativePostData($param, $value);

            $data['account_id'] = $account_param->account_id;
            $data['access_token'] = $account_param->access_token;
            $data['campaign_id'] = $param->ad1_id;
            $data['adcreative_name'] = "{$param->ad2_id}_广告创意_{$ad_num}_{$ad_datetime}";
            $data['adcreative_template_id'] = $other_setting->template_id;
            if ($other_setting->live_video_mode == 'LIVE_VIDEO_MODE_LIVE') {

                $data["promoted_object_type"] = "PROMOTED_OBJECT_TYPE_WECHAT_CHANNELS";
                $data["promoted_object_id"] = $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? '';
                $data["live_video_mode"] = "LIVE_VIDEO_MODE_VIDEO"; // 视频号直播
                $data["live_video_sub_mode"] = "LIVE_VIDEO_SUBMODE_LIVE_ROOM"; // 视频号

                $data['page_type'] = 'PAGE_TYPE_CHANNELS_WATCH_LIVE';
                $data['page_spec']['wechat_channels_spec'] = [
                    'username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? '',
                    "live_recycle_flag" => 1
                ];

                if ($other_setting->live_ad_type == 'LIVE') {
                    $data["head_click_type"] = "HEAD_CLICK_TYPE_LIVE_PROFILE"; // 视频号
                    $data['head_click_spec'] = [
                        'finder_username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''
                    ];
                    $data['adcreative_elements'] = [
                        'wechat_channels_spec' => [
                            "username" => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? '',
                            "live_promoted_type" => "LIVE_PROMOTED_TYPE_NATIVE_VIDEO"
                        ]
                    ];
                } else {
                    $data['adcreative_elements'] = $this->getElementsStructure($other_setting->template_id, $value, $material_media_id_map, $param);
                    $data['adcreative_elements']['wechat_channels_spec']['live_promoted_type'] = 'LIVE_PROMOTED_TYPE_SHORT_VIDEO';
                    $data['adcreative_elements']['wechat_channels_spec']['username'] = $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? '';

                    if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                        $promoted_object_type = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
                    } else {
                        $promoted_object_type = $param->getSiteConfig()->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;
                    }

                    $data['wechat_channels_tracking_spec'] = [
                        "promoted_object_type" => $promoted_object_type,
                        "promoted_object_id" => $param->getSiteConfig()->appid
                    ];
                }
            } else {
                $data['adcreative_elements'] = $this->getElementsStructure($other_setting->template_id, $value, $material_media_id_map, $param);
            }

            $ad_creative_result = $create_model->add(new AdCreativeCreateParam($data));

            if ($ad_creative_result['adcreative_id']) {
                // 创建广告
                $ad_data = [
                    'account_id' => $account_param->account_id,
                    'access_token' => $account_param->access_token,
                    'adgroup_id' => $param->ad2_id,
                    'adcreative_id' => $ad_creative_result['adcreative_id'],
                    'ad_name' => "{$param->ad2_id}_{$ad_creative_result['adcreative_id']}_广告创意_{$ad_num}_{$ad_datetime}",
//                    'click_tracking_url' => $param->getSiteConfig()->action_track_url,
//                    'impression_tracking_url' => $param->getSiteConfig()->display_track_url,
                ];
                $ads_model->add(new AdCreateParam($ad_data));
                $ad3_ids[] = $ad_creative_result['adcreative_id'];
            } else {
                throw new AppException('创建广告失败:' . print_r($ad_creative_result, true));
            }
        }
        return $ad3_ids;
    }

    /**
     * 构造创意请求参数
     * @param ADTaskParam $param
     * @param $creative_info
     * @return array
     */
    protected function structureCreativePostData(ADTaskParam $param, $creative_info)
    {
        /** @var ADSettingContentParam $setting * */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        $data = [];

        // 朋友圈头像
        if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || $other_setting->site_set_none == 'auto') {
            if ($profile_id = $other_setting->getProfileInfo($param->account_id)->profile_id ?? '') {
                $data['profile_id'] = $profile_id;
                $data['link_name_type'] = $other_setting->link_name_type;
                $data['link_page_type'] = $this->getLinkType($param);
            } else if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                throw new AppException("找不到{$param->account_id}对应的头像信息");
            }
        }

        // 视频号
        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            $data['head_click_type'] = $other_setting->head_click_type;
            if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_DEFAULT) {
                $data['profile_id'] = $other_setting->getProfileInfo($param->account_id)->profile_id ?? '';
            }
            if ($other_setting->head_click_type === TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE && in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                $data['head_click_spec'] = ['finder_username' => $other_setting->getVideoNumberInfo($param->account_id)->promoted_object_id ?? ''];
                unset($data['profile_id']);
            }
        }

        // 自定义版位和自动版位创意规格
        // 修改了$other_setting->template_id
        if (!($param->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE && $other_setting->dynamic_creative_type == TencentEum::DYNAMIC_CREATIVE_TYPE_PROGRAM)) {
            if (!(in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) || in_array(TencentEum::SITE_SET_WECHAT, $other_setting->site_set))) {
                if (in_array($other_setting->site_set_none, ['zidingyi', 'auto'])) {
                    $other_setting->template_id = '';
                }
                if (isset($creative_info['cover_info'])) {
                    if ((int)$creative_info['video_info']['width'] > (int)$creative_info['video_info']['height']) {
                        $other_setting->template_id = !empty($other_setting->template_id) ? $other_setting->template_id : 720;
                    } else {
                        if ((int)$creative_info['video_info']['width'] == 1080) {
                            $other_setting->template_id = !empty($other_setting->template_id) ? $other_setting->template_id : 1529;
                        } else {
                            $other_setting->template_id = !empty($other_setting->template_id) ? $other_setting->template_id : 721;
                        }
                    }
                } else {
                    if ((int)$creative_info['image_info']['width'] > (int)$creative_info['image_info']['height']) {
                        if ((int)$creative_info['image_info']['width'] == 960 && (int)$creative_info['image_info']['height'] == 274) {
                            $other_setting->template_id = !empty($other_setting->template_id) ? $other_setting->template_id : 713;
                        } else {
                            $other_setting->template_id = !empty($other_setting->template_id) ? $other_setting->template_id : 711;
                        }
                    } else {
                        $other_setting->template_id = !empty($other_setting->template_id) ? $other_setting->template_id : 712;
                    }

                }
            }
        }

        if ($other_setting->site_set_none == 'search') {
            $other_setting->template_id = 2083;
        }

        // 微信朋友圈版位突破
        if ($other_setting->isWechatSingleSite()) {
            $data['enable_breakthrough_siteset'] = $other_setting->enable_breakthrough_siteset ?? false;
        }

        // 微信朋友圈
        if ((in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set))) {
            // 文字链
            if ($other_setting->link_name_type) {
                $data['link_name_type'] = $other_setting->link_name_type;
                $data['link_page_type'] = $this->getLinkType($param);
            }
        }

        // 礼包码
        if ($other_setting->app_gift_pack_switch) {
            $data['app_gift_pack_code'] = array_filter([
                'code' => $other_setting->app_gift_pack_code_code,
                'tips' => $other_setting->app_gift_pack_code_tips,
                'description' => $other_setting->app_gift_pack_code_description,
            ]);
        }

        // 落地页参数
        $page_info = $other_setting->getPageInfo($param->account_id);

        if ($page_id = $page_info->page_id ?? '') {

            $data['page_spec'] = ['page_id' => $page_id];

            // 微信朋友圈单版位
            if (in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set) && count($other_setting->site_set) === 1) {
                $data['page_spec']['override_canvas_head_option'] = 'OPTION_CREATIVE_OVERRIDE_CANVAS';
            }

            // 文字链类型 (包含朋友圈)
            if ((in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) &&
                $other_setting->link_name_type
            ) {
                $data['link_page_spec'] = [
                    'page_id' => $page_id
                ];
            } else {
                $data['link_page_type'] = 'LINK_PAGE_TYPE_NOT_USED';
            }

            // 不支持文字链的创意规格
            if (in_array($other_setting->template_id, [450, 452, 589, 588, 311])) {
                unset(
                    $data['link_page_type'],
                    $data['link_name_type'],
                    $data['link_page_spec']
                );
            }
        }

        // 选了哪种落地页就是哪种类型了 否则就默认了
        $data['page_type'] = $page_info->page_type ?? 'PAGE_TYPE_DEFAULT';
        $data['promoted_object_type'] = $param->getSiteConfig()->game_type == 'IOS' ? TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS : TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID;

        // 小游戏
        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $data['promoted_object_type'] = TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT;
            $data['page_spec']['mini_game_spec']['mini_game_tracking_parameter'] = "?game_id={$param->getSiteConfig()->game_id}&agent_id={$param->agent_id}&site_id={$param->site_id}";
            if ($other_setting->is_xijing == 4) {
                $data['page_type'] = 'PAGE_TYPE_MINI_GAME_WECHAT'; // 微信小游戏落地页类型
                if ($other_setting->link_name_type && in_array(TencentEum::SITE_SET_MOMENTS, $other_setting->site_set)) {
                    $data['link_page_type'] = 'LINK_PAGE_TYPE_MINI_GAME_WECHAT';
                    $data['link_name_type'] = $other_setting->link_name_type;
                }
            }
        }

        $data['promoted_object_id'] = $param->getSiteConfig()->appid;
        $data['site_set'] = $other_setting->site_set;
        if ($other_setting->site_set_none == 'auto') {
            $data['automatic_site_enabled'] = true;
            $data['site_set'] = [];
        }

        // 自定义创意
        if ($param->creative_mode == BatchAD::CREATIVE_CUSTOM_MODE) {
            if ($other_setting->isWechatSingleSite()) {
                if ($setting->conversion_data_type == 'CONVERSION_DATA_ADMETRIC' && $setting->conversion_target_type && in_array($other_setting->template_id, [311, 641, 642, 643, 618, 721])) {
                    $data['conversion_data_type'] = $setting->conversion_data_type;
                    $data['conversion_target_type'] = $setting->conversion_target_type;
                }
            } else {
                $data['conversion_data_type'] = $data['conversion_target_type'] = '';
            }
        }

        return $data;
    }

    /**
     * 获取文字链类型
     * @param ADTaskParam $param
     * @return string
     */
    protected function getLinkType(ADTaskParam $param)
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        // 落地页参数
        $page_info = $other_setting->getPageInfo($param->account_id);

        if ($other_setting->is_xijing == 4) {
            return 'LINK_PAGE_TYPE_MINI_GAME_WECHAT';
        }

        // 落地页必传
        if (!($page_info->page_type ?? '')) {
            return 'LINK_PAGE_TYPE_DEFAULT';
        }

        // 蹊径落地页
        if ($page_info->page_type == 'PAGE_TYPE_TSA_APP') {
            if ($param->getSiteConfig()->game_type == 'IOS') {
                return 'LINK_PAGE_TYPE_XJ_IOS_APP_H5';
            }
            if ($param->getSiteConfig()->game_type == '安卓') {
                return 'LINK_PAGE_TYPE_XJ_ANDROID_APP_H5';
            }
        }

        // 蹊径性能版
        if ($page_info->page_type == 'PAGE_TYPE_XIJING_QUICK') {
            return 'LINK_PAGE_TYPE_XJ_QUICK';
        }

        // 微信原生页
        if ($page_info->page_type == 'PAGE_TYPE_CANVAS_WECHAT') {
            return 'LINK_PAGE_TYPE_CANVAS_WECHAT';
        }

        // 默认落地页
        if ($page_info->page_type == 'PAGE_TYPE_DEFAULT') {
            return 'LINK_PAGE_TYPE_DEFAULT';
        }

        return 'LINK_PAGE_TYPE_NOT_USED';
    }

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        return '';
    }

    /**
     * 根据ids获取人群包内容
     * @param array $ids
     * @param int $status
     * @return mixed
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        return [];
    }

    /**
     * 获取计划行为徕卡
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        $tx_ad_log_model = new OdsTencentAdGroupLogModel();
        $list = $tx_ad_log_model->getAdList($condition['ad_name'] ?? '');
        $return_list = [];
        foreach ($list as $key => $value) {
            $behavior_or_interest = json_decode($value->behavior_or_interest, true);
            if (isset($behavior_or_interest['behavior'])) {
                $return_list[] = [
                    'adgroup_id' => $value->adgroup_id,
                    'adgroup_name' => $value->adgroup_name,
                    'behavior' => $behavior_or_interest['behavior'][0],
                ];
            }
        }
        return $return_list;
    }

    /**
     * 获取计划兴趣徕卡
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        $tx_ad_log_model = new OdsTencentAdGroupLogModel();
        $list = $tx_ad_log_model->getAdList($condition['ad_name'] ?? '');
        $return_list = [];
        foreach ($list as $key => $value) {
            $behavior_or_interest = json_decode($value->behavior_or_interest, true);
            if (isset($behavior_or_interest['interest'])) {
                $return_list[] = [
                    'adgroup_id' => $value->adgroup_id,
                    'adgroup_name' => $value->adgroup_name,
                    'interest' => $behavior_or_interest['interest'],
                ];
            }

        }
        return $return_list;
    }

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        if (isset($data['category_id_list'])) {
            $tag_spec = [];
            $tag_spec['behavior_spec'] = [
                'query_mode' => 'TARGETING_TAG_QUERY_MODE_COMMON',
            ];
            $account_info = (new MediaAccountModel())->getRandomDataByMediaType(MediaType::TENCENT);
            if ($account_info) {
                $behavior_tag_info = (new TargetingTagsModel())->getTargetingTags(
                    $account_info->account_id,
                    $account_info->access_token,
                    'BEHAVIOR',
                    $tag_spec
                );
                if (isset($behavior_tag_info['list'])) {
                    foreach ($behavior_tag_info['list'] as $v) {
                        foreach ($data['category_id_list'] as $id) {
                            if ((int)$v['id'] === (int)$id) {
                                $data['category_id_list_map'][] = [
                                    'id' => "{$v['id']}",
                                    'name' => $v['name'],
                                ];
                            }
                        }
                    }
                }
            }
        }
        return $data;
    }

    /**
     * 获取计划徕卡兴趣id转词汇
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        if (isset($data['category_id_list'])) {
            $tag_spec = [];
            $tag_spec['interest_spec'] = [
                'query_mode' => 'TARGETING_TAG_QUERY_MODE_COMMON',
            ];
            $account_info = (new MediaAccountModel())->getRandomDataByMediaType(MediaType::TENCENT);
            if ($account_info) {
                $behavior_tag_info = (new TargetingTagsModel())->getTargetingTags(
                    $account_info->account_id,
                    $account_info->access_token,
                    'INTEREST',
                    $tag_spec
                );
                if (isset($behavior_tag_info['list'])) {
                    foreach ($behavior_tag_info['list'] as $v) {
                        foreach ($data['category_id_list'] as $id) {
                            if ((int)$v['id'] === (int)$id) {
                                $data['category_id_list_map'][] = [
                                    'id' => "{$v['id']}",
                                    'name' => $v['name'],
                                ];
                            }
                        }
                    }
                }
            }
        }
        return $data;
    }

    /**
     * 获取流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return array
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        return (new OdsTencentUnionPositionPackagesLogModel())->getList($name, $account_id, $page, $row);
    }

    /**
     * @param $advertiser_id
     * @param $access_token
     * @param array $package_params
     * @return mixed
     */
    public function uploadFlow($advertiser_id, $access_token, $package_params)
    {
        if (!$package_params['union_position_id_list']) {
            return [];
        }
        $flow_package_model = new UnionPositionPackagesModel();
        $union_package_name = $package_params['union_package_name'];
        $union_package_type = $package_params['union_package_type'];
        $union_position_id_list = json_encode($package_params['union_position_id_list']);
        return $flow_package_model->createUnionPositionPackages(
            $advertiser_id, $access_token, $union_package_name, $union_package_type, $union_position_id_list);
    }

    /**
     * @param $flow_md5_list
     * @param $account_id
     * @return Collection
     */
    public function getAlreadyExistFlowMd5List($flow_md5_list, $account_id)
    {
        $tcal_model = new OdsTencentUnionPositionPackagesLogModel();
        return $tcal_model->getAlreadyExistFlowList($flow_md5_list, $account_id)->keyBy('union_position_id_md5');
    }

    /**
     * 更新流量包上传成功的重置参数
     * @param int $flow_package_id
     * @param array $flow_rit_id_list
     * @return mixed
     */
    public function getSuccessFlowMap(int $flow_package_id, array $flow_rit_id_list)
    {
        sort($flow_rit_id_list['union_position_id_list']);
        $rit_md5 = $flow_rit_id_list['union_position_id_list'] ? md5(json_encode($flow_rit_id_list['union_position_id_list'])) : md5($flow_rit_id_list['union_package_name']);
        $reset_flow_map[$rit_md5] = [
            'union_package_id' => $flow_package_id,
            'union_position_id_md5' => $rit_md5,
            'union_package_name' => $flow_rit_id_list['union_package_name'],
        ];
        return $reset_flow_map;
    }

    /**
     * 请求结果提取流量包ID
     * @param $request
     * @return int
     */
    public function getFlowPackageIdByRequest($request)
    {
        return $request['union_package_id'] ?? 0;
    }

    /**
     * 同步流量包
     * @param $account_id
     * @return array
     */
    public function syncFlowPackage($account_id)
    {
        return (new GDTTaskModel)->unionPositionPackages($account_id);
    }

    /**
     * 修改广告计划-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告计划的开关-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告创意的开关-返回修改的广告创意id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改腾讯系各级广告开关
     * @param $input
     * @param $format_target
     * @param $media_type
     * @return array
     */
    public function updateADAnalysisStatus($media_type, $input, $format_target)
    {
        if (3 === $input['ad_level']) {
            //腾讯三级
            throw new AppException('腾讯/微信广告三级禁止开关');
        } elseif (2 === $input['ad_level']) {
            //腾讯系二级
            if (MediaType::TENCENT === $media_type) {
                $ad_model = new OdsTencentAdGroupLogModel();
                $ad_his_log_model = new OdsTencentAdGroupHisLogModel();
            } else {
                $ad_model = new OdsWeChatAdGroupLogModel();
                $ad_his_log_model = new OdsWechatAdGroupHisLogModel();
            }
            $http_model = new AdGroupModel();
            $ad_id_type = 'adgroup_id';
            $ad_name_type = 'adgroup_name';
        } else {
            //腾讯系一级
            if (MediaType::TENCENT === $media_type) {
                $ad_model = new OdsTencentCampaignLogModel();
                $ad_his_log_model = new OdsTencentCampaignHisLogModel();
            } else {
                $ad_model = new OdsWeChatCampaignLogModel();
                $ad_his_log_model = new OdsWechatCampaignHisLogModel();
            }
            $http_model = new CampaignModel();
            $ad_id_type = 'campaign_id';
            $ad_name_type = 'campaign_name';
        }
        $close = 'AD_STATUS_SUSPEND';
        $open = 'AD_STATUS_NORMAL';

        $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        $result = $ad_model->getAccountId($format_target, $column);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];
        if (0 === $input['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = 2;
            $request_data['configured_status'] = $close;
            $switch_option = $close;
            $edit_detail = '关闭';
        } else {
            $single_record_operate['edit_type'] = 1;
            $request_data['configured_status'] = $open;
            $switch_option = $open;
            $edit_detail = '开启';
        }

        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($result as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['ad_level'] = $input['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update_switch = [];
        foreach ($result as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->$ad_id_type;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data[$ad_id_type] = (int)$v->$ad_id_type;
            try {
                $http_model->updateForADAnalysisMultiEdit($request_data, $access_tokens[$v->account_id]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;
                $update_switch[$v->account_id][] = $request_data[$ad_id_type];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "失败，错误信息：" .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }

            $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->$ad_name_type;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_type;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update_switch && $media_type === MediaType::TENCENT) {
            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                'configured_status',
                $switch_option,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表一级广告
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisFirstClass(array $data)
    {
        if ((int)$data['media_type'] === MediaType::TENCENT) {
            $tencent_model = new OdsTencentCampaignLogModel();
        } else {
            $tencent_model = new OdsWeChatCampaignLogModel();
        }
        $account_info = $tencent_model->getAccountIdAndPlatform($data);
        if (!$account_info) {
            throw new AppException('修改失败');
        }
        $tencent_http_model = new CampaignModel();
        //获取access_token并注入权限
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$account_info->account_id], null, $leader_permission);
        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 1;
        $record_operate['media_type'] = $data['media_type'];
        $record_operate['editor_id'] = $data['editor_id'];
        $record_operate['edit_type'] = 5;
        $record_operate['platform'] = $account_info->platform;
        $record_operate['editor_name'] = $data['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $record_operate['account_id'] = (int)$account_info->account_id;
        $record_operate['ad_id'] = $return_data['ad_id'] = (int)$data['ad_id'];

        if ($access_token->isNotEmpty()) {
            $ad_info = [];
            $access_token = $access_token->pop()->access_token;
            $request_data['account_id'] = (int)$account_info->account_id;
            $request_data['campaign_id'] = (int)$data['ad_id'];
            try {
                $tencent_http_model->updateForADAnalysisMultiEdit($request_data, $access_token);
                //修改成功
                $return_data['message'] = 'success';
                $record_operate['ad_name'] = $return_data['value'] = $data['ad_group_name'];
                $record_operate['status'] = $return_data['status'] = 1;
                $record_operate['edit_detail'] = "广告组名称由[ {$account_info->campaign_name} ] " .
                    "修改为 [ {$data['ad_group_name']} ]";
                $ad_info[$request_data['account_id']] = [$request_data['campaign_id']];
            } catch (AppException $e) {
                //修改失败
                $return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $record_operate['edit_detail'] = "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
                $record_operate['status'] = $return_data['status'] = 2;
                $record_operate['ad_name'] = $account_info->campaign_name;
                $return_data['value'] = 0;
            }

            if ($ad_info && (int)$data['media_type'] === MediaType::TENCENT) {
                (new OdsTencentCampaignHisLogModel())->getLatestByADInfoAndInsert(
                    $ad_info,
                    'campaign_name',
                    $data['ad_group_name'],
                    $record_operate['insert_time']
                );
            }
        } else {
            //没有权限操作
            $return_data['message'] = $record_operate['edit_detail'] = '当前用户无操作权限';
            $return_data['value'] = 0;
            $record_operate['status'] = $return_data['status'] = 2;
            $record_operate['ad_name'] = $account_info->campaign_name;
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [$return_data];

    }

    /**
     * 修改基本报表二级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        //腾讯系二级
        if (MediaType::TENCENT === $media_type) {
            $ad_model = new OdsTencentAdGroupLogModel();
        } else {
            $ad_model = new OdsWeChatAdGroupLogModel();
        }

        $column = ['adgroup_id', 'adgroup_name', 'account_id', 'platform'];
        $account_info = $ad_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];
        //避免循环查库，手动来组合广告数据
        if (isset($data['ad_name'])) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = 6;
            $update_value = $request_data['adgroup_name'] = $data['ad_name'];
            $edit_detail = '广告二级名';
            $update_field = 'adgroup_name';
        } else {
            //修改schedule_time
            $single_record_operate['edit_type'] = 4;
            $update_value = $request_data['time_series'] = $data['schedule_time'];
            $edit_detail = '广告投放时段';
            $update_field = 'time_series';
        }

        $http_model = new AdGroupModel();
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->adgroup_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data['adgroup_id'] = (int)$v->adgroup_id;
            try {
                $http_model->updateForADAnalysisMultiEdit($request_data, $access_tokens[$v->account_id]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $update_value;
                $update[$v->account_id][] = $request_data['adgroup_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "修改成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "修改失败，错误信息：" .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->adgroup_name;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->adgroup_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update && $media_type === MediaType::TENCENT) {
            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_value,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表预算和出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $update_field = 'budget' === $update_field ? 'daily_budget' : 'bid_amount';
        //腾讯系二级
        if (MediaType::TENCENT === $media_type) {
            $ad_model = new OdsTencentAdGroupLogModel();
        } else {
            $ad_model = new OdsWeChatAdGroupLogModel();
        }
        $column = ['adgroup_id', 'adgroup_name', 'account_id', 'platform', $update_field];
        $account_info = $ad_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = 3;
//        $request_type = 'daily_budget';
        $opt_cn_name = '预算';
        if ('bid_amount' === $update_field) {
//            $request_type = 'bid_amount';
            $single_record_operate['edit_type'] = 7;
            $opt_cn_name = '出价';
        }
        $http_model = new AdGroupModel();
        //存储所有需要定时修改的数据
        $timing_execute_data = [];
        //即时修改到数据库的数据
        $update_data = [];
        $request_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_return_data['value'] = 0;
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->adgroup_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            //保存原始值
            $original_value = $v->$update_field;
            if (1 === (int)$data['change_type']) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === (int)$data['change_type']) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } elseif (3 === (int)$data['change_type']) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } else {
                throw new AppException('参数错误');
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data['adgroup_id'] = (int)$v->adgroup_id;
            $request_data[$update_field] = $v->$update_field * 100;

            if (1 === (int)$data['execute_type']) {
                //即时修改，直接请求腾讯修改
                try {
                    $http_model->updateForADAnalysisMultiEdit($request_data, $access_tokens[$v->account_id]);
                    //修改成功
                    $update_data[$v->account_id][] = $v->adgroup_id;
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = (string)$v->$update_field;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name . '修改成功，由[' . $original_value .
                        ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    //修改失败
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //操作失败
                    $single_return_data['message'] = $e->getMessage();
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name . "修改失败，错误信息：" . $failed_info;
                }
            } elseif (0 === (int)$data['execute_type']) {
                //定时修改，将请求数据汇总之后放入RabbitMQ
                $single_return_data['status'] = $single_record_operate['status'] = 1;
                $single_return_data['value'] = $original_value;
                $single_return_data['message'] = $single_record_operate['edit_detail'] =
                    $opt_cn_name . '修改准备' . $data['execute_time'] . '定时执行，由[' . $original_value .
                    ']修改为[' . (string)$v->$update_field . ']';
                //$request_data存入session信息,access_token消费队列时重新查
                $request_data['editor_id'] = $single_record_operate['editor_id'];
                $request_data['editor_name'] = $single_record_operate['editor_name'];
                $request_data['media_type'] = $media_type;
                $request_data['request_type'] = $update_field;
                $timing_execute_data[] = $request_data;
            }
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->adgroup_id;
            $single_record_operate['ad_name'] = $v->adgroup_name;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update_data && $media_type === MediaType::TENCENT) {
            //组合要update的sql
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, 2 ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        if ($timing_execute_data) {
            $time = strtotime($data['execute_time']) - time();
            if ('bid_amount' === $update_field) {
                (new ADAnalysisMQLogic())->produceDelayChangeBidTask($timing_execute_data, $time);
            } elseif ('daily_budget' === $update_field) {
                (new ADAnalysisMQLogic())->produceDelayChangeBudgetTask($timing_execute_data, $time);
            }
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 处理基本报表定时修改出价预算RabbitMQ
     * @param int $media_type
     * @param array $data
     * @return mixed|void
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        //腾讯系二级
        if (MediaType::TENCENT === $media_type) {
            $ad_model = new OdsTencentAdGroupLogModel();
        } else {
            $ad_model = new OdsWeChatAdGroupLogModel();
        }
        //腾讯二级表查询条件
        $condition = [];
        foreach ($data as $v) {
            $condition[$v['account_id']][] = $v['adgroup_id'];
        }
        unset($v);
        if (!$condition) {
            return;
        }
        $account_ids = array_keys($condition);
        $ad_info = $ad_model->getADInfoByAccountIdAndADId($condition);
        $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids);
        if ($ad_info->isEmpty() || $access_token_info->isEmpty()) {
            return;
        }
        //整合access_token方便后面使用
        $access_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
        }
        unset($v);

        $http_model = new AdGroupModel();
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];

        $opt_cn_name = ['bid_amount' => '出价', 'daily_budget' => '预算'];
        foreach ($ad_info as $value) {
            foreach ($data as $v) {
                if ((int)$value->adgroup_id !== (int)$v['adgroup_id'] ||
                    (int)$value->account_id !== (int)$v['account_id']) {
                    continue;
                }
                $request_type = $v['request_type'];
                $single_record_operate['edit_type'] = $request_type === 'bid_amount' ? 7 : 3;
                $request_data = [];
                $request_data['account_id'] = (int)$v['account_id'];
                $request_data['adgroup_id'] = (int)$v['adgroup_id'];
                $request_data[$request_type] = $v[$request_type];

                try {
                    $http_model->updateForADAnalysisMultiEdit($request_data, $access_token[$v['account_id']]);
                    //修改成功
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = Math::div($v[$request_type], 100, 2);
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$request_type] . '定时修改成功，由[' .
                        $value->$request_type . ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //修改失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //操作失败
                    $single_return_data['message'] = $e->getMessage();
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$request_type] . "定时修改失败，错误信息：" .
                        $failed_info;
                }
                $single_record_operate['platform'] = $value->platform;
                $single_record_operate['account_id'] = (int)$value->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$value->adgroup_id;
                $single_record_operate['ad_name'] = $value->adgroup_name;
                $single_record_operate['editor_id'] = $v['editor_id'];
                $single_record_operate['editor_name'] = $v['editor_name'];
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($v);
        }
        unset($value);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
    }

    /**
     * 修改基本报表定向
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        $access_token = [];
        $platform_for_targeting = '';
        $account_ids_for_targeting = [];
        foreach ($account_info as $item) {
            $access_token[$item->account_id] = $item->access_token;
            if (!$platform_for_targeting) {
                $platform_for_targeting = $item->platform;
            }
            $account_ids_for_targeting[] = $item->account_id;
        }
        unset($item);

        $request_data = [];
        if ($platform_for_targeting) {
            $targeting_info = (new ADServingLogic())->pushAudienceForAnalysis(
                (int)$data['targeting_id'],
                (string)$platform_for_targeting,
                $account_ids_for_targeting
            );
            //推送人群包并获取定向包内容
            $request_data['targeting'] = $targeting_info['targeting'];
            $request_data['expand_enabled'] = $targeting_info['expand_enabled'];
            $request_data['expand_targeting'] = $targeting_info['expand_targeting'];
        }
        $tencent_http_model = new AdGroupModel();
        //单条操作记录
        $single_record_operate = [];

        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价,
        // 9: 修改定向
        $single_record_operate['edit_type'] = 9;
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $data['media_type'];
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        $single_return_data['value'] = 0;
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($data['ad_info'] as $v) {
            $single_record_operate['ad_name'] = $v['ad_name'];
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_record_operate['platform'] = $v['platform'];
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v['ad_id'];

            if (!isset($access_token[$v['account_id']])) {
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['account_id'] = (int)$v['account_id'];
            $request_data['adgroup_id'] = (int)$v['ad_id'];
            try {
                $tencent_http_model->updateForADAnalysisMultiEdit(http_build_query($request_data), $access_token[$v['account_id']]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = "定向修改成功";
            } catch (AppException $e) {
                //修改失败
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '定向修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    public function getTargetingDataByAD2($company, $audience_md5)
    {
        throw new AppException('腾讯暂时不支持，复制计划定向');
    }

    /**
     * 补充定向包信息
     * @param array $targeting_info
     * @param bool $is_return
     * @return array
     */
    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        $targeting_info['geo_location']['regions'] = $targeting_info['geo_location']['regions'] ?? [];
        $targeting_info['geo_location']['regions_none'] = count($targeting_info['geo_location']['regions']) > 0 ? 'zidingyi' : 'buxian';
        foreach ($targeting_info['geo_location']['regions'] as $region_id) {
            $targeting_info['geo_location']['regions_map'][] = [
                'id' => $region_id,
                'name' => $region_id
            ];
        }

        $targeting_info['age']['age_none'] = ($targeting_info['age_max'] ?? '' && $targeting_info['age_max'] ?? '') ? 'zidingyi' : 'buxian';

        $targeting_info['education_none'] = $targeting_info['education'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['financial_situation_none'] = $targeting_info['financial_situation'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['consumption_type_none'] = $targeting_info['consumption_type'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['consumption_status_none'] = $targeting_info['consumption_status'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['game_consumption_level_none'] = $targeting_info['game_consumption_level'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['new_device_none'] = $targeting_info['new_device'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['network_scene_none'] = $targeting_info['network_scene'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['network_type_none'] = $targeting_info['network_type'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['network_operator_none'] = $targeting_info['network_operator'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['device_price_none'] = $targeting_info['device_price'] ?? [] ? 'zidingyi' : 'buxian';
        $targeting_info['user_os_none'] = ($targeting_info['user_os_ios'] ?? '' && $targeting_info['user_os_android'] ?? '') ? 'zidingyi' : 'buxian';
        $targeting_info['gamer_consumption_ability']['gamer_consumption_ability_none'] = ($targeting_info['gamer_consumption_ability_min'] ?? '' && $targeting_info['gamer_consumption_ability_max'] ?? '') ? 'zidingyi' : 'buxian';
        $targeting_info['zidingyirenqun'] = ($targeting_info['custom_audience'] ?? '' && $targeting_info['excluded_custom_audience'] ?? '') ? 'zidingyi' : 'buxian';

        $targeting_info['behavior_or_interest_none'] = 'none';
        $targeting_info['behavior_or_interest']['interest']['category_id_list_map'] = [];
        foreach ($targeting_info['behavior_or_interest']['interest']['category_id_list'] as $category_id) {
            $targeting_info['behavior_or_interest']['interest']['category_id_list_map'][] = [
                'id' => $category_id,
                'name' => $category_id,
            ];
            $targeting_info['behavior_or_interest_none'] = 'zidingyi';
        }

        $targeting_info['behavior_or_interest']['behavior']['category_id_list_map'] = [];
        foreach ($targeting_info['behavior_or_interest']['behavior']['category_id_list'] as $category_id) {
            $targeting_info['behavior_or_interest']['behavior']['category_id_list_map'][] = [
                'id' => $category_id,
                'name' => $category_id,
            ];
            $targeting_info['behavior_or_interest_none'] = 'zidingyi';
        }

        $targeting_info['behavior_or_interest']['intention']['targeting_tags_map'] = [];
        foreach ($targeting_info['behavior_or_interest']['intention']['targeting_tags'] as $targeting_id) {
            $targeting_info['behavior_or_interest']['intention']['targeting_tags_map'][] = [
                'id' => $targeting_id,
                'name' => $targeting_id,
            ];
            $targeting_info['behavior_or_interest_none'] = 'zidingyi';
        }

        foreach ($targeting_info['custom_audience'] as $audience_id) {
            $targeting_info['custom_audience_map'][] = [
                'audience_id' => $audience_id,
                'name' => $audience_id,
            ];
        }

        foreach ($targeting_info['excluded_custom_audience'] as $audience_id) {
            $targeting_info['excluded_custom_audience_map'][] = [
                'audience_id' => $audience_id,
                'name' => $audience_id,
            ];
        }

        foreach ($targeting_info['cold_start_audience'] as $audience_id) {
            $targeting_info['cold_start_audience_map'][] = [
                'audience_id' => $audience_id,
                'name' => $audience_id,
            ];
        }

        $targeting_info['mobile_union_industry_none'] = 'buxian';
        $targeting_info['mobile_union_industry'] = '';

        return $targeting_info;
    }

    /**
     * @param array $create_data
     * @return int
     */
    public function addADTaskByApi(array $create_data)
    {
        if ($create_data['account_id']) {
            $account_info = (new MediaAccountModel())->getDataByAccountId($create_data['account_id'], MediaType::TENCENT);
            if ($account_info) {
                $create_data['company'] = $account_info->company;
                $create_data['account_name'] = $account_info->account_name;
            } else {
                throw new AppException('找不到账号信息');
            }
        } else {
            throw new AppException('account_id为空');
        }

        $create_list = new ADComposeCreativeListParam();
        foreach ($create_data['creative_list'] as $create_info) {
            if (isset($create_info['cover_info'])) {
                $create_list->addVideoToList(
                    $create_info['title'],
                    new ADComposeCreativeVideoCoverParam([
                        'id' => $create_info['cover_info']['id'],
                        'url' => $create_info['cover_info']['url'],
                        'width' => $create_info['cover_info']['width'],
                        'height' => $create_info['cover_info']['height'],
                    ]),
                    new ADComposeCreativeVideoParam([
                        'id' => $create_info['video_info']['id'],
                        'url' => $create_info['video_info']['url'],
                        'width' => $create_info['video_info']['width'],
                        'height' => $create_info['video_info']['height'],
                    ])
                );
            } else {
                $create_list->addImageToList(
                    $create_info['title'],
                    new ADComposeCreativeImageParam([
                        'id' => $create_info['image_info']['id'],
                        'url' => $create_info['image_info']['url'],
                        'width' => $create_info['image_info']['width'],
                        'height' => $create_info['image_info']['height'],
                    ])
                );
            }
        }

        $targeting_data = [
            'geo_location' => [
                'regions' => $create_data['geo_location_regions'] ?? [],
                'location_types' => $create_data['geo_location_location_types'] ?? ['LIVE_IN'],
            ],
            'age' => [
                'min' => $create_data['age_min'],
                'max' => $create_data['age_max'],
            ],
            'gender' => $create_data['gender'],
            'education' => $create_data['education'],
            'financial_situation' => $create_data['financial_situation'],
            'consumption_type' => $create_data['consumption_type'],
            'consumption_status' => $create_data['consumption_status'],
            'game_consumption_level' => $create_data['game_consumption_level'],
            'new_device' => $create_data['new_device'],
            'gamer_consumption_ability' => [
                'min' => $create_data['gamer_consumption_ability_min'],
                'max' => $create_data['gamer_consumption_ability_max'],
            ],
            'device_brand_model_type' => $create_data['device_brand_model_type'],
            'device_brand_model_list' => $create_data['device_brand_model_list'],
            'custom_audience' => $create_data['custom_audience'],
            'excluded_custom_audience' => $create_data['excluded_custom_audience'],
            'behavior_or_interest' => [
                'interest' => [
                    'category_id_list' => $create_data['interest_category_id_list'],
                    'keyword_list' => $create_data['interest_keyword_list']
                ],
                'behavior' => [
                    'category_id_list' => $create_data['behavior_category_id_list'],
                    'keyword_list' => $create_data['behavior_keyword_list'],
                    'scene' => $create_data['behavior_scene'],
                    'time_window' => $create_data['behavior_time_window'],
                    'intensity' => $create_data['behavior_intensity'],
                ],
                'intention' => [
                    'targeting_tags' => $create_data['intention_targeting_tags']
                ]
            ],
            'app_install_status' => $create_data['app_install_status'],
            'network_scene' => $create_data['network_scene'],
            'user_os_ios' => $create_data['user_os_ios'],
            'user_os_android' => $create_data['user_os_android'],
            'network_type' => $create_data['network_type'],
            'network_operator' => $create_data['network_operator'],
            'device_price' => $create_data['device_price'],
            'expand_enabled' => $create_data['expand_enabled'] == 1 ? 'true' : 'false',
            'expand_targeting' => $create_data['expand_targeting'],
            'auto_audience' => $create_data['auto_audience'],
            'cold_start_audience' => $create_data['cold_start_audience'],
        ];

        $targeting_data = $this->fillTargetingInfo($targeting_data, true);

        $targeting_pa = new ADTargetingContentParam($targeting_data);
        $targeting_pa->format(ADTargetingPacketParam::FINISH_STATE);

        $setting_data = [
            'is_xijing' => $create_data['is_xijing'],
            'campaign_name' => $create_data['campaign_name'],
            'campaign_name_text' => $create_data['campaign_name_text'] ?? '',
            'ad_name' => $create_data['ad_name'],
            'ad_name_text' => $create_data['ad_name_text'] ?? '',
            'word_num_in_ad' => $create_data['word_num_in_ad'],
            'pic_num_in_ad' => $create_data['pic_num_in_ad'],
            'video_num_in_ad' => $create_data['video_num_in_ad'],
            'word_type' => (int)($create_data['word_type'] ?? 1),
            'campaign_type' => 'CAMPAIGN_TYPE_NORMAL',
            'promoted_object_type' => $create_data['promoted_object_type'],
            'site_set' => $create_data['site_set'],
            'template_id' => $create_data['template_id'],
            'scene_spec' => [
                'display_scene_none' => $create_data['scene_spec_display_scene'] ? 'zidingyi' : 'buxian',
                'display_scene' => $create_data['scene_spec_display_scene']
            ],
            'campaign_configured_status' => $create_data['campaign_configured_status'],
            'ad_configured_status' => $create_data['ad_configured_status'],
            'schedule_type' => ($create_data['begin_date'] ?? '' || $create_data['end_date'] ?? '') ? 'buxian' : 'zidingyi',
            'begin_date' => $create_data['begin_date'],
            'end_date' => $create_data['end_date'],
            'time_series' => $create_data['time_series'],
            'campaign_daily_budget_none' => $create_data['campaign_daily_budget'] > 0 ? 'zidingyi' : 'buxian',
            'campaign_daily_budget' => $create_data['campaign_daily_budget'],
            'speed_mode' => $create_data['speed_mode'],
            'ad_daily_budget_none' => $create_data['ad_daily_budget'] > 0 ? 'zidingyi' : 'buxian',
            'optimization_goal' => ConvertType::MEDIA[MediaType::TENCENT][$create_data['convert_type']],
            'ad_daily_budget' => $create_data['ad_daily_budget'],
            'bid_strategy' => $create_data['bid_strategy'],
            'billing_event' => 'BILLINGEVENT_IMPRESSION',
            'bid_amount' => $create_data['bid_amount'],
            'deep_conversion_spec_none' => $create_data['deep_conversion_spec_none'] ?? 'false',
            'deep_conversion_spec' => $create_data['deep_conversion_spec'],
            'idea_type' => $create_data['idea_type'],
            'brand_img' => $create_data['brand_img'],
            'brand_name' => $create_data['brand_name'],
            'title' => $create_data['title'],
            'button_text' => $create_data['button_text'],
            'bottom_text' => $create_data['bottom_text'],
            'label' => $create_data['label'],
            'page_map' => $create_data['page_map'],
            'profile_map' => $create_data['profile_map'],
            'link_name_type' => $create_data['link_name_type'],
            'conversion_target_type' => $create_data['conversion_target_type'],
            'conversion_data_type' => $create_data['conversion_data_type'],
            'excluded_dimension_mode' => $create_data['excluded_dimension'] ? 'zidingyi' : 'none',
            'excluded_dimension' => $create_data['excluded_dimension'],
            'is_bid_coefficient' => '0',
            'site_set_bid_coefficient' => [],
            'is_deep_bid_coefficient' => '0',
            'site_set_deep_bid_coefficient' => [],
            'auto_acquisition_enabled' => $create_data['auto_acquisition_enabled'],
            'auto_acquisition_budget' => $create_data['auto_acquisition_budget'],
            'site_set_none' => 'zidingyi',
            'corporate_name' => $create_data['corporate_name'] ?? '',
            'corporate_img' => $create_data['corporate_img'] ?? '',
        ];

        $setting_pa = new ADSettingContentParam($setting_data);
        $setting_pa->format();

        $task_param = (new ADTaskParam([
            'compose_id' => 0,
            'company' => $create_data['company'],
            'platform' => $create_data['platform'],
            'media_type' => $create_data['media_type'],
            'creative_mode' => count($create_data['word_list']) > 0 ? BatchAD::CREATIVE_PROGRAM_MODE : BatchAD::CREATIVE_CUSTOM_MODE,
            'media_agent_type' => 0,
        ]))->initByCompose(
            $account_info->account_id,
            $account_info->account_name,
            $create_list,
            $create_data['word_list'] ?? [],
            '智能投放定向',
            $targeting_pa,
            '智能投放参数',
            $setting_pa,
            (new SiteConfigParam($create_data))->toArray(),
            new ADComposeConfigParam([
                'ad1_name' => $create_data['campaign_name'],
                'ad2_name' => $create_data['ad_name']
            ]),
            new ADOtherSettingContentParam($create_data),
            $create_data['calc_rule_list'] ?: [],
            $create_data['rta_list'] ?: []
        );

        $task_id = (new ADTaskModel())->addTask($task_param);

        if ($task_id) {
            $task_param->id = $task_id;
            (new ADTaskMasterMQLogic())->produceTask($task_param->toMQData());
            return $task_param->id;
        } else {
            throw  new AppException('新建广告任务失败!');
        }
    }

    /**
     * 更新渠道包
     * @param array $input
     * @return array|mixed
     */
    public function updateAndroidChannelPackage(array $input)
    {
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountId($input['account_id']);
        if (!$access_token) {
            throw new AppException('账号信息不存在');
        }
        return (new AndroidUnionChannelPackagesModel())
            ->update($input['account_id'], $access_token, $input['app_id'], '0;' . $input['channel_package_id']);
    }

    /**
     * 基本报表-批量修改深度优化出价或ROI
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        //腾讯系二级
        if (MediaType::TENCENT === $media_type) {
            $ad_model = new OdsTencentAdGroupLogModel();
        } else {
            throw new AppException('微信不支持修改深度优化出价或深度优化价值的期望ROI');
        }
        $column = ['adgroup_id', 'adgroup_name', 'account_id', 'platform', 'deep_conversion_behavior_goal',
            'deep_conversion_behavior_bid_amount', 'deep_conversion_worth_expected_roi'];
        $account_info = $ad_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];

        if ('deep_bid' === $update_field) {
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_DEEP_BID;
            $update_field = 'deep_conversion_behavior_bid_amount';
            $media_field = 'deep_conversion_behavior_bid';
            $edit_detail = '深度优化行为的出价';
        } else {
            //修改ROI
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_ROI;
            $update_field = 'deep_conversion_worth_expected_roi';
            $media_field = 'deep_conversion_worth_rate';
            $edit_detail = '深度优化价值的期望ROI';
        }
        $decimal = 2;
        $http_model = new AdGroupModel();
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->adgroup_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['account_id'] = (int)$v->account_id;
            $request_data['adgroup_id'] = (int)$v->adgroup_id;
            $original_value = $v->$update_field;
            if (1 === intval($data['change_type'])) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === intval($data['change_type'])) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    $decimal, '.', '');
            } elseif (3 === intval($data['change_type'])) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    $decimal, '.', '');
            } else {
                throw new AppException('参数错误');
            }

            if ('deep_conversion_behavior_bid' === $media_field) {
                if ($v->$update_field < 1 || $v->$update_field > 500000) {
                    throw new AppException('深度优化出价允许范围为：1-500000');
                }
                $request_data[$media_field] = $v->$update_field * 100;
            }
            if ('deep_conversion_worth_rate' === $media_field) {
                if ($v->$update_field < 0.01 || $v->$update_field > 1000) {
                    throw new AppException('深度转化ROI系数允许范围为：0.01-1000');
                }
                $request_data[$media_field] = $v->$update_field;
            }

            try {
                $http_model->updateForADAnalysisMultiEdit($request_data, $access_tokens[$v->account_id]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->$update_field;
                $update[$v->account_id][] = $request_data['adgroup_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "由[" . $original_value .
                    ']修改为[' . $v->$update_field . ']' . "，修改成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "由[" . $original_value . ']修改为[' .
                    $v->$update_field . ']' . "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->adgroup_name;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->adgroup_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update && $media_type === MediaType::TENCENT) {
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsTencentAdGroupHisLogModel())->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 删除基本报表二三级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return array|mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        if (MediaType::TENCENT === $media_type) {
            $class_half_name = 'App\\Model\\SqlModel\\DataMedia\\OdsTencent';
        } else {
            $class_half_name = 'App\\Model\\SqlModel\\DataMedia\\OdsWechat';
        }
        if (3 === $data['ad_level']) {
            //腾讯三级
            $class_name = $class_half_name . 'AdLogModel';
            $his_class_name = $class_half_name . 'AdHisLogModel';
            $ad_model = new $class_name;
            $ad_his_log_model = new $his_class_name;
            $http_model = new AdsModel();
            $ad_id_type = 'ad_id';
            $ad_name_type = 'ad_name';
        } elseif (2 === $data['ad_level']) {
            //腾讯系二级
            $class_name = $class_half_name . 'AdGroupLogModel';
            $his_class_name = $class_half_name . 'AdGroupHisLogModel';
            $ad_model = new $class_name;
            $ad_his_log_model = new $his_class_name;
            $http_model = new AdGroupModel();
            $ad_id_type = 'adgroup_id';
            $ad_name_type = 'adgroup_name';
        } else {
            //腾讯系一级
            throw new AppException('腾讯一级广告禁止删除');
        }

        $switch_option = 'AD_STATUS_SUSPEND';
        $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        $result = $ad_model->getAccountId($ad_format_target, $column);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;

        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($result as $value) {
            if (!in_array($value->account_id, $account_ids)) {
                $account_ids[] = $value->account_id;
            }
        }
        unset($value);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update_switch = [];
        foreach ($result as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->$ad_id_type;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            if (isset($data['risk_operate'][$media_type][$v->$ad_id_type]) &&
                $data['risk_operate'][$media_type][$v->$ad_id_type]) {
                $risk_operate = '本次为风险操作';
                $risk_code = 1;
            } else {
                $risk_operate = '';
                $risk_code = 0;
            }
            $request_data['account_id'] = (int)$v->account_id;
            $request_data[$ad_id_type] = (int)$v->$ad_id_type;
            $access_token = $access_tokens[$v->account_id];
            try {
                $http_model->ADAnalysisDelete($request_data, $access_token);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;
                $update_switch[$v->account_id][] = $request_data[$ad_id_type];
                $single_record_operate['status'] = $single_return_data['status'] = $risk_code ? ADAnalysisModel::SUCCESS_WITH_RISK : ADAnalysisModel::SUCCESS;
                $single_record_operate['edit_detail'] = "删除成功 " . $risk_operate;
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = $risk_code ? ADAnalysisModel::FAILED_WITH_RISK : ADAnalysisModel::FAILED;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = "删除失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }

            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->$ad_name_type;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_type;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update_switch && $media_type === MediaType::TENCENT) {
            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                'configured_status',
                $switch_option,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表-批量修改一级预算
     * @param int $media_type
     * @param array $input
     * @param array $tencent_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $input, array $tencent_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $tencent_model = new OdsTencentCampaignLogModel();
        $column = ['campaign_id', 'account_id', 'platform', 'campaign_name', 'daily_budget'];
        $account_info = $tencent_model->getAccountId($tencent_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $tencent_http_model = new CampaignModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $opt_detail = '腾讯一级广告预算';
        $decimal = 2;

        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->campaign_id;
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['campaign_id'] = (int)$v->campaign_id;
            $request_data['account_id'] = (int)$v->account_id;
            try {
                $original_value = $v->daily_budget;
                if (1 === intval($input['change_type'])) {
                    $v->daily_budget = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->daily_budget += number_format($v->daily_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->daily_budget -= number_format($v->daily_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }
                $request_data['daily_budget'] = $v->daily_budget * 100;
                //发起请求
                $tencent_http_model->updateForADAnalysisMultiEdit($request_data, $access_tokens[$v->account_id]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->daily_budget;
                $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->campaign_name;
                $update_data[$v->account_id][] = (int)$v->campaign_id;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = (int)$v->daily_budget ?: '无限';
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->campaign_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (1 === (int)$input['change_type']) {
                $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( daily_budget + daily_budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( daily_budget - daily_budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsTencentCampaignHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                'daily_budget',
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表更新账号预算
     * @param int $media_type
     * @param array $input
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $input, array $ad_format_target, string $update_field)
    {
        $tencent_model = new OdsTencentAccountLogModel();
        $column = ['account_id', 'daily_budget', 'platform'];
        $account_info = $tencent_model->getAccountInfoInAccountIds($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $http_model = new AdvertiserModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $decimal = 2;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['ad_id'] = 0;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['account_id'] = (int)$v->account_id;
            try {
                $original_value = $v->daily_budget;
                if (1 === intval($input['change_type'])) {
                    $v->daily_budget = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->daily_budget += number_format($v->daily_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->daily_budget -= number_format($v->daily_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }
                $request_data['daily_budget'] = $v->daily_budget * 100;

                //发起请求
                $http_model->update($request_data, $access_tokens[$v->account_id]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->daily_budget;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = 0 === (int)$v->daily_budget ? '无限' : $v->daily_budget;
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = "腾讯账号预算由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";

            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '腾讯账号预算修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = '';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = 0;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 绑定或解绑RTA策略
     * @param array $input
     * @return mixed|void
     */
    public function bindADAnalysisADRTA(array $input)
    {
        $ad_info = [];
        foreach ($input['ad_info'] as $value) {
            $ad_info[$value['ad_id']] = $value;
        }
        unset($value);
        $is_mp = (int)$input['media_type'] === MediaType::MP ? 1 : 0;
        $loop_times = (int)ceil(count($input['ad_info']) / 10);
        $http_model = new TargetModel();
        $record_bind_log = [];
        $record_bind_list_log = [];
        for ($i = 1; $i <= $loop_times; $i++) {
            //每次最多绑定/解绑10个
            $part_of_data = array_slice($input['ad_info'], ($i - 1) * 10, 10);
            $request_data = [];
            foreach ($part_of_data as $item) {
                $one_request = [];
                $one_request['Id'] = $item['ad_id'];
                $one_request['TargetType'] = 1;
                $one_request['IsMp'] = $is_mp;
                $one_request['OuterTargetId'] = $input['out_target_id'] ?? $input['outer_target_id'];
                $one_request['UId'] = $item['account_id'];

                $request_data['data'][] = $one_request;
            }
            unset($item);
            try {
                $bind_log = [];
                $bind_log['insert_time'] = date('Y-m-d H:i:s');
                $bind_list_log = [];
                $response = $http_model->bindAdd($request_data);

                if (isset($response['success']) && $response['success']) {
                    foreach ($response['success'] as $res) {

                        $bind_log['rta_id'] = $bind_list_log['rta_id'] = $input['rta_id'];
                        $bind_log['outer_target_id'] = $bind_list_log['outer_target_id'] = $input['out_target_id'] ?? $input['outer_target_id'];
                        $bind_log['target_name'] = $bind_list_log['target_name'] = $input['target_name'];
                        $bind_log['binding_target_id'] = $bind_list_log['binding_target_id'] = $res['Id'];
                        $bind_log['target_type'] = $bind_list_log['target_type'] = $input['target_type'];
                        $bind_log['is_mp'] = $bind_list_log['is_mp'] = $is_mp;
                        $bind_log['account_id'] = $bind_list_log['account_id'] = $ad_info[$res['Id']]['account_id'];
                        $bind_list_log['create_source'] = 'system';
                        $bind_log['operator'] = $bind_list_log['creator'] = $input['editor_name'];
                        $bind_list_log['is_del'] = 0;
                        $bind_list_log['update_time'] = date('Y-m-d H:i:s');
                        $bind_log['operate_type'] = 1;
                        $bind_log['operate_detail'] = '绑定成功';
                        $bind_log['platform'] = $ad_info[$res['Id']]['platform'];
                        $bind_log['status'] = 1;
                        $record_bind_log[] = $bind_log;
                        $record_bind_list_log[] = $bind_list_log;
                    }
                    unset($res);
                }
                $bind_log = [];
                $bind_log['insert_time'] = date('Y-m-d H:i:s');
                if (isset($response['error']) && $response['error']) {
                    foreach ($response['error'] as $res) {
                        $bind_log['platform'] = $ad_info[$res['Id']]['platform'];
                        $bind_log['rta_id'] = $input['rta_id'];
                        $bind_log['outer_target_id'] = $input['out_target_id'] ?? $input['outer_target_id'];
                        $bind_log['target_name'] = $input['target_name'];
                        $bind_log['binding_target_id'] = $res['Id'];
                        $bind_log['target_type'] = $input['target_type'];
                        $bind_log['is_mp'] = $is_mp;
                        $bind_log['account_id'] = $ad_info[$res['Id']]['account_id'];
                        $bind_log['operator'] = $input['editor_name'];
                        $bind_log['operate_type'] = 1;
                        $bind_log['operate_detail'] = '绑定失败，失败原因：' . $res['reason'];
                        $bind_log['status'] = 2;
                        $record_bind_log[] = $bind_log;
                    }
                    unset($res);
                }
            } catch (AppException $e) {
                //未知错误
                $bind_log = [];
                $bind_log['insert_time'] = date('Y-m-d H:i:s');
                foreach ($part_of_data as $item) {
                    $bind_log['platform'] = $item['platform'];
                    $bind_log['rta_id'] = $input['rta_id'];
                    $bind_log['outer_target_id'] = $input['out_target_id'] ?? $input['outer_target_id'];
                    $bind_log['target_name'] = $input['target_name'];
                    $bind_log['binding_target_id'] = $item['ad_id'];
                    $bind_log['target_type'] = $input['target_type'];
                    $bind_log['is_mp'] = $is_mp;
                    $bind_log['account_id'] = $item['account_id'];
                    $bind_log['operator'] = $input['editor_name'];
                    $bind_log['operate_type'] = 1;
                    $bind_log['operate_detail'] = '绑定失败，失败原因：' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE);
                    $bind_log['status'] = 2;
                    $record_bind_log[] = $bind_log;
                }
                unset($item);
            }
        }

        (new OdsADAnalysisTencentRTATargetBindLogModel)->add($record_bind_log);
        (new OdsTencentRTATargetBindListLogModel())->add($record_bind_list_log);
    }

    /**
     * 绑定或解绑RTA策略
     * @param array $data
     * @param array $input
     * @return mixed|void
     */
    public function unbindADAnalysisADRTA(array $data, array $input)
    {
        $ad_info = [];
        foreach ($data as &$value) {
            $value['is_mp'] = (int)$value['media_type'] === MediaType::MP ? 1 : 0;
            $ad_info[$value['ad_id']] = $value;
        }
        unset($value);

        $loop_times = (int)ceil(count($data) / 10);
        $http_model = new TargetModel();
        $record_bind_log = [];
        $record_unbind_condition = [];
        for ($i = 1; $i <= $loop_times; $i++) {
            //每次最多绑定/解绑10个
            $part_of_data = array_slice($data, ($i - 1) * 10, 10);
            $request_data = [];
            foreach ($part_of_data as $item) {
                $one_request = [];
                $one_request['Id'] = $item['ad_id'];
                $one_request['TargetType'] = 1;
                $one_request['IsMp'] = $item['is_mp'];

                $request_data['data'][] = $one_request;
            }
            unset($item);
            try {
                $bind_log = [];
                $bind_log['insert_time'] = date('Y-m-d H:i:s');
                $unbind_condition = [];
                $response = $http_model->bindDelete($request_data);

                if (isset($response['success']) && $response['success']) {
                    foreach ($response['success'] as $res) {
                        $bind_log['rta_id'] = $unbind_condition['rta_id'] = (string)$ad_info[$res['Id']]['rta_id'] ?? 0;
                        $bind_log['outer_target_id'] = $unbind_condition['outer_target_id'] = (string)$ad_info[$res['Id']]['out_target_id'] ?? 0;
                        $bind_log['target_name'] = $ad_info[$res['Id']]['target_name'] ?? '';
                        $bind_log['binding_target_id'] = $unbind_condition['binding_target_id'] = (int)$res['Id'];
                        $bind_log['target_type'] = $input['target_type'];
                        $bind_log['is_mp'] = $ad_info[$res['Id']]['is_mp'] ?? 0;
                        $bind_log['account_id'] = $ad_info[$res['Id']]['account_id'] ?? 0;
                        $bind_log['operator'] = $input['editor_name'];
                        $bind_log['operate_type'] = 2;
                        $bind_log['operate_detail'] = '解绑成功';
                        $bind_log['platform'] = $ad_info[$res['Id']]['platform'] ?? '';
                        $bind_log['status'] = 1;
                        $record_bind_log[] = $bind_log;
                        $record_unbind_condition[] = $unbind_condition;
                    }
                    unset($res);
                }
                $bind_log = [];
                $bind_log['insert_time'] = date('Y-m-d H:i:s');
                if (isset($response['error']) && $response['error']) {
                    foreach ($response['error'] as $res) {
                        $bind_log['platform'] = $ad_info[$res['Id']]['platform'] ?? '';
                        $bind_log['rta_id'] = $ad_info[$res['Id']]['rta_id'] ?? 0;
                        $bind_log['outer_target_id'] = $ad_info[$res['Id']]['outer_target_id'] ?? 0;
                        $bind_log['target_name'] = $ad_info[$res['Id']]['target_name'] ?? '';
                        $bind_log['binding_target_id'] = $res['Id'];
                        $bind_log['target_type'] = $input['target_type'];
                        $bind_log['is_mp'] = $ad_info[$res['Id']]['is_mp'] ?? 0;
                        $bind_log['account_id'] = $ad_info[$res['Id']]['account_id'] ?? 0;
                        $bind_log['operator'] = $input['editor_name'];
                        $bind_log['operate_type'] = 2;
                        $bind_log['operate_detail'] = '解绑失败，失败原因：' . $res['reason'];
                        $bind_log['status'] = 2;
                        $record_bind_log[] = $bind_log;
                    }
                    unset($res);
                }
            } catch (AppException $e) {
                //未知错误
                $bind_log = [];
                $bind_log['insert_time'] = date('Y-m-d H:i:s');
                foreach ($part_of_data as $item) {
                    $bind_log['platform'] = $item['platform'];
                    $bind_log['rta_id'] = $item['rta_id'];
                    $bind_log['outer_target_id'] = $item['out_target_id'];
                    $bind_log['target_name'] = $item['target_name'];
                    $bind_log['binding_target_id'] = $item['ad_id'];
                    $bind_log['target_type'] = $input['target_type'];
                    $bind_log['is_mp'] = $item['is_mp'];
                    $bind_log['account_id'] = $item['account_id'];
                    $bind_log['operator'] = $input['editor_name'];
                    $bind_log['operate_type'] = 2;
                    $bind_log['operate_detail'] = '解绑失败，失败原因：' .
                        json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE);
                    $bind_log['status'] = 2;
                    $record_bind_log[] = $bind_log;
                }
                unset($item);
            }
        }

        (new OdsTencentRTATargetBindListLogModel())->multiRemove($record_unbind_condition, ['update_time' => date("Y-m-d H:i:s"), 'is_del' => 1]);

        (new OdsADAnalysisTencentRTATargetBindLogModel)->add($record_bind_log);
    }

    /**
     * 人群预估
     * @param array $target
     * @param $account_info
     * @return array
     */
    public function getTargetAudienceEstimate(array $target, $account_info)
    {
        $body = array_merge($target, [
            'access_token' => $account_info->access_token,
            'account_id' => $account_info->account_id
        ]);
        return (new EstimationModel())->getEstimation($body);
    }

    public function updateADTaskByApi(array $input)
    {
    }

    /**
     * <<<<<<< HEAD
     * 创建广告配置前置
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {

    }

    /**
     * 创建广告配置后置
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    public function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
    }

    /**
     * 新建广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     * @throws RedisException
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            if (!(new ADTaskTencentFilterModel())->incrBy($param->account_id)) {
                throw new AppException('视频号频次控制', ADTaskDigestLogic::RESTART_TASK_CODE);
            }
        }

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName(
                    $param,
                    $account_param->account_id,
                    $account_param->access_token
                ) ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));


        // 自定义
        if ($param->creative_mode == BatchAD::CREATIVE_CUSTOM_MODE) {

            // 新建广告二级
            if (!$param->ad2_id) {
                try {
                    $param->ad2_id = $this->createAD2($param, $account_param);
                    $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                    $ad_task_model->updateTask($param, $param->id);

                } catch (Throwable $e) {
                    $ad_task_model->updateTask($param, $param->id);
                    throw new AppException('新建广告二级错误:' . $e->getMessage());
                }
            }

            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

            // 新建广告三级
            if (!$param->ad3_ids) {
                $param->state_code = ADTaskModel::FINISH_CREATE_AD;
                try {
                    $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                    $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                    $param->ad3_ids = $creative_ids;
                    $ad_task_model->updateTask($param, $param->id);
                } catch (Throwable $e) {
                    $ad_task_model->updateTask($param, $param->id);
                    throw new AppException('新建广告三级错误:' . $e->getMessage());
                }
            }

            $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
            ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        } else {
            // 程序化

            // 新建广告三级
            if (!$param->ad3_ids) {
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                try {
                    $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                    $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                    $param->ad3_ids = $creative_ids;
                    $ad_task_model->updateTask($param, $param->id);
                } catch (Throwable $e) {
                    $ad_task_model->updateTask($param, $param->id);
                    throw new AppException('新建广告三级错误:' . $e->getMessage());
                }
            }

            $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
            ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

            // 新建广告二级
            if (!$param->ad2_id) {
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                try {
                    $param->ad2_id = $this->createAD2($param, $account_param, $param->ad3_ids[0]);
                    $param->state_code = ADTaskModel::FINISH_CREATE_AD;
                    $ad_task_model->updateTask($param, $param->id);
                } catch (Throwable $e) {
                    $ad_task_model->updateTask($param, $param->id);
                    throw new AppException('新建广告二级错误:' . $e->getMessage());
                }
            }

            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
        }
    }

    /**
     * 推送流量包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    public function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $all_flow_id_md5_list = $param->targeting->getAllFlowIdMd5MapList();
        $flow_md5_list = array_keys($all_flow_id_md5_list);
        $already_exist_list_data = $this->getAlreadyExistFlowMd5List(
            $flow_md5_list,
            $account_param->account_id
        );
        $need_push_flow_list_data = [];
        $reset_flow_map = [];
        if ($already_exist_list_data) {
            foreach ($all_flow_id_md5_list as $md5 => $id) {
                if (isset($already_exist_list_data[$md5])) {
                    $already_exist_list_data_array = (array)$already_exist_list_data[$md5];
                    $already_exist_list_data_array['md5'] = $md5;
                    $reset_flow_map[$md5] = $already_exist_list_data_array;
                } else {
                    $all_flow_id_md5_list[$md5]['md5'] = $md5;
                    $need_push_flow_list_data[] = $all_flow_id_md5_list[$md5];
                }
            }
        }

        if ($need_push_flow_list_data) {
            foreach ($need_push_flow_list_data as $key => $flow_rit_id_list) {
                try {
                    $flow_package_id_data = $this->uploadFlow(
                    // 有这个流量包的account_id
                        $account_param->account_id,
                        // 有这个流量包的account_id的access_token
                        $account_param->access_token,
                        $flow_rit_id_list
                    );
                } catch (Throwable $e) {
                    throw new AppException("上传流量包{$flow_rit_id_list['union_package_name']}-{$flow_rit_id_list['md5']}错误:" . $e->getMessage());
                }
                // 上传成功后记录
                if ($flow_package_id = $this->getFlowPackageIdByRequest($flow_package_id_data)) {
                    $reset_flow_map = array_merge($reset_flow_map, $this->getSuccessFlowMap($flow_package_id, $flow_rit_id_list));
                }
            }
            $this->syncFlowPackage($account_param->account_id);
        }
        $param->targeting->resetFlowPackage($reset_flow_map);
    }

    /**
     * 推送人群包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    public function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $need_push_audience_list_data = $this->getNeedUploadAudienceList(
            $param->targeting->getAllAudienceIdList(),
            $param->account_id,
            $param->platform,
            $param->company
        );
        if ($need_push_audience_list_data->isNotEmpty()) {
            $audience_account_id_list = $need_push_audience_list_data->pluck('account_id')->toArray();
            if ($audience_account_id_list) {
                $this->pushAudience(
                    $audience_account_id_list,
                    $need_push_audience_list_data->toArray(),
                    [$param->account_id]
                );
            } else {
                throw new AppException("人群包从未上传过");
            }
        }
    }

    /**
     * 检查打包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return bool|mixed
     */
    public function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        if ($param->is_wait_package == 1 && $param->getSiteConfig()->game_type == "安卓") {
            (new SiteMQLogic)->produceTencentCheckPackageTask($param->toArray(), 1);
            throw new AppException('打包未完成', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }
        return false;
    }

    /**
     * 过滤无效素材
     * @param array $upload_info_list
     * @param MediaAccountInfoParam $account_param
     * @return array
     */
    public function filter_invalid_material(array $upload_info_list, MediaAccountInfoParam $account_param)
    {
        $video_list = $video_resp = $image_list = $image_resp = [];
        foreach ($upload_info_list as $file_id => $material_file) {
            if ($material_file->file_type == MaterialFileModel::FILE_TYPE_VIDEO) {
                $video_list[] = $material_file->media_material_file_id;
            } else {
                $image_list[] = $material_file->media_material_file_id;
            }
        }
        if ($image_list) {
            $list = array_chunk($image_list, 100);
            foreach ($list as $i) {
                $image_result = (new ImagesModel())->getImageInfo($account_param->account_id, $account_param->access_token, $i);
                $image_resp = array_merge($image_resp, isset($image_resp['list']) ? array_column(array_filter($image_result['list'], function ($item) {
                    return $item['status'] != 'ADSTATUS_DELETED';
                }), 'image_id') : []);
            }
        }
        if ($video_list) {
            $video_resp = (new VideosModel())->getVideoInfo($account_param->account_id, $account_param->access_token, $video_list);
            $video_resp = isset($video_resp['list']) ? array_column(array_filter($video_resp['list'], function ($item) {
                return $item['status'] != 'ADSTATUS_DELETED';
            }), 'video_id') : [];
        }

        $invalid_ids = [];
        foreach ($upload_info_list as $file_id => $material_file) {
            if ($material_file->file_type == MaterialFileModel::FILE_TYPE_VIDEO) {
                if (!in_array($material_file->media_material_file_id, $video_resp)) {
                    unset($upload_info_list[$file_id]);
                    $invalid_ids[] = $material_file->id;
                }
            } else {
                if (!in_array($material_file->media_material_file_id, $image_resp)) {
                    unset($upload_info_list[$file_id]);
                    $invalid_ids[] = $material_file->id;
                }
            }
        }
        // 删除已失效的文件上传记录
        $invalid_ids && (new MaterialUploadMediaTaskModel)->deleteLogByIds($invalid_ids, MediaType::TENCENT, $account_param->account_id);

        return $upload_info_list;
    }

    /**
     * 上传素材
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return array|mixed
     */
    public function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::TENCENT, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        $upload_info_list = $this->filter_invalid_material($upload_info_list, $account_param);

        $result = [];
        foreach ($material_file_list as $material_file) {
            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::TENCENT;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;

            if ($upload_info_list[$material_file->id] ?? false) {
                $result[$material_file->id] = [
                    'id' => $upload_info_list[$material_file->id]->media_material_file_id,
                    'url' => $upload_info_list[$material_file->id]->url,
                ];
            } else {
                if ($material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ||
                    $material_file->file_type == MaterialFileModel::FILE_TYPE_COVER) {
                    $media_file_info = $this->uploadImage(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                } else {
                    $media_file_info = $this->uploadVideo(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                }
                if ($media_file_id) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * 是否重启任务
     * @param ADTaskParam $task_param
     * @return bool|mixed
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        $need_restart_message_format_list = [
            '请重试',
            '依赖服务访问超时',
            '不允许并发修改同一个广告组下的创意',
        ];
        foreach ($need_restart_message_format_list as $message_format) {
            if (strpos($task_param->error_msg, $message_format) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 创意素材处理
     * @param ADTaskParam $param
     * @return mixed|void
     * @throws RedisException
     */
    public function prepareCreativeList(ADTaskParam $param)
    {
        $material_file_model = new MaterialFileModel();
        $ods_material_file_log_model = new OdsMaterialFileLogModel();

        $ids = $param->getImageCreativeID();

        $images_map = $material_file_model->getListByIds($ids)->keyBy('id');
        $new_images_map = [];

        $redis_lock_model = new ADTaskMaterialModel();

        foreach ($images_map as $file_id => $material_file) {
            if (($material_file->size / 1024) < 140) continue;
            $sub_path = $material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ? EnvConfig::MATERIAL_IMG_DIR_NAME : EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
            $upload_path = SRV_DIR . "/{$access_path}";
            $ext_array = explode('.', $material_file->filename);
            $ext = array_pop($ext_array);
            $base_name = implode('.', $ext_array);
            if (strpos($base_name, '(腾讯投放压缩)') !== false) {
                throw new AppException("{$base_name}图片素材已经被压缩，大小依旧不符合腾讯规范，请手动压缩");
            }

            $ext_name = "{$base_name}_(腾讯投放压缩){$material_file->width}x$material_file->height.$ext";
            $file_data = $material_file_model->getDataByName($ext_name);
            //$d = file_get_contents($material_file->url);
            //file_put_contents("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename",$d);
            if ($file_data) {
                $new_images_map[$file_id] = (new MaterialFileParam($file_data))->toArray();
            } else {
                if ($redis_lock_model->getTencentPicLock($file_id)) {
                    throw new AppException("腾讯图片压缩队列调整.", ADTaskDigestLogic::RESTART_TASK_CODE);
                } else {
                    if (!$redis_lock_model->lockTencentPic($file_id)) {
                        throw new AppException("腾讯图片压缩队列调整..", ADTaskDigestLogic::RESTART_TASK_CODE);
                    }
                }
                $result = Image::compress(
                    "$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename",
                    "$upload_path/$material_file->platform/$material_file->material_id/$ext_name",
                    120
                );
                if ($result) {
                    $insert_data = [
                        'platform' => $material_file->platform,
                        'media_type' => 0,
                        'material_id' => $material_file->material_id,
                        'file_type' => $material_file->file_type,
                        'filename' => $ext_name,
                        'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material_file->platform/$material_file->material_id/$ext_name",
                        'width' => $material_file->width,
                        'height' => $material_file->height,
                        'scale' => Math::div($material_file->width, $material_file->height),
                        'signature' => md5_file("$upload_path/$material_file->platform/$material_file->material_id/$ext_name"),
                        'bitrate' => 0,
                        'size' => filesize("$upload_path/$material_file->platform/$material_file->material_id/$ext_name"),
                        'format' => $material_file->format,
                        'uploader' => $material_file->uploader,
                        'create_time' => time(),
                        'update_time' => time(),
                        'is_del' => 0,
                        'is_ext' => 1
                    ];
                    $insert_data['id'] = $material_file_model->add($insert_data);
                    if (EnvConfig::ENV === 'production' && $insert_data['id']) {
                        $insert_data['insert_time'] = date("Y-m-d H:i:s", $insert_data['create_time']);
                        $insert_data['update_time'] = date("Y-m-d H:i:s", $insert_data['update_time']);
                        unset($insert_data['create_time']);
                        $ods_material_file_log_model->add($insert_data);
                    }
                    $new_images_map[$file_id] = $insert_data;
                    $redis_lock_model->unlockTencentPic($file_id);
                } else {
                    $redis_lock_model->unlockTencentPic($file_id);
                    throw new AppException('压缩腾讯图片失败');
                }
            }
        }

        foreach ($param->creative_list as $id => &$creative_info) {
            if (isset($creative_info['cover_info']['id']) && isset($new_images_map[$creative_info['cover_info']['id']])) {
                $creative_info['cover_info'] = [
                    'id' => $new_images_map[$creative_info['cover_info']['id']]['id'],
                    'url' => $new_images_map[$creative_info['cover_info']['id']]['url'],
                    'width' => $new_images_map[$creative_info['cover_info']['id']]['width'],
                    'height' => $new_images_map[$creative_info['cover_info']['id']]['height'],
                ];
            } elseif (isset($creative_info['image_info']['image_list']) && $creative_info['image_info']['image_list']) {
                foreach ($creative_info['image_info']['image_list'] as $image_key => &$image_value) {
                    if (isset($image_value['id']) && isset($new_images_map[$image_value['id']])) {
                        $image_value = [
                            'id' => $new_images_map[$image_value['id']]['id'],
                            'url' => $new_images_map[$image_value['id']]['url'],
                            'width' => $new_images_map[$image_value['id']]['width'],
                            'height' => $new_images_map[$image_value['id']]['height'],
                        ];
                    }
                }
            } elseif (isset($creative_info['image_info']['id']) && isset($new_images_map[$creative_info['image_info']['id']])) {
                $creative_info['image_info'] = [
                    'id' => $new_images_map[$creative_info['image_info']['id']]['id'],
                    'url' => $new_images_map[$creative_info['image_info']['id']]['url'],
                    'width' => $new_images_map[$creative_info['image_info']['id']]['width'],
                    'height' => $new_images_map[$creative_info['image_info']['id']]['height'],
                ];
            }
        }
    }

    /**
     * 获取渠道组
     * @param ADTaskParam $param
     * @return int
     */
    public function getAgentGroup(ADTaskParam $param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ($other_setting->site_set_none === 'search') {
            return AgentGroup::TENCENT_SOUGOU_SEARCH;
        }

        if (in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            return AgentGroup::TENCENT_CHANNELS;
        }
        // 这里是还没有可以建site的，所以先用数组
        if ($param->site_config['plat_id'] == PlatId::MINI) {
            return AgentGroup::ADQ_WX_MINI_GAME;
        }
        return AgentGroup::TENCENT;
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaStatus(): Collection
    {
        $list = (new OdsTencentAdGroupLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::EN_TO_CN[$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        $list = (new OdsTencentAdGroupLogModel())->getInventoryList($condition);
        if ($list->isEmpty()) {
            return $list;
        }
        return $list->map(function ($item) use ($keyword) {
            if ('[]' === $item->site_set) {
                $label = '腾讯自动版位';
            } else {
                $inventory_type = json_decode($item->site_set, true);
                $inventory_type_cn = [];
                foreach ($inventory_type as $value) {
                    if (is_null($value)) {
                        return false;
                    }
                    $inventory_type_cn[] = ADFieldsENToCNMap::INVENTORY_TO_CN[$value] ?? $value;
                }
                unset($value);
                $label = implode(',', $inventory_type_cn);
            }
            if ('' === $keyword || ('' !== $keyword && false !== strpos($label, $keyword))) {
                return [
                    'label' => $label,
                    'value' => $item->site_set,
                ];
            } else {
                return false;
            }
        })->filter()->values();
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        return '';
    }

    public function getTransferSet($data)
    {
        //计算每个account_id下有几条关联记录
        $count_related_record = array_count_values($data['account_ids']);

        $account_ids = array_keys($count_related_record);
        //注入权限获取access_token
        $agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
        $account_info = (new OdsTencentAccountLogModel())->getAccountTransferInfosInAccountIds($account_ids, $agent_permission);
        $account_info_ids = $account_info->pluck('account_id')->toArray();

        if ($no_permission = array_diff($account_ids, $account_info_ids)) {
            throw new AppException('账号ID：' . json_encode(array_values($no_permission)) . '无操作权限或未录入结算主体/代理商ID');
        }

        $account_set = [];
        foreach ($account_info as $item) {
            $item->related_record = $count_related_record[$item->account_id];
            $transferable_key = $item->transferable_key;
            unset($item->transferable_key);
            $account_set[$transferable_key][] = (array)$item;
        }

        return $account_set;
    }

    /**
     * 根据transferable_key获取可转账号列表
     * @param $data
     * @return Collection|mixed
     */
    public function getAccountIdListByTransferableKey($data)
    {
        $agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
        return (new OdsTencentAccountLogModel())->getAccountIdListByTransferableKey(
            $agent_permission,
            [$data['transferable_key']],
            $data['account_ids'],
            $data['keyword']
        );
    }

    /**
     * 转账
     * @param ADAnalysisTencentTransferParam $param
     * @return array
     */
    public function transfer(ADAnalysisTencentTransferParam $param)
    {
        //获取所有account_id
        $account_ids = array_column($param->transfer_info, 'operate_account_id');

        foreach ($param->transfer_info as $item) {
            $account_ids = array_merge(array_column($item['account_info'], 'account_id'), $account_ids);
        }

        $leader_permission = $param->leader_permission ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, MediaType::TENCENT, $leader_permission)->keyBy('account_id');

        $http_model = new MergeFundTypeSubcustomerTransferModel();
        $one_record = [];
        $one_record['operator'] = $param->operator;
        $one_record['operate_time'] = date('Y-m-d H:i:s');
        $all_record = [];
        $update_account_ids = [];
        $failed_account_infos = [];
        foreach ($param->transfer_info as $one_transfer_info) {
            if (!$one_transfer_info['account_info']) {
                continue;
            }
            foreach ($one_transfer_info['account_info'] as $item) {
                if (0.0 === (float)$item['amount']) {
                    continue;
                }
                $failed_account_info = [];
                $error = [];
                if ('RECHARGE' === $param->operate_type) {
                    //充值
                    $account_id = (int)$one_transfer_info['operate_account_id'];
                    $to_account_id = (int)$item['account_id'];
                } else {
                    //退款
                    $account_id = (int)$item['account_id'];
                    $to_account_id = (int)$one_transfer_info['operate_account_id'];
                }

                $amount = (float)Math::decimal($item['amount'], 2);
                $transfer_type = $item['transfer_type'];

                $one_record['platform'] = $access_token_info[$account_id]->platform ?? '';
                $one_record['account_id'] = $account_id;
                $one_record['account_name'] = $access_token_info[$account_id]->account_name ?? '';
                $one_record['target_account_id'] = $to_account_id;
                $one_record['target_account_name'] = $access_token_info[$to_account_id]->account_name ?? '';
                $one_record['amount'] = $amount;
                $one_record['transfer_type'] = $param::TRANSFER_TYPE[$transfer_type];
                //如果没有拿到access_token，充值跳出两层循环，退款跳出一层
                if (!isset($access_token_info[$account_id]->access_token) || !isset($access_token_info[$to_account_id]->access_token)) {
                    $one_record['external_bill_no'] = 0;
                    $one_record['operate_detail'] = "账号ID $account_id -> $to_account_id 转账失败，没有转账权限";
                    $one_record['status'] = 2;
                    $all_record[] = $one_record;
                    $failed_account_info['account_id'] = $to_account_id;
                    $failed_account_info['operate_account_id'] = $account_id;
                    $failed_account_info['transfer_type'] = $transfer_type;
                    $failed_account_info['msg'] = "没有转账权限";
                    $failed_account_info['amount'] = $amount;
                    $failed_account_infos[] = $failed_account_info;
                    //对于充值，来源账号没权限直接跳出两层，对于退款，目标账号没有权限也跳出两层
                    if (('RECHARGE' === $param->operate_type && !isset($access_token_info[$account_id])) ||
                        ('REFUND' === $param->operate_type && !isset($access_token_info[$to_account_id]))) {
                        continue 2;
                    } else {
                        continue 1;
                    }
                }
                $access_token = $access_token_info[$account_id]->access_token;
                try {
                    $response = $http_model->add($account_id, intval($amount * 100), $to_account_id, $transfer_type, $access_token);
                    $one_record['external_bill_no'] = $response['external_bill_no'];
                    $one_record['transaction_time'] = date('Y-m-d H:i:s', $response['time']);
                    $one_record['status'] = 1;
                    $one_record['operate_detail'] = "账号ID $account_id -> $to_account_id" . ' 转账成功';
                    $update_account_ids[] = $account_id;
                    $update_account_ids[] = $to_account_id;
                } catch (AppException $e) {
                    $failed_account_info['account_id'] = $to_account_id;
                    $failed_account_info['operate_account_id'] = $account_id;
                    $failed_account_info['transfer_type'] = $transfer_type;
                    $failed_account_info['msg'] = $e->getMessage();
                    $failed_account_info['amount'] = $amount;
                    $failed_account_infos[] = $failed_account_info;

                    $one_record['external_bill_no'] = 0;
                    $one_record['transaction_time'] = '1970-01-01 08:00:00';
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $one_record['operate_detail'] = "账号ID $account_id -> $to_account_id 转账失败，失败原因：" . json_encode($error, JSON_UNESCAPED_UNICODE);
                    $one_record['status'] = 2;
                }
                $all_record[] = $one_record;
            }
        }
        if ($update_account_ids) {
            $update_account_ids = array_flip(array_flip($update_account_ids));
            (new GDTTaskModel())->account(array_values($update_account_ids));
        }

        (new OdsTencentTransferOperateLogModel())->add($all_record);

        return $failed_account_infos;
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if (!$param->convert_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新账号余额信息
     * @param $data
     */
    public function refreshAccountBalance($data)
    {
        $task_model = new GDTTaskModel();
        $task_model->setAsync(false);
        $task_model->account(array_unique($data['account_ids']));
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
    }

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassScheduleTime() method.
    }

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateSecondClassScheduleTime() method.
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status(): Collection
    {
        $list = (new OdsTencentAdLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::EN_TO_CN[$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 广告一键起量
     *
     * @param $data
     *
     * @return mixed
     */
    public function updateADAnalysisADRaise($data)
    {
        // TODO: Implement updateADAnalysisADRaise() method.
    }

    /**
     * @param $account_id
     * @param $creative_mode
     * @param $site_config
     * @param $other_setting
     * @return array
     */
    public function getCreativeTemplate($account_id, $creative_mode, $site_config, $other_setting): array
    {
        $acc_info = (new MediaAccountModel())->getDataByAccountId($account_id, MediaType::TENCENT);

        $param = [
            'account_id' => $account_id,
            'marketing_goal' => 'MARKETING_GOAL_USER_GROWTH',
        ];

        $site_config = (array)$site_config;

        if ($site_config['convert_type'] == 'OPTIMIZATIONGOAL_BACK_FLOW') {
            $param['marketing_sub_goal'] = 'MARKETING_SUB_GOAL_MINI_GAME_RETURN_CUSTOMER_ENGAGEMENT';
        } else {
            $param['marketing_sub_goal'] = $other_setting['marketing_scene'] == 'GAME_RESERVATION' ?
                'MARKETING_SUB_GOAL_NEW_GAME_RESERVE' :
                ($site_config['plat_id'] != PlatId::MINI ? 'MARKETING_SUB_GOAL_PLATEAU_PHASE_LAUNCH' : 'MARKETING_SUB_GOAL_MINI_GAME_NEW_CUSTOMER_GROWTH');
        }

        if ($site_config['plat_id'] == PlatId::MINI) {
            $param['marketing_carrier_type'] = 'MARKETING_CARRIER_TYPE_WECHAT_MINI_GAME';
            $param['marketing_target_type'] = 'MARKETING_TARGET_TYPE_WECHAT_MINI_GAME';
        } else {
            $param['marketing_carrier_type'] = $site_config['game_type'] == 'IOS' ? 'MARKETING_CARRIER_TYPE_APP_IOS' : 'MARKETING_CARRIER_TYPE_APP_ANDROID';
            $param['marketing_target_type'] = $site_config['game_type'] == 'IOS' ? 'MARKETING_TARGET_TYPE_APP_IOS' : 'MARKETING_TARGET_TYPE_APP_ANDROID';
        }

        if ($creative_mode === BatchAD::CREATIVE_CUSTOM_MODE) {
            $param['delivery_mode'] = 'DELIVERY_MODE_CUSTOMIZE';
        } else {
            $param['delivery_mode'] = 'DELIVERY_MODE_COMPONENT';
        }

        if ($creative_mode === BatchAD::CREATIVE_CUSTOM_MODE) {
            $param['dynamic_creative_type'] = 'DYNAMIC_CREATIVE_TYPE_COMMON';
        } else {
            $param['dynamic_creative_type'] = $other_setting['dynamic_creative_type'];
        }

        $param['creative_template_id'] = $other_setting['template_id'];
        $param['automatic_site_enabled'] = $other_setting['automatic_site_enabled'];
        $param['site_set'] = $other_setting['site_set'];
        $param['automatic_site_enabled'] = $param['automatic_site_enabled'] ? 'true' : 'false';
        $param['site_set'] = $param['site_set'] ? json_encode($param['site_set']) : [];

        $result = (new AdcreativeTemplateModel())->infoV3((new CreativeTemplateParam($param)), $acc_info->access_token);

        return $result['list'][0] ?? [];
    }

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
