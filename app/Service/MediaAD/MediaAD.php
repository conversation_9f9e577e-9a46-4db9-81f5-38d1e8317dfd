<?php
/**
 * 广告配置列表-广告部分的Service
 * User: zzh
 * Date: 2020/02/21
 * Time: 15:45
 */

namespace App\Service\MediaAD;

use App\Constant\BatchADClassMap;
use App\Constant\MediaType;
use App\Exception\AppException;

use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\MediaAccountInfoParam;
use App\Struct\Input;
use App\Utils\Helpers;
use Illuminate\Support\Collection;

/**
 * Class MediaAD
 * @package App\Service
 */
class MediaAD
{
    /**
     * @var AbstractMedia $media_service
     */
    private $media_service;

    /**
     * 初始化媒体的对应Service
     * @param $media_type
     * @param $media_agent_type
     */
    private function initMediaService($media_type, $media_agent_type)
    {
        $media_name = Helpers::pascal(ucfirst(strtolower(MediaType::CONST_NAME_LIST[$media_type])));
        $media_service_class = __NAMESPACE__ . '\\Media' . $this->fillNamespace($media_type, ucfirst($media_name), $media_agent_type);
        if (class_exists($media_service_class)) {
            $this->media_service = new $media_service_class();
        } else {
            throw new AppException("错误的MediaADService类名,{$media_service_class}");
        }
    }

    public function __construct($media_type, $media_agent_type = 0)
    {
        $this->initMediaService($media_type, $media_agent_type);
    }

    /**
     * @param int $media_type
     * @param string $class_name
     * @param int $media_agent_type
     * @return string
     */
    private function fillNamespace(int $media_type, string $class_name, int $media_agent_type = 0)
    {
        $suffix = BatchADClassMap::SERVICE_MAP[$media_type][$media_agent_type];

        return "{$class_name}{$suffix}";
    }

    //---------------------------------------每个媒体不一样的代码---------------------------------------------------------

    /**
     * 获取行为类目词列表
     * @param Input $input
     * @return mixed
     */
    public function getInterestActionCategoryList(Input $input)
    {
        return $this->media_service->getInterestActionCategoryList($input);
    }

    /**
     * 获取行为关键词列表
     * @param Input $input
     * @return mixed
     */
    public function getInterestActionKeywordList(Input $input)
    {
        return $this->media_service->getInterestActionKeywordList($input);
    }

    /**
     * 获取兴趣类目词列表
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        return $this->media_service->getInterestInterestCategoryList($input);
    }

    /**
     * 获取兴趣关键词列表
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        return $this->media_service->getInterestInterestKeywordList($input);
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getIndustryList(Input $input)
    {
        return $this->media_service->getIndustryList($input);
    }

    //-------------------------------------------单个广告投放代码---------------------------------------------------------

    /**
     * 获取已创建好的广告组
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        return $this->media_service->campaignList($input);
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        return $this->media_service->isCreateConvert($param);
    }

    /**
     * 获取已创建好的广告组ID
     * @param ADTaskParam $param
     * @param $account_id
     * @param $access_token
     * @return int
     */
    public function getCampaignIdByName(ADTaskParam $param, $account_id, $access_token)
    {
        return $this->media_service->getAD1IdByName($param, $account_id, $access_token);
    }

    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        return $this->media_service->convertList($input);
    }

    /**
     * 创建转化
     * @param ConvertCreateParam $param
     * @return array
     */
    public function createConvert(ConvertCreateParam $param)
    {
        return $this->media_service->createConvert($param);
    }

    /**
     * 创建渠道包
     * @param ChannelPackageParam $param
     * @return array
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        return $this->media_service->createChannelPackage($param);
    }

    /**
     * 获取广告计划中的定向包列表
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        return $this->media_service->audiencePackageList($input);
    }


    /**
     * 修改一个计划
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return $this->media_service->updateAd($param, $account_id, $access_token);
    }

    /**
     * 修改一个计划的开关
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return $this->media_service->updateADStatus($param, $account_id, $access_token);
    }

    /**
     * 修改一个创意的开关
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return $this->media_service->updateCreativeStatus($param, $account_id, $access_token);
    }

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return $this->media_service->createAudience($param, $account_id, $access_token);
    }

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return $this->media_service->expandAudience($param, $account_id, $access_token);
    }

    /**
     * 上传图片到媒体-返回媒体图片素材id
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     * [
     *  'id'=id,
     *  'url'=url
     * ]
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        return $this->media_service->uploadImage($param, $advertiser_id, $access_token);
    }

    /**
     * 上传视频到媒体-返回媒体视频素材id
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     * [
     *  'id'=id,
     *  'url'=url
     * ]
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        return $this->media_service->uploadVideo($param, $advertiser_id, $access_token);
    }

    /**
     * id转词语
     * @param array $input
     * @param $media_type
     * @return mixed
     */
    public function getWordById(array $input, $media_type)
    {
        return $this->media_service->getWordById($input, $media_type);
    }

    /**
     * @param array $ids
     * @param int $status
     * @return Collection
     */
    public function getAudienceListByIds(array $ids, $status = 2)
    {
        return $this->media_service->getAudienceListByIds($ids, $status);
    }

    /**
     * 推送人群包
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        return $this->media_service->pushAudience($audience_account_id_list, $need_push_audience_list_data, $target_account_ids);
    }

    /**
     * 人群包列表
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        return $this->media_service->audienceList($company, $page, $rows, $id, $name, $tag, $source, $account_id, $extra);
    }

    /**
     * 流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $rows
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $rows = 15)
    {
        return $this->media_service->flowList($name, $account_id, $page, $rows);
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        return $this->media_service->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        return $this->media_service->getTargetingName($audience_info);
    }

    /**
     * 补充定向包信息
     * @param int $media_type
     * @param array $targeting_info
     * @return array
     */
    public function fillTargetingInfo(array $targeting_info)
    {
        return $this->media_service->fillTargetingInfo($targeting_info);
    }

    /**
     * @param $audience_md5
     * @return
     */
    public function getTargetingDataByAD2($company, $audience_md5)
    {
        return $this->media_service->getTargetingDataByAD2($company, $audience_md5);
    }

    /**
     * 获取广告计划行为列表
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        return $this->media_service->getAdActionList($condition);
    }

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        return $this->media_service->getActionWord($data);
    }

    /**
     * 获取广告计划兴趣列表
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        return $this->media_service->getAdInterestList($condition);
    }

    /**
     * 获取计划兴趣徕卡id转词汇
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        return $this->media_service->getInterestWord($data);
    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        return $this->media_service->updateADAnalysisStatus($media_type, $data, $ad_format_target);
    }

    /**
     * 修改基本报表一级广告
     * @param array $input
     * @return mixed
     */
    public function updateADAnalysisFirstClass(array $input)
    {
        return $this->media_service->updateADAnalysisFirstClass($input);
    }

    /**
     * 修改基本报表二级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        return $this->media_service->updateADAnalysisSecondClass($media_type, $data, $ad_format_target);
    }

    /**
     * 修改基本报表预算和出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->media_service->updateADAnalysisBudgetOrBid($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 修改基本报表预算和出价
     * @param int $media_type
     * @param array $data
     * @return mixed
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        return $this->media_service->handleADAnalysisBudgetOrBidMQData($media_type, $data);
    }

    /**
     * 修改基本报表定向包
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        return $this->media_service->updateADAnalysisTargeting($data, $account_info);
    }

    /**
     * 使用接口新建广告任务
     * @param array $input
     * @return int
     */
    public function addADTaskByApi(array $input)
    {
        return $this->media_service->addADTaskByApi($input);
    }

    /**
     * 使用接口修改广告任务
     * @param array $input
     * @return mixed
     */
    public function updateADTaskByApi(array $input)
    {
        return $this->media_service->updateADTaskByApi($input);
    }

    /**
     * 更新渠道包
     * @param array $input
     * @return mixed
     */
    public function updateAndroidChannelPackage(array $input)
    {
        return $this->media_service->updateAndroidChannelPackage($input);
    }

    /**
     * 修改基本报表深度转化出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisDeepBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->media_service->updateADAnalysisDeepBidOrROI($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 修改基本报表深度转化出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->media_service->updateADAnalysisDeepBidOrROI($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 删除基本报表广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        return $this->media_service->deleteADAnalysisAD($media_type, $data, $ad_format_target);
    }

    /**
     * 修改基本报表一级预算
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->media_service->updateADAnalysisFirstClassBudget($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 修改基本报表一级转化出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->media_service->updateADAnalysisFirstClassCpaBid($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 修改基本报表一级ROI系数
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        return $this->media_service->updateFirstClassRoiGoal($data, $ad_format_target);
    }

    /**
     * 修改基本报表账户预算
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->media_service->updateADAnalysisAccountBudget($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 修改基本报表一级投放时段
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        return $this->media_service->updateFirstClassScheduleTime($media_type, $data, $ad_format_target);
    }

    /**
     * 修改基本报表一级投放时段
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        return $this->media_service->updateSecondClassScheduleTime($media_type, $data, $ad_format_target);
    }

    /**
     * 基本报表广告绑定RTA
     * @param array $input
     * @return mixed
     */
    public function bindADAnalysisADRTA(array $input)
    {
        return $this->media_service->bindADAnalysisADRTA($input);
    }

    /**
     * 基本报表广告解绑RTA
     * @param array $data
     * @param array $input
     * @return mixed
     */
    public function unbindADAnalysisADRTA(array $data, array $input)
    {
        return $this->media_service->unbindADAnalysisADRTA($data, $input);
    }

    /**
     * 人群预估
     * @param array $target
     * @param $account_info
     * @return mixed
     */
    public function getTargetAudienceEstimate(array $target, $account_info)
    {
        return $this->media_service->getTargetAudienceEstimate($target, $account_info);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        return $this->media_service->beforeMakeSite($param, $account_param);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        return $this->media_service->afterMakeSite($param, $account_param);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        return $this->media_service->createAD($param, $account_param, $material_media_id_map);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        return $this->media_service->pushFlowPackage($param, $account_param);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        return $this->media_service->pushAudiencePackage($param, $account_param);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        return $this->media_service->waitDelayPack($param, $account_param);
    }

    /**
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        return $this->media_service->getMaterialFileMediaInfo($material_file_list, $account_param);
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    public function isRestartADTask(ADTaskParam $task_param)
    {
        return $this->media_service->isRestartADTask($task_param);
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    public function prepareCreativeList(ADTaskParam $task_param)
    {
        return $this->media_service->prepareCreativeList($task_param);
    }

    public function getAgentGroup(ADTaskParam $task_param)
    {
        return $this->media_service->getAgentGroup($task_param);
    }

    /*
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaStatus()
    {
        return $this->media_service->getDiffMediaStatus();
    }

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition)
    {
        return $this->media_service->getDiffMediaInventoryList($keyword, $condition);
    }

    /**
     * @param ADTaskParam $task_param
     * @param $name_norms
     */
    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        return $this->media_service->getADName($task_param, $name_norms);
    }

    /**
     * 获取转账集合
     * @param $data
     * @return mixed
     */
    public function getTransferSet($data)
    {
        return $this->media_service->getTransferSet($data);
    }

    /**
     * 根据transferable_key获取可转账号列表
     * @param $data
     * @return mixed
     */
    public function getAccountIdListByTransferableKey($data)
    {
        return $this->media_service->getAccountIdListByTransferableKey($data);
    }

    /**
     * 刷新账号余额信息
     * @param $data
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        return $this->media_service->refreshAccountBalance($data);
    }

    /*
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status()
    {
        return $this->media_service->getDiffMediaAD3Status();
    }

    /**
     * 广告一键起量
     * @param $data
     * @return mixed
     */
    public function updateADAnalysisADRaise($data)
    {
        return $this->media_service->updateADAnalysisADRaise($data);
    }
}
