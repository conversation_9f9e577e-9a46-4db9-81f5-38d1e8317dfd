<?php
/**
 * Created by Php<PERSON>tor<PERSON>.
 * User: 张中昊
 * Date: 2020/02/25
 * Time: 10:44
 */

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ConvertSourceType;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Constant\PlatId;
use App\Constant\UcEnum;
use App\Exception\AppException;
use App\Logic\DSP\ADServingLogic;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\TrinoTask\UCTaskModel;
use App\Model\HttpModel\UC\AdConvert\AdConvertModel;
use App\Model\HttpModel\UC\AdGroup\AdGroupModel;
use App\Model\HttpModel\UC\Campaign\CampaignModel;
use App\Model\HttpModel\UC\Creative\CreativeModel;
use App\Model\HttpModel\UC\DMP\AudienceModel;
use App\Model\HttpModel\UC\DMP\AudienceModel as DMPAudienceModel;
use App\Model\HttpModel\UC\Material\ConvertModel;
use App\Model\HttpModel\UC\Material\CountyModel;
use App\Model\HttpModel\UC\Material\ImageModel;
use App\Model\HttpModel\UC\Material\IndustryModel;
use App\Model\HttpModel\UC\Material\InterestModel;
use App\Model\HttpModel\UC\Material\LogoModel;
use App\Model\HttpModel\UC\Material\ProvinceModel;
use App\Model\HttpModel\UC\Material\VideoModel;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\DataMedia\OdsUCAudiencePackageLogModel;
use App\Model\SqlModel\DataMedia\OdsUcGeySiteLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\CampaignCreateMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Uc\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\Uc\Basics\ADSettingContentParam;
use App\Param\ADServing\Uc\ADTargetingContentParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Param\UC\AdGroupCreateParam;
use App\Param\UC\CampaignCreateParam;
use App\Param\UC\ConvertInfoTypeParam;
use App\Param\UC\CreativeCreateParam;
use App\Param\UC\CreativeProceduralCreateParam;
use App\Param\UC\ObjectivesParam;
use App\Param\UC\TargetingsParam;
use App\Service\PlatformAD\PlatformAD;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Image;
use App\Utils\Math;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use Throwable;

class MediaUc extends AbstractMedia
{
    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var \App\Param\ADServing\UC\ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if ($param->site_config['plat_id'] == PlatId::MINI) {
            $site_ext = $param->site_config['ext'];
            // 三元运算符 如果是落地页 则获取落地页信息,,否则获取锦帆建站url
            $site_ext['external_url'] = $other_setting->download_type == UcEnum::DOWNLOAD_URL ? $other_setting->getUcPageWebUrlMapInfo($param->platform, $param->agent_id, $param->site_id)
                : ($other_setting->getUcPageExternalUrlMapInfo($account_param->account_id) ? $other_setting->getUcPageExternalUrlMapInfo($account_param->account_id)['online_url'] : '');
            $param->setSiteConfigProp('ext', $site_ext);
        }
    }

    /**
     * 获取城市列表
     * @return array
     */
    public function getProvince()
    {
        $media_account_model = new MediaAccountModel();
        $acc_info = $media_account_model->getRandomDataByMediaType(MediaType::UC);
        if (!$acc_info) {
            throw new AppException('找不到对应的渠道数据');
        }
        $target = $acc_info->account_name;
        $password = $acc_info->account_password;
        $token = $acc_info->access_token;
        $username = $acc_info->wechat_account_name;

        if (empty($username)) {
            $username = $target;
            $target = '';
        }
        return (new ProvinceModel())->getProvince($username, $password, $token, $target);
    }

    /**
     * 获取区县列表
     * @return array
     */
    public function getCounty()
    {
        $media_account_model = new MediaAccountModel();
        $acc_info = $media_account_model->getRandomDataByMediaType(MediaType::UC);
        if (!$acc_info) {
            throw new AppException('找不到对应的渠道数据');
        }
        $target = $acc_info->account_name;
        $password = $acc_info->account_password;
        $token = $acc_info->access_token;
        $username = $acc_info->wechat_account_name;

        if (empty($username)) {
            $username = $target;
            $target = '';
        }
        return (new CountyModel())->getCounty($username, $password, $token, $target);
    }


    /**
     * 获取兴趣列表
     * @return array
     */
    public function getInterest()
    {
        $media_account_model = new MediaAccountModel();
        $acc_info = $media_account_model->getRandomDataByMediaType(MediaType::UC);
        if (!$acc_info) {
            throw new AppException('找不到对应的渠道数据');
        }
        $target = $acc_info->account_name;
        $password = $acc_info->account_password;
        $token = $acc_info->access_token;
        $username = $acc_info->wechat_account_name;

        if (empty($username)) {
            $username = $target;
            $target = '';
        }
        $data = (new InterestModel())->getInterest($username, $password, $token, $target);
        return $this->interestActionCategoryFormat($data['interestTypes']);
    }

    private function interestActionCategoryFormat($data)
    {
        foreach ($data as $key => &$value) {
            $value['id'] = $value['value'];
            if (isset($value['children']) && $value['children']) {
                $value['children'] = $this->interestActionCategoryFormat($value['children']);
            } else {
                unset($value['children']);
            }
            $value['value'] = ['id' => $value['id'] ?? '', 'name' => $value['name'] ?? ''];
        }
        return $data;
    }

    /**
     * 已创建好的广告组列表
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
//        $campaign_model = new CampaignModel();
//        $media_account_model = new MediaAccountModel();
//        $account_data = $media_account_model->getDataByPlatformAgentId($input['platform'], $input['agent_id']);
//        if (!$account_data) {
//            throw new AppException('找不到对应的渠道数据');
//        }
//        $filters = [];
//        $input['campaign_name'] && $filters['campaign_name'] = $input['campaign_name'];
//        return $campaign_model->info($account_data->account_id, $account_data->access_token, [], $filters, $input['page'], $input['page_size']);
    }

    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        return [];
    }

    /**
     * 获取广告计划可用的定向包列表
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionCategoryList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionKeywordList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取兴趣类目
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取兴趣关键词
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        return [];
    }

    /**
     * 获取锦帆落地页
     * @param $account_id
     * @param $opt_from
     * @param string $key_word
     * @return Collection
     */
    public function getLandingPageList($account_id, $opt_from, $key_word = '')
    {
        return (new OdsUcGeySiteLogModel())->getList($account_id, $opt_from, $key_word);
    }

    /**
     * 上传图片到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $images = ['imageUrls' => [str_replace(" ", "%20", $param->url)]];
            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->wechat_account_name;
            $password = $media_account_info->account_password;
            $token = $media_account_info->access_token;
            $target = $media_account_info->account_name;
            $image_model = new ImageModel();
            $result = $image_model->upload(
                $username,
                $password,
                $token,
                $target,
                $images);
            return [
                'id' => $result['uploadImages'][0]['id'],
                'url' => $result['uploadImages'][0]['hcImageUrl'],
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传视频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file = curl_file_create("$upload_path/$video_path/$platform/$material_id/$file_name", null, $file_name);
            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->wechat_account_name;
            $password = $media_account_info->account_password;
            $token = $media_account_info->access_token;
            $target = $media_account_info->account_name;
            $video_model = new VideoModel();
            $result = $video_model->upload(
                $username,
                $password,
                $token,
                $target,
                $file
            );
            return [
                'id' => $result['mcVideos'][0]['hcVideoId'],
                'url' => $result['mcVideos'][0]['videoUrl']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 获取创意行业列表
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $acc_info = $media_account_model->getRandomDataByMediaType(MediaType::UC);
        if (!$acc_info) {
            throw new AppException('找不到对应的渠道数据');
        }
        $target = $acc_info->account_name;
        $password = $acc_info->account_password;
        $token = $acc_info->access_token;
        $username = $acc_info->wechat_account_name;

        if (empty($username)) {
            $username = $target;
            $target = '';
        }
        $data = (new IndustryModel())->getIndustry($username, $password, $token, $target);

        return $this->industryFormat($data['creativeIndustryTypes'] ?? []);
    }

    private function industryFormat($data)
    {
        foreach ($data as $key => &$value) {
            if (isset($value['children']) && $value['children']) {
                $value['children'] = $this->industryFormat($value['children']);
            } else {
                unset($value['children']);
            }
        }
        return $data;
    }

    /**
     * 获取Logo
     * @param Input $input
     * @return array
     */
    public function getUCLogo(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $acc_info = $media_account_model->getDataByAccountId($input['account_id'], MediaType::UC);
        if (!$acc_info) {
            throw new AppException('找不到对应的渠道数据');
        }
        $target = $acc_info->account_name;
        $password = $acc_info->account_password;
        $token = $acc_info->access_token;
        $username = $acc_info->wechat_account_name;

        if (empty($username)) {
            $username = $target;
            $target = '';
        }
        $data = (new LogoModel())->getLogo($username, $password, $token, $target);
        return $data['mcLogos'] ?? [];
    }

    /**
     * id转词汇
     * @param array $input
     * @param $media_type
     * @return array
     */
    public function getWordById($input, $media_type)
    {
        return [];
    }


    /**
     * 获取人群包列表
     * @param $account_id_list
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($account_id_list, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        return (new OdsUCAudiencePackageLogModel())->getListByCompany($account_id_list, $page, $rows, $id, $name, $tag, $source, $account_id);
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        $model = new OdsUCAudiencePackageLogModel();
        return $model->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    /**
     * @inheritDoc
     */
    public function createConvert(ConvertCreateParam $param)
    {
        $convert_data = (new AdConvertModel())->add($param);
        $convert_data['convert_id'] = $convert_data['adConvertIds'][0];
//        $convert_data['convert_id'] = ********;
        return $convert_data;
    }

    /**
     * 新建渠道包
     * @param ChannelPackageParam $param
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {

    }

    /**
     * 获取UC转化类型列表
     * 调试用看转化类型用
     * @param $input
     * @return array|mixed
     */
    public function getConvertType($input)
    {
        $media_account_model = new MediaAccountModel();
        $account_param = $media_account_model->getDataByAccountId(*********, MediaType::UC);

        $convert_type_list = (new ConvertModel())->getConvertType(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name, $input['objective_type']);
        return $convert_type_list ?? [];
    }

    /**
     * 根据指定的推广组id获取推广创意样式模板（包含搜索和信息流）-getCreativeTemplates
     * @param $account_id
     * @param $ad_group_id
     * @return array|mixed
     */
//    public function getCreativeTemplates ($ad_group_id, MediaAccountInfoParam $account_param)
    public function getCreativeTemplates($account_id = '', $ad_group_id = '')
    {
        $acc_id = empty($account_id) ? ********* : $account_id;
        $param['adGroupId'] = empty($ad_group_id) ? ********* : (int)$ad_group_id;
        $media_account_model = new MediaAccountModel();
        $account_param = $media_account_model->getDataByAccountId($acc_id, MediaType::UC);
        $campaign_info = (new CreativeModel())->getTemplate(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name, $param);
        return $campaign_info ?? [];
    }

    /**
     * 获取程序化创意支持的组件模板-getMaterialTemplates
     * @param MediaAccountInfoParam $account_param
     * @return array|mixed
     */
    public function getMaterialTemplates(MediaAccountInfoParam $account_param)
    {
        $procedural_templates_info = (new CreativeModel())->getMaterialTemplates(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name);
        return $procedural_templates_info ?? [];
    }

    /**
     * 获取推荐投放列表-listRecommendDelivery
     * @param MediaAccountInfoParam $account_param
     * @return array|mixed
     */
    private function getRecommend(MediaAccountInfoParam $account_param)
    {
        $procedural_templates_info = (new CampaignModel())->getRecommend(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name);
        return $procedural_templates_info ?? [];
    }


    /**
     * 已创建好的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $campaign_info = (new CampaignCreateMediaLogModel())->getCreateLog(MediaType::UC, $account_param->account_id, $param->ad1_name_text);
        return $campaign_info ? $campaign_info->campaign_id : 0;
    }


    /**
     * 创建广告组-返回创建的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $otherSetting */
        $other_setting = $param->other_setting;
        $data = [
            'name' => $param->ad1_name_text,
            'objectiveType' => $other_setting->objective_type,
            'paused' => $setting->paused_adgroup, //默认关闭
        ];

        if (empty($account_param->wechat_account_name)) {
            $account_param->wechat_account_name = $account_param->account_name;
            $account_param->account_name = '';
        }

        $result = (new AdGroupModel())->createAdGroup(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name,
            new AdGroupCreateParam($data));
        $ad_group_id = $result['adGroupIds'][0];
        (new CampaignCreateMediaLogModel())->addCreateLog($param->media_type, $param->account_id, $ad_group_id, $param->ad1_name_text);


        return $result['adGroupIds'][0];
    }

    /**
     * @param $subject
     * @param $platform
     * @param $agent_id
     * @param $site_id
     * @param $url
     * @return string
     */
    private function getCompleteUrl($subject, $platform, $agent_id, $site_id, $url)
    {
        if ($subject != 1) {
            return '';
        }

        return PlatformAD::create($platform)->getSiteLDYUrl($agent_id, $site_id, $url);
    }

    /**
     * 整合数据上报处理
     * convertType 1 = 激活   1000 = 付费
     * @param ADTaskParam $param
     * @return array
     */
    private function convertChainTypes(ADTaskParam $param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        $data = [];
        // 单价出 - 单独 1-激活、27-注册、1000-付费 -- 并且没有深度的情况下empty($param->getSiteConfig()->deep_external_action)
        if (in_array($param->getSiteConfig()->convert_type, [1, 27, 1000]) && empty($param->getSiteConfig()->deep_external_action)) {
            $data[0]['convertType'] = (int)$param->getSiteConfig()->convert_type;
            $data[0]['tcpa'] = (int)$setting->cpc_bid; // 出价
            $data[0]['cvrRatio'] = 0;
            $data[0]['deliveryType'] = (int)$setting->delivery;
            // 选择ROI  激活+roi or 付费+roi or 注册+roi
            if ($param->getSiteConfig()->convert_data_type == 1) {
                $data[0]['roiTarget'] = (string)$setting->roi_target;
                $data[0]['roiDeliveryType'] = (int)$setting->delivery_roi;
            }
            // 如果有深度转化
        } else if (!empty($param->getSiteConfig()->deep_external_action) && $param->getSiteConfig()->deep_external_action == 1000) {
            // 如果有deep_external_action深度 且 convert_data_type=1的情况下  默认付费+ROI
            if ($param->getSiteConfig()->convert_data_type == 1) {
                $data[0]['convertType'] = (int)$param->getSiteConfig()->deep_external_action;
                $data[0]['tcpa'] = (int)$setting->cpc_bid; // 出价
                $data[0]['cvrRatio'] = 0;
                $data[0]['deliveryType'] = (int)$setting->delivery;
                // 选择ROI 付费+roi
                $data[0]['roiTarget'] = (string)$setting->roi_target;
                $data[0]['roiDeliveryType'] = (int)$setting->delivery_roi;

            } else {// 激活加付费， 注册加付费
                $convert_types = [$param->getSiteConfig()->deep_external_action, $param->getSiteConfig()->convert_type]; // 付费加激活， 付费加注册
                foreach ($convert_types as $type_key => $type_value) {
                    $data[$type_key]['convertType'] = (int)$type_value;
                    $data[$type_key]['tcpa'] = $type_value === $param->getSiteConfig()->deep_external_action ? (int)$setting->tcpa : (int)$setting->cpc_bid;
                    $data[$type_key]['cvrRatio'] = 0;
                    $data[$type_key]['deliveryType'] = (int)$setting->delivery;
                }
            }
        }
        return array_values($data) ?? [];
    }


    /**
     * 创建广告计划-返回创建的广告计划id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $campaign = new CampaignCreateParam([
            'adGroupId' => (int)$param->ad1_id,
            'name' => $param->ad2_name_text,
            'type' => (int)$other_setting->objective_type,
            'optTarget' => 3,
            'delivery' => (int)$setting->delivery,
            'chargeType' => (int)$setting->charge_type,
            'trackArgs' => 111,
            'budget' => $setting->budget_type == 1 ? '-1' : (int)$setting->budget,
            'enableAnxt' => $setting->enable_anxt,
            'paused' => $setting->paused_campaign,
            'cpcBid' => (float)$setting->cpc_bid
        ]);
        // 落地页地址 download_type == DOWNLOAD_URL 为落地页    download_type == DOWNLOAD_URL_JF 是锦帆建站落地页
        $ldy = $other_setting->download_type == UcEnum::DOWNLOAD_URL ? $other_setting->getUcPageWebUrlMapInfo(
            $param->platform,
            $param->agent_id,
            $param->site_id,
        ) : '';

        $jf_down_url_id = 0;
        if ((isset($other_setting->page_map[$account_param->account_id]) && $other_setting->page_map[$account_param->account_id]) && $other_setting->download_type == UcEnum::DOWNLOAD_URL_JF) {
            $page_map = $other_setting->getUcPageExternalUrlMapInfo($account_param->account_id);
            $jf_down_url_id = $page_map['page_id'] ?? 0;
            if ($jf_down_url_id == 0) {
                throw new AppException('找不到账号' . $account_param->account_id . '的锦帆落地页的信息');
            }
        }

        // UC小程序处理
        if ($other_setting->download_type === UcEnum::DOWNLOAD_URL_MIN_GAME) {
            if (empty($param->getSiteConfig()->sdk_ext['mini_game_original_id'])) {
                throw new AppException('小游戏APPID错误, 请在SDK管理配置UC小程序APPID和对应落地页');
            }
        }

        // 处理UC 落地页问题
        if ($other_setting->objective_type == 1 && ($other_setting->download_type === UcEnum::DOWNLOAD_URL || $other_setting->download_type === UcEnum::DOWNLOAD_URL_JF)) {
            $jf_ldy = $page_map['online_url'] ?? '';
            if (empty($ldy) && empty($jf_ldy)) {
                throw new AppException('使用锦帆落地页和自定义落地页类型时,落地页地址url不能为空');
            }
            $ldy = $ldy ?: $jf_ldy;
        }


        $campaign->setObjectives(new ObjectivesParam([
            'miniAppId' => $other_setting->download_type === UcEnum::DOWNLOAD_URL_MIN_GAME ? $param->getSiteConfig()->sdk_ext['mini_game_original_id'] : '',
            'miniAppPath' => $other_setting->download_type === UcEnum::DOWNLOAD_URL_MIN_GAME ? $param->getSiteConfig()->ext['mini_game_tracking_parameter'] : '',
            'targetUrl' => $ldy,
            'siteId' => $other_setting->objective_type == 4 ? (int)$jf_down_url_id : '',
            'appKey' => $other_setting->objective_type == 2 ? (int)$param->getSiteConfig()->appid : '',
            'appName' => $other_setting->app_name,
//            'packageKey' => $other_setting->objective_type == 4 ? (int)$param->getSiteConfig()->appid : '',
            'downloadUrl' => $other_setting->objective_type == 4 ? $param->getSiteConfig()->download_url : '',
//            // 以下 有downloadUrl 时候必填
            'packageName' => $other_setting->objective_type == 4 ? $param->getSiteConfig()->package : '',
            'versionName' => $other_setting->objective_type == 4 ? $other_setting->version_name : '',
            'developer' => $other_setting->objective_type == 4 ? $other_setting->developer : '',
            'functionDesc' => $other_setting->objective_type == 4 ? $other_setting->function_desc : '',
            'permission' => $other_setting->objective_type == 4 ? PlatformAbility::permissionURL($param->platform, $param->getSiteConfig()->game_id) : '',
            'privacy' => $other_setting->objective_type == 4 ? PlatformAbility::privateURL($param->platform, $param->getSiteConfig()->game_id) : '',
            'updateTime' => $other_setting->objective_type == 4 ? (int)(date('Ymd')) : '',
            'appLogo' => $other_setting->objective_type == 4 ? $other_setting->logo_map[$account_param->account_id]['logo_url'] : '',
            // 通用链接选填 白名单控制
            'admFixedUlk' => ''
        ]));

        // todo 新文档 链路参数 ConvertChainType 约束条件
        $convert_chain_types = $this->convertChainTypes($param);

        $campaign->setConvertInfoType(new ConvertInfoTypeParam([
            'convertId' => (int)$param->getSiteConfig()->convert_id,
            'convertChainId' => $param->site_config['plat_id'] == PlatId::MINI ? 101 : 36, // 36是落地页应用 101是小游戏程序
            'trackMethod' => $param->getSiteConfig()->convert_source_type === ConvertSourceType::SDK ? 5 : 4, // 转化回传
            'skipFirstStage' => (int)$setting->skip_first_stage, // 默认1
            'convertChainTypes' => $convert_chain_types,
        ]));

        if ($param->getSiteConfig()->deep_external_action == 1000) {
            $campaign->convertRepeatType = (int)$setting->convert_repeat_type;
        }

        $campaign->setSchedule($setting->schedule_time, $setting->start_date, $setting->end_date);
        /* @var ADTargetingContentParam $targeting */

        $targeting = $param->targeting;

//        /**
//         * 额外地域代号去除
//         */
//        if (
//            ($param->platform == 'TW' && !in_array($param->game_id, [2921, 2915, 2917, 2919, 2949, 2953, 2961, 2959, 2957, 2955, 2967, 1943, 2777, 2963, 2965, 2971, 2969, 1137, 2887, 2885, 2875, 2873, 2877, 1350, 2853, 1805, 1813, 1801, 1781, 1803, 2857, 1811, 2491, 2047, 2477, 2585, 376, 2161, 2793, 2179, 1509, 1957, 2219, 2031, 2639, 767, 2663, 1817, 2067, 1859, 1777, 1139, 2613, 2057, 1472, 2671, 1482, 1500, 2265, 1921, 1913, 1933, 2073, 683, 1935, 2163, 1250, 2215, 1416, 2687, 885, 2167, 970, 2311, 1140, 935, 2703, 2207, 1895, 619, 1340, 1953, 2787, 1520, 1723, 2475, 2545, 2757, 2173, 1941, 932, 2063, 1995, 2085, 2071, 1823, 1705, 1462, 1717, 1474, 1987, 2177, 2775, 2661, 1649, 1669, 2143, 1789, 1855, 1418, 1951, 1496, 2027, 2029, 2287, 1081, 1907, 2089, 1659, 687, 2781, 1851, 1911, 2607, 2115, 2831, 1961, 1564, 2075, 1490, 1388, 2677, 1470, 2693, 1959, 1285, 2699, 1290, 1422, 1827, 2525, 2159, 2691, 647, 2741, 1917, 1797, 2603, 1263, 2147, 2155, 1354, 1883, 1581, 934, 2217, 1583, 1967, 2111, 1745, 1259, 1204, 648, 2139, 1963, 1261, 2611, 2037, 1915, 2187, 2705, 1220, 2739, 1945, 2169, 2049, 1284, 2009, 1360, 1123, 1755, 1919, 2657, 1769, 2445, 2675, 2149, 1121, 2183, 2123, 1579, 950, 2129, 1293, 1673, 736, 686, 2745, 1775, 933, 2785, 1122, 1128, 349, 1955, 1887, 1821, 1839, 2121, 920, 2707, 1258, 2101, 1502, 2131, 1819, 1247, 2835, 2805, 1203, 2135, 2379, 1713, 2117, 2247, 2151, 2065, 773, 1492, 2165, 2527, 1905, 1460, 2145, 2731, 1352, 1837, 1885, 2547, 940, 1356, 2125, 1385, 1923, 1635, 1759, 2819, 690, 1605, 2113, 2249, 1257, 2033, 2809, 1524, 2133, 2605, 949, 971, 2769, 1989, 688, 1901, 1667, 2107, 2443, 1709, 2171, 1607, 2465, 2767, 766, 1687, 691, 2483, 1891, 2567, 2001, 2539, 471, 1903, 2807, 1985, 1675, 1194, 1426, 1296, 1909, 1767, 2689, 1498, 1562, 2729, 2039, 1267, 1761, 2099, 1937, 2263, 1893, 2457, 1861, 1965, 2157, 1050, 2035, 2695, 2713, 1867, 693, 2091, 1939, 2635, 1657, 1420, 1639, 1693, 1316, 1931, 2795, 2501, 1641, 1577, 2231, 1809, 2077, 1873, 775, 1685, 774, 1947, 1899, 1835, 2599, 2733, 1897, 2397, 2041, 1637, 2841, 726, 936, 764, 1969, 1971, 1749, 1424, 1787, 969, 583, 2755, 1303, 2549, 1799, 1280, 915, 1979, 2563, 2565, 1829, 1877, 2467, 2455, 2629, 2223, 2153, 2799, 2655, 1297, 2137, 1342, 1757, 2025, 1136, 2087, 765, 1494, 1881, 919, 2059, 2119, 600, 2213, 1981, 1773, 1400, 1534, 1647, 1807, 1262, 1695, 1348, 2637, 1889, 2175, 1560, 2061, 2079, 1815, 1949, 1458, 2459, 1743, 938, 1480, 1661, 1869, 2097, 2685, 1488, 2141, 1929, 1879, 1875, 1925, 1927, 1871, 1849, 2925])) ||
//            ($param->platform == 'xinxin' && !in_array($param->game_id, [1336, 1332, 1056, 400, 1370, 51, 992, 1326, 97, 1338, 1310, 1322, 1132, 1912, 54, 1650, 1320, 1342, 454, 50, 1312, 53, 94, 1530, 1324, 694, 1584, 1806, 2040, 1350, 36, 1318, 1308, 412, 1330, 1186, 96, 101, 1340, 1540, 56, 1328, 1334, 542, 55, 452]))
//        ) {
//            if (!$targeting->region) {
//                $targeting->region = [];
//                $time = time();
//                if ($time >= 1602669600) {
//                    $targeting->all_region = 0;
//                    $targeting->region = LimitADRegionTask::UC_COUNTRY_EXCEPT_GUANGDONG;
//                }
//            } else {
//                $time = time();
//                if ($time >= 1602669600) {
//                    $targeting->region = array_diff($targeting->region, [LimitADRegionTask::UC_CHOSEN_GUANGDONG]);
//                    $targeting->region = array_diff($targeting->region, LimitADRegionTask::UC_GUANGDONG_CITY);
//                    $targeting->region = array_values($targeting->region);
//                }
//            }
//        }

        $campaign->setTargetings(new TargetingsParam([
            'audienceTargeting' => $targeting->audience_targeting,
            'allRegion' => $targeting->all_region,
            'include_audiences' => $targeting->include_audiences,
            'exclude_audiences' => $targeting->exclude_audiences,
            'region' => $targeting->region,
            'gender' => $targeting->gender,
            'age' => $targeting->age,
            'interest' => $targeting->interest,
            'word' => $targeting->word,
            'networkEnv' => $targeting->network_env,
            'intelliTargeting' => (int)$targeting->intelli_targeting,
            'autoExpand' => $targeting->auto_expand,
            'platform' => $other_setting->objective_type == 1 ? $other_setting->platform_os : ($param->getSiteConfig()->game_type == '安卓' ? '010' : '001'),
            'convertFilter' => (int)$setting->convert_filter,
            'autoTargeting' => (int)$targeting->auto_targeting,
        ]));

        if (empty($account_param->wechat_account_name)) {
            $account_param->wechat_account_name = $account_param->account_name;
            $account_param->account_name = '';
        }

        $result = (new CampaignModel())->createCampaign(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name,
            $campaign);

        return $result['campaignIds'][0];
    }

    /**
     * 自定义创意模板类型
     * 创意的样式组id 1大图 2小图 4三图8GIF 16横版视频 32竖版视频 64竖图 128直播 512topview竖版视频
     * @param $creative
     * @return int
     */
    private function getCreativeStyle($creative)
    {
        if (isset($creative['video_info'])) {
            return $creative['video_info']['width'] > $creative['video_info']['height'] ? 16 : 32;
        } else {
            if ($creative['image_info']['width'] > $creative['image_info']['height']) {
                return 1;
            } else {
                return 64;
            }
        }
    }

    /**
     * 程序化模板类型
     * styleType :{
     * 100080(标题)  key: c_text    必填，10-70字符
     * 101080(大图) 101081(小图) 101082(竖图)  key: c_image_url
     * 105080(三图)  key: c_image_url_picture_1  c_image_url_picture_2  c_image_url_picture_3
     * 105081(竖版视频) 105082(横板视频)  key: c_video_id_video_1
     * 105084(推广来源)  key: c_text   1-20个字符
     * }
     * @param $creative
     * @return int
     */
    private function getProceduralCreativeStyle($creative)
    {
        if (isset($creative['video_info'])) {
            return $creative['video_info']['width'] > $creative['video_info']['height'] ? 105082 : 105081;
        } else {
            if ($creative['image_info']['width'] > $creative['image_info']['height']) {
                return 101080;
            } else {
                return 101082;
            }
        }
    }

    /**
     * componentTypes 数据整合
     * styleType :{
     * 100080(标题)  key: c_text    必填，10-70字符
     * 101080(大图) 101081(小图) 101082(竖图)  key: c_image_url
     * 105080(三图)  key: c_image_url_picture_1  c_image_url_picture_2  c_image_url_picture_3
     * 105081(竖版视频) 105082(横板视频)  key: c_video_id_video_1
     * 105084(推广来源)  key: c_text   1-20个字符
     * @param $val
     * @param $styleType
     * @param $source
     * @param $material_media_id_map
     * @return false|string
     */
    protected function componentTypesJsonData($creative_list, $source, $material_media_id_map, $word_list)
    {
        foreach ($creative_list as $key => $val) {
            $styleType = $this->getProceduralCreativeStyle($val);
            if (isset($val['video_info'])) {
                $result[] =
                    [
                        'styleType' => $styleType,
                        'content' => json_encode([
                            'c_video_video_1' => $material_media_id_map[$val['video_info']['id']]['url'],
                            'c_image_url_video_1' => $material_media_id_map[$val['cover_info']['id']]['url'],
                            'c_text_text_5' => $val['cover_info']['width'], // 图片宽度 '720' 竖版视频 ： '640' 横板视频
                            'c_text_text_6' => $val['cover_info']['height'], // 图片高度 '1280' 竖版视频 : '360' 横板视频
                            'c_text_text_3' => 0, // 视频播放时长
                            'c_text_text_4' => 0, // 视频大小
                        ])
                    ];
            } else {
                $result[] =
                    [
                        'styleType' => $styleType,
                        'content' => json_encode([
                            'c_image_url' => $material_media_id_map[$val['image_info']['id']]['url'],
                            'c_image_id' => $material_media_id_map[$val['image_info']['id']]['id'],
                            'c_image_url_w' => $val['image_info']['width'], // 竖图 1080 * 1920  横图 640*360
                            'c_image_url_h' => $val['image_info']['height'],
                        ])
                    ];
            }
        }

        // 推广来源只能有一个
        $result[] = [
            'styleType' => 105084,
            'content' => json_encode([
                'c_text' => $source
            ])
        ];
        // 标题 至少一个
        foreach ($word_list as $word) {
            $result[] = [
                'styleType' => 100080,
                'content' => json_encode([
                    'c_text' => $word
                ])
            ];
        }

        return $result;
    }

    /**
     * 广告3级别创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
//        $style_procedural_templates_type = $this->getMaterialTemplates($account_param);
        // 程序化创意
        $is_procedural = BatchAD::CREATIVE_PROGRAM_MODE;

        if ($param->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            $custom_creative_list = [];
            // 获取模板
            $ccp = new CreativeProceduralCreateParam([
                'adGroupId' => (int)$param->ad1_id,
                'campaignId' => (int)$param->ad2_id,
                'clickMonitorUrl' => $param->getSiteConfig()->action_track_url,
                'componentTypes' => $this->componentTypesJsonData($param->creative_list, $other_setting->common_source, $material_media_id_map, $param->word_list),
                'label' => $setting->ucLabStringChanArray($setting->label),
                'industry' => array_map(function ($data) {
                    return (int)$data;
                }, $setting->industry),
                'logoId' => (int)($other_setting->logo_map[$param->account_id]['logo_id'] ?? 0),
            ]);

            $custom_creative_list[] = $ccp;
        } else {
            $style_templates_type = $this->getCreativeTemplates($account_param->account_id, $param->ad1_id);
            $style_type_arr = array_column($style_templates_type['adGroupCreativeTemplates'], 'creativeTemplateStyleType', 'creativeTemplateId');
            $is_procedural = BatchAD::CREATIVE_CUSTOM_MODE;
            $custom_creative_list = [];
            foreach ($param->creative_list as $key => $val) {

                $style = $this->getCreativeStyle($val);
                // 获取模板
                $style_type = (int)($style_type_arr[$style]) ?? 0;
                if (isset($val['video_info'])) {
                    $ccp = new CreativeCreateParam([
                        'adGroupId' => (int)$param->ad1_id,
                        'campaignId' => (int)$param->ad2_id,
                        'style' => $style,
                        'styleType' => $style_type,
                        'showMode' => (int)$setting->show_mode,
                        'clickMonitorUrl' => $param->getSiteConfig()->action_track_url,
                        'videoId' => (int)($material_media_id_map[$val['video_info']['id']]['id'] ?? 0),
                        'content' => json_encode([
                            'title' => $val['title'],
                            'pic1_img' => $material_media_id_map[$val['cover_info']['id']]['url'],
                            'source' => $other_setting->common_source
                        ]),
                        'label' => $setting->ucLabStringChanArray($setting->label),
                        'industry' => array_map(function ($data) {
                            return (int)$data;
                        }, $setting->industry),
                        'logoId' => (int)($other_setting->logo_map[$param->account_id]['logo_id'] ?? 0),
                        'commonSource' => $other_setting->common_source,
                    ]);
                } else {
                    $ccp = new CreativeCreateParam([
                        'adGroupId' => (int)$param->ad1_id,
                        'campaignId' => (int)$param->ad2_id,
                        'style' => $style,
                        'styleType' => $style_type,
                        'showMode' => (int)$setting->show_mode,
                        'clickMonitorUrl' => $param->getSiteConfig()->action_track_url,
                        'content' => json_encode([
                            'title' => $val['title'],
                            'pic1_img' => $material_media_id_map[$val['image_info']['id']]['url'],
                            'source' => $other_setting->common_source
                        ]),
                        'label' => $setting->ucLabStringChanArray($setting->label),
                        'industry' => array_map(function ($data) {
                            return (int)$data;
                        }, $setting->industry),
                        'logoId' => (int)($other_setting->logo_map[$param->account_id]['logo_id'] ?? 0),
                        'commonSource' => $other_setting->common_source
                    ]);
                }

                $custom_creative_list[] = $ccp;
            }
        }

        if (empty($account_param->wechat_account_name)) {
            $account_param->wechat_account_name = $account_param->account_name;
            $account_param->account_name = '';
        }

        (new CreativeModel())->createCreative(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name,
            $custom_creative_list,
            $is_procedural
        );

        return [];
    }

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        return '';
    }

    /**
     * 补充定向包信息
     * @param array $targeting_info
     * @param bool $is_return
     * @return array
     */
    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        return [];
    }

    /**
     * 根据ids获取人群包内容
     * @param array $ids
     * @param int $status
     * @return Collection
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        return collect();
    }

    /**
     * 获取计划行为徕卡
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        return [];
    }

    /**
     * 获取计划兴趣徕卡
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        return [];
    }

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        return [];
    }

    /**
     * 获取计划徕卡兴趣id转词汇
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        return [];
    }

    /**
     * 获取流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        return [];
    }

    /**
     * 推送人群包
     * @param array $audience_account_id_list // 账号列表
     * @param array $need_push_audience_list_data // 人群包数据
     * @param array $target_account_ids // 投放计划的账号id
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        $media_account_model = new MediaAccountModel();
        $audience_account_id_map = $media_account_model->getDataInAccountIds(
            $audience_account_id_list,
            MediaType::UC
        )->keyBy('account_id');

        if (!$audience_account_id_map) {
            throw new AppException('找不到' . implode(',', $audience_account_id_list) . '的账号信息');
        }
        $uc_share_audience = new AudienceModel();

        $is_push = false;

        foreach ($need_push_audience_list_data as $key => $value) {
            $account_info = $audience_account_id_map[$key];
            if (!$account_info) {
                throw new AppException("找不到{$account_info->account_id}的信息");
            }
            // 把当前要推送的账号人群包数据抽离
            $package_id_list = array_column($value, 'audience_id');
            // 不为空
            if ($package_id_list) {
                $username = $account_info->wechat_account_name;
                $password = $account_info->account_password;
                $token = $account_info->access_token;
                $target = $account_info->account_name;
                $data['accountIdList'] = $target_account_ids;
                $data['packageIdList'] = $package_id_list;
                $result = $uc_share_audience->sharePackage($username, $password, $token, $target, $data);
                if ($result['status']) {
                    $is_push = true;
                }
            }
        }
        if ($is_push) {
            // 后面大数据提供接口来同步
            $uc_audience = new UCTaskModel();
            $uc_audience->audiencePackage($target_account_ids[0]);
        }
        return [];
    }

    /**
     * 已创建好的广告组id
     * @param $campaign_name
     * @param $account_id
     * @param $access_token
     * @return int
     */
    public function getCampaignIdByName($campaign_name, $account_id, $access_token)
    {
        $result = (new CampaignCreateMediaLogModel())->getCreateLog(MediaType::UC, $account_id, $campaign_name);
        if ($result) {
            return $result->campaign_id;
        } else {
            return 0;
        }
    }

    /**
     * 修改广告计划-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告计划的开关-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告创意的开关-返回修改的广告创意id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * @param $company
     * @param $audience_md5
     * @return void
     */
    public function getTargetingDataByAD2($company, $audience_md5)
    {
        throw new AppException('UC暂时不支持，复制计划定向');
    }

    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        return [];
    }

    public function updateADAnalysisFirstClass(array $data)
    {
        return [];
    }

    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        return [];
    }

    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return [];
    }

    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        return [];
    }

    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        return [];
    }

    public function addADTaskByApi(array $input)
    {
        return 0;
    }

    public function updateAndroidChannelPackage(array $input)
    {
    }

    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisDeepBidOrROI() method.
    }

    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement deleteADAnalysisAD() method.
    }

    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassBudget() method.
    }

    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisAccountBudget() method.
    }

    public function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    public function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    function getTargetAudienceEstimate(array $target, $account_info)
    {
        // TODO: Implement getTargetAudienceEstimate() method.
    }

    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    /**
     * @inheritDoc
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if ($other_setting->objective_type == 1 && $other_setting->target_url_content) {
            // 切广告位
            $option = [json_encode([
                'game_id' => $param->getSiteConfig()->game_id,
                'game_pack' => $param->getSiteConfig()->game_pack,
                'adid' => $other_setting->target_url_content,
            ])];

            PlatformAD::create($param->platform)->switchAD(
                [$param->site_id],
                $option,
                date('Y-m-d H:i:s'),
                0,
                0,
                $param->getSiteConfig()->convert_source_type == ConvertSourceType::API ? 0 : 1,
                $param->creator);
        }
    }

    /**
     * 计划 三 步 骤
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告二级
        if (!$param->ad2_id) {
            try {
                $param->ad2_id = $this->createAD2($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // $this->bindRecommendReasons($param,$account_param);

        // 新建广告三级
        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));


    }

    /**
     * @inheritDoc
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
    }

    /**
     * @inheritDoc
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        if ($param->targeting->getAllAudienceIdList()) {
            $need_push_audience_list_data = $this->getNeedUploadAudienceList(
                $param->targeting->getAllAudienceIdList(),
                $param->account_id,
                $param->platform,
                $param->company
            );
            if ($need_push_audience_list_data->isNotEmpty()) {
                # todo 去重：获取除当前账号ID下其他唯一账号ID
                $audience_account_id_list = $need_push_audience_list_data->unique('account_id')->pluck('account_id')->diff([$param->account_id])->toArray();
                if ($audience_account_id_list) {
                    $this->pushAudience(
                        $audience_account_id_list,
                        $need_push_audience_list_data->unique('audience_id')->groupBy('account_id')->toArray(), // 去除重复账号人群包值取唯一
                        [$param->account_id]
                    );
                } else {
                    throw new AppException("人群包从未上传过");
                }
            }
        }
    }

    /**
     * 检查是否打包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /**
         * @var ADOtherSettingContentParam $other_setting ;
         */
        $other_setting = $param->other_setting;
        // 如果是需要等待打包的任务，需要走解析包链接
        if ($param->is_wait_package == 1 || $other_setting->objective_type == 4) {

            $data = array_merge($param->toArray(), [
                'ad_task_list_id' => $param->id,
                'target_url' => '',
                'sdk_ext' => $param->getSiteConfig()->sdk_ext,
                'game_type' => $param->getSiteConfig()->game_type,
                'download_url' => $param->getSiteConfig()->download_url,
            ]);

            (new SiteMQLogic)->produceUCTask($data, 1);

            throw new AppException('等待打包中', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }

    }

    /**
     * @inheritDoc
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::UC, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        $result = [];
        foreach ($material_file_list as $material_file) {
            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::UC;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;
            if ($upload_info_list[$material_file->id] ?? '') {
                $result[$material_file->id] = [
                    'id' => $upload_info_list[$material_file->id]->media_material_file_id,
                    'url' => $upload_info_list[$material_file->id]->url,
                ];
            } else {
                if (in_array(
                    $material_file->file_type,
                    [MaterialFileModel::FILE_TYPE_IMAGE, MaterialFileModel::FILE_TYPE_COVER])
                ) {
                    $media_file_info = $this->uploadImage($material_file, $account_param->account_id, $account_param->access_token);
                } else {
                    $media_file_info = $this->uploadVideo($material_file, $account_param->account_id, $account_param->access_token);
                }
                $insert['media_material_file_id'] = $media_file_info['id'];
                $insert['url'] = $media_file_info['url'];
                $result[$material_file->id] = [
                    'id' => $media_file_info['id'],
                    'url' => $media_file_info['url']
                ];
                if ($media_file_info['id']) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * @inheritDoc
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
    }

    /**
     * 创意素材处理
     * @param ADTaskParam $param
     * @return mixed|void
     */
    function prepareCreativeList(ADTaskParam $param)
    {
        $material_file_model = new MaterialFileModel();
        $ods_material_file_log_model = new OdsMaterialFileLogModel();

        $ids = $param->getImageCreativeID();

        $images_map = $material_file_model->getListByIds($ids)->keyBy('id');
        $new_images_map = [];

        foreach ($images_map as $file_id => $material_file) {
            if ($material_file->width > $material_file->height) {
                if ($material_file->width == 640 && $material_file->height == 360) {
                    continue;
                }
                $width = 640;
                $height = 360;
                $sub_path = $material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ? EnvConfig::MATERIAL_IMG_DIR_NAME : EnvConfig::MATERIAL_VIDEO_DIR_NAME;
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                $ext_array = explode('.', $material_file->filename);
                $base_name = $ext_array[0];
                $ext = end($ext_array);
                $ext_name = "{$base_name}_(UC投放扩展){$width}x{$height}.{$ext}";
                $file_data = $material_file_model->getDataByName($ext_name);
                if ($file_data) {
                    $new_images_map[$file_id] = (new MaterialFileParam($file_data))->toArray();
                } else {
                    $result = Image::thumb(
                        "$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename",
                        "$upload_path/$material_file->platform/$material_file->material_id/$ext_name",
                        '',
                        $width,
                        $height
                    );
                    if ($result) {
                        $insert_data = [
                            'platform' => $material_file->platform,
                            'media_type' => 0,
                            'material_id' => $material_file->material_id,
                            'file_type' => $material_file->file_type,
                            'filename' => $ext_name,
                            'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material_file->platform/$material_file->material_id/$ext_name",
                            'width' => $width,
                            'height' => $height,
                            'scale' => Math::div($width, $height),
                            'signature' => md5_file("$upload_path/$material_file->platform/$material_file->material_id/$ext_name"),
                            'bitrate' => 0,
                            'size' => filesize("$upload_path/$material_file->platform/$material_file->material_id/$ext_name"),
                            'format' => $material_file->format,
                            'uploader' => $material_file->uploader,
                            'create_time' => time(),
                            'update_time' => time(),
                            'is_del' => 0,
                            'is_ext' => 1
                        ];
                        $insert_data['id'] = $material_file_model->add($insert_data);
                        if (EnvConfig::ENV === 'production' && $insert_data['id']) {
                            $insert_data['insert_time'] = date("Y-m-d H:i:s", $insert_data['create_time']);
                            $insert_data['update_time'] = date("Y-m-d H:i:s", $insert_data['update_time']);
                            unset($insert_data['create_time']);
                            $ods_material_file_log_model->add($insert_data);
                        }
                        $new_images_map[$file_id] = $insert_data;
                    } else {
                        throw new AppException('扩展UC横屏图片失败');
                    }
                }
            }
        }
        foreach ($param->creative_list as $id => &$creative_info) {
            if (isset($creative_info['cover_info']['id']) && isset($new_images_map[$creative_info['cover_info']['id']])) {
                $creative_info['cover_info'] = [
                    'id' => $new_images_map[$creative_info['cover_info']['id']]['id'],
                    'url' => $new_images_map[$creative_info['cover_info']['id']]['url'],
                    'width' => $new_images_map[$creative_info['cover_info']['id']]['width'],
                    'height' => $new_images_map[$creative_info['cover_info']['id']]['height'],
                ];
            } elseif (isset($creative_info['image_info']['image_list']) && $creative_info['image_info']['image_list']) {
                foreach ($creative_info['image_info']['image_list'] as $image_key => &$image_value) {
                    if (isset($image_value['id']) && isset($new_images_map[$image_value['id']])) {
                        $image_value = [
                            'id' => $new_images_map[$image_value['id']]['id'],
                            'url' => $new_images_map[$image_value['id']]['url'],
                            'width' => $new_images_map[$image_value['id']]['width'],
                            'height' => $new_images_map[$image_value['id']]['height'],
                        ];
                    }
                }
            } elseif (isset($creative_info['image_info']['id']) && isset($new_images_map[$creative_info['image_info']['id']])) {
                $creative_info['image_info'] = [
                    'id' => $new_images_map[$creative_info['image_info']['id']]['id'],
                    'url' => $new_images_map[$creative_info['image_info']['id']]['url'],
                    'width' => $new_images_map[$creative_info['image_info']['id']]['width'],
                    'height' => $new_images_map[$creative_info['image_info']['id']]['height'],
                ];
            }
        }
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed|void
     */
    public function getAgentGroup(ADTaskParam $task_param)
    {
        if ($task_param->site_config['plat_id'] == PlatId::MINI) {
            return AgentGroup::UC_MINI_GAME;
        } else {
            return AgentGroup::UC;
        }
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaStatus()
    {
    }

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        // TODO: Implement getDiffMediaInventoryList() method.
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        return '';
    }

    public function getTransferSet($data)
    {
        // TODO: Implement getTransferSet() method.
    }

    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if (!$param->convert_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新账号余额信息
     *
     * @param $data
     *
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid( int $media_type, array $data, array $ad_format_target, string $update_field ) {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
}

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateFirstClassScheduleTime() method.
}

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateSecondClassScheduleTime() method.
}

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status() {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    public function updateADAnalysisADRaise( array $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
    }

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
