<?php

namespace App\Service\MediaAD;

use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\Project\projectModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\Tencent\ADTargetingContentParam;
use App\Param\ADServing\Tencent\Ada\ADOtherSettingContentParam;
use App\Param\ADServing\Tencent\Ada\ADSettingContentParam;
use App\Param\MediaAccountInfoParam;
use App\Param\Tencent\ProjectParam;
use Throwable;

class MediaTencentADA extends MediaTencent
{
    /**
     * 新建广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return void
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建动态广告
        if (!$param->ad3_ids) {
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建动态广告错误:' . $e->getMessage());
            }
        }

        // 新建项目
        if (!$param->ad1_id) {
            try {
                $ad1_id = $this->createProject($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad1_id = $ad1_id;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建项目错误:' . $e->getMessage());
            }
        }
    }

    /**
     * 创建创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;

        // icon上传
        if ($setting->brand_img) {
            $setting->brand_img = $this->uploadImageByUrl($param, $account_param, $setting->brand_img);
        }
        // 卖点图上传
        if ($setting->shop_img) {
            $setting->shop_img = $this->uploadImageByUrl($param, $account_param, $setting->shop_img);
        }
        // 主播卡
        if ($other_setting->promotion_card_id && in_array(TencentEum::SITE_SET_CHANNELS, $other_setting->site_set)) {
            $other_setting->promotion_card_id = $this->uploadImageByUrl($param, $account_param, $other_setting->promotion_card_id);
        }

        return $this->createProgramCreative($param, $account_param, $material_media_id_map, true);
    }

    public function createProject(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        $request = [
            'account_id' => $param->account_id,
            'automatic_site_enabled' => $other_setting->automatic_site_enabled,
            'begin_date' => $setting->begin_date ?: date('Y-m-d'),
            'bid_amount' => strval($setting->getBidAmount() * 100),
            'bid_mode' => $setting->billing_event,
            'bid_scene' => $setting->bid_scene, //与下字段互斥
            'bid_strategy' => $setting->bid_strategy, //与上字段互斥
            'project_name' => $param->ad1_name_text,
            'configured_status' => $setting->configured_status,
            'conversion_id' => $param->convert_id ? (int)$param->convert_id : '',
            'deep_conversion_behavior_bid' =>
                in_array($param->getSiteConfig()->deep_external_action, ['OPTIMIZATIONGOAL_FIRST_PURCHASE', 'OPTIMIZATIONGOAL_APP_PURCHASE']) ?
                    $setting->deep_conversion_spec['deep_conversion_behavior_spec']['bid_amount']
                    :
                    null
            ,
            'deep_conversion_worth_advanced_rate' =>
                $param->getSiteConfig()->deep_external_action == 'ADVANCED_GOAL_1DAY_PURCHASE_ROAS' ?
                    trim($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'])
                    :
                    null
            ,
            'deep_conversion_worth_rate' =>
                in_array($param->getSiteConfig()->deep_external_action, ['GOAL_1DAY_PURCHASE_ROAS', 'GOAL_7DAY_PURCHASE_ROAS']) ?
                    trim($setting->deep_conversion_spec['deep_conversion_worth_spec']['expected_roi'])
                    :
                    null
            ,
            'dynamic_creative_id' => $param->ad3_ids[0],
            'end_date' => $setting->end_date ?: '',
            'first_day_begin_time' => $setting->first_day_begin_time,
            'flow_optimization_enabled' => $other_setting->flow_optimization_enabled,
            'marketing_scene' => $other_setting->marketing_scene,
            'optimization_goal' => ConvertType::MEDIA[MediaType::TENCENT][$param->getSiteConfig()->convert_type],
            'promoted_object_id' => $param->getSiteConfig()->appid,
            'promoted_object_type' =>
                $param->getSiteConfig()->plat_id == 7 ?
                    TencentEum::PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT : (
                $param->getSiteConfig()->game_type == 'IOS' ?
                    TencentEum::PROMOTED_OBJECT_TYPE_APP_IOS
                    :
                    TencentEum::PROMOTED_OBJECT_TYPE_APP_ANDROID
                )
            ,
            'scene_spec' => $other_setting->scene_spec,
            'site_set' => $other_setting->site_set,
            'smart_delivery_platform' => 'SMART_DELIVERY_PLATFORM_EDITION_GAME',
            'speed_mode' => $setting->speed_mode,
            'time_series' => implode('', $setting->time_series),
            'total_budget' => strval($setting->total_budget * 100),
            'daily_budget' => strval($setting->daily_budget * 100),
        ];

        if ($param->getSiteConfig()->game_type == '安卓') {
            $request['app_android_channel_package_id'] = $param->getSiteConfig()->app_android_channel_package_id;
        }

        $project_param = new ProjectParam($request);
        $project_param->setTargeting($targeting);

        if ($setting->excluded_dimension_mode == 'zidingyi') {
            $project_param->targeting['excluded_converted_audience']['excluded_dimension'] = $setting->excluded_dimension;
        }

        return (new projectModel())->add($project_param, $account_param->access_token)['project_id'];
    }
}