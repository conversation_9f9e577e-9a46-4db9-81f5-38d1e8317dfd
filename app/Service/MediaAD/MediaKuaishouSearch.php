<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: 张中昊
 * Date: 2020/02/25
 * Time: 10:44
 */

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\ConvertSourceType;
use App\Constant\KuaishouEnum;
use App\Constant\ADFieldsENToCNMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Logic\DSP\ADAnalysisMQLogic;
use App\Logic\DSP\ADServingComposeAlgorithmLogic;
use App\Logic\DSP\ADServingLogic;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\ADTaskMasterMQLogic;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\Kuaishou\Ad\AdModel;
use App\Model\HttpModel\Kuaishou\ADUnit\ADUnitModel;
use App\Model\HttpModel\Kuaishou\Advertiser\AdvertiserModel;
use App\Model\HttpModel\Kuaishou\App\AppModel;
use App\Model\HttpModel\Kuaishou\AppCenter\AppCenterModel;
use App\Model\HttpModel\Kuaishou\Asset\AdvCard\AdvCardModel;
use App\Model\HttpModel\Kuaishou\BehaviorInterest\BehaviorInterestModel;
use App\Model\HttpModel\Kuaishou\Campaign\CampaignModel;
use App\Model\HttpModel\Kuaishou\Creative\CreativeModel;
use App\Model\HttpModel\Kuaishou\DMP\PopulationModel;
use App\Model\HttpModel\Kuaishou\V1ADVideo\V1ADVideoModel;
use App\Model\HttpModel\Kuaishou\V2\File\ADImage\ADImageModel;
use App\Model\HttpModel\Kuaishou\V2\File\ADVideo\ADVideoModel;
use App\Model\HttpModel\Kuaishou\WordInfo\WordInfoModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouAdCreatorVideoLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouAppManagementLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouCampaignLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouCreativeHisLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouCreativeLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouNativeUserLog;
use App\Model\SqlModel\DataMedia\OdsKuaishouOrientationLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouUnitHisLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouUnitLogModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTargetingPacketModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADComposeContent\ADComposeConfigParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeImageParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeListParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoCoverParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\ADServing\ADTaskCreateParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Kuaishou\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\Kuaishou\Basics\ADSettingContentParam;
use App\Param\ADServing\Kuaishou\ADTargetingContentParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Kuaishou\AD\CampaignParam;
use App\Param\Kuaishou\AD\CreateCascadeAllParam;
use App\Param\Kuaishou\AD\CreateUnitAndCreativeParam;
use App\Param\Kuaishou\AD\CreativeCustomListParam;
use App\Param\Kuaishou\AD\CreativeCustomParam;
use App\Param\Kuaishou\AD\CreativeProgramParam;
use App\Param\Kuaishou\AD\UnitParam;
use App\Param\Kuaishou\Search\AdUnitCreateSearchParam;
use App\Param\Kuaishou\Search\CampaignCreateSearchParam;
use App\Param\Kuaishou\AD2\AdCardCreateParam;
use App\Param\Kuaishou\AD2\GiftDataParam;
use App\Param\Kuaishou\Search\CreativeBatchCreateSearchParam;
use App\Param\Kuaishou\Search\CreativeCreativesSearchParam;
use App\Param\Kuaishou\Search\TargetSearchParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Param\SiteConfigParam;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Math;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use Throwable;

class MediaKuaishouSearch extends AbstractMedia
{

    /**
     * @param int $ad_id
     * @return mixed
     */
    public function getKuaishouFeedDeliverySearchSuggestKeywordList(int $ad_id)
    {
        $ad_info = (new OdsKuaishouUnitLogModel())->getDataByAdId($ad_id);
        if (!$ad_info) {
            throw new AppException('找不到对应广告组下的信息');
        }
        $account_info = (new MediaAccountModel())->getDataByAccountId($ad_info->account_id);
        if (!$account_info) {
            throw new AppException("找不到账号{$ad_info->account_id}的信息");
        }
        return (new WordInfoModel())->getFeedDeliverySearchSuggestKeywordList(
            $account_info->account_id,
            $account_info->access_token,
            $ad_id
        );
    }

    /**
     * 推送素材
     * @param $account_id
     * @param $photo_id
     * @param $target_account_id
     * @return string
     */
    public function pushVideo($account_id, $photo_id, $target_account_id)
    {
        $media_account_model = new MediaAccountModel();
        $account_info = $media_account_model->getDataByAccountId($account_id);
        if (!$account_info) {
            throw new AppException("找不到账号:{$account_id}的信息");
        }
        $access_token = $account_info->access_token;
        $result = (new V1ADVideoModel())->share($account_id, $access_token, [$photo_id], [$target_account_id]);
        return $result['details'][0]['photo_id'];
    }

    /**
     * 已创建好的广告组列表
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        return [];
    }

    /**
     * 快手达人视频列表
     * @param $data
     * @return array
     */
    public function getNativeMaterialFileList($data)
    {
        return (new OdsKuaishouNativeUserLog())->getListByCondition(
            $data, $data['page'] ?? 1, $data['rows'] ?? 20
        );
    }

    /**
     * 已创建好的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $filters['campaign_name'] = $param->ad1_name_text;
        $filters['campaign_type'] = 2;
        $filters['page'] = 1;
        $filters['page_size'] = 1;
        $response_data = (new CampaignModel())->list($account_param->account_id, $account_param->access_token, $filters);
        if (count($response_data['details']) > 0) {
            return $response_data['details'][0]['campaign_id'] ?? 0;
        } else {
            return 0;
        }
    }

    /**
     * 已创建好的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    private function getAD1IdByParam(ADTaskParam $param, $account_param)
    {
        $filters['campaign_name'] = $param->ad1_name_text;
        $filters['campaign_type'] = $this->getCampaignType($param);
        $filters['status'] = KuaishouEnum::CAMPAIGN_STATUS_UNLIMITED;
        $filters['page'] = 1;
        $filters['page_size'] = 1;
        $response_data = (new CampaignModel())->list($param->account_id, $account_param->access_token, $filters);
        $details = $response_data['details'] ?? false;
        if (!empty($details)) {
            //已删除计划
            if ($details[0]['put_status'] == KuaishouEnum::CAMPAIGN_PUT_STATUS_DELETED) {
                throw new AppException('该账户下计划名重复,状态为已删除');
            }
            // 搜索广告计划
            if ($details[0]['ad_type'] != KuaishouEnum::SEARCH_CAMPAIGN) {
                throw new AppException('该账户下计划为信息流广告计划,非搜索广告,建议更换一级名称,勿重复');
            }

            /* @var ADSettingContentParam $setting */
            $setting = $param->setting;
            if ($setting->bid_scene == KuaishouEnum::BIG_TYPE_MAX_CONVERSION) {
                //最大转化
                $bid_type = KuaishouEnum::BIG_TYPE_MAX_CONVERSION;
                $message = '已经存在同名的广告计划，类型为常规投放，不能新建同名的MCB广告计划，请更换计划名称';
            } else {
                $bid_type = KuaishouEnum::BIG_TYPE_NORMAL;
                $message = '已经存在同名的广告计划，类型为最大转化(MCB)，不能新建同名常规投放的计划，请更换计划名称';
            }

            //兼容旧写法bug
            if ($details[0]['bid_type'] == KuaishouEnum::BID_TYPE_OCPM) {
                $details[0]['bid_type'] = KuaishouEnum::BIG_TYPE_NORMAL;
            }

            if ($details[0]['bid_type'] != $bid_type) {
                throw new AppException($message);
            }
            return $details[0]['campaign_id'] ?? 0;
        } else {
            return 0;
        }
    }

    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        return [];
    }

    /**
     * 新建转化列表
     * @param ConvertCreateParam $param
     * @return array
     */
    public function createConvert(ConvertCreateParam $param)
    {
        $convert_data = (new AppModel())->create($param);
        $convert_data['convert_id'] = $convert_data['app_id'];
        return $convert_data;
    }

    /**
     * 新建渠道包
     * @param ChannelPackageParam $param
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        $data = (new AppCenterModel())->addSubPackage($param);
        $data['app_android_channel_package_id'] = $data[0]['package_id'];
        $data['download_url'] = $data[0]['package_id']; // 用这个来判断是否已建过分包 防止重复建分包
        return $data;
    }

    /**
     * 创建广告组(广告1级)
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string 广告组id
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;
        $request_data['ad_type'] = 1;
        $request_data['advertiser_id'] = $param->account_id;
        $request_data['access_token'] = $account_param->access_token;
        $request_data['type'] = $this->getCampaignType($param);
        $request_data['campaign_name'] = $param->ad1_name_text;
        $request_data['day_budget'] = $setting->campaign_day_budget;

        $result = (new CampaignModel())->createSearchAD1(new CampaignCreateSearchParam($request_data));

        return $result['campaign_id'] ?? 0;
    }

    /**
     * 获取广告计划类型
     * @param ADTaskParam $param
     * @return int
     */
    private function getCampaignType(ADTaskParam $param)
    {
        /** @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            //`线索`
            return KuaishouEnum::TYPE_CLUES;
        } elseif (in_array(KuaishouEnum::SCENE_ID_SPLASH, $other_setting->scene_id)) {
            //开屏用`线索`
            return KuaishouEnum::TYPE_CLUES;
        } else {
            //默认`提升应用安装`
            return KuaishouEnum::TYPE_APP;
        }
    }

    /**
     * 创建广告计划(广告2级)
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /** @var ADTargetingContentParam $targeting ; */
        $targeting = $param->targeting;

        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting ; */
        $other_setting = $param->other_setting;


        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            $targeting->app_interest = [];
            $targeting->app_interest_ids = [];

            if ((int)$other_setting->site_type_mode === 1) {
                if ($param->getSiteConfig()->game_type == 'IOS') {
                    $other_setting->os = KuaishouEnum::PLATFORM_OS_IOS;
                    $other_setting->site_type = KuaishouEnum::APPOINTED_IOS;
                } else {
                    $other_setting->os = KuaishouEnum::PLATFORM_OS_ANDROID;
                    $other_setting->site_type = KuaishouEnum::APPOINTED_ANDROID;
                }
            }

            if ($other_setting->os == KuaishouEnum::PLATFORM_OS_ANDROID) {
                $targeting->platform_os = KuaishouEnum::PLATFORM_OS_ANDROID;
                $targeting->ios_osv = 0;
            }
            if ($other_setting->os == KuaishouEnum::PLATFORM_OS_IOS) {
                $targeting->platform_os = KuaishouEnum::PLATFORM_OS_IOS;
                $targeting->android_osv = 0;
            }
        } else {
            //platform_os和系统版本
            if ($param->getSiteConfig()->game_type == KuaishouEnum::GAME_TYPE_ANDROID) {
                $targeting->ios_osv = 0;
                $targeting->platform_os = $targeting->android_osv == KuaishouEnum::ANDROID_OSV_NONE ? KuaishouEnum::PLATFORM_OS_NONE : KuaishouEnum::PLATFORM_OS_ANDROID;
            } else {
                $targeting->android_osv = 0;
                $targeting->platform_os = $targeting->ios_osv == KuaishouEnum::IOS_OSV_NONE ? KuaishouEnum::PLATFORM_OS_NONE : KuaishouEnum::PLATFORM_OS_IOS;
                //ios的话定向包的APP行为无效
                $targeting->app_interest = [];
                $targeting->app_interest_ids = [];
            }
        }

        //行为兴趣不限
        if (in_array($targeting->behavior_interest_none, KuaishouEnum::NONE)) {
            $targeting->behavior_interest = [];
        }

        //非智能定向
        if (!$targeting->auto_target) {
            //行为
            if ($targeting->behavior_interest['behavior']['keyword'] ?? []) {
                $word_ids = array_column($targeting->behavior_interest['behavior']['keyword'], 'id');
                $targeting->behavior_interest['behavior']['keyword'] = (new BehaviorInterestModel())->keyList($account_param->account_id, $account_param->access_token, '', 1, $word_ids)['keyword'];
            }

            //兴趣
            if ($targeting->behavior_interest['interest']['keyword'] ?? []) {
                $word_ids = array_column($targeting->behavior_interest['interest']['keyword'], 'id');
                $targeting->behavior_interest['interest']['keyword'] = (new BehaviorInterestModel())->keyList($account_param->account_id, $account_param->access_token, '', 1, $word_ids)['keyword'];
            }
        }

        if (in_array(KuaishouEnum::SCENE_ID_UNION, $other_setting->scene_id)) {
            //联盟广告位
            $targeting->filter_converted_level = 0;
            $targeting->intelli_extend = [];
            $targeting->business_interest_type = 0;
            $targeting->business_interest = [];
            $targeting->app_interest_ids = [];

        } elseif (in_array(KuaishouEnum::SCENE_ID_SPLASH, $other_setting->scene_id)) {
            //开屏广告位
            $setting->splash_ad_switch = true;
            $setting->web_uri_type = KuaishouEnum::WEB_URI_TYPE_DOWNLOAD_PAGE;
        }

        //销售线索
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            if ((int)$other_setting->page_mode === 2) {
                $setting->web_uri_type = KuaishouEnum::WEB_URI_TYPE_DOWNLOAD_PAGE;
            }
            if ((int)$other_setting->page_mode === 4) {
                $setting->web_uri_type = KuaishouEnum::WEB_URI_TYPE_DEEP_LINK_WECHAT;
            }
        } else {
            $setting->web_uri_type = '';
        }

        // 排除人群包
        $exclude_population = [];
        if ($targeting->exclude_population_map){
            $exclude_population = array_column($targeting->exclude_population_map, 'orientation_id');
        }
        $target = new TargetSearchParam($targeting);
        $target->exclude_population = $exclude_population;

        $setting->conversion_type = 0;


        //最大转化投放
        if ($setting->bid_scene == KuaishouEnum::BIG_TYPE_MAX_CONVERSION) {
            $setting->bid_type = KuaishouEnum::BID_TYPE_MAX_CONVERSION;
        }

        //礼包
        if ($setting->ad_code_type == KuaishouEnum::AD_CODE_TYPE_GIFT) {
            $gift_data = new GiftDataParam($setting);
            if ($param->getSiteConfig()->game_type == KuaishouEnum::GAME_TYPE_IOS) {
                $gift_data->target_action_type = KuaishouEnum::TARGET_ACTION_TYPE_IOS;
            }
            $setting->gift_data = $gift_data->toArray();
        }

        //映射赋值
        $request_data = array_merge($param->toArray(), $setting->toArray(), $other_setting->toArray());
        $request_data['advertiser_id'] = $account_param->account_id;
        $request_data['access_token'] = $account_param->access_token;
        $request_data['campaign_id'] = $param->ad1_id;
        $request_data['unit_name'] = $param->ad2_name_text;
        $request_data['schedule_time'] = implode('', $setting->schedule_time);
        $request_data['day_budget'] = $setting->ad_day_budget;
//        $request_data['app_id'] = $param->convert_id;
//        $request_data['app_id'] = ********;
        $request_data['put_status'] = $setting->ad_operation == KuaishouEnum::AD_OPTION_DISABLE ? KuaishouEnum::PUT_STATUS_STOP : KuaishouEnum::PUT_STATUS_MANUAL;
        $request_data['cpa_bid'] = $setting->getCpaBid();
        $request_data['deep_conversion_bid'] = $setting->deep_conversion_bid ? ($setting->deep_conversion_bid[array_rand($setting->deep_conversion_bid)] ?? 0) : 0;
        $request_data['roi_ratio'] = $setting->getRoiRatio();
        $request_data['unit_type'] = KuaishouEnum::UNIT_TYPE_CUSTOM; //当前搜索广告仅支持自定义创意
        $request_data['url'] = (int)$other_setting->page_mode === 1 ? $other_setting->page_url : (string)($other_setting->getPageInfo($param->account_id)->id ?? '');
        $request_data['target'] = $target->toFilterArray();

        if ($param->getSiteConfig()->game_type == 'IOS' && $param->getSiteConfig()->plat_id != 7) {
            $ks_app_info = (new OdsKuaishouAppManagementLogModel())->getDataByPackageName($param->platform, $param->account_id, $param->getSiteConfig()->package);
            if ($ks_app_info) {
                $request_data['app_id'] = $ks_app_info->app_id;
                $request_data['package_id'] = $ks_app_info->package_id;
            } else {
                throw new AppException('快手新版应用中没有相关信息');
            }
        } elseif ($param->getSiteConfig()->game_pack == 2) {
            $request_data['app_id'] = $param->getSiteConfig()->appid;
            $request_data['package_id'] = $param->getSiteConfig()->app_android_channel_package_id;
        } else {
            $request_data['app_id'] = $param->convert_id;
        }


        if ($setting->ocpx_action_type != 191) {
            unset($request_data['roi_ratio']);
        }

        if (in_array($setting->ocpx_action_type, [191, 774, 937])) {
            $request_data['cpa_bid'] = 0;
        }

        // 搜索否词
        if ($targeting->quick_search_ext == KuaishouEnum::QUICK_SEARCH_EXT && $targeting->open_noword_search == KuaishouEnum::OPEN_NO_WORD && !empty($targeting->feed_search_noword_list)){
            $noword_map = [];
            // 1 = 精准匹配   2= 短语匹配
            foreach ($targeting->feed_search_noword_list as $noword_value){
                if ($noword_value['match_type'] == KuaishouEnum::NO_WORD_ACCURATE) {
                    $noword_map['exact_words'][] = $noword_value['word'];
                } else {
                    $noword_map['phrase_words'][] = $noword_value['word'];
                }
            }
            $request_data['negative_word'] = $noword_map;
        }

        $request_data['scene_id'] = [39];

        // 创建搜索快投广告组  quick_search_ext == 0 开启搜索快投
        if ($targeting->quick_search_ext == KuaishouEnum::QUICK_SEARCH_EXT_NO && $targeting->quick_search == KuaishouEnum::QUICK_SEARCH) {
            $request_data['quick_search'] = (int)$targeting->quick_search;
            $request_data['extend_search'] = (int)$targeting->extend_search;
            $request_data['target_explore'] = (int)$targeting->target_explore;
        }


        $result = (new ADUnitModel())->createSearch(new AdUnitCreateSearchParam($request_data));
        return $result['unit_id'] ?? 0;
    }

    /**
     * 创建一个广告创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting ; */
        $other_setting = $param->other_setting;

        //字段映射
        $request_data = array_merge($param->toArray(), $setting->toArray(), $other_setting->toArray());
        $request_data['advertiser_id'] = $account_param->account_id;
        $request_data['access_token'] = $account_param->access_token;
        $request_data['unit_id'] = $param->ad2_id;
        $request_data['click_track_url'] = $param->getSiteConfig()->action_track_url;
        $request_data['click_url'] = $param->getSiteConfig()->action_track_url;

        $word_list = $param->getWordList();
        foreach ($param->creative_list as $key => $creative_info) {
            $index = ($word_list[$key] ?? '') ? $key : 0;
            $creative = [];
            $creative['creative_name'] = sprintf("%s_创意_%s_%s", $param->ad2_name_text, $key, date('Y-m-d:H:i:s'));
            $creative['photo_id'] = $material_media_id_map[$creative_info['video_info']['id']]['id'];
            $creative['image_token'] = $material_media_id_map[$creative_info['cover_info']['id']]['id'];
            $creative['description'] = $creative_info['title'] ?: $word_list[$index];
            $creative['action_bar_text'] = $other_setting->action_bar_text;
            $creative['creative_material_type'] = $this->getMaterialType($creative_info);

            if ($setting->new_expose_tag) {
                $creative['new_expose_tag'] = [];
                foreach ($setting->new_expose_tag as $text) {
                    $creative['new_expose_tag'][] = ['text' => $text];
                }
            }

            $request_data['creatives'][] = new CreativeCreativesSearchParam($creative);

        }
        $result = (new CreativeModel())->createSearch(new CreativeBatchCreateSearchParam($request_data));
        return $result['add_creative_ids'] ?? [];


    }

    /**
     * 获取快手媒体素材类型
     * @param array $creative_info
     * @return int
     */
    public function getMaterialType($creative_info)
    {
        return (int)$creative_info['video_info']['width'] > (int)$creative_info['video_info']['height'] ? KuaishouEnum::CREATIVE_MATERIAL_TYPE_HORIZONTAL : KuaishouEnum::CREATIVE_MATERIAL_TYPE_VERTICAL;
    }

    /**
     * 获取广告计划可用的定向包列表
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionCategoryList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::KUAISHOU);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        // behavior_interest
        $behavior_interest_model = new BehaviorInterestModel();
        $response_data = $behavior_interest_model->labelList($account_data->account_id, $account_data->access_token);
        return $this->interestActionCategoryFormat($response_data['behavior_interest']['behavior']);
    }

    private function interestActionCategoryFormat($data, $parent_id = '')
    {
        foreach ($data as $key => &$value) {
            if ($parent_id) {
                $value['id'] = "{$parent_id}-{$value['id']}";
            }
            if (isset($value['children']) && $value['children']) {
                $value['children'] = $this->interestActionCategoryFormat($value['children'], $value['id'] ?? '');
            } else {
                unset($value['children']);
            }
            $value['value'] = ['id' => $value['id'] ?? '', 'name' => $value['name'] ?? ''];
        }
        return $data;
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionKeywordList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::KUAISHOU);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        // behavior_interest
        $behavior_interest_model = new BehaviorInterestModel();
        $response_data = $behavior_interest_model->keyList($account_data->account_id, $account_data->access_token, $input['key_word']);
        return $response_data['keyword'];
    }

    /**
     * 根据输入获取兴趣类目
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::KUAISHOU);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        // behavior_interest
        $behavior_interest_model = new BehaviorInterestModel();
        $response_data = $behavior_interest_model->labelList($account_data->account_id, $account_data->access_token);
        return $this->interestActionCategoryFormat($response_data['behavior_interest']['interest']);
    }

    /**
     * 根据输入获取兴趣关键词
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType(MediaType::KUAISHOU);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;
        // behavior_interest
        $behavior_interest_model = new BehaviorInterestModel();
        $response_data = $behavior_interest_model->keyList($account_data->account_id, $account_data->access_token, $input['key_word']);
        return $response_data['keyword'];
    }

    /**
     * 上传图片到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            if ($param->file_type == MaterialFileModel::FILE_TYPE_IMAGE) {
                $img_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
                $type = ADImageModel::TYPE_COVER;
            } elseif ($param->file_type == MaterialFileModel::FILE_TYPE_ICON) {
                $img_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
                $type = ADImageModel::TYPE_ICON;
            } else {
                $img_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
                $type = ADImageModel::TYPE_COVER;
            }
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $image_model = new ADImageModel();
            $file = "$upload_path/$img_path/$platform/$material_id/$file_name";
            if (!file_exists($file)) {
                throw new AppException("uploadImage方法,找不到素材文件{$file}");
            }
            $result = $image_model->upload(
                $advertiser_id,
                $access_token,
                $type,
                ADImageModel::UPLOAD_TYPE_FILE,
                $param->signature,
                $file);
            if ($param->file_type == MaterialFileModel::FILE_TYPE_ICON) {
                $save_icon_url = SRV_DIR . EnvConfig::UPLOAD_PATH . '/' . EnvConfig::ICON_IMG_DIR_NAME . '/' . $result['image_token'];
                if (!file_exists($save_icon_url)) {
                    copy($file, $save_icon_url);
                }
            }
            return [
                'id' => $result['image_token'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传视频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = mb_substr($param->filename, 0, 49, 'utf8');
            $video_model = new ADVideoModel();
            $file = "$upload_path/$video_path/$platform/$material_id/$param->filename";
            $result = $video_model->upload(
                $advertiser_id,
                $access_token,
                $param->scale >= 1 ? ADVideoModel::TYPE_HORIZONTAL : ADVideoModel::TYPE_VERTICAL,
                $param->signature,
                $file,
                $file_name
            );
            return [
                'id' => $result['photo_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传快手开屏视频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadSplashAdVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = mb_substr($param->filename, 0, 49, 'utf8');
            $video_model = new ADVideoModel();
            $file = "$upload_path/$video_path/$platform/$material_id/$param->filename";
            $material_type = $this->getMaterialSplashType($param->width, $param->height);
            $result = $video_model->upload(
                $advertiser_id,
                $access_token,
                $material_type,
                $param->signature,
                $file,
                $file_name
            );
            return [
                'id' => $result['photo_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }


    /**
     * 获取开屏视频素材的类型 5: 开屏视频-720 1280 6: 开屏视频-720 1440 7: 开屏视频-720 1520 8: 开屏视频-720 1560
     * @param int $width
     * @param int $height
     * @return false|int
     */
    private function getMaterialSplashType(int $width, int $height)
    {
        if ($width == 720 && $height == 1280) {
            $type = 5;
        } elseif ($width == 720 && $height == 1440) {
            $type = 6;
        } elseif ($width == 720 && $height == 1520) {
            $type = 7;
        } elseif ($width == 720 && $height == 1560) {
            $type = 8;
        } else {
            throw new AppException("快手开屏视频格式错误");
        }
        return $type ?? false;
    }

    /**
     * 获取创意行业列表
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        return [];
    }

    /**
     * id转词汇
     * @param array $input
     * @param $media_type
     * @return array
     */
    public function getWordById($input, $media_type)
    {
        return [];
    }


    /**
     * 获取人群包列表
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = "", $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        $model = new OdsKuaishouOrientationLogModel();
        return $model->getListByCompany($company, $page, $rows, $id, $name, $tag, $source, $account_id, $extra);
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        $model = new OdsKuaishouOrientationLogModel();
        return $model->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    /**
     * 推送人群包
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        $media_account_model = new MediaAccountModel();
        $audience_account_id_map = $media_account_model->getDataInAccountIds($audience_account_id_list)->keyBy('account_id');
        if ($audience_account_id_map->isEmpty()) {
            throw new AppException('找不到' . implode(',', $audience_account_id_list) . '的账号信息');
        }
        $population_model = new PopulationModel();
        foreach ($need_push_audience_list_data as $audience_key => $audience_data) {
            try {
                $population_model->accountPush(
                    $audience_data->account_id,
                    $audience_account_id_map[$audience_data->account_id]->access_token,
                    $audience_data->orientation_id,
                    $target_account_ids
                );
            } catch (Throwable $e) {
                throw new AppException("账号{$audience_data->account_id}授权人群包{$audience_data->orientation_id}错误:" . $e->getMessage());
            }
        }
        return [];
    }

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        return '';
    }

    /**
     * 补充定向包信息
     * @param array $targeting
     * @param bool $is_return
     * @return array
     */
    public function fillTargetingInfo(array $targeting, $is_return = false)
    {
        $targeting['age_none'] = 'buxian';

        if (($targeting['age']['max'] ?? '') && ($targeting['age']['min'] ?? '')) {
            $targeting['age_none'] = 'custom';
        }

        if ($targeting['ages_range'] ?? []) {
            $targeting['age_none'] = 'zidingyi';
        }


        if ($targeting['region'] ?? []) {
            $targeting['region_none'] = 'zidingyi';
            $targeting['region_map'] = [];
            foreach ($targeting['region'] as $region_id) {
                $targeting['region_map'][] = [
                    'id' => $region_id,
                    'name' => $region_id
                ];
            }
        } else {
            $targeting['region_none'] = 'buxian';
        }

        if ($targeting['business_interest'] ?? []) {
            $targeting['business_interest_map'] = [];
            foreach ($targeting['business_interest'] as $b_i) {
                $targeting['business_interest_map'][] = [
                    'id' => $b_i,
                    'name' => $b_i,
                ];
            }
        }

        $app_interest_ids_list = $targeting['app_interest_ids'] ? $targeting['app_interest_ids'] : $targeting['app_interest'];
        if ($app_interest_ids_list ?? []) {
            $targeting['app_interest_none'] = 'zidingyi';
            $targeting['app_interest_map'] = [];
            foreach ($app_interest_ids_list as $a_i) {
                $targeting['app_interest_map'][] = [
                    'id' => $a_i,
                    'name' => $a_i,
                ];
            }
        } else {
            $targeting['app_interest_none'] = 'buxian';
        }

        if ($targeting['behavior_interest']['behavior']['label'] ?? []) {
            $targeting['behavior_interest']['behavior']['label_map'] = [];
            foreach ($targeting['behavior_interest']['behavior']['label'] as $label) {
                $targeting['behavior_interest']['behavior']['label_map'][] = [
                    'id' => $label,
                    'name' => $label,
                ];
            }
        }

        if ($targeting['behavior_interest']['interest']['label'] ?? []) {
            $targeting['behavior_interest']['interest']['label_map'] = [];
            foreach ($targeting['behavior_interest']['interest']['label'] as $label) {
                $targeting['behavior_interest']['interest']['label_map'][] = [
                    'id' => $label,
                    'name' => $label,
                ];
            }
        }

        if (($targeting['behavior_interest']['interest']['label'] ?? []) ||
            ($targeting['behavior_interest']['behavior']['label'] ?? []) ||
            ($targeting['behavior_interest']['behavior']['keyword'] ?? [])
        ) {
            $targeting['behavior_interest_none'] = 'zidingyi';
        } else {
            $targeting['behavior_interest_none'] = 'buxian';
        }

        if ($targeting['device_brand'] ?? []) {
            $targeting['device_brand_none'] = 'zidingyi';
        } else {
            $targeting['device_brand_none'] = 'buxian';
        }

        if ($targeting['device_price'] ?? []) {
            $targeting['device_price_none'] = 'zidingyi';
        } else {
            $targeting['device_price_none'] = 'buxian';
        }

        $orientation_id_list = array_merge($targeting['exclude_population'] ?? [], $targeting['population'] ?? []);
        if ($orientation_id_list) {
            $targeting['zidingyirenqun'] = 'zidingyi';
            if ($targeting['seed_population'] ?? []) {
                $targeting['zidingyirenqun'] = 'seed';
            }
        } else {
            $targeting['zidingyirenqun'] = 'buxian';
        }


        $targeting['exclude_population_map'] = [];
        if ($targeting['exclude_population'] ?? []) {
            foreach ($targeting['exclude_population'] as $orientation_id) {
                $targeting['exclude_population_map'][] = [
                    'orientation_id' => $orientation_id,
                    'orientation_name' => $orientation_id
                ];
            }
        }

        $targeting['population_map'] = [];
        if ($targeting['population'] ?? []) {
            foreach ($targeting['population'] as $orientation_id) {
                $targeting['population_map'][] = [
                    'orientation_id' => $orientation_id,
                    'orientation_name' => $orientation_id
                ];
            }
        }

        $targeting['seed_population_map'] = [];
        if ($targeting['seed_population'] ?? []) {
            foreach ($targeting['seed_population'] as $orientation_id) {
                $targeting['seed_population_map'][] = [
                    'orientation_id' => $orientation_id,
                    'orientation_name' => $orientation_id
                ];
            }
        }

        $targeting_info['targeting'] = $targeting;
        if (!$is_return) {
            $param = new ADTargetingPacketParam($targeting_info);
            $param->setTargetingByArray($targeting);
            (new ADTargetingPacketModel())->editTargetingPacket($targeting_info['id'], $param);
            return [];
        } else {
            return $targeting_info;
        }
    }

    /**
     * 根据ids获取人群包内容
     * @param array $ids
     * @param int $status
     * @return Collection
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        return (new OdsKuaishouOrientationLogModel())->getListByAudienceIds($ids);
    }

    /**
     * 获取计划行为徕卡
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        return [];
    }

    /**
     * 获取计划兴趣徕卡
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        return [];
    }

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        return [];
    }

    /**
     * 获取计划徕卡兴趣id转词汇
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        return [];
    }

    /**
     * 获取流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        return [];
    }

    /**
     * 修改广告计划-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告计划的开关-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告创意的开关-返回修改的广告创意id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        if (3 === $data['ad_level']) {
            //三级
            $ad_model = new OdsKuaishouCreativeLogModel();
            $ad_his_log_model = new OdsKuaishouCreativeHisLogModel();
            $http_model = new CreativeModel();
            $ad_id_type = 'creative_id';
            $ad_name_type = 'creative_name';
            $switch_close_value = 'CREATIVE_STATUS_DISABLE';
            $switch_open_value = 'CREATIVE_STATUS_ENABLE';
            $switch_delete_value = 'CREATIVE_STATUS_DELETED';
            $history_update_field = 'put_status';
        } elseif (2 === $data['ad_level']) {
            //二级
            $ad_model = new OdsKuaishouUnitLogModel();
            $ad_his_log_model = new OdsKuaishouUnitHisLogModel();
            $http_model = new ADUnitModel();
            $ad_id_type = 'ad_id';
            $ad_name_type = 'ad_name';
            $switch_close_value = 'AD_STATUS_DISABLE';
            $switch_open_value = 'AD_STATUS_ENABLE';
            $switch_delete_value = 'AD_STATUS_DELETE';
            $history_update_field = 'ad_put_status';
        } else {
            //一级
            $ad_model = new OdsKuaishouCampaignLogModel();
            $ad_his_log_model = new OdsKuaishouCampaignHisLogModel();
            $http_model = new CampaignModel();
            $ad_id_type = 'campaign_id';
            $ad_name_type = 'campaign_name';
            $switch_close_value = 'CAMPAIGN_STATUS_DISABLE';
            $switch_open_value = 'CAMPAIGN_STATUS_ENABLE';
            $switch_delete_value = 'CAMPAIGN_STATUS_DELETE';
            $history_update_field = 'campaign_put_status';
        }

        $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        $result = $ad_model->getAccountId($ad_format_target, $column);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        if (0 === (int)$data['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_OFF;
            $put_status = 2;
            $switch_option = $switch_close_value;
            $edit_detail = '关闭';
        } elseif (1 === (int)$data['switch']) {
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_ON;
            $put_status = 1;
            $switch_option = $switch_open_value;
            $edit_detail = '开启';
        } else {
            $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;
            $put_status = 3;
            $switch_option = $switch_delete_value;
            $edit_detail = '删除';
        }

        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($result as $value) {
            $account_ids[] = $value->account_id;
        }
        unset($value);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);

        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update_switch = [];
        foreach ($result as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->$ad_id_type;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            if (isset($data['risk_operate'][MediaType::KUAISHOU][$v->$ad_id_type]) &&
                $data['risk_operate'][MediaType::TENCENT][$v->$ad_id_type]) {
                $risk_operate = '本次为风险操作';
                $risk_code = 1;
            } else {
                $risk_operate = '';
                $risk_code = 0;
            }
            $advertiser_id = (int)$v->account_id;
            $ad_id = (int)$v->$ad_id_type;
            $access_token = $access_tokens[$v->account_id];
            //请求快手修改开关状态
            try {
                $http_model->updateStatus($advertiser_id, $access_token, $ad_id, $put_status);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;
                $update_switch[$v->account_id][] = $ad_id;
                $single_record_operate['status'] = $single_return_data['status'] = $risk_code ? ADAnalysisModel::SUCCESS_WITH_RISK : ADAnalysisModel::SUCCESS;
                $single_record_operate['edit_detail'] = $edit_detail . "成功 " . $risk_operate;
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = $risk_code ? ADAnalysisModel::FAILED_WITH_RISK : ADAnalysisModel::FAILED;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "失败，" . $risk_operate . " 错误信息：" .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->$ad_name_type;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_type;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update_switch) {
            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                $history_update_field,
                $put_status,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表一级广告
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisFirstClass(array $data)
    {
        $kuaishou_model = new OdsKuaishouCampaignLogModel();
        $ad_his_log_model = new OdsKuaishouCampaignHisLogModel();
        $account_info = $kuaishou_model->getAccountIdAndPlatform($data);
        if (!$account_info) {
            throw new AppException('修改失败');
        }
        $kuaishou_http_model = new CampaignModel();
        //获取access_token并注入权限
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$account_info->account_id], null, $leader_permission);
        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 1;
        $record_operate['media_type'] = $data['media_type'];
        $record_operate['editor_id'] = $data['editor_id'];
        $record_operate['edit_type'] = 5;
        $record_operate['platform'] = $account_info->platform;
        $record_operate['editor_name'] = $data['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $record_operate['account_id'] = (int)$account_info->account_id;
        $record_operate['ad_id'] = $return_data['ad_id'] = (int)$data['ad_id'];

        if ($access_token->isNotEmpty()) {
            $ad_info = [];
            $access_token = $access_token->pop()->access_token;
            $request_data['advertiser_id'] = (int)$account_info->account_id;
            $request_data['campaign_id'] = (int)$data['ad_id'];
            $request_data['campaign_name'] = $data['ad_group_name'];

            //请求快手修改开关状态
            try {
                $kuaishou_http_model->update($request_data, $access_token);
                //修改成功
                $return_data['message'] = 'success';
                $record_operate['ad_name'] = $return_data['value'] = $data['ad_group_name'];
                $record_operate['status'] = $return_data['status'] = 1;
                $record_operate['edit_detail'] = "广告组名称由[ {$account_info->campaign_name} ] " .
                    "修改为 [ {$data['ad_group_name']} ]";
                $ad_info[$request_data['advertiser_id']] = [$request_data['campaign_id']];
            } catch (AppException $e) {
                //修改失败
                $return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $record_operate['edit_detail'] = "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
                $record_operate['status'] = $return_data['status'] = 2;
                $record_operate['ad_name'] = $account_info->campaign_name;
                $return_data['value'] = 0;
            }

            if ($ad_info) {
                $ad_his_log_model->getLatestByADInfoAndInsert(
                    $ad_info,
                    'campaign_name',
                    $data['ad_group_name'],
                    $record_operate['insert_time']
                );
//                $async_data = [];
//                $async_data['ad_info'] = $ad_info;
//                $async_data['ad_level'] = $record_operate['ad_level'];
//                $async_data['update_field'] = 'campaign_name';
//                $async_data['update_value'] = $data['ad_group_name'];
//                $async_data['modify_time'] = $record_operate['insert_time'];
//                Container::getServer()->task([
//                    'action' => 'ADAnalysisInsertHisLogTask.handleKuaishouADAnalysisInsertHisLog',
//                    'data' => $async_data,
//                ]);
            }

        } else {
            //没有权限操作
            $return_data['message'] = $record_operate['edit_detail'] = '当前用户无操作权限';
            $return_data['value'] = 0;
            $record_operate['status'] = $return_data['status'] = 2;
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [$return_data];
    }

    /**
     * 修改基本报表二级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        $ad_model = new OdsKuaishouUnitLogModel();
        $ad_his_log_model = new OdsKuaishouUnitHisLogModel();
        $column = ['ad_id', 'ad_name', 'account_id', 'platform'];
        $account_info = $ad_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];
        //避免循环查库，手动来组合广告数据
        if (isset($data['ad_name'])) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = 6;
            $update_value = $request_data['unit_name'] = $data['ad_name'];
            $edit_detail = '广告二级名';
            $update_field = 'ad_name';
        } else {
            //修改schedule_time
            $single_record_operate['edit_type'] = 4;
            $update_value = $request_data['schedule_time'] = $data['schedule_time'];
            $edit_detail = '广告投放时段';
            $update_field = 'schedule_time';
        }

        $http_model = new ADUnitModel();
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            $account_ids[] = $value->account_id;
        }
        unset($value);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->ad_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['advertiser_id'] = (int)$v->account_id;
            $request_data['unit_id'] = (int)$v->ad_id;
            $access_token = $access_tokens[$v->account_id];
            try {
                $http_model->update($request_data, $access_token);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $update_value;
                $update[$request_data['advertiser_id']][] = $request_data['unit_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "修改成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "修改失败，错误信息：" .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->ad_name;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->ad_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update) {
            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_value,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表预算和出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $update_field = 'budget' === $update_field ? 'ad_day_budget' : 'cpa_bid';
        $ad_model = new OdsKuaishouUnitLogModel();
        $column = ['ad_id', 'ad_name', 'account_id', 'platform', $update_field];
        $account_info = $ad_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            $account_ids[] = $item->account_id;
        }
        unset($item);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = 3;
        $request_type = 'day_budget';
        $opt_cn_name = '预算';
        if ('cpa_bid' === $update_field) {
            $request_type = 'cpa_bid';
            $single_record_operate['edit_type'] = 7;
            $opt_cn_name = '出价';
        }
        $http_model = new AdUnitModel();
        //存储所有需要定时修改的数据
        $timing_execute_data = [];
        //即时修改到数据库的数据
        $update_data = [];
        //用于记录his表的数据
//        $update_data_for_his = [];
        $request_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_return_data['status'] = $single_record_operate['status'] = 2;
                $single_return_data['value'] = 0;
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->ad_id;
                $single_record_operate['ad_name'] = $v->ad_name ?? '';
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            //保存原始值
            $original_value = $v->$update_field;
            if (1 === intval($data['change_type'])) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === intval($data['change_type'])) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } elseif (3 === intval($data['change_type'])) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } else {
                throw new AppException('参数错误');
            }

            $request_data['advertiser_id'] = (int)$v->account_id;
            $request_data['unit_id'] = (int)$v->ad_id;
            $request_data[$request_type] = $v->$update_field * 1000;
            $access_token = $access_tokens[$v->account_id];
            if (1 === (int)$data['execute_type']) {
                //即时修改，直接请求快手修改
                try {
                    $http_model->update($request_data, $access_token);
                    //修改成功
                    $update_data[$request_data['advertiser_id']][] = $v->ad_id;
//                    $update_data_for_his[$request_data['unit_id']] = (float)$v->$update_field;
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = (string)$v->$update_field;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name . '修改成功，由[' . $original_value .
                        ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    //操作失败
                    $single_return_data['message'] = $e->getMessage();
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    $single_record_operate['edit_detail'] = $opt_cn_name . "修改失败，错误信息：" . $failed_info;
                }
            } elseif (0 === intval($data['execute_type'])) {
                //定时修改，将请求数据汇总之后放入RabbitMQ
                $single_return_data['status'] = $single_record_operate['status'] = 1;
                $single_return_data['value'] = $original_value;
                $single_return_data['message'] = $single_record_operate['edit_detail'] =
                    $opt_cn_name . '修改准备' . $data['execute_time'] . '定时执行，由[' . $original_value .
                    ']修改为[' . (string)$v->$update_field . ']';

                //$request_data存入session信息,access_token消费队列时重新查
                $request_data['editor_id'] = $single_record_operate['editor_id'];
                $request_data['editor_name'] = $single_record_operate['editor_name'];
                $request_data['media_type'] = $media_type;
                $request_data['request_type'] = $request_type;
                $timing_execute_data[] = $request_data;
            }
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->ad_id;
            $single_record_operate['ad_name'] = $v->ad_name;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update_data) {
            //组合要update的sql
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, 2 ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } else {
                throw new AppException('参数错误');
            }
            (new OdsKuaishouUnitHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        if ($timing_execute_data) {
            $time = strtotime($data['execute_time']) - time();
            if ('cpa_bid' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBidTask($timing_execute_data, $time);
            } elseif ('day_budget' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBudgetTask($timing_execute_data, $time);
            }
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 处理基本报表定时修改出价预算RabbitMQ
     * @param int $media_type
     * @param array $data
     * @return mixed|void
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        $ad_model = new OdsKuaishouUnitLogModel();
        //二级表查询条件
        $condition = [];
        foreach ($data as $v) {
            $condition[$v['advertiser_id']][] = $v['unit_id'];
        }
        unset($v);
        if (!$condition) {
            return;
        }
        $account_ids = array_keys($condition);
        $ad_info = $ad_model->getADInfoByAccountIdAndADId($condition);
        $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids);
        if ($ad_info->isEmpty() || $access_token_info->isEmpty()) {
            return;
        }
        //整合access_token方便后面使用
        $access_tokens = [];
        foreach ($access_token_info as $v) {
            $access_tokens[$v->account_id] = $v->access_token;
        }
        unset($v);

        $http_model = new AdUnitModel();
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];

        $opt_cn_name = ['cpa_bid' => '出价', 'day_budget' => '预算'];
        foreach ($ad_info as $value) {
            foreach ($data as $v) {
                if ((int)$value->ad_id !== (int)$v['unit_id'] ||
                    (int)$value->account_id !== (int)$v['advertiser_id']) {
                    continue;
                }
                $request_type = $v['request_type'];
                if ($request_type === 'cpa_bid') {
                    $single_record_operate['edit_type'] = 7;
                    $table_field = 'cpa_bid';
                } else {
                    $single_record_operate['edit_type'] = 3;
                    $table_field = 'ad_day_budget';
                }
                $request_data = [];
                $request_data['advertiser_id'] = (int)$v['advertiser_id'];
                $request_data['unit_id'] = (int)$v['unit_id'];
                $request_data[$request_type] = $v[$request_type];
                $access_token = $access_tokens[$v['advertiser_id']];

                try {
                    $http_model->update($request_data, $access_token);
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = Math::div($v[$request_type], 1000, 2);
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$request_type] . '定时修改成功，由[' .
                        $value->$table_field . ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    //修改失败
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //操作失败
                    $single_return_data['message'] = $e->getMessage();
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$request_type] . "定时修改失败，错误信息：" .
                        $failed_info;
                }
                $single_record_operate['platform'] = $value->platform;
                $single_record_operate['account_id'] = (int)$value->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$value->ad_id;
                $single_record_operate['ad_name'] = $value->ad_name;
                $single_record_operate['editor_id'] = $v['editor_id'];
                $single_record_operate['editor_name'] = $v['editor_name'];
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($v);
        }
        unset($value);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
    }

    /**
     * 修改基本报表定向
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        $access_token = [];
        $platform_for_targeting = '';
        $account_ids_for_targeting = [];
        foreach ($account_info as $item) {
            $access_token[$item->account_id] = $item->access_token;
            if (!$platform_for_targeting) {
                $platform_for_targeting = $item->platform;
            }
            $account_ids_for_targeting[] = $item->account_id;
        }
        unset($item);

        $request_data = [];
        if ($platform_for_targeting) {
            //推送人群包并获取定向包内容
            $request_data['target'] = (new ADServingLogic())->pushAudienceForAnalysis(
                (int)$data['targeting_id'],
                (string)$platform_for_targeting,
                $account_ids_for_targeting
            )['targeting'];
        }
        $kuaishou_http_model = new ADUnitModel();
        //单条操作记录
        $single_record_operate = [];

        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价,
        // 9: 修改定向
        $single_record_operate['edit_type'] = 9;
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = MediaType::KUAISHOU;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        $single_return_data['value'] = 0;
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($data['ad_info'] as $v) {
            $single_record_operate['ad_name'] = $v['ad_name'];
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_record_operate['platform'] = $v['platform'];
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v['ad_id'];

            if (!isset($access_token[$v['account_id']])) {
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['advertiser_id'] = (int)$v['account_id'];
            $request_data['unit_id'] = (int)$v['ad_id'];
            try {
                $kuaishou_http_model->update($request_data, $access_token[$v['account_id']]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = "定向修改成功";
            } catch (AppException $e) {
                //修改失败
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '定向修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
            }

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    public function getTargetingDataByAD2($company, $audience_md5)
    {
        throw new AppException('快手暂时不支持，复制计划定向');
    }

    /**
     * @param $create_data
     * @return ADTaskParam
     */
    private function makeADTaskData($create_data)
    {
        if ($create_data['account_id']) {
            $account_info = (new MediaAccountModel())->getDataByAccountId($create_data['account_id'], MediaType::KUAISHOU);
            if ($account_info) {
                $create_data['company'] = $account_info->company;
                $create_data['account_name'] = $account_info->account_name;
            } else {
                throw new AppException('找不到账号信息');
            }
        } else {
            throw new AppException('account_id为空');
        }

        if (!($create_data['creative_list'] ?? '')) {
            throw new AppException('创意列表不能为空');
        }

        $create_list = new ADComposeCreativeListParam();
        foreach ($create_data['creative_list'] as $create_info) {
            if (isset($create_info['cover_info'])) {
                $create_list->addVideoToList(
                    $create_info['title'] ?? '',
                    new ADComposeCreativeVideoCoverParam([
                        'id' => $create_info['cover_info']['id'],
                        'url' => $create_info['cover_info']['url'],
                        'width' => $create_info['cover_info']['width'],
                        'height' => $create_info['cover_info']['height'],
                    ]),
                    new ADComposeCreativeVideoParam([
                        'id' => $create_info['video_info']['id'],
                        'url' => $create_info['video_info']['url'],
                        'width' => $create_info['video_info']['width'],
                        'height' => $create_info['video_info']['height'],
                    ])
                );
            } else {
                $create_list->addImageToList(
                    $create_info['title'] ?? '',
                    new ADComposeCreativeImageParam([
                        'id' => $create_info['image_info']['id'],
                        'url' => $create_info['image_info']['url'],
                        'width' => $create_info['image_info']['width'],
                        'height' => $create_info['image_info']['height'],
                    ])
                );
            }
        }

        $targeting_data = [
            'auto_target' => $create_data['auto_target'],
            'region' => $create_data['region'],
            'age' => [
                'min' => $create_data['age_min'] ?? '',
                'max' => $create_data['age_max'] ?? '',
            ],
            'ages_range' => $create_data['ages_range'] ?? [],
            'gender' => $create_data['gender'],
            'android_osv' => $create_data['android_osv'] ?: '3',
            'ios_osv' => $create_data['ios_osv'] ?: '6',
            'network' => $create_data['network'],
            'device_brand' => $create_data['device_brand'],
            'device_price' => $create_data['device_price'],
            'business_interest_type' => $create_data['business_interest_type'] ?: '0',
            'business_interest' => $create_data['business_interest'],
            'population' => $create_data['population'],
            'exclude_population' => $create_data['exclude_population'],
            'seed_population' => $create_data['seed_population'],
            'app_interest' => $create_data['app_interest'] ?? [],
            'app_interest_ids' => $create_data['app_interest_ids'] ?? [],
            'intelli_extend' => [
                'is_open' => $create_data['intelli_extend_is_open'],
                'no_age_break' => $create_data['intelli_extend_no_age_break'],
                'no_gender_break' => $create_data['intelli_extend_no_gender_break'],
                'no_area_break' => $create_data['intelli_extend_no_area_break'],
            ],
            'filter_converted_level' => $create_data['filter_converted_level'],
            'behavior_interest' => [
                'behavior' => [
                    'keyword' => $create_data['behavior_keyword'],
                    'label' => $create_data['behavior_label'],
                    'time_type' => $create_data['behavior_time_type'],
                    'strength_type' => $create_data['behavior_strength_type'],
                    'scene_type' => $create_data['behavior_scene_type'],
                ],
                'interest' => [
                    'label' => $create_data['interest_label'],
                    'strength_type' => $create_data['interest_strength_type'] ?? 0,
                ]
            ]
        ];

        $targeting_data = ($this->fillTargetingInfo($targeting_data, true))['targeting'];
        $targeting_pa = new ADTargetingContentParam($targeting_data);
        $targeting_pa->format(ADTargetingPacketParam::FINISH_STATE);

        $setting_data = [
            'campaign_day_budget_mode' => $create_data['campaign_day_budget'] > 0 ? 'zidingyi' : 'buxian',
            'campaign_day_budget' => $create_data['campaign_day_budget'],
            'bid_type' => $create_data['bid_type'],
            'ocpx_action_type' => $create_data['ocpx_action_type'],
            'cpa_bid' => $create_data['cpa_bid'],
            'deep_conversion_type' => $create_data['deep_conversion_type'],
            'deep_conversion_bid' => $create_data['deep_conversion_bid'],
            'roi_ratio' => $create_data['roi_ratio'],
            'agent_type' => ($create_data['scene_id_mode'] ?? 'custom') == 'union' ? 'union' : 'default',
            'scene_id_mode' => in_array(1, $create_data['scene_id']) ? 'default' : $create_data['scene_id_mode'],
            'scene_id' => $create_data['scene_id_mode'] == 'union' ? [5] : $create_data['scene_id'],
            'unit_type' => $create_data['unit_type'],
            'schedule_type' => $create_data['schedule_type'] ?? 'buxian',
            'begin_time' => $create_data['begin_time'] ? $create_data['begin_time'] * 1000 : time() * 1000,
            'end_time' => $create_data['end_time'] ? $create_data['end_time'] * 1000 : strtotime("+12 month", time()) * 1000,
            'schedule_time' => $create_data['schedule_time'],
            'ad_day_budget_mode' => $create_data['ad_day_budget'] > 0 ? 'zidingyi' : 'buxian',
            'ad_day_budget' => $create_data['ad_day_budget'],
            'speed' => $create_data['speed'],
            'show_mode' => $create_data['show_mode'],
            'action_bar_text' => $create_data['action_bar_text'],
            'site_config_img_url' => $create_data['site_config_img_url'],
            'ad_code_type' => $create_data['ad_code_type'],
            'target_action_type' => $create_data['target_action_type'],
            'code' => $create_data['code'],
            'creative_category' => $create_data['creative_category'],
            'creative_tag' => $create_data['creative_tag'],
            'new_expose_tag' => $create_data['new_expose_tag'],
            'ad_operation' => 'enable',
            //智能托管参数
            'use_app_market' => $create_data['use_app_market'] ?? false,
            'app_store' => $create_data['app_store'] ?? '',
            'smart_cover' => $create_data['smart_cover'] ?? false,
            'asset_mining' => $create_data['asset_mining'] ?? '',
            'rule_enable' => $create_data['rule_enable'] ?? '',
            'campaign_name_rule' => $create_data['campaign_name_rule'] ?? '',
            'unit_name_rule' => $create_data['unit_name_rule'] ?? '',
            'creative_name_rule' => $create_data['creative_name_rule'] ?? '',
            'bid_scene' => $create_data['bid_scene'] ?? '0',
        ];

        $setting_pa = new ADSettingContentParam($setting_data);
        $setting_pa->format();

        $other_setting = new ADOtherSettingContentParam($create_data);

        $site_config = new SiteConfigParam($create_data);
        if ($other_setting->campaign_type == 5) {
            $site_config->convert_source_type = ConvertSourceType::H5_API;
        }

        return (new ADTaskParam([
            'compose_id' => 0,
            'company' => $create_data['company'],
            'origin_type' => 3,
            'platform' => $create_data['platform'],
            'media_type' => $create_data['media_type'],
            'creative_mode' => count($create_data['word_list']) > 0 ? BatchAD::CREATIVE_PROGRAM_MODE : BatchAD::CREATIVE_CUSTOM_MODE,
            'media_agent_type' => 0,
            'creator' => $create_data['creator'],
            'creator_id' => $create_data['creator_id'],
        ]))->initByCompose(
            $account_info->account_id,
            $account_info->account_name,
            $create_list,
            $create_data['word_list'] ?? [],
            '智能投放定向',
            $targeting_pa,
            '智能投放参数',
            $setting_pa,
            $site_config->toArray(),
            new ADComposeConfigParam([
                'ad1_name' => $create_data['campaign_name'],
                'ad2_name' => $create_data['ad_name']
            ]),
            $other_setting,
            $create_data['calc_rule_list'] ?? [],
            []
        );
    }

    /**
     * 通过API添加广告任务
     * @param array $create_data
     * @return int|string
     */
    public function addADTaskByApi(array $create_data)
    {
        $create_data['auto_download'] = $create_data['auto_download'] ?? 0;
        $create_data['forbid_tuitan'] = $create_data['forbid_tuitan'] ?? 0;
        $create_data['state'] = $create_data['state'] ?? 1;
        $create_data['template_address'] = $create_data['template_address'] ?? '';
        $task_param = $this->makeADTaskData($create_data);
        $task_create_param = new ADTaskCreateParam();
        $task_create_param->addADTaskParam($task_param);
        $task_create_param->setADTaskQueueIndex();
        $task_param->queue_index = $task_create_param->getAccountQueueIndexMap()[$task_param->account_id] ?? 1;
        $task_id = (new ADTaskModel())->addTask($task_param);

        if ($task_id) {
            $task_param->id = $task_id;
            (new ADTaskMasterMQLogic())->produceTask($task_param->toMQData());
            return $task_param->id;
        } else {
            throw  new AppException('新建广告任务失败!');
        }
    }

    public function updateADTaskByApi(array $input)
    {
        throw new AppException('更新失败');
    }

    /**
     * @param array $update_data
     * @return mixed|void
     */
    public function updateAndroidChannelPackage(array $update_data)
    {

    }

    /**
     * 基本报表-批量修改深度优化出价或ROI
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $ad_model = new OdsKuaishouUnitLogModel();
        $ad_his_log_model = new OdsKuaishouUnitHisLogModel();
        $column = ['ad_id', 'ad_name', 'account_id', 'platform', 'cpa_bid', 'deep_conversion_bid', 'roi_ratio'];
        $account_info = $ad_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //向媒体发起请求的数据
        $request_data = [];

        if ('deep_bid' === $update_field) {
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_DEEP_BID;
            $update_field = 'deep_conversion_bid';
            $edit_detail = '深度转化目标出价';
            $decimal = 2;
            $integer = 12;
        } else {
            //修改ROI
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_ROI;
            $update_field = 'roi_ratio';
            $edit_detail = '付费ROI系数';
            $decimal = 3;
            $integer = 6;
        }

        $http_model = new ADUnitModel();
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            $account_ids[] = $value->account_id;
        }
        unset($value);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->ad_id;
                $single_record_operate['ad_name'] = $v->adgroup_name;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            $request_data['advertiser_id'] = (int)$v->account_id;
            $request_data['unit_id'] = (int)$v->ad_id;
            $access_token = $access_tokens[$v->account_id];
            $original_value = $v->$update_field;

            if (1 === intval($data['change_type'])) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === intval($data['change_type'])) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    $decimal, '.', '');
            } elseif (3 === intval($data['change_type'])) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    $decimal, '.', '');
            } else {
                throw new AppException('参数错误');
            }
            if ('deep_conversion_bid' === $update_field) {
                if ((int)$v->$update_field === 0 || $v->$update_field <= $v->cpa_bid) {
                    throw new AppException('深度转化目标出价不能为0或小于cpa_bid');
                }
                $request_data[$update_field] = $v->$update_field * 1000;
            }
            if ('roi_ratio' === $update_field) {
                if ($v->$update_field <= 0 || $v->$update_field > 100) {
                    throw new AppException('深度转化ROI系数允许范围为：(0-100]');
                }
                $request_data[$update_field] = $v->$update_field;
            }

            try {
                $http_model->update($request_data, $access_token);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->$update_field;
                $update[$request_data['advertiser_id']][] = $request_data['unit_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "由[" . $original_value .
                    ']修改为[' . $v->$update_field . ']' . "，修改成功";
            } catch (AppException $e) {
                //操作失败
                $single_return_data['message'] = $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $edit_detail . "由[" . $original_value . ']修改为[' .
                    $v->$update_field . ']' . "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }
            $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->ad_name;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->ad_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        if ($update) {
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( " . $integer . ", " . $decimal . " ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( " . $integer . ", " . $decimal . " ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( " . $integer . ", " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            $ad_his_log_model->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 删除基本报表二三级广告
     * @param int $media_type
     * @param array $data
     * @param array $format_target
     * @return mixed|void
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $format_target)
    {
        if (1 === (int)$data['ad_level']) {
            throw new AppException('一级广告禁止删除');
        }
        return $this->updateADAnalysisStatus($media_type, $data, $format_target);
    }

    /**
     * 基本报表-批量修改一级预算
     * @param int $media_type
     * @param array $input
     * @param array $kuaishou_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $input, array $kuaishou_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $kuaishou_model = new OdsKuaishouCampaignLogModel();
        $column = ['campaign_id', 'account_id', 'platform', 'campaign_name', 'campaign_day_budget'];
        $account_info = $kuaishou_model->getAccountId($kuaishou_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $kuaishou_http_model = new CampaignModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $opt_detail = '快手一级广告预算';
        $decimal = 2;

        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->campaign_id;
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['campaign_id'] = (int)$v->campaign_id;
            $request_data['advertiser_id'] = (int)$v->account_id;
            try {
                $original_value = $v->campaign_day_budget;
                if (1 === intval($input['change_type'])) {
                    $v->campaign_day_budget = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->campaign_day_budget += number_format($v->campaign_day_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->campaign_day_budget -= number_format($v->campaign_day_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }
                $request_data['day_budget'] = $v->campaign_day_budget * 1000;
                $request_data['access_token'] = $access_tokens[$v->account_id];
                //发起请求
                $kuaishou_http_model->update($request_data, $access_tokens[$v->account_id]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->campaign_day_budget;
                $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->campaign_name;
                $update_data[$v->account_id][] = (int)$v->campaign_id;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = (int)$v->campaign_day_budget ?: '无限';
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->campaign_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (1 === (int)$input['change_type']) {
                $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( campaign_day_budget + campaign_day_budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( campaign_day_budget - campaign_day_budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsKuaishouCampaignHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                'campaign_day_budget',
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表更新账号预算
     * @param int $media_type
     * @param array $input
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $input, array $ad_format_target, string $update_field)
    {
        $kuaishou_model = new OdsKuaishouAccountLogModel();
        $column = ['account_id', 'day_budget', 'platform'];
        $account_info = $kuaishou_model->getAccountInfoInAccountIds($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $http_model = new AdvertiserModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $decimal = 2;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['ad_id'] = 0;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $request_data['advertiser_id'] = (int)$v->account_id;
            try {
                $original_value = $v->day_budget;
                if (1 === intval($input['change_type'])) {
                    $v->day_budget = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->day_budget += number_format($v->day_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->day_budget -= number_format($v->day_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }
                $request_data['day_budget'] = $v->day_budget * 1000;

                //发起请求
                $http_model->update($request_data, $access_tokens[$v->account_id]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->day_budget;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = 0 === (int)$v->day_budget ? '无限' : $v->day_budget;
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = "快手账号预算由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";

            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '快手账号预算修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = '';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = 0;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    public function bindADAnalysisADRTA(array $input)
    {
    }

    public function unbindADAnalysisADRTA(array $data, array $input)
    {
    }

    function getTargetAudienceEstimate(array $target, $account_info)
    {
    }

    /**
     * 校验素材是否存在 不存在则过滤
     * @param array $upload_info_list
     * @param MediaAccountInfoParam $account_param
     * @return array
     */
    public function verifyAndResetMaterial(array $upload_info_list, MediaAccountInfoParam $account_param)
    {
        $video_upload_info_list = array_filter($upload_info_list, function ($value) {
            return $value->file_type == 2;
        });
        if (!$video_upload_info_list) {
            return $upload_info_list;
        }
        $verify_result = (new V1ADVideoModel())->verify(
            $account_param->account_id, $account_param->access_token, array_values(array_unique(array_column($video_upload_info_list, 'media_material_file_id'))));
        $verify_result_ids = array_column($verify_result, 'new_status', 'photo_id');
        foreach ($video_upload_info_list as $key => $info) {
            if (!isset($verify_result_ids[$info->media_material_file_id]) || $verify_result_ids[$info->media_material_file_id] != 1) {
                unset($upload_info_list[$key]);
            }
        }
        return $upload_info_list;
    }

    /**
     * 创建广告位前置
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting ; */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting ; */
        $other_setting = $param->other_setting;



        if ($setting->site_config_img_url) {
            if (
                strpos($setting->site_config_img_url, parse_url(EnvConfig::DOMAIN)['host']) === false &&
                strpos($setting->site_config_img_url, 'dms.zeda.cn') === false
            ) {
                $param->setSiteConfigProp('image_token', $setting->site_config_img_url);
            } else {
                $name = basename($setting->site_config_img_url);
                $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
                $data = (new MaterialFileModel())->getDataByName($name);
                if ($data) {
                    $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
                        [$data->id],
                        $param->platform,
                        $param->media_type,
                        $param->account_id
                    );

                    $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
                    if ($upload_info_list) {
                        $param->setSiteConfigProp('image_token', $upload_info_list[$data->id]->media_material_file_id);
                    } else {
                        $data = (array)$data;

                        $insert = [];
                        $insert['platform'] = $param->platform;
                        $insert['media_type'] = $param->media_type;
                        $insert['account_id'] = $param->account_id;
                        $insert['material_id'] = $data['material_id'];
                        $insert['file_type'] = $data['file_type'];
                        $insert['file_id'] = $data['id'];
                        $insert['filename'] = $data['filename'];
                        $insert['uploader'] = $param->creator;
                        $insert['signature'] = $data['signature'];
                        $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                        $media_file_info = $this->uploadImage(
                            new MaterialFileParam($data),
                            $account_param->account_id,
                            $account_param->access_token
                        );
                        if (!$media_file_info['id']) {
                            throw new AppException('上传icon失败');
                        }
                        $media_file_id = $media_file_info['id'];
                        $media_file_url = $media_file_info['url'];
                        $insert['media_material_file_id'] = $media_file_id;
                        $insert['url'] = $media_file_url;
                        $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                        $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                        $param->setSiteConfigProp('image_token', $media_file_id);
                    }
                } else {
                    throw new AppException('找不到应用图标信息');
                }
            }
        }
    }

    /**
     * 获取渠道组
     * @param ADTaskParam $param
     * @return int
     */
    public function getAgentGroup(ADTaskParam $param)
    {
        return AgentGroup::KUAISHOU_SEARCH;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if ((int)$other_setting->campaign_type === 5) {
            return AgentGroup::KUAISHOU_MINI_GAME;
        } else {
            // $other_setting->campaign_type === 2

            if ($other_setting->agent_type == 'default') {
                return AgentGroup::KUAISHOU;
            } else {
                return AgentGroup::KUAISHOU_UNION;
            }
        }
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
    }

    /**
     * 创建三级广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByParam($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告二级
        if (!$param->ad2_id) {
            try {
                /** @var ADTargetingContentParam $targeting ; */
                $targeting = $param->targeting;
                $param->ad2_id = $this->createAD2($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
                // 创建关键词
                if (!empty($targeting->search_keyword_type) && !empty($targeting->feed_search_keyword_list)) {
                    (new WordInfoModel())->createWordInfos(
                        $account_param->account_id,
                        $account_param->access_token,
                        $param->ad1_id,
                        $param->ad2_id,
                        $targeting->feed_search_keyword_list
                    );
                }
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告三级
        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

    }

    /**
     * 暂停广告组
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function updateAD2Status(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;

        if ($param->ad2_id && $setting->ad_operation == KuaishouEnum::AD_OPTION_DISABLE) {
            (new ADUnitModel())->updateStatus($account_param->account_id, $account_param->access_token, $param->ad2_id, KuaishouEnum::PUT_STATUS_STOP);
        }
    }


    /**
     * 创建广告组、创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     */
    private function createUnitAndCreative(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $request_param = new CreateUnitAndCreativeParam();
        $request_param->setCampaignID($param->ad1_id);
        $request_param->setUnitInfo($this->makeAD2Param($param, $account_param));

        if ($param->creative_mode == ADServingComposeAlgorithmLogic::CREATIVE_PROGRAM_MODE) {
            // 程序化
            $request_param->setAdvancedCreativeInfo($this->makeAD3ProgramParam($param, $account_param, $material_media_id_map));
        } else {
            // 自定义
            $request_param->setCustomizedCreativeInfo($this->makeAD3CustomParam($param, $account_param, $material_media_id_map));
        }

        $request_result = (new AdModel())->createUnitAndCreative($request_param, $account_param->account_id, $account_param->access_token);

        $param->ad2_id = $request_result['unit_id'];
        $param->ad3_ids = $request_result['creative_ids'] ?: [];

        if ($request_result['package_id']) {
            $param->ad3_ids = [$request_result['package_id']];
        }
    }

    /**
     * 创建广告计划、广告组、创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     */
    private function createCascadeAll(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $request_param = new CreateCascadeAllParam();
        $request_param->setCampaignInfo($this->makeAD1Param($param, $account_param));
        $request_param->setUnitInfo($this->makeAD2Param($param, $account_param));

        if ($param->creative_mode == ADServingComposeAlgorithmLogic::CREATIVE_PROGRAM_MODE) {
            // 程序化
            $request_param->setAdvancedCreativeInfo($this->makeAD3ProgramParam($param, $account_param, $material_media_id_map));
        } else {
            // 自定义
            $request_param->setCustomizedCreativeInfo($this->makeAD3CustomParam($param, $account_param, $material_media_id_map));
        }

        $request_result = (new AdModel())->createCascadeAll($request_param, $account_param->account_id, $account_param->access_token);

        $param->ad1_id = $request_result['campaign_id'];
        $param->ad2_id = $request_result['unit_id'];
        $param->ad3_ids = $request_result['creative_ids'] ?: [];

        if ($request_result['package_id']) {
            $param->ad3_ids = [$request_result['package_id']];
        }
    }

    /**
     * 新建广告计划参数
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return CampaignParam
     */
    private function makeAD1Param(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;

        $campaign_param = [];
        $campaign_param['type'] = $this->getCampaignType($param);
        $campaign_param['name'] = $param->ad1_name_text;
        $campaign_param['day_budget'] = $setting->campaign_day_budget * 1000;
        $campaign_param['bid_type'] = 0;

        //最大转化投放
        if ($setting->bid_scene == KuaishouEnum::BIG_TYPE_MAX_CONVERSION) {
            $campaign_param['bid_type'] = KuaishouEnum::BIG_TYPE_MAX_CONVERSION;
        }

        return new CampaignParam($campaign_param);
    }

    /**
     * 新建广告组参数
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return UnitParam
     */
    private function makeAD2Param(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /** @var ADTargetingContentParam $targeting ; */
        $targeting = $param->targeting;
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting ; */
        $other_setting = $param->other_setting;

        //platform_os和系统版本
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            $targeting->app_interest = [];
            $targeting->app_interest_ids = [];
            if ($other_setting->os == KuaishouEnum::PLATFORM_OS_ANDROID) {
                $targeting->platform_os = KuaishouEnum::PLATFORM_OS_ANDROID;
                $targeting->ios_osv = 0;
            }
            if ($other_setting->os == KuaishouEnum::PLATFORM_OS_IOS) {
                $targeting->platform_os = KuaishouEnum::PLATFORM_OS_IOS;
                $targeting->android_osv = 0;
            }
        } else {
            //platform_os和系统版本
            if ($param->getSiteConfig()->game_type == KuaishouEnum::GAME_TYPE_ANDROID) {
                $targeting->ios_osv = 0;
                $targeting->platform_os = KuaishouEnum::PLATFORM_OS_ANDROID;
            } else {
                $targeting->android_osv = 0;
                $targeting->platform_os = KuaishouEnum::PLATFORM_OS_IOS;
                //ios的话定向包的APP行为无效
                $targeting->app_interest = [];
                $targeting->app_interest_ids = [];
            }
        }

        //行为兴趣不限
        if (in_array($targeting->behavior_interest_none, KuaishouEnum::NONE)) {
            $targeting->behavior_interest = [];
        }

        //非智能定向
        if (!$targeting->auto_target) {
            //行为
            if ($targeting->behavior_interest['behavior']['keyword'] ?? []) {
                $word_ids = array_column($targeting->behavior_interest['behavior']['keyword'], 'id');
                $targeting->behavior_interest['behavior']['keyword'] = (new BehaviorInterestModel())->keyList($account_param->account_id, $account_param->access_token, '', 1, $word_ids)['keyword'];
            }

            //兴趣
            if ($targeting->behavior_interest['interest']['keyword'] ?? []) {
                $word_ids = array_column($targeting->behavior_interest['interest']['keyword'], 'id');
                $targeting->behavior_interest['interest']['keyword'] = (new BehaviorInterestModel())->keyList($account_param->account_id, $account_param->access_token, '', 1, $word_ids)['keyword'];
            }
        }

        if (in_array(KuaishouEnum::SCENE_ID_UNION, $other_setting->scene_id)) {
            //联盟广告位
            $targeting->filter_converted_level = 0;
            $targeting->intelli_extend = [];
            $targeting->business_interest_type = 0;
            $targeting->business_interest = [];
            $targeting->app_interest_ids = [];

        } elseif (in_array(KuaishouEnum::SCENE_ID_SPLASH, $other_setting->scene_id)) {
            //开屏广告位
            $setting->splash_ad_switch = true;
            $setting->web_uri_type = KuaishouEnum::WEB_URI_TYPE_DOWNLOAD_PAGE;
        }

        //销售线索
        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            if ((int)$other_setting->page_mode === 2) {
                $setting->web_uri_type = KuaishouEnum::WEB_URI_TYPE_DOWNLOAD_PAGE;
            }
            if ((int)$other_setting->page_mode === 4) {
                $setting->web_uri_type = KuaishouEnum::WEB_URI_TYPE_DEEP_LINK_WECHAT;
            }
        }

        $target = new \App\Param\Kuaishou\AD\TargetParam(array_merge($targeting->toArray(), [
            'behavior_type' => $targeting->intention_target === 'true' ? 2 : ($targeting->behavior_interest_none == 'buxian' ? 0 : 1),
        ]));


        $setting->conversion_type = 0;


        //最大转化投放
        if ($setting->bid_scene == KuaishouEnum::BIG_TYPE_MAX_CONVERSION) {
            $setting->bid_type = KuaishouEnum::BID_TYPE_MAX_CONVERSION;
        }

        $unit_param = array_merge($setting->toArray(), $other_setting->toArray());
        $unit_param['campaign_id'] = $param->ad1_id;
        $unit_param['name'] = $param->ad2_name_text;
        $unit_param['scene_ids'] = $other_setting->scene_id;
        $unit_param['schedule'] = $this->makeWeekTime($setting->schedule_time);
        $unit_param['day_budget'] = $setting->ad_day_budget ? $setting->ad_day_budget * 1000 : 0;
        $unit_param['app_id'] = $param->convert_id;
        $unit_param['convert_id'] = 0;
        $unit_param['put_status'] = $setting->ad_operation == KuaishouEnum::AD_OPTION_DISABLE ? KuaishouEnum::PUT_STATUS_STOP : KuaishouEnum::PUT_STATUS_MANUAL;
        $unit_param['cpa_bid'] = intval(($setting->getCpaBid() ?: 0) * 1000);
        $unit_param['deep_conversion_bid'] = $setting->deep_conversion_bid ? ($setting->deep_conversion_bid[array_rand($setting->deep_conversion_bid)] ?? '') : '';
        $unit_param['roi_ratio'] = $setting->getRoiRatio();

        if ($setting->deep_conversion_type != 3) {
            unset($unit_param['deep_conversion_bid']);
        }

        if ($setting->deep_conversion_type == 3) {
            unset($unit_param['roi_ratio']);
        }

        if ($other_setting->campaign_type == KuaishouEnum::TYPE_CLUES) {
            if ($other_setting->page_mode == 1) {
                $unit_param['web_uri'] = $other_setting->page_url ?? '';
            }
            if ($other_setting->page_mode == 2) {
                $unit_param['site_id'] = $other_setting->getPageInfo($param->account_id)->id ?? '';
            }
            if ($other_setting->page_mode == 4) {
                $unit_param['schema_id'] = $param->getSiteConfig()->sdk_ext['mini_game_original_id'] ?? '';
                $unit_param['schema_uri'] = $param->getSiteConfig()->ext['mini_game_tracking_parameter'];
                $unit_param['web_uri'] = $other_setting->page_url ?? '';
            }
        }

        if (in_array($setting->ocpx_action_type, [191, 774, 937])) {
            $unit_param['cpa_bid'] = 0;
        }

        $unit_param = new UnitParam($unit_param);
        $unit_param->setTargeting($target, $targeting->auto_target ? true : false);

        return new UnitParam($unit_param);
    }

    /**
     * 新建自定义创意参数
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return CreativeCustomListParam
     */
    private function makeAD3CustomParam(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /** @var ADOtherSettingContentParam $other_setting * */
        $other_setting = $param->other_setting;
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;

        $creative_list_param = [];
        $creative_list_param['creative_category'] = $setting->creative_category ?: '';
        $creative_list_param['creative_tag'] = $setting->creative_tag ? implode(',', $setting->creative_tag) : [];
        $creative_list_param['show_mode'] = $setting->show_mode;


        $creative_list_param['click_url'] = $param->getSiteConfig()->action_track_url;


        $creative_list = new CreativeCustomListParam($creative_list_param);

        foreach ($param->creative_list as $key => $creative_info) {
            $creative = [];
            $creative['name'] = sprintf("%s_创意_%s_%s", $param->ad2_name_text, $key, date('Y-m-d:H:i:s'));
            $creative['photo_id'] = $material_media_id_map[$creative_info['video_info']['id']]['id'];
            $creative['cover_token'] = $material_media_id_map[$creative_info['cover_info']['id']]['id'];
            $creative['description'] = $creative_info['title'];
            $creative['action_bar'] = $other_setting->action_bar_text;
            $creative['creative_material_type'] = $this->getMaterialType($creative_info);

            if ($setting->new_expose_tag) {
                $creative['new_expose_tag'] = [];
                foreach ($setting->new_expose_tag as $text) {
                    $creative['new_expose_tag'][] = $text;
                }
            }

            //快手开屏视频
            if ($creative_info['video_info']['video_list'] ?? false) {
                $creative['photo_id'] = 0;
                $creative['cover_token'] = '';
                $creative['description'] = '';
                $creative['action_bar'] = '';
                $creative['creative_material_type'] = KuaishouEnum::CREATIVE_MATERIAL_TYPE_SPLASH_VIDEO;
                foreach ($creative_info['video_info']['video_list'] as $splash_video) {
                    $creative['splash_photos'][] = ['photo_id' => $material_media_id_map[$splash_video['id']]['id']];
                }
            }
            $creative_list->addCreative(new CreativeCustomParam($creative));
        }

        return $creative_list;
    }

    /**
     * 新建程序化创意参数
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return CreativeProgramParam
     */
    private function makeAD3ProgramParam(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /** @var ADSettingContentParam $setting ; */
        $setting = $param->setting;
        /** @var ADOtherSettingContentParam $other_setting ; */
        $other_setting = $param->other_setting;

        $creative = [];
        $creative['action_bar'] = $other_setting->action_bar_text;
        $creative['package_name'] = $param->getSiteConfig()->package;
        $creative['click_url'] = $param->getSiteConfig()->action_track_url;
        $creative['creative_category'] = $setting->creative_category ?: '';
        $creative['creative_tag'] = $setting->creative_tag ? implode(',', $setting->creative_tag) : [];
        $creative['photo_list'] = [];

        if ($setting->new_expose_tag) {
            $creative['expose_tag_str'] = [];
            foreach ($setting->new_expose_tag as $text) {
                $creative['expose_tag_str'][] = $text;
            }
        }

        foreach ($param->getWordList() as $word) {
            $creative['captions'][] = trim($word);
        }

        foreach ($param->creative_list as $creative_info) {
            $photo_info = [
                'photo_id' => $material_media_id_map[$creative_info['video_info']['id']]['id'],
                'image_token' => $material_media_id_map[$creative_info['cover_info']['id']]['id'],
                'photo_orientation' => $this->getMaterialType($creative_info)
            ];
            $creative['photo_infos'][] = $photo_info;
        }
        return new CreativeProgramParam($creative);
    }

    /**
     * @inheritDoc
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
    }

    /**
     * 推送人群包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $need_push_audience_list_data = $this->getNeedUploadAudienceList(
            $param->targeting->getAllAudienceIdList(),
            $param->account_id,
            $param->platform,
            $param->company
        );
        if ($need_push_audience_list_data->isNotEmpty()) {
            $audience_account_id_list = $need_push_audience_list_data->pluck('account_id')->toArray();
            if ($audience_account_id_list) {
                $this->pushAudience(
                    $audience_account_id_list,
                    $need_push_audience_list_data->toArray(),
                    [$param->account_id]
                );
                // 后面大数据提供接口来同步
//                $ks_audience = new KSAudience();
//                $media_audience_data = $ks_audience->request(
//                    $account_id,
//                    $access_token
//                );
//                $ks_audience->store($param->platform, $param->account_id, $media_audience_data);
            } else {
                throw new AppException("人群包从未上传过");
            }
        }
    }

    /**
     * 等待打包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        if ($param->getSiteConfig()->game_type == "安卓") {
            $data = array_merge(
                $param->toArray(),
                [
                    'game_type' => $param->getSiteConfig()->game_type,
                    'download_url' => $param->getSiteConfig()->download_url
                ]);
            (new SiteMQLogic)->produceKuaishouCheckPackageTask($data, 5);
            throw new AppException('打包未完成', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }
    }

    /**
     * 上传创意文件素材
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return array|mixed
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::KUAISHOU, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        // 校验素材 防止复用媒体方已删除素材文件
        $upload_info_list = $this->verifyAndResetMaterial($upload_info_list, $account_param);

        $result = [];
        foreach ($material_file_list as $material_file) {

            // 素造素材
            if ($material_file->notify == 1) {
                $result[$material_file->id] = $this->uploadFileForCreatorVideo(
                    $account_param,
                    $material_file
                );
                // 不同步了
//                // 同步更新对应素材
//                $ks_ad_video_list = new KSAdVideoList();
//                $ks_ad_video_data = $ks_ad_video_list->request(
//                    $account_id,
//                    $access_token
//                );
//                $ks_ad_video_list->store($account_info->platform, $account_id, $ks_ad_video_data);
                continue;
            }

            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::KUAISHOU;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;
            if (isset($upload_info_list[$material_file->id]) && $upload_info_list[$material_file->id]) {
                $result[$material_file->id] = [
                    'id' => $upload_info_list[$material_file->id]->media_material_file_id,
                    'url' => $upload_info_list[$material_file->id]->url,
                ];
            } else {
                if ($material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ||
                    $material_file->file_type == MaterialFileModel::FILE_TYPE_COVER) {
                    $media_file_info = $this->uploadImage(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                } else {
                    $media_file_info = $this->uploadVideo(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                }
                if ($media_file_id) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * 推送素造素材
     * @param MediaAccountInfoParam $account_param
     * @param $material_file
     * @return array
     */
    public function uploadFileForCreatorVideo(MediaAccountInfoParam $account_param, $material_file)
    {
        $file_info = (new OdsKuaishouAdCreatorVideoLogModel())->getNeedUploadVideo($account_param->account_id, $material_file['signature']);

        if ($file_info) {
            if ($file_info->is_upload == 0) {
                // 推送
                (new MediaKuaishou())->pushVideo(
                    ********,
                    $file_info->photo_id,
                    $account_param->account_id
                );

                // 组装数据
                return [
                    'id' => $file_info->photo_id,
                    'url' => $material_file['url']
                ];
            } else {
                return [
                    'id' => $file_info->photo_id,
                    'url' => $material_file['url']
                ];
            }
        } else {
            throw new AppException("找不到mad({$material_file['signature']})对应的素造素材信息");
        }
    }

    /**
     * 是否重启任务
     * @param ADTaskParam $task_param
     * @return bool|mixed
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        $need_restart_message_format_list = [
            '当前应用权限扫描中',
            '应用分钟级请求过于频繁，请稍后再试',
            '应用扫描暂未成功，不可选择此应用投放',
            '正在尝试重新解析',
            '文件操作过于频繁',
            '存在尚未完成转码的视频',
            '该应用请求过于频繁',
        ];
        foreach ($need_restart_message_format_list as $message_format) {
            if (strpos($task_param->error_msg, $message_format) !== false) {
                return true;
            }
        }

        return false;
    }



    /**
     * @inheritDoc
     */
    function prepareCreativeList(ADTaskParam $task_param)
    {
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaStatus(): Collection
    {
        $list = (new OdsKuaishouUnitLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::DIFF_MEDIA_OPT_STATUS_MAP[MediaType::KUAISHOU][2][$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        $list = (new OdsKuaishouUnitLogModel())->getInventoryList($condition);
        if ($list->isEmpty()) {
            return $list;
        }
        return $list->map(function ($item) use ($keyword) {
            $inventory_type = json_decode($item->scene_id, true);
            $inventory_type_cn = [];
            foreach ($inventory_type as $value) {
                if (is_null($value)) {
                    return false;
                }
                $inventory_type_cn[] = ADFieldsENToCNMap::INVENTORY_TO_CN['KUAISHOU-' . $value] ?? $value;
            }
            unset($value);
            $label = implode(',', $inventory_type_cn);

            if ('' === $keyword || ('' !== $keyword && false !== strpos($label, $keyword))) {
                return [
                    'label' => $label,
                    'value' => $item->scene_id,
                ];
            } else {
                return false;
            }
        })->filter()->values();
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        return '';
    }

    /**
     * 处理投放排期时间
     * -格式为[[0,1,23],[],[],[],[],[],[]]
     * -7天24小时格式
     * @param $schedule_time
     * @return string
     */
    protected function makeWeekTime($schedule_time)
    {
        $schedule_time = array_chunk($schedule_time, 24);
        foreach ($schedule_time as &$week) {
            foreach ($week as $hour => $bool) {
                if ($bool) {
                    $week[$hour] = $hour;
                } else {
                    unset($week[$hour]);
                }
            }
            $week = array_values($week);
        }
        return json_encode($schedule_time);
    }

    public function getTransferSet($data)
    {
        // TODO: Implement getTransferSet() method.
    }

    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if ($param->getSiteConfig()->game_pack == 2) {
            return false;
        } else {
            if (!$param->convert_id) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 刷新账号余额信息
     *
     * @param $data
     *
     * @return mixed
     */
    public function refreshAccountBalance( $data ) {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid( int $media_type, array $data, array $ad_format_target, string $update_field ) {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
}

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateFirstClassScheduleTime() method.
}

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateSecondClassScheduleTime() method.
}

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status() {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    /**
     * 广告一键起量
     *
     * @param $data
     *
     * @return mixed
     */
    public function updateADAnalysisADRaise( $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
}

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
