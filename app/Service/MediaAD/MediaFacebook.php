<?php

namespace App\Service\MediaAD;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\FaceBook\Creative\CreativeModel;
use App\Model\RedisModel\FaceBookCreateFolderModel;
use App\Model\SqlModel\Zeda\FacebookCreativeFolderLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Model\SqlModel\Zeda\MaterialThemeModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\MediaAccountInfoParam;
use App\Struct\Input;
use Illuminate\Support\Collection;
use RedisException;
use Throwable;

class MediaFacebook extends AbstractMedia
{

    public function getInterestActionCategoryList(Input $input)
    {
        // TODO: Implement getInterestActionCategoryList() method.
    }

    public function getInterestActionKeywordList(Input $input)
    {
        // TODO: Implement getInterestActionKeywordList() method.
    }

    public function getInterestInterestCategoryList(Input $input)
    {
        // TODO: Implement getInterestInterestCategoryList() method.
    }

    public function getInterestInterestKeywordList(Input $input)
    {
        // TODO: Implement getInterestInterestKeywordList() method.
    }

    public function campaignList(Input $input)
    {
        // TODO: Implement campaignList() method.
    }

    public function convertList(Input $input)
    {
        // TODO: Implement convertList() method.
    }

    public function createConvert(ConvertCreateParam $param)
    {
        // TODO: Implement createConvert() method.
    }

    public function createChannelPackage(ChannelPackageParam $param)
    {
        // TODO: Implement createChannelPackage() method.
    }

    public function audiencePackageList(Input $input)
    {
        // TODO: Implement audiencePackageList() method.
    }

    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateAd() method.
    }

    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateADStatus() method.
    }

    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateCreativeStatus() method.
    }

    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement createAudience() method.
    }

    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement expandAudience() method.
    }

    /**
     * @param $account_id
     * @param $business_id
     * @param $access_token
     * @param $sub_folder_name
     * @return mixed
     * @throws RedisException
     * @throws Throwable
     */
    private function getCreativeFolder($account_id, $business_id, $access_token, $sub_folder_name = '')
    {
        $rm = new FaceBookCreateFolderModel();
        try {
            for ($i = 0; !$rm->setLock(); $i++) {
                if ($i > 30) {
                    throw new AppException('创建文件夹失败');
                } else {
                    sleep(1);
                }
            }
            $root_folder_name = "{$business_id}共享";
            $data = (new FacebookCreativeFolderLogModel())->getFacebookCreativeFolder($business_id, $root_folder_name);

            if ($data) {
                $root_folder_id = $data->creative_folder_id;
            } else {
                $result = (new CreativeModel($business_id))->createFolders($root_folder_name, $access_token);
                if ($result['id'] ?? '') {
                    $root_folder_id = $result['id'];
                    (new FacebookCreativeFolderLogModel())->insertLog($account_id, $business_id, $root_folder_name, $result['id']);
                } else {
                    $rm->delLock();
                    throw new AppException("新建文件夹($root_folder_name)失败");
                }
            }

            if ($sub_folder_name) {
                $sub_data = (new FacebookCreativeFolderLogModel())->getFacebookCreativeFolder($business_id, $sub_folder_name);
                if ($sub_data) {
                    $rm->delLock();
                    return $sub_data->creative_folder_id;
                } else {
                    $sub_result = (new CreativeModel($business_id))->createFolders($sub_folder_name, $access_token, $root_folder_id);
                    if ($sub_result['id'] ?? '') {
                        $sub_folder_id = $sub_result['id'];
                        (new FacebookCreativeFolderLogModel())->insertLog($account_id, $business_id, $sub_folder_name, $sub_result['id']);
                        $rm->delLock();
                        return $sub_folder_id;
                    } else {
                        $rm->delLock();
                        throw new AppException("新建文件夹($root_folder_name)失败");
                    }
                }
            } else {
                $rm->delLock();
                return $root_folder_id;
            }
        } catch (Throwable $e) {
            $rm->delLock();
            throw $e;
        }
    }


    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        $media_account_info = (new MediaAccountModel())->getDataByAccountId($advertiser_id);

        $log = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdAndBSID($param->id, MediaType::FACEBOOK, $media_account_info->toutiao_majordomo_id);
        if ($log) {
            return [
                'id' => $log->media_material_file_id,
                'business_id' => $media_account_info->toutiao_majordomo_id,
                'url' => ''
            ];
        }

        $theme_data = (new MaterialFileModel())->getListOfThemeInfoByIds([$param->id]);
        if ($theme_data->isEmpty()) {
            throw new AppException('找不到素材信息');
        }
        $theme_data = $theme_data[0];
        $sub_folder_name_data = (new MaterialThemeModel())->getDataOfPTheme((int)($theme_data->theme_id / 1000), $theme_data->platform);

        if (!$sub_folder_name_data) {
            throw new AppException('找不到题材信息');
        }

        $creative_folder_id = $this->getCreativeFolder($advertiser_id, $media_account_info->toutiao_majordomo_id, $access_token, $sub_folder_name_data->name . '-API');
        if ($param->file_type == 3) {
            $image_file_url = MaterialFileModel::VIDEO_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
        } elseif ($param->file_type == 4) {
            $image_file_url = MaterialFileModel::ICON_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
        } else {
            $image_file_url = MaterialFileModel::IMG_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
        }
        $fp = fopen($image_file_url, "r", 0);
        $gambar = fread($fp, filesize($image_file_url));
        fclose($fp);
        $base64 = chunk_split(base64_encode($gambar));
        $result = (new CreativeModel($media_account_info->toutiao_majordomo_id))->uploadImage($base64, $param->filename, $access_token, $creative_folder_id);
        return [
            'id' => $result['images'][$param->filename]['id'],
            'business_id' => $media_account_info->toutiao_majordomo_id,
            'url' => $result['images'][$param->filename]['url']
        ];
    }

    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        $media_account_info = (new MediaAccountModel())->getDataByAccountId($advertiser_id);

        $log = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdAndBSID($param->id, MediaType::FACEBOOK, $media_account_info->toutiao_majordomo_id);
        if ($log) {
            return [
                'id' => $log->media_material_file_id,
                'business_id' => $media_account_info->toutiao_majordomo_id,
                'url' => ''
            ];
        }

        $theme_data = (new MaterialFileModel())->getListOfThemeInfoByIds([$param->id]);
        if ($theme_data->isEmpty()) {
            throw new AppException('找不到素材信息');
        }
        $theme_data = $theme_data[0];
        $sub_folder_name_data = (new MaterialThemeModel())->getDataOfPTheme((int)($theme_data->theme_id / 1000), $theme_data->platform);
        if (!$sub_folder_name_data) {
            throw new AppException('找不到题材信息');
        }

        $creative_folder_id = $this->getCreativeFolder($advertiser_id, $media_account_info->toutiao_majordomo_id, $access_token, $sub_folder_name_data->name . '-API');
        $video_file_url = MaterialFileModel::VIDEO_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
        $creativeModel = new CreativeModel($media_account_info->toutiao_majordomo_id);
        $fp = fopen($video_file_url, 'r');
        $session_info = $creativeModel->startVideoChunk($param->filename, $creative_folder_id, $access_token, $param->size);
        $business_video_id = $session_info['business_video_id'];
        $session_id = $session_info['upload_session_id'];
        $start_offset = $session_info['start_offset'];
        $end_offset = $session_info['end_offset'];

        $tmp_srv = SRV_DIR . "/tmp/video/$param->platform/$param->material_id/$param->id";

        if (!file_exists($tmp_srv)) {
            mkdir($tmp_srv, 0755, true);
        }

        while ($start_offset != $end_offset) {
            $video_file_chunk = fread($fp, $end_offset - $start_offset);

            $file = tempnam($tmp_srv, $advertiser_id);

            file_put_contents($file, $video_file_chunk);

            $chunk_info = $creativeModel->uploadVideoChunk(
                $param->filename,
                $creative_folder_id,
                $access_token,
                $session_id,
                $start_offset,
                $file
            );
            $start_offset = $chunk_info['start_offset'];
            $end_offset = $chunk_info['end_offset'];

            if (file_exists($file)) {
                unlink($file);
            }
        }

        $creativeModel->finishVideoChunk(
            $param->filename,
            $creative_folder_id,
            $access_token,
            $session_id
        );

        return [
            'id' => $business_video_id,
            'business_id' => $media_account_info->toutiao_majordomo_id,
            'url' => ''
        ];
        // stream_get_contents
    }

    public function getIndustryList(Input $input)
    {
        // TODO: Implement getIndustryList() method.
    }

    public function getWordById($input, $media_type)
    {
        // TODO: Implement getWordById() method.
    }

    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        // TODO: Implement getAudienceListByIds() method.
    }

    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        // TODO: Implement pushAudience() method.
    }

    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        // TODO: Implement audienceList() method.
    }

    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        // TODO: Implement flowList() method.
    }

    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        // TODO: Implement getNeedUploadAudienceList() method.
    }

    public function getTargetingDataByAD2($company, $audience_md5)
    {
        // TODO: Implement getTargetingDataByAD2() method.
    }

    public function getTargetingName(array $audience_info)
    {
        // TODO: Implement getTargetingName() method.
    }

    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        // TODO: Implement fillTargetingInfo() method.
    }

    public function getAdActionList(array $condition)
    {
        // TODO: Implement getAdActionList() method.
    }

    public function getActionWord(array $data)
    {
        // TODO: Implement getActionWord() method.
    }

    public function getAdInterestList(array $condition)
    {
        // TODO: Implement getAdInterestList() method.
    }

    public function getInterestWord(array $data)
    {
        // TODO: Implement getInterestWord() method.
    }

    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisStatus() method.
    }

    public function updateADAnalysisFirstClass(array $data)
    {
        // TODO: Implement updateADAnalysisFirstClass() method.
    }

    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisSecondClass() method.
    }

    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisBudgetOrBid() method.
    }

    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        // TODO: Implement handleADAnalysisBudgetOrBidMQData() method.
    }

    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        // TODO: Implement updateADAnalysisTargeting() method.
    }

    public function addADTaskByApi(array $input)
    {
        // TODO: Implement addADTaskByApi() method.
    }

    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    public function updateAndroidChannelPackage(array $input)
    {
        // TODO: Implement updateAndroidChannelPackage() method.
    }

    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisDeepBidOrROI() method.
    }

    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement deleteADAnalysisAD() method.
    }

    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassBudget() method.
    }

    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisAccountBudget() method.
    }

    function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    function getTargetAudienceEstimate(array $target, $account_info)
    {
        // TODO: Implement getTargetAudienceEstimate() method.
    }

    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement beforeMakeSite() method.
    }

    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement afterMakeSite() method.
    }

    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        // TODO: Implement createAD() method.
    }

    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushFlowPackage() method.
    }

    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushAudiencePackage() method.
    }

    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement waitDelayPack() method.
    }

    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement getMaterialFileMediaInfo() method.
    }

    function isRestartADTask(ADTaskParam $task_param)
    {
        // TODO: Implement isRestartADTask() method.
    }

    function prepareCreativeList(ADTaskParam $task_param)
    {
        // TODO: Implement prepareCreativeList() method.
    }

    function getAgentGroup(ADTaskParam $task_param)
    {
        // TODO: Implement getAgentGroup() method.
    }

    public function getDiffMediaStatus()
    {
        // TODO: Implement getDiffMediaStatus() method.
    }

    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        // TODO: Implement getDiffMediaInventoryList() method.
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        // TODO: Implement getADName() method.
    }

    public function getTransferSet($data)
    {
        // TODO: Implement getTransferSet() method.
    }

    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if (!$param->convert_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新账号余额信息
     *
     * @param $data
     *
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
    }

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassScheduleTime() method.
    }

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateSecondClassScheduleTime() method.
    }

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status()
    {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    /**
     * 广告一键起量
     *
     * @param $data
     *
     * @return mixed
     */
    public function updateADAnalysisADRaise( $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
}

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
