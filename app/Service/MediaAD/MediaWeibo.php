<?php

namespace App\Service\MediaAD;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Weibo\Assets\AssetsModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\MediaAccountInfoParam;
use App\Struct\Input;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use Throwable;

class MediaWeibo extends AbstractMedia
{

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestActionCategoryList(Input $input)
    {
        // TODO: Implement getInterestActionCategoryList() method.
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestActionKeywordList(Input $input)
    {
        // TODO: Implement getInterestActionKeywordList() method.
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        // TODO: Implement getInterestInterestCategoryList() method.
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        // TODO: Implement getInterestInterestKeywordList() method.
    }

    /**
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        // TODO: Implement campaignList() method.
    }

    /**
     * @param ADTaskParam $param
     * @return mixed
     */
    public function isCreateConvert(ADTaskParam $param)
    {
        // TODO: Implement isCreateConvert() method.
    }

    /**
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        // TODO: Implement convertList() method.
    }

    /**
     * @param ConvertCreateParam $param
     * @return array
     */
    public function createConvert(ConvertCreateParam $param)
    {
        // TODO: Implement createConvert() method.
    }

    /**
     * @param ChannelPackageParam $param
     * @return array
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        // TODO: Implement createChannelPackage() method.
    }

    /**
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        // TODO: Implement audiencePackageList() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateAd() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateADStatus() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateCreativeStatus() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement createAudience() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement expandAudience() method.
    }

    /**
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $account_info = (new MediaAccountModel())->getDataByAccountId($advertiser_id, MediaType::WEIBO);
            if (!$account_info) {
                throw new AppException('找不到账号：' . $advertiser_id);
            }
            $model = new AssetsModel();

            if ($param->file_type == MaterialFileModel::FILE_TYPE_IMAGE) {
                $img_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
            } elseif ($param->file_type == MaterialFileModel::FILE_TYPE_ICON) {
                $img_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
            } else {
                $img_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            }

            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file_path = "$upload_path/$img_path/$platform/$material_id/$file_name";

            $result = $model->imageUpload($file_path, $file_name, $access_token, $advertiser_id);
            return [
                'id' => $result['pic_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $account_info = (new MediaAccountModel())->getDataByAccountId($advertiser_id, MediaType::WEIBO);
            if (!$account_info) {
                throw new AppException('找不到账号：' . $advertiser_id);
            }
            $model = new AssetsModel();

            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file_path = "$upload_path/$video_path/$platform/$material_id/$file_name";

            $result = $model->videoUpload($file_path, $file_name, $access_token, $advertiser_id);

            return [
                'id' => $result['fid'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        // TODO: Implement getIndustryList() method.
    }

    /**
     * @param array $input
     * @param $media_type
     * @return mixed
     */
    public function getWordById($input, $media_type)
    {
        // TODO: Implement getWordById() method.
    }

    /**
     * @param array $ids
     * @param int $status
     * @return mixed
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        // TODO: Implement getAudienceListByIds() method.
    }

    /**
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        // TODO: Implement pushAudience() method.
    }

    /**
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        // TODO: Implement audienceList() method.
    }

    /**
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        // TODO: Implement flowList() method.
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        // TODO: Implement getNeedUploadAudienceList() method.
    }

    /**
     * @param $company
     * @param $audience_md5
     * @return mixed
     */
    public function getTargetingDataByAD2($company, $audience_md5)
    {
        // TODO: Implement getTargetingDataByAD2() method.
    }

    /**
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        // TODO: Implement getTargetingName() method.
    }

    /**
     * @param array $targeting_info
     * @param bool $is_return
     * @return array
     */
    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        // TODO: Implement fillTargetingInfo() method.
    }

    /**
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        // TODO: Implement getAdActionList() method.
    }

    /**
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        // TODO: Implement getActionWord() method.
    }

    /**
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        // TODO: Implement getAdInterestList() method.
    }

    /**
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        // TODO: Implement getInterestWord() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisStatus() method.
    }

    /**
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisFirstClass(array $data)
    {
        // TODO: Implement updateADAnalysisFirstClass() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisSecondClass() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisBudgetOrBid() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @return mixed
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        // TODO: Implement handleADAnalysisBudgetOrBidMQData() method.
    }

    /**
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        // TODO: Implement updateADAnalysisTargeting() method.
    }

    /**
     * @param array $input
     * @return int
     */
    public function addADTaskByApi(array $input)
    {
        // TODO: Implement addADTaskByApi() method.
    }

    /**
     * @param array $input
     * @return mixed
     */
    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    /**
     * @param array $input
     * @return mixed
     */
    public function updateAndroidChannelPackage(array $input)
    {
        // TODO: Implement updateAndroidChannelPackage() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisDeepBidOrROI() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement deleteADAnalysisAD() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassBudget() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisAccountBudget() method.
    }

    /**
     * @param array $input
     * @return mixed
     */
    function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    /**
     * @param array $data
     * @param array $input
     * @return mixed
     */
    function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    /**
     * @param array $target
     * @param $account_info
     * @return mixed
     */
    function getTargetAudienceEstimate(array $target, $account_info)
    {
        // TODO: Implement getTargetAudienceEstimate() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement beforeMakeSite() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement afterMakeSite() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        // TODO: Implement createAD() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushFlowPackage() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushAudiencePackage() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement waitDelayPack() method.
    }

    /**
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement getMaterialFileMediaInfo() method.
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        // TODO: Implement isRestartADTask() method.
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    function prepareCreativeList(ADTaskParam $task_param)
    {
        // TODO: Implement prepareCreativeList() method.
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    function getAgentGroup(ADTaskParam $task_param)
    {
        // TODO: Implement getAgentGroup() method.
    }

    /**
     * @return Collection
     */
    public function getDiffMediaStatus()
    {
        // TODO: Implement getDiffMediaStatus() method.
    }

    /**
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        // TODO: Implement getDiffMediaInventoryList() method.
    }

    /**
     * @param ADTaskParam $task_param
     * @param $name_norms
     * @return mixed
     */
    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        // TODO: Implement getADName() method.
    }

    /**
     * @param array $data
     * @return mixed
     */
    public function getTransferSet(array $data)
    {
        // TODO: Implement getTransferSet() method.
    }

    /**
     * @param $data
     * @return mixed
     */
    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    /**
     * @param $data
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
    }

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassScheduleTime() method.
    }

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateSecondClassScheduleTime() method.
    }

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status()
    {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    public function updateADAnalysisADRaise( array $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
    }

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
