<?php
/**
 * Created by PhpStorm.
 * User: 张中昊
 * Date: 2020/02/25
 * Time: 10:44
 */

namespace App\Service\MediaAD;

use App\Constant\UcEnum;
use App\Exception\AppException;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\UC\V2\Ad\AdModel;
use App\Model\HttpModel\UC\V2\Project\ProjectModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Uc\ADTargetingContentParam;
use App\Param\ADServing\Uc\V2\ADOtherSettingContentParam;
use App\Param\ADServing\Uc\V2\ADSettingContentParam;
use App\Param\MediaAccountInfoParam;
use App\Param\UC\V2\AdParam;
use App\Param\UC\V2\ProjectParam;
use App\Service\ScriptMsgService;
use Throwable;

class MediaUcV2 extends MediaUc
{
    /**
     * @inheritDoc
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
    }

    /**
     * 检查是否打包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /**
         * @var ADOtherSettingContentParam $other_setting ;
         */
        $other_setting = $param->other_setting;
        // 如果是需要等待打包的任务，需要走解析包链接
        if ($param->getSiteConfig()->game_type == '安卓') {

            $data = array_merge($param->toArray(), [
                'ad_task_list_id' => $param->id,
                'target_url' => '',
                'sdk_ext' => $param->getSiteConfig()->sdk_ext,
                'game_type' => $param->getSiteConfig()->game_type,
                'download_url' => $param->getSiteConfig()->download_url,
            ]);

            (new SiteMQLogic)->produceUCTask($data, 1);

            throw new AppException('等待打包中', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }

    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        $project = new ProjectParam([
            'name' => $param->ad1_name_text,
            'start_date' => date('Ymd', strtotime($setting->start_date)),
            'end_date' => date('Ymd', strtotime($setting->end_date)),
            'paused' => $setting->project_paused,
            'convert_monitor_group_id' => $param->getSiteConfig()->ext['convert_monitor_group_id']
        ]);

        $project->setSchedule($setting->schedule_time);
        $project->setTargeting([
            'manual_targeting' => 1,
            'audience_targeting' => $targeting->audience_targeting,
            'include_audiences' => $targeting->include_audiences,
            'exclude_audiences' => $targeting->exclude_audiences,
            'all_region' => $targeting->all_region,
            'regions' => $targeting->region,
            'region_people' => 0,
            'gender' => $targeting->gender,
//            'gender' => -1,
            'age' => $targeting->age == '000000' ? '-1' : $targeting->age,
//            'age' => '-1',
            'convert_filter' => $setting->convert_filter,
//            'convert_filter' => 0,
        ]);

        $rep = (new ProjectModel())->add(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name,
            $project
        );

        $param->ad1_id = $rep['successIdIndexes'][0]['id'] ?? 0;

        return $param->ad1_id;
    }

    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $ad = new AdParam([
            'name' => $param->ad1_name_text . "的广告创意" . date('Y-m-d H:i:s'),
            'project_id' => $param->ad1_id,
            'track_args' => 111,
            'industry' => $setting->industry,
            'label' => json_decode($setting->label, true),
            'logo_id' => $other_setting->logo_map[$account_param->account_id] ? $other_setting->logo_map[$account_param->account_id]['logo_id'] : 0,
            'paused' => $setting->ad_paused,
        ]);

        $ad->setComponentTypes($this->componentTypesJsonData($param->creative_list, $param->getSiteConfig()->app_name, $material_media_id_map, $param->word_list));

        $jf_down_url_id = 0;
        if ((isset($other_setting->page_map[$account_param->account_id]) && $other_setting->page_map[$account_param->account_id]) && $other_setting->download_type == UcEnum::DOWNLOAD_URL_JF) {
            $page_map = $other_setting->getUcPageExternalUrlMapInfo($account_param->account_id);
            $jf_down_url_id = $page_map['page_id'] ?? 0;
        } else {
            $jf_down_url_id = $param->getSiteConfig()->download_url;
        }

        $ad->setAdUrlObjectType($jf_down_url_id);

        $rep = (new AdModel())->add(
            $account_param->wechat_account_name,
            $account_param->account_password,
            $account_param->access_token,
            $account_param->account_name,
            $ad
        );

        return $rep['idIndexes'][0]['id'] ?? 0;
    }

    /**
     * 计划 三 步 骤
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建广告一级
        if (!$param->ad1_id) {
            try {
                $param->ad1_id = $this->getAD1IdByName($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告三级
        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));


    }
}
