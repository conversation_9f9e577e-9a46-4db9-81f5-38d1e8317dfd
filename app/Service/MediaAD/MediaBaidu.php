<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: 张中昊
 * Date: 2020/02/25
 * Time: 10:44
 */

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\BaiduEnum;
use App\Constant\BatchAD;
use App\Constant\ADFieldsENToCNMap;
use App\Constant\ConvertSourceType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Logic\DSP\ADAnalysisMQLogic;
use App\Logic\DSP\ADServingLogic;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\Baidu\AccountFeed\AccountFeedServiceModel;
use App\Model\HttpModel\Baidu\AdGroupFeed\AdGroupFeedModel;
use App\Model\HttpModel\Baidu\CampaignFeed\CampaignFeedModel;
use App\Model\HttpModel\Baidu\CreativeFeed\CreativeFeedModel;
use App\Model\HttpModel\Baidu\ImageManageService\ImageManageServiceModel;
use App\Model\HttpModel\Baidu\Image\ImageServiceModel;
use App\Model\HttpModel\Baidu\IntentTagFeed\IntentTagFeedServiceModel;
use App\Model\HttpModel\Baidu\OcpcTransFeed\OcpcTransFeedModel;
use App\Model\HttpModel\Baidu\ProjectFeed\ProjectFeedModel;
use App\Model\HttpModel\Baidu\VideoUpload\VideoUploadModel;
use App\Model\HttpModel\BaiduSearch\AppCenterJobService\AppCenterJobModel;
use App\Model\HttpModel\BaiduSearch\ExtAudienceService\ExtAudienceModel;
use App\Model\HttpModel\BaiduSearch\ShareService\ShareModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\DataMedia\OdsBaiduAccountLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduADHisLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduADLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduAudienceCrowdLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduAudienceGroupModel;
use App\Model\SqlModel\DataMedia\OdsBaiduCampaignHisLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduCampaignLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduCreativeHisLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduCreativeLogModel;
use App\Model\SqlModel\DataMedia\OdsBaiduLandingPageLogModel;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\CampaignCreateMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Baidu\ADTargetingContentParam;
use App\Param\ADServing\Baidu\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\Baidu\Basics\ADSettingContentParam;
use App\Param\Baidu\AdCreateParam;
use App\Param\Baidu\CampaignCreateParam;
use App\Param\Baidu\CreativeCreateParam;
use App\Param\Baidu\CustomMaterialParam;
use App\Param\Baidu\ElementsParam;
use App\Param\Baidu\IdeaPluginGroupParam;
use App\Param\Baidu\ProgramMaterialParam;
use App\Param\Baidu\ProjectParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Service\PlatformAD\PlatformAD;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Helpers;
use App\Utils\Image;
use App\Utils\Math;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use Throwable;

class MediaBaidu extends AbstractMedia
{
    /**
     * 已创建好的广告组列表
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        return [];
    }

    /**
     * 已创建好的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $campaign_info = (new CampaignCreateMediaLogModel())->getCreateLog(MediaType::BAIDU, $account_param->account_id, $param->ad1_name_text);
        return $campaign_info ? $campaign_info->campaign_id : 0;
    }

    /**
     * 创建广告组-返回创建的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        $campaign_data = [
            'campaign_feed_name' => $param->ad1_name_text,
            'pause' => $setting->campaign_pause,
            'starttime' => $setting->starttime ? date('Y-m-d', $setting->starttime / 1000) : null,
            'endtime' => $setting->starttime ? date('Y-m-d', $setting->endtime / 1000) : null,
            'schedule' => $setting->schedule_map ? Helpers::formatBaiduScheduleFromBinaryToJson(implode('', $setting->schedule_map)) : $setting->schedule_map,
            'campaign_type' => $setting->campaign_type,
            'objective' => 0
        ];

        $campaign_data = array_merge($setting->toArray(), $other_setting->toArray(), $campaign_data);

        if ($other_setting->inherit_ascription_type) {
            if ($other_setting->inherit_ascription_type == 2) {
                $campaign_data['inherit_user_ids'] = array_map(function ($e) {
                    return $e['account_id'];
                }, $other_setting->inherit_user_ids);
            }
            if ($other_setting->inherit_ascription_type == 1) {
                $campaign_data['inherit_user_ids'] = [$param->account_id];
            }
        } else {
            $campaign_data['inherit_ascription_type'] = 0;
            $campaign_data['inherit_user_ids'] = [];
        }

        if ($other_setting->bid_position == 1) {
            $campaign_data['bid_position'] = $other_setting->bid_position;


            if ($other_setting->ftypes_none == 'targeting') {
                if ($targeting->ftypes_none == 'zidingyi' || $targeting->ftypes_none == 'baiqingteng') {
                    $campaign_data['ftypes'] = $targeting->ftypes;
                } else {
                    $campaign_data['ftypes'] = [];
                }
            } else {
                $campaign_data['ftypes'] = $other_setting->ftypes;
            }

            $campaign_data['bidtype'] = $setting->bidtype;
            $campaign_data['bid'] = 0;
        }

        $ccp = new CampaignCreateParam($campaign_data);
        $app_url = $other_setting->app_url;

        //推广IOS应用
        if ($other_setting->subject == BaiduEnum::SUBJECT_APP_IOS) {
            $app_url_array = explode('?', $app_url);
            $app_url = $app_url_array[0];
        }

        $app_info_data = [
            'app_name' => $other_setting->app_name,
            'app_url' => trim($app_url),
            'apk_name' => $param->getSiteConfig()->package,
        ];

        //推广安卓应用
        if ($other_setting->subject == BaiduEnum::SUBJECT_APP_ANDROID) {
            $app_info_data = [
                'channel_id' => $param->getSiteConfig()->app_android_channel_package_id,
            ];
        }

        $ccp->setAppInfo($app_info_data);

        if ($other_setting->bid_position == 1) {
            $ccp->setOcpc($this->getOcpcData($param));
        }

        $campaign_feed_model = new CampaignFeedModel();
        $response_data = $campaign_feed_model->addCampaignFeed(
            $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name,
            $ccp
        );

        $campaign_id = $response_data[0]['campaignFeedId'];
        (new CampaignCreateMediaLogModel())->addCreateLog($param->media_type, $param->account_id, $campaign_id, $param->ad1_name_text);

        return $campaign_id;
    }

    /**
     * 获取广告计划可用的转化列表
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        return [];
    }

    /**
     * 新建转化
     * @param ConvertCreateParam $param
     * @return array
     */
    public function createConvert(ConvertCreateParam $param)
    {
        $convert_data = (new OcpcTransFeedModel())->add($param);
        $convert_data[0]['convert_id'] = $convert_data[0]['appTransId'];
        return $convert_data[0];
    }

    /**
     * 新建渠道包
     * @param ChannelPackageParam $param
     * @return array
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        $data = (new AppCenterJobModel())->add($param);
        return [
            'app_android_channel_package_id' => "job_id;{$data[0]['jobId']}",
        ];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionCategoryList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionKeywordList(Input $input)
    {
        $media_account_model = new MediaAccountModel();
        $account_data = $media_account_model->getRandomDataByMediaType($input['media_type']);
        if (!$account_data) {
            throw new AppException('找不到对应的渠道数据');
        }
        $input['access_token'] = $account_data->access_token;
        $input['advertiser_id'] = $account_data->account_id;

        $intent_tag_model = new IntentTagFeedServiceModel();
        $password = $account_data->access_token;
        $username = $account_data->company;
        $token = $account_data->refresh_token;
        $target = $account_data->account_name;
        $response_data_search = $intent_tag_model->get($username, $password, $token, $target, $input['query']);
        if (count($response_data_search) > 0) {
            $response_data['list'] = $response_data_search;
        } else {
            $response_data['list'] = [];
        }
        return $response_data;
    }

    /**
     * 根据输入获取兴趣类目
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        return [];
    }

    /**
     * 根据输入获取兴趣关键词
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        return [];
    }

    /**
     * 百度SDK管理图片logo - 应用截图 - 版权证明文件 上传到媒体
     * @param $path
     * @return string
     */
    public function uploadSdkImage($path): string
    {
        $file = file_get_contents(SRV_DIR . $path);
        $base_image = chunk_split(base64_encode($file));

        if (strlen($base_image) > ********) {
            throw new AppException("图片请求体过大，图片不要超过10M");
        }
        try {
            // 从数据库随机挑选出一个幸运观众
            $account_data = (new MediaAccountModel())->getDataByAccountId(********, MediaType::BAIDU);
            $image_model = new ImageManageServiceModel();
            $username = $account_data->company;
            $password = $account_data->access_token;
            $target = $account_data->account_name;
            $token = $account_data->refresh_token;
            $images = [['content' => $base_image]];
            $result = $image_model->upload(
                $username,
                $password,
                $token,
                $target,
                $images
            );
            if (isset($result[0]['url'])) {
                return $result[0]['url'];
            } else {
                throw new AppException("上传失败，请重新上传图片");
            }
        } catch (Throwable $e) {
            $error_msg = $e->getMessage();
            if (strpos($error_msg, '上传图片格式有误')) {
                $error_msg .= ',请重新上传图片或检查图片格式';
            }
            throw new AppException($error_msg);
        }
    }

    /**
     * 上传图片到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $img_path = $param->file_type == MaterialFileModel::FILE_TYPE_IMAGE ? EnvConfig::MATERIAL_IMG_DIR_NAME : EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file = file_get_contents("$upload_path/$img_path/$platform/$material_id/$file_name");
            // 这里需要是数组
            $images = [
                [
                    'content' => chunk_split(base64_encode($file)),
                ]
            ];
            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->company;
            $password = $media_account_info->access_token;
            $target = $media_account_info->account_name;
            $token = $media_account_info->refresh_token;
            $image_model = new ImageManageServiceModel();
            $result = $image_model->upload(
                $username,
                $password,
                $token,
                $target,
                $images,
                true
            );
            return [
                'id' => $result[0]['imageId'],
                'url' => $result[0]['url']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . "$upload_path/$img_path/$platform/$material_id/$file_name" . $e->getMessage());
        }
    }

    /**
     * 上传图片到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @return array
     */
    public function uploadIcon(MaterialFileParam $param, $advertiser_id)
    {
        try {
            $img_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file = file_get_contents("$upload_path/$img_path/$platform/$material_id/$file_name");
            // 这里需要是数组
            $images = [['content' => chunk_split(base64_encode($file))]];
            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->company;
            $password = $media_account_info->access_token;
            $target = $media_account_info->account_name;
            $token = $media_account_info->refresh_token;
            $image_model = new ImageManageServiceModel();
            $result = $image_model->upload(
                $username,
                $password,
                $token,
                $target,
                $images,
                true
            );
            return [
                'id' => $result[0]['imageId'],
                'url' => $result[0]['url']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传视频到媒体
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file = "$upload_path/$video_path/$platform/$material_id/$file_name";

            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->company;
            $password = $media_account_info->access_token;
            $token = $media_account_info->refresh_token;
            $target = $media_account_info->account_name;

            $video_model = new VideoUploadModel();
            $result = $video_model->upload(
                $username,
                $password,
                $token,
                $target,
                $file,
                $param->signature,
                ['format' => $param->format, 'source' => 2, 'videoName' => $param->filename]);
            if (!$result) {
                throw new AppException("{$param->filename}上传出错:无返回数据!");
            }
            return [
                'id' => $result[0]['videoId'],
                'url' => $result[0]['url']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 获取创意行业列表
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        return [];
    }

    /**
     * id转词汇
     * @param array $input
     * @param $media_type
     * @return array
     */
    public function getWordById($input, $media_type)
    {
        return [];
    }


    /**
     * 获取人群包列表
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        $tcal_model = new OdsBaiduAudienceGroupModel();
        return $tcal_model->getListByCompany($company, $page, $rows, $id, $name, $tag);
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        $model = new OdsBaiduAudienceGroupModel();
        return $model->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    /**
     * @param $subject
     * @param $platform
     * @param $agent_id
     * @param $site_id
     * @param $url
     * @param $lp_mode
     * @return string
     */
    private function getCompleteUrl($subject, $platform, $agent_id, $site_id, $url, $lp_mode)
    {
        if ($subject == 4) {
            return $url;
        }
        if ($subject != 1) {
            return '';
        }
        if ((int)$lp_mode === 1) {
            return $url;
        } else {
            return PlatformAD::create($platform)->getSiteLDYUrl($agent_id, $site_id, $url);
        }
    }

    private function getOcpcData(ADTaskParam $param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        $setting_array = $setting->toArray();
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        $other_setting_array = $other_setting->toArray();

        $setting_array['ocpc_bid'] = $setting->getOcpcBid();
        $setting_array['deep_ocpc_bid'] = $setting->getDeepOcpcBid();

        $ad_ocpc_data = array_merge(
            $setting_array,
            $other_setting_array,
            [
                'lp_url' => $this->getCompleteUrl(
                    $other_setting->subject,
                    $param->platform,
                    $param->agent_id,
                    $param->site_id,
                    $other_setting->lp_url,
                    $other_setting->lp_mode
                ),
                'app_trans_id' => $param->convert_id,
                'trans_from' => $param->getSiteConfig()->convert_source_type == ConvertSourceType::API ? BaiduEnum::TRANS_FROM_API : BaiduEnum::TRANS_FROM_SDK,
                'trans_type' => (int)$param->getSiteConfig()->convert_type
            ]
        );

        $ad_ocpc_data['use_roi'] = $setting->deep_trans_type == BaiduEnum::DEEP_TRANS_TYPES_CUSTOM;

        // 线索
        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            $ad_ocpc_data['app_trans_id'] = $other_setting->getConvertMap($param->account_id)->convert_id ?? 0;
            $ad_ocpc_data['trans_from'] = BaiduEnum::TRANS_FROM_CLUE;
            $ad_ocpc_data['trans_type'] = (int)$param->getSiteConfig()->convert_type === BaiduEnum::CONVERT_TYPE_ACTIVE ? BaiduEnum::CONVERT_TYPE_LOGIN : (int)$param->getSiteConfig()->convert_type;
        }

        $ad_ocpc_data['is_manual_bid_for_max_mode'] = BaiduEnum::IS_MAX_MODE_FALSE;
        $ad_ocpc_data['is_manual_deep_bid_for_max_mode'] = BaiduEnum::IS_MAX_MODE_FALSE;
        if ($setting->campaign_type == BaiduEnum::CAMPAIGN_TYPE_MAX_MODE) {
            if ($param->site_config['convert_type'] == BaiduEnum::CONVERT_TYPE_ACTIVE && $setting->optimize_deep_trans !== 'false') {
                //激活+深度转化才有深度出价
                $ad_ocpc_data['is_manual_deep_bid_for_max_mode'] = BaiduEnum::IS_MAX_MODE_TRUE;
            } else {
                //其他情况浅度出价
                $ad_ocpc_data['is_manual_bid_for_max_mode'] = BaiduEnum::IS_MAX_MODE_TRUE;
                $ad_ocpc_data['deep_ocpc_bid'] = 0;
            }
        }

        if ($other_setting->scene_type == 'RESERVE') {
            $ad_ocpc_data['trans_type'] = $other_setting->reserve_type;
            $ad_ocpc_data['trans_from'] = 2;
            $ad_ocpc_data['app_trans_id'] = $other_setting->getSceneConvertMap($param->account_id);
        }

        return $ad_ocpc_data;
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        $setting_array = $setting->toArray();
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        $other_setting_array = $other_setting->toArray();
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        $targeting_array = $targeting->toArray();

        if ($targeting_array['keywords_map']) {
            $targeting_array['keywords'] = implode(',', array_map(function ($v) {
                return str_replace(',', '，', $v);
            }, $targeting_array['keywords_map']));
        }

        $setting_array['ocpc_bid'] = $setting->getOcpcBid();

        if ($other_setting->subject != 1) {
            if (!$other_setting->ftypes) {
                $setting_array['ftypes'] = [2];
            }
        }
        $ad_post_data = array_merge(
            $setting_array,
            $other_setting_array,
            [
                'campaign_feed_id' => $param->ad1_id,
                'adgroup_feed_name' => $param->ad2_name_text,
                'producttypes' => $other_setting->producttypes,
                'pause' => $setting->ad_pause,
            ]
        );


        if ($other_setting->subject != BaiduEnum::SUBJECT_SITE_URL) {
            if ($other_setting->subject == BaiduEnum::SUBJECT_APP_IOS) {
                $targeting_array['device'] = BaiduEnum::DEVICE_IOS;
            } elseif ($other_setting->subject == BaiduEnum::SUBJECT_APP_ANDROID) {
                $targeting_array['device'] = BaiduEnum::DEVICE_ANDROID;
            }
        }
        $ad_targeting_data = array_merge(
            $targeting_array,
            [
                'exclude_trans' => $setting_array['exclude_trans'],
                'media_categories_bind_type' => 0,
                'media_categories' => '',
                'media_ids_bind_type' => 0,
                'mediaids' => '',
                'media_package' => '',
                'custom_media_package' => '',
            ]
        );

        if ($ad_targeting_data['age_none'] == 'zidingyi') {
            unset($ad_targeting_data['custom_age']);
        }
        if ($ad_targeting_data['age_none'] == 'custom') {
            unset($ad_targeting_data['age']);
        }

        if (in_array($setting_array['exclude_trans'], [1, 4, 5])) {
            $ad_targeting_data['exclude_trans_filter_time'] = $setting_array['exclude_trans_filter_time'];
        }

        if ($other_setting->subject == BaiduEnum::SUBJECT_PAGE_URL) {
            $other_setting->lp_url = $other_setting->getPageMap($account_param->account_id)->online_url ?? '';
        }


        if ($other_setting->ftypes_none == 'targeting') {
            if ($targeting->ftypes_none == 'zidingyi' || $targeting->ftypes_none == 'baiqingteng') {
                $ad_post_data['ftypes'] = $targeting->ftypes;
            } else {
                $ad_post_data['ftypes'] = [];
            }

            if ($targeting->producttypes_none != 'buxian') {
                $ad_post_data['producttypes'] = $targeting->producttypes;
            } else {
                $ad_post_data['producttypes'] = [];
            }
        }

        $acp = new AdCreateParam($ad_post_data);
        $acp->setAudience($ad_targeting_data);
        if ($other_setting->bid_position != 1) {
            $acp->setOcpc($this->getOcpcData($param));
        }

        $acp->setAutoOpt([
            'auto_opt_max_bid' => $setting->auto_opt_strategies,
            'auto_opt_strategies' => $setting->auto_opt_strategies
        ]);

        $ad_model = new AdGroupFeedModel();
        $response_data = $ad_model->addAdgroupFeed(
            $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name, $acp
        );
        return $response_data[0]['adgroupFeedId'];
    }

    private function makeProgramCreative(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $data = [
            'horizontal_videos' => [],
            'vertical_videos' => [],
            'large_pic' => [],
            'single_pic' => [],
            'package_3_pic' => [],
        ];
        foreach ($param->creative_list as $key => $value) {
            if (isset($value['cover_info'])) {
                $media_material_video_data = $material_media_id_map[$value['video_info']['id']];
                $media_material_cover_data = $material_media_id_map[$value['cover_info']['id']];
                if ($value['video_info']['width'] > $value['video_info']['height']) {
                    $data['horizontal_videos'][] = "{$media_material_video_data['id']} {$media_material_cover_data['url']}";
                } else {
                    if (
                        ($value['video_info']['width'] == 720 && $value['video_info']['height'] == 1280) &&
                        ($value['cover_info']['width'] == 1140 && $value['cover_info']['height'] == 640)
                    ) {
                        $vertical_cover_id = $this->getVerticalVideoCoverInfo(
                            $value['video_info']['url'],
                            $param->platform,
                            $account_param,
                            $param->creator
                        );
                        $data['vertical_videos'][] = "{$media_material_video_data['url']} {$media_material_video_data['id']} {$vertical_cover_id}||{$media_material_cover_data['url']}";
                    } else {
                        $data['vertical_videos'][] = "{$media_material_video_data['id']} {$media_material_cover_data['url']}";
                    }
                }
            } else {
                $data['large_pic'][] = "{$material_media_id_map[$value['image_info']['id']]['url']}";
            }
        }
        return $data;
    }

    /**
     * @param ADOtherSettingContentParam $other_setting
     * @param array $data
     * @return int
     */
    private function getMaterialStyle(ADOtherSettingContentParam $other_setting, array $data)
    {

        if (isset($data['cover_info'])) {
            if ($data['video_info']['width'] > $data['video_info']['height']) {
                if (in_array($other_setting->subject, [1, 4])) {
                    return 108;
                } else {
                    return 109;
                }
            } else {
                if (in_array($other_setting->subject, [1, 4])) {
                    return 111;
                } else {
                    return 112;
                }
            }
        } else {
            if (in_array($other_setting->subject, [1, 4])) {
                return 107;
            } else {
                return 106;
            }
        }
    }

    /**
     * @param string $video_url
     * @param $platform
     * @param MediaAccountInfoParam $account_param
     * @param $creator
     * @return mixed
     */
    private function getVerticalVideoCoverInfo(string $video_url, $platform, MediaAccountInfoParam $account_param, $creator)
    {
        $video_name = basename($video_url);

        $name = str_replace('.mp4', "_creative_1_(百度投放扩展)640x1140.jpg", $video_name);
        $cover_name = str_replace('.mp4', "_creative_1.jpg", $video_name);

        $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
        $material_file_model = new MaterialFileModel();
        $ods_material_file_log_model = new OdsMaterialFileLogModel();

        $data = $material_file_model->getDataByName($name);
        if ($data) {
            $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
                [$data->id],
                $platform,
                MediaType::BAIDU,
                $account_param->account_id
            );
            $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
            if ($upload_info_list) {
                return $upload_info_list[$data->id]->url;
            } else {
                $data = (array)$data;

                $insert = [];
                $insert['platform'] = $platform;
                $insert['media_type'] = MediaType::BAIDU;
                $insert['account_id'] = $account_param->account_id;
                $insert['material_id'] = $data['material_id'];
                $insert['file_type'] = $data['file_type'];
                $insert['file_id'] = $data['id'];
                $insert['filename'] = $data['filename'];
                $insert['uploader'] = $creator;
                $insert['signature'] = $data['signature'];
                $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                $media_file_info = $this->uploadImage(
                    new MaterialFileParam($data),
                    $account_param->account_id,
                    $account_param->access_token
                );
                if (!$media_file_info['id']) {
                    throw new AppException('上传竖版封面图失败');
                }
                $media_file_id = $media_file_info['id'];
                $media_file_url = $media_file_info['url'];
                $insert['media_material_file_id'] = $media_file_id;
                $insert['url'] = $media_file_url;
                $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                return $media_file_url;
            }
        } else {

            $cover_data = $material_file_model->getDataByName($cover_name);

            $sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
            $upload_path = SRV_DIR . "/{$access_path}";
            $result = Image::thumb(
                "$upload_path/$cover_data->platform/$cover_data->material_id/$cover_name",
                "$upload_path/$cover_data->platform/$cover_data->material_id/$name",
                '',
                640,
                1140
            );
            if ($result) {
                $material_file = [
                    'platform' => $platform,
                    'media_type' => 0,
                    'material_id' => $cover_data->material_id,
                    'file_type' => $cover_data->file_type,
                    'filename' => $name,
                    'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$cover_data->platform/$cover_data->material_id/$name",
                    'width' => 640,
                    'height' => 1140,
                    'scale' => Math::div(640, 1140),
                    'signature' => md5_file("$upload_path/$cover_data->platform/$cover_data->material_id/$name"),
                    'bitrate' => 0,
                    'size' => filesize("$upload_path/$cover_data->platform/$cover_data->material_id/$name"),
                    'format' => $cover_data->format,
                    'uploader' => $cover_data->uploader,
                    'create_time' => time(),
                    'update_time' => time(),
                    'is_del' => 0,
                    'is_ext' => 1
                ];
                $material_file['id'] = $material_file_model->add($material_file);
                if (EnvConfig::ENV === 'production' && $material_file['id']) {
                    $material_file['insert_time'] = date("Y-m-d H:i:s", $material_file['create_time']);
                    $material_file['update_time'] = date("Y-m-d H:i:s", $material_file['update_time']);
                    unset($material_file['create_time']);
                    $ods_material_file_log_model->add($material_file);
                }

                $insert = [];
                $insert['platform'] = $platform;
                $insert['media_type'] = MediaType::BAIDU;
                $insert['account_id'] = $account_param->account_id;
                $insert['material_id'] = $material_file['material_id'];
                $insert['file_type'] = $material_file['file_type'];
                $insert['file_id'] = $material_file['id'];
                $insert['filename'] = $material_file['filename'];
                $insert['uploader'] = $creator;
                $insert['signature'] = $material_file['signature'];
                $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                $media_file_info = $this->uploadImage(
                    new MaterialFileParam($material_file),
                    $account_param->account_id,
                    $account_param->access_token
                );
                if (!$media_file_info['id']) {
                    throw new AppException('上传竖版封面图失败');
                }
                $media_file_id = $media_file_info['id'];
                $media_file_url = $media_file_info['url'];
                $insert['media_material_file_id'] = $media_file_id;
                $insert['url'] = $media_file_url;
                $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                return $media_file_url;

            } else {
                throw new AppException('拓展封面失败!!');
            }
        }
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     * @throws Throwable
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        foreach ($param->word_list as &$word) {
            $word = trim($word);
        }

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        // 判断icon是否有上传到媒体
        if ($setting->user_portrait) {
            $name = basename($setting->user_portrait);

            $material_upload_media_task_model = new MaterialUploadMediaTaskModel();
            $data = (new MaterialFileModel())->getDataByName($name);
            if ($data) {
                $upload_info_list = $material_upload_media_task_model->getUploadLogByFileIdList(
                    [$data->id],
                    $param->platform,
                    $param->media_type,
                    $account_param->account_id
                );
                $upload_info_list = $upload_info_list->keyBy('file_id')->toArray();
                if ($upload_info_list) {
                    $setting->user_portrait = $upload_info_list[$data->id]->url;
                } else {
                    $data = (array)$data;

                    $insert = [];
                    $insert['platform'] = $param->platform;
                    $insert['media_type'] = $param->media_type;
                    $insert['account_id'] = $account_param->account_id;
                    $insert['material_id'] = $data['material_id'];
                    $insert['file_type'] = $data['file_type'];
                    $insert['file_id'] = $data['id'];
                    $insert['filename'] = $data['filename'];
                    $insert['uploader'] = $param->creator;
                    $insert['signature'] = $data['signature'];
                    $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
                    $media_file_info = $this->uploadIcon(
                        new MaterialFileParam($data),
                        $account_param->account_id
                    );
                    if (!$media_file_info['id']) {
                        throw new AppException('上传icon失败');
                    }
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    $material_upload_media_task_model->add(new MaterialUploadMediaTaskParam($insert));
                    $setting->user_portrait = $media_file_url;
                }
            } else {
                throw new AppException('找不到icon信息：' . $name);
            }
        }

        if ($other_setting->subject == BaiduEnum::SUBJECT_PAGE_URL) {
            $other_setting->lp_url = $other_setting->getPageMap($account_param->account_id)->online_url ?? '';
        }

        $creative_model = new CreativeFeedModel();
        if ($param->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            $ccp = new CreativeCreateParam([
                'adgroup_feed_id' => $param->ad2_id,
                'materialstyle' => in_array($other_setting->subject, [BaiduEnum::SUBJECT_SITE_URL, BaiduEnum::SUBJECT_PAGE_URL]) ? BaiduEnum::MATERIAL_STYLE_PRO_URL : BaiduEnum::MATERIAL_STYLE_PRO_DOWNLOAD,
                'pause' => false,
                'idea_type' => BaiduEnum::IDEA_TYPE_PROGRAM,
                'creative_feed_name' => '程序化创意' . time()
            ]);

            $program_creative = new ProgramMaterialParam([
                'brand' => $setting->brand,
                'subtitle' => $setting->subtitle,
                'user_portrait' => $setting->user_portrait,
                'url' => $this->getCompleteUrl(
                    $other_setting->subject,
                    $param->platform,
                    $param->agent_id,
                    $param->site_id,
                    $other_setting->lp_url,
                    $other_setting->lp_mode
                )
            ]);
            $program_creative->setElements(new ElementsParam(array_merge(
                [
                    'titles' => $param->word_list,
                ],
                $this->makeProgramCreative($param, $account_param, $material_media_id_map)
            )));

            // 线索
            if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                if ((int)$other_setting->url_type === 2) {
                    $program_creative->setIdeaPluginGroup(new IdeaPluginGroupParam([
                        'wechat_mini_program_name' => $param->getSiteConfig()->sdk_ext['asset_app_name'] ?? '',
                        'wechat_mini_program_id' => $param->getSiteConfig()->sdk_ext['mini_game_original_id'] ?? '',
                        'wechat_page_path' => $param->getSiteConfig()->ext['mini_game_tracking_parameter'] ?? '',
                        'wechat_page_version_type' => $param->getSiteConfig()->sdk_ext['mini_game_version_type'] ?? '',
                    ]));
                }
            }

            $program_creative->setMonitorInfo([
                'monitor_url' => $param->getSiteConfig()->action_track_url,
                'exposure_url' => $param->getSiteConfig()->display_track_url
            ]);

            $ccp->setMaterial($program_creative);

            try {
                $resp = $creative_model->addCreativeFeed(
                    $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name, [$ccp->addCreativeFeedRequestBody()]
                );
                $ad3_ids = array_column($resp, 'creativeFeedId');
            } catch (Throwable $e) {
                if (strpos($e->getMessage(), '封面url为：https://feed-image.') !== false) {
                    if ($ccp->material['elements']['horizontalVideos'] ?? []) {
                        $horizontal_videos = [];
                        foreach ($ccp->material['elements']['horizontalVideos'] as $m) {
                            $m_array = explode(' ', $m);
                            if (strpos($e->getMessage(), $m_array[1]) === false) {
                                $horizontal_videos[] = $m;
                            }
                        }
                        $ccp->material['elements']['horizontalVideos'] = $horizontal_videos;
                    }

                    if ($ccp->material['elements']['verticalVideos'] ?? []) {
                        $vertical_videos = [];
                        foreach ($ccp->material['elements']['verticalVideos'] as $m) {
                            $m_array = explode(' ', $m);
                            if (strpos($e->getMessage(), $m_array[1]) === false) {
                                $vertical_videos[] = $m;
                            }
                        }
                        $ccp->material['elements']['verticalVideos'] = $vertical_videos;
                    }
                    $resp = $creative_model->addCreativeFeed(
                        $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name, [$ccp->addCreativeFeedRequestBody()]
                    );
                    return array_column($resp, 'creativeFeedId');
                }
                throw $e;
            }
        } else {
            $custom_creative_list = [];
            foreach ($param->creative_list as $key => $val) {
                if (isset($val['cover_info'])) {
                    $material_file_name = basename($val['video_info']['url']);
                } else {
                    $material_file_name = basename($val['image_info']['url']);
                }
                $ccp = new CreativeCreateParam(array_merge([
                    'adgroup_feed_id' => $param->ad2_id,
                    'materialstyle' => $this->getMaterialStyle($other_setting, $val),
                    'pause' => false,
                    'idea_type' => BaiduEnum::IDEA_TYPE_CUSTOM,
                    'creative_feed_name' => "{$material_file_name}_{$key}"
                ]));
                if (isset($val['cover_info'])) {
                    $media_material_video_data = $material_media_id_map[$val['video_info']['id']];
                    $media_material_cover_data = $material_media_id_map[$val['cover_info']['id']];
                    $material_data = [
                        'videoid' => $media_material_video_data['id'],
                        'poster' => $media_material_cover_data['url'],
                        'pictures' => [["image" => $media_material_cover_data['url']]],
                    ];
                    if (
                        ($val['video_info']['width'] == 720 && $val['video_info']['height'] == 1280) &&
                        ($val['cover_info']['width'] == 1140 && $val['cover_info']['height'] == 640)
                    ) {
                        $vertical_cover_id = $this->getVerticalVideoCoverInfo(
                            $val['video_info']['url'],
                            $param->platform,
                            $account_param,
                            $param->creator
                        );
                        $material_data['horizontal_cover'] = $material_data['poster'];
                        $material_data['poster'] = $vertical_cover_id;
                    }
                } else {
                    $material_data = [
                        'pictures' => [["image" => $material_media_id_map[$val['image_info']['id']]['url']]],
                    ];
                }
                $custom_creative = new CustomMaterialParam(array_merge([
                    'title' => $val['title'],
                    'brand' => $setting->brand,
                    'subtitle' => $setting->subtitle,
                    'user_portrait' => $setting->user_portrait,
                    'url' => $this->getCompleteUrl(
                        $other_setting->subject,
                        $param->platform,
                        $param->agent_id,
                        $param->site_id,
                        $other_setting->lp_url,
                        $other_setting->lp_mode
                    )
                ], $material_data));

                // 线索
                if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                    if ((int)$other_setting->url_type === 2) {
                        $custom_creative->setIdeaPluginGroup(new IdeaPluginGroupParam([
                            'wechat_mini_program_name' => $param->getSiteConfig()->sdk_ext['asset_app_name'] ?? '',
                            'wechat_mini_program_id' => $param->getSiteConfig()->sdk_ext['mini_game_original_id'] ?? '',
                            'wechat_page_path' => $param->getSiteConfig()->ext['mini_game_tracking_parameter'] ?? '',
                            'wechat_page_version_type' => $param->getSiteConfig()->sdk_ext['mini_game_version_type'] ?? '',
                        ]));
                    }
                }

                $custom_creative->setMonitorInfo([
                    'monitor_url' => $param->getSiteConfig()->action_track_url,
                    'exposure_url' => $param->getSiteConfig()->display_track_url
                ]);

                $ccp->setMaterial($custom_creative);
                $custom_creative_list[] = $ccp->addCreativeFeedRequestBody();
            }

            $resp = $creative_model->addCreativeFeed(
                $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name, $custom_creative_list
            );
            $ad3_ids = array_column($resp, 'creativeFeedId');
        }

        return $ad3_ids;
    }

    /**
     * 获取定向包的名字
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        return '';
    }

    /**
     * 补充定向包信息
     * @param array $targeting_info
     * @param bool $is_return
     */
    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
    }

    /**
     * 获取广告计划可用的定向包列表
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        return [];
    }

    /**
     * 根据ids获取人群包内容
     * @param array $ids
     * @param int $status
     * @return mixed
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        $model = new OdsBaiduAudienceCrowdLogModel();
        return $model->getListByAudienceIds($ids, $status);
    }

    /**
     * 获取计划行为徕卡
     * @param array $condition
     * @return mixed
     */
    public function getAdActionList(array $condition)
    {
        $tt_ad_log_model = new OdsBaiduAdLogModel();
        $list = $tt_ad_log_model->getAdList($condition['ad_name'] ?? '');
        $return_list = [];
        foreach ($list as $key => $value) {
            $return_list[] = [
                'ad_id' => $value->ad_id,
                'ad_name' => $value->ad_name,
                'keywords' => $value->keywords,
                'keywords_extend' => $value->keywords_extend,
            ];

        }
        return $return_list;
    }

    /**
     * 获取计划兴趣徕卡
     * @param array $condition
     * @return mixed
     */
    public function getAdInterestList(array $condition)
    {
        $tt_ad_log_model = new OdsBaiduAdLogModel();
        $list = $tt_ad_log_model->getAdInterestList($condition['ad_name'] ?? '');
        $return_list = [];
        foreach ($list as $key => $value) {
            $return_list[] = [
                'ad_id' => $value->ad_id,
                'ad_name' => $value->ad_name,
                'intent_tag' => $value->intent_tag,
                'intent_intension' => $value->intent_intension,
            ];

        }
        return $return_list;
    }

    /**
     * 获取计划徕卡行为id转词汇
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        return [];
    }

    /**
     * 获取计划徕卡兴趣id转词汇
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        return [];
    }

    /**
     * 获取流量包列表
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        return [];
    }

    /**
     * 推送人群包
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        $media_account_model = new MediaAccountModel();
        $audience_account_id_list[] = $target_account_ids[0];
        $audience_account_id_map = $media_account_model->getDataInAccountIds(
            $audience_account_id_list,
            MediaType::BAIDU
        )->keyBy('account_id');

        if (!$audience_account_id_map) {
            throw new AppException('找不到' . implode(',', $audience_account_id_list) . '的账号信息');
        }

        $ext_audience_model = new ExtAudienceModel();
        $share_model = new ShareModel();
        foreach ($need_push_audience_list_data as $audience_key => $audience_data) {
            try {
                $account_id_data = $audience_account_id_map[$audience_data->account_id];
                if (!$account_id_data) {
                    throw new AppException("找不到{$audience_data->account_id}的信息");
                }
                $username = $account_id_data->company;
                $password = $account_id_data->access_token;
                $token = $account_id_data->refresh_token;
                $target = $account_id_data->account_name;

                $ext_audience_model->updateAudienceDsp(
                    $username,
                    $password,
                    $token,
                    $target,
                    $audience_data->account_id,
                    $audience_data->custom_audience_id,
                    'feedcpc,fc,feedgd,jinnang,pinzhuan'
                );

                $share_model->saveSharingBatchDr(
                    $username,
                    $password,
                    $token,
                    $target,
                    [$audience_data->custom_audience_id],
                    $target_account_ids
                );

            } catch (Throwable $e) {
                throw new AppException("人群包{$audience_data->custom_audience_id}上传错误:" . $e->getMessage());
            }
        }
        return [];
    }

    /**
     * 修改广告计划-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告计划的开关-返回修改的广告计划id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改广告创意的开关-返回修改的广告创意id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 创建一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 拓展一个人群包-返回一个人群包id
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        return [];
    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        if (3 === $data['ad_level']) {
            //百度三级
            $http_model = new CreativeFeedModel();
            $baidu_ad_model = new OdsBaiduCreativeLogModel();
            $baidu_his_log_model = new OdsBaiduCreativeHisLogModel();
            $ad_id_type = 'creative_id';
            $ad_name_type = 'title';
            $pause_field = 'pause';
            $switch_close_value = 'CREATIVE_STATUS_DISABLE';
            $switch_open_value = 'CREATIVE_STATUS_ENABLE';
            $request_name = 'creativeFeedId';
        } elseif (2 === $data['ad_level']) {
            //百度二级
            $http_model = new AdGroupFeedModel();
            $ad_id_type = 'ad_id';
            $ad_name_type = 'ad_name';
            $baidu_ad_model = new OdsBaiduAdLogModel();
            $baidu_his_log_model = new OdsBaiduADHisLogModel();
            $pause_field = 'ad_pause';
            $switch_close_value = 'AD_STATUS_DISABLE';
            $switch_open_value = 'AD_STATUS_ENABLE';
            $request_name = 'adgroupFeedId';
        } else {
            //百度一级
            $http_model = new CampaignFeedModel();
            $ad_id_type = 'campaign_id';
            $ad_name_type = 'campaign_name';
            $baidu_ad_model = new OdsBaiduCampaignLogModel();
            $baidu_his_log_model = new OdsBaiduCampaignHisLogModel();
            $pause_field = 'campaign_pause';
            $switch_close_value = 'CAMPAIGN_STATUS_DISABLE';
            $switch_open_value = 'CAMPAIGN_STATUS_ENABLE';
            $request_name = 'campaignFeedId';
        }

        $result = $baidu_ad_model->getEditRequestInfoById($ad_format_target);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }

        //存储到操作日志的单条数据
        $single_record_operate = [];

        if (0 === $data['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = 2;
            $pause = true;
            $edit_detail = '关闭';
            $return_opt_status = $switch_close_value;
        } else {
            $single_record_operate['edit_type'] = 1;
            $pause = false;
            $edit_detail = '开启';
            $return_opt_status = $switch_open_value;
        }

        //将所有account_id放在一起，去重
        $account_ids = [];
        $all_ad_ids = [];
        foreach ($result as $value) {
            $account_ids[] = $value->account_id;
            $all_ad_ids[] = $value->$ad_id_type;
        }
        unset($value);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        //要直接update到数据库的数据
        $update_switch = [];

        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //有权限操作的广告id集合
        $permission_ad_ids = [];
        //把account_id相同的放入同一个数组
        foreach ($access_token as $item) {
            $ad_ids = [];
            $multi_request_data = [];
            $baidu_header = [];
            $baidu_header['target'] = $item->account_name;
            $baidu_header['password'] = $item->access_token;
            $baidu_header['username'] = $item->company;
            $baidu_header['token'] = $item->refresh_token;
            //access_token和account_id找到对应关系
            foreach ($result as $k => $v) {
                if ((int)$v->account_id === (int)$item->account_id) {
                    //保存有权限的ad_id
                    $permission_ad_ids[] = (int)$v->$ad_id_type;
                    //一次批量请求的所有ad_id
                    $ad_ids[] = (int)$v->$ad_id_type;

                    $baidu_body = [];
                    $baidu_body['pause'] = $pause;
                    if (3 === $data['ad_level']) {
                        $baidu_body['creativeFeedId'] = (int)$v->creative_id;
                        $baidu_body['adgroupFeedId'] = (int)$v->ad_id;
                    } elseif (2 === $data['ad_level']) {
                        $baidu_body['adgroupFeedId'] = (int)$v->ad_id;
                        $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
                    } else {
                        $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
                    }
                    $multi_request_data[] = $baidu_body;

                }
            }
            unset($v);
            //请求百度修改开关状态
            try {
                $http_model->updateFeed($baidu_header, $multi_request_data);
                //操作成功
                $success_ad_ids = array_merge($success_ad_ids, $ad_ids);
                $update_switch[$item->account_id] = $success_ad_ids;
            } catch (AppException $e) {
                $failed_info = json_decode($e->getMessage(), true);
                //批量请求成功+失败时
                $success = [];
                //以下逻辑这么复杂， 完全是因为百度太奇葩..
                if (is_array($failed_info) &&
                    ('success with failures' === $failed_info['header']['desc'] || 'failure' === $failed_info['header']['desc']) &&
                    is_array($failed_info['header']['failures'])) {
                    //部分成功的把成功的广告哦ID提取出来
                    if ('success with failures' === $failed_info['header']['desc'] &&
                        is_array($failed_info['body']['data']) &&
                        !empty($failed_info['body']['data'])) {

                        foreach ($failed_info['body']['data'] as $value) {
                            if (isset($value[$request_name])) {
                                $success[] = $value[$request_name];
                            }
                        }
                        $success_ad_ids = array_merge($success_ad_ids, $success);
                    }

                    //access_token错误， 就算是批量也只有一个failures信息，特殊处理（很折磨
                    if (count($failed_info['header']['failures']) === 1) {
                        $err_msg = $failed_info['header']['failures'][0]['message'];
                        $ad_ids = array_diff($ad_ids, $success);
                        foreach ($ad_ids as $one_ad_id) {
                            $failed_ad_ids[$one_ad_id] = $err_msg;
                        }
                    }

                    //将百度错误信息和广告ID对应
                    $ad_ids = array_diff($ad_ids, $success);
                    foreach ($ad_ids as $one_ad_id) {
                        foreach ($failed_info['header']['failures'] as $value) {
                            if (false !== strpos($value['message'], $one_ad_id)) {
                                $failed_ad_ids[$one_ad_id] = $value['message'];
                            }
                        }
                    }
                } else {
                    //其他异常
                    $err_msg = $e->getMessage();
                    $ad_ids = array_diff($ad_ids, $success);
                    foreach ($ad_ids as $one_ad_id) {
                        $failed_ad_ids[$one_ad_id] = $err_msg;
                    }
                }
            }
        }
        unset($item);

        if ($update_switch) {
            $baidu_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                $pause_field,
                (int)$pause,
                $single_record_operate['insert_time']
            );
        }
        //无权限的账号
        $not_permission = array_diff($all_ad_ids, $permission_ad_ids);
        //将发起请求之后失败或成功的ad_id的其它信息对应出来
        foreach ($result as $v) {
            //处理成功的
            if ($success_ad_ids) {
                foreach ($success_ad_ids as $ad_id) {
                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 1;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = $return_opt_status;
                    $single_record_operate['ad_name'] = $v->$ad_name_type;
                    $single_record_operate['edit_detail'] = $edit_detail . '成功';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($ad_id);
            }

            //处理失败的
            if ($failed_ad_ids) {
                foreach ($failed_ad_ids as $ad_id => $err_info) {
                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 2;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                    $single_record_operate['ad_name'] = $v->$ad_name_type;
                    $single_return_data['message'] = $err_info;
                    $single_return_data['value'] = 0;
                    $single_record_operate['edit_detail'] = $edit_detail . '失败, 错误信息：' . $err_info;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($err_info);
            }

            //处理没有权限的
            if ($not_permission) {
                foreach ($not_permission as $ad_id) {
                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 2;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                    $single_record_operate['ad_name'] = $v->$ad_name_type;
                    $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                    $single_return_data['value'] = 0;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($ad_id);
            }
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表一级广告
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisFirstClass(array $data)
    {
        $baidu_model = new OdsBaiduCampaignLogModel();
        $baidu_his_log_model = new OdsBaiduCampaignHisLogModel();
        $account_info = $baidu_model->getAccountIdAndPlatform($data);
        if (!$account_info) {
            throw new AppException('修改失败');
        }
        $baidu_http_model = new CampaignFeedModel();
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$account_info->account_id], null, $leader_permission);
        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 1;
        $record_operate['media_type'] = MediaType::BAIDU;
        $record_operate['editor_id'] = $data['editor_id'];
        $record_operate['editor_name'] = $data['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $record_operate['edit_type'] = ADAnalysisModel::CHANGE_FIRST_CLASS_AD_NAME;
        $record_operate['platform'] = $account_info->platform;
        $record_operate['account_id'] = (int)$account_info->account_id;
        $record_operate['ad_id'] = $return_data['ad_id'] = (int)$data['ad_id'];
        $record_operate['ad_name'] = $return_data['value'] = $account_info->campaign_name;
        $record_operate['status'] = $return_data['status'] = 2;

        if ($access_token->isNotEmpty()) {
            $ad_info = [];
            $access_token_info = $access_token->pop();

            $baidu_header = [];
            $baidu_header['target'] = $access_token_info->account_name;
            $baidu_header['password'] = $access_token_info->access_token;
            $baidu_header['username'] = $access_token_info->company;
            $baidu_header['token'] = $access_token_info->refresh_token;

            $baidu_body['campaignFeedId'] = $record_operate['ad_id'];
            $baidu_body['campaignFeedName'] = $data['ad_group_name'];
            try {
                $baidu_http_model->updateFeed($baidu_header, [$baidu_body]);
                //修改成功
                $return_data['message'] = 'success';
                $record_operate['ad_name'] = $return_data['value'] = $data['ad_group_name'];
                $record_operate['status'] = $return_data['status'] = 1;
                $record_operate['edit_detail'] = "百度广告一级名称由[ {$account_info->campaign_name} ] " .
                    "修改为 [ {$data['ad_group_name']} ]";
                $ad_info[$account_info->account_id] = [$data['ad_id']];
            } catch (AppException $e) {
                $err_msg = json_decode($e->getMessage(), true);
                $response = is_array($err_msg) && isset($err_msg['header']['failures'][0]['message']) ?
                    $err_msg['header']['failures'][0]['message'] : '媒体端错误';
                //修改失败
                $record_operate['edit_detail'] = $return_data['message'] = '修改失败，错误原因：' . $response;
            }

            if ($ad_info) {
                $baidu_his_log_model->getLatestByADInfoAndInsert(
                    $ad_info,
                    'campaign_name',
                    $data['ad_group_name'],
                    $record_operate['insert_time']
                );
            }
        } else {
            //没有权限操作
            $return_data['message'] = $record_operate['edit_detail'] = '当前用户无操作权限';
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [$return_data];
    }

    public function updateADAnalysisCampaignSchedule(array $data, array $ad_format_target)
    {
        return $this->multiUpdateADAnalysis(MediaType::BAIDU, $data, $ad_format_target);
    }

    private function multiUpdateADAnalysis(int $media_type, array $data, array $ad_format_target)
    {
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //避免循环查库，手动来组合广告数据
        if (isset($data['ad_name'])) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SECOND_CLASS_AD_NAME;
            $single_record_operate['ad_level'] = 2;
            $update_value = $data['ad_name'];
            $edit_detail = '广告二级名';
            $http_model = new AdGroupFeedModel();
            $ad_model = new OdsBaiduAdLogModel();
            $baidu_his_log_model = new OdsBaiduADHisLogModel();
            $ad_id_field = 'ad_id';
            $update_field = $ad_name_field = 'ad_name';
        } elseif (isset($data['schedule_time'])) {
            //修改schedule_time
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SCHEDULE_TIME;
            $single_record_operate['ad_level'] = 1;
            $update_value = Helpers::formatBaiduScheduleFromBinaryToJson($data['schedule_time']);
            $update_field = 'campaign_schedule';
            $edit_detail = '广告投放时段';
            $http_model = new CampaignFeedModel();
            $ad_model = new OdsBaiduCampaignLogModel();
            $baidu_his_log_model = new OdsBaiduCampaignHisLogModel();
            $ad_id_field = 'campaign_id';
            $ad_name_field = 'campaign_name';
        } else {
            throw new AppException('参数错误');
        }

        $account_info = $ad_model->getEditRequestInfoById($ad_format_target);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        //将所有account_id放在一起，去重
        $account_ids = [];
        foreach ($account_info as $value) {
            $account_ids[] = $value->account_id;
        }
        unset($value);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_token = [];
        $account_name = [];
        $company = [];
        $refresh_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
            $account_name[$v->account_id] = $v->account_name;
            $company[$v->account_id] = $v->company;
            $refresh_token[$v->account_id] = $v->refresh_token;
        }
        unset($v);

        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //要直接update到数据库的数据
        $update = [];
        //access_token和account_id找到对应关系
        foreach ($account_info as $v) {
            if (!isset($access_token[$v->account_id])) {
                //没有操作权限的
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->$ad_id_field;
                $single_record_operate['ad_name'] = $v->$ad_name_field;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            $baidu_header = [];
            $baidu_header['target'] = $account_name[$v->account_id];
            $baidu_header['password'] = $access_token[$v->account_id];
            $baidu_header['username'] = $company[$v->account_id];
            $baidu_header['token'] = $refresh_token[$v->account_id];
            $baidu_body = [];
            if (isset($data['ad_name'])) {
                $baidu_body['adgroupFeedName'] = $update_value;
                $baidu_body['adgroupFeedId'] = (int)$v->ad_id;
                $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
            } else {
                $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
                $baidu_body['schedule'] = $update_value;
            }

            //请求百度修改广告二级名
            try {
//                var_dump($baidu_header, [$baidu_body]);return [];
                $http_model->updateFeed($baidu_header, [$baidu_body]);
                //操作成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $data['ad_name'] ?? $data['schedule_time'];
                $update[$v->account_id][] = $v->$ad_id_field;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $edit_detail . "修改成功";
                $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->campaign_name;
            } catch (AppException $e) {
                $err_msg = json_decode($e->getMessage(), true);
                $response = is_array($err_msg) && isset($err_msg['header']['failures'][0]['message']) ?
                    $err_msg['header']['failures'][0]['message'] : '媒体端错误';
                //操作失败
                $single_return_data['message'] = $response;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_record_operate['edit_detail'] = $edit_detail . "修改失败，错误信息：" . $response;
                $single_record_operate['ad_name'] = $v->$ad_name_field;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_field;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update) {
            $baidu_his_log_model->getLatestByADInfoAndInsert(
                $update,
                $update_field,
                $update_value,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表二级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        return $this->multiUpdateADAnalysis($media_type, $data, $ad_format_target);
//        //存储到操作日志的单条数据
//        $single_record_operate = [];
//        //避免循环查库，手动来组合广告数据
//        if (isset($data['ad_name'])) {
//            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
//            $single_record_operate['edit_type'] = 6;
//            $single_record_operate['ad_level'] = 2;
//            $update_value = $data['ad_name'];
//            $edit_detail = '广告二级名';
//            $http_model = new AdGroupFeedModel();
//            $ad_model = new OdsBaiduAdLogModel();
//            $baidu_his_log_model = new OdsBaiduADHisLogModel();
//            $ad_id_field = 'ad_id';
//            $update_field = $ad_name_field = 'ad_name';
//        } else {
//            //TODO::先屏蔽掉百度投放时段编辑,之后打开注释即可
//            throw new AppException('暂不支持百度投放时段修改');
//            //修改schedule_time
////            $single_record_operate['edit_type'] = 4;
////            $single_record_operate['ad_level'] = 1;
////            $update_value = Helpers::formatBaiduScheduleFromBinaryToJson($data['schedule_time']);
////            $update_field = 'campaign_schedule';
////            $edit_detail = '广告投放时段';
////            $http_model = new CampaignFeedModel();
////            $ad_model = new OdsBaiduCampaignLogModel();
////            $ad_id_field = 'campaign_id';
////            $ad_name_field = 'campaign_name';
//        }
//
//        $account_info = $ad_model->getEditRequestInfoById($ad_format_target);
//        if ($account_info->isEmpty()) {
//            throw new AppException('修改失败');
//        }
//        //将所有account_id放在一起，去重
//        $account_ids = [];
//        foreach ($account_info as $value) {
//            $account_ids[] = $value->account_id;
//        }
//        unset($value);
//        $account_ids = array_flip(array_flip($account_ids));
//        //获取access_token
//        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
//        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
//            null, $leader_permission);
//
//        $access_token = [];
//        $account_name = [];
//        $company = [];
//        $refresh_token = [];
//        foreach ($access_token_info as $v) {
//            $access_token[$v->account_id] = $v->access_token;
//            $account_name[$v->account_id] = $v->account_name;
//            $company[$v->account_id] = $v->company;
//            $refresh_token[$v->account_id] = $v->refresh_token;
//        }
//        unset($v);
//
//        $single_record_operate['media_type'] = MediaType::BAIDU;
//        $single_record_operate['editor_id'] = $data['editor_id'];
//        $single_record_operate['editor_name'] = $data['editor_name'];
//        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
//        //存储到操作日志的所有数据
//        $record_operate = [];
//        //返回前端的单条数据
//        $single_return_data = [];
//        //返回到前端的数据
//        $return_data = [];
//        //要直接update到数据库的数据
//        $update = [];
//        //access_token和account_id找到对应关系
//        foreach ($account_info as $v) {
//            if (!isset($access_token[$v->account_id])) {
//                //没有操作权限的
//                $single_record_operate['platform'] = $v->platform;
//                $single_record_operate['account_id'] = (int)$v->account_id;
//                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = $v->$ad_id_field;
//                $single_record_operate['ad_name'] = $v->$ad_name_field;
//                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
//                $single_return_data['value'] = 0;
//                $single_record_operate['status'] = $single_return_data['status'] = 2;
//                $record_operate[] = $single_record_operate;
//                $return_data[] = $single_return_data;
//            }
//            $baidu_header = [];
//            $baidu_header['target'] = $account_name[$v->account_id];
//            $baidu_header['password'] = $access_token[$v->account_id];
//            $baidu_header['username'] = $company[$v->account_id];
//            $baidu_header['token'] = $refresh_token[$v->account_id];
//            $baidu_body = [];
//            if (isset($data['ad_name'])) {
//                $baidu_body['adgroupFeedName'] = $update_value;
//                $baidu_body['adgroupFeedId'] = (int)$v->ad_id;
//                $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
//            } else {
//                $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
//                $baidu_body['schedule'] = $update_value;
//            }
//
//            //请求百度修改广告二级名
//            try {
//                $http_model->updateFeed($baidu_header, [$baidu_body]);
//                //操作成功
//                $single_return_data['message'] = 'success';
//                $single_return_data['value'] = $data['ad_name'] ?? $data['schedule_time'];
//                $update[$v->account_id][] = $v->$ad_id_field;
//                $single_record_operate['status'] = $single_return_data['status'] = 1;
//                $single_record_operate['edit_detail'] = $edit_detail . "修改成功";
//                $single_record_operate['ad_name'] = $data['ad_name'] ?? $v->campaign_name;
//            } catch (AppException $e) {
//                $err_msg = json_decode($e->getMessage(), true);
//                $response = is_array($err_msg) && isset($err_msg['header']['failures'][0]['message']) ?
//                    $err_msg['header']['failures'][0]['message'] : '媒体端错误';
//                //操作失败
//                $single_return_data['message'] = $response;
//                $single_return_data['value'] = 0;
//                $single_record_operate['status'] = $single_return_data['status'] = 2;
//                $single_record_operate['edit_detail'] = $edit_detail . "修改失败，错误信息：" . $response;
//                $single_record_operate['ad_name'] = $v->$ad_name_field;
//            }
//            $single_record_operate['account_id'] = (int)$v->account_id;
//            $single_record_operate['platform'] = $v->platform;
//            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v->$ad_id_field;
//
//            $record_operate[] = $single_record_operate;
//            $return_data[] = $single_return_data;
//
//        }
//        unset($v);
//
//        if ($update) {
//            $baidu_his_log_model->getLatestByADInfoAndInsert(
//                $update,
//                $update_field,
//                $update_value,
//                $single_record_operate['insert_time']
//            );
//        }
//
//        //记录操作日志
//        (new ADAnalysisOperateLogModel())->add($record_operate);
//
//        return $return_data;
    }

    /**
     * 修改基本报表百度深度转化出价（兼容工厂抽象类二次调用）
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->updateADAnalysisBudgetOrBid($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 修改基本报表预算和出价
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
//        $update_field = 'budget' === $update_field ? 'campaign_budget' : 'ocpc_bid';
        if ('budget' === $update_field || 'ad1_budget' === $update_field) {
            $update_field = 'campaign_budget';
        } elseif ('deep_bid' === $update_field) {
            $update_field = 'deep_ocpc_bid';
        } elseif ('cpa_bid' === $update_field) {
            $update_field = 'ocpc_bid';
        } else {
            throw new AppException('参数错误');
        }
        //存储到操作日志的单条数据
        $single_record_operate = [];
        if ('campaign_budget' === $update_field) {
            $ad_model = new OdsBaiduCampaignLogModel();
            $baidu_his_log_model = new OdsBaiduCampaignHisLogModel();
            $ad_id_field = 'campaign_id';
            $ad_name_field = 'campaign_name';
            $single_record_operate['ad_level'] = 1;
            $http_model = new CampaignFeedModel();
        } else {
            $ad_model = new OdsBaiduADLogModel();
            $baidu_his_log_model = new OdsBaiduADHisLogModel();
            $ad_id_field = 'ad_id';
            $ad_name_field = 'ad_name';
            $single_record_operate['ad_level'] = 2;
            $http_model = new AdGroupFeedModel();
        }

        $account_info = $ad_model->getEditRequestInfoById($ad_format_target, $update_field);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        $all_ids = [];
        foreach ($account_info as $item) {
            $account_ids[] = $item->account_id;
            $all_ids[] = $item->$ad_id_field;
        }
        unset($item);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_token = [];
        $account_name = [];
        $company = [];
        $refresh_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
            $account_name[$v->account_id] = $v->account_name;
            $company[$v->account_id] = $v->company;
            $refresh_token[$v->account_id] = $v->refresh_token;
        }
        unset($v);

        $single_record_operate['media_type'] = MediaType::BAIDU;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        //9.修改定向 10.深度优化/转化出价
//        $single_record_operate['edit_type'] = 3;
//        $request_type = 'campaign_budget';
//        $opt_cn_name = '预算';
        if ('ocpc_bid' === $update_field) {
            $request_type = 'ocpc_bid';
            $single_record_operate['edit_type'] = 8;
            $opt_cn_name = '百度ocpc出价';
        } elseif ('campaign_budget' === $update_field) {
            $single_record_operate['edit_type'] = 3;
            $request_type = 'campaign_budget';
            $opt_cn_name = '预算';
        } else {
            $single_record_operate['edit_type'] = 10;
            $request_type = 'deep_ocpc_bid';
            $opt_cn_name = '百度深度转化出价';
        }

        //有操作权限的ad_id集合
        $permission_ad_ids = [];
        //百度出价bid_type为1（暂时禁止修改）的ad_id集合
        $bid_type_suspend_ids = [];
        //存储所有需要定时修改的数据
        $timing_execute_data = [];
        //即时修改到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_token[$v->account_id])) {
                continue;
            }
            //记录有权限的ad_id
            $permission_ad_ids[] = $v->$ad_id_field;
            //保存原始值
            $original_value = $v->$update_field;
            if (1 === intval($data['change_type'])) {
                $v->$update_field = $data['change_value'];
            } elseif (2 === intval($data['change_type'])) {
                $v->$update_field += number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } elseif (3 === intval($data['change_type'])) {
                $v->$update_field -= number_format($v->$update_field * ($data['change_value'] / 100),
                    2, '.', '');
            } else {
                throw new AppException('参数错误');
            }

            $baidu_header = [];
            $baidu_header['target'] = $account_name[$v->account_id];
            $baidu_header['password'] = $access_token[$v->account_id];
            $baidu_header['username'] = $company[$v->account_id];
            $baidu_header['token'] = $refresh_token[$v->account_id];
            $baidu_body = [];
            $mq_data = [];
            if ('ocpc_bid' === $update_field || 'deep_ocpc_bid' === $update_field) {
                if (3 !== (int)$v->ad_bidtype) {
                    $bid_type_suspend_ids[] = $v->ad_id;
                    continue;
                }
                $baidu_body['bidtype'] = 3;
                $baidu_body['ocpc']['appTransId'] = (int)$v->appTransId;
                $baidu_body['ocpc']['transFrom'] = (int)$v->transFrom;
                if ('ocpc_bid' === $update_field) {
                    $mq_data['ocpc']['ocpcBid'] = $baidu_body['ocpc']['ocpcBid'] = (float)$v->$update_field;
                    $baidu_body['ocpc']['deepOcpcBid'] = (float)$v->deep_ocpc_bid;
                } else {
                    $baidu_body['ocpc']['ocpcBid'] = (float)$v->ocpc_bid;
                    $mq_data['ocpc']['deepOcpcBid'] = $baidu_body['ocpc']['deepOcpcBid'] = (float)$v->$update_field;
                }
                $baidu_body['ocpc']['LpUrl'] = $v->LpUrl;
                $baidu_body['ocpc']['transType'] = (int)$v->transType;
                $baidu_body['ocpc']['ocpcLevel'] = (int)$v->ocpcLevel;
                $baidu_body['ocpc']['payMode'] = (int)$v->payMode;
                if (1 === $baidu_body['ocpc']['payMode']) {
                    $baidu_body['ocpc']['isSkipStageOne'] = (int)$v->isSkipStageOne ? true : false;
                }
                $baidu_body['ocpc']['optimizeDeepTrans'] = (int)$v->optimizeDeepTrans ? true : false;
                $baidu_body['ocpc']['deepTransType'] = (int)$v->deepTransType;
                $mq_data['adgroupFeedId'] = $baidu_body['adgroupFeedId'] = (int)$v->ad_id;
                $mq_data['campaignFeedId'] = $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
            } else {
                $mq_data['campaignFeedId'] = $baidu_body['campaignFeedId'] = (int)$v->campaign_id;
                $mq_data['budget'] = $baidu_body['budget'] = (float)$v->$update_field;
            }

            if (1 === intval($data['execute_type'])) {
                //即时修改，直接请求百度修改
                try {
                    $http_model->updateFeed($baidu_header, [$baidu_body]);
                    //修改成功
                    $update_data[$v->account_id][] = $v->$ad_id_field;
                    $single_return_data['message'] = 'success';
                    $single_return_data['value'] = (string)$v->$update_field;
                    $single_record_operate['status'] = $single_return_data['status'] = 1;
                    $single_record_operate['edit_detail'] = $opt_cn_name . '修改成功，由[' . $original_value .
                        ']修改为[' . $single_return_data['value'] . ']';
                } catch (AppException $e) {
                    //修改失败
                    $err_msg = json_decode($e->getMessage(), true);
                    $response = is_array($err_msg) && isset($err_msg['header']['failures'][0]['message']) ?
                        $err_msg['header']['failures'][0]['message'] : '媒体端错误';
                    $single_return_data['message'] = $response;
                    $single_return_data['value'] = 0;
                    $single_record_operate['status'] = $single_return_data['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name . "修改失败，错误信息：" . $response;
                }
            } elseif (0 === intval($data['execute_type'])) {
                //定时修改，将请求数据汇总之后放入RabbitMQ
                $single_return_data['status'] = $single_record_operate['status'] = 1;
                $single_return_data['value'] = $original_value;
                $single_return_data['message'] = $single_record_operate['edit_detail'] =
                    $opt_cn_name . '修改准备' . $data['execute_time'] . '定时执行，由[' . $original_value .
                    ']修改为[' . (string)$v->$update_field . ']';

                //$request_data存入session信息,access_token消费队列时重新查
                $request_data['account_id'] = $v->account_id;
                $request_data['baidu_body'] = $mq_data;
                $request_data['editor_id'] = $single_record_operate['editor_id'];
                $request_data['editor_name'] = $single_record_operate['editor_name'];
                $request_data['media_type'] = MediaType::BAIDU;
                $request_data['request_type'] = $request_type;
                $request_data['platform'] = $v->platform;
                $timing_execute_data[] = $request_data;
            }
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->$ad_id_field;
            $single_record_operate['ad_name'] = $v->$ad_name_field;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;

        }
        unset($v);

        if ($update_data) {
            //组合要update的sql
            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, 2 ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field + $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set = "CAST ( ( $update_field - $update_field * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } else {
                throw new AppException('参数错误');
            }

            $baidu_his_log_model->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        if ($timing_execute_data) {
            $time = strtotime($data['execute_time']) - time();
            if ('ocpc_bid' === $request_type || 'deep_ocpc_bid' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBidTask($timing_execute_data, $time);
            } elseif ('campaign_budget' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBudgetTask($timing_execute_data, $time);
            }
        }

        //无权限的ad2_id
        $not_permission = array_diff($all_ids, $permission_ad_ids);
        //处理没有权限的
        if ($not_permission || $bid_type_suspend_ids) {
            foreach ($account_info as $v) {
                if ($not_permission) {
                    foreach ($not_permission as $ad_id) {
                        if ((int)$ad_id !== (int)$v->$ad_id_field) {
                            continue;
                        }
                        $single_return_data['status'] = $single_record_operate['status'] = 2;
                        $single_return_data['value'] = 0;
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                        $single_record_operate['ad_name'] = $v->$ad_name_field;
                        $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                        $record_operate[] = $single_record_operate;
                        $return_data[] = $single_return_data;
                    }
                    unset($ad_id);
                }

                if ($bid_type_suspend_ids) {
                    foreach ($bid_type_suspend_ids as $ad_id) {
                        if ((int)$ad_id !== (int)$v->$ad_id_field) {
                            continue;
                        }
                        $single_return_data['status'] = $single_record_operate['status'] = 2;
                        $single_return_data['value'] = 0;
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                        $single_record_operate['ad_name'] = $v->$ad_name_field;
                        $single_return_data['message'] = $single_record_operate['edit_detail'] =
                            '第二阶段目标转化成本暂时不可修改';
                        $record_operate[] = $single_record_operate;
                        $return_data[] = $single_return_data;
                    }
                    unset($ad_id);
                }
            }
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 处理基本报表定时修改出价预算RabbitMQ
     * @param int $media_type
     * @param array $data
     * @return mixed|void
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $update_field = $data[0]['request_type'];
        if ('ocpc_bid' === $update_field || 'deep_ocpc_bid' === $update_field) {
            $ad_model = new OdsBaiduADLogModel();
            $http_model = new AdGroupFeedModel();
            $ad_id_field = 'ad_id';
            $ad_name_field = 'ad_name';
            $single_record_operate['ad_level'] = 2;
            $single_record_operate['edit_type'] = 8;
            $request_name = 'ocpcBid';
            if ('deep_ocpc_bid' === $update_field) {
                $single_record_operate['edit_type'] = 10;
                $request_name = 'deepOcpcBid';
            }

            $request_ad_id = 'adgroupFeedId';
        } else {
            $ad_model = new OdsBaiduCampaignLogModel();
            $http_model = new CampaignFeedModel();
            $ad_id_field = 'campaign_id';
            $ad_name_field = 'campaign_name';
            $single_record_operate['ad_level'] = 1;
            $single_record_operate['edit_type'] = 3;
            $request_ad_id = 'campaignFeedId';
            $request_name = 'budget';
        }

        $condition = [];
        $account_ids = [];
        foreach ($data as $v) {
            $condition[$v['platform']][] = $v['baidu_body'][$request_ad_id];
            $account_ids[] = $v['account_id'];
        }
        unset($v);
        if (!$condition || !$account_ids) {
            return;
        }
        $ad_info = $ad_model->getEditRequestInfoById($condition, $update_field);
        $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids);
        if ($ad_info->isEmpty() || $access_token_info->isEmpty()) {
            return;
        }
        //整合access_token方便后面使用
        $access_token = [];
        $account_name = [];
        $company = [];
        $refresh_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
            $account_name[$v->account_id] = $v->account_name;
            $company[$v->account_id] = $v->company;
            $refresh_token[$v->account_id] = $v->refresh_token;
        }
        unset($v);

        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        $opt_cn_name = ['ocpc_bid' => '百度ocpc出价', 'campaign_budget' => '预算', 'deep_ocpc_bid' => '百度深度转化出价'];
        foreach ($ad_info as $value) {
            foreach ($data as $v) {
                if ((int)$value->$ad_id_field !== (int)$v['baidu_body'][$request_ad_id] ||
                    (int)$value->account_id !== (int)$v['account_id']) {
                    continue;
                }
                $baidu_header = [];
                $baidu_header['target'] = $account_name[$v['account_id']];
                $baidu_header['password'] = $access_token[$v['account_id']];
                $baidu_header['username'] = $company[$v['account_id']];
                $baidu_header['token'] = $refresh_token[$v['account_id']];

                if ('ocpc_bid' === $update_field || 'deep_ocpc_bid' === $update_field) {
                    $baidu_body = $v['baidu_body'];
                    $baidu_body['bidtype'] = 3;
                    $baidu_body['ocpc']['appTransId'] = (int)$value->appTransId;
                    $baidu_body['ocpc']['transFrom'] = (int)$value->transFrom;
                    if ('ocpc_bid' === $update_field) {
                        $baidu_body['ocpc']['deepOcpcBid'] = (float)$value->deep_ocpc_bid;
                    } else {
                        $baidu_body['ocpc']['ocpcBid'] = (float)$value->ocpc_bid;
                    }
                    $baidu_body['ocpc']['LpUrl'] = $value->LpUrl;
                    $baidu_body['ocpc']['transType'] = (int)$value->transType;
                    $baidu_body['ocpc']['ocpcLevel'] = (int)$value->ocpcLevel;
                    $baidu_body['ocpc']['payMode'] = (int)$value->payMode;
                    if (1 === $baidu_body['ocpc']['payMode']) {
                        $baidu_body['ocpc']['isSkipStageOne'] = (int)$value->isSkipStageOne ? true : false;
                    }
                    $baidu_body['ocpc']['optimizeDeepTrans'] = (int)$value->optimizeDeepTrans ? true : false;
                    $baidu_body['ocpc']['deepTransType'] = (int)$value->deepTransType;
                } else {
                    $baidu_body = $v['baidu_body'];
                }

                try {
                    $http_model->updateFeed($baidu_header, [$baidu_body]);
                    //修改成功
                    $update_data[$v['account_id']][] = $value->$ad_id_field;
                    $single_record_operate['status'] = 1;
                    if ('ocpc_bid' === $update_field || 'deep_ocpc_bid' === $update_field) {
                        $new_value = $v['baidu_body']['ocpc'][$request_name];
                    } else {
                        $new_value = $v['baidu_body'][$request_name];
                    }
                    $single_record_operate['edit_detail'] = $opt_cn_name[$update_field] . '定时修改成功，由[' .
                        $value->$update_field . ']修改为[' . $new_value . ']';
                } catch (AppException $e) {
                    $err_msg = json_decode($e->getMessage(), true);
                    $response = is_array($err_msg) && isset($err_msg['header']['failures'][0]['message']) ?
                        $err_msg['header']['failures'][0]['message'] : '媒体端错误';
                    //修改失败
                    $single_record_operate['status'] = 2;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$update_field] . "定时修改失败，错误信息：" .
                        $response;
                }
                $single_record_operate['platform'] = $value->platform;
                $single_record_operate['account_id'] = (int)$value->account_id;
                $single_record_operate['ad_id'] = (int)$value->$ad_id_field;
                $single_record_operate['ad_name'] = $value->$ad_name_field;
                $single_record_operate['editor_id'] = $v['editor_id'];
                $single_record_operate['editor_name'] = $v['editor_name'];
                $record_operate[] = $single_record_operate;
            }
            unset($v);
        }
        unset($value);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
    }

    /**
     * 修改基本报表定向
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        $access_token = [];
        $account_name = [];
        $company = [];
        $refresh_token = [];
        $platform_for_targeting = '';
        $account_ids_for_targeting = [];
        foreach ($account_info as $item) {
            $access_token[$item->account_id] = $item->access_token;
            $account_name[$item->account_id] = $item->account_name;
            $company[$item->account_id] = $item->company;
            $refresh_token[$item->account_id] = $item->refresh_token;
            if (!$platform_for_targeting) {
                $platform_for_targeting = $item->platform;
            }
            $account_ids_for_targeting[] = $item->account_id;
        }
        unset($item);

        $baidu_body = [];
        if ($platform_for_targeting) {
            //推送人群包并获取定向包内容
            $baidu_body['audience'] = (new ADServingLogic())->pushAudienceForAnalysis(
                (int)$data['targeting_id'],
                (string)$platform_for_targeting,
                $account_ids_for_targeting
            )['targeting'];
        }
        $baidu_http_model = new AdGroupFeedModel();
        //单条操作记录
        $single_record_operate = [];

        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价,
        // 9: 修改定向
        $single_record_operate['edit_type'] = 9;
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = MediaType::BAIDU;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        $single_return_data['value'] = 0;
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($data['ad_info'] as $v) {
            $single_record_operate['ad_name'] = $v['ad_name'];
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_record_operate['platform'] = $v['platform'];
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (int)$v['ad_id'];

            if (!isset($access_token[$v['account_id']])) {
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            $baidu_header = [];
            $baidu_header['target'] = $account_name[$v['account_id']];
            $baidu_header['password'] = $access_token[$v['account_id']];
            $baidu_header['username'] = $company[$v['account_id']];
            $baidu_header['token'] = $refresh_token[$v['account_id']];

            $baidu_body['adgroupFeedId'] = (int)$v['ad_id'];
//            $baidu_body['campaignFeedId'] = (int)$v['campaign_id'];
            try {
                $baidu_http_model->updateFeed($baidu_header, [$baidu_body]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = "定向修改成功";
            } catch (AppException $e) {
                $err_msg = json_decode($e->getMessage(), true);
                $response = is_array($err_msg) && isset($err_msg['header']['failures'][0]['message']) ?
                    $err_msg['header']['failures'][0]['message'] : '媒体端错误';
                //修改失败
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $response;
                $single_record_operate['edit_detail'] = '定向修改失败，错误信息：' . $response;
            }

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    public function getTargetingDataByAD2($company, $audience_md5)
    {
        throw new AppException('百度暂时不支持，复制计划定向');
    }

    public function addADTaskByApi(array $input)
    {
        return 0;
    }

    public function updateAndroidChannelPackage(array $input)
    {
        $ext_info = (new OdsMediaSDKModel())->getDataByGame($input['platform'], $input['media_type'], $input['game_id']);
        if (!$ext_info || !isset($ext_info->ext)) {
            throw new AppException('无可更新数据');
        }
        $sdk_ext = json_decode($ext_info->ext, true);
        $job = [
            'packageId' => $input['channel_package_id'],
            'packageLink' => $input['package_link'],
            'appLogo' => EnvConfig::DOMAIN . $sdk_ext['app_logo'],
            'appScreenshots' => array_map(function ($uri) {
                return EnvConfig::DOMAIN . $uri;
            }, $sdk_ext['app_screen_shots']),
            'appIntroduce' => $sdk_ext['app_introduce'],
            'category' => $sdk_ext['category'],
            'developerName' => $sdk_ext['developer_name'],
            'privacyProtectionAgreement' => true
        ];

        $job_list[] = $job;
        $unique_id = md5(json_encode($job_list));
        $body = [
            'uniqueId' => $unique_id,
            'jobList' => $job_list,
        ];

        $access_token_info = (new MediaAccountModel())->getDataByAccountId($input['account_id']);

        if (!$access_token_info) {
            throw new AppException('无相关账号信息');
        }

        return (new AppCenterJobModel())->update(
            $access_token_info->account_name,
            $access_token_info->access_token,
            $access_token_info->company,
            $access_token_info->refresh_token,
            $body
        );

    }

    /**
     * 基本报表删除二级或三级广告
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return array|mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        if (3 === $data['ad_level']) {
            //百度三级
            $http_model = new CreativeFeedModel();
            $baidu_ad_model = new OdsBaiduCreativeLogModel();
            $baidu_his_log_model = new OdsBaiduCreativeHisLogModel();
            $ad_id_type = 'creative_id';
            $ad_name_type = 'title';
            $pause_field = 'pause';
            $delete_value = 'CREATIVE_STATUS_DELETED';
            $request_name = 'creativeFeedId';
            $method = 'deleteCreativeFeed';
        } elseif (2 === $data['ad_level']) {
            //百度二级
            $http_model = new AdGroupFeedModel();
            $baidu_ad_model = new OdsBaiduAdLogModel();
            $baidu_his_log_model = new OdsBaiduADHisLogModel();
            $ad_id_type = 'ad_id';
            $ad_name_type = 'ad_name';
            $pause_field = 'ad_pause';
            $delete_value = 'AD_STATUS_DELETED';
            $request_name = 'adgroupFeedId';
            $method = 'deleteAdgroupFeed';
        } else {
            //百度一级
            throw new AppException('百度一级广告禁止删除');
        }

        $result = $baidu_ad_model->getEditRequestInfoById($ad_format_target);
        if ($result->isEmpty()) {
            throw new AppException('修改失败');
        }

        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;

        //将所有account_id放在一起，去重
        $account_ids = [];
//        $all_ad_ids = [];
        foreach ($result as $value) {
            $account_ids[] = $value->account_id;
//            $all_ad_ids[] = $value->$ad_id_type;
        }
        unset($value);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);
        //要直接update到数据库的数据
        $update_switch = [];

        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        $access_token = [];
        $account_name = [];
        $company = [];
        $refresh_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
            $account_name[$v->account_id] = $v->account_name;
            $company[$v->account_id] = $v->company;
            $refresh_token[$v->account_id] = $v->refresh_token;
        }
        unset($v);
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];
        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        $data_set = [];
        $ad_ids = [];
        $not_permission = [];
        foreach ($result as $k => $v) {
            $data_set[$v->account_id][] = $v->$ad_id_type;
        }
        unset($v);
        foreach ($data_set as $account_id => $ad_id_set) {
            if (!isset($access_token[$account_id])) {
                $not_permission[] = array_merge($ad_id_set, $not_permission);
            }
            $baidu_header = [];
            $baidu_header['target'] = $account_name[$account_id];
            $baidu_header['password'] = $access_token[$account_id];
            $baidu_header['username'] = $company[$account_id];
            $baidu_header['token'] = $refresh_token[$account_id];
            $baidu_body = [];
            if (3 === $data['ad_level']) {
                $baidu_body['creativeFeedIds'] = $ad_id_set;
            } else {
                $baidu_body['adgroupFeedIds'] = $ad_id_set;
            }
            //请求百度
            try {
                $http_model->$method($baidu_header, $baidu_body);
                //操作成功
                $success_ad_ids = array_merge($success_ad_ids, $ad_id_set);
                $update_switch[$account_id] = $success_ad_ids;
            } catch (AppException $e) {
                $failed_info = json_decode($e->getMessage(), true);
                //批量请求成功+失败时
                $success = [];
                //以下逻辑这么复杂，完全是因为百度是xx
                if (is_array($failed_info) &&
                    ('success with failures' === $failed_info['header']['desc'] ||
                        'failure' === $failed_info['header']['desc']) &&
                    is_array($failed_info['header']['failures'])) {
                    //部分成功的时候，把成功的广告ID提取出来
                    if ('success with failures' === $failed_info['header']['desc'] &&
                        is_array($failed_info['body']['data']) &&
                        !empty($failed_info['body']['data'])) {

                        foreach ($failed_info['body']['data'] as $value) {
                            if (isset($value[$request_name])) {
                                $success[] = $value[$request_name];
                            }
                        }
                        $success_ad_ids = array_merge($success_ad_ids, $success);
                    }

                    //access_token之类的错误，就算是批量也只有一个failures信息，特殊处理（很折磨
                    if (count($failed_info['header']['failures']) === 1) {
                        $err_msg = $failed_info['header']['failures'][0]['message'];
                        $ad_ids = array_diff($ad_id_set, $success);
                        foreach ($ad_ids as $one_ad_id) {
                            $failed_ad_ids[$one_ad_id] = $err_msg;
                        }
                        unset($one_ad_id);
                    } else {
                        //将百度错误信息和广告ID对应
                        $ad_ids = array_diff($ad_id_set, $success);
                        foreach ($ad_ids as $one_ad_id) {
                            foreach ($failed_info['header']['failures'] as $value) {
                                if (false !== strpos($value['message'], $one_ad_id)) {
                                    $failed_ad_ids[$one_ad_id] = $value['message'];
                                }
                            }
                            unset($value);
                        }
                        unset($one_ad_id);
                    }
                } else {
                    //其他异常
                    $err_msg = $e->getMessage();
                    $ad_ids = array_diff($ad_ids, $success);
                    foreach ($ad_ids as $one_ad_id) {
                        $failed_ad_ids[$one_ad_id] = $err_msg;
                    }
                }
            }
        }

        if ($update_switch) {
            $baidu_his_log_model->getLatestByADInfoAndInsert(
                $update_switch,
                $pause_field,
                -100,
                $single_record_operate['insert_time']
            );
        }
        //将发起请求之后失败或成功的ad_id的其它信息对应出来
        foreach ($result as $v) {
            if (isset($data['risk_operate'][MediaType::BAIDU][$v->$ad_id_type]) &&
                $data['risk_operate'][MediaType::BAIDU][$v->$ad_id_type]) {
                $risk_operate = '本次为风险操作';
                $risk_code = 1;
            } else {
                $risk_operate = '';
                $risk_code = 0;
            }
            //处理成功的
            if ($success_ad_ids && in_array($v->$ad_id_type, $success_ad_ids)) {
//                foreach ($success_ad_ids as $ad_id) {
//                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
//                        continue;
//                    }
                $single_return_data['status'] = $single_record_operate['status'] = $risk_code ? ADAnalysisModel::SUCCESS_WITH_RISK : ADAnalysisModel::SUCCESS;
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$v->$ad_id_type;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $delete_value;
                $single_record_operate['ad_name'] = $v->$ad_name_type;
                $single_record_operate['edit_detail'] = '删除百度' . $data['ad_level'] . '级广告成功 ' . $risk_operate;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
//                }
//                unset($ad_id);
            }

            //处理失败的
            if ($failed_ad_ids) {
                foreach ($failed_ad_ids as $ad_id => $err_info) {
                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = $risk_code ? ADAnalysisModel::FAILED_WITH_RISK : ADAnalysisModel::FAILED;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                    $single_record_operate['ad_name'] = $v->$ad_name_type;
                    $single_return_data['message'] = $err_info;
                    $single_return_data['value'] = 0;
                    $single_record_operate['edit_detail'] = '删除百度' . $data['ad_level'] . '级广告失败, ' . $risk_operate . ' 错误信息：' . $err_info;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($err_info);
            }

            //处理没有权限的
            if ($not_permission) {
                foreach ($not_permission as $ad_id) {
                    if ((int)$ad_id !== (int)$v->$ad_id_type) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (int)$ad_id;
                    $single_record_operate['ad_name'] = $v->$ad_name_type;
                    $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                    $single_return_data['value'] = 0;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($ad_id);
            }
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表-批量修改一级预算
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed|void
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        return $this->updateADAnalysisBudgetOrBid($media_type, $data, $ad_format_target, $update_field);
    }

    /**
     * 基本报表更新账号预算
     * @param int $media_type
     * @param array $input
     * @param array $ad_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $input, array $ad_format_target, string $update_field)
    {
        $baidu_model = new OdsBaiduAccountLogModel();
        $column = ['account_id', 'account_budget', 'platform'];
        $account_info = $baidu_model->getAccountInfoInAccountIds($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_token = [];
        $account_name = [];
        $company = [];
        $refresh_token = [];
        foreach ($access_token_info as $v) {
            $access_token[$v->account_id] = $v->access_token;
            $account_name[$v->account_id] = $v->account_name;
            $company[$v->account_id] = $v->company;
            $refresh_token[$v->account_id] = $v->refresh_token;
        }
        unset($v);
        $http_model = new AccountFeedServiceModel();
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $decimal = 2;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_token[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
                $single_record_operate['ad_id'] = 0;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            try {
                $original_value = $v->account_budget;
                if (1 === intval($input['change_type'])) {
                    $v->account_budget = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->account_budget += number_format($v->account_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->account_budget -= number_format($v->account_budget * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }
                $baidu_header = [];
                $baidu_header['target'] = $account_name[$v->account_id];
                $baidu_header['password'] = $access_token[$v->account_id];
                $baidu_header['username'] = $company[$v->account_id];
                $baidu_header['token'] = $refresh_token[$v->account_id];
                $baidu_body = [];
                $baidu_body['budget'] = $v->account_budget;

                //发起请求
                $http_model->updateFeed($baidu_header, $baidu_body);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->account_budget;
                $single_record_operate['ad_name'] = '';
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = 0 === (int)$v->account_budget ? '无限' : $v->account_budget;
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = "百度账号预算由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";

            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = '百度账号预算修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = '';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_return_data['account_id'] = $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = 0;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    public function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    public function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    public function getTargetAudienceEstimate(array $target, $account_info)
    {

    }

    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    /**
     * 获取基木鱼落地页
     * @param $account_id
     * @param string $key_word
     * @return Collection
     */
    public function getLandingPageList($account_id, $key_word = '')
    {
        return (new OdsBaiduLandingPageLogModel)->getList($account_id, $key_word);
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var  ADOtherSettingContentParam $baidu_other_setting */
        $baidu_other_setting = $param->other_setting;

        if (!$param->site_id) {
            if ($baidu_other_setting->subject == 3) {
                $param->setSiteConfigProp('app_android_channel_package_id', SiteModel::CHANNEL_PACKAGE_WAITING);
            } else {
                $param->setSiteConfigProp('app_android_channel_package_id', SiteModel::CHANNEL_PACKAGE_NO_NEED);
            }
        }
    }

    /**
     * 获取渠道组
     * @param ADTaskParam $param
     * @return int
     */
    public function getAgentGroup(ADTaskParam $param)
    {
        if ((int)$param->site_config['plat_id'] === PlatId::MINI) {
            return AgentGroup::BAIDU_MINI_GAME;
        } else {
            return AgentGroup::BAIDU;
        }
    }

    /**
     * @inheritDoc
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if ($other_setting->subject == 1 && $other_setting->lp_url_content) {
            // 切广告位
            $option = [json_encode([
                'game_id' => $param->getSiteConfig()->game_id,
                'game_pack' => $param->getSiteConfig()->game_pack,
                'adid' => $other_setting->lp_url_content,
            ])];

            PlatformAD::create($param->platform)->switchAD(
                [$param->site_id],
                $option,
                date('Y-m-d H:i:s'),
                0,
                0,
                $param->getSiteConfig()->convert_source_type == ConvertSourceType::API ? 0 : 1,
                $param->creator);
        }

        if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
            if ((int)$other_setting->url_type === 2) {
                if (!$param->getSiteConfig()->ext['mini_game_tracking_parameter']) {
                    throw new AppException("未设置微信小程序路径，不能使用微信小程序调用类型 , 请检查");
                }
            }
        }
    }

    /**
     * 新建广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {

        $ad_task_model = new ADTaskModel();

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告二级
        if (!$param->ad2_id) {
            try {
                $param->ad2_id = $this->createAD2($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // $this->bindRecommendReasons($param,$account_param);

        // 新建广告三级
        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 智投广告处理
        if ($param->ad1_id) {
            try {
                $this->projectBind($param, $account_param);
            } catch (Throwable $e) {
                throw new AppException('绑定智投项目错误:' . $e->getMessage());
            }
        }
    }

    /**
     * 智投项目绑定
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return bool
     */
    public function projectBind(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if (!$param->ad1_id) return false;
        if (!$other_setting->project_switch) return false;

        $project_info = $other_setting->getProjectMap($account_param->account_id);
        $project_data = $this->getProjectData($account_param, [$project_info->project_id]);
        $campaign_ids = $project_data[0]['campaignFeedIds'];

        if (!in_array($param->ad1_id, $campaign_ids)) {
            array_push($campaign_ids, $param->ad1_id);
            $this->updateProject($account_param, $project_info->project_id, ['campaignFeedIds' => $campaign_ids]);
        }
        return true;
    }

    /**
     * 推荐理由
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function bindRecommendReasons(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        // 推荐理由
        if ($setting->binded_reasons_status) {
            (new CreativeFeedModel())->bindRecommendReasons(
                $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name, $param->ad2_id, $setting->binded_reasons
            );
        }
    }

    /**
     * @inheritDoc
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushFlowPackage() method.
    }

    /**
     * @inheritDoc
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $need_push_audience_list_data = $this->getNeedUploadAudienceList(
            $param->targeting->getAllAudienceIdList(),
            $param->account_id,
            $param->platform,
            $param->company
        );
        if ($need_push_audience_list_data->isNotEmpty()) {
            $audience_account_id_list = $need_push_audience_list_data->pluck('account_id')->toArray();
            if ($audience_account_id_list) {
                $this->pushAudience(
                    $audience_account_id_list,
                    $need_push_audience_list_data->toArray(),
                    [$param->account_id]
                );
            } else {
                throw new AppException("人群包从未上传过");
            }
        }
    }

    /**
     * 检查打包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        // 如果是落地页和安卓渠道包的任务，需要解析
        if (in_array($other_setting->subject, [1, 3]) && !is_numeric($param->getSiteConfig()->app_android_channel_package_id)) {

            $param->setSiteConfigProp('add_number', 1);

            $delay_queue_data = array_merge($param->toArray(), [
                'ad_task_list_id' => $param->id,
                'ldy_url' => '',
                'sdk_ext' => $param->getSiteConfig()->sdk_ext,
                'subject' => $other_setting->subject,
                'lp_mode' => $other_setting->lp_mode,
                'app_android_channel_package_id' => $param->getSiteConfig()->app_android_channel_package_id,
                'site_config' => $param->getSiteConfig()->toArray(),
                'plat_id' => $param->getSiteConfig()->plat_id,
                'download_url' => $param->getSiteConfig()->download_url,
                'game_type' => $param->getSiteConfig()->game_type
            ]);

            if ($other_setting->subject == 1 && $other_setting->lp_mode == 0) {
                $complete_url = $other_setting->lp_url;
                if ($complete_url) {
                    $delay_queue_data['ldy_url'] = PlatformAD::create($param->platform)->getSiteLDYUrl(
                        $param->agent_id,
                        $param->site_id,
                        $other_setting->lp_url
                    );
                }
            }

            if ($param->getSiteConfig()->app_android_channel_package_id != SiteModel::CHANNEL_PACKAGE_NO_NEED) {
                (new SiteMQLogic)->produceBaiduTask($delay_queue_data, 10);

                throw new AppException('解析落地页或渠道包未完成', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
            }
        }

        // 如果是需要等待打包的任务，需要走解析包链接
        if ($param->is_wait_package == 1) {

            $data = array_merge($param->toArray(), [
                'ad_task_list_id' => $param->id,
                'ldy_url' => '',
                'sdk_ext' => $param->getSiteConfig()->sdk_ext,
                'plat_id' => $param->getSiteConfig()->plat_id,
            ]);

            (new SiteMQLogic)->produceBaiduTask($data, 1);

            throw new AppException('等待打包中', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }

    }

    /**
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return array|mixed
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::BAIDU, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        $result = [];
        foreach ($material_file_list as $material_file) {
            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::BAIDU;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;
            if ($upload_info_list[$material_file->id]) {
                $result[$material_file->id] = [
                    'id' => $upload_info_list[$material_file->id]->media_material_file_id,
                    'url' => $upload_info_list[$material_file->id]->url,
                ];
            } else {
                if ($material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ||
                    $material_file->file_type == MaterialFileModel::FILE_TYPE_COVER) {
                    $media_file_info = $this->uploadImage(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                } else {
                    $media_file_info = $this->uploadVideo(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                }
                if ($media_file_id) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * 是否重启任务
     * @param ADTaskParam $task_param
     * @return bool|mixed
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        $need_restart_message_format_list = [
            'docId not exists'
        ];
        foreach ($need_restart_message_format_list as $message_format) {
            if (strpos($task_param->error_msg, $message_format) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 创意素材处理
     * @param ADTaskParam $param
     * @return mixed|void
     */
    function prepareCreativeList(ADTaskParam $param)
    {
        $material_file_model = new MaterialFileModel();
        $ods_material_file_log_model = new OdsMaterialFileLogModel();

        $ids = $param->getImageCreativeID();

        $images_map = $material_file_model->getListByIds($ids)->keyBy('id');
        $new_images_map = [];

        foreach ($images_map as $file_id => $material_file) {
            if ($material_file->width > $material_file->height) {
                if ($material_file->width == 1140 && $material_file->height == 640) {
                    continue;
                }
                $width = 1140;
                $height = 640;
            } else {
                if ($material_file->width == 640 && $material_file->height == 1140) {
                    continue;
                }
                $width = 640;
                $height = 1140;
            }
            $sub_path = $material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ? EnvConfig::MATERIAL_IMG_DIR_NAME : EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
            $upload_path = SRV_DIR . "/{$access_path}";
            $ext_array = explode('.', $material_file->filename);
            $base_name = $ext_array[0];
            $ext = end($ext_array);
            $ext_name = "{$base_name}_(百度投放扩展){$width}x{$height}.{$ext}";
            $file_data = $material_file_model->getDataByName($ext_name);
            if ($file_data) {
                $new_images_map[$file_id] = (new MaterialFileParam($file_data))->toArray();
            } else {
                $result = Image::thumb(
                    "$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename",
                    "$upload_path/$material_file->platform/$material_file->material_id/$ext_name",
                    '',
                    $width,
                    $height
                );
                if ($result) {
                    $insert_data = [
                        'platform' => $material_file->platform,
                        'media_type' => 0,
                        'material_id' => $material_file->material_id,
                        'file_type' => $material_file->file_type,
                        'filename' => $ext_name,
                        'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material_file->platform/$material_file->material_id/$ext_name",
                        'width' => $width,
                        'height' => $height,
                        'scale' => Math::div($width, $height),
                        'signature' => md5_file("$upload_path/$material_file->platform/$material_file->material_id/$ext_name"),
                        'bitrate' => 0,
                        'size' => filesize("$upload_path/$material_file->platform/$material_file->material_id/$ext_name"),
                        'format' => $material_file->format,
                        'uploader' => $material_file->uploader,
                        'create_time' => time(),
                        'update_time' => time(),
                        'is_del' => 0,
                        'is_ext' => 1
                    ];
                    $insert_data['id'] = $material_file_model->add($insert_data);
                    if (EnvConfig::ENV === 'production' && $insert_data['id']) {
                        $insert_data['insert_time'] = date("Y-m-d H:i:s", $insert_data['create_time']);
                        $insert_data['update_time'] = date("Y-m-d H:i:s", $insert_data['update_time']);
                        unset($insert_data['create_time']);
                        $ods_material_file_log_model->add($insert_data);
                    }
                    $new_images_map[$file_id] = $insert_data;
                } else {
                    throw new AppException('扩展百度图片失败');
                }
            }
        }
        foreach ($param->creative_list as $id => &$creative_info) {
            if (isset($creative_info['cover_info']['id']) && isset($new_images_map[$creative_info['cover_info']['id']])) {
                $creative_info['cover_info'] = [
                    'id' => $new_images_map[$creative_info['cover_info']['id']]['id'],
                    'url' => $new_images_map[$creative_info['cover_info']['id']]['url'],
                    'width' => $new_images_map[$creative_info['cover_info']['id']]['width'],
                    'height' => $new_images_map[$creative_info['cover_info']['id']]['height'],
                ];
            } elseif (isset($creative_info['image_info']['image_list']) && $creative_info['image_info']['image_list']) {
                foreach ($creative_info['image_info']['image_list'] as $image_key => &$image_value) {
                    if (isset($image_value['id']) && isset($new_images_map[$image_value['id']])) {
                        $image_value = [
                            'id' => $new_images_map[$image_value['id']]['id'],
                            'url' => $new_images_map[$image_value['id']]['url'],
                            'width' => $new_images_map[$image_value['id']]['width'],
                            'height' => $new_images_map[$image_value['id']]['height'],
                        ];
                    }
                }
            } elseif (isset($creative_info['image_info']['id']) && isset($new_images_map[$creative_info['image_info']['id']])) {
                $creative_info['image_info'] = [
                    'id' => $new_images_map[$creative_info['image_info']['id']]['id'],
                    'url' => $new_images_map[$creative_info['image_info']['id']]['url'],
                    'width' => $new_images_map[$creative_info['image_info']['id']]['width'],
                    'height' => $new_images_map[$creative_info['image_info']['id']]['height'],
                ];
            }
        }
    }

    /**
     * 获取媒体所有广告状态
     * @return Collection
     */
    public function getDiffMediaStatus(): Collection
    {
        $list = (new OdsBaiduADLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::DIFF_MEDIA_OPT_STATUS_MAP[MediaType::BAIDU][2][$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        $list = (new OdsBaiduADLogModel())->getInventoryList($condition);
        if ($list->isEmpty()) {
            return $list;
        }
        return $list->map(function ($item) use ($keyword) {
            $inventory_type = json_decode($item->flow_types, true);
            $inventory_type_cn = [];
            foreach ($inventory_type as $value) {
                if (is_null($value)) {
                    return false;
                }
                $inventory_type_cn[] = ADFieldsENToCNMap::INVENTORY_TO_CN['BAIDU_INVENTORY-' . $value] ?? $value;
            }
            unset($value);
            $label = implode(',', $inventory_type_cn);

            if ('' === $keyword || ('' !== $keyword && false !== strpos($label, $keyword))) {
                return [
                    'label' => $label,
                    'value' => $item->flow_types,
                ];
            } else {
                return false;
            }
        })->filter()->values();
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        return '';
    }

    public function getTransferSet($data)
    {
        // TODO: Implement getTransferSet() method.
    }

    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if (!$param->convert_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新账号余额信息
     *
     * @param $data
     *
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 获取百度智投项目
     * @param MediaAccountInfoParam $account_param
     * @param array $project_id
     * @return mixed
     */
    public function getProjectData(MediaAccountInfoParam $account_param, $project_id = [])
    {
        return (new ProjectFeedModel())->getProject(
            $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name,
            ['projectFeedId', 'projectFeedName', 'pause', 'status', 'campaignFeedIds'], $project_id
        );
    }

    /**
     * 添加百度智投项目
     * @param MediaAccountInfoParam $account_param
     * @param ProjectParam $param
     * @return mixed
     */
    public function addProject(MediaAccountInfoParam $account_param, ProjectParam $param)
    {
        return (new ProjectFeedModel())->createProject(
            $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name,
            $param
        );
    }

    /**
     * 更新百度智投项目
     * @param MediaAccountInfoParam $account_param
     * @param $project_id
     * @param array $data
     * @return mixed
     */
    public function updateProject(MediaAccountInfoParam $account_param, $project_id, array $data)
    {
        return (new ProjectFeedModel())->updateProject(
            $account_param->company, $account_param->access_token, $account_param->refresh_token, $account_param->account_name,
            $project_id, $data
        );
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
    }

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassScheduleTime() method.
    }

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateSecondClassScheduleTime() method.
    }

    /**
     * 获取媒体广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status(): Collection
    {
        $list = (new OdsBaiduCreativeLogModel())->getStatusList();
        if ($list->isEmpty()) {
            return $list;
        }

        return $list->map(function ($item) {
            return [
                'label' => ADFieldsENToCNMap::DIFF_MEDIA_OPT_STATUS_MAP[MediaType::BAIDU][3][$item] ?? $item,
                'value' => $item,
            ];
        });
    }

    /**
     * 广告一键起量
     *
     * @param $data
     *
     * @return mixed
     */
    public function updateADAnalysisADRaise( $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
}

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
