<?php

namespace App\Service\MediaAD;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\FaceBook\Creative\CreativeModel;
use App\Model\HttpModel\Tiktok\ImageUpload;
use App\Model\HttpModel\Tiktok\VideoUpload;
use App\Model\SqlModel\Zeda\FacebookCreativeFolderLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\MediaAccountInfoParam;
use App\Struct\Input;
use Illuminate\Support\Collection;
use Throwable;

class MediaTiktok extends AbstractMedia
{

    public function getInterestActionCategoryList(Input $input)
    {
        // TODO: Implement getInterestActionCategoryList() method.
    }

    public function getInterestActionKeywordList(Input $input)
    {
        // TODO: Implement getInterestActionKeywordList() method.
    }

    public function getInterestInterestCategoryList(Input $input)
    {
        // TODO: Implement getInterestInterestCategoryList() method.
    }

    public function getInterestInterestKeywordList(Input $input)
    {
        // TODO: Implement getInterestInterestKeywordList() method.
    }

    public function campaignList(Input $input)
    {
        // TODO: Implement campaignList() method.
    }

    public function convertList(Input $input)
    {
        // TODO: Implement convertList() method.
    }

    public function createConvert(ConvertCreateParam $param)
    {
        // TODO: Implement createConvert() method.
    }

    public function createChannelPackage(ChannelPackageParam $param)
    {
        // TODO: Implement createChannelPackage() method.
    }

    public function audiencePackageList(Input $input)
    {
        // TODO: Implement audiencePackageList() method.
    }

    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateAd() method.
    }

    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateADStatus() method.
    }

    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateCreativeStatus() method.
    }

    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement createAudience() method.
    }

    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement expandAudience() method.
    }

    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            if ($param->file_type == 3) {
                $image_file = MaterialFileModel::VIDEO_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
            } elseif ($param->file_type == 4) {
                $image_file = MaterialFileModel::ICON_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
            } else {
                $image_file = MaterialFileModel::IMG_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
            }
            $result = (new ImageUpload())->upload($advertiser_id, $access_token, $param->filename, $image_file, $param->signature);
            return [
                'id' => $result['image_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_file = MaterialFileModel::VIDEO_PATH . DIRECTORY_SEPARATOR . $param->platform . DIRECTORY_SEPARATOR . $param->material_id . DIRECTORY_SEPARATOR . $param->filename;
            $result = (new VideoUpload())->upload($advertiser_id, $access_token, $param->filename, $video_file, $param->signature);
            return [
                'id' => $result[0]['video_id'],
                'url' => ''
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    public function getIndustryList(Input $input)
    {
        // TODO: Implement getIndustryList() method.
    }

    public function getWordById($input, $media_type)
    {
        // TODO: Implement getWordById() method.
    }

    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        // TODO: Implement getAudienceListByIds() method.
    }

    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        // TODO: Implement pushAudience() method.
    }

    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        // TODO: Implement audienceList() method.
    }

    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        // TODO: Implement flowList() method.
    }

    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        // TODO: Implement getNeedUploadAudienceList() method.
    }

    public function getTargetingDataByAD2($company, $audience_md5)
    {
        // TODO: Implement getTargetingDataByAD2() method.
    }

    public function getTargetingName(array $audience_info)
    {
        // TODO: Implement getTargetingName() method.
    }

    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        // TODO: Implement fillTargetingInfo() method.
    }

    public function getAdActionList(array $condition)
    {
        // TODO: Implement getAdActionList() method.
    }

    public function getActionWord(array $data)
    {
        // TODO: Implement getActionWord() method.
    }

    public function getAdInterestList(array $condition)
    {
        // TODO: Implement getAdInterestList() method.
    }

    public function getInterestWord(array $data)
    {
        // TODO: Implement getInterestWord() method.
    }

    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisStatus() method.
    }

    public function updateADAnalysisFirstClass(array $data)
    {
        // TODO: Implement updateADAnalysisFirstClass() method.
    }

    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisSecondClass() method.
    }

    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisBudgetOrBid() method.
    }

    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        // TODO: Implement handleADAnalysisBudgetOrBidMQData() method.
    }

    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        // TODO: Implement updateADAnalysisTargeting() method.
    }

    public function addADTaskByApi(array $input)
    {
        // TODO: Implement addADTaskByApi() method.
    }

    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    public function updateAndroidChannelPackage(array $input)
    {
        // TODO: Implement updateAndroidChannelPackage() method.
    }

    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisDeepBidOrROI() method.
    }

    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement deleteADAnalysisAD() method.
    }

    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassBudget() method.
    }

    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisAccountBudget() method.
    }

    function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    function getTargetAudienceEstimate(array $target, $account_info)
    {
        // TODO: Implement getTargetAudienceEstimate() method.
    }

    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement beforeMakeSite() method.
    }

    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement afterMakeSite() method.
    }

    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        // TODO: Implement createAD() method.
    }

    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushFlowPackage() method.
    }

    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushAudiencePackage() method.
    }

    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement waitDelayPack() method.
    }

    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement getMaterialFileMediaInfo() method.
    }

    function isRestartADTask(ADTaskParam $task_param)
    {
        // TODO: Implement isRestartADTask() method.
    }

    function prepareCreativeList(ADTaskParam $task_param)
    {
        // TODO: Implement prepareCreativeList() method.
    }

    function getAgentGroup(ADTaskParam $task_param)
    {
        // TODO: Implement getAgentGroup() method.
    }

    public function getDiffMediaStatus()
    {
        // TODO: Implement getDiffMediaStatus() method.
    }

    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        // TODO: Implement getDiffMediaInventoryList() method.
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        // TODO: Implement getADName() method.
    }

    public function getTransferSet($data)
    {
        // TODO: Implement getTransferSet() method.
    }

    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if (!$param->convert_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新账号余额信息
     *
     * @param $data
     *
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid( int $media_type, array $data, array $ad_format_target, string $update_field ) {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
}

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateFirstClassScheduleTime() method.
}

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateSecondClassScheduleTime() method.
}

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status() {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    /**
     * 广告一键起量
     *
     * @param $data
     *
     * @return mixed
     */
    public function updateADAnalysisADRaise( $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
}

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}