<?php
/**
 * 百度搜索
 * User: Lin
 * Date: 2021/6/1
 * Time: 12:00
 */

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\BsEnum;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Logic\DSP\SiteMQLogic;
use App\Model\HttpModel\Baidu\Image\ImageServiceModel;
use App\Model\HttpModel\Baidu\ImageManageService\ImageManageServiceModel;
use App\Model\HttpModel\Baidu\VideoUpload\VideoUploadModel;
use App\Model\HttpModel\BaiduSearch\AdgroupImageSegmentBindService\AdgroupImageSegmentBindServiceModel;
use App\Model\HttpModel\BaiduSearch\AdgroupService\AdgroupServiceModel;
use App\Model\HttpModel\BaiduSearch\AdvancedSegmentBindService\AdvancedSegmentBindServiceModel;
use App\Model\HttpModel\BaiduSearch\AppCenterJobService\AppCenterJobModel;
use App\Model\HttpModel\BaiduSearch\CampaignService\CampaignServiceModel;
use App\Model\HttpModel\BaiduSearch\CreativeService\CreativeServiceModel;
use App\Model\HttpModel\BaiduSearch\CrowdBindService\CrowdBindModel;
use \App\Model\HttpModel\BaiduSearch\AdvancedSegmentService\AdvancedSegmentServiceModel;
use App\Model\HttpModel\BaiduSearch\ImageManagementService\ImageManagementServiceModel;
use App\Model\HttpModel\BaiduSearch\ImageSegmentService\ImageSegmentServiceModel;
use App\Model\HttpModel\BaiduSearch\KeywordService\KeywordModel;
use App\Model\HttpModel\BaiduSearch\OcpcService\OcpcModel;
use App\Model\SqlModel\DataMedia\OdsBaiduAudienceGroupModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\CampaignCreateMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\BaiduSearch\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\BaiduSearch\Basics\ADSettingContentParam;
use App\Param\ADServing\BaiduSearch\ADTargetingContentParam;
use App\Param\BaiduSearch\AdvancedSegmentParam;
use App\Param\BaiduSearch\AdCreateParam;
use App\Param\BaiduSearch\CampaignCreateParam;
use App\Param\BaiduSearch\CreativeCreateParam;
use App\Param\BaiduSearch\CrowdBindParam;
use App\Param\BaiduSearch\KeyWordParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Helpers;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use Throwable;

class MediaBaiduSearch extends AbstractMedia
{

    public function getInterestActionCategoryList(Input $input)
    {
        // TODO: Implement getInterestActionCategoryList() method.
    }

    public function getInterestActionKeywordList(Input $input)
    {
        // TODO: Implement getInterestActionKeywordList() method.
    }

    public function getInterestInterestCategoryList(Input $input)
    {
        // TODO: Implement getInterestInterestCategoryList() method.
    }

    public function getInterestInterestKeywordList(Input $input)
    {
        // TODO: Implement getInterestInterestKeywordList() method.
    }

    public function campaignList(Input $input)
    {
        // TODO: Implement campaignList() method.
    }

    /**
     * 已创建好的广告组id
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $campaign_info = (new CampaignCreateMediaLogModel())->getCreateLog(MediaType::BAIDU_SEARCH, $account_param->account_id, $param->ad1_name_text);
        return $campaign_info ? $campaign_info->campaign_id : 0;
    }

    /**
     * 创建计划
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|string
     */
    public function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;

        $campaign_data = [
            'campaign_name' => $param->ad1_name_text,
            'budget' => $setting->budget ?? null,
            'region_target' => $targeting->region_target ?? null,
            'schedule' => $setting->schedule_map ? Helpers::formatBaiduScheduleFromBinaryToJson(implode('', $setting->schedule_map)) : $setting->schedule_map,
            'pause' => $setting->ad1_pause,
            'equipment_type' => $other_setting->bid_prefer,
            'region_price_factor' => $setting->region_price_factor,
            'schedule_price_factors' => $setting->schedule_price_factors,
            'marketing_target_id' => $other_setting->marketing_target_id,
            'business_point_id' => $other_setting->business_point_id,
        ];

        $response_data = (new CampaignServiceModel())->add(
            $account_param->company, $account_param->access_token,
            $account_param->refresh_token, $account_param->account_name,
            new CampaignCreateParam($campaign_data), $other_setting->ad_type);

        $campaign_id = $response_data[0]['campaignId'];
        (new CampaignCreateMediaLogModel())->addCreateLog($param->media_type, $param->account_id, $campaign_id, $param->ad1_name_text);

        return $campaign_id;
    }

    /**
     * 创建单元
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return string
     */
    public function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $ad_data = [
            'campaign_id' => $param->ad1_id,
            'adgroup_name' => $param->ad2_name_text,
            'max_price' => $setting->max_price,
            'pause' => $setting->ad2_pause,
            'negative_words' => $targeting->negative_words ?? null,
            'exact_negative_words' => $targeting->exact_negative_words ?? null,
            'ad_type' => $other_setting->ad_type,
            'segment_recommend_status' => $setting->segment_recommend_status,
            'equipment_type' => $other_setting->bid_prefer,
        ];

        $ad_param = new AdCreateParam($ad_data);

        if ($other_setting->subject == BsEnum::SUBJECT_ANDROID) {
            $ad_param->setAndroidApp($setting->bid_ratio, $param->getSiteConfig()->app_android_channel_package_id);
        }

        if ($other_setting->subject == BsEnum::SUBJECT_IOS) {
            $ad_param->setIosApp($setting->bid_ratio, $param->getSiteConfig()->appid);
        }

        $response_data = (new AdgroupServiceModel())->add(
            $account_param->company, $account_param->access_token,
            $account_param->refresh_token, $account_param->account_name,
            $ad_param, $other_setting->ad_type);

        return $response_data[0]['adgroupId'];
    }

    /**
     * 添加关键词
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return bool
     */
    private function create_keyword(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if (!$targeting->keyword_status) {
            return false;
        }

        //获取已创建的关键词 避免任务重启导致冲突
        $keyword_created = $this->get_created_keyword($account_param, $param->ad2_id);
        $keyword_created = array_flip($keyword_created);

        // 获取关键词出价策略ID
        $strategy_id = $other_setting->getPriceStrategyInfo($param->account_id)->strategy_id ?? 0;

        try {
            $keyword_data = [];
            foreach ($targeting->keyword_map as $item) {
                if (isset($keyword_created[$item['keyword']])) {
                    continue;
                }
                $keyword_info = [
                    'ad_group_id' => $param->ad2_id,
                    'keyword' => $item['keyword'],
                    'price' => $item['price'],
                    'match_type' => $item['match_type'],
                    'phrase_type' => $item['phrase_type'],
                ];
                // 关键词标签
                if ($item['tabs']) {
                    $keyword_info['tabs'] = $item['tabs'];
                }
                if ($strategy_id) {
                    $keyword_info['strategy_id'] = $strategy_id;
                }
                $keyword_data[] = (new KeyWordParam($keyword_info))->toRequest();
            }

            if ($keyword_data) {
                (new KeywordModel())->add($account_param->company, $account_param->access_token,
                    $account_param->refresh_token, $account_param->account_name, $keyword_data);
            }

        } catch (Throwable $e) {
            throw new AppException('添加关键词失败：' . $e->getMessage());
        }

        return true;
    }

    /**
     * 获取已创建的关键词
     * @param MediaAccountInfoParam $account_param
     * @param int $ad_id
     * @return array
     */
    private function get_created_keyword(MediaAccountInfoParam $account_param, int $ad_id)
    {
        $param_filter = [
            'idType' => '5', // 单元维度
            'ids' => [$ad_id],
            'wordFields' => ['keyword'],
        ];
        $response_data = (new KeywordModel())->get($account_param->company, $account_param->access_token,
            $account_param->refresh_token, $account_param->account_name, $param_filter);
        return array_column($response_data, 'keyword');
    }

    /**
     * 人群绑定
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    private function crowedBind(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $crowd_list = $other_setting->getCrowdInfo($param->account_id);

        if (!$other_setting->crowd_status || !$crowd_list) {
            return;
        }
        try {
            $crowd_bind_model = new CrowdBindModel();

            // 查询绑定关系 防止重启冲突
            $bind_resp = $crowd_bind_model->get($account_param->company, $account_param->access_token,
                $account_param->refresh_token, $account_param->account_name, $param->ad1_id);
            $bind_crowd = array_column($bind_resp, 'crowdId');

            $crowed_data = [];
            foreach ($crowd_list as $crowd_info) {
                // 跳过
                if (in_array($crowd_info['crowd_id'], $bind_crowd)) {
                    continue;
                }
                $crowed_info = [
                    'target_type' => 1,
                    'target_id' => $param->ad1_id,
                    'crowd_id' => $crowd_info['crowd_id'],
                    'crowd_price_ratio' => $crowd_info['crowd_price_ratio'] ?? '1.1',
                    'flow_expand_status' => $crowd_info['flow_expand_status'] ?? 'false',
                ];
                $crowed_data[] = (new CrowdBindParam($crowed_info))->toRequest();
            }
            if ($crowed_data) {
                $crowd_bind_model->add($account_param->company, $account_param->access_token,
                    $account_param->refresh_token, $account_param->account_name, $crowed_data);
            }

        } catch (Throwable $e) {
            throw new AppException("人群绑定异常：" . $e->getMessage());
        }
    }

    /**
     * 素材绑定 
     * @param MediaAccountInfoParam $account_param
     * @param int $ad_id
     * @param array $materials
     * @return array
     */
    private function materialBind(MediaAccountInfoParam $account_param, int $ad_id, array $materials)
    {
        if (!$materials) {
            return [];
        }
        try {
            $bind_list = [];
            foreach ($materials as $material_id) {
                $bind_list[] = [
                    'segmentId' => $material_id,
                    'adgroupId' => $ad_id,
                ];
            }
            return (new AdgroupImageSegmentBindServiceModel())->add($account_param->company, $account_param->access_token,
                $account_param->refresh_token, $account_param->account_name, $bind_list);

        } catch (Throwable $e) {
            throw new AppException("素材绑定异常：" . $e->getMessage());
        }
    }

    /**
     * 组件绑定 账号下-单进程
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return array|bool
     */
    private function advancedSegmentBind(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if (!$other_setting->segments_status) {
            return [];
        }

        try {
            $segments_info = $other_setting->getSegmentsInfo($param->account_id);

            $advanced_segment_bind_model = new AdvancedSegmentBindServiceModel();

            foreach ($segments_info as $segment) {

                $segment_id = $segment['segment_id'];

                // 查询绑定关系
                $bind_resp = $advanced_segment_bind_model->get($account_param->company, $account_param->access_token,
                    $account_param->refresh_token, $account_param->account_name,$segment_id);

                $bind_ad2 = array_column($bind_resp[0]['fields'] ?? [],'adgroupId');
                $bind_ids = array_column($bind_resp[0]['fields'] ?? [],'bindId');

                // 已绑定单元 跳过
                if (in_array($param->ad2_id, $bind_ad2)) {
                    continue;
                }

                $bind_list = [];

                foreach($bind_resp[0]['fields'] ?? [] as $segment_info) {
                    $bind_list[] = [
                        'segmentId' => $segment_info['segmentId'],
                        'adgroupId' => $segment_info['adgroupId'],
                        'segmentType' => $segment['segment_type'],
                        'bindSource' => 0, // 单元层级
                        'bindLevel' => 1,
                    ];
                }
                $bind_list[] = [
                    'segmentId' => $segment_id,
                    'adgroupId' => $param->ad2_id,
                    'segmentType' => $segment['segment_type'],
                    'bindSource' => 0, // 单元层级
                    'bindLevel' => 1,
                ];

                // 删除所有绑定
                if($bind_ids) {
                    $advanced_segment_bind_model->del($account_param->company, $account_param->access_token,
                        $account_param->refresh_token, $account_param->account_name, $bind_ids);
                }

                // 批量添加绑定
                $advanced_segment_bind_model->add($account_param->company, $account_param->access_token,
                    $account_param->refresh_token, $account_param->account_name, $bind_list);

            }
            return true;

        } catch (Throwable $e) {
            throw new AppException("组件绑定异常：" . $e->getMessage());
        }
    }

    /**
     * 添加组件
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $segment_map
     * @return mixed
     */
    private function createAdvancedSegment(ADTaskParam $param, MediaAccountInfoParam $account_param, array $segment_map)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $segment_id = [];

        try {
            foreach ($param->creative_list as $index => $creative_info) {
                $audit_content_list = [];

                switch ((int)$other_setting->segment_type) {
                    case 310: // 移动图集
                    case 311: // 计算机图集
                        foreach ($creative_info['image_info']['image_list'] as $key => $image) {
                            $data = [
                                'picUrl' => $segment_map[$image['id']]['url'],
                            ];
                            $audit_content_list['items'][] = $data;
                        }
                        break;
                    case 312: // 移动图文
                        foreach ($creative_info['image_info']['image_list'] as $key => $image) {
                            $data = [
                                'picUrl' => $segment_map[$image['id']]['url'],
                                'picText' => $creative_info['title_list'][$key],
                                'targetUrl' => $other_setting->getPageInfo($param->account_id)->online_url,
                            ];
                            if ($key == 0) $data['targetUrl'] .= '?';  // 保证至少有两条不同
                            $audit_content_list['items'][] = $data;
                        }
                        break;
                    case 313: // 计算机图文
                        foreach ($creative_info['image_info']['image_list'] as $key => $image) {
                            $data = [
                                'rawPicUrl' => $segment_map[$image['id']]['url'],
                                'picUrl' => $segment_map[$image['id']]['url'],
                                'picText' => $creative_info['title_list'][$key],
                                'targetUrl' => $other_setting->getPageInfo($param->account_id)->online_url,
                                'desc' => '',
                            ];
                            if ($key == 0) $data['targetUrl'] .= '?';  // 保证至少有两条不同
                            $audit_content_list['items'][] = $data;
                        }
                        break;
                    case 320: // 移动大图
                        $data = [
                            'picUrl' => $segment_map[$creative_info['image_info']['id']]['url'],
                            'desc' => $creative_info['title'],
                        ];
                        $audit_content_list = $data;
                        break;
                    case 321: // 计算机大图
                        $data = [
                            'picUrl' => $segment_map[$creative_info['image_info']['id']]['url'],
                            'rawPicUrl' => $segment_map[$creative_info['image_info']['id']]['url'],
                            'desc' => $creative_info['title'],
                        ];
                        $audit_content_list = $data;
                        break;
                    case 10001: // 16:9视频
                    case 10002: // 9:16视频
                    case 10003: // 1:1视频
                        // 上传视频封面
                        $picUrl = $this->coverUploadToUrl($account_param, $creative_info['cover_info']['url']);
                        $video_image_map = $other_setting->getVideoImageMap();
                        $data = [
                            'videoId' => $segment_map[$creative_info['video_info']['id']]['id'],
                            'videoUrl' => $segment_map[$creative_info['video_info']['id']]['url'],
                            'videoDesc' => $creative_info['title'],
                            'images' => [
                                $video_image_map['video_image_type'] => [[
                                    'picUrl' => $picUrl,
                                    'imageRatio' => $video_image_map['video_image_radio'],
                                ]],
                            ],
                        ];
                        $audit_content_list['videos'][] = $data;
                        break;
                    default:
                        throw new AppException("未知组件类型");
                }
                $bind_list = (new AdvancedSegmentParam([
                    'source' => 0,
                    'segment_type' => $other_setting->segment_type,
                    'audit_content' => $audit_content_list,
                ]))->toRequest();
                $response_data = (new AdvancedSegmentServiceModel())->add($account_param->company, $account_param->access_token,
                    $account_param->refresh_token, $account_param->account_name, [$bind_list]);
                $segment_id[] = $response_data[0]['segmentId'];
            }
        } catch (Throwable $e) {
            throw new AppException('添加组件异常：' . $e->getMessage());
        }

        return $segment_id;
    }

    /**
     * 封面上传 仅用于视频封面上传
     * @param MediaAccountInfoParam $account_param
     * @param string $url
     * @return mixed
     */
    private function coverUploadToUrl(MediaAccountInfoParam $account_param, string $url)
    {
        $file = file_get_contents($url);
        $images = [['content' => chunk_split(base64_encode($file))]];
        $image_model = new ImageManageServiceModel();
        $result = $image_model->upload(
            $account_param->company, $account_param->access_token,
            $account_param->refresh_token, $account_param->account_name,
            $images);
        return $result[0]['url'];
    }

    /**
     * oCPC出价策略 账号下-单进程
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    private function ocpcBind(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if (!$other_setting->ocpc_status ||
            !$ocpc_id = $other_setting->getOcpcInfo($account_param->account_id)->ocpc_id) {
            return;
        }
        try {
            $ocpc_model = new OcpcModel();
            // 获取当前ocpc生效范围
            $ocpc_list = $ocpc_model->get($account_param->company, $account_param->access_token,
                $account_param->refresh_token, $account_param->account_name, $ocpc_id, 2);
            $scope = $ocpc_list[0]['scope'];
            // 组装修改scope结构
            $newScope = [];
            foreach ($scope as $item) {
                $newScope[] = [
                    'levelId' => $item['levelId'],
                    'level' => $item['level'],
                ];
            }
            $newScope[] = [
                'levelId' => $param->ad1_id,
                'level' => 2,
            ];
            $ocpc_model->updateScope($account_param->company, $account_param->access_token,
                $account_param->refresh_token, $account_param->account_name, $ocpc_id, $newScope);

        } catch (Throwable $e) {
            throw new AppException('绑定ocpc出价策略失败：' . $e->getMessage());
        }
    }

    public function convertList(Input $input)
    {
        // TODO: Implement convertList() method.
    }

    public function createConvert(ConvertCreateParam $param)
    {
        // TODO: Implement createConvert() method.
    }

    public function createChannelPackage(ChannelPackageParam $param)
    {
        $data = (new AppCenterJobModel())->add($param);
        return [
            'app_android_channel_package_id' => "job_id;{$data[0]['jobId']}",
        ];
    }

    public function audiencePackageList(Input $input)
    {
        // TODO: Implement audiencePackageList() method.
    }

    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateAd() method.
    }

    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateADStatus() method.
    }

    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateCreativeStatus() method.
    }

    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement createAudience() method.
    }

    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement expandAudience() method.
    }

    /**
     * 创建创意
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return array
     */
    public function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        // 创建创意
        $creative_data = [
            'campaign_id' => $param->ad1_id,
            'adgroup_id' => $param->ad2_id,
            'title' => $setting->title,
            'description1' => $setting->description1,
            'description2' => $setting->description2,
            'mobile_destination_url' => $other_setting->getPageInfo($param->account_id)->online_url,
            'tabs' => $setting->tabs,
        ];

        // pc端
        if ($other_setting->isPc()) {
            $creative_data['pc_destination_url'] = $other_setting->getPageInfo($param->account_id)->online_url;
        }

        $creative_data = (new CreativeCreateParam($creative_data))->toRequestBody();
        $request_creative_list = [$creative_data];

        $response_data = (new CreativeServiceModel())->add($account_param->company, $account_param->access_token,
            $account_param->refresh_token, $account_param->account_name, $request_creative_list);

        return array_column($response_data, 'creativeId');
    }

    /**
     * 生成物料并绑定单元
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    public function createPicMaterialAndBind(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // 添加素材
        $segment_id = $this->createBasePicMaterial($param, $account_param);
        // 素材绑定
        $this->materialBind($account_param, $param->ad2_id, $segment_id);
    }

    /**
     * 创建基础创意图片素材
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return array
     */
    private function createBasePicMaterial(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        try {
            $segment_ids = [];
            foreach ($param->creative_list as $item) {
                $file = file_get_contents($item['image_info']['url']);
                if (!$file) continue;
                $image = [
                    'srcImageContent' => chunk_split(base64_encode($file)),
                    'segmentType' => 101,
                    'desc' => [$item['title']],
                ];

                $result = (new ImageSegmentServiceModel())->add(
                    $account_param->company, $account_param->access_token,
                    $account_param->refresh_token, $account_param->account_name, [$image]);
                $segment_ids = array_merge($segment_ids, array_column($result, 'segmentId'));
            }
            return $segment_ids;
        } catch (Throwable $e) {
            throw new AppException('创建基础创意图片素材失败：' . $e->getMessage());
        }
    }

    /**
     * 创建组件创意图片素材
     * @param MediaAccountInfoParam $account_param
     * @param array $creative_list
     * @return array
     */
    private function createAdvancedPicMaterial(MediaAccountInfoParam $account_param, array $creative_list)
    {
        try {
            $result = [];
            foreach ($creative_list as $item) {

                // 可以批量 但由于需要映射map，只好轮询
                if ($item['image_info']['image_list']) {
                    foreach ($item['image_info']['image_list'] as $image_info) {
                        $file = file_get_contents($image_info['url']);
                        if (!$file) continue;
                        $image = [
                            'content' => chunk_split(base64_encode($file)),
                        ];
                        $response_data = (new ImageManagementServiceModel())->add($account_param->company, $account_param->access_token,
                            $account_param->refresh_token, $account_param->account_name, [$image]);
                        $result[$image_info['id']]['url'] = $response_data[0]['molaUrl'];
                    }
                } else {
                    $file = file_get_contents($item['image_info']['url']);
                    if (!$file) continue;
                    $image = [
                        'content' => chunk_split(base64_encode($file)),
                    ];
                    $response_data = (new ImageManagementServiceModel())->add($account_param->company, $account_param->access_token,
                        $account_param->refresh_token, $account_param->account_name, [$image]);
                    $result[$item['image_info']['id']]['url'] = $response_data[0]['molaUrl'];
                }
            }
            return $result;

        } catch (Throwable $e) {
            throw new AppException('创建组件创意图片素材失败：' . $e->getMessage());
        }
    }

    /**
     * 上传图片
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $file = file_get_contents($param->url);
            // 这里需要是二维数组
            $images = [['content' => chunk_split(base64_encode($file))]];
            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->company;
            $password = $media_account_info->access_token;
            $target = $media_account_info->account_name;
            $token = $media_account_info->refresh_token;
            $image_model = new ImageManageServiceModel();
            $result = $image_model->upload(
                $username,
                $password,
                $token,
                $target,
                $images);
            return [
                'id' => $result[0]['imageId'],
                'url' => $result[0]['url']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * 上传视频
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file = "$upload_path/$video_path/$platform/$material_id/$file_name";


            $media_account = new MediaAccountModel();
            $media_account_info = $media_account->getDataByAccountId($advertiser_id);
            $username = $media_account_info->company;
            $password = $media_account_info->access_token;
            $token = $media_account_info->refresh_token;
            $target = $media_account_info->account_name;

            $video_model = new VideoUploadModel();
            $result = $video_model->upload(
                $username,
                $password,
                $token,
                $target,
                $file,
                $param->signature,
                ['format' => $param->format, 'source' => 2, 'videoName' => $param->filename]);
            if (!$result) {
                throw new AppException("{$param->filename}上传出错:无返回数据!");
            }
            return [
                'id' => $result[0]['videoId'],
                'url' => $result[0]['url']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    public function getIndustryList(Input $input)
    {
        // TODO: Implement getIndustryList() method.
    }

    public function getWordById($input, $media_type)
    {
        // TODO: Implement getWordById() method.
    }

    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        // TODO: Implement getAudienceListByIds() method.
    }

    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        (new MediaBaidu())->pushAudience($audience_account_id_list, $need_push_audience_list_data, $target_account_ids);
    }

    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        return (new OdsBaiduAudienceGroupModel())->getListByCompanyOfBS($company, $page, $rows, $id, $name);
    }

    public function flowList($name = '',$account_id = '', $page = 1, $row = 15)
    {
        // TODO: Implement flowList() method.
    }

    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        return (new OdsBaiduAudienceGroupModel())->getNeedUploadAudienceList($audience_list, $account_id, $platform, $company);
    }

    public function getTargetingDataByAD2($company, $audience_md5)
    {
        // TODO: Implement getTargetingDataByAD2() method.
    }

    public function getTargetingName(array $audience_info)
    {
        // TODO: Implement getTargetingName() method.
    }

    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        // TODO: Implement fillTargetingInfo() method.
    }

    public function getAdActionList(array $condition)
    {
        // TODO: Implement getAdActionList() method.
    }

    public function getActionWord(array $data)
    {
        // TODO: Implement getActionWord() method.
    }

    public function getAdInterestList(array $condition)
    {
        // TODO: Implement getAdInterestList() method.
    }

    public function getInterestWord(array $data)
    {
        // TODO: Implement getInterestWord() method.
    }

    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisStatus() method.
    }

    public function updateADAnalysisFirstClass(array $data)
    {
        // TODO: Implement updateADAnalysisFirstClass() method.
    }

    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisSecondClass() method.
    }

    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisBudgetOrBid() method.
    }

    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        // TODO: Implement handleADAnalysisBudgetOrBidMQData() method.
    }

    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        // TODO: Implement updateADAnalysisTargeting() method.
    }

    public function addADTaskByApi(array $input)
    {
        // TODO: Implement addADTaskByApi() method.
    }

    public function updateAndroidChannelPackage(array $input)
    {
        $ext_info = (new OdsMediaSDKModel())->getDataByGame($input['platform'], $input['media_type'], $input['game_id']);
        if (!$ext_info || !isset($ext_info->ext)) {
            throw new AppException('无可更新数据');
        }
        $sdk_ext = json_decode($ext_info->ext, true);
        $job = [
            'packageId' => $input['channel_package_id'],
            'packageLink' => $input['package_link'],
            'appLogo' => EnvConfig::DOMAIN . $sdk_ext['app_logo'],
            'appScreenshots' => array_map(function ($uri) {
                return EnvConfig::DOMAIN . $uri;
            }, $sdk_ext['app_screen_shots']),
            'appIntroduce' => $sdk_ext['app_introduce'],
            'category' => $sdk_ext['category'],
            'developerName' => $sdk_ext['developer_name'],
            'privacyProtectionAgreement' => true
        ];

        $job_list[] = $job;
        $unique_id = md5(json_encode($job_list));
        $body = [
            'uniqueId' => $unique_id,
            'jobList' => $job_list,
        ];

        $access_token_info = (new MediaAccountModel())->getDataByAccountId($input['account_id']);

        if (!$access_token_info) {
            throw new AppException('无相关账号信息');
        }

        return (new AppCenterJobModel())->update(
            $access_token_info->account_name,
            $access_token_info->access_token,
            $access_token_info->company,
            $access_token_info->refresh_token,
            $body
        );
    }

    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisDeepBidOrROI() method.
    }

    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement deleteADAnalysisAD() method.
    }

    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassBudget() method.
    }

    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisAccountBudget() method.
    }

    function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    function getTargetAudienceEstimate(array $target, $account_info)
    {
        // TODO: Implement getTargetAudienceEstimate() method.
    }

    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    /**
     * 创建广告位前置
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var  ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        if (!$param->site_id) {
            if ($other_setting->subject == BsEnum::SUBJECT_ANDROID) {
                $param->setSiteConfigProp('app_android_channel_package_id', SiteModel::CHANNEL_PACKAGE_WAITING);
            } else {
                $param->setSiteConfigProp('app_android_channel_package_id', SiteModel::CHANNEL_PACKAGE_NO_NEED);
            }
        }
    }

    /**
     * 创建广告位后置
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement afterMakeSite() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed|void
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // ---------------------------------新建广告一级--------------------------------

        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        //  ---------------------------------新建广告二级--------------------------------

        if (!$param->ad2_id) {
            try {
                $param->ad2_id = $this->createAD2($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        //  ---------------------------------广告二级的拓展--------------------------------

        // 添加关键词
        $this->create_keyword($param, $account_param);

        // 绑定oCPC出价策略
        $this->ocpcBind($param, $account_param);

        // 绑定人群
        $this->crowedBind($param, $account_param);

        // 创建基础创意素材&绑定单元
        $this->createPicMaterialAndBind($param, $account_param);

        // 绑定组件
        $this->advancedSegmentBind($param, $account_param);

        // ---------------------------------新建广告三级--------------------------------

        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
    }

    /**
     * @inheritDoc
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushFlowPackage() method.
    }

    /**
     * @inheritDoc
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushAudiencePackage() method.
    }

    /**
     * 打包
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        // 如果是落地页和安卓渠道包的任务，需要解析
        if (in_array($other_setting->subject, [BsEnum::SUBJECT_ANDROID, BsEnum::SUBJECT_LINK]) &&
            !is_numeric($param->getSiteConfig()->app_android_channel_package_id)) {

            $param->setSiteConfigProp('add_number', 1);

            $delay_queue_data = array_merge($param->toArray(), [
                'ad_task_list_id' => $param->id,
                'ldy_url' => '',
                'sdk_ext' => $param->getSiteConfig()->sdk_ext,
                'subject' => $other_setting->subject,
                'app_android_channel_package_id' => $param->getSiteConfig()->app_android_channel_package_id,
                'site_config' => $param->getSiteConfig()->toArray(),
                'download_url' => $param->getSiteConfig()->download_url,
                'game_type' => $param->getSiteConfig()->game_type,
                'plat_id' => $param->getSiteConfig()->plat_id,
            ]);

            (new SiteMQLogic)->produceBaiduTask($delay_queue_data, 10);

            throw new AppException('解析落地页或渠道包未完成', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }

        // 如果是需要等待打包的任务，需要走解析包链接
        if ($param->is_wait_package == 1) {

            $data = array_merge($param->toArray(), [
                'ad_task_list_id' => $param->id,
                'ldy_url' => '',
                'sdk_ext' => $param->getSiteConfig()->sdk_ext,
                'plat_id' => $param->getSiteConfig()->plat_id,
            ]);

            (new SiteMQLogic)->produceBaiduTask($data, 1);

            throw new AppException('等待打包中', ADTaskDigestLogic::STOP_TASK_WAIT_PACK_CODE);
        }
    }

    /**
     * 获取素材信息
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return array|mixed
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::BAIDU_SEARCH, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        $result = [];
        foreach ($material_file_list as $material_file) {
            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::BAIDU_SEARCH;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;
            if ($upload_info_list[$material_file->id]) {
                $result[$material_file->id] = [
                    'id' => $upload_info_list[$material_file->id]->media_material_file_id,
                    'url' => $upload_info_list[$material_file->id]->url,
                ];
            } else {
                if (in_array($material_file->file_type, [
                    MaterialFileModel::FILE_TYPE_COVER,
                    MaterialFileModel::FILE_TYPE_IMAGE,
                    MaterialFileModel::FILE_TYPE_COMBINE3,
                    MaterialFileModel::FILE_TYPE_COMBINE4,
                    MaterialFileModel::FILE_TYPE_COMBINE6,
                ])) {
                    // 不用处理
                    $media_file_id = 0;
                } else {
                    $media_file_info = $this->uploadVideo(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                }
                if ($media_file_id) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * @inheritDoc
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        // TODO: Implement isRestartADTask() method.
    }

    function prepareCreativeList(ADTaskParam $task_param)
    {
        // TODO: Implement prepareCreativeList() method.
    }

    function getAgentGroup(ADTaskParam $task_param)
    {
        return AgentGroup::BAIDU_SEARCH;
    }

    /**
     * 获取媒体所有广告状态
     * @return void
     */
    public function getDiffMediaStatus()
    {
        // TODO: Implement getDiffMediaStatus() method.
    }

    /**
     * 获取不同媒体的资源位列表
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        // TODO: Implement getDiffMediaInventoryList() method.
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        return '';
    }

    public function getTransferSet( $data ) {
        // TODO: Implement getTransferSet() method.
    }

    public function getAccountIdListByTransferableKey( $data ) {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    public function isCreateConvert(ADTaskParam $param)
    {
        if (!$param->convert_id) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新账号余额信息
     *
     * @param $data
     *
     * @return mixed
     */
    public function refreshAccountBalance( $data ) {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid( int $media_type, array $data, array $ad_format_target, string $update_field ) {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
}

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateFirstClassScheduleTime() method.
}

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime( int $media_type, array $data, array $ad_format_target ) {
        // TODO: Implement updateSecondClassScheduleTime() method.
}

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status() {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    /**
     * 广告一键起量
     *
     * @param $data
     *
     * @return mixed
     */
    public function updateADAnalysisADRaise( $data ) {
        // TODO: Implement updateADAnalysisADRaise() method.
}

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
