<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: Lin
 * Date: 2022/6/9
 * Time: 10:40
 */

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\Platform;
use App\Constant\PlatId;
use App\Constant\ToutiaoEnum;
use App\Exception\AppException;
use App\Logic\DSP\ADAnalysisMQLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\HttpModel\Toutiao\Carousel\CarouselModel;
use App\Model\HttpModel\Toutiao\Creative\CreativeModel;
use App\Model\HttpModel\Toutiao\File\FileVideoModel;
use App\Model\HttpModel\Toutiao\File\VideoModel;
use App\Model\HttpModel\Toutiao\Keyword\KeywordModel;
use App\Model\HttpModel\Toutiao\Tools\PromotionRaiseModel;
use App\Model\HttpModel\Toutiao\V2\Project\ProjectModel;
use App\Model\HttpModel\Toutiao\V2\Material\MaterialModel;
use App\Model\HttpModel\Toutiao\V2\Promotion\PromotionModel;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\SqlModel\DataMedia\ADAnalysisModel;
use App\Model\SqlModel\DataMedia\OdsLianShanYunMaterialFileLog;
use App\Model\SqlModel\DataMedia\OdsToutiaoADHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdRaiseStatusHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdRaiseVersionLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCampaignLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCarouselModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCreativeHisLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCreativeLogModel;
use App\Model\SqlModel\DataMedia\V2OdsToutiaoCampaignLogModel;
use App\Model\SqlModel\Zeda\ADAnalysisOperateLogModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\CampaignCreateMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\Toutiao\ADTargetingContentParam;
use App\Param\ADServing\Toutiao\V2\ADOtherSettingContentParam;
use App\Param\ADServing\Toutiao\V2\ADSettingContentParam;
use App\Param\Material\MaterialFileParam;
use App\Param\MediaAccountInfoParam;
use App\Param\Toutiao\FileVideoUploadParam;
use App\Param\Toutiao\V2\ProjectCreateParam;
use App\Param\Toutiao\V2\PromotionCreateParam;
use App\Service\ScriptMsgService;
use App\Utils\Helpers;
use Exception;
use Illuminate\Support\Collection;
use Throwable;

class MediaToutiaoV2 extends MediaToutiao
{

    public function isCreateConvert(ADTaskParam $param)
    {
//        if ($param->getSiteConfig()->plat_id == 7) {
//            return false;
//        }

        if ($param->getSiteConfig()->convert_toolkit == ConvertToolkit::TOUTIAO_ASSET) {
            if (empty($param->getSiteConfig()->ext['asset_ids'] ?? '')) {
                return true;
            } else {
                return false;
            }
        } else {
            if (!$param->convert_id) {
                return true;
            } else {
                return false;
            }
        }

    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed|void
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        if ($param->site_config['convert_source_type'] == ConvertSourceType::H5_API) {
            $site_ext = $param->site_config['ext'];
            $site_ext['external_url'] = $other_setting->getV2PageExternalUrlMapInfo($account_param->account_id)[0] ?? '';
            $param->setSiteConfigProp('ext', $site_ext);
        }

        if (in_array($other_setting->marketing_goal, [ToutiaoEnum::MARKETING_GOAL_LIVE]) && ($other_setting->ies_map[$param->account_id] ?? false)) {
            $site_ext = $param->site_config['ext'];
            $site_ext['aweme_account'] = $other_setting->ies_map[$param->account_id];
            $param->setSiteConfigProp('ext', $site_ext);
        }
    }

    public function removeEmoji($text)
    {
        $text = preg_replace_callback(
            '/./u',
            function (array $matches) {
                $str = mb_ereg_match('\p{So}', $matches[0]) ? '' : $matches[0];

                return strlen($str) >= 4 ? '' : $str;
            },
            $text);
        $pattern = '/[\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{3030}-\x{30FF}\x{FE00}-\x{FEFF}]/u';
        return preg_replace($pattern, '', $text);
    }

    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $task_param->other_setting;
        if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_HOT) {
            $first_hot_material_info = $other_setting->hot_material_map[$task_param->account_id][0];
            switch ($name_norms) {
                case 'material_author':
                    // 去除字符串中的 Emoji
                    return $this->removeEmoji($first_hot_material_info['aweme_name']);
                case 'material_size':
                    if ($first_hot_material_info['image_mode'] == 'CREATIVE_IMAGE_MODE_VIDEO_VERTICAL') {
                        return '竖版';
                    } else {
                        return '横版';
                    }
                case 'material_name':
                    $str = "{$first_hot_material_info['aweme_id']}-{$first_hot_material_info['aweme_name']}";
                    return $this->removeEmoji($str);
                case 'material_id':
                    return $this->removeEmoji(mb_substr($first_hot_material_info['title'] ?? '', 0, 5));
                case 'material_file_name':
                    $time = date('m-d', strtotime($first_hot_material_info['start_time']));
                    $str = "{$first_hot_material_info['aweme_id']}-{$first_hot_material_info['aweme_name']}-$time";
                    return $this->removeEmoji($str);
            }
        }


        if ($other_setting->origin_ad_type == 'origin') {
            if ($name_norms == 'aweme_account') {
                // 去除字符串中的 Emoji
                return $this->removeEmoji($other_setting->ies_map[$task_param->account_id]);
            }
        }

        if ($other_setting->origin_ad_type == 'star') {
            if ($name_norms == 'star_task_id') {
                return $other_setting->star_task_id ? intval($other_setting->star_task_id) : '';
            }
        }

        return '';
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return void
     */
    public function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建项目
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByName($param, $account_param)
                    ?: $this->createProject($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 推送试玩素材
        $this->pushPlayableMaterial($param, $account_param);

        // 新建广告
        if (!$param->ad2_id) {
            try {
                $param->ad2_id = $this->createPromotion($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));
    }

    private function getAD1InfoByName($ad1_name_text, $account_id, $access_token)
    {
        $ad1_log_model = new CampaignCreateMediaLogModel();
        $campaign_info = $ad1_log_model->getCreateLog(MediaType::TOUTIAO, $account_id, $ad1_name_text);
        $campaign_id = $campaign_info ? $campaign_info->campaign_id : 0;

        if ($campaign_id) {
            $filters = ['ids' => [$campaign_id]];
            $response_data = (new ProjectModel())->info($account_id, $access_token, [
                'project_id',
                'download_url',
                'optimize_goal',
                'delivery_setting'
            ], $filters, 1, 1);

            $campaign_id = $response_data['list'][0]['project_id'] ?? 0;
            if (!$campaign_id) {
                $ad1_log_model->deleteLogByID($campaign_info->id);
                return [];
            }
            return $response_data;
        }
        return [];
    }

    /**
     * 已创建好的项目ID
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getAD1IdByName(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $response_data = $this->getAD1InfoByName($param->ad1_name_text, $account_param->account_id, $account_param->access_token);
        $campaign_id = $response_data['list'][0]['project_id'] ?? 0;

        if (empty($campaign_id)) {
            return $campaign_id;
        }

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        if (
            $response_data['list'][0]['optimize_goal']['external_action'] != ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type] ||
            ($response_data['list'][0]['optimize_goal']['deep_external_action'] ?: 'DEEP_BID_DEFAULT') != ($param->getSiteConfig()->deep_external_action ?: 'DEEP_BID_DEFAULT') ||
            ($response_data['list'][0]['delivery_setting']['deep_bid_type'] ?: 'DEEP_BID_DEFAULT') != ($setting->deep_bid_type ?: 'DEEP_BID_DEFAULT')
        ) {
            $d = [
                'media_data' => [
                    'project_id' => $campaign_id,
                    'external_action' => $response_data['list'][0]['optimize_goal']['external_action'],
                    'deep_external_action' => $response_data['list'][0]['optimize_goal']['deep_external_action'],
                    'deep_bid_type' => $response_data['list'][0]['delivery_setting']['deep_bid_type']
                ],
                'system_data' => [
                    'external_action' => ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type],
                    'deep_external_action' => $param->getSiteConfig()->deep_external_action,
                    'deep_bid_type' => $setting->deep_bid_type
                ],
            ];
            throw new AppException("对比" . json_encode($d, JSON_UNESCAPED_UNICODE) . "对比失败,丢弃广告一级,请重新改广告一级名字" . PHP_EOL);
        }

        return $campaign_id;
    }

    /**
     * 创建项目
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return int|mixed
     * @throws Throwable
     */
    public function createProject(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADTargetingContentParam $targeting */
        $targeting = $param->targeting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;
        // 人群包年龄设置
        $targeting->age = $targeting->audienceAge();

        $data = [];
        $data['advertiser_id'] = $account_param->account_id;
        $data['operation'] = $setting->ad1_status;
        $data['landing_type'] = $other_setting->promotion_type;
        $data['marketing_goal'] = $other_setting->marketing_goal;
        $data['ad_type'] = $other_setting->ad_type;
        $data['name'] = $param->ad1_name_text;

        // 投放内容与目标
        if ($other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
            // 子目标，当 landing_type = APP 有效且必填
            $data['app_promotion_type'] = $other_setting->app_promotion_type;
            // 投放内容与目标
            if (in_array($other_setting->app_promotion_type, [ToutiaoEnum::APP_PROMOTION_TYPE_DOWNLOAD, ToutiaoEnum::APP_PROMOTION_TYPE_LAUNCH])) {
                $data['download_url'] = $param->getSiteConfig()->download_url;
            }
            if (in_array($other_setting->app_promotion_type, [ToutiaoEnum::APP_PROMOTION_TYPE_DOWNLOAD])) {
                $data['download_type'] = $other_setting->download_type;
                $data['download_mode'] = $param->getSiteConfig()->game_type == '安卓' ? $other_setting->download_mode : '';
            }
            if (in_array($other_setting->app_promotion_type, [ToutiaoEnum::APP_PROMOTION_TYPE_RESERVE])) {
                $data['subscribe_url'] = $other_setting->subscribe_url;
            }
            if ($other_setting->app_promotion_type === ToutiaoEnum::APP_PROMOTION_TYPE_LAUNCH) {
                $data['launch_type'] = $other_setting->launch_type;
            }
        }

        $ext_data = $param->getSiteConfig()->ext ?: [];
        // 优化目标
        if ($other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
            if (in_array($other_setting->app_promotion_type, [ToutiaoEnum::APP_PROMOTION_TYPE_DOWNLOAD, ToutiaoEnum::APP_PROMOTION_TYPE_LAUNCH])) {
                $data['optimize_goal'] = [];
                if ($param->getSiteConfig()->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
                    $data['optimize_goal']['asset_ids'] = $ext_data['asset_ids'] ?? [];
                    $data['optimize_goal']['external_action'] = ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type];
                } else {
                    $data['optimize_goal']['convert_id'] = $param->convert_id;
                }
                if ($param->getSiteConfig()->deep_external_action) {
                    $data['optimize_goal']['deep_external_action'] = $param->getSiteConfig()->deep_external_action;
                }
            }

            if ($other_setting->app_promotion_type == ToutiaoEnum::APP_PROMOTION_TYPE_RESERVE) {
                $data['optimize_goal'] = [];
                $data['optimize_goal']['external_action'] = ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type];
            }
        }

        //优化目标 小游戏时使 external_action
        if (in_array($other_setting->promotion_type, [ToutiaoEnum::LANDING_TYPE_MICRO_GAME, ToutiaoEnum::LANDING_TYPE_LINK])) {
            $data['optimize_goal']['external_action'] = ConvertType::MEDIA[MediaType::TOUTIAO][$param->getSiteConfig()->convert_type];
            //小游戏类型
            if ($other_setting->promotion_type == ToutiaoEnum::LANDING_TYPE_MICRO_GAME) {
                $data['micro_promotion_type'] = $other_setting->micro_promotion_type;
                if (in_array($other_setting->micro_promotion_type, ['BYTE_APP', 'BYTE_GAME', 'WECHAT_GAME', 'WECHAT_APP'])) {
//                    $data['micro_app_instance_id'] = $this->getByteGameInfo($param->platform, $param->media_type, $param->getSiteConfig()->game_id)['micro_game_instance_id'];
                    $data['micro_app_instance_id'] = $ext_data['micro_game_instance_id'] ?? "";
                    if (in_array($other_setting->micro_promotion_type, ['BYTE_APP', 'BYTE_GAME'])) {
                        $data['optimize_goal']['asset_ids'] = $ext_data['asset_ids'] ?? [];
                    }
                }
            } else {
                // 资产类型 landing_type = LINK 时有效且必填 允许值： ORANGE 橙子落地页、THIRDPARTY 自研落地页
                $data['asset_type'] = $other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL ? 'THIRDPARTY' : 'ORANGE';

                // landing_type=LINK 落地页类型为 THIRDPARTY (DOWNLOAD_URL) 时有效且必填
                if ($other_setting->download_type == ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
                    $data['optimize_goal']['asset_ids'] = $ext_data['asset_ids'] ?? [];
                }
            }

            if ($param->getSiteConfig()->deep_external_action) {
                $data['optimize_goal']['deep_external_action'] = $param->getSiteConfig()->deep_external_action;
            }
        }

        //  广告版位
        $data['delivery_mode'] = $setting->delivery_mode;
        $data['delivery_range'] = [];
        if ($setting->delivery_range == ToutiaoEnum::DELIVERY_RANGE_DEFAULT) {
            $data['delivery_range']['inventory_catalog'] = ToutiaoEnum::INVENTORY_CATALOG_MANUAL;
            $data['delivery_range']['inventory_type'] = $setting->inventory_type;
            if ($setting->isUnion()) {
                $data['delivery_range']['union_video_type'] = $setting->union_video_type;
            }
        } else {
            if ($setting->delivery_range == ToutiaoEnum::DELIVERY_RANGE_UNIVERSAL) {
                $data['delivery_range']['inventory_catalog'] = ToutiaoEnum::INVENTORY_CATALOG_UNIVERSAL;
            }
        }

        if ($param->getSiteConfig()->game_type == 'IOS' && !in_array($param->getSiteConfig()->plat_id, [PlatId::MINI, PlatId::DY_MINI])) {
            $data['app_name'] = $param->getSiteConfig()->app_name;
        }

        $request_param = new ProjectCreateParam($data);
        // 设置定向
        $request_param->setAudience(array_merge($targeting->toArray(), [
            "hide_if_exists" => $setting->hide_if_exists,
            "hide_if_converted" => $setting->hide_if_converted,
            "filter_event" => $setting->filter_event,
            "converted_time_duration" => in_array($setting->hide_if_converted, ["APP", "CUSTOMER"]) ? $setting->converted_time_duration : "",
            "launch_price" => $setting->delivery_mode == 'PROCEDURAL' ? [] : $targeting->launch_price,
            "ac" => $setting->delivery_mode == 'PROCEDURAL' ? [] : $targeting->ac,
            "superior_popularity_type" => $setting->delivery_mode == 'PROCEDURAL' ? '' : $targeting->superior_popularity_type,
            "interest_action_mode" => $setting->delivery_mode == 'PROCEDURAL' ? '' : $targeting->interest_action_mode,
            "action_scene" => $setting->delivery_mode == 'PROCEDURAL' ? [] : $targeting->action_scene,
            "action_days" => $setting->delivery_mode == 'PROCEDURAL' ? '' : $targeting->action_days,
            "action_categories" => $setting->delivery_mode == 'PROCEDURAL' ? [] : $targeting->action_categories,
            "interest_categories" => $setting->delivery_mode == 'PROCEDURAL' ? [] : $targeting->interest_categories,
            "device_type" => $setting->delivery_mode == 'PROCEDURAL' ? [] : $targeting->device_type,
        ]));


        if (
            $targeting->search_keyword_status == 1 &&
            $setting->delivery_range != ToutiaoEnum::DELIVERY_RANGE_UNION &&
            $other_setting->promotion_type != 'MICRO_GAME'
        ) {
            if ($targeting->search_keyword_type == 'feed_delivery_search') {
                $keyword_list = array_map(function ($data) {
                    $data['bid_type'] = 'FEED_TO_SEARCH';
                    return $data;
                }, $targeting->feed_search_keyword_list);
                $request_param->keywords = $keyword_list;
            }
            if ($setting->smart_bid_type != 'NO_BID' && $setting->delivery_mode != 'PROCEDURAL') {
                if (in_array($setting->deep_bid_type, ['DEEP_BID_DEFAULT', 'BID_PER_ACTION', 'DEEP_BID_PACING', 'ROI_COEFFICIENTROI'])) {
                    $request_param->search_bid_ratio = $targeting->search_bid_ratio;
                }
                $request_param->audience_extend = $targeting->audience_extend;
            }
        }

        if (in_array($other_setting->promotion_type, [ToutiaoEnum::LANDING_TYPE_MICRO_GAME, ToutiaoEnum::LANDING_TYPE_LINK])) {
            $request_param->audience['platform'] = $other_setting->os;
            if (in_array('ANDROID', $other_setting->os) && !in_array('IOS', $other_setting->os)) {
                unset($request_param->audience['ios_osv']);
            } elseif (!in_array('ANDROID', $other_setting->os) && in_array('IOS', $other_setting->os)) {
                unset($request_param->audience['android_osv']);
            }
        } else {
            if ($param->getSiteConfig()->game_type == '安卓') {
                unset($request_param->audience['ios_osv']);
                $request_param->audience['platform'] = ['ANDROID'];
            } else {
                unset($request_param->audience['android_osv']);
                $request_param->audience['platform'] = ['IOS'];
            }
        }

        // 自动UBA 如果开启了BUDGET_MODE_DAY 日预算 以及 NO_BID 最大转化投放 则预算择优开启，，否则空
        if ($setting->delivery_mode === ToutiaoEnum::BUDGET_MODE_PROCEDURAL) {
            if ($setting->ad1_budget_mode === ToutiaoEnum::BUDGET_MODE_DAY && $setting->smart_bid_type === ToutiaoEnum::NO_BID) {
                $setting->budget_optimize_switch = ToutiaoEnum::BUDGET_OPTIMIZE_SWITCH_ON;
            } else {
                $setting->budget_optimize_switch = '';
            }
        }

        // 星广联投任务
        $request_param->star_task_id = $other_setting->star_task_id ? intval($other_setting->star_task_id) : '';

        // 设置预算
        $request_param->setDelivery(array_merge($setting->toArray(), [
            'budget' => $setting->ad1_budget,
            'budget_mode' => $setting->ad1_budget_mode,
            'bid_type' => $setting->smart_bid_type,
            'roi_goal' =>
                (
                    $setting->delivery_mode == 'PROCEDURAL' &&
                    in_array(
                        $setting->deep_bid_type,
                        [
                            ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT,
                            ToutiaoEnum::DEEP_BID_TYPE_FIRST_AND_SEVEN_PAY_ROI,
                            ToutiaoEnum::DEEP_BID_TYPE_PER_AND_SEVEN_PAY_ROI,
                        ]
                    )
                ) ? round($setting->getRoiGoal(), 4) : ''
            ,
            'first_roi_goal' => $setting->first_roi_goal ? round((float)$setting->first_roi_goal, 4) : '',
            'cpa_bid' => $setting->delivery_mode == 'PROCEDURAL' ? round($setting->getCpaBid(), 4) : '',
            'budget_optimize_switch' => $setting->budget_optimize_switch,
        ]));

        // 设置监测链接
        // 固定监测链接类型为自定义
        $request_param->setTrackUrl([
            'track_url' => $param->getSiteConfig()->display_track_url ? [$param->getSiteConfig()->display_track_url] : [],
            'action_track_url' => [$param->getSiteConfig()->action_track_url],
        ]);

        try {
            $result = (new ProjectModel())->create($request_param, $account_param->account_id, $account_param->access_token);
        } catch (Throwable $e) {
            //            与项目ID=7224021038384316472的名称重复
            $msg = $e->getMessage();
            $campaign_id = '';
            if (strpos($msg, '与项目ID=') !== false && strpos($msg, '的名称重复') !== false) {
                $campaign_id_string = trim(str_replace('与项目ID=', '', $msg));
                $campaign_id = trim(str_replace('的名称重复', '', $campaign_id_string));
                $cut_index = strpos($campaign_id, ',');
                $campaign_id = trim(substr($campaign_id, 0, $cut_index));
                $param->ad1_id = $campaign_id;
                return $param->ad1_id;
            }
            if (!is_numeric($campaign_id)) {
                throw $e;
            }
        }

        $param->ad1_id = $result['project_id'] ?? 0;
        if ($param->ad1_id) {
            // 更新项目状态
            $this->addAD1Log($param->account_id, $param->ad1_id, $param->ad1_name_text);
        }
        return $param->ad1_id;
    }

    /**
     * 关闭项目
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    private function updateProjectStatus(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        if ($setting->ad1_status === ToutiaoEnum::OPERATION_DISABLE_2) {
            // 关闭项目
            (new ProjectModel())->updateProjectStatus(
                [$param->ad1_id], $setting->ad1_status, $account_param->account_id, $account_param->access_token);
        }
    }

    /**
     * 创建广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return int|mixed
     */
    public function createPromotion(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $data = [];
        $data['advertiser_id'] = $account_param->account_id;
        $data['project_id'] = $param->ad1_id;
        $data['name'] = (string)$param->ad2_name_text;
        $data['operation'] = $setting->ad2_status;

        // 广告来源
        if ($other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL &&
            in_array($other_setting->promotion_type, [ToutiaoEnum::LANDING_TYPE_LINK, ToutiaoEnum::LANDING_TYPE_MICRO_GAME])) {
            $data['source'] = $other_setting->source ?: $param->getSiteConfig()->app_name;
        }

        // 设置预算
        if ($setting->delivery_mode != 'PROCEDURAL') {
            $data['budget'] = $setting->ad2_budget;
        }
        // 支持预算择优分配  budget 必须为0 或者空
        if ($setting->delivery_mode != 'PROCEDURAL' && $setting->budget_optimize_switch === ToutiaoEnum::BUDGET_OPTIMIZE_SWITCH_ON) {
            $data['budget'] = 0;
        }

        if ($setting->smart_bid_type !== 'NO_BID' && $setting->delivery_mode != 'PROCEDURAL') {
            $data['cpa_bid'] = $setting->getCpaBid();
            if (in_array($setting->deep_bid_type, [ToutiaoEnum::DEEP_BID_TYPE_DEEP_BID_MIN])) {
                $data['deep_cpabid'] = $setting->deep_cpabid;
            }
            if (in_array($setting->deep_bid_type, [ToutiaoEnum::DEEP_BID_TYPE_ROI_COEFFICIENT])) {
                $data['roi_goal'] = round((float)$setting->getRoiGoal(), 4);
            }
        }

        // 素材组合
        $promotion_materials = [];

        // 设置素材
        if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_HOT) {
            // 加热
            foreach ($other_setting->hot_material_map[$param->account_id] as $creative) {
                $promotion_materials['video_material_list'][] = [
                    'video_id' => (string)$creative['video_id'],
                    'item_id' => (int)$creative['item_id'],
                    'video_hp_visibility' => 'HIDE_VIDEO_ON_HP',
                    'video_cover_id' => $creative['video_cover_id'],
                    'image_mode' => $creative['image_mode'] ?? CreativeModel::CREATIVE_IMAGE_MODE_VIDEO_VERTICAL
                ];
                $data['native_setting']['aweme_id'] = $creative['aweme_id'];
            }
        } elseif ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_STAR) {
            $guide_video_id = 0;
            if ($other_setting->guide_video_type == 'yes') {
                $guide_video_id = $this->getGuideVideoMediaId($other_setting->guide_video_id_map[$account_param->account_id], $account_param);
            }
            // 联投
            foreach ($other_setting->star_deliver_map[$param->account_id] as $creative) {
                $c_data = [
                    'video_id' => (string)$creative['video_id'],
                    'item_id' => (int)$creative['item_id'],
                    'video_hp_visibility' => 'HIDE_VIDEO_ON_HP',
                    'video_cover_id' => $creative['video_cover_id'],
                    'image_mode' => $creative['image_mode'] ?? CreativeModel::CREATIVE_IMAGE_MODE_VIDEO_VERTICAL
                ];

                if ($other_setting->guide_video_type == 'yes' && $guide_video_id) {
                    $c_data['guide_video_id'] = $guide_video_id;
                }
                $promotion_materials['video_material_list'][] = $c_data;

                $data['native_setting']['aweme_id'] = $creative['aweme_id'];
            }
        } else {
            // 信息流
            if ($other_setting->is_carousel) {
                // 图文
                $promotion_materials['carousel_material_list'][] = [
                    'carousel_id' => $this->uploadCarouse($param, $account_param, $material_media_id_map)
                ];
            } else {
                $already_file = [];

                $guide_video_id = 0;
                if ($other_setting->guide_video_type == 'yes') {
                    $guide_video_id = $this->getGuideVideoMediaId($other_setting->guide_video_id_map[$account_param->account_id], $account_param);
                }

                foreach ($param->creative_list as $creative_info) {
                    if (!isset($creative_info['video_info'])) {
                        $images = isset($creative_info['image_info']['id']) ? [['image_id' => $material_media_id_map[$creative_info['image_info']['id']]['id']]] : [['image_id' => $material_media_id_map[$creative_info['image_info']['image_id']]]];
                        if (!in_array(($material_media_id_map[$creative_info['image_info']['id']]['signature'] ?? $images), $already_file)) {
                            $promotion_materials['image_material_list'][] = [
                                'images' => $images,
                                'image_mode' => $this->getMaterialFileType(1, $creative_info['image_info']['width'], $creative_info['image_info']['height'], $setting->isUnionSplash())
                            ];
                            $already_file[] = $material_media_id_map[$creative_info['image_info']['id']]['signature'] ?? $images;
                        }

                    } else {
                        $video_id = isset($creative_info['video_info']['id']) ? $material_media_id_map[$creative_info['video_info']['id']]['id'] : $material_media_id_map[$creative_info['video_info']['video_id']];
                        if (!in_array(($material_media_id_map[$creative_info['video_info']['id']]['signature'] ?? $video_id), $already_file)) {
                            $c_data = [
                                'video_cover_id' => isset($creative_info['cover_info']['id']) ? $material_media_id_map[$creative_info['cover_info']['id']]['id'] : $material_media_id_map[$creative_info['cover_info']['image_id']],
                                'video_id' => $video_id,
                                'image_mode' => $this->getMaterialFileType(2, $creative_info['video_info']['width'], $creative_info['video_info']['height'], $setting->isUnionSplash()),
                            ];
                            if ($other_setting->guide_video_type == 'yes' && $guide_video_id) {
                                $c_data['guide_video_id'] = $guide_video_id;
                            }
                            $promotion_materials['video_material_list'][] = $c_data;
                            $already_file[] = $material_media_id_map[$creative_info['video_info']['id']]['signature'] ?? $video_id;
                        }
                    }
                }
            }
        }


        // 设置文案
        $promotion_materials['title_material_list'] = [];
        foreach ($param->getWordList() as $title) {
            $material = [
                'title' => $title
            ];
            if ($word_ids = $this->getWordIds($title)) {
                foreach ($word_ids as $word_id) {
                    $material['word_list'][] = $word_id;
                }
            }
            $promotion_materials['title_material_list'][] = $material;
        }

        if (in_array($other_setting->promotion_type, [ToutiaoEnum::LANDING_TYPE_LINK, ToutiaoEnum::LANDING_TYPE_MICRO_GAME])) {
            $promotion_materials['external_url_material_list'] = $other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL ? $other_setting->getV2PageWebUrlMapInfo($account_param->account_id)
                : $other_setting->getV2PageExternalUrlMapInfo($account_param->account_id);
        }

        // APP 设置落地页 且不是直播场景的时候
        if ($other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP && $other_setting->marketing_goal !== ToutiaoEnum::MARKETING_GOAL_LIVE) {

            if ($other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_EXTERNAL_URL) {
                $promotion_materials['external_url_material_list'] = $other_setting->getV2PageExternalUrlMapInfo($account_param->account_id);
            }

            if ($param->getSiteConfig()->game_type == '安卓' && $other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL) {
                $promotion_materials['web_url_material_list'] = $other_setting->getV2PageWebUrlMapInfo($account_param->account_id);
            }
            // 试玩素材
            if ($setting->delivery_range == ToutiaoEnum::DELIVERY_RANGE_DEFAULT && $other_setting->is_playable) {
                if ($other_setting->download_type === ToutiaoEnum::DOWNLOAD_TYPE_DOWNLOAD_URL ||
                    ((in_array(ToutiaoEnum::INVENTORY_TYPE_UNION, $setting->inventory_type) && count($setting->inventory_type) === 1) &&
                        $setting->union_video_type === ToutiaoEnum::UNION_VIDEO_TYPE_REWARDED_VIDEO)) {
                    $promotion_materials['playable_url_material_list'] = [$this->getPlayableUrl($param)];
                }
            }
        }

        // 原生广告设置
        if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_ORIGIN) {
            $data['native_setting']['aweme_id'] = $other_setting->ies_map[$account_param->account_id];
//            if ($param->platform == 'TW' && $param->creator != '陈林沛') {
//                $data['native_setting']['is_feed_and_fav_see'] = 'ON';
//            } else {
//                $data['native_setting']['is_feed_and_fav_see'] = $other_setting->is_feed_and_fav_see;
//            }
            $data['native_setting']['anchor_related_type'] = $other_setting->anchor_related_type;
            // 原生锚点类型素材手动时 必填
            if ($other_setting->anchor_related_type === ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                $promotion_materials['anchor_material_list'][] = [
                    'anchor_type' => ToutiaoEnum::ANCHOR_TYPE_APP_GAME,
                    'anchor_id' => trim($other_setting->anchor_related_map[$account_param->account_id]),
                ];
            }
        }

        // 加热广告设置
        if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_HOT) {
//            $data['native_setting']['is_feed_and_fav_see'] = 'OFF';
            $data['native_setting']['anchor_related_type'] = $other_setting->anchor_related_type;
            if ($other_setting->anchor_related_type === ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                $promotion_materials['anchor_material_list'][] = [
                    'anchor_type' => ToutiaoEnum::ANCHOR_TYPE_APP_GAME,
                    'anchor_id' => trim($other_setting->anchor_related_map[$account_param->account_id]),
                ];
            }
        }

        // 联投广告设置
        if ($other_setting->origin_ad_type == ToutiaoEnum::ORIGIN_AD_TYPE_STAR) {
//            $data['native_setting']['is_feed_and_fav_see'] = 'OFF';
            $data['native_setting']['anchor_related_type'] = $other_setting->anchor_related_type;
            if ($other_setting->anchor_related_type === ToutiaoEnum::ANCHOR_RELATED_TYPE_SELECT) {
                $promotion_materials['anchor_material_list'][] = [
                    'anchor_type' => ToutiaoEnum::ANCHOR_TYPE_APP_GAME,
                    'anchor_id' => trim($other_setting->anchor_related_map[$account_param->account_id]),
                ];
            }
        }

        // 创意组件设置
        if ($component_id = $other_setting->getComponentId($account_param->account_id)) {
            $promotion_materials['component_material_list'][] = [
                'component_id' => $component_id
            ];
        }

        // 上传产品图
        if ($setting->product_info_image_id) {
            $setting->product_info_image_id = $this->checkCardImageUpload(
                $param, $setting->product_info_image_id, $account_param);
        } else {
            throw new AppException('产品主图不能为空');
        }

        // 设置产品卖点
        $product_info = [
            'titles' => [$setting->product_info_title],
            'image_ids' => [$setting->product_info_image_id],
            'selling_points' => $setting->product_info_selling_points,
        ];
        $promotion_materials['product_info'] = $product_info;
        $promotion_materials['call_to_action_buttons'] = $setting->call_to_action_buttons;

        // 素材类型，直播场景必填，允许值：LIVE_MATERIALS直播素材，PROMOTION_MATERIALS广告素材
        if ($other_setting->marketing_goal === ToutiaoEnum::MARKETING_GOAL_LIVE) {
            $data['materials_type'] = $other_setting->materials_type;
        }

        if ($other_setting->marketing_goal === ToutiaoEnum::MARKETING_GOAL_LIVE && $other_setting->materials_type === ToutiaoEnum::LIVE_MATERIALS) {
            if ($other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_APP) {
                $promotion_materials = null;
            }
            if ($other_setting->promotion_type === ToutiaoEnum::LANDING_TYPE_MICRO_GAME) {
                $promotion_materials = ['external_url_material_list' => $promotion_materials['external_url_material_list']];
            }
        }

        if ($other_setting->promotion_type == ToutiaoEnum::LANDING_TYPE_MICRO_GAME && in_array($other_setting->micro_promotion_type, ['BYTE_APP', 'BYTE_GAME'])) {
            $info = $this->getByteGameInfo($param->platform, $param->media_type, $param->getSiteConfig()->game_id);
            $promotion_materials['mini_program_info'] = [
                'app_id' => $info['byte_game_id']
            ];

            if ($other_setting->micro_promotion_type == 'BYTE_APP') {
                unset($promotion_materials['app_id']);
                $promotion_materials['url'] = $info['byte_game_url'];
            }
        }

        $data['promotion_materials'] = $promotion_materials;

        $result = (new PromotionModel())->create(new PromotionCreateParam($data), $account_param->account_id, $account_param->access_token);

        $param->ad2_id = $result['promotion_id'] ?? 0;
//        if ($param->ad2_id && $setting->delivery_mode != 'PROCEDURAL') {
//            $this->updatePromotionStatus($param, $account_param);
//        }

        return $param->ad2_id;
    }

    public function getGuideVideoMediaId($material_file_id, MediaAccountInfoParam $account_param)
    {
        $material_file = (new MaterialFileModel())->getData($material_file_id);
        $param = new MaterialFileParam($material_file);
        try {
            $lsy_data = (new OdsLianShanYunMaterialFileLog())->getDataByFileId($material_file_id);
            if ($lsy_data) {
                $param->url = "https://$lsy_data->bucket_name.tos-cn-beijing.volces.com/$lsy_data->signature.mp4";
            }

            $data_param = new FileVideoUploadParam($param, $account_param->account_id, $account_param->access_token);

            $data_param->is_guide_video = true;

            $result = (new FileVideoModel())->upload($data_param);
            return $result['video_id'];
        } catch (Throwable $e) {
            throw new AppException("引导视频{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed
     */
    public function uploadCarouse(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $images = [];
        $audio_id = '';
        foreach ($param->creative_list as $creative_info) {
            if ($creative_info['video_info']['id']) {
                $audio_id = $material_media_id_map[$creative_info['video_info']['id']]['id'];
            } else {
                $images[] = ['image_id' => $material_media_id_map[$creative_info['image_info']['id']]['id']];
            }
        }

        $check_unique = implode('', array_column($images, 'image_id'));
        $audio_id && $check_unique .= $audio_id;
        if ($carousel_id = (new OdsToutiaoCarouselModel())->getCarouselIdByMd5AndAccountID(
            $account_param->account_id, md5($check_unique))) {
            return $carousel_id;
        }

        $body = [
            'images' => $images,
            'carousel_type' => 'INFORMATION_FLOW_IMAGE',
        ];
        if ($audio_id) {
            $body['video_id'] = $audio_id;
        }
        $resp = (new CarouselModel())->addCarousel($account_param->account_id, $account_param->access_token, $body);

        (new ToutiaoTaskModel())->setAsync(false)->carousel([$account_param->account_id]);

        return (string)$resp['carousel']['carousel_id'];
    }

    /**
     * 关闭广告
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     */
    private function updatePromotionStatus(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;
        if ($setting->ad2_status === ToutiaoEnum::OPERATION_DISABLE_2) {
            // 关闭广告
            (new PromotionModel())->updatePromotionStatus(
                [$param->ad2_id], $setting->ad2_status, $account_param->account_id, $account_param->access_token);
        }
    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return array|mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $toutiao_format_target)
    {
        if (2 === (int)$data['ad_level']) {
            //广告
            $model = new PromotionModel();
            $ad_id_type = 'ad_id';
            $media_ad_id = 'promotion_id';
            $ad_name_type = 'ad_name';
            $batch_limit = 10;
            $method = 'updatePromotionStatus';
            $toutiao_ad_model = new OdsToutiaoAdLogModel();
            $toutiao_his_log_model = new OdsToutiaoADHisLogModel();
            $history_update_field = 'opt_status';
            $close = 'AD_STATUS_DISABLE';
            $open = 'AD_STATUS_ENABLE';
            $delete = 'AD_STATUS_DELETE';
            $column = ['ad_log.' . $ad_id_type, 'ad_log.' . $ad_name_type, 'ad_log.account_id', 'ad_log.platform', 'ad_log.site_id'];
        } elseif (1 === (int)$data['ad_level']) {
            //项目
            $model = new ProjectModel();
            $ad_id_type = 'campaign_id';
            $media_ad_id = 'project_id';
            $ad_name_type = 'campaign_name';
            $batch_limit = 10;
            $method = 'updateProjectStatus';
            $toutiao_ad_model = new OdsToutiaoCampaignLogModel();
            $toutiao_his_log_model = new V2OdsToutiaoCampaignLogModel();
            $history_update_field = 'status';
            $close = 'PROJECT_STATUS_DISABLE';
            $open = 'PROJECT_STATUS_ENABLE';
            $delete = 'PROJECT_STATUS_DELETE';
            $column = [$ad_id_type, $ad_name_type, 'account_id', 'platform'];
        }

        //存储到操作日志的单条数据
        $single_record_operate = [];
        if (0 === (int)$data['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_OFF;
            $opt_status = 'DISABLE';
            $switch_option = $close;
            $edit_detail = '关闭';
        } elseif (1 === (int)$data['switch']) {
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_ON;
            $opt_status = 'ENABLE';
            $switch_option = $open;
            $edit_detail = '开启';
        } else {
            $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;
            $opt_status = 'delete';
            $switch_option = $delete;
            $edit_detail = '删除';

        }

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        //操作计划开启开关的时候校验打包状态
        if (in_array('ad_log.site_id', $column) && 1 === $data['switch']) {
            $check = $this->checkPackageStatus($column, $toutiao_ad_model, $toutiao_format_target, $data['editor_id'], $data['editor_name']);
            $result = $check['result'];

            /* @var Collection $result */
            if ($result->isEmpty()) {
                return $check['return_data'];
            }
            $return_data = $check['return_data'];
            $record_operate = $check['record_operate'];
        } else {
            $result = $toutiao_ad_model->getAccountId($toutiao_format_target, $column);
            //检查是否被智投接管
//            if ((!isset($data['is_machine']) || 1 !== (int)$data['is_machine']) &&
//                2 === $data['ad_level'] && 0 === (int)$data['switch']) {
//                $check_score = $this->checkResultAction($result, $data['editor_id'], $data['editor_name']);
//                $return_data = $check_score['return_data'];
//                $record_operate = $check_score['record_operate'];
//                $result = $check_score['result'];
//            }
            if ($result->isEmpty()) {
                return $return_data;
//                throw new AppException('修改失败');
            }
        }

        $all_ad_ids = [];
        $account_ids = [];
        $format_data = [];
        foreach ($result as $ad_info) {
            $format_data[$ad_info->account_id][] = (int)$ad_info->$ad_id_type;
            $all_ad_ids[] = $ad_info->$ad_id_type;
            $account_ids[] = $ad_info->account_id;
        }

//        foreach ($toutiao_format_target as $ad_infos) {
//            $all_ad_ids = array_merge($all_ad_ids, $ad_infos);
//        }

//        $account_ids = array_keys($toutiao_format_target);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');
        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //如果是机器学习来操作且是开启广告， 则标记result_action=1
//        if (isset($data['is_machine']) && 1 === (int)$data['is_machine'] && 1 === (int)$data['switch']) {
//            $single_record_operate['result_action'] = 1;
//        }
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //无权限操作的广告id集合
        $no_permission_ad_ids = [];

        foreach ($format_data as $acc_id => $ad_ids) {
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$acc_id])) {
                $no_permission_ad_ids = array_merge(
                    $no_permission_ad_ids,
                    $ad_ids
                );
                continue;
            }
            //解决同一账号中ad_id可能超出的问题
            $ids_num = count($ad_ids);
            $loop_times = (int)ceil($ids_num / $batch_limit);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-99、100-199、200-299....
                $part_of_ad_ids = array_slice($ad_ids, ($i - 1) * $batch_limit, $batch_limit);
                //请求头条修改开关状态
                try {
                    $response = $model->$method($part_of_ad_ids, $opt_status, (int)$acc_id, $access_token_info[$acc_id]->access_token);

                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response['errors']) && $response['errors']) {
                        foreach ($response['errors'] as $one_error) {
                            $failed_ad_ids[$one_error['error_message']][] = $one_error[$media_ad_id];
                            $error_ad_ids[] = $one_error[$media_ad_id];
                        }
                    }
                    $part_of_success = $response[$media_ad_id . 's'] ?? array_diff($part_of_ad_ids, $error_ad_ids);
                    //操作成功
                    //因为account_id相同的ad_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
                    $success_ad_ids = array_merge($success_ad_ids, $part_of_success);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_ad_ids[$failed_info])) {
                        $failed_ad_ids[$failed_info] = array_merge($part_of_ad_ids, $failed_ad_ids[$failed_info]);
                    } else {
                        $failed_ad_ids[$failed_info] = $part_of_ad_ids;
                    }
                }
            }
        }
        unset($item);

        //插入his表
        if ($success_ad_ids) {
            $toutiao_his_log_model->getLatestByADInfoAndInsert(
                $success_ad_ids,
                $history_update_field,
                $switch_option,
                $single_record_operate['insert_time']
            );
        }

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['ad_id']] = $item;
        }, $data['update']);

        //处理成功的
        if ($success_ad_ids) {
            foreach ($success_ad_ids as $ad_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $risk_operate = '';
                if (isset($opt_data[$ad_id]['risk_operate']) && 1 === (int)$opt_data[$ad_id]['risk_operate']) {
                    $risk_operate = '本次为风险操作';
                    $success_code = ADAnalysisModel::SUCCESS_WITH_RISK;
                }
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;

                $single_record_operate['edit_detail'] = $edit_detail . '成功 ' . $risk_operate;

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }

        //处理失败的
        if ($failed_ad_ids) {
            foreach ($failed_ad_ids as $key => $ad_ids) {
                foreach ($ad_ids as $one_ad_id) {
                    $risk_operate = '';
                    $failed_code = ADAnalysisModel::FAILED;
                    if (isset($opt_data[$one_ad_id]['risk_operate']) && 1 === (int)$opt_data[$one_ad_id]['risk_operate']) {
                        $risk_operate = '本次为风险操作';
                        $failed_code = ADAnalysisModel::FAILED_WITH_RISK;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_ad_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_ad_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$one_ad_id;
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = $edit_detail . '失败，' . $risk_operate . ' 错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_ad_id);
            }
            unset($ad_ids);
        }

        //处理没有权限的
        if ($no_permission_ad_ids) {
            foreach ($no_permission_ad_ids as $ad_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表头条2.0素材状态
     * @param array $data
     * @param array $toutiao_format_target
     * @return array|mixed
     */
    public function updateADAnalysisMaterialStatus(array $data, array $toutiao_format_target)
    {
        //存储到操作日志的单条数据
        $single_record_operate = [];
        if (0 === (int)$data['switch']) {
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_OFF;
            $opt_status = 'DISABLE';
            $switch_option = 'MATERIAL_STATUS_DISABLE';
            $edit_detail = '素材关闭';
        } else {
            $single_record_operate['edit_type'] = ADAnalysisModel::SWITCH_ON;
            $opt_status = 'ENABLE';
            $switch_option = 'MATERIAL_STATUS_OK';
            $edit_detail = '素材启用';
        }

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        $result = (new OdsToutiaoCreativeLogModel())->getAccountId(
            $toutiao_format_target,
            ['creative_id', 'ad_id', 'material_id', 'account_id', 'platform']
        );

        if ($result->isEmpty()) {
            return $return_data;
        }

        $account_ids = [];
        $format_data = [];
        $creative_material_relation = [];
        foreach ($result as $ad_info) {
            $format_data[$ad_info->account_id . '-' . $ad_info->ad_id][] = (int)$ad_info->material_id;
            $creative_material_relation[$ad_info->material_id] = $ad_info->creative_id;
            $account_ids[] = $ad_info->account_id;
        }

        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');
        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的ad_id集合
        $success_creative_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_creative_ids = [];
        //无权限操作的广告id集合
        $no_permission_creative_ids = [];

        foreach ($format_data as $acc_id => $material_ids) {
            $info = explode('-', $acc_id);
            $account_id = $info[0];
            $promotion_id = $info[1];
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$account_id])) {
                foreach ($material_ids as $one_material) {
                    $no_permission_creative_ids[] = $creative_material_relation[$one_material];
                }
                continue;
            }
            //解决同一账号中ad_id可能超出的问题
            $ids_num = count($material_ids);
            $loop_times = (int)ceil($ids_num / 10);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                $part_of_material_ids = array_slice($material_ids, ($i - 1) * 10, 10);
                $request_group = [];
                $part_of_creative_ids = [];
                foreach ($part_of_material_ids as $single_material_id) {
                    $request_group[] = ['material_id' => $single_material_id, 'opt_status' => $opt_status];
                    $part_of_creative_ids[] = $creative_material_relation[$single_material_id];
                }
                //请求头条修改开关状态
                try {
                    $response = (new MaterialModel())->updateMaterialStatus(
                        (int)$acc_id,
                        (int)$promotion_id,
                        $request_group,
                        $access_token_info[$account_id]->access_token
                    );

                    //部分失败
                    if (isset($response['errors']) && $response['errors']) {
                        $failed_creative_ids['媒体未知错误'] = $failed_creative_ids['媒体未知错误'] ?? [];
                        foreach ($response['errors'] as $error_material) {
                            $failed_creative_ids['媒体未知错误'] = array_merge($failed_creative_ids['媒体未知错误'], [$creative_material_relation[$error_material['material_id']]]);
                        }
                    }
                    //操作成功
                    if (isset($response['material_ids']) && $response['material_ids']) {
                        foreach ($response['material_ids'] as $one_material_id) {
                            $success_creative_ids[] = $creative_material_relation[$one_material_id];
                        }
                    }
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_creative_ids[$failed_info])) {
                        $failed_creative_ids[$failed_info] = array_merge($part_of_creative_ids, $failed_creative_ids[$failed_info]);
                    } else {
                        $failed_creative_ids[$failed_info] = $part_of_creative_ids;
                    }
                }
            }
        }
        unset($item);

        //插入his表
        if ($success_creative_ids) {
            (new OdsToutiaoCreativeHisLogModel())->getLatestByADInfoAndInsert(
                $success_creative_ids,
                'opt_status',
                $switch_option,
                $single_record_operate['insert_time']
            );
        }

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['ad_id']] = $item;
        }, $data['update']);

        //处理成功的
        if ($success_creative_ids) {
            foreach ($success_creative_ids as $creative_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $risk_operate = '';
                if (isset($opt_data[$creative_id]['risk_operate']) && 1 === (int)$opt_data[$creative_id]['risk_operate']) {
                    $risk_operate = '本次为风险操作';
                    $success_code = ADAnalysisModel::SUCCESS_WITH_RISK;
                }
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$creative_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$creative_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$creative_id;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $switch_option;

                $single_record_operate['edit_detail'] = $edit_detail . '成功 ' . $risk_operate;

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }

        //处理失败的
        if ($failed_creative_ids) {
            foreach ($failed_creative_ids as $key => $creative_ids) {
                foreach ($creative_ids as $one_creative_id) {
                    $risk_operate = '';
                    $failed_code = ADAnalysisModel::FAILED;
                    if (isset($opt_data[$one_creative_id]['risk_operate']) && 1 === (int)$opt_data[$one_creative_id]['risk_operate']) {
                        $risk_operate = '本次为风险操作';
                        $failed_code = ADAnalysisModel::FAILED_WITH_RISK;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_creative_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_creative_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$one_creative_id;
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = $edit_detail . '失败，' . $risk_operate . ' 错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_ad_id);
            }
            unset($ad_ids);
        }

        //处理没有权限的
        if ($no_permission_creative_ids) {
            foreach ($no_permission_creative_ids as $creative_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$creative_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$creative_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$creative_id;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改基本报表广告开关状态
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return array|mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $toutiao_format_target)
    {
        if (2 === $data['ad_level']) {
            //广告
            $model = new PromotionModel();
            $media_ad_id = 'promotion_id';
            $toutiao_his_log_model = new OdsToutiaoADHisLogModel();
            $history_update_field = 'opt_status';
            $delete = 'AD_STATUS_DELETE';
            $method = 'deletePromotion';
        } else {
            //项目
            $model = new ProjectModel();
            $media_ad_id = 'project_id';
            $toutiao_his_log_model = new V2OdsToutiaoCampaignLogModel();
            $history_update_field = 'status';
            $delete = 'PROJECT_STATUS_DELETE';
            $method = 'deleteProject';
        }

        //存储到操作日志的单条数据
        $single_record_operate = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = ADAnalysisModel::DELETE_AD;

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        $all_ad_ids = [];
        foreach ($toutiao_format_target as $ad_infos) {
            $all_ad_ids = array_merge($all_ad_ids, $ad_infos);
        }

        $account_ids = array_keys($toutiao_format_target);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');
        $single_record_operate['ad_level'] = $data['ad_level'];
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //无权限操作的广告id集合
        $no_permission_ad_ids = [];

        foreach ($toutiao_format_target as $acc_id => $ad_ids) {
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$acc_id])) {
                $no_permission_ad_ids = array_merge(
                    $no_permission_ad_ids,
                    $ad_ids
                );
                continue;
            }
            //解决同一账号中ad_id可能超出的问题
            $ids_num = count($ad_ids);
            $loop_times = (int)ceil($ids_num / 10);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-99、100-199、200-299....
                $part_of_ad_ids = array_slice($ad_ids, ($i - 1) * 10, 10);
                //请求头条修改开关状态
                try {
                    $response = $model->$method($part_of_ad_ids, (int)$acc_id, $access_token_info[$acc_id]->access_token);

                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response['errors']) && $response['errors']) {
                        foreach ($response['errors'] as $one_error) {
                            $failed_ad_ids[$one_error['error_message']][] = $one_error[$media_ad_id];
                            $error_ad_ids[] = $one_error[$media_ad_id];
                        }
                    }
                    $part_of_success = $response[$media_ad_id . 's'] ?? array_diff($part_of_ad_ids, $error_ad_ids);
                    //操作成功
                    //因为account_id相同的ad_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
                    $success_ad_ids = array_merge($success_ad_ids, $part_of_success);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_ad_ids[$failed_info])) {
                        $failed_ad_ids[$failed_info] = array_merge($part_of_ad_ids, $failed_ad_ids[$failed_info]);
                    } else {
                        $failed_ad_ids[$failed_info] = $part_of_ad_ids;
                    }
                }
            }
        }
        unset($item);

        //插入his表
        if ($success_ad_ids) {
            $toutiao_his_log_model->getLatestByADInfoAndInsert(
                $success_ad_ids,
                $history_update_field,
                $delete,
                $single_record_operate['insert_time']
            );
        }

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['ad_id']] = $item;
        }, $data['update']);

        //处理成功的
        if ($success_ad_ids) {
            foreach ($success_ad_ids as $ad_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $risk_operate = '';
                if (isset($opt_data[$ad_id]['risk_operate']) && 1 === (int)$opt_data[$ad_id]['risk_operate']) {
                    $risk_operate = '本次为风险操作';
                    $success_code = ADAnalysisModel::SUCCESS_WITH_RISK;
                }
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $delete;

                $single_record_operate['edit_detail'] = '删除成功 ' . $risk_operate;

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }

        //处理失败的
        if ($failed_ad_ids) {
            foreach ($failed_ad_ids as $key => $ad_ids) {
                foreach ($ad_ids as $one_ad_id) {
                    $risk_operate = '';
                    $failed_code = ADAnalysisModel::FAILED;
                    if (isset($opt_data[$one_ad_id]['risk_operate']) && 1 === (int)$opt_data[$one_ad_id]['risk_operate']) {
                        $risk_operate = '本次为风险操作';
                        $failed_code = ADAnalysisModel::FAILED_WITH_RISK;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_ad_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_ad_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$one_ad_id;
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = '删除失败，' . $risk_operate . ' 错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_ad_id);
            }
            unset($ad_ids);
        }

        //处理没有权限的
        if ($no_permission_ad_ids) {
            foreach ($no_permission_ad_ids as $ad_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 修改头条广告组数据
     * @param array $input
     * @return array
     */
    public function updateADAnalysisFirstClass(array $input)
    {
        $toutiao_model = new OdsToutiaoCampaignLogModel();
        $account_info = $toutiao_model->getAccountIdAndPlatform($input);
        if (!$account_info) {
            throw new AppException('修改失败');
        }
        $toutiao_http_model = new ProjectModel();
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$input['account_id']], null, $leader_permission);

        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 1;
        $record_operate['media_type'] = MediaType::TOUTIAO;
        $record_operate['editor_id'] = $input['editor_id'];
        $record_operate['editor_name'] = $input['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $record_operate['edit_type'] = ADAnalysisModel::CHANGE_FIRST_CLASS_AD_NAME;
        $record_operate['platform'] = $account_info->platform;
        $record_operate['account_id'] = (int)$account_info->account_id;
        $record_operate['ad_id'] = $return_data['ad_id'] = (string)$input['ad_id'];
//        $record_operate['ad_name'] = $return_data['value'] = $input['ad_group_name'];
        $record_operate['status'] = $return_data['status'] = ADAnalysisModel::FAILED;

        if ($access_token->isNotEmpty()) {
            $access_token = $access_token->pop()->access_token;
            $advertiser_id = (int)$account_info->account_id;
            $project_id = (int)$input['ad_id'];
            $request_data['name'] = $input['ad_group_name'];

            try {
                $update_info = [];
                $toutiao_http_model->updateProject($request_data, $project_id, $advertiser_id, $access_token);
                //修改成功
                $return_data['message'] = 'success';
                $record_operate['ad_name'] = $return_data['value'] = $input['ad_group_name'];
                $record_operate['status'] = $return_data['status'] = ADAnalysisModel::SUCCESS;
                $record_operate['edit_detail'] = "项目名称修改为 [ {$input['ad_group_name']} ]";
//                    $update_info[$request_data['advertiser_id']] = [$request_data['campaign_id']];
                $update_info[] = $project_id;

                if ($update_info) {
                    (new V2OdsToutiaoCampaignLogModel())->getLatestByADInfoAndInsert(
                        $update_info,
                        'campaign_name',
                        $input['ad_group_name'],
                        $record_operate['insert_time']
                    );
                }
            } catch (AppException $e) {
                $return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $record_operate['edit_detail'] = "修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            }
        } else {
            //没有权限操作
            $return_data['message'] = $record_operate['edit_detail'] = '当前用户无操作权限';
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return [$return_data];
    }

    /**
     * 修改头条广告计划数据
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @return array
     */
    public function updateADAnalysisSecondClass(int $media_type, array $input, array $toutiao_format_target)
    {
        //使用新的批量修改二级投放时段
        if (isset($input['schedule_time'])) {
            foreach ($input['update'] as $value) {
                $format_target[$value['account_id']][] = (int)$value['ad_id'];
            }
            return $this->updateSecondClassScheduleTime($media_type, $input, $format_target);
        }

        $account_ids = array_unique(array_column($toutiao_format_target, 'account_id'));
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);

        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        if (!isset($input['schedule_time'])) {
            $delivery_mode_data = (new OdsToutiaoAdLogModel())->getAccountId(
                $toutiao_format_target,
                ['ad_log.ad_id', 'first_ad_log.delivery_mode'],
                false,
                true
            )->keyBy('ad_id');
            $toutiao_http_model = new PromotionModel();
            $request_method = 'updatePromotion';
            $input_ad_type = 'ad_id';
            $single_record_operate['ad_level'] = 2;
            $update_value = $request_data['name'] = $input['ad_name'];
            //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SECOND_CLASS_AD_NAME;
            $update_field = 'ad_name';
            $opt_detail = '广告二级名';
        } else {
            $toutiao_http_model = new ProjectModel();
            $request_method = 'updateProject';
            $input_ad_type = 'ad1_id';
            $single_record_operate['ad_level'] = 1;
            //修改schedule_time
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SCHEDULE_TIME;
            $update_value = $request_data['delivery_setting']['schedule_time'] = $input['schedule_time'];
            $update_field = 'schedule_time';
            $opt_detail = '广告投放时段';
        }

        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        $his_record = [];
        $his_return = [];
        foreach ($toutiao_format_target as $v) {
            if (!isset($access_tokens[$v['account_id']])) {
                $single_record_operate['platform'] = $v['platform'];
                $single_record_operate['account_id'] = (int)$v['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v[$input_ad_type];
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_record_operate['status'] = $single_return_data['status'] = ADAnalysisModel::FAILED;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            try {
                if (!isset($input['schedule_time']) && isset($delivery_mode_data)) {
                    //2.0广告为全量更新方式，先获取广告参数
                    $filtering = [
                        'ids' => [(int)$v[$input_ad_type]],
                        'delivery_mode' => $delivery_mode_data[$v[$input_ad_type]]->delivery_mode
                    ];
                    $promotion_data_list = $toutiao_http_model->info(
                        (int)$v['account_id'],
                        $access_tokens[$v['account_id']],
                        [],
                        $filtering
                    );

                    $promotion_data = Helpers::filterArray($promotion_data_list['list'][0]);
                    $request_data['name'] = $input['ad_name'];
                    $request_data['promotion_materials'] = $promotion_data['promotion_materials'] ?? (object)[];

                    if (isset($promotion_data['budget'])) {
                        $request_data['budget'] = $promotion_data['budget'];
                    }
                    if (isset($promotion_data['cpa_bid'])) {
                        $request_data['cpa_bid'] = $promotion_data['cpa_bid'];
                    }
                    if (isset($promotion_data['deep_cpabid'])) {
                        $request_data['deep_cpabid'] = $promotion_data['deep_cpabid'];
                    }
                    if (isset($promotion_data['roi_goal'])) {
                        $request_data['roi_goal'] = $promotion_data['roi_goal'];
                    }

                } else {
                    //如果这个一级投放时段已经改过， 则找到历史的数据存储然后跳过
                    if (isset($his_record[$v['ad1_id']])) {
                        $his_record[$v['ad1_id']]['ad_id'] = $v['ad_id'];
                        $his_return[$v['ad1_id']]['ad_id'] = $v['ad_id'];
                        $record_operate[] = $his_record[$v['ad1_id']];
                        $return_data[] = $his_return[$v['ad1_id']];
                        continue;
                    }
                }

                //向头条发起请求
                $toutiao_http_model->$request_method($request_data, $v[$input_ad_type], $v['account_id'], $access_tokens[$v['account_id']]);
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $update_value;
                $update_data[] = (int)$v['ad_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $opt_detail . "修改成功";

            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' . json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_record_operate['platform'] = $v['platform'];
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (string)$v['ad_id'];
            if (isset($v['ad1_id'])) {
                $his_record[$v['ad1_id']] = $single_record_operate;
                $his_return[$v['ad1_id']] = $single_return_data;
            }
            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_value,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return array
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $toutiao_format_target)
    {
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SCHEDULE_TIME;

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        $all_ad_ids = [];
        foreach ($toutiao_format_target as $ad_infos) {
            $all_ad_ids = array_merge($all_ad_ids, $ad_infos);
        }

        $account_ids = array_keys($toutiao_format_target);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');
        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //无权限操作的广告id集合
        $no_permission_ad_ids = [];

        $project_model = new ProjectModel();

        foreach ($toutiao_format_target as $acc_id => $ad_ids) {
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$acc_id])) {
                $no_permission_ad_ids = array_merge(
                    $no_permission_ad_ids,
                    $ad_ids
                );
                continue;
            }
            //解决同一账号中ad_id可能超出的问题
            $ids_num = count($ad_ids);
            $loop_times = (int)ceil($ids_num / 10);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-9、10-19、20-29....
                $part_of_ad_ids = array_slice($ad_ids, ($i - 1) * 10, 10);
                $request_data['data'] = [];
                foreach ($part_of_ad_ids as $one_id) {
                    $request_data['data'][] = [
                        'project_id' => (int)$one_id,
                        'schedule_time' => $data['schedule_time'],
                        'schedule_scene' => $data['schedule_scene']
                    ];
                }
                try {
                    $response = $project_model->updateProjectScheduleTime($request_data, (int)$acc_id, $access_token_info[$acc_id]->access_token);

                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response['errors']) && $response['errors']) {
                        foreach ($response['errors'] as $one_error) {
                            $failed_ad_ids[$one_error['error_message']][] = $one_error['project_id'];
                            $error_ad_ids[] = $one_error['project_id'];
                        }
                    }
                    $part_of_success = $response['project_ids'] ?? array_diff($part_of_ad_ids, $error_ad_ids);
                    //操作成功
                    //因为account_id相同的ad_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
                    $success_ad_ids = array_merge($success_ad_ids, $part_of_success);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_ad_ids[$failed_info])) {
                        $failed_ad_ids[$failed_info] = array_merge($part_of_ad_ids, $failed_ad_ids[$failed_info]);
                    } else {
                        $failed_ad_ids[$failed_info] = $part_of_ad_ids;
                    }
                }
            }
        }
        unset($item);

        //插入his表
        if ($success_ad_ids) {
            (new V2OdsToutiaoCampaignLogModel())->getLatestByADInfoAndInsert(
                $success_ad_ids,
                'schedule_time',
                $data['schedule_time'],
                $single_record_operate['insert_time']
            );
        }

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['ad_id']] = $item;
        }, $data['update']);

        //处理成功的
        if ($success_ad_ids) {
            foreach ($success_ad_ids as $ad_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $data['schedule_time'];

                $single_record_operate['edit_detail'] = '修改成功';

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }

        //处理失败的
        if ($failed_ad_ids) {
            foreach ($failed_ad_ids as $key => $ad_ids) {
                foreach ($ad_ids as $one_ad_id) {
                    $failed_code = ADAnalysisModel::FAILED;
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_ad_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_ad_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$one_ad_id;
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = '修改失败，错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_ad_id);
            }
            unset($ad_ids);
        }

        //处理没有权限的
        if ($no_permission_ad_ids) {
            foreach ($no_permission_ad_ids as $ad_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }


    /**
     * @param int $media_type
     * @param array $data
     * @param array $toutiao_format_target
     * @return array
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $toutiao_format_target)
    {
        //存储到操作日志的单条数据
        $single_record_operate = [];
        //编辑类型，1：开关开启，2：开关关闭，3：调整预算，4：调整投放时段，5：修改广告组名，6：修改广告计划名，7：调整出价，8：百度ocpc出价
        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_SCHEDULE_TIME;

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        $all_ad_ids = [];
        foreach ($toutiao_format_target as $ad_infos) {
            $all_ad_ids = array_merge($all_ad_ids, $ad_infos);
        }

        $account_ids = array_keys($toutiao_format_target);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //无权限操作的广告id集合
        $no_permission_ad_ids = [];

        $promotion_model = new PromotionModel();

        foreach ($toutiao_format_target as $acc_id => $ad_ids) {
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$acc_id])) {
                $no_permission_ad_ids = array_merge(
                    $no_permission_ad_ids,
                    $ad_ids
                );
                continue;
            }
            //解决同一账号中ad_id可能超出的问题
            $ids_num = count($ad_ids);
            $loop_times = (int)ceil($ids_num / 10);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-9、10-19、20-29....
                $part_of_ad_ids = array_slice($ad_ids, ($i - 1) * 10, 10);
                $request_data['data'] = [];
                foreach ($part_of_ad_ids as $one_id) {
                    $request_data['data'][] = [
                        'promotion_id' => (int)$one_id,
                        'schedule_time' => $data['schedule_time'],
                    ];
                }
                try {
                    $response = $promotion_model->updatePromotionScheduleTime($request_data, (int)$acc_id, $access_token_info[$acc_id]->access_token);

                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response['errors']) && $response['errors']) {
                        foreach ($response['errors'] as $one_error) {
                            $failed_ad_ids[$one_error['error_message']][] = $one_error['promotion_id'];
                            $error_ad_ids[] = $one_error['promotion_id'];
                        }
                    }
                    $part_of_success = $response['promotion_ids'] ?? array_diff($part_of_ad_ids, $error_ad_ids);
                    //操作成功
                    //因为account_id相同的ad_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
                    $success_ad_ids = array_merge($success_ad_ids, $part_of_success);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_ad_ids[$failed_info])) {
                        $failed_ad_ids[$failed_info] = array_merge($part_of_ad_ids, $failed_ad_ids[$failed_info]);
                    } else {
                        $failed_ad_ids[$failed_info] = $part_of_ad_ids;
                    }
                }
            }
        }
        unset($item);

        //插入his表
        if ($success_ad_ids) {
            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $success_ad_ids,
                'schedule_time',
                $data['schedule_time'],
                $single_record_operate['insert_time']
            );
        }

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['ad_id']] = $item;
        }, $data['update']);

        //处理成功的
        if ($success_ad_ids) {
            foreach ($success_ad_ids as $ad_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $data['schedule_time'];

                $single_record_operate['edit_detail'] = '修改成功';

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }

        //处理失败的
        if ($failed_ad_ids) {
            foreach ($failed_ad_ids as $key => $ad_ids) {
                foreach ($ad_ids as $one_ad_id) {
                    $failed_code = ADAnalysisModel::FAILED;
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_ad_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_ad_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$one_ad_id;
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = '修改失败，错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_ad_id);
            }
            unset($ad_ids);
        }

        //处理没有权限的
        if ($no_permission_ad_ids) {
            foreach ($no_permission_ad_ids as $ad_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) &&
            (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoAdLogModel();
        $column = ['ad_id', 'ad_name', 'account_id', 'platform', $update_field];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        $all_ad_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }

            $all_ad_ids[] = $item->ad_id;
        }
        unset($item);
        $account_ids = array_flip(array_flip($account_ids));
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            $account_ids,
            null,
            $leader_permission
        );

        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回给前端的所有数据
        $return_data = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $request_type = 'budget';
        $opt_cn_name = '预算';
        $method = 'updatePromotionBudget';
        if ('cpa_bid' === $update_field) {
            $request_type = 'bid';
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BID;
            $opt_cn_name = '出价';
            $method = 'updatePromotionBid';
        }
        $toutiao_http_model = new PromotionModel();
        //有操作权限的ad_id集合
        $permission_ad_ids = [];
        //修改成功的数据集合
        $success_data = [];
        //定时执行的数据集合
        $timed_execution_data = [];
        //修改失败的数据集合
        $failed_data = [];
        //存储所有需要定时修改的数据
        $all_data = [];
        //即时修改到数据库的数据
        $all_update = [];

        foreach ($access_token as $value) {
            $request_data = [];
            $data = [];
            $ad_ids = [];
            $all_change_info = [];
            foreach ($account_info as $v) {
                if ((int)$value->account_id === (int)$v->account_id) {
                    //记录有权限的ad_id
                    $permission_ad_ids[] = $v->ad_id;
                    //保存原始值
                    $original_value = $v->$update_field;
                    if (1 === (int)$input['change_type']) {
                        $v->$update_field = $input['change_value'];
                    } elseif (2 === (int)$input['change_type']) {
                        $v->$update_field += number_format($v->$update_field * ($input['change_value'] / 100),
                            2, '.', '');
                    } elseif (3 === (int)$input['change_type']) {
                        $v->$update_field -= number_format($v->$update_field * ($input['change_value'] / 100),
                            2, '.', '');
                    } else {
                        throw new AppException('参数错误');
                    }
                    //组合请求的数据
                    $ad_ids[] = $single_data['promotion_id'] = (int)$v->ad_id;
                    $single_data[$request_type] = round($v->$update_field, 2);
                    $data[] = $single_data;
                    //记录每一个ad_id的原始值和修改后的值，用于之后组装操作日志数据
                    $single_change_info['ad_id'] = (int)$v->ad_id;
                    $single_change_info['account_id'] = $v->account_id;
                    $single_change_info['value'] = $v->$update_field;
                    $single_change_info['original_value'] = $original_value;
                    $all_change_info[(int)$v->ad_id] = $single_change_info;
                }
            }
            unset($v);
            if (!$data) {
                continue;
            }
            //即时修改，直接进行修改
            if (1 === (int)$input['execute_type']) {
                $data_num = count($data);
                $loop_times = (int)ceil($data_num / 10);
                //循环请求，每个account_id最多带10个数据
                for ($i = 1; $i <= $loop_times; $i++) {
                    $part_of_data = array_slice($data, ($i - 1) * 10, 10);
                    try {
                        $response_data = $toutiao_http_model->$method(
                            $part_of_data,
                            (int)$value->account_id,
                            $value->access_token
                        );
                        //部分失败
                        $error_ad_ids = [];
                        if (isset($response_data['errors']) && $response_data['errors']) {
                            foreach ($response_data['errors'] as $item) {
                                $failed_data[$item['error_message']][] = $all_change_info[$item['promotion_id']];
                                $error_ad_ids[] = $item['promotion_id'];
                                unset($all_change_info[$item['promotion_id']]);
                            }
                        }
                        //修改成功
                        $success_ad_ids = $response_data['promotion_ids'] ?? array_diff($ad_ids, $error_ad_ids);
                        if ($success_ad_ids) {
                            $all_update = array_merge($all_update, $success_ad_ids);
                        }
                        $success_data = array_merge($success_data, $all_change_info);
                    } catch (AppException $e) {
                        $response['message'] = $e->getMessage();
                        $response['code'] = $e->getCode();
                        //修改失败
                        //合并错误类型相同的信息之后一起处理
                        if (isset($failed_data[$response['message']])) {
                            $failed_data[$response['message']] = array_merge($all_change_info, $failed_data[$response['message']]);
                        } else {
                            $failed_data[$response['message']] = $all_change_info;
                        }
                    }
                }
            } elseif (0 === (int)$input['execute_type']) {
                //定时修改，将请求数据汇总之后放入RabbitMQ
                $timed_execution_data = array_merge($timed_execution_data, $all_change_info);
                //$request_data存入session信息,access_token消费队列时重新查
                $request_data['account_id'] = $value->account_id;
                $request_data['data'] = $data;
                $request_data['port_version'] = '2.0';
                $request_data['editor_id'] = $single_record_operate['editor_id'];
                $request_data['editor_name'] = $single_record_operate['editor_name'];
                $request_data['request_type'] = $request_type;
                $request_data['media_type'] = MediaType::TOUTIAO;
                $all_data[] = $request_data;
            }
        }

        if ($all_data) {
            $time = strtotime($input['execute_time']) - time();
            if ('bid' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBidTask($all_data, $time);
            } elseif ('budget' === $request_type) {
                (new ADAnalysisMQLogic())->produceDelayChangeBudgetTask($all_data, $time);
            }
        }

        //无权限的ad2_id
        $not_permission = array_diff($all_ad_ids, $permission_ad_ids);
        foreach ($account_info as $v) {
            //处理成功的
            if ($success_data) {
                foreach ($success_data as $change_info) {
                    if ((int)$change_info['ad_id'] !== (int)$v->ad_id ||
                        (int)$change_info['account_id'] !== (int)$v->account_id) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 1;
                    $single_return_data['value'] = (string)$change_info['value'];
                    $single_return_data['message'] = 'success';
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->ad_id;
                    $single_record_operate['edit_detail'] = $opt_cn_name . '修改成功，由[' .
                        $change_info['original_value'] . ']修改为[' . $change_info['value'] . ']';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($change_info);
            }

            //处理定时执行的
            if ($timed_execution_data) {
                foreach ($timed_execution_data as $one_data) {
                    if ((int)$one_data['ad_id'] !== (int)$v->ad_id ||
                        (int)$one_data['account_id'] !== (int)$v->account_id) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 1;
                    $single_return_data['value'] = $one_data['original_value'];
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->ad_id;
                    $single_return_data['message'] = $single_record_operate['edit_detail'] =
                        $opt_cn_name . '修改准备' . $input['execute_time'] . '定时执行，由[' . $one_data['original_value'] .
                        ']修改为[' . $one_data['value'] . ']';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_data);
            }

            //处理失败的
            if ($failed_data) {
                foreach ($failed_data as $key => $change_info) {
                    foreach ($change_info as $one_info) {
                        if ((int)$one_info['ad_id'] !== (int)$v->ad_id ||
                            (int)$one_info['account_id'] !== (int)$v->account_id) {
                            continue;
                        }
                        $single_return_data['status'] = $single_record_operate['status'] = 2;
                        $single_return_data['value'] = 0;
                        $err_info = json_decode($key, true);
                        $single_return_data['message'] = $err_info['message'] ?? $key;
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->ad_id;
                        $single_record_operate['edit_detail'] = $opt_cn_name . '由[' . $one_info['original_value'] . ']修改为[' . $one_info['value'] . ']修改失败，错误信息：' . $key;
                        $record_operate[] = $single_record_operate;
                        $return_data[] = $single_return_data;
                    }
                    unset($one_info);
                }
                unset($change_info);
            }

            //处理没有权限的
            if ($not_permission) {
                foreach ($not_permission as $ad_id) {
                    if ((int)$ad_id !== (int)$v->ad_id) {
                        continue;
                    }
                    $single_return_data['status'] = $single_record_operate['status'] = 2;
                    $single_return_data['value'] = 0;
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                    $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($ad_id);
            }
        }

        if ($all_update) {
            //组合要update的sql
            if (1 === (int)$input['change_type']) {
                $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, 2 ) )";
            } elseif (2 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } elseif (3 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, 2 ) )";
            } else {
                throw new AppException('参数错误');
            }
            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $all_update,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 处理队列中头条定时修改预算或出价的数据
     * @param int $media_type
     * @param array $all_toutiao_mq_data
     * @return void
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $all_toutiao_mq_data)
    {
        $toutiao_http_model = new PromotionModel();
        //头条二级表查询条件
        $condition = [];
        foreach ($all_toutiao_mq_data as $v) {
            foreach ($v['data'] as $item) {
                $condition[$v['account_id']][] = $item['promotion_id'];
            }
            unset($item);
        }
        unset($v);
        if (!$condition) {
            return;
        }
        $account_ids = array_keys($condition);
        $ad_info = (new OdsToutiaoAdLogModel())->getADInfoByAccountIdAndADId($condition);
        $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids);
        if ($ad_info->isEmpty() || $access_token_info->isEmpty()) {
            return;
        }
        //整合access_token方便后面使用
        $access_tokens = [];
        foreach ($access_token_info as $v) {
            $access_tokens[$v->account_id] = $v->access_token;
        }
        unset($v);

        $method_map = ['bid' => 'updatePromotionBid', 'budget' => 'updatePromotionBudget'];
        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');
        //存储到操作日志的所有数据
        $record_operate = [];

        $success_data = [];
        $failed_data = [];
        $all_update = [];
        foreach ($all_toutiao_mq_data as $v) {
            $request_type = $v['request_type'];
            $method = $method_map[$request_type];

            //把操作者和account_id信息塞进去
            $all_change_info = [];
            $datum = [];
            foreach ($v['data'] as $datum) {
                $datum['editor_id'] = $v['editor_id'];
                $datum['editor_name'] = $v['editor_name'];
                $datum['account_id'] = $v['account_id'];
                $all_change_info[$datum['promotion_id']] = $datum;
            }
            unset($datum);
            $data_num = count($v['data']);
            $loop_times = (int)ceil($data_num / 10);
            //循环请求，每个account_id最多带10个数据
            for ($i = 1; $i <= $loop_times; $i++) {
                $part_of_data = array_slice($v['data'], ($i - 1) * 10, 10);
                try {
                    $response_data = $toutiao_http_model->$method(
                        $part_of_data,
                        (int)$v['account_id'],
                        $access_tokens[$v['account_id']]
                    );
                    //部分失败
//                    $error_ad_ids = [];
                    if (isset($response_data['errors']) && $response_data['errors']) {
                        foreach ($response_data['errors'] as $item) {
                            $failed_data[$item['error_message']][] = $all_change_info[$item['promotion_id']];
//                            $error_ad_ids[] = $item['promotion_id'];
                            unset($all_change_info[$item['promotion_id']]);
                        }
                    }
                    //修改成功
                    $success_ad_ids = $response_data['promotion_ids'] ?? [];
                    if ($success_ad_ids) {
//                            $all_update[$request_data['advertiser_id']] = $success_ad_ids;
                        $all_update = array_merge($all_update, $success_ad_ids);
                    }
                    $success_data = array_merge($success_data, $all_change_info);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //修改失败
//                        $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的信息之后一起处理
                    if (isset($failed_data[$response['message']])) {
                        $failed_data[$response['message']] = array_merge($all_change_info, $failed_data[$response['message']]);
                    } else {
                        $failed_data[$response['message']] = $all_change_info;
                    }
                }
            }
        }
        $opt_cn_name = ['cpa_bid' => '出价', 'budget' => '预算'];
        //组合操作记录数据
        foreach ($ad_info as $v) {
            //处理成功的
            if ($success_data) {
                foreach ($success_data as $change_info) {
                    if ((int)$change_info['promotion_id'] !== (int)$v->ad_id ||
                        (int)$change_info['account_id'] !== (int)$v->account_id) {
                        continue;
                    }

                    if (isset($change_info['budget'])) {
                        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
                        $update_field = $change_info_index = 'budget';
                    } else {
                        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BID;
                        $update_field = 'cpa_bid';
                        $change_info_index = 'bid';
                    }
                    $single_record_operate['status'] = 1;
                    $single_record_operate['editor_name'] = $change_info['editor_name'];
                    $single_record_operate['editor_id'] = $change_info['editor_id'];
                    $single_record_operate['platform'] = $v->platform;
                    $single_record_operate['account_id'] = (int)$v->account_id;
                    $single_record_operate['ad_id'] = (int)$v->ad_id;
                    $single_record_operate['edit_detail'] = $opt_cn_name[$update_field] . '定时修改成功，由[' . $v->$update_field . ']修改为[' . $change_info[$change_info_index] . ']';
                    $record_operate[] = $single_record_operate;

                }
                unset($change_info);
            }

            //处理失败的
            if ($failed_data) {
                foreach ($failed_data as $key => $change_info) {
                    foreach ($change_info as $one_info) {
                        if ((int)$one_info['promotion_id'] !== (int)$v->ad_id ||
                            (int)$one_info['account_id'] !== (int)$v->account_id) {
                            continue;
                        }

                        if (isset($one_info['budget'])) {
                            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
                            $update_field = $change_info_index = 'budget';
                        } else {
                            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BID;
                            $update_field = 'cpa_bid';
                            $change_info_index = 'bid';
                        }
                        $single_record_operate['status'] = 2;
                        $single_record_operate['editor_name'] = $one_info['editor_name'];
                        $single_record_operate['editor_id'] = $one_info['editor_id'];
                        $single_record_operate['platform'] = $v->platform;
                        $single_record_operate['account_id'] = (int)$v->account_id;
                        $single_record_operate['ad_id'] = (int)$v->ad_id;
                        $error_info = json_decode($key, true);
                        $error_msg = $error_info['message'] ?? $key;
                        $single_record_operate['edit_detail'] = $opt_cn_name[$update_field] . '由[' . $v->$update_field . ']修改为[' . $one_info[$change_info_index] . '] 定时修改失败，失败信息：' . $error_msg;
                        $record_operate[] = $single_record_operate;
                    }
                    unset($one_info);
                }
                unset($change_info);
            }
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);
    }

    /**
     * 基本报表-批量修改深度优化出价或ROI
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoAdLogModel();
        $column = ['ad_id', 'account_id', 'platform', 'ad_name', 'deep_bid_type', 'deep_cpabid', 'roi_goal'];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new PromotionModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        if ('deep_bid' === $update_field) {
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_DEEP_BID;
            $update_field = 'deep_cpabid';
            $opt_detail = '深度转化出价';
            $decimal = 2;
        } else {
            //修改ROI
            $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_ROI;
            $update_field = 'roi_goal';
            $opt_detail = 'ROI';
            $decimal = 4;
        }

        $single_record_operate['ad_level'] = 2;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (string)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->ad_id;
//                $single_record_operate['ad_name'] = $v->ad_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            try {
                //2.0广告为全量更新方式，先获取广告参数
                $filtering = ['ids' => [(int)$v->ad_id]];
                $promotion_data_list = $toutiao_http_model->info(
                    (int)$v->account_id,
                    $access_tokens[$v->account_id],
                    [],
                    $filtering
                );
                $promotion_data = Helpers::filterArray($promotion_data_list['list'][0]);
                $request_data['name'] = $promotion_data['promotion_name'];
                if (isset($promotion_data['promotion_materials'])) {
                    $request_data['promotion_materials'] = $promotion_data['promotion_materials'];
                } else {
                    $request_data['promotion_materials'] = (object)[];
                }

                if (isset($promotion_data['budget'])) {
                    $request_data['budget'] = $promotion_data['budget'];
                }
                if (isset($promotion_data['cpa_bid'])) {
                    $request_data['cpa_bid'] = $promotion_data['cpa_bid'];
                }

                $original_value = $v->$update_field;
                if (1 === intval($input['change_type'])) {
                    $v->$update_field = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->$update_field += number_format($v->$update_field * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->$update_field -= number_format($v->$update_field * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }

                $v->$update_field = round($v->$update_field, $decimal);
                if ('deep_cpabid' === $update_field) {

                    if ($v->$update_field < 0.1 || $v->$update_field > 10000) {
                        throw new AppException('深度优化出价允许范围为：0.1-10000');
                    }
                    if ($v->deep_bid_type !== 'DEEP_BID_MIN') {
                        throw new AppException('当前深度优化方式不允许修改深度优化出价');
                    }
                    $request_data['deep_cpabid'] = $v->$update_field;
                    if (isset($promotion_data['roi_goal']) && $promotion_data['roi_goal']) {
                        $request_data['roi_goal'] = $promotion_data['roi_goal'];
                    }

                }
                if ('roi_goal' === $update_field) {
                    if (($v->$update_field <= 0 || $v->$update_field > 5)) {
                        throw new AppException('深度转化ROI系数允许范围为：0.1-10000');
                    }
                    if ($v->deep_bid_type !== 'ROI_COEFFICIENT') {
                        throw new AppException('当前深度优化方式不允许修改深度转化ROI系数');
                    }
                    $request_data['roi_goal'] = $v->$update_field;
                    if (isset($promotion_data['deep_cpabid']) && $promotion_data['deep_cpabid']) {
                        $request_data['deep_cpabid'] = $promotion_data['deep_cpabid'];
                    }
                }
                //向头条发起请求
                $toutiao_http_model->updatePromotion(
                    $request_data,
                    (int)$v->ad_id,
                    (int)$v->account_id,
                    $access_tokens[$v->account_id]
                );
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = (string)$v->$update_field;
//                $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->ad_name;
//                    $update_data[$v->account_id][] = (int)$v->ad_id;
                $update_data[] = (int)$v->ad_id;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                    ']修改为[' . $v->$update_field . ']' . "，修改成功";
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' .
                    json_encode($response, JSON_UNESCAPED_UNICODE);
//                $single_record_operate['ad_name'] = $v->ad_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (string)$v->ad_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (1 === (int)$input['change_type']) {
                $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field + $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$input['change_type']) {
                $update_set =
                    "CAST ( ( $update_field - $update_field * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new OdsToutiaoADHisLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                $update_field,
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表-批量修改一级预算
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoCampaignLogModel();
        $column = ['campaign_id', 'account_id', 'platform', 'campaign_name', 'budget'];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new ProjectModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BUDGET;
        $opt_detail = '头条一级广告预算';
        $decimal = 2;

        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->campaign_id;
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            try {
                $original_value = $v->budget;
                if (0 === (int)$input['change_value']) {
                    $request_data['delivery_setting']['budget_mode'] = 'BUDGET_MODE_INFINITE';
                    $request_data['delivery_setting']['budget'] = $v->budget = 0;
                } else {
                    if (1 === intval($input['change_type'])) {
                        $v->budget = $input['change_value'];
                    } elseif (2 === intval($input['change_type'])) {
                        $v->budget += number_format($v->budget * ($input['change_value'] / 100),
                            $decimal, '.', '');
                    } elseif (3 === intval($input['change_type'])) {
                        $v->budget -= number_format($v->budget * ($input['change_value'] / 100),
                            $decimal, '.', '');
                    } else {
                        throw new AppException('参数错误');
                    }
                    $request_data['delivery_setting']['budget_mode'] = 'BUDGET_MODE_DAY';
                    $request_data['delivery_setting']['budget'] = round($v->budget, $decimal);
                }

                //向头条发起请求
                $toutiao_http_model->updateProject(
                    $request_data,
                    (int)$v->campaign_id,
                    (int)$v->account_id,
                    $access_tokens[$v->account_id]
                );
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->budget;
                $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->campaign_name;
                $update_data[] = (int)$v->campaign_id;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = 0 === $v->budget ? '无限' : $v->budget;
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' . json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (string)$v->campaign_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (0 === (int)$input['change_value']) {
                $update_set = 0;
            } else {
                if (1 === (int)$input['change_type']) {
                    $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
                } elseif (2 === (int)$input['change_type']) {
                    $update_set =
                        "CAST ( ( budget + budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
                } elseif (3 === (int)$input['change_type']) {
                    $update_set =
                        "CAST ( ( budget - budget * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
                } else {
                    throw new AppException('参数错误');
                }
            }

            (new V2OdsToutiaoCampaignLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                ['budget', 'budget_mode'],
                [$update_set, $request_data['delivery_setting']['budget_mode']],
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表-批量修改一级转化出价
     * @param int $media_type
     * @param array $input
     * @param array $toutiao_format_target
     * @param string $update_field
     * @return array|mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $input, array $toutiao_format_target, string $update_field)
    {
        if ((2 === (int)$input['change_type'] || 3 === (int)$input['change_type']) && (int)$input['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
        $toutiao_model = new OdsToutiaoCampaignLogModel();
        $column = ['campaign_id', 'account_id', 'platform', 'campaign_name', 'cpa_bid'];
        $account_info = $toutiao_model->getAccountId($toutiao_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }
        $account_ids = [];
        foreach ($account_info as $item) {
            if (!in_array($item->account_id, $account_ids)) {
                $account_ids[] = $item->account_id;
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids,
            null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new ProjectModel();
        $request_data = [];
        //单条操作记录
        $single_record_operate = [];

        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_BID;
        $opt_detail = '头条一级转化出价';
        $decimal = 2;

        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = $media_type;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //即时更新到数据库的数据
        $update_data = [];
        foreach ($account_info as $v) {
            if (!isset($access_tokens[$v->account_id])) {
                $single_record_operate['platform'] = $v->platform;
                $single_record_operate['account_id'] = (int)$v->account_id;
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$v->campaign_id;
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }
            try {
                $original_value = $v->cpa_bid;
//                if (0 === (int)$input['change_value']) {
//                    $request_data['delivery_setting']['budget_mode'] = 'BUDGET_MODE_INFINITE';
//                    $request_data['delivery_setting']['budget'] = $v->budget = 0;
//                } else {
                if (1 === intval($input['change_type'])) {
                    $v->cpa_bid = $input['change_value'];
                } elseif (2 === intval($input['change_type'])) {
                    $v->cpa_bid += number_format($v->cpa_bid * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } elseif (3 === intval($input['change_type'])) {
                    $v->cpa_bid -= number_format($v->cpa_bid * ($input['change_value'] / 100),
                        $decimal, '.', '');
                } else {
                    throw new AppException('参数错误');
                }
                $request_data['delivery_setting']['cpa_bid'] = round($v->cpa_bid, $decimal);
//                }

                //向头条发起请求
                $toutiao_http_model->updateProject(
                    $request_data,
                    (int)$v->campaign_id,
                    (int)$v->account_id,
                    $access_tokens[$v->account_id]
                );
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $v->cpa_bid;
                $single_record_operate['ad_name'] = $input['ad_name'] ?? $v->campaign_name;
                $update_data[] = (int)$v->campaign_id;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $change_value = 0 === $v->cpa_bid ? '无限' : $v->cpa_bid;
                $original_value = (int)$original_value ?: '无限';
                $single_record_operate['edit_detail'] = $opt_detail . "由[" . $original_value .
                    ']修改为[' . $change_value . ']' . "，修改成功";
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $single_record_operate['edit_detail'] = $opt_detail . '修改失败，错误信息：' . json_encode($response, JSON_UNESCAPED_UNICODE);
                $single_record_operate['ad_name'] = $v->campaign_name;
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
            }
            $single_record_operate['account_id'] = (int)$v->account_id;
            $single_record_operate['platform'] = $v->platform;
            $single_record_operate['ad_id'] = $single_return_data['ad_id'] = (string)$v->campaign_id;

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);
        //写入his表
        if ($update_data) {
            if (0 === (int)$input['change_value']) {
                $update_set = 0;
            } else {
                if (1 === (int)$input['change_type']) {
                    $update_set = "CAST ( {$input['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
                } elseif (2 === (int)$input['change_type']) {
                    $update_set =
                        "CAST ( ( cpa_bid + cpa_bid * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
                } elseif (3 === (int)$input['change_type']) {
                    $update_set =
                        "CAST ( ( cpa_bid - cpa_bid * ({$input['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
                } else {
                    throw new AppException('参数错误');
                }
            }

            (new V2OdsToutiaoCampaignLogModel())->getLatestByADInfoAndInsert(
                $update_data,
                'cpa_bid',
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }

    /**
     * 基本报表-批量修改一级ROI系数
     * @param array $data
     * @param array $ad_format_target
     * @return array|mixed
     */
    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        if ((2 === (int)$data['change_type'] || 3 === (int)$data['change_type']) && (int)$data['change_value'] > 100) {
            throw new AppException('增加减少百分比禁止大于100');
        }
//        if ((int)$data['change_type'] === 1 && ($data['change_value'] < 0.01 || $data['change_value'] > 5)) {
//            throw new AppException('ROI系数允许范围为：0.01-5');
//        }

        $toutiao_model = new OdsToutiaoCampaignLogModel();
        $column = ['campaign_id', 'account_id', 'platform', 'campaign_name', 'roi_goal'];
        $account_info = $toutiao_model->getAccountId($ad_format_target, $column);
        if ($account_info->isEmpty()) {
            throw new AppException('修改失败');
        }

        // 准备修改的数据，按[account_id][campaign_id]存储
        $account_ad1_data = [];
        foreach ($account_info as $item) {
            $account_ad1_data[$item->account_id][$item->campaign_id] = $item;
        }
        $account_ids = array_keys($account_ad1_data);
        //获取access_token
        $leader_permission = $data['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission)->keyBy('account_id');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回前端的单条数据
        $single_return_data = [];
        //返回到前端的数据
        $return_data = [];

        $all_ad_ids = [];
        foreach ($ad_format_target as $ad_infos) {
            $all_ad_ids = array_merge($all_ad_ids, $ad_infos);
        }

        //存储到操作日志的单条数据
        $single_record_operate = [];
        $single_record_operate['edit_type'] = ADAnalysisModel::CHANGE_FIRST_CLASS_ROI;
        $single_record_operate['ad_level'] = 1;
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $data['editor_id'];
        $single_record_operate['editor_name'] = $data['editor_name'];
        //将时间提前，防止ad_modify_time比从媒体拉到的时间晚导致更新不准确
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //向媒体发起请求后成功的ad_id集合
        $success_ad_ids = [];
        //向媒体发起请求后失败的ad_id集合
        $failed_ad_ids = [];
        //无权限操作的广告id集合
        $no_permission_ad_ids = [];
        // 小数点后四位
        $decimal = 4;

        $project_model = new ProjectModel();

        foreach ($ad_format_target as $acc_id => $ad_ids) {
            //access_token和account_id找到对应关系
            if (!isset($access_token_info[$acc_id])) {
                $no_permission_ad_ids = array_merge(
                    $no_permission_ad_ids,
                    $ad_ids
                );
                continue;
            }
            //解决同一账号中ad_id可能超出的问题
            $ids_num = count($ad_ids);
            $loop_times = (int)ceil($ids_num / 10);
            //循环请求
            for ($i = 1; $i <= $loop_times; $i++) {
                //0-9、10-19、20-29....
                $part_of_ad_ids = array_slice($ad_ids, ($i - 1) * 10, 10);
                $request_data = [];
                foreach ($part_of_ad_ids as $one_id) {
                    // 记录roi初始值
                    $account_ad1_data[$acc_id][$one_id]->original_goal = $account_ad1_data[$acc_id][$one_id]->roi_goal;
                    if (1 === intval($data['change_type'])) {
                        $account_ad1_data[$acc_id][$one_id]->roi_goal = $data['change_value'];
                    } elseif (2 === intval($data['change_type'])) {
                        $account_ad1_data[$acc_id][$one_id]->roi_goal += (float)number_format($account_ad1_data[$acc_id][$one_id]->original_goal * ($data['change_value'] / 100),
                            $decimal, '.', '');
                    } elseif (3 === intval($data['change_type'])) {
                        $account_ad1_data[$acc_id][$one_id]->roi_goal -= number_format($account_ad1_data[$acc_id][$one_id]->original_goal * ($data['change_value'] / 100),
                            $decimal, '.', '');
                    } else {
                        throw new AppException('参数错误');
                    }
                    $request_data['data'][] = [
                        'project_id' => (int)$one_id,
                        'roi_goal' => (float)$account_ad1_data[$acc_id][$one_id]->roi_goal
                    ];
                }
                try {
                    $response = $project_model->updateRoiGoal($request_data, (int)$acc_id, $access_token_info[$acc_id]->access_token);

                    //部分失败
                    $error_ad_ids = [];
                    if (isset($response['errors']) && $response['errors']) {
                        foreach ($response['errors'] as $one_error) {
                            $failed_ad_ids[$one_error['error_message']][] = $one_error['project_id'];
                            $error_ad_ids[] = $one_error['project_id'];
                        }
                    }
                    $part_of_success = $response['project_ids'] ?? array_diff($part_of_ad_ids, $error_ad_ids);
                    //操作成功
                    //因为account_id相同的ad_id合到同一个数组做了批量请求，之后为了操作记录要再拆开
                    $success_ad_ids = array_merge($success_ad_ids, $part_of_success);
                } catch (AppException $e) {
                    $response['message'] = $e->getMessage();
                    $response['code'] = $e->getCode();
                    //操作失败
                    $failed_info = json_encode($response, JSON_UNESCAPED_UNICODE);
                    //合并错误类型相同的ad_id之后一起处理
                    if (isset($failed_ad_ids[$failed_info])) {
                        $failed_ad_ids[$failed_info] = array_merge($part_of_ad_ids, $failed_ad_ids[$failed_info]);
                    } else {
                        $failed_ad_ids[$failed_info] = $part_of_ad_ids;
                    }
                }
            }
        }

        //插入his表
        if ($success_ad_ids) {

            if (1 === (int)$data['change_type']) {
                $update_set = "CAST ( {$data['change_value']} AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (2 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( roi_goal + roi_goal * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } elseif (3 === (int)$data['change_type']) {
                $update_set =
                    "CAST ( ( roi_goal - roi_goal * ({$data['change_value']} / 100) ) AS DECIMAL ( 12, " . $decimal . " ) )";
            } else {
                throw new AppException('参数错误');
            }

            (new V2OdsToutiaoCampaignLogModel())->getLatestByADInfoAndInsert(
                $success_ad_ids,
                'roi_goal',
                $update_set,
                $single_record_operate['insert_time']
            );
        }

        //整合数据格式， 减少遍历次数
        $opt_data = [];
        array_map(function ($item) use (&$opt_data) {
            $opt_data[$item['ad_id']] = $item;
        }, $data['update']);

        $opt_detail = '头条一级roi系数';

        //处理成功的
        if ($success_ad_ids) {
            foreach ($success_ad_ids as $ad_id) {
                $success_code = ADAnalysisModel::SUCCESS;
                $single_return_data['status'] = $single_record_operate['status'] = $success_code;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = $account_ad1_data[(string)$opt_data[$ad_id]['account_id']][$ad_id]->roi_goal;

                $single_record_operate['edit_detail'] = $opt_detail . "由[" . $account_ad1_data[(string)$opt_data[$ad_id]['account_id']][$ad_id]->original_goal .
                    ']修改为[' . $account_ad1_data[(string)$opt_data[$ad_id]['account_id']][$ad_id]->roi_goal . ']' . "，修改成功";

                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }

        //处理失败的
        if ($failed_ad_ids) {
            foreach ($failed_ad_ids as $key => $ad_ids) {
                foreach ($ad_ids as $one_ad_id) {
                    $failed_code = ADAnalysisModel::FAILED;
                    $single_return_data['status'] = $single_record_operate['status'] = $failed_code;
                    $single_record_operate['platform'] = $opt_data[$one_ad_id]['platform'];
                    $single_record_operate['account_id'] = (string)$opt_data[$one_ad_id]['account_id'];
                    $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$one_ad_id;
                    $err_info = json_decode($key, true);
                    $single_return_data['message'] = $err_info['message'] ?? $key;
                    $single_return_data['value'] = 0;

                    $single_record_operate['edit_detail'] = $opt_detail . ' 修改失败，错误信息：' . $key;
                    $record_operate[] = $single_record_operate;
                    $return_data[] = $single_return_data;
                }
                unset($one_ad_id);
            }
            unset($ad_ids);
        }

        //处理没有权限的
        if ($no_permission_ad_ids) {
            foreach ($no_permission_ad_ids as $ad_id) {
                $single_return_data['status'] = $single_record_operate['status'] = ADAnalysisModel::FAILED;
                $single_record_operate['platform'] = $opt_data[$ad_id]['platform'];
                $single_record_operate['account_id'] = (string)$opt_data[$ad_id]['account_id'];
                $single_return_data['ad_id'] = $single_record_operate['ad_id'] = (string)$ad_id;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
            }
            unset($ad_id);
        }
        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        return $return_data;
    }


    /**
     * 获取渠道组
     * @param ADTaskParam $param
     * @return int
     */
    public function getAgentGroup(ADTaskParam $param): int
    {
        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        if ($other_setting->marketing_goal == ToutiaoEnum::MARKETING_GOAL_LIVE) {
            return AgentGroup::TOUTIAO_LIVE;
        }

        if ($other_setting->origin_ad_type == 'star') {
            return AgentGroup::TOUTIAO_XINGTU_LIANTOU;
        }

        if ($other_setting->origin_ad_type == 'origin') {
            return AgentGroup::TOUTIAO_ORIGIN;
        }

        if ($other_setting->origin_ad_type == 'hot') {
            return AgentGroup::TOUTIAO_HOT;
        }

        if (in_array(ToutiaoEnum::INVENTORY_TYPE_UNION, $setting->inventory_type)) {
            return AgentGroup::TOUTIAO_UNION;
        }

        return AgentGroup::TOUTIAO;
    }

    /**
     * 通过AD1的名称获取AD1上的广告位
     * @param $name
     * @param MediaAccountInfoParam $account_param
     * @return int
     */
    public function getSiteIdByAD1Name($name, MediaAccountInfoParam $account_param)
    {
        $response_data = $this->getAD1InfoByName($name, $account_param->account_id, $account_param->access_token);
        $campaign_id = $response_data['list'][0]['project_id'] ?? 0;
        $download_url = $response_data['list'][0]['download_url'] ?? "";

        if (empty($campaign_id)) {
            $adb_data = (new OdsToutiaoCampaignLogModel())->getAD1InfoByName($name, $account_param->account_id);
            if ($adb_data && $adb_data->campaign_id) {
                $campaign_id = $adb_data->campaign_id;

                $filters = ['ids' => [$campaign_id]];
                $new_response_data = (new ProjectModel())->info($account_param->account_id, $account_param->access_token, [
                    'project_id',
                    'download_url',
                    'optimize_goal',
                    'delivery_setting'
                ], $filters, 1, 1);

                $download_url = $new_response_data['list'][0]['download_url'] ?? "";
            }
        }

        if (empty($campaign_id)) {
            return 0;
        }

        if (!$download_url) {
            return 0;
        }

        /**
         * 下载链接示例：https://apps.bytesfield.com/download/extend/cur/9f224b24833ed7e609fbd388a14206ad8369d2d6/********
         * 最后路径为广告位ID
         */
        return (int)basename(parse_url($download_url, PHP_URL_PATH));
    }

    /**
     * 广告一键起量
     * @param array $data
     * @return void
     * @throws Exception
     */
    public function updateADAnalysisADRaise(array $data): void
    {

        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission(
            [$data['account_id']], null, $leader_permission);

        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 2;
        $record_operate['media_type'] = MediaType::TOUTIAO;
        $record_operate['editor_id'] = $data['editor_id'];
        $record_operate['editor_name'] = $data['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        $record_operate['edit_type'] = ADAnalysisModel::CLICK_AD_RAISE;
        $record_operate['platform'] = $data['platform'];
        $record_operate['account_id'] = (int)$data['account_id'];
        $record_operate['ad_id'] = (string)$data['ad_id'];
        $record_operate['status'] = 2;

        if ($access_token->isNotEmpty()) {
            $toutiao_http_model = new PromotionRaiseModel();
            $access_token = $access_token->pop()->access_token;
            $request_data['advertiser_id'] = (int)$data['account_id'];
            $request_data['promotion_id'] = (int)$data['ad_id'];
            $request_data['raise_info'] = $data['raise_info'];

            try {
                $toutiao_http_model->setPromotionRaise($request_data, $access_token);
                //修改成功
                $record_operate['status'] = 1;
                $record_operate['edit_detail'] = "广告起量修改成功";
                //记录操作日志
                (new ADAnalysisOperateLogModel())->add($record_operate);
                //成功则写入起量状态his表
                try {
                    $raise_status = $this->getADRaiseStatus($data);
                    $his_data['account_id'] = (int)$data['account_id'];
                    $his_data['ad_id'] = (string)$data['ad_id'];
                    $his_data['raise_status'] = $raise_status;
                    $his_data['is_virtual'] = 1;
                    (new OdsToutiaoAdRaiseStatusHisLogModel())->add($his_data);
                } catch (AppException $e) {
                }

                //调用大数据接口刷新数据
                $trino_model = new ToutiaoTaskModel();
                $trino_model->raiseVersion($data['account_id'], $data['ad_id']);
                $trino_model->raiseVersionHis($data['account_id']);

            } catch (AppException $e) {
                $response['message'] = $e->getMessage();
                $response['code'] = $e->getCode();
                $record_operate['edit_detail'] = "广告起量修改失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
                //记录操作日志
                (new ADAnalysisOperateLogModel())->add($record_operate);
                throw new AppException('广告起量修改失败，错误信息: ' . $e->getMessage());
            }
        } else {
            //没有权限操作
            throw new AppException('无操作权限');
        }
    }

    /**
     * 获取广告起量状态
     * @param array $data
     * @return string
     * @throws AppException
     */
    public function getADRaiseStatus(array $data): string
    {
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountId($data['account_id']);
        if (!$access_token) {
            throw new AppException('未找到access_token');
        }
        $raise_status_data = (new PromotionRaiseModel())->getPromotionRaiseStatus($data, $access_token);
        if (isset($raise_status_data['data'][0]['status']) && $raise_status_data['data'][0]['status']) {
            $raise_status = $raise_status_data['data'][0]['status'];
        } elseif (isset($raise_status_data['error_list'][0]['error_reason']) && $raise_status_data['error_list'][0]['error_reason']) {
            throw new AppException('获取失败: ' . $raise_status_data['error_list'][0]['error_reason']);
        } else {
            throw new AppException('获取失败');
        }

        return $raise_status;
    }

    /**
     * 获取广告起量计划
     * @param array $data
     * @return array
     * @throws AppException
     */
    public function getADRaisePlan(array $data): array
    {
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountId($data['account_id']);
        if (!$access_token) {
            throw new AppException('未找到access_token');
        }
        $raise_plan = (new PromotionRaiseModel())->getPromotionRaisePlan($data, $access_token);

        return $raise_plan['list'][0]['raise_info'];
    }

    /**
     * 关闭广告起量
     * @param array $data
     * @throws AppException
     */
    public function stopRaisingAD(array $data)
    {
        $access_token = (new MediaAccountModel())->getAccessTokenByAccountId($data['account_id']);
        if (!$access_token) {
            throw new AppException('未找到access_token');
        }
        //记录操作日志
        $record_operate = [];
        $record_operate['ad_level'] = 2;
        $record_operate['media_type'] = MediaType::TOUTIAO;
        $record_operate['editor_id'] = $data['editor_id'];
        $record_operate['editor_name'] = $data['editor_name'];
        $record_operate['insert_time'] = date('Y-m-d H:i:s');
        $record_operate['edit_type'] = ADAnalysisModel::STOP_AD_RAISE;
        $record_operate['platform'] = $data['platform'];
        $record_operate['account_id'] = (int)$data['account_id'];
        $record_operate['ad_id'] = (string)$data['ad_id'];
        $record_operate['status'] = 2;
        try {
            $request_data['advertiser_id'] = (int)$data['account_id'];
            $request_data['promotion_ids'] = [(int)$data['ad_id']];
            (new PromotionRaiseModel())->stopRaisingPromotion($request_data, $access_token);
            $record_operate['edit_detail'] = "广告起量关闭成功";
            $record_operate['status'] = 1;
            //记录操作日志
            (new ADAnalysisOperateLogModel())->add($record_operate);
            //成功则写入his
            try {
                $raise_status = $this->getADRaiseStatus($data);
                $his_data['account_id'] = (int)$data['account_id'];
                $his_data['ad_id'] = (string)$data['ad_id'];
                $his_data['raise_status'] = $raise_status;
                $his_data['is_virtual'] = 1;
                (new OdsToutiaoAdRaiseStatusHisLogModel())->add($his_data);
            } catch (AppException $e) {
            }
        } catch (AppException $e) {
            $response['message'] = $e->getMessage();
            $response['code'] = $e->getCode();
            $record_operate['edit_detail'] = "广告起量关闭失败，错误信息：" . json_encode($response, JSON_UNESCAPED_UNICODE);
            //记录操作日志
            (new ADAnalysisOperateLogModel())->add($record_operate);
            throw new AppException('广告起量关闭失败，错误信息: ' . $e->getMessage());
        }

    }

    /**
     * 获取起量报告
     * @param array $data
     * @return array
     */
    public function getADRaiseReport(array $data): array
    {
        //调用大数据接口刷新数据
        $trino_model = new ToutiaoTaskModel();
        $trino_model->setAsync(false);
        $trino_model->raiseVersion($data['account_id'], $data['ad_id']);
        $trino_model->raiseVersionHis($data['account_id']);

        return (new OdsToutiaoAdRaiseVersionLogModel)->getADRaiseReport($data)->toArray();
    }

    /**
     * 基本报表创建一键起量任务
     * @param array $input
     * @return array|mixed
     */
    public function createADAnalysisMaterialClearTask(array $input)
    {
        $account_ids = [];
        foreach ($input['data'] as $item) {
            if (!in_array($item['account_id'], $account_ids)) {
                $account_ids[] = $item['account_id'];
            }
        }
        unset($item);
        //获取access_token
        $leader_permission = $input['leader_permission'] ?? (new PermissionLogic())->getLoginUserLeaderPermission();
        $access_token_info = (new MediaAccountModel())->getAccessTokenByAccountIdsInjectPermission($account_ids, null, $leader_permission);

        $access_tokens = [];
        foreach ($access_token_info as $item) {
            $access_tokens[$item->account_id] = $item->access_token;
        }
        unset($item);
        $toutiao_http_model = new VideoModel();
        $request_data['material_source'] = 'AD';
        if (isset($input['clear_material_types']) && $input['clear_material_types']) {
            $request_data['clear_material_types'] = $input['clear_material_types'];
        }
        if (isset($input['create_time_upper']) && $input['create_time_upper']) {
            $request_data['create_time_upper'] = $input['create_time_upper'];
        }
        if (isset($input['start_time']) && $input['start_time']) {
            $request_data['start_time'] = $input['start_time'];
        }
        if (isset($input['end_time']) && $input['end_time']) {
            $request_data['end_time'] = $input['end_time'];
        }
        if (isset($input['convert']) && $input['convert']) {
            $request_data['convert'] = (int)$input['convert'];
        }
        if (isset($input['cost']) && $input['cost']) {
            $request_data['cost'] = (float)$input['cost'];
        }
        //单条操作记录
        $single_record_operate = [];
        $single_record_operate['edit_type'] = ADAnalysisModel::CREATE_MATERIAL_CLEAR_TASK;

        $single_record_operate['ad_level'] = 0;
        $single_record_operate['media_type'] = MediaType::TOUTIAO;
        $single_record_operate['editor_id'] = $input['editor_id'];
        $single_record_operate['editor_name'] = $input['editor_name'];
        $single_record_operate['insert_time'] = date('Y-m-d H:i:s');

        //存储到操作日志的所有数据
        $record_operate = [];
        //返回给前端的数据
        $return_data = [];
        //异步调用Trino拉取数据的数据集
        $update_data = [];
        $all_update_data = [];
        $part_of_success = false;
        foreach ($input['data'] as $v) {
            if (!isset($access_tokens[$v['account_id']])) {
                $single_record_operate['platform'] = $v['platform'];
                $single_record_operate['account_id'] = (string)$v['account_id'];
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $single_return_data['message'] = $single_record_operate['edit_detail'] = '当前用户无操作权限';
                $single_return_data['value'] = 0;
                $record_operate[] = $single_record_operate;
                $return_data[] = $single_return_data;
                continue;
            }

            try {
                $result = $toutiao_http_model->createMaterialClearTask(
                    (int)$v['account_id'],
                    $access_tokens[$v['account_id']],
                    $request_data
                );

                if (!isset($result['clear_id'])) {
                    throw new AppException('媒体返回参数错误');
                }
                $update_data['account_id'] = (string)$v['account_id'];
                $update_data['clear_id'] = (string)$result['clear_id'];
                $all_update_data[] = $update_data;
                //修改成功
                $single_return_data['message'] = 'success';
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 1;
                $single_record_operate['edit_detail'] = $result['clear_id'];
            } catch (AppException $e) {
                $single_return_data['message'] = $e->getMessage();
                $single_record_operate['edit_detail'] = '操作失败，错误信息：' . $e->getMessage();
                $single_return_data['value'] = 0;
                $single_record_operate['status'] = $single_return_data['status'] = 2;
                $part_of_success = true;
            }
            $single_record_operate['account_id'] = (int)$v['account_id'];
            $single_record_operate['platform'] = $v['platform'];

            $record_operate[] = $single_record_operate;
            $return_data[] = $single_return_data;
        }
        unset($v);

        //记录操作日志
        (new ADAnalysisOperateLogModel())->add($record_operate);

        //调用Trino接口拉取起量任务结果
        (new ToutiaoTaskModel())->materialClearTaskResult($all_update_data);

        return [
            'return_data' => $return_data,
            'part_of_success' => $part_of_success
        ];
    }
}
