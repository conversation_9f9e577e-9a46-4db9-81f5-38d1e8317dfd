<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Constant\ToutiaoEnum;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialThemeModel;
use App\Param\ADServing\AbstractADSettingContentParam;
use App\Param\ADServing\Toutiao\Basics\ADSettingContentParam;

class MaterialThemeFilterService
{
    /**
     * @param array $material_file_id_list
     * @return array
     */
    static public function inCorrectDownload(array $material_file_id_list)
    {
        $result = [];
        $material_file_list = (new MaterialFileModel())->getListOfThemeInfoByIds($material_file_id_list);
        foreach ($material_file_list as $file) {
            if (((int)($file->theme_id / 1000)) == 87) {
                $result[$file->id] = "素材文件id:$file->id,{$file->filename}属于原生加热题材，不允许下载";
            }
        }
        return $result;
    }

    /**
     * @param $media_type
     * @param array $material_file_id_list
     * @param AbstractADSettingContentParam $setting
     * @return array
     */
    static public function inCorrectInventoryType($media_type, array $material_file_id_list, AbstractADSettingContentParam $setting)
    {
        $result = [];
        $material_file_list = (new MaterialFileModel())->getListOfThemeInfoByIds($material_file_id_list);
        foreach ($material_file_list as $file) {
            if (((int)($file->theme_id / 1000)) == 87 || in_array((int)($file->theme_id), [171010, 173010])) {
                if ($media_type != MediaType::TOUTIAO) {
                    $result[$file->id] = false;
                    continue;
                }

                /* @var ADSettingContentParam $setting */
                $inventory_type = $setting->inventory_type;
                if (!(count($inventory_type) == 1 && in_array(ToutiaoEnum::INVENTORY_AWEME_FEED, $inventory_type))) {
                    $result[$file->id] = false;
                }
            }
        }
        return $result;
    }

    /**
     * @param $platform
     * @param int $game_id
     * @param array $material_file_id_list
     * @return array
     */
    static public function inCorrectGame($platform, int $game_id, array $material_file_id_list = [])
    {
        $result = [];
        $material_file_list = (new MaterialFileModel())->getListOfThemeInfoByIds($material_file_id_list);
        $all_theme_pid = [];
        foreach ($material_file_list as $file) {
            $all_theme_pid[$file->platform][] = (int)($file->theme_id / 1000);
        }

        $all_theme_info_list = (new MaterialThemeModel())->getListByIdAndPlatform($all_theme_pid);

        $game_info = (new V2DimGameIdModel())->getDataByGameId($platform, $game_id);

        $root_game_id = $game_info->root_game_id;

        $main_game_id = $game_info->main_game_id;

        $all_root_game_list = [];

        $all_main_game_list = [];

        $all_game_list = [];

        foreach ($all_theme_info_list as $theme_info) {
            $theme_info->game_list = json_decode($theme_info->game_list, true);

            $all_root_game_list[$theme_info->theme_id] = array_map(function ($ele) {
                return array_values(array_keys($ele))[0];
            }, $theme_info->game_list[$platform]['root_game_id'] ?? []);

            $all_main_game_list[$theme_info->theme_id] = array_map(function ($ele) {
                return array_values(array_keys($ele))[0];
            }, $theme_info->game_list[$platform]['main_game_id'] ?? []);

            $all_game_list[$theme_info->theme_id] = array_map(function ($ele) {
                return array_values(array_keys($ele))[0];
            }, $theme_info->game_list[$platform]['game_id'] ?? []);
        }


        foreach ($material_file_list as $file) {
            $theme_pid = (int)($file->theme_id / 1000);
            if ($all_game_list[$theme_pid] ?? false) {
                if (in_array($game_id, $all_game_list[$theme_pid])) {
                    $result[$file->id] = true;
                } else {
                    $result[$file->id] = false;
                }
            } else {
                if ($all_main_game_list[$theme_pid] ?? false) {
                    if (in_array($main_game_id, $all_main_game_list[$theme_pid])) {
                        $result[$file->id] = true;
                    } else {
                        $result[$file->id] = false;
                    }
                } else {
                    if ($all_root_game_list[$theme_pid] ?? false) {
                        if (in_array($root_game_id, $all_root_game_list[$theme_pid])) {
                            $result[$file->id] = true;
                        } else {
                            $result[$file->id] = false;
                        }
                    } else {
                        $result[$file->id] = true;
                    }
                }
            }
        }

        return $result;
    }
}
