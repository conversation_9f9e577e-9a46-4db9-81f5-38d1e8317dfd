<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>页面加载中……</title>
</head>
<body>
<script src="http://ip.tanwan.com/index.php?action=iplookup&format=js" type="text/javascript"></script>
<script type="text/javascript">
    var cplaceid = getQueryString("cplaceid");

    var gid_arr = [{gid_str}];
    var adid_arr = [{adid_str}];

    var ua = navigator.userAgent.toLowerCase();
    var isIos = /ipad|iphone|ipod|ios/i;
    var isAndroid = /android/i;
    //获取当前IP解析结果
    var ip_info = remote_ip_info;
    //根据不同地区，切换不同的创意：
    // 1,2轮为正常投放；3,4轮为非正常投放
    // 1,3轮投放安卓游戏和创意；2,4轮投放ios游戏和创意
    if (ip_info && ip_info['type'] == '1') {
        //正常投放
        if (isIos.test(ua)) {
            var gid = gid_arr[1];
            var adid = adid_arr[1];
            if (!gid) {
                gid = gid_arr[0];
            }      //如果只有一轮gameid和创意，说明该广告只投放当前渠道
            if (!adid) {
                adid = adid_arr[0];
            }
        } else {
            var gid = gid_arr[0];
            var adid = adid_arr[0];
        }

    } else {
        //非正常投放
        //判断当前是否有4轮游戏和创意
        if (isIos.test(ua)) {
            var gid = gid_arr[3];
            var adid = adid_arr[3];

            if (!gid) {
                gid = gid_arr[1];
            }
            if (!adid) {
                adid = adid_arr[1];
            }
            if (!gid) {
                gid = gid_arr[0];
            }
            if (!adid) {
                adid = adid_arr[0];
            }
        } else {
            var gid = gid_arr[2];
            var adid = adid_arr[2];
            if (!gid) {
                gid = gid_arr[0];
            }
            if (!adid) {
                adid = adid_arr[0];
            }
        }
    }


    var gourl = '{tc_url}{tc_dir}' + adid + '/cp/index.html?agent_id={agent_id}&sid={site_id}&aid=' + adid + '&game_id=' + gid + '&type={adtype}&dl={mark}&cplaceid=' + cplaceid;

    document.writeln('<meta http-equiv=refresh content="0; url=' + gourl + '">');

    function getQueryString(queryStringName) {
        var returnValue = "";
        var URLString = new String(document.location);
        var serachLocation = -1;
        var queryStringLength = queryStringName.length;
        do {
            serachLocation = URLString.indexOf(queryStringName + "\=");
            if (serachLocation != -1) {
                if ((URLString.charAt(serachLocation - 1) == '?') || (URLString.charAt(serachLocation - 1) == '&')) {
                    URLString = URLString.substr(serachLocation);
                    break;
                }
                URLString = URLString.substr(serachLocation + queryStringLength + 1);
            }
        } while (serachLocation != -1)
        if (serachLocation != -1) {
            var seperatorLocation = URLString.indexOf("&");
            if (seperatorLocation == -1) {
                returnValue = URLString.substr(queryStringLength + 1);
            } else {
                returnValue = URLString.substring(queryStringLength + 1, seperatorLocation);
            }
        }
        returnValue = returnValue.replace(/#/g, '');
        return returnValue;
    }
</script>
</body>
</html>
