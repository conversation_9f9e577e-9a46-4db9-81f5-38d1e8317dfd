<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>{title}</title>
</head>
<body>

<?php

// 需要填入的变量为 title,tj_url,agent_id,site_id,gid,adid,go_url

function getIP()
{
    if ($_SERVER["HTTP_X_FORWARDED_FOR"]) {
        $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        $ips = explode(',', $ip);//阿里cdn
        $ip = $ips[0];
    } else if ($_SERVER["HTTP_CDN_SRC_IP"]) {
        $ip = $_SERVER["HTTP_CDN_SRC_IP"];
    } else if (getenv('HTTP_CLIENT_IP')) {
        $ip = getenv('HTTP_CLIENT_IP');
    } else if (getenv('HTTP_X_FORWARDED')) {
        $ip = getenv('HTTP_X_FORWARDED');
    } else if (getenv('HTTP_FORWARDED_FOR')) {
        $ip = getenv('HTTP_FORWARDED_FOR');
    } else if (getenv('HTTP_FORWARDED')) {
        $ip = getenv('HTTP_FORWARDED');
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    $ip = str_replace(array('::ffff:', '[', ']'), array('', '', ''), $ip);
    return $ip;
}

function tjget($url)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_exec($ch);
    curl_close($ch);
}

function getOs()
{
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $isAndroid = preg_match('/(Android|Adr)/i', $userAgent);
    return $isAndroid ? 1 : 2;// 1：安卓，2：iOS
}

$ip = getIP();
$os = getOs();
$ref = urlencode($_SERVER["HTTP_REFERER"]);
$date_time = time();

$tjurl = "{tj_url}?&ip=__CLIENT_IP__&activity=ods_ad_show_log&ad_platform_id=0&platform={platform}&game_id={game_id}&agent_id={agent_id}&site_id={site_id}&show_time={$date_time}&receive_time={$date_time}";

tjget($tjurl);

$go_url = '{go_url}';

Header("HTTP/1.1 302 Moved Permanently");
Header("Location: " . $go_url);

?>
</body>
</html>
