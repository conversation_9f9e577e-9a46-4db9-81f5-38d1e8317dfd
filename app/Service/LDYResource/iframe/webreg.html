<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
</head>
<body>
<script type="text/javascript">
var gourl = '{gourl}';
if(!gourl){
	gourl = 'http://www.tanwan.com/';//TODO
}
var tjurl = '{tj_url}?ref='+escape(document.referrer)+'&uid={agent_id}&sid={site_id}&gid={gid}&aid={adid}&rand={rand}&type=1&step=5&cplaceid='+getQueryString('cplaceid');
var img=new Image(1,1);
img.src=tjurl;
window.location.href = gourl;
function getQueryString(queryStringName){var returnValue="";var URLString=new String(document.location);var serachLocation=-1;var queryStringLength=queryStringName.length;do{serachLocation=URLString.indexOf(queryStringName+"\=");if(serachLocation!=-1){if((URLString.charAt(serachLocation-1)=='?')||(URLString.charAt(serachLocation-1)=='&')){URLString=URLString.substr(serachLocation);break;}URLString=URLString.substr(serachLocation+queryStringLength+1);}}while(serachLocation!=-1)if(serachLocation!=-1){var seperatorLocation=URLString.indexOf("&");if(seperatorLocation==-1){returnValue=URLString.substr(queryStringLength+1);}else{returnValue=URLString.substring(queryStringLength+1,seperatorLocation);}}returnValue=returnValue.replace(/#/g,'');return returnValue;}
</script>
</body>
</html>
