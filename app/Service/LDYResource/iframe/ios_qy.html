<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>页面加载中……</title>
</head>
<body>
<script type="text/javascript">
    var cplaceid = getQueryString("cplaceid");

    var adturn = {adturn};
    var gid_arr = [{gid_str}];
    var adid_arr = [{adid_str}];
    var cookie_name = "twun_{site_id}";
    var turn = getcookie(cookie_name);
    if (!turn || turn >= adturn) {
        var rand = 1;
        turn = 0;
    } else {
        var rand = Number(turn) + Number(1);
    }
    setcookie(cookie_name, rand, 86400);
    var gid = gid_arr[turn];
    var adid = adid_arr[turn];
    var tc_url = '';//TODO 找贪玩的域名
    var gourl = tc_url + adid + '/cp/index.html?agent_id={agent_id}&sid={site_id}&aid=' + adid + '&game_id=' + gid + '&type={adtype}&dl={mark}&at_dl_time={at_dl_time}&cplaceid=' + cplaceid + '&app_type=ios_qy';

    document.writeln('<meta http-equiv=refresh content="0; url=' + gourl + '">');

    function getcookie(cName) {
        var search = cName + "=";
        var returnvalue = "";
        if (document.cookie.length > 0) {
            offset = document.cookie.indexOf(search);
            if (offset != -1) {
                offset += search.length;
                end = document.cookie.indexOf(";", offset);
                if (end == -1) {
                    end = document.cookie.length;
                }
                returnvalue = unescape(document.cookie.substring(offset, end));
            }
        }
        return returnvalue;
    }

    function setcookie(cName, value, cookie_time) {
        var then = new Date();
        then.setTime(then.getTime() + cookie_time * 1000);
        document.cookie = cName + '=' + value + ';expires=' + then.toGMTString() + ';path=/;';
    }

    function getQueryString(queryStringName) {
        var returnValue = "";
        var URLString = new String(document.location);
        var serachLocation = -1;
        var queryStringLength = queryStringName.length;
        do {
            serachLocation = URLString.indexOf(queryStringName + "\=");
            if (serachLocation != -1) {
                if ((URLString.charAt(serachLocation - 1) == '?') || (URLString.charAt(serachLocation - 1) == '&')) {
                    URLString = URLString.substr(serachLocation);
                    break;
                }
                URLString = URLString.substr(serachLocation + queryStringLength + 1);
            }
        } while (serachLocation != -1)
        if (serachLocation != -1) {
            var seperatorLocation = URLString.indexOf("&");
            if (seperatorLocation == -1) {
                returnValue = URLString.substr(queryStringLength + 1);
            } else {
                returnValue = URLString.substring(queryStringLength + 1, seperatorLocation);
            }
        }
        returnValue = returnValue.replace(/#/g, '');
        return returnValue;
    }

</script>
</body>
</html>
