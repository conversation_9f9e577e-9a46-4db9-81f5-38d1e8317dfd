<?php

namespace App\Service;

use Common\EnvConfig;
use App\Container;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Param\MediaAccountInfoParam;
use App\Model\HttpModel\Apple\Oauth2Model;

class AppleService
{
    /**
     * @param $platform
     * @param $client_id
     * @param $team_id
     * @param $key_id
     * @return string
     */
    public function addAccount($platform, $client_id, $team_id, $key_id): string
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;

        $private_key_file_path = SRV_DIR. EnvConfig::UPLOAD_PATH."/apple_account/private-key.pem";
        $private_key = file_get_contents($private_key_file_path);

        //获取token
        $oauth_model = new Oauth2Model();
        $result = $oauth_model->accessToken($client_id, $team_id, $key_id, $private_key);
        if (!isset($result['access_token'])) throw new AppException("获取token失败，请重新授权");

        //通过access_token 获取ACL关系，轮询帐号入库
        $account_list = $oauth_model->accessControlList($result['access_token']);
        if (!isset($account_list['data'])) throw new AppException("获取角色关系列表失败，请重新授权");

        $message = '';
        $media_service = new MediaService();
        foreach ($account_list['data'] as $item) {
            $account_id = $item['orgId'];
            $account_name = $item['orgName'];
            $majordomo_id = $item['parentOrgId'] ?? 0;

            /**
             * 为了不开启新的字段，复用其他媒体的字段来保存client_id,team_id,key_id
             * 字段 => 苹果参数
             * account_password     => client_id,
             * majordomo_name       => team_id,
             * wechat_account_name  => key_id
             */
            $media_account_param = new MediaAccountInfoParam([
                'media_type' => MediaType::ASA,
                'platform' => $platform,
                'account_id' => $account_id,
                'account_name' => $account_name,
                'access_token' => $result['access_token'],
                'access_token_expires' => time() + 3000,//苹果有效期为3600秒，设为提前过期，提前重刷，防止请求中过期
                'refresh_token' => '',
                'refresh_token_expires' => 0,
                'account_password' => $client_id,
                'majordomo_name' => $team_id,
                'toutiao_majordomo_id' => $majordomo_id,
                'wechat_account_name' => $key_id,
            ]);
            $tmp_message = $media_service->saveAuthAccount($media_account_param, $creator_id, $creator_name);
            $message .= "{$account_name}-$tmp_message;";
        }

        return $message;
    }
}
