<?php


namespace App\Service;

use App\Constant\ActionTrackType;
use App\Constant\AgentGroup;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\EventManager;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Constant\PlatId;
use App\Constant\TencentEum;
use App\Exception\AppException;
use App\Model\HttpModel\Kuaishou\AppCenter\AppCenterModel;
use App\Model\HttpModel\UC\V2\Account\AccountModel;
use App\Model\HttpModel\UC\V2\Convert\ConvertModel;
use App\Model\SqlModel\DataMedia\DimSiteGameIdModel;
use App\Model\SqlModel\DataMedia\OdsLiveUserInterfacePersonChangeLogModel;
use App\Model\SqlModel\Tanwan\V2DimCallbackStrategyLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameCallbackStrategyLogModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Tanwan\V2ODSAppleAbPageLogModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MediaMajordomoAccountModel;
use App\Model\SqlModel\Zeda\SiteGameChangeLogModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Model\SqlModel\Zeda\UserLevelModel;
use App\Model\SqlModel\Zeda\WechatAppModel;
use App\Param\AssetCreateParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\EventCreateParam;
use App\Param\MediaAccountInfoParam;
use App\Param\SiteConfigParam;
use App\Param\SiteGroupEditParam;
use App\Param\Toutiao\EventOptimizedGoalParam;
use App\Param\TrackUrlGroupCreateParam;
use App\Service\MediaAD\MediaAD;
use App\Service\MediaAD\MediaToutiao;
use App\Service\PlatformAD\PlatformAD;
use App\Struct\RedisCache;
use App\Utils\Math;
use Common\EnvConfig;

class SiteService
{

    /**
     * @param SiteConfigParam $param
     * @param $user_id
     * @param $username
     * @param bool $default
     * @return SiteConfigParam
     */
    public function addSite(SiteConfigParam $param, $user_id, $username, $default = false)
    {
        $agent_model = new AgentModel();
        $agent_info = $agent_model->getDataByPlatformAgentId($param->platform, $param->agent_id);
        $param->pay_type = $agent_info->pay_type;
        $param->media_type = $agent_info->media_type;
        $param->account_id = $agent_info->account_id;
        $param->agent_group = $agent_info->agent_group;
        $param->ori_game_id = $param->game_id;
        $param->site_name = $param->full_site_suffix_name;
        $statistic_caliber = $agent_info->statistic_caliber; // 统计口径

        $supplement_options = [
            'account_id' => $agent_info->account_id,
            'company' => $agent_info->company,
            'default' => $default,
        ];

        if ($agent_info->media_type === MediaType::MP) {
            $wechat_app = (new WechatAppModel())->getDataByAccountId($agent_info->account_id);
            if (!isset($wechat_app->app_secret) || empty($wechat_app->app_secret)) {
                throw new AppException('历史数据尚未补全app_secret，请联系管理员');
            }
            $supplement_options['wx_app_id'] = $wechat_app->app_id;
            $supplement_options['wx_app_secret'] = $wechat_app->app_secret;
            $supplement_options['wx_account_name'] = $wechat_app->wechat_account_name;
        }

        if ($agent_info->media_type === MediaType::SHENMA_SEARCH) {
            $media_account = (new MediaAccountModel())->getDataByAccountId($agent_info->account_id, $agent_info->media_type);
            if (!isset($media_account->account_id)) {
                throw new AppException('神马历史数据尚未补全，请联系管理员');
            }
            $supplement_options['account_password'] = $media_account->account_password;
            $supplement_options['access_token'] = $media_account->access_token;
        }

        // 判断直播间对接人及媒介对接人权限 skip_interface_person 脚本跳过权限检查 按需使用
        $aweme_account = $param->ext['aweme_account'] ?? '';
        $interface_person = $param->ext['interface_person'] ?? '';
        $intermediary_person = $param->ext['intermediary_person'] ?? '';
        if ((!empty($interface_person) || !empty($intermediary_person)) && empty($param->ext['skip_interface_person'] ?? '')) {
            $leader_permission = $this->getInterfacePersonList($param->platform, $user_id);
            // 抖音加热的视频对接人需要填设计人员，不需要负责人权限判断
            if (!empty($interface_person) && !in_array($interface_person, $leader_permission) && !in_array($param->agent_group, [AgentGroup::DOUYIN_HOT, AgentGroup::XINGTU_SPREAD_STAR])) {
                throw new AppException("您没有直播间对接人{$interface_person}的权限");
            }
            if (!empty($intermediary_person) && !in_array($intermediary_person, $leader_permission)) {
                throw new AppException("您没有媒介对接人{$intermediary_person}的权限");
            }
        }

        // 抖音直播 抖音小手柄 快手直播 判断是否存在直播间对接关系
        if (
            in_array($param->agent_group, [
                AgentGroup::DOUYIN_LIVE, AgentGroup::DOUYIN_UOP, AgentGroup::KUAISHOU_LIVE, AgentGroup::DOUYIN_WINDMILL_FANS,
                AgentGroup::DOUYIN_UOP_FANS, AgentGroup::DOUYIN_ENTERPRISE, AgentGroup::CHENZHONG_LIVE, AgentGroup::DOUYIN_PINXUAN,
                AgentGroup::ZB_FANS
            ])
            && !empty($aweme_account) && !empty($interface_person)
        ) {
            $media_type = $param->media_type == MediaType::DOUYIN_ENTERPRISE ? MediaType::TOUTIAO : $param->media_type;
            $check_interface = (new OdsLiveUserInterfacePersonChangeLogModel())->getListByLimitPersonTime(
                $param->platform, $param->game_id, $media_type, strtolower($aweme_account), $interface_person, date("Y-m-d H:i:s")
            );
            if ($check_interface->isEmpty()) {
                throw new AppException("该直播间{$aweme_account}与{$interface_person}的关系不存在或已过期，请到直播对接关系管理添加");
            }
        }

        // 获取媒体上报策略
        $param = $this->getCallbackStrategy($param);
        $param->money_level = $param->money_level === 'none' ? json_encode((object)[]) : ($param->money_level ?: json_encode((object)[]));
        $param->money_range = $param->money_range === 'none' ? NULL : ($param->money_range ?: NULL);
        $param->strategy_json = $param->strategy_json === 'none' ? NULL : ($param->strategy_json ?: NULL);
        $param->ext['money_level'] = $param->money_level;
        $param->ext['money_range'] = $param->money_range;
        $param->ext['strategy_json'] = $param->strategy_json;

        // 非头条删除是否上报金额配置
        if (!empty($param->strategy_json)) {
            $tmp_strategy_json = json_decode($param->strategy_json, true);
            if (isset($tmp_strategy_json['is_callback_with_money_amount']) && $param->media_type != MediaType::TOUTIAO) {
                unset($tmp_strategy_json['is_callback_with_money_amount']);
                if (empty($tmp_strategy_json)) {
                    $param->ext['strategy_json'] = null;
                } else {
                    $param->ext['strategy_json'] = json_encode($tmp_strategy_json);
                }
            }
        }

        // 结算类型
        $settlement_type = (new OuterService())->switchSettlementPayType('', $agent_info->pay_type);
        if ($settlement_type === 'cps') {
            $param->ad_pop_zk_type = 2;
        } else if ($settlement_type === 'cpa') {
            $param->ad_pop_zk_type = 1;
        } else if ($settlement_type !== '免费') {
            $param->ad_pop_zk_type = 0;
        } else {
            // 免费渠道的统计口径，默认按子游戏，即2
            $statistic_caliber = 2;
        }

        // 广告位扣量有配置上报统计方式 要写回广告位的统计口径 反之也要写回广告位扣量配置 两者需要统一
        if ($settlement_type === '免费') {
            if (!empty($param->ext['strategy_json'])) {
                $tmp_strategy_json = json_decode($param->ext['strategy_json'], true);

                if (($tmp_strategy_json['extra_source_type'] ?? 0) == 30) {
                    $statistic_caliber = 4;
                    $tmp_strategy_json['callback_statistical_type'] = 2;
                } elseif (($tmp_strategy_json['callback_statistical_type'] ?? 0) == 2) {
                    $statistic_caliber = 3;
                } elseif (($tmp_strategy_json['callback_statistical_type'] ?? 0) == 1) {
                    $statistic_caliber = 2;
                }

                // 默认没配置按子
                if (!isset($tmp_strategy_json['callback_statistical_type']) && $statistic_caliber == 2) {
                    $tmp_strategy_json['callback_statistical_type'] = 1;
                }
            } else {
                $tmp_strategy_json = [
                    'callback_statistical_type' => 1
                ];
            }
            $param->strategy_json = json_encode($tmp_strategy_json);
            $param->ext['strategy_json'] = json_encode($tmp_strategy_json);
        }

        // 扣量比例(小数是批量任务那边传过来的)
        if ($param->ad_pop_zk < 1) {
            $param->ad_pop_zk = $param->ad_pop_zk * 100;
        } elseif (!in_array($settlement_type, ['免费']) || $param->media_type === MediaType::OW) {
            // 手动建广告位 只有免费渠道能设置扣量 非免费渠道和官网渠道扣量设置0
            $param->ad_pop_zk = 0;
        }

        if ($param->ad_pop_zk_type === 2) {
            $param->pay_discount = $param->ad_pop_zk;
        } elseif ($param->ad_pop_zk_type === 1) {
            $param->reg_discount = $param->ad_pop_zk;
        }

        // 判断账号是否投过腾讯的ADA 投过需要标记且不能投普通广告 为了拉数据时能区分
        $tencent_ada = false;
        if (in_array($param->media_type, [MediaType::TENCENT])) {
            $media_account = (new MediaAccountModel())->getDataByAccountId($agent_info->account_id, $agent_info->media_type);
            if (!empty($media_account)) {
                if ($media_account->wechat_account_name !== 'ADA' && $param->action_track_type === ActionTrackType::TENCENT_ADA) {
                    $tencent_ada = true;
                }
            }
        }

        $ads_platform = PlatformAD::create($param->platform);
        $platform_site_data = $ads_platform->addSite($param, $agent_info->agent_leader, $agent_info->user_name, $username, $supplement_options);

        $param->site_id = $platform_site_data['site_id'] ?? 0;
        $param->action_track_url = $platform_site_data['call_back'] ?? '';
        $param->display_track_url = $platform_site_data['display_track_url'] ?? '';

        if ($param->game_pack === 2) {
            // 媒体分包
            // 头条分包链接 不取子平台
            $param->app_android_channel_package_id = $platform_site_data['media_site_id'];
            $param->download_url = '';
        } else if ($param->game_pack === 1) {
            $param->download_url = $platform_site_data['download_url'] ?? '';
        } else if ($param->game_pack === 0 && $param->game_type === "IOS") {
            $param->download_url = $platform_site_data['download_url'];
        }

        // 腾讯ADA账号标记
        if ($tencent_ada) {
            (new MediaAccountModel())->updateByAccountId($agent_info->media_type, $agent_info->account_id, ['wechat_account_name' => 'ADA']);
        }

        // 苹果自定义产品页
        if ($param->game_type === "IOS" && !empty($param->ext['apple_ppid'] ?? '')) {
            $param->download_url = $this->getApplAbDownloadUrl($param->download_url, $param->ext['apple_ppid']);
            // 获取自定义产品页ppid_name
            $apple_ab_page = (new V2ODSAppleAbPageLogModel())->getDataByApplePPID($param->platform, $param->game_id, $param->ext['apple_ppid']);
            $param->ext['ppid_name'] = $apple_ab_page->ppid_name ?? '';
        }

        // 虎牙投小游戏生成投放链接
        if ($param->media_type === MediaType::HUYA && $param->plat_id == PlatId::MINI) {
            $mini_game_original_id = $param->sdk_ext['mini_game_original_id'];
            $mini_game_path = "page/index?media_type={$param->media_type}&game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$param->site_id}&request_id=__TRACEID__&caid1=__CAID1__";
            $param->download_url = "https://m.huya.com?hyaction=launchweixinminiprogram&username={$mini_game_original_id}&path=" . urlencode($mini_game_path);
        }

        // 抖音红人 对接人如果没填写则默认为agent_leader
        if ($param->media_type === MediaType::DOUYIN_STAR) {
            $param->ext['interface_person'] = $param->ext['interface_person'] ?: $agent_info->agent_leader;
        }

        // 抖音加热渠道组 媒介对接人如果没填写则默认为agent_leader
        if ($param->agent_group === AgentGroup::DOUYIN_HOT) {
            $param->ext['intermediary_person'] = $param->ext['intermediary_person'] ?: $agent_info->agent_leader;
        }

        if (isset($platform_site_data['landing_url'])) {
            $param->template_address = $platform_site_data['landing_url'];
        }

        if (!empty($platform_site_data['mini_game_tracking_parameter'])) {
            $param->ext['mini_game_tracking_parameter'] = $platform_site_data['mini_game_tracking_parameter'] . "&media_type={$param->media_type}";
        }

        if (in_array($param->media_type, [MediaType::UC, MediaType::UC_LIVE]) && !empty($platform_site_data['mini_game_url_scheme'])) {
            $param->ext['mini_game_url_scheme'] = $platform_site_data['mini_game_url_scheme'];
        }

        // 爱奇艺小游戏路径
        if ($param->media_type === MediaType::IQIYI && $param->plat_id == PlatId::MINI) {
            $mini_game_original_id = $param->sdk_ext['mini_game_original_id'];
            $mini_game_path = "page/index?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$param->site_id}";
            $param->ext['mini_game_tracking_parameter'] = "miniAppName={$mini_game_original_id}&miniAppPath=" . urlencode($mini_game_path);
        }

        // 快手小游戏路径
        if (in_array($param->media_type, [MediaType::KUAISHOU, MediaType::CHENZHONG, MediaType::KUAISHOU_PINPAI, MediaType::KUAISHOU_STAR]) && $param->plat_id == PlatId::MINI) {
            $param->ext['mini_game_tracking_parameter'] .= "&callback=__CALLBACK__";
        }

        // 斗鱼小游戏路径
        if (in_array($param->media_type, [MediaType::DOUYU]) && $param->plat_id == PlatId::MINI) {
            $param->ext['mini_game_tracking_parameter'] .= "&request_id=[[_DYREQUESTID_]]";
        }

        // 喜马拉雅小游戏路径
        if (in_array($param->media_type, [MediaType::XIMALAYA]) && $param->plat_id == PlatId::MINI) {
            $param->ext['mini_game_tracking_parameter'] .= "&callback_url=_CALLBACK_URL_";
        }

        // 预约监测链接
        if (in_array($param->media_type, [MediaType::FANZHUO, MediaType::YUNSI, MediaType::YUCHE, MediaType::WEILAIYOU])) {
            $param->ext['subscribe_track_url'] = $platform_site_data['subscribe_track_url'] ?? '';
        }

        // 批量广告创建广告位不保存直播间，只传给高热，防止头条2.0一个项目广告位对应多个直播间
        if ($param->site_from === 1 && $param->media_type === MediaType::TOUTIAO && !empty($param->ext['aweme_account'] ?? '')) {
            $param->ext['aweme_account'] = '';
        }

        // B站联运CPS渠道组下载链接特殊处理，子平台限制只能浏览器下载，需要在下载链接后加上签名，服务器才能直接下载
        if ($param->agent_group === AgentGroup::BILIBILI_LY_CPS && $param->game_type === "安卓") {
            $param->download_url = $this->genDownloadUrlWithSign($param->download_url);
        }

        $now = $default ? 0 : time();

        $insert_data[] = [
            'platform' => $param->platform,
            'site_id' => $param->site_id,
            'site_name' => $param->site_name,
            'account_id' => $agent_info->account_id,
            'user_name' => $agent_info->user_name,
            'media_type' => $agent_info->media_type,
            'agent_group' => $agent_info->agent_group,
            'agent_id' => $param->agent_id,
            'game_id' => $param->game_id,
            'game_type' => $param->game_type,
            'game_pack' => $param->game_pack,
            'ori_game_id' => $param->ori_game_id,
            'package' => $param->package,
            'app_android_channel_package_id' => $param->app_android_channel_package_id,
            'convert_type' => $param->convert_type,
            'convert_source_type' => $param->convert_source_type,
            'convert_data_type' => $param->convert_data_type,
            'convert_id' => $param->convert_id,
            'action_track_type' => $param->action_track_type,
            'action_track_url' => $param->action_track_url,
            'display_track_url' => $param->display_track_url,
            'download_url' => $param->download_url,
            'ad_turn' => $param->ad_turn,
            'ad_price' => $param->ad_price,
            'ad_pop_zk' => $param->ad_pop_zk,
            'auto_download' => $param->auto_download,
            'auto_download_second' => $param->auto_download_second,
            'cps_divide_rate' => $param->cps_divide_rate,
            'forbid_tuitan' => $param->forbid_tuitan,
            'template_type' => $param->template_type,
            'template_address' => $param->template_address,
            'pay_type' => $agent_info->pay_type,
            'upt_state' => $param->upt_state,
            'upt_rate' => $param->upt_rate,
            'pay_discount' => $param->pay_discount,
            'reg_discount' => $param->reg_discount,
            'is_third' => $param->is_third,
            'deep_external_action' => $param->deep_external_action,
            'app_name' => $param->app_name,
            'image_token' => $param->image_token,
            'appid' => $param->appid,
            'akey' => $param->akey,
            'ext' => json_encode((object)$param->ext),
            'state' => $param->state,
            'creator' => $username,
            'creator_id' => $user_id,
            'ad_pop_zk_type' => $param->ad_pop_zk_type,
            'create_time' => $now,
            'update_time' => $now,
            'convert_toolkit' => $param->convert_toolkit,
            'is_callback_with_money_amount' => $param->is_callback_with_money_amount
        ];

        // adb dim_site 扣量比例万分比
        $dim_insert_data = $insert_data;
        $dim_insert_data[0]['ad_pop_zk'] = Math::div($param->ad_pop_zk, 100);

        // 免费/cpa/cps广告位插入扣量策略
        if (in_array($settlement_type, ['免费'])) {
            $dim_insert_data[0]['money_level'] = $param->money_level;
            $dim_insert_data[0]['money_range'] = $param->money_range;
            $dim_insert_data[0]['strategy_json'] = $param->strategy_json;
            $dim_insert_data[0]['skew_type'] = $param->skew_type ?: '';
            $dim_insert_data[0]['skew_num'] = $param->skew_num ?: 0;
        }

        $model = new SiteModel();
        $model->addMany($insert_data);
        (new SiteGameChangeLogModel())->addMany($insert_data, $now);
        (new V2DimSiteIdModel())->addMany($dim_insert_data, $settlement_type, $statistic_caliber, (int)$default);
        (new DimSiteGameIdModel())->replaceMany($insert_data, DimSiteGameIdModel::START_TIME);

        return $param;
    }

    /**
     * @param SiteConfigParam $param
     * @return SiteConfigParam
     */
    public function addChannelPackage(SiteConfigParam $param)
    {
        $create_tencent_package = in_array($param->media_type, [MediaType::TENCENT, MediaType::MP, MediaType::CHANNELS_LIVE, MediaType::MEDIA_SCHEDULE])
            && (
                ($param->game_pack === 1 && $param->app_android_channel_package_id === '')
                || ($param->game_pack === 2 && $param->download_url === '')
            );
        $create_toutiao_package = in_array($param->media_type, [MediaType::TOUTIAO, MediaType::DOUYIN_STAR, MediaType::TOUTIAO_APP, MediaType::TOUTIAO_PINPAI, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE]) && $param->game_pack === 2 && $param->download_url === '';
        $create_kuaishou_package = in_array($param->media_type, [MediaType::KUAISHOU, MediaType::CHENZHONG, MediaType::KUAISHOU_PINPAI, MediaType::KUAISHOU_STAR]) && $param->game_pack === 2 && $param->download_url === '';
        if (($create_tencent_package || $create_toutiao_package || $create_kuaishou_package) &&
            $param->plat_id != PlatId::MINI &&
            $param->game_type === "安卓" &&
            $param->android_union_channel_package_account_id > 0 &&
            ($param->ext['skip_channel_convert'] ?? 0) !== 1) {
            if ($create_toutiao_package) {
                $media_type = MediaType::TOUTIAO;
                $channel_id = $param->app_android_channel_package_id;
                $app_id = $param->app_secret;
                $prefix_name = $param->media_type === MediaType::TOUTIAO_APP ? '应用中心' : '';
                $suffix_name = $param->account_id;
            } else if ($create_tencent_package) {
                $media_type = MediaType::TENCENT;
                $channel_id = $param->game_pack === 2 ? $param->app_android_channel_package_id : $param->site_id;
                $app_id = $param->appid;
                $prefix_name = $param->media_type === MediaType::MP ? '微信' : '';
                $suffix_name = trim("$param->account_id-$param->site_suffix_name", '-');
            } else {
                $media_type = MediaType::KUAISHOU;
                $channel_id = $param->app_android_channel_package_id;
                $app_id = $param->app_secret;
                $prefix_name = '';
                $suffix_name = $param->account_id;
            }

            if ($param->account_type === 'BP') {
                $media_account_info = (new MediaMajordomoAccountModel())->getDataByAccountId($media_type, $param->android_union_channel_package_account_id);
            } else {
                $media_account_info = (new MediaAccountModel())->getDataByAccountId($param->android_union_channel_package_account_id);
            }

            // 快手媒体分包 母包更新后母包package_id会变化 需要每次创建分包前查询一次最新版本的母包package_id
            // TODO 等媒体优化接口后再删除
            if ($media_type === MediaType::KUAISHOU) {
                $package_id = "";
                $data = (new AppCenterModel())->getAppList(
                    $param->android_union_channel_package_account_id,
                    $media_account_info->access_token ?? "",
                    $param->appid, 1, 10
                );
                foreach ($data['list'] as $item) {
                    if ($item['app_id'] == $param->appid) {
                        $package_id = $item['package_id'];
                        break;
                    }
                }

                if (empty($package_id)) {
                    throw new AppException("请检查SDK管理对应的APPID是否正确，或请前往媒体后台确认已上传应用包");
                }
                $app_id = $package_id;
                $param->app_secret = $package_id;
            }

            $channel_package_param = new ChannelPackageParam([
                'media_account' => new MediaAccountInfoParam((array)$media_account_info),
                'game_id' => $param->game_id,
                'site_id' => $channel_id,
                'prefix_name' => $prefix_name,
                'suffix_name' => $suffix_name,
                'app_id' => $app_id,
                'download_url' => $param->download_url,
                'account_type' => $param->account_type,
                'game_pack' => $param->game_pack
            ]);
            $channel_package_data = (new MediaAD($media_type))->createChannelPackage($channel_package_param);
            $param->app_android_channel_package_id = $channel_package_data['app_android_channel_package_id'];
            if ($create_toutiao_package || (($create_tencent_package || $create_kuaishou_package) && $param->game_pack === 2)) {
                $param->download_url = $channel_package_data['download_url'];
            }

            (new SiteModel())->edit($param->platform, $param->site_id, [
                'app_android_channel_package_id' => $param->app_android_channel_package_id,
                'download_url' => $param->download_url,
            ]);
            (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                'app_android_channel_package_id' => $param->app_android_channel_package_id,
            ]);
        }
        return $param;
    }

    /**
     * @param SiteConfigParam $param
     * @return SiteConfigParam
     */
    public function addConvert(SiteConfigParam $param)
    {
        // 已成功新建 无需再次新建
        if (!empty($param->convert_id) || ($param->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET && !empty($param->ext['asset_ids'] ?? ''))) {
            return $param;
        }

        // 快手无需创建应用 接口已下线
        if (in_array($param->media_type, [MediaType::KUAISHOU, MediaType::CHENZHONG, MediaType::KUAISHOU_PINPAI, MediaType::KUAISHOU_STAR])) {
            return $param;
        }

        $create_convert = in_array($param->media_type, [
            MediaType::TOUTIAO, MediaType::TENCENT, MediaType::KUAISHOU,
            MediaType::BAIDU, MediaType::UC, MediaType::DOUYIN_STAR, MediaType::TOUTIAO_APP, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR,
            MediaType::KUAISHOU_STAR, MediaType::DOUYIN_ENTERPRISE
        ]);

        // 抖音小手柄IOS不需要建资产
        $create_agent_group_convert = !(
            in_array($param->agent_group, [AgentGroup::TOUTIAO_LIVE, AgentGroup::TOUTIAO_UOP, AgentGroup::DOUYIN_UOP])
            && !in_array($param->convert_source_type, [ConvertSourceType::SDK, ConvertSourceType::H5_API])
            && (
                $param->convert_toolkit === ConvertToolkit::TOUTIAO_CONVERT
                || (in_array($param->agent_group, [AgentGroup::DOUYIN_UOP]) && $param->game_type === "IOS")
            )
        );

        $create_game_pack_convert = !(
            in_array($param->media_type, [MediaType::TOUTIAO, MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE])
            && $param->game_type === "安卓"
            && $param->game_pack === 1
            && $param->convert_source_type === ConvertSourceType::SDK
        );

        $create_not_mini_game_convert = !(in_array($param->media_type, [MediaType::KUAISHOU, MediaType::BAIDU]) && $param->plat_id == PlatId::MINI);

        // 是否跳过创建转化步骤 0: 不跳过 1: 跳过
        $not_skip_channel_convert = ($param->ext['skip_channel_convert'] ?? 0) !== 1;

        if ($create_convert && $create_agent_group_convert && $create_game_pack_convert && $create_not_mini_game_convert && $not_skip_channel_convert) {
            $media_type = $param->media_type;
            if (in_array($media_type, [MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE])) {
                $media_type = MediaType::TOUTIAO; //抖音红人创建头条转化
                $param->account_id = $param->ext['convert_account_id'] ?? 0;
            }

            $media_account_info = (new MediaAccountModel())->getDataByAccountId($param->account_id, $media_type);
            // 查无账号 无需建转化
            if (empty($media_account_info)) {
                return $param;
            }
            // 旧链路账号无需新建转化
            if ($media_type === MediaType::TENCENT && in_array($param->account_id, [********, ********, ********, ********,])) {
                return $param;
            }

            if (in_array($media_type, [MediaType::KUAISHOU]) && $param->game_type === "安卓" && isset($param->ext['jingle_bell_download_url'])) {
                $param->download_url = $param->ext['jingle_bell_download_url'] ?: $param->download_url;
            }

            $media_account_info_param = new MediaAccountInfoParam((array)$media_account_info);
            /** 事件管理 */
            if ($param->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
                // 抖音红人、头条应用管理中心无需建资产
                if (in_array($param->media_type, [MediaType::TOUTIAO_APP, MediaType::DOUYIN_STAR, MediaType::XINGTU, MediaType::XINGTU_STAR, MediaType::DOUYIN_ENTERPRISE])) {
                    return $param;
                }
                $param = $this->addAssetAndEvent($param, $media_account_info_param);
            } elseif ($param->media_type === MediaType::UC && $param->action_track_type === ActionTrackType::UC_V2) {
                // UC2.0 获取账号绑定的转化ID，创建转化监测组
                $account = (new AccountModel())->getAccount(
                    $media_account_info->wechat_account_name,
                    $media_account_info->account_password,
                    $media_account_info->access_token,
                    $media_account_info->account_name
                );

                if(!isset($account['accountType']['convertInfoType']['convertId'])){
                    throw new AppException('找不到该账户的转化ID，请确认该账户是否为UC2.0账户');
                }

                $param->convert_id = $account['accountType']['convertInfoType']['convertId'];

                $convert_monitor_group_id = (new ConvertModel())->addMonitor(
                    $media_account_info->wechat_account_name,
                    $media_account_info->account_password,
                    $media_account_info->access_token,
                    $media_account_info->account_name,
                    $account['accountType']['convertInfoType']['convertId'],
                    $param->site_id,
                    $param->action_track_url
                );

                $param->ext['convert_monitor_group_id'] = $convert_monitor_group_id['successIdIndexes'][0]['id'];

                (new SiteModel())->edit($param->platform, $param->site_id, [
                    'convert_id' => $param->convert_id,
                    'ext' => json_encode((object)$param->ext)
                ]);
                (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                    'convert_id' => $param->convert_id,
                ]);
                (new DimSiteGameIdModel())->edit($param->platform, $param->site_id, [
                    'convert_id' => $param->convert_id,
                ]);

            } else {
                /** 转化跟踪 */
                $convert_create_param = new ConvertCreateParam([
                    'media_account'          => $media_account_info_param,
                    'agent_id'               => $param->agent_id,
                    'game_id'                => $param->game_id,
                    'site_id'                => $param->site_id,
                    'app_name'               => $param->app_name,
                    'convert_type'           => $param->convert_type,
                    'convert_source_type'    => $param->convert_source_type,
                    'convert_data_type'      => $param->convert_data_type,
                    'download_url'           => $param->download_url,
                    'app_id'                 => $param->appid,
                    'app_secret'             => $param->app_secret,
                    'game_type'              => $param->game_type,
                    'image_token'            => $param->image_token,
                    'action_track_url'       => $param->action_track_url,
                    'display_track_url'      => $param->display_track_url,
                    'package_name'           => $param->package,
                    'deep_external_action'   => $param->deep_external_action,
                    'plat_id'                => $param->plat_id,
                    'external_url'           => $param->ext['external_url'] ?? '',
                    'action_track_type'      => $param->action_track_type,
                    'conversion_template_id' => $param->ext['conversion_template_id'] ?? '',
                ]);

                $convert_data = (new MediaAD($media_type))->createConvert($convert_create_param);
                $param->convert_id = $convert_data['convert_id'];
                (new SiteModel())->edit($param->platform, $param->site_id, [
                    'convert_id' => $param->convert_id,
                ]);
                (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                    'convert_id' => $param->convert_id,
                ]);
                (new DimSiteGameIdModel())->edit($param->platform, $param->site_id, [
                    'convert_id' => $param->convert_id,
                ]);

                // 小游戏混变场景化投放 需要创建两个转化ID
                // 注册+首日付费ROI
                // 注册+首日变现ROI
                if (isset($param->ext['adgroup_type']) && $param->ext['adgroup_type'] === TencentEum::SMART_DELIVERY_PLATFORM_EDITION_SCENE) {
                    $convert_create_param->convert_type = 'OPTIMIZATIONGOAL_APP_REGISTER'; // 注册
                    $convert_create_param->deep_external_action = 'GOAL_1DAY_MONETIZATION_ROAS'; // 首日变现ROI

                    $convert_data = (new MediaAD($media_type))->createConvert($convert_create_param);
                    $param->ext['convert_id_list'] = [$param->convert_id, $convert_data['convert_id']];

                    $ext = json_encode((object)$param->ext);

                    (new SiteModel())->edit($param->platform, $param->site_id, [
                        'convert_id' => $param->convert_id,
                        'ext' => $ext,
                    ]);
                    (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                        'convert_id' => $param->convert_id,
                        'extra_conf' => $ext
                    ]);
                }
            }
        }
        return $param;
    }

    public function groupEdit(SiteGroupEditParam $param, $user_id, $username)
    {
        $data = [];
        $dim_site_data = [];
        if (!is_null($param->pay_type)) {
            $data['pay_type'] = $param->pay_type;
        }

        if (!is_null($param->ad_turn)) {
            $data['ad_turn'] = $param->ad_turn;
        }

        if (!is_null($param->ad_price)) {
            $data['ad_price'] = $param->ad_price;
        }

        if (!is_null($param->ad_pop_zk_type)) {
            $site_list = (new SiteModel())->getAllInSite($param->platform, $param->site_ids);

            $error_site_ids = $site_list
                ->where('ad_pop_zk_type', '!=', $param->ad_pop_zk_type)
                ->where('ad_pop_zk_type', '!=', 0)
                ->pluck('site_id');

            // 已有的扣量类型不能更改
            if ($error_site_ids->count() > 0) {
                throw new AppException("广告位ID：" . implode("，", $error_site_ids->toArray()) . "，不能更改扣量类型");
            }

            $data['ad_pop_zk_type'] = $param->ad_pop_zk_type;
            $dim_site_data['deduction_type'] = $param->ad_pop_zk_type;


            //因为不设置money_level也是{}，用isset(money_level)来判断是否已刷新前端且有权限编辑扣量
            if (isset($param->ext['money_level']) || isset($param->ext['money_range']) || isset($param->ext['strategy_json'])) {
                // 扣量金额范围只能在扣订单时配置
                if ($param->ext['money_range']
                    && ((isset($data['ad_pop_zk_type']) && $data['ad_pop_zk_type'] !== 2) || $site_list->whereNotIn('ad_pop_zk_type', [0, 2])->isNotEmpty())
                ) {
                    throw new AppException('扣量金额范围只能在扣订单时配置');
                }

                $money_level = $param->ext['money_level'] ?: json_encode((object)[]);
                $data['ext->money_level'] = $money_level;
                $dim_site_data['money_level'] = $money_level;

                $money_range = $param->ext['money_range'] ?: NULL;
                $data['ext->money_range'] = $money_range;
                $dim_site_data['money_range'] = $money_range;

                // money_range 同时入到 strategy_json
                if (!empty($param->ext['money_range'])) {
                    $tmp_strategy_json = json_decode($param->ext['strategy_json'], true);
                    $tmp_strategy_json['money_range'] = json_decode($param->ext['money_range'], true);
                    $param->ext['strategy_json'] = json_encode($tmp_strategy_json);
                }

                // 非头条删除是否上报金额配置
                if ($site_list->where('media_type', '!=', MediaType::TOUTIAO)->count() > 0) {
                    $tmp_strategy_json = json_decode($param->ext['strategy_json'], true);
                    if (isset($tmp_strategy_json['is_callback_with_money_amount'])) {
                        unset($tmp_strategy_json['is_callback_with_money_amount']);
                        if (empty($tmp_strategy_json)) {
                            $param->ext['strategy_json'] = null;
                        } else {
                            $param->ext['strategy_json'] = json_encode($tmp_strategy_json);
                        }
                    }
                }

                $strategy_json = $param->ext['strategy_json'] ?: NULL;
                $data['ext->strategy_json'] = $strategy_json;
                $dim_site_data['strategy_json'] = $strategy_json;
            }
        }

        if (!is_null($param->ad_pop_zk)) {
            $data['ad_pop_zk'] = $param->ad_pop_zk;
            $dim_site_data['deduction_value'] = Math::div($param->ad_pop_zk, 100);
        }

        if (!is_null($param->ad_pop_zk) && !is_null($param->ad_pop_zk_type)) {
            if ($param->ad_pop_zk_type === 2) {
                $param->pay_discount = $param->ad_pop_zk;
            } elseif ($param->ad_pop_zk_type === 1) {
                $param->reg_discount = $param->ad_pop_zk;
            }
        }

        if (!is_null($param->pay_discount)) {
            $data['pay_discount'] = $param->pay_discount;
        }

        if (!is_null($param->reg_discount)) {
            $data['reg_discount'] = $param->reg_discount;
        }

        if (!is_null($param->auto_download)) {
            $data['auto_download'] = $param->auto_download;
        }

        if (!is_null($param->auto_download_second)) {
            $data['auto_download_second'] = $param->auto_download_second;
        }

        if (!is_null($param->forbid_tuitan)) {
            $data['forbid_tuitan'] = $param->forbid_tuitan;
        }

        if (!is_null($param->template_type)) {
            $data['template_type'] = $param->template_type;
        }

        if (!is_null($param->state)) {
            $data['state'] = $param->state;
        }

        if (!is_null($param->site_suffix_name)) {
            $data['site_name'] = $param->site_suffix_name;
            $dim_site_data['site_name'] = $param->site_suffix_name;
        }

        if (!is_null($param->statistic_caliber)) {
            $dim_site_data['statistical_type'] = $param->statistic_caliber; // 统计口径 注意两个字段名不一样
        }

        if (!empty($dim_site_data)) {
            (new V2DimSiteIdModel())->editInSiteId(
                $param->platform,
                $param->site_ids,
                $dim_site_data
            );
        }

        if (count($data) > 0) {
            $ads_platform = PlatformAD::create($param->platform);
            $ads_platform->groupEditSite($param, $username);
        }

        (new SiteModel())->editInSiteId($param->platform, $param->site_ids, $data);
        (new AgentSiteLogService())->batchEditSiteLog($param->toArray(), $user_id, $username);
    }

    /**
     * 生成IOS AB产品页链接
     * @param $download_url
     * @param $apple_ppid
     * @return string
     */
    public function getApplAbDownloadUrl($download_url, $apple_ppid)
    {
        if (empty($download_url) || empty($apple_ppid)) {
            return $download_url;
        }

        $pos = strpos($download_url, '?');
        if ($pos > 0) {
            $url = $download_url . '&ppid=' . $apple_ppid;
        } else {
            $url = $download_url . '?ppid=' . $apple_ppid;
        }

        return $url;
    }

    /**
     * 获取广告位 上报策略配置
     * @param SiteConfigParam $param
     * @return SiteConfigParam
     */
    public function getCallbackStrategy(SiteConfigParam $param)
    {
        $callback_strategy = (new V2DimGameCallbackStrategyLogModel())->getDataByGameId($param->platform, $param->media_type, $param->game_id);
        if (!empty($callback_strategy)) {
//            后续更新扣量 暂时只获取是否上报金额
//            if ($param->ad_pop_zk_type === 2) {
//                $param->ad_pop_zk = $param->ad_pop_zk ?: $callback_strategy->pay_buckle_ratio * 100;
//            } elseif ($param->ad_pop_zk_type === 1) {
//                $param->ad_pop_zk = $param->ad_pop_zk ?: $callback_strategy->register_buckle_ratio * 100;
//            }
            $param->is_callback_with_money_amount = $callback_strategy->is_callback_with_money_amount;
        }

        // 按游戏扣量配置 目前只针对免费+非官网渠道的广告位
        if (in_array((int)$param->pay_type, [5]) && $param->media_type !== MediaType::OW) {
            // 自定义扣量配置或游戏扣量配置 放ext里面
            if (!empty($param->ext['strategy_type'] ?? 0)) {
                // 手动设置的扣量配置
                $param->money_level = $param->ext['money_level'] ?: json_encode((object)[]);
                $param->money_range = $param->ext['money_range'] ?: NULL;
                $param->strategy_json = $param->ext['strategy_json'] ?: NULL;

                // money_range 同时入到 strategy_json
                if (!empty($param->ext['money_range'])) {
                    $tmp_strategy_json = json_decode($param->ext['strategy_json'], true);
                    $tmp_strategy_json['money_range'] = json_decode($param->ext['money_range'], true);
                    $param->ext['strategy_json'] = json_encode($tmp_strategy_json);
                    $param->strategy_json = $param->ext['strategy_json'];
                }
            } else {
                $game_strategy = (new V2DimCallbackStrategyLogModel())->getDataByGameId($param->platform, $param->media_type, $param->game_id);
                if (!empty($game_strategy)) {
                    // 没有手动设置类型 用游戏扣量配置类型
                    $param->ad_pop_zk_type = $param->ad_pop_zk_type == 0 ? $game_strategy->deduction_type : $param->ad_pop_zk_type;
                    // 没有手动设置比例 用游戏扣量配置的比例
                    $param->ad_pop_zk = $param->ad_pop_zk == 0 ? $game_strategy->deduction_value * 100 : $param->ad_pop_zk;
                    $param->money_level = $game_strategy->money_level;
                    $param->money_range = $game_strategy->money_range;
                    $param->strategy_json = $game_strategy->strategy_json;
                    $param->skew_type = $game_strategy->skew_type;
                    $param->skew_num = $game_strategy->skew_num;
                }
            }
        }

        return $param;
    }

    /**
     * 头条创建资产与事件
     * @param SiteConfigParam $param
     * @param MediaAccountInfoParam $media_account_info_param
     * @return SiteConfigParam
     * @throws \Throwable
     */
    public function addAssetAndEvent(SiteConfigParam $param, MediaAccountInfoParam $media_account_info_param)
    {
        /** 落地页资产 橙子建站 */
        if ($param->convert_source_type === ConvertSourceType::H5_API) {
            // 判断是否字节小游戏，获取小游戏资产ID
            if (in_array($param->plat_id, [PlatId::DY_MINI, PlatId::DY_MINI_PROGRAM])) {
                $new_create_asset = true;
                $mini_game_asset_id = '';
                $media_toutiao = new MediaToutiao();
                $byte_game_info = $media_toutiao->getByteGameInfo($param->platform, $param->media_type, $param->game_id);

                // 先获取是否有自建的字节小游戏
                $instance_id = $media_toutiao->getMicroGameInstanceId(
                    $media_account_info_param->account_id,
                    $media_account_info_param->access_token,
                    $byte_game_info['byte_game_id'],
                    "CREATE_ONLY"
                );
                // 没有再获取共享的
                if (!$instance_id) {
                    $instance_id = (new MediaToutiao())->getMicroGameInstanceId(
                        $media_account_info_param->account_id,
                        $media_account_info_param->access_token,
                        $byte_game_info['byte_game_id'],
                        "SHARE_ONLY"
                    );
                    if (!$instance_id) {
                        throw new AppException("获取字节小游戏资产ID失败，请确认媒体后台是否拥有小游戏权限或媒体SDK信息的字节小游戏ID是否正确");
                    }
                }

                // 获取创建的资产
                $page = 1;
                $page_size = 100;
                while (true) {
                    $data = $media_toutiao->getAssetList(
                        $media_account_info_param->account_id,
                        $media_account_info_param->access_token,
                        ["asset_type" => EventManager::ASSET_DY_MINI_GAME],
                        $page,
                        $page_size
                    );
                    $list = $data['asset_list'];

                    $asset_id_list = [];
                    foreach ($list as $item) {
                        if ($item["asset_type"] === EventManager::ASSET_DY_MINI_GAME && in_array($item["share_type"], [EventManager::ASSET_ROLE_SHARED, EventManager::ASSET_ROLE_MINE])) {
                            $asset_id_list[] = $item["asset_id"];
                        }
                    }

                    // 获取资产详情
                    $chunks = array_chunk($asset_id_list, 50);
                    foreach ($chunks as $chunk) {
                        $asset_list = $media_toutiao->getAssetDetail(
                            $media_account_info_param->account_id,
                            $media_account_info_param->access_token,
                            $chunk
                        );

                        if (!empty($asset_list['asset_list'] ?? [])) {
                            foreach ($asset_list['asset_list'] as $asset) {
                                if ($asset["asset_type"] === EventManager::ASSET_DY_MINI_GAME && $asset['micro_app_instance_id'] == $instance_id) {
                                    $mini_game_asset_id = $asset['asset_id'];
                                    $new_create_asset = false;
                                    break 3;
                                }
                            }
                        }
                    }

                    $page_info = $data['page_info'];
                    $total_page = $page_info['total_page'] ?? 0;
                    $page++;
                    if ($page > $total_page) {
                        break;
                    }
                }

                if ($new_create_asset) {
                    /** 创建头条小游戏资产 */
                    $asset_create_param = new AssetCreateParam([
                        'media_account' => $media_account_info_param,
                        'asset_type' => EventManager::ASSET_DY_MINI_GAME,
                        'app_name' => $param->app_name,
                        'app_id' => $byte_game_info['byte_game_id'],
                        'game_type' => $param->game_type,
                        'convert_type' => $param->convert_type,
                        'site_id' => $param->site_id,
                        'instance_id' => $instance_id,
                        'plat_id' => $param->plat_id
                    ]);
                    $asset_data = (new MediaToutiao())->createAsset($asset_create_param);
                    $mini_game_asset_id = $asset_data['asset_id'];
                }

                // 因为要对比转化来源，所以必须获取资产已创建事件列表
                $event_list = $media_toutiao->getEventConfigsList($media_account_info_param->account_id, $media_account_info_param->access_token, $mini_game_asset_id);

                // 找出所需创建事件的ID
                $exist_event_ids = array_column($event_list['event_configs'], 'event_id');
                $event_ids = array_diff(EventManager::EVENT_ID_MAP, $exist_event_ids);

                /** 创建事件 */
                foreach ($event_ids as $event_id) {
                    try {
                        $event_create_param = new EventCreateParam([
                            'media_account'       => $media_account_info_param,
                            'asset_id'            => $mini_game_asset_id,
                            'event_id'            => $event_id,
                            'convert_source_type' => "MINI_PROGRAME_API",
                        ]);
                        (new MediaToutiao())->createEvent($event_create_param);
                    } catch (\Throwable $e) {
                        // 达不到付费ROI的要求，不能创建，先跳过
                        if (strpos($e->getMessage(), '事件已被创建或不支持创建') !== false && $event_id === EventManager::EVENT_ID_MAP[EventManager::EVENT_PURCHASE_ROI]) {
                            continue;
                        }
                        throw $e;
                    }
                }

                $param->ext['asset_ids'] = [$mini_game_asset_id];
                $param->ext['micro_game_instance_id'] = $instance_id;

                (new SiteModel())->edit($param->platform, $param->site_id, [
                    'ext' => json_encode((object)$param->ext)
                ]);
                (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                    'asset_ids' => json_encode($param->ext['asset_ids'] ?? [])
                ]);
            } else {
                // 微信小游戏
                $media_toutiao = new MediaToutiao();
                $wechat_game_info = $media_toutiao->getWechatGameInfo($param->platform, $param->media_type, $param->game_id);

                // 先获取是否有自建的字节小游戏
                $instance_id = $media_toutiao->getWechatGameInstanceId(
                    $media_account_info_param->account_id,
                    "AD",
                    $media_account_info_param->access_token,
                    $wechat_game_info['mini_game_original_id'],
                    "CREATE_ONLY"
                );
                // 没有再获取共享的
                if (!$instance_id) {
                    $instance_id = (new MediaToutiao())->getWechatGameInstanceId(
                        $media_account_info_param->account_id,
                        "AD",
                        $media_account_info_param->access_token,
                        $wechat_game_info['mini_game_original_id'],
                        "SHARE_ONLY"
                    );
                    if (!$instance_id) {
                        throw new AppException("获取微信小游戏资产ID失败，请确认媒体后台是否拥有小游戏权限或媒体SDK信息的微信小程序原始ID是否正确");
                    }
                }

                $param->ext['asset_ids'] = [];
                $param->ext['micro_game_instance_id'] = $instance_id;

                (new SiteModel())->edit($param->platform, $param->site_id, [
                    'ext' => json_encode((object)$param->ext)
                ]);
                (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                    'asset_ids' => json_encode($param->ext['asset_ids'] ?? [])
                ]);
            }
        } else {
            /** 应用资产 */
            $media_toutiao = new MediaToutiao();

            // 先检查该应用有无已创建资产
            $new_create_asset = true;
            $asset_id = '';
            $asset_download_url = "";
            $page = 1;
            $page_size = 100;
            while (true) {
                $data = $media_toutiao->getAssetList(
                    $media_account_info_param->account_id,
                    $media_account_info_param->access_token,
                    ["asset_type" => EventManager::ASSET_APP],
                    $page,
                    $page_size
                );
                $list = $data['asset_list'];

                $asset_id_list = [];
                foreach ($list as $item) {
                    if ($item["asset_type"] === EventManager::ASSET_APP && $item["share_type"] == EventManager::ASSET_ROLE_MINE) {
                        $asset_id_list[] = $item["asset_id"];
                    }
                }

                // 获取资产详情
                $chunks = array_chunk($asset_id_list, 50);
                foreach ($chunks as $chunk) {
                    $asset_list = $media_toutiao->getAssetDetail(
                        $media_account_info_param->account_id,
                        $media_account_info_param->access_token,
                        $chunk
                    );

                    if (!empty($asset_list['asset_list'] ?? [])) {
                        foreach ($asset_list['asset_list'] as $asset) {
                            if ($asset["asset_type"] === EventManager::ASSET_APP && $asset['package_name'] === $param->package) {
                                $asset_id = $asset['asset_id'];
                                $asset_download_url = $asset['download_url'];
                                $new_create_asset = false;
                                break 3;
                            }
                        }
                    }
                }

                $page_info = $data['page_info'];
                $total_page = $page_info['total_page'] ?? 0;
                $page++;
                if ($page > $total_page) {
                    break;
                }
            }

            $download_url = $param->download_url;
            // IOS如果使用了苹果自定义落地页 下载链接会带上apple_ppid参数 创建资产和监测需要原下载链接
            if ($param->game_type === "IOS" && !empty($param->ext['apple_ppid'] ?? '')) {
                $apple_ppid = $param->ext['apple_ppid'];
                $pos = strpos($param->download_url, "?ppid={$apple_ppid}");
                if ($pos > 0) {
                    $download_url = str_replace("?ppid={$apple_ppid}", "", $param->download_url);
                } else {
                    $download_url = str_replace("&ppid={$apple_ppid}", "", $param->download_url);
                }
            }

            if ($new_create_asset) {
                /** 创建资产 */
                $asset_create_param = new AssetCreateParam([
                    'media_account' => $media_account_info_param,
                    'asset_type' => EventManager::ASSET_APP,
                    'app_name' => $param->app_name,
                    'asset_app_name' => $param->sdk_ext['asset_app_name'] ?? '',
                    'package_name' => $param->package,
                    'download_url' => $download_url,
                    'app_id' => $param->appid,
                    'package_id' => $param->app_secret,
                    'game_type' => $param->game_type,
                    'convert_source_type' => $param->convert_source_type,
                    'convert_type' => $param->convert_type,
                    'site_id' => $param->site_id
                ]);
                $asset_data = (new MediaToutiao())->createAsset($asset_create_param);
                $asset_id = $asset_data['asset_id'];
            }

            $param->ext['asset_ids'] = [$asset_id];

            if ($asset_download_url && $param->game_type == 'IOS') {
                $download_url = $asset_download_url;
            }

            // 创建监测链接组
            $track_url_create_param = new TrackUrlGroupCreateParam([
                'media_account' => $media_account_info_param,
                'site_id' => $param->site_id,
                'asset_id' => $asset_id,
                'download_url' => $download_url,
                'action_track_url' => $param->action_track_url,
                'track_url' => $param->display_track_url,
                'app_name' => $param->app_name,
                'package_id' => $param->app_secret,
                'game_type' => $param->game_type,
                'convert_type' => $param->convert_type,
                'deep_external_action' => $param->deep_external_action,
                'convert_data_type' => $param->convert_data_type
            ]);

            $i = 0;
            while ($i < 20) {
                try {
                    $i++;
                    $media_toutiao->createTrackUrlGroup($track_url_create_param);
                    break;
                } catch (\Throwable $e) {
                    // 批量任务会重启，广告位id会相同，跳过此报错
                    if (strpos($e->getMessage(), '存在相同的监测链接组名称') !== false) {
                        break;
                    }
                    if (strpos($e->getMessage(), '只有应用资产需要传递下载链接') === false) {
                        throw $e;
                    }
                }
            }

            // 暂时先不获取刚创建的监测链接组ID 等后续接口优化

            // 因为要对比转化来源，所以必须获取资产已创建事件列表
            $event_list = $media_toutiao->getEventConfigsList($media_account_info_param->account_id, $media_account_info_param->access_token, $asset_id);

            // 对比回传方式和所选转化来源是否一致 TODO 后续在前端加一个检查
            $track_types_list = array_column($event_list['event_configs'], 'track_types');
            foreach ($track_types_list as $item) {
                $track_type = $item[0] ?? '';
                if (!empty($track_type)) {
                    $event_convert_source_type = array_search($track_type, EventCreateParam::TRACK_TYPES[$param->media_type]);
                    if ($event_convert_source_type && $event_convert_source_type !== $param->convert_source_type) {
                        throw new AppException('资产事件已有转化来源与广告配置转化来源不一致，请选择 ' . ConvertSourceType::MAP[$event_convert_source_type]);
                    }
                }
            }

            // 找出所需创建事件的ID
            $exist_event_ids = array_column($event_list['event_configs'], 'event_id');
            $event_ids = array_diff(EventManager::EVENT_ID_MAP, $exist_event_ids);

            /** 创建事件 */
            foreach ($event_ids as $event_id) {
                try {
                    $event_create_param = new EventCreateParam([
                        'media_account'       => $media_account_info_param,
                        'asset_id'            => $asset_id,
                        'event_id'            => $event_id,
                        'convert_source_type' => $param->convert_source_type,
                        'convert_data_type'   => $param->convert_data_type,
                        'statistical_type'    => 'ONLY_ONE' // 暂定仅一次，只有付费允许每一次，该字段后续会下线
                    ]);
                    (new MediaToutiao())->createEvent($event_create_param);
                } catch (\Throwable $e) {
                    // 达不到付费ROI的要求，不能创建，先跳过
                    if (strpos($e->getMessage(), '事件已被创建或不支持创建') !== false && $event_id === EventManager::EVENT_ID_MAP[EventManager::EVENT_PURCHASE_ROI]) {
                        continue;
                    }
                    throw $e;
                }
            }

            (new SiteModel())->edit($param->platform, $param->site_id, [
                'ext' => json_encode((object)$param->ext)
            ]);
            (new V2DimSiteIdModel())->edit($param->platform, $param->site_id, [
                'asset_ids' => json_encode($param->ext['asset_ids'])
            ]);
        }
        return $param;
    }

    /**
     * 获取直播间对接人权限
     * @param $user_id
     * @param $platform
     * @return array
     */
    public function getInterfacePersonList($platform, $user_id)
    {
        $user_level = (new UserLevelModel())->getUserLevel($user_id);
        if ($user_level->isNotEmpty()) {
            $module_level_info = (array)$user_level->where('module', 'dsp')->first();
            if (!empty($module_level_info)) {
                $level = $module_level_info['level'];
                $rank_id = (new PermissionService())->getUserRankId($module_level_info);
            } else {
                throw new AppException("获取异常，user_id:{$user_id}没有dsp权限，无法获取");
            }
        } else {
            $level = UserService::LEVEL_SUPER;
            $rank_id = 0;
        }

        // 平台权限
        $platforms = (new PermissionService())->getPlatform($level, $rank_id);
        $platform_arr = array_column($platforms['platform_list'], 'platform_id');

        if ($platform) {
            $option = [
                ['platform = ?', $platform],
            ];
        } else {
            $option[] = [rtrim("platform IN (" . str_repeat('?,', count($platform_arr)), ',') . ')', $platform_arr];
        }
        return (new V2DimSiteIdModel())->getListLikeAgentLeader('', $option, -1, 0)->pluck('agent_leader')->unique()->toArray();
    }

    /**
     * 子平台下载链接特殊签名
     * @param $download_url
     * @return string
     */
    public function genDownloadUrlWithSign($download_url)
    {
        // 已有签名则直接返回
        if (strpos($download_url, '?sign') !== false || empty($download_url)) {
            return $download_url;
        }

        $time = time();
        $rand = chr(rand(97, 122)) . $time . chr(rand(97, 122));
        $m = "$time-$rand-0-";
        $sign = md5(parse_url($download_url, PHP_URL_PATH) . '-' . $m . 'mTUSHmysz4b0y94GtkLC');
        $download_url .= "?sign={$m}{$sign}";
        return $download_url;
    }
}
