<?php

namespace App\Service;

use App\Model\HttpModel\Material\MaterialTaskModel;
use App\Model\SqlModel\DataMedia\OdsMaterialTagsModel;
use App\Model\SqlModel\Tanwan\V2DimAgentLeaderGroupModel;
use App\Model\SqlModel\Zeda\MaterialExpertModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Utils\Helpers;
use Throwable;

class MaterialExpertFilterService
{
    /**
     * @param $data
     */
    static public function addExpertTask(array $material_file_ids = [])
    {
        if ($material_file_ids) {
            try {
                $result = (new MaterialTaskModel())->addExpertTask($material_file_ids);
                Helpers::getLogger('material_expert')->info('material_expert', [
                    'request' => $material_file_ids,
                    'response' => $result,
                ]);
            } catch (Throwable $e) {
                Helpers::getLogger('material_expert')->error('material_expert error', [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
    }

    /**
     * @param $agent_leader
     * @param $media_type
     * @param $platform
     * @param array $material_file_id_list
     * @param string $from
     * @return array
     */
    static public function incorrectExpert($agent_leader, $media_type, $platform, array $material_file_id_list = [], $from = 'common'): array
    {
        if (!$material_file_id_list) {
            return [];
        }

        $result = [];

        $expert_list = [];

        $agent_leader_group = (new V2DimAgentLeaderGroupModel())->getDataByLeaders([$agent_leader]);


        if ($agent_leader_group->isNotEmpty()) {
            $agent_leader_group = $agent_leader_group[0];
        } else {
            return [];
        }

        $material_file_info_list = (new MaterialFileModel())->getListOfThemeInfoByIds($material_file_id_list);

        $material_file_tag_list = (new OdsMaterialTagsModel())->getTagInfoByFileIds($material_file_id_list)->keyBy('material_file_id');

        foreach ($material_file_tag_list as $expert) {
            $expert_list = array_merge($expert_list, json_decode($expert->talent_tag, true) ?: []);
        }

        $expert_list = array_values(array_unique($expert_list));

        if ($material_file_tag_list && $material_file_info_list && $expert_list) {
            $expert_info_list = (new MaterialExpertModel())->getListByNames($platform, $expert_list)->keyBy('name');

            foreach ($material_file_info_list as $file) {
                $experts = json_decode($material_file_tag_list[$file->id]->talent_tag ?? '[]', true);
                $objects = json_decode($material_file_tag_list[$file->id]->object_tag ?? '[]', true);
                $marker_list = array_merge($experts ?: [], $objects ?: []);
                $expert_error_content = '';
                $material_time_error_content = '';
                if (((int)($file->theme_id / 1000)) != 93) {
                    if ($marker_list) {
                        foreach ($marker_list as $marker) {
                            if ($expert_info_list[$marker] ?? '') {
                                if ($expert_info_list[$marker]->status != 1) {
                                    $expert_error_content .= ($expert_error_content ? "$marker," : "素材文件id:$file->id,$file->filename,涉及过期的标识物:$marker,");
                                } else {
                                    $sign_time_list = json_decode($expert_info_list[$marker]->sign_time_list, true);
                                    $current_time = time();
                                    foreach ($sign_time_list as $sign_time_info) {
                                        $star_timestamp = strtotime($sign_time_info['start_time']);

                                        if ($from == 'batch_ad') {
                                            $star_timestamp = strtotime($sign_time_info['start_time']) - (86400 * $expert_info_list[$marker]->batch_ad_advance_day);
                                        }

                                        if (
                                            (!(
                                                $current_time >= $star_timestamp &&
                                                $current_time <= strtotime($sign_time_info['end_time'])
                                            ))
                                            &&
                                            in_array($agent_leader_group->agent_leader_group_id, $sign_time_info['agent_leader_group_list'])
                                            &&
                                            (empty($media_type) || empty($sign_time_info['media_type_list']) || in_array($media_type, $sign_time_info['media_type_list']))
                                        ) {
                                            $expert_error_content .= ($expert_error_content ? "$marker," : "素材文件id:$file->id,{$file->filename}涉及过期 或 非法 的标识物:$marker,");
                                        }
                                    }
                                }

                                $material_time_list = json_decode($expert_info_list[$marker]->material_time_list, true);
                                foreach ($material_time_list as $material_time_info) {
                                    if (($material_time_info['start_time'] ?? '') && ($material_time_info['end_time'] ?? '')) {
                                        if (
                                            (!(
                                                strtotime("{$material_time_info['start_time']} 00:00:00 ") <= $file->create_time &&
                                                strtotime("{$material_time_info['end_time']} 23:59:59") >= $file->create_time
                                            ))
                                            &&
                                            in_array($agent_leader_group->agent_leader_group_id, $material_time_info['agent_leader_group_list'])
                                        ) {
                                            $material_time_error_content .= ($expert_error_content ? "$marker," : "素材文件id:$file->id,{$file->filename}包含$marker,创建时间不在{$marker}的素材可用时间段,");
                                        }
                                    }
                                }
                            }
                        }
                        $expert_error_content = trim($expert_error_content, ',');
                        $material_time_error_content = trim($material_time_error_content, ',');
                    }
                }
                $result[$file->id] = [
                    'status' => !($expert_error_content || $material_time_error_content),
                    'content' => $expert_error_content . ";" . $material_time_error_content,
                ];
            }
        } else {
            foreach ($material_file_info_list as $file) {
                $result[$file->id] = [
                    'status' => true,
                    'content' => '',
                ];
            }
        }
        return $result;
    }
}
