<?php


namespace App\Service;

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use Common\EnvConfig;

class AudienceService
{
    const AUDIENCE_DIR = SRV_DIR . '/audience';

    /**
     * @param $media_type
     * @return string
     */
    static public function getAudienceDir($media_type)
    {
        switch ($media_type) {
            case MediaType::KUAISHOU:
                return self::AUDIENCE_DIR . '/kuaishou/';
            case MediaType::BAIDU:
                return self::AUDIENCE_DIR . '/baidu/';
            case MediaType::UC:
                return self::AUDIENCE_DIR . '/uc/';
            case MediaType::JN_PUSH:
                return SRV_DIR . EnvConfig::UPLOAD_PATH . '/audience/';
            case MediaType::TOUTIAO:
                return self::AUDIENCE_DIR . '/toutiao/';
            case MediaType::TENCENT:
                return self::AUDIENCE_DIR . '/tencent/';
            case MediaType::IQIYI:
                return self::AUDIENCE_DIR . '/iqiyi/';
            case MediaType::HUAWEI:
                return self::AUDIENCE_DIR . '/huawei/';
            default:
                throw new AppException("尚未支持的媒体类型，请直接使用常量AUDIENCE_DIR");
        }
    }

    /**
     * @param $uuid
     * @param int $user_id_type
     * @param bool $with_dir
     * @return string
     * @see UserIDType $user_id_type
     */
    static public function getFile($uuid, $user_id_type, $with_dir = true)
    {
        switch ($user_id_type) {
            case UserIDType::IMEI_MD5:
                $suffix = '安卓';
                break;
            case UserIDType::IDFA_MD5:
                $suffix = 'IOS';
                break;
            case UserIDType::OAID:
                $suffix = 'OAID';
                break;
            case UserIDType::OAID_MD5:
                $suffix = 'OAID_MD5';
                break;
            case UserIDType::OPEN_ID:
                $suffix = 'OPEN_ID';
                break;
            default:
                throw new AppException("尚未支持的文件类型({$user_id_type})");
        }
        return $with_dir ? (self::AUDIENCE_DIR . "/task-{$uuid}-{$suffix}.txt") : "task-{$uuid}-{$suffix}.txt";
    }

    /**
     * @param $upload_file_name
     * @return string
     */
    static public function getUploadFile($upload_file_name)
    {
        $filename = basename($upload_file_name, ".txt");
        $filename_arr = explode('-', $filename);
        if (count($filename_arr) !== 5) {
            throw new AppException("{$upload_file_name}文件名错误");
        }
        $file_yearmonth = date('Ym', strtotime($filename_arr[2]));
        $archive_month_path = implode('/', [AudienceService::AUDIENCE_DIR, $file_yearmonth]);
        if (file_exists("{$archive_month_path}/{$filename}.txt")) {
            return "{$archive_month_path}/{$filename}.txt";
        } else if (file_exists(AudienceService::AUDIENCE_DIR . "/{$filename}.txt")) {
            return AudienceService::AUDIENCE_DIR . "/{$filename}.txt";
        }
        throw new AppException("{$upload_file_name}文件不存在");
    }

    /**
     * 不保证文件是否存在，只是简单拼接路径
     * @param $file_time
     * @param $filename
     * @param $operation_type
     * @return string
     * @see AudienceChangeLogModel operation_type APPEND | RESET
     */
    static public function getBackupFile($file_time, $filename, $operation_type)
    {
        $sub_path = date("Ym", $file_time);
        if ($operation_type === AudienceChangeLogModel::APPEND) {
            return AudienceService::AUDIENCE_DIR . '/' . $sub_path . '/' . $filename;
        } else if ($operation_type === AudienceChangeLogModel::RESET) {
            $name = date("Ymd", $file_time);
            $list = explode('.', $filename);
            $last_idx = count($list) - 2;
            $list[$last_idx] .= "-{$name}";
            return AudienceService::AUDIENCE_DIR . '/' . $sub_path . '/' . implode('.', $list);
        } else {
            throw new AppException('尚未支持的操作类型');
        }

    }

    public function syncAudienceInfo($audience_id, $state)
    {
        (new NoticeService())->broadcast(NoticeService::NOTICE_AUDIENCE_TASK, [
            'audience_id' => $audience_id,
            'state' => $state,
            'state_name' => AudienceModel::STATE_MAP[$state],
        ]);
    }
}
