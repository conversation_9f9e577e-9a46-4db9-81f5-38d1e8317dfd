<?php


namespace App\Service;

use App\Constant\AgentGroup;
use App\Constant\AgentType;
use App\Constant\MediaType;
use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Zeda\AgentGroupModel;
use App\Model\SqlModel\Zeda\AgentGroupUserModel;
use App\Model\SqlModel\Zeda\AgentLeaderChangeLogModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\AgentTypeModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\MysqlConnection;
use App\Param\AgentParam;
use App\Param\MediaAccountInfoParam;
use App\Service\PlatformAD\PlatformAD;
use Throwable;

class AgentService
{
    public function getAgentTypeOptions()
    {
        $model = new AgentTypeModel();
        return $model->getAll()->map(function ($item) {
            $item->label = $item->name;
            $item->value = $item->id;
            return $item;
        });
    }

    public function getAgentGroupOptions()
    {
        $model = new AgentGroupModel();
        $agent_group_list = $model->getAll();
        return $agent_group_list
            ->transform(function ($item) {
                $item->label = $item->name;
                $item->value = $item->id;
                return $item;
            });
    }

    /**
     * @param        $account_info
     * @param        $agent_group_id
     * @param        $creator_id
     * @param        $creator
     * @param bool   $is_cpa 是否为CPA结算
     * @param string $aweme_account
     * @return \App\Model\SqlModel\Database\ZDBuilder|\Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object
     * @throws \Exception
     */
    public function getADAgentByAccount($account_info, $agent_group_id, $creator_id, $creator, $is_cpa = false, $aweme_account = '')
    {
        $agent_group_name = AgentGroup::TYPE_MAP[$agent_group_id];

        $outer_service = new OuterService();
        $create_toutiao_cpa_agent = $is_cpa && in_array($agent_group_id, [AgentGroup::TOUTIAO_LIVE, AgentGroup::TOUTIAO_UOP]);
        if ($create_toutiao_cpa_agent) {
            $agent_info = (new AgentModel())->getDataForAwemeAD($account_info->platform, $account_info->account_id, $agent_group_id, $aweme_account);
        } else {
            $agent_info = (new AgentModel())->getDataForAD($account_info->platform, $account_info->account_id, $agent_group_id);
        }

        if (empty($agent_info)) {
            if ($create_toutiao_cpa_agent) {
                $agent_param = new AgentParam([
                    'platform' => $account_info->platform,
                    'account_id' => $account_info->account_id,
                    'media_type' => $account_info->media_type,
                    'agent_name' => "{$agent_group_name}-系统-{$account_info->account_name}-cpa",
                    'agent_group' => $agent_group_id,
                    'agent_group_name' => AgentGroup::TYPE_MAP[$agent_group_id],
                    'agent_leader' => $account_info->agent_leader,
                    'company' => $account_info->company,
                    'user_name' => $outer_service->getAccountByAwemeAccount($aweme_account),
                    'user_pwd' => "{$aweme_account}@{$account_info->platform}1234",
                    'pay_type' => 3, // cpa结算类型
                    'statistic_caliber' => 3, // 统计口径：按根游戏注册（默认使用）
                    'route_list' => '1,2', // 固定权限 推广数据分析 / 推广总览 / 指标选择 / 转化链 / 注册数
                    'route_permission_ids' => [15],
                    'for_ad' => 1,
                ]);

                $rollback = false;
                $err_message = '';
                MysqlConnection::getConnection()->beginTransaction();
                try {
                    $agent_info = (new AgentService())->addAgent($agent_param, $creator_id, $creator);
                } catch (Throwable $e) {
                    $rollback = true;
                    $err_message = $e->getMessage();
                }

                MysqlConnection::getConnection('agency')->beginTransaction();
                try {
                    $outer_service->addAccount($agent_param, $creator);
                } catch (Throwable $e) {
                    $rollback = true;
                    $err_message = $e->getMessage();
                }

                if ($rollback) {
                    MysqlConnection::getConnection()->rollBack();
                    MysqlConnection::getConnection('agency')->rollBack();
                    throw new AppException($err_message);
                }

                MysqlConnection::getConnection()->commit();
                MysqlConnection::getConnection('agency')->commit();

            } else {
                $agent_param = new AgentParam([
                    'platform' => $account_info->platform,
                    'account_id' => $account_info->account_id,
                    'media_type' => $account_info->media_type,
                    'agent_name' => "{$agent_group_name}-系统-{$account_info->account_name}",
                    'agent_group' => $agent_group_id,
                    'agent_group_name' => AgentGroup::TYPE_MAP[$agent_group_id],
                    'agent_leader' => $account_info->agent_leader,
                    'company' => $account_info->company,
                    'user_name' => "{$agent_group_name}-系统-{$account_info->account_id}",
                    'user_pwd' => '123456',
                    'for_ad' => 1,
                ]);
                MysqlConnection::getConnection()->beginTransaction();
                try {
                    $agent_info = (new AgentService())->addAgent($agent_param, $creator_id, $creator);
                    MysqlConnection::getConnection()->commit();
                } catch (Throwable $e) {
                    MysqlConnection::getConnection()->rollBack();
                    throw new AppException($e->getMessage());
                }
            }
        }

        return $agent_info;
    }

    /**
     * 需要保证强一致性，请调用者自行开事务
     * @param AgentParam $param
     * @param $user_id
     * @param $username
     * @return object
     * @throws \Exception
     */
    public function addAgent(AgentParam $param, $user_id, $username)
    {
        $ads_platform = PlatformAD::create($param->platform);
        $platform_agent_data = $ads_platform->addAgent($param, $username);
        $param->agent_id = $agent_id = $platform_agent_data['agent_id'];
        $create_time = $param->create_time;

        // 联运渠道处理
        if (isset($param::HARD_LY_AGENT_GROUP_CHANNEL[$param->agent_group])) {
            $param->proxy_type = 2;
            $param->channel_id = $param::HARD_LY_AGENT_GROUP_CHANNEL[$param->agent_group]['channel_id'];
            $param->channel_name = $param::HARD_LY_AGENT_GROUP_CHANNEL[$param->agent_group]['channel_name'];
        }

        $agent_data = [
            'platform' => $param->platform,
            'media_type' => $param->media_type,
            'account_id' => $param->account_id,
            'agent_id' => $agent_id,
            'agent_name' => $param->agent_name,
            'agent_group' => $param->agent_group,
            'agent_group_id' => $param->agent_group,
            'agent_leader' => $param->agent_leader,
            'agent_type' => $param->agent_type,
            'company' => $param->company,
            'own' => $param->own,
            'account_type' => $param->account_type,
            'id_card' => $param->id_card,
            'id_img' => $param->id_img,
            'user_name' => $param->user_name,
            'user_pwd' => $param->user_pwd,
            'bank_holder' => $param->bank_holder,
            'bank_area' => $param->bank_area,
            'bank_name' => $param->bank_name,
            'bank_card_number' => $param->bank_card_number,
            'person' => $param->person,
            'qq' => $param->qq,
            'email' => $param->email,
            'mobile' => $param->mobile,
            'tel' => $param->tel,
            'address' => $param->address,
            'detail_address' => $param->detail_address,
            'protocol_number' => $param->protocol_number,
            'protocol_type' => $param->protocol_type,
            'ad_number' => $param->ad_number,
            'creator_id' => $user_id,
            'creator' => $username,
            'create_time' => time(),
            'update_time' => time(),
            'state' => $param->state,
            'pay_type' => $param->pay_type,
            'statistic_caliber' => $param->statistic_caliber,
            'for_ad' => $param->for_ad,
            'proxy_type' => $param->proxy_type,
            'channel_id' => $param->channel_id,
            'channel_name' => $param->channel_name,
        ];
        (new AgentModel())->add($agent_data);
        (new AgentLeaderChangeLogModel())->add($param, $create_time);
        if (!isset($platform_agent_data['is_created']) || $platform_agent_data['is_created'] !== true) {
            // 获取原来的adb的agent, 拿到已有渠道的proxy_type
            $v2_dim_agent_id_model = new V2DimAgentIdModel();
            $ori_adb_agent_info = $v2_dim_agent_id_model->getDataByAgentId($param->platform, $param->agent_id);
            if (!empty($ori_adb_agent_info)) {
                $param->proxy_type = $ori_adb_agent_info->proxy_type ?? 1;
                $param->channel_id = $ori_adb_agent_info->channel_id ?? 0;
                $param->channel_name = $ori_adb_agent_info->channel_name ?? '';
            }

            $v2_dim_agent_id_model->replace($param, $create_time > 0 ? (date('Y-m-d', $create_time) . ' 00:00:00') : '1970-01-01 08:00:00');
        }
        return (object)$agent_data;
    }

    /**
     * 需要保证强一致性，请调用者自行开事务
     * @param AgentParam $param
     * @param object $ori_agent_info 用于记录agent修改
     * @param $user_id
     * @param $username
     * @return bool
     */
    public function editAgent(AgentParam $param, $ori_agent_info, $user_id, $username)
    {
        $agent_model = new AgentModel();
        $ads_platform = PlatformAD::create($param->platform);
        $update_time = $param->update_time > 0 ? $param->update_time : time();

        // 背景：需要传负责人时间区间给他们。
        // 问题：用户编辑渠道信息时（非修改渠道负责人），chargeman_starttime会取当前时间，导致时间区间错误
        // 处理：此传入v2_dim_agent_id表里最新的agent_leader_start_time，然后AgentService->editAgent()方法判断负责人是否有改变，
        //      有改变，则按原逻辑取当前时间作为update_time，若负责人无改变，则取最大的agent_leader_start_time作为chargeman_starttime
        if ($ori_agent_info->agent_leader !== $param->agent_leader) {
            // 如果切换了负责人，则取当下的更新时间
            $param->update_time = $update_time;
        }else{
            // 如果没切换负责人，应取回v2_dim_agent最新一条的数据agent_leader_start_time
            $dim_agent_info = (new V2DimAgentIdModel())->getAgentListByAgentId($param->platform, $param->agent_id);
            $param->update_time = strtotime($dim_agent_info->pluck('agent_leader_start_time')->max());
        }

        $param->for_ad = $ori_agent_info->for_ad;
        $ads_platform->editAgent($param, $username);
        $data = [
            'platform' => $param->platform,
            'media_type' => $param->media_type,
            'account_id' => $param->account_id,
            'agent_id' => $param->agent_id,
            'agent_name' => $param->agent_name,
            'agent_group' => $param->agent_group,
            'agent_group_id' => $param->agent_group,
            'agent_leader' => $param->agent_leader,
            'agent_type' => $param->agent_type,
            'company' => $param->company,
            'own' => $param->own,
            'account_type' => $param->account_type,
            'id_card' => $param->id_card,
            'id_img' => $param->id_img,
            'bank_holder' => $param->bank_holder,
            'bank_area' => $param->bank_area,
            'bank_name' => $param->bank_name,
            'bank_card_number' => $param->bank_card_number,
            'person' => $param->person,
            'qq' => $param->qq,
            'email' => $param->email,
            'mobile' => $param->mobile,
            'tel' => $param->tel,
            'address' => $param->address,
            'detail_address' => $param->detail_address,
            'protocol_number' => $param->protocol_number,
            'protocol_type' => $param->protocol_type,
            'ad_number' => $param->ad_number,
            'editor_id' => $user_id,
            'editor' => $username,
            'update_time' => $update_time,
            'state' => $param->state,
        ];

        $agent_model->edit($param->platform, $param->agent_id, $data);
        $v2_dim_agent_id_model = new V2DimAgentIdModel();
        if ($ori_agent_info->agent_leader !== $param->agent_leader) {
            // 获取原来的adb的agent
            $ori_adb_agent_info = $v2_dim_agent_id_model->getDataByAgentId($param->platform, $param->agent_id);
            if (!empty($ori_adb_agent_info)) {
                $param->proxy_type = $ori_adb_agent_info->proxy_type ?? 1;
                $param->channel_id = $ori_adb_agent_info->channel_id ?? 0;
                $param->channel_name = $ori_adb_agent_info->channel_name ?? '';
            }
            // 硬核联运渠道特殊处理
            if (isset($param::HARD_LY_AGENT_GROUP_CHANNEL[$param->agent_group])) {
                $param->proxy_type = 2;
                $param->channel_id = $param::HARD_LY_AGENT_GROUP_CHANNEL[$param->agent_group]['channel_id'];
                $param->channel_name = $param::HARD_LY_AGENT_GROUP_CHANNEL[$param->agent_group]['channel_name'];
            }

            $agent_leader_model = new AgentLeaderChangeLogModel();
            $agent_leader_model->editEndTime($param, $update_time);
            $agent_leader_model->add($param, $update_time);
            $v2_dim_agent_id_model->editEndTime($param, date('Y-m-d', strtotime('-1 day', $update_time)) . ' 23:59:59');
            $v2_dim_agent_id_model->replace($param, date('Y-m-d', $update_time) . ' 00:00:00');
        }

        if ($ori_agent_info->agent_name !== $param->agent_name) {
            $v2_dim_agent_id_model->editAgentName($param);
        }

        if ($ori_agent_info->agent_group !== $param->agent_group) {
            (new SiteModel())->editByAgentId($param->platform, $param->agent_id, [
                'media_type' => $param->media_type,
                'agent_group' => $param->agent_group,
            ]);

            (new V2DimSiteIdModel())->editByAgentId($param->platform, $param->agent_id, [
                'media_type_id' => $param->media_type
            ]);

            $v2_dim_agent_id_model->editAgentGroup($param);
        }

        (new AgentSiteLogService())->editAgentLog((array)$ori_agent_info, $data, $user_id, $username);
        return true;
    }
}
