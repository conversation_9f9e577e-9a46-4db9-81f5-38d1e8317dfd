<?php

namespace App\Service;

use App\Model\SqlModel\DataMedia\OdsMediaGamePriceRangeLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;

class GamePriceRangeFilterService
{
    /**
     * @param $media_type
     * @param $platform
     * @param $game_id
     * @param $os
     * @param $agent_group_id
     * @param $convert_type
     * @param $deep_bid_type
     * @param $cpa_bid
     * @param $rol_goal
     * @return array
     */
    static public function isCorrectPrice(
        $media_type, $platform, $game_id, $os, $agent_group_id, $convert_type, $deep_bid_type, $cpa_bid, $rol_goal
    ): array
    {
        // todo 先获取游戏的根id和集团id
        $game_info = (new V2DimGameIdModel())->getDataByGameId($platform, $game_id);

        $result = [];
        if ($game_info) {
            // todo 根据根id，集团id，渠道组id，转化类型，深度转化类型 在表查询1次获取所有相关数据
            $rule_list = (new OdsMediaGamePriceRangeLogModel())->getRuleDataForADCompose(
                $media_type,
                $platform,
                $os,
                $agent_group_id,
                $game_id,
                $game_info->root_game_id,
                $game_info->clique_id,
                $convert_type,
                $deep_bid_type
            );
            if (count($rule_list) > 0) {


                // todo 判断有无根，有根按根
                foreach ($rule_list as $rule) {
                    if (!$rule->exclude_game_id) {
                        $exclude_game_id = json_decode($rule->exclude_game_id, true);
                        if ($exclude_game_id && in_array($game_id, $exclude_game_id)) {
                            continue;
                        }
                    }
                    // todo 判断出价和roi系数
                    if ($cpa_bid) {
                        if ($rule->cpa_bid < $cpa_bid) {
                            $result['bid'] = "game_id为{$game_id}在{$rule->agent_group_name}的情况下转化出价只能小于等于$rule->cpa_bid";
                        }
                    }
                    if ($rol_goal) {
                        if ($rule->roi_goal > $rol_goal) {
                            $result['roi_goal'] = "game_id为{$game_id}在{$rule->agent_group_name}的情况下roi系数只能大于等于$rule->roi_goal";
                        }
                    }
                    if ($result) {
                        break;
                    }
                }
            } else {
                $result = self::isCommonCorrectPrice($media_type, $convert_type, $deep_bid_type, $cpa_bid, $rol_goal);
            }
        } else {
            $result = self::isCommonCorrectPrice($media_type, $convert_type, $deep_bid_type, $cpa_bid, $rol_goal);
        }
        return $result;
    }

    /**
     * @param $media_type
     * @param $convert_type
     * @param $deep_bid_type
     * @param $cpa_bid
     * @param $rol_goal
     * @return array
     */
    static private function isCommonCorrectPrice($media_type, $convert_type, $deep_bid_type, $cpa_bid, $rol_goal): array
    {
        $result = [];
        switch ($media_type) {
            case 1:
                if ($convert_type == 'AD_CONVERT_TYPE_ACTIVE') {
                    if ($cpa_bid > 199) {
                        $result['bid'] = "dsp系统限制出价，出价只能小于等于199";
                    }
                }

                if ($convert_type == 'AD_CONVERT_TYPE_ACTIVE' && in_array($deep_bid_type, ['ROI_COEFFICIENT', 'BID_PER_ACTION', 'FIRST_AND_SEVEN_PAY_ROI', 'PER_AND_SEVEN_PAY_ROI'])) {
                    if ($cpa_bid > 999) {
                        $result['bid'] = "dsp系统限制出价，出价只能小于等于999";
                    }
                    if ($rol_goal < 0.02) {
                        $result['roi_goal'] = "dsp系统限制出价，roi系数只能大于等于 0.02";
                    }
                }

                if ($convert_type == 'AD_CONVERT_TYPE_PAY' && $deep_bid_type == 'BID_PER_ACTION') {
                    if ($cpa_bid > 599) {
                        $result['bid'] = "dsp系统限制出价，出价只能小于等于599";
                    }
                }
                return $result;
            default:
                if ($cpa_bid > 999) {
                    $result['bid'] = "dsp系统限制出价，出价只能小于等于999";
                }
                return $result;
        }
    }
}
