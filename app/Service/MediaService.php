<?php

/**
 * 媒体管理
 * User: zz
 * Date: 2018/5/18
 * Time: 14:29
 */

namespace App\Service;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\MediaAccountClueModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\MediaMajordomoAccountModel;
use App\Model\SqlModel\Zeda\MediaTypeModel;
use App\Param\MediaAccountInfoParam;
use App\Param\MediaMajordomoAccountInfoParam;
use App\Struct\Generate\MediaTypeGenerator;
use App\Utils\Helpers;
use Common\EnvConfig;

class MediaService
{
    const TOUTIAO = MediaType::TOUTIAO;
    const TENCENT = MediaType::TENCENT;
    const KUAISHOU = MediaType::KUAISHOU;
    const BAIDU = MediaType::BAIDU;
    const MP = MediaType::MP;
    const IQIYI = MediaType::IQIYI;

    private $developer_company_list = [];
    private $majordomo_account_list = [];

    static public function of($media_type)
    {
        $media_name = ucfirst(strtolower(MediaType::CONST_NAME_LIST[$media_type]));
        if (class_exists(__NAMESPACE__ . '\\' . $media_name . 'Service')) {
            $class = __NAMESPACE__ . '\\' . $media_name . 'Service';
            return new $class();
        } else if (class_exists(__NAMESPACE__ . '\\' . Helpers::pascal($media_name) . 'Service')) {
            $class = __NAMESPACE__ . '\\' . Helpers::pascal($media_name) . 'Service';
            return new $class();
        } else if (class_exists(__NAMESPACE__ . '\\' . strtoupper($media_name) . 'Service')) {
            $class = __NAMESPACE__ . '\\' . strtoupper($media_name) . 'Service';
            return new $class();
        } else {
            throw new AppException("错误的MediaService类名,{$media_name}");
        }
    }

    public function getMediaTypeOptions()
    {
        $model = new MediaTypeModel();
        return $model->getAll()->map(function ($item) {
            $item->label = $item->name;
            $item->value = $item->id;
            return $item;
        });
    }

    /**
     * 编辑媒体类型
     */
    public function genMediaTypeConstFile()
    {
        $generator = new MediaTypeGenerator();
        $generator->generate();
        $content = ob_get_contents();
        ob_clean();
        return $content;
    }

    public function getAuthCompanyList($platform, $media_type)
    {
        $list = (new MediaDeveloperAccountModel())->getListByPlatformMedia($platform, $media_type);
        return $list->pluck('company');
    }

    public function getAuthURL($platform, $media_type, $company, $creator_id, $auth_version = '', $majordomo_account_id = '', $account_type = '', $port_version = '')
    {
        $state = [
            'platform' => $platform,
            'creator_id' => $creator_id
        ];
        $developer_media_type = $media_type;
        if (in_array($developer_media_type, [MediaType::BAIDU, MediaType::BAIDU_SEARCH])) {
            $developer_media_type = MediaType::BAIDU;
        }
        $developer_account_list = (new MediaDeveloperAccountModel())->getListByPlatformMedia($platform, $developer_media_type);
        $developer_account = $developer_account_list->where('company', $company)->first();
        if (empty($developer_account)) {
            throw new AppException("请选择正确的主体");
        }
        $app_id = $developer_account->app_id;
        $domain = urlencode(EnvConfig::DOMAIN);
        switch ($media_type) {
            case MediaType::TOUTIAO:
            case MediaType::XINGTU:
                $state['company'] = $company;
                $state['media_type'] = $media_type;
                $state_json = json_encode($state);
                $state_json = urlencode($state_json);
                $url = "https://ad.oceanengine.com/openapi/audit/oauth.html?app_id={$app_id}&material_auth=1&state={$state_json}&redirect_uri={$domain}%2Ftoutiao%2Fnotify.html";
                break;
            case MediaType::TENCENT:
                $account_type = $account_type ?: "ACCOUNT_TYPE_QQ";
                $state['company'] = $company;
                $state['auth_version'] = $auth_version; // 授权类型 区分授权用来拉取线索的账号
                $state['port_version'] = $port_version; // 授权接口版本 区分1.0和3.0授权
                $state_json = json_encode($state);
                $state_json = urlencode($state_json);
                $url = "https://developers.e.qq.com/oauth/authorize?client_id={$app_id}&account_type={$account_type}&account_display_number=1&redirect_uri={$domain}%2Ftencent%2Fnotify.html&state={$state_json}";
                break;
            case MediaType::KUAISHOU:
                $state['company'] = $company;
                $state_json = json_encode($state);
                $state_json = urlencode($state_json);
                $url = "https://developers.e.kuaishou.com/tools/authorize?app_id={$app_id}&oauth_type=advertiser&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22account_service%22%2C%22ad_manage%22%5D&redirect_uri={$domain}%2Fkuaishou%2Fnotify.html&state={$state_json}";
                break;
            case MediaType::BAIDU:
            case MediaType::BAIDU_SEARCH:
                $state_str = urlencode("{$platform},{$creator_id},{$media_type}");
                $url = "https://u.baidu.com/oauth/page/index?platformId=4960345965958561794&scope=65,66,67,68,69,70,71,72,73,74,75,1001788,1001789,1001455,1001790,1001791,1002161,1002829,1004606&appId={$app_id}&state={$state_str}&callback={$domain}%2Fbaidu%2Fnotify.html";
                break;
            case MediaType::MP:
                $state_str = "{$platform},{$creator_id}";
                $url = "https://developers.e.qq.com/oauth/authorize?client_id={$app_id}&account_type=ACCOUNT_TYPE_WECHAT&account_display_number=1&redirect_uri={$domain}%2Fmp%2Fnotify.html&state={$state_str}";
                break;
            case MediaType::IQIYI:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://tuiguang.iqiyi.com/platform/authApp?app_id={$app_id}&state={$state_str}&scope=account,material,order,finance,report,page,dmp,tool&redirect_uri={$domain}%2Fiqiyi%2Fnotify.html";
                break;
            case MediaType::WEIBO:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://api.biz.weibo.com/oauth/authorize?client_id={$app_id}&state={$state_str}&response_type=code&scope=ads_management&redirect_uri={$domain}%2Fweibo%2Fnotify.html";
                break;
            case MediaType::YOUTUBE:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://accounts.google.com/o/oauth2/v2/auth?client_id={$app_id}&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fyoutube%20openid%20profile%20email&access_type=offline&include_granted_scopes=true&prompt=consent&state={$state_str}&response_type=code&redirect_uri={$domain}%2Fyoutube%2Fnotify.html";
                break;
            case MediaType::DOUYIN:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://open.douyin.com/platform/oauth/connect?client_key={$app_id}&scope=user_info,mobile_alert,item.comment,data.external.user,data.external.item,fans.data.bind,data.external.fans_source,data.external.fans_favourite,star_tops,star_top_score_display,star_author_score_display,video.list.bind&state={$state_str}&response_type=code&redirect_uri={$domain}%2Fdouyin%2Fnotify.html";
                break;
            case MediaType::QIANCHUAN:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://qianchuan.jinritemai.com/openapi/qc/audit/oauth.html?app_id={$app_id}&material_auth=1&state={$state_str}&redirect_uri={$domain}%2Fqianchuan%2Fnotify.html";
                break;
            case MediaType::TIKTOK:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://business-api.tiktok.com/portal/auth?app_id={$app_id}&state={$state_str}&redirect_uri={$domain}%2Ftiktok%2Fnotify.html";
                break;
            case MediaType::HUAWEI_JINGHONG:
                $state_str = "{$platform}.{$creator_id}.{$app_id}.$majordomo_account_id";
                $url = "https://oauth-login.cloud.huawei.com/oauth2/v2/authorize?response_type=code&client_id={$app_id}&scope=https%3A%2F%2Fwww.huawei.com%2Fauth%2Faccount%2Fbase.profile%20https%3A%2F%2Fads.cloud.huawei.com%2Freport%20https%3A%2F%2Fads.cloud.huawei.com%2Fpromotion%20https%3A%2F%2Fads.cloud.huawei.com%2Ftools%20https%3A%2F%2Fads.cloud.huawei.com%2Faccount&access_type=offline&redirect_uri={$domain}%2FhuaweiJinghong%2Fnotify.html&state={$state_str}";
                break;
            case MediaType::VIVO:
                $state_str = "{$platform},{$creator_id},{$company}";
                $url = "https://open-ad.vivo.com.cn/OAuth?clientId={$app_id}&state={$state_str}&redirectUri={$domain}%2Fvivo%2Fnotify.html";
                break;
            default:
                throw new AppException('该媒体不允许自动授权');
        }
        return $url;
    }

    public function getAccountList($platform, $media_type, $page, $rows)
    {
        $data = (new MediaAccountModel())->getListByPlatformMedia($platform, $media_type, $page, $rows);
        if ($media_type === MediaType::BAIDU) {
            $data['list'] = $data['list']->transform(function ($item) {
                return [
                    'account_id' => $item->account_id,
                    'password' => $item->access_token,
                    'username' => $item->company,
                    'target' => $item->account_name,
                    'token' => $item->refresh_token,
                ];
            });
        }
        return $data;
    }

    /**
     * 保存媒体账户
     * @param MediaMajordomoAccountInfoParam $media_account_info
     * @param $creator_id
     * @param $creator
     */
    public function saveAuthMajordomoAccount(MediaMajordomoAccountInfoParam $media_account_info, $creator_id, $creator)
    {
        if (in_array($media_account_info->account_id, [****************,****************])) {
            throw new AppException("管家{$media_account_info->account_id}不允许授权");
        }

        if (in_array($media_account_info->account, [
            '争游百度管家账户01','高热-量多多','BDCC-八九乐开天管家','原生-争游I组-B21KA04736-管家','BDCC-争游知定GRE管家'
        ])) {
            throw new AppException("管家{$media_account_info->account}不允许授权");
        }

        if (!$this->checkDevelopCompanyExists($media_account_info->media_type, $media_account_info->platform, $media_account_info->company, $media_account_info->account_id, true)) {
            throw new AppException("主体{$media_account_info->account_id}-{$media_account_info->company}不允许授权");
        }

        $majordomo_model = new MediaMajordomoAccountModel();
        if ($media_account_info->account_id === 0) {
            $majordomo = $majordomo_model->getDataByAccount($media_account_info->media_type, $media_account_info->account);
        } else {
            $majordomo = $majordomo_model->getDataByAccountId($media_account_info->media_type, $media_account_info->account_id);
        }
        if (empty($majordomo)) {
            $majordomo_model->add(
                [
                    'platform' => $media_account_info->platform,
                    'media_type' => $media_account_info->media_type,
                    'access_token' => $media_account_info->access_token,
                    'access_token_expires' => $media_account_info->access_token_expires,
                    'refresh_token' => $media_account_info->refresh_token,
                    'refresh_token_expires' => $media_account_info->refresh_token_expires,
                    'account_id' => $media_account_info->account_id,
                    'account' => $media_account_info->account,
                    'name' => $media_account_info->name,
                    'password' => $media_account_info->password,
                    'company' => $media_account_info->company,
                    'creator_id' => $creator_id,
                    'creator' => $creator,
                    'create_time' => time(),
                    'update_time' => time(),
                    'developer_app_id' => $media_account_info->developer_app_id,
                    'state' => 1,
                ]
            );
        } else {
            $data = [
                'company' => $media_account_info->company,
                'access_token' => $media_account_info->access_token,
                'access_token_expires' => $media_account_info->access_token_expires,
                'refresh_token' => $media_account_info->refresh_token,
                'refresh_token_expires' => $media_account_info->refresh_token_expires,
                'developer_app_id' => $media_account_info->developer_app_id,
                'state' => 1,
            ];

            if ($media_account_info->password) {
                $data['password'] = $media_account_info->password;
            }

            $majordomo_model->edit(
                $majordomo->id,
                $data
            );
        }
    }

    /**
     * 保存媒体账户
     * @param MediaAccountInfoParam $media_account_info
     * @param $creator_id
     * @param $creator
     * @return string 授权信息
     */
    public function saveAuthAccount(MediaAccountInfoParam $media_account_info, $creator_id, $creator)
    {
        if (!$this->checkDevelopCompanyExists($media_account_info->media_type, $media_account_info->platform, $media_account_info->company, $media_account_info->toutiao_majordomo_id)) {
            return "{$media_account_info->account_id}-{$media_account_info->company}不允许授权";
        }

        if (!$this->checkMajordomoPlatform($media_account_info->platform, $media_account_info->media_type, $media_account_info->toutiao_majordomo_id)) {
            return "{$media_account_info->account_id}与管家{$media_account_info->toutiao_majordomo_id}不同平台，不允许授权";
        }

        // 临时黑名单
        if ($media_account_info->company == '上饶市如玩信息技术有限公司' && $media_account_info->toutiao_majordomo_id == ****************) {
            return "该{$media_account_info->account_id}-{$media_account_info->company}不允许授权";
        }

        $media_account_model = new MediaAccountModel();
        $media_account = $media_account_model->getDataByAccountId($media_account_info->account_id, $media_account_info->media_type);
        if (empty($media_account)) {
            $data = [
                'platform' => $media_account_info->platform,
                'media_type' => $media_account_info->media_type,
                'access_token' => $media_account_info->access_token,
                'access_token_expires' => $media_account_info->access_token_expires,
                'refresh_token' => $media_account_info->refresh_token,
                'refresh_token_expires' => $media_account_info->refresh_token_expires,
                'account_id' => $media_account_info->account_id,
                'account_name' => $media_account_info->account_name,
                'account_password' => $media_account_info->account_password,
                'company' => $media_account_info->company,
                'agent' => $media_account_info->agent,
                'email' => $media_account_info->email,
                'toutiao_majordomo_id' => $media_account_info->toutiao_majordomo_id,
                'majordomo_name' => $media_account_info->majordomo_name,
                'wechat_account_name' => $media_account_info->wechat_account_name,
                'creator_id' => $creator_id,
                'creator' => $creator,
                'create_time' => $media_account_info->create_time > 0 ? $media_account_info->create_time : time(),
                'update_time' => $media_account_info->update_time > 0 ? $media_account_info->update_time : time(),
                'state' => 1
            ];
            if (!empty($media_account_info->ext)) {
                $data['ext'] = json_encode($media_account_info->ext, JSON_UNESCAPED_UNICODE);
            }

            $media_account_model->add($data);
            return "{$media_account_info->account_id}授权成功";
        } else {
            $data = [
                'media_type' => $media_account_info->media_type,
                'toutiao_majordomo_id' => $media_account_info->toutiao_majordomo_id,
                'account_name' => $media_account_info->account_name,
                'account_password' => $media_account_info->account_password,
                'company' => $media_account_info->company,
                'agent' => $media_account_info->agent,
                'wechat_account_name' => $media_account_info->wechat_account_name,
                'access_token' => $media_account_info->access_token,
                'access_token_expires' => $media_account_info->access_token_expires,
                'refresh_token' => $media_account_info->refresh_token,
                'refresh_token_expires' => $media_account_info->refresh_token_expires,
                'majordomo_name' => $media_account_info->majordomo_name,
                'update_time' => $media_account_info->update_time > 0 ? $media_account_info->update_time : time(),
                'state' => 1
            ];

            if (!empty($media_account_info->ext)) {
                $data['ext'] = json_encode($media_account_info->ext, JSON_UNESCAPED_UNICODE);
            }

            // 没有负责人时，更新平台，满足无数据子账户切换平台的需求
            if (empty($media_account->agent_leader)) {
                $data['platform'] = $media_account_info->platform;
            }

            $media_account_model->edit($media_account->id, $data);
            if ($media_account->creator !== $creator) {
                return "{$media_account->account_id}不允许重复授权(所属{$media_account->creator}), 如需使用请联系上级切换账户负责人";
            } else {
                return "{$media_account_info->account_id}授权成功";
            }
        }
    }

    /**
     * 保存线索授权的媒体账户
     * @param MediaAccountInfoParam $media_account_info
     * @param $creator_id
     * @param $creator
     * @return string 授权信息
     */
    public function saveAuthClueAccount(MediaAccountInfoParam $media_account_info, $creator_id, $creator)
    {
        if (!$this->checkDevelopCompanyExists($media_account_info->media_type, $media_account_info->platform, $media_account_info->company, $media_account_info->toutiao_majordomo_id)) {
            return "{$media_account_info->account_id}-{$media_account_info->company}不允许授权";
        }

        $media_account_clue_model = new MediaAccountClueModel();
        $media_account = $media_account_clue_model->getDataByAccountId($media_account_info->account_id, $media_account_info->media_type);
        if (empty($media_account)) {
            $media_account_clue_model->add([
                'platform' => $media_account_info->platform,
                'media_type' => $media_account_info->media_type,
                'access_token' => $media_account_info->access_token,
                'access_token_expires' => $media_account_info->access_token_expires,
                'refresh_token' => $media_account_info->refresh_token,
                'refresh_token_expires' => $media_account_info->refresh_token_expires,
                'account_id' => $media_account_info->account_id,
                'account_name' => $media_account_info->account_name,
                'account_password' => $media_account_info->account_password,
                'company' => $media_account_info->company,
                'agent' => $media_account_info->agent,
                'email' => $media_account_info->email,
                'toutiao_majordomo_id' => $media_account_info->toutiao_majordomo_id,
                'majordomo_name' => $media_account_info->majordomo_name,
                'wechat_account_name' => $media_account_info->wechat_account_name,
                'creator_id' => $creator_id,
                'creator' => $creator,
                'create_time' => $media_account_info->create_time > 0 ? $media_account_info->create_time : time(),
                'update_time' => $media_account_info->update_time > 0 ? $media_account_info->update_time : time(),
                'state' => 1
            ]);
            return "{$media_account_info->account_id}授权成功";
        } else {
            $data = [
                'media_type' => $media_account_info->media_type,
                'toutiao_majordomo_id' => $media_account_info->toutiao_majordomo_id,
                'account_name' => $media_account_info->account_name,
                'account_password' => $media_account_info->account_password,
                'company' => $media_account_info->company,
                'agent' => $media_account_info->agent,
                'wechat_account_name' => $media_account_info->wechat_account_name,
                'access_token' => $media_account_info->access_token,
                'access_token_expires' => $media_account_info->access_token_expires,
                'refresh_token' => $media_account_info->refresh_token,
                'refresh_token_expires' => $media_account_info->refresh_token_expires,
                'majordomo_name' => $media_account_info->majordomo_name,
                'update_time' => $media_account_info->update_time > 0 ? $media_account_info->update_time : time(),
                'state' => 1
            ];

            // 没有负责人时，更新平台，满足无数据子账户切换平台的需求
            if (empty($media_account->agent_leader)) {
                $data['platform'] = $media_account_info->platform;
            }

            $media_account_clue_model->edit($media_account->id, $data);

            return "{$media_account_info->account_id}授权成功";
        }
    }

    /**
     * 检查主体是否在开发者列表中
     * @param $media_type
     * @param $platform
     * @param $company
     * @param int $majordomo_account_id
     * @param bool $is_major_check 是否为管家校验 true则为是管家 false是子账号校验
     * @return bool
     */
    public function checkDevelopCompanyExists($media_type, $platform, $company, $majordomo_account_id = 0, $is_major_check = false)
    {
        if (empty($this->developer_company_list[$media_type] ?? '')) {
            $this->developer_company_list[$media_type] = (new MediaDeveloperAccountModel())->getCompanyListByMedia($media_type);
        }

        // 特殊进入的管家
        $white_list = [];

        // 特殊进入的管家+主体
        if (
            ($platform === 'GR' && $company === '广州淘玩网络科技有限公司' && $majordomo_account_id == ****************) ||
            ($platform === 'GRBB' && $company === '广州八九游网络科技有限公司' && $majordomo_account_id == ****************) ||
            ($platform === 'GRBB' && $company === '广州八九游网络科技有限公司' && $majordomo_account_id == ****************) ||
            ($platform === 'GRBB' && $company === '广州八九游网络科技有限公司' && $majordomo_account_id == ****************) ||
            ($platform === 'GRBB' && $company === '广州八九游网络科技有限公司' && $majordomo_account_id == ****************)
        ) {
            return true;
        }

        if (
            !in_array($majordomo_account_id, $white_list)
            && !in_array($media_type, [MediaType::BAIDU, MediaType::BAIDU_SEARCH])
            && !empty($company)
            && $this->developer_company_list[$media_type]
                ->where('platform', $platform)
                ->where('company', $company)
                ->isEmpty()
        ) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否与管家同平台
     * @param $platform
     * @param $media_type
     * @param $majordomo_id
     * @return bool
     */
    public function checkMajordomoPlatform($platform, $media_type, $majordomo_id)
    {
        if (empty($majordomo_id)) {
            return true;
        }

        $key = "{$media_type}-{$majordomo_id}";
        if (empty($this->majordomo_account_list[$key] ?? '')) {
            $this->majordomo_account_list[$key] = (new MediaMajordomoAccountModel())->getDataByAccountId($media_type, $majordomo_id);
        }

        if (
            !empty($this->majordomo_account_list[$key])
            && $this->majordomo_account_list[$key]->platform !== (string)$platform
        ) {
            return false;
        }
        return true;
    }
}
