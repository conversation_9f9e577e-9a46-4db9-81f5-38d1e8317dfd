<?php
/**
 * 用户管理服务
 * User: Dhq
 * Date: 2018/5/18
 * Time: 14:29
 */

namespace App\Service;

use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsMaterialLogModel;
use App\Model\SqlModel\Zeda\RankPermissionAllModel;
use App\Model\SqlModel\Zeda\UserLevelModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Model\SqlModel\Zeda\ViewPositionModel;
use Illuminate\Support\Collection;

class UserService
{
    /**
     * 人员管理等级 数字越小表示级别越高 权限越高
     */
    const LEVEL_SUPER = 0;                              // 超管
    const LEVEL_PLATFORM = 1;                           // 平台管理等级
    const LEVEL_DEPARTMENT = 2;                         // 部门管理等级
    const LEVEL_DEPARTMENT_GROUP = 3;                   // 部门分组管理等级
    const LEVEL_DEPARTMENT_GROUP_POSITION = 4;          // 岗位
    const LEVEL_DEPARTMENT_GROUP_POSITION_WORKER = 5;   // 岗位再下一级 5
    const LEVEL_SIX = 6;     // 6级
    const LEVEL_SEVEN = 7;   // 7级
    const LEVEL_EIGHT = 8;   // 8级

    const NORMAL = 0;
    const SUPER_MANAGER = 1;
    const BOSS = 2;

    /**
     * 生成一个hash密文
     *
     * @param $password   string    明文密码
     *
     * @return string
     */
    static public function genHashPwd($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * 验证密码正确性
     *
     * @param $password
     * @param $hash
     *
     * @return bool
     */
    static public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }

    // 获取超管列表
    public function getSuperManagerList()
    {
        return (new UserModel())->getSuperManagerList();
    }

    /**
     * @param $module
     *
     * @return array
     */
    public function getPositionCascader($module)
    {
        $position_list = (new ViewPositionModel())->getAllByModule($module);
        return [
            'platform_list'                         => $position_list->where('level', 1)->values(),
            'department_list'                       => $position_list->where('level', 2)->values(),
            'department_group_list'                 => $position_list->where('level', 3)->values(),
            'department_group_position_list'        => $position_list->where('level', 4)->values(),
            'department_group_position_worker_list' => $position_list->where('level', 5)->values(),
            'department_six_list'                   => $position_list->where('level', 6)->values(),
            'department_seven_list'                 => $position_list->where('level', 7)->values(),
            'department_eight_list'                 => $position_list->where('level', 8)->values(),
        ];
    }

    /**
     * 获取所有用户列表以及等级
     *
     * @param $module
     *
     * @return Collection
     */
    public function getUserListWithLevel($module)
    {
        $user_model = new UserModel();
        return $user_model->getAllUserWithLevel($module);
    }


    /**
     * 获取level下用户列表(包括自己和下一级用户)
     *
     * @param string $module
     * @param string $state
     *
     * @return Collection
     */
    public function getUserOptions($module, $state = null)
    {
        [$level, $rank_id] = self::getLoginUserLevelAndRankId($module);
        $model = new UserModel();
        $super_manager = Container::getSession()->super_manager;
        if ($super_manager === self::SUPER_MANAGER) {
            $super_manager_list = $model->getSuperManagerList();
            $list = $super_manager_list->merge($model->getAllByZeda($module));
        } elseif ($super_manager === self::BOSS) {
            $list = $model->getAllByZeda($module);
        } else {
            if ($level == UserService::LEVEL_EIGHT) {
                $list = collect();
            } else {
                $list = $model->getAllByRankID($rank_id, $level);
            }
//            switch ($level) {
//                case UserService::LEVEL_PLATFORM:
//                    $list = $model->getAllByPlatformId($rank_id);
//                    break;
//                case UserService::LEVEL_DEPARTMENT:
//                    $list = $model->getAllByDepartmentId($rank_id);
//                    break;
//                case UserService::LEVEL_DEPARTMENT_GROUP:
//                    $list = $model->getAllByGroupId($rank_id);
//                    break;
//                case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
//                    $list = $model->getAllByPositionId($rank_id);
//                    break;
//                case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
//                    $list = collect();
//                    break;
//                default:
//                    throw new AppException('参数异常');
//            }
        }
        if (!is_null($state)) {
            $list = $list->where('state', $state);
        }
        $list = $list->map(function ($item) {
            unset($item->password);
            return $item;
        });
        return Collection::make([array_merge(Container::getSession()->getUserInfo(), Container::getSession()->getUserDetail($module))])->merge($list)->unique('user_id')->values();
    }

    public function getMaterialUserOptions($module, $material_permission)
    {
        $user_permission = $this->getUserOptions($module)->pluck('name')->toArray();
        $material_user_list = (new OdsMaterialLogModel())->getUserOptions($user_permission, $material_permission);
        $user_name_list = collect();
        foreach ($material_user_list as $material_user) {
            $user_name_list = $user_name_list
                ->push($material_user->author)
                ->merge(json_decode($material_user->c_author))
                ->merge(json_decode($material_user->a_author))
                ->merge(json_decode($material_user->m1_author))
                ->merge(json_decode($material_user->m2_author))
                ->merge(json_decode($material_user->m3_author))
                ->merge(json_decode($material_user->m4_author))
                ->merge(json_decode($material_user->m5_author))
                ->merge(json_decode($material_user->actor))
                ->merge(json_decode($material_user->shoot));
        }
        $user_list = (new UserModel())->getAllUserWithLevel($module)->whereIn('name', $user_name_list->unique())->values();
        $position_list = (new ViewPositionModel())->getAllByModule('dsp');
        if (Container::getSession()->user_level !== UserService::LEVEL_SUPER) {
            $co_position_list = [
                'platform_list'                         => $position_list
                    ->where('level', UserService::LEVEL_PLATFORM)
                    ->whereIn('platform_id', $user_list->pluck('platform_id'))
                    ->values(),
                'department_list'                       => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT)
                    ->whereIn('department_id', $user_list->pluck('department_id'))
                    ->values(),
                'department_group_list'                 => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT_GROUP)
                    ->whereIn('department_group_id', $user_list->pluck('department_group_id'))
                    ->values(),
                'department_group_position_list'        => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION)
                    ->whereIn('department_group_position_id', $user_list->pluck('department_group_position_id'))
                    ->values(),
                'department_group_position_worker_list' => $position_list
                    ->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER)
                    ->whereIn('department_group_position_worker_id', $user_list->pluck('department_group_position_worker_id'))
                    ->values(),
                'department_six_list'                   => $position_list
                    ->where('level', UserService::LEVEL_SIX)
                    ->whereIn('department_six_id', $user_list->pluck('department_six_id'))
                    ->values(),
                'department_seven_list'                 => $position_list
                    ->where('level', UserService::LEVEL_SEVEN)
                    ->whereIn('department_seven_id', $user_list->pluck('department_seven_id'))
                    ->values(),
                'department_eight_list'                 => $position_list
                    ->where('level', UserService::LEVEL_EIGHT)
                    ->whereIn('department_eight_id', $user_list->pluck('department_eight_id'))
                    ->values(),
            ];

        } else {
            $co_position_list = [
                'platform_list'                         => $position_list->where('level', UserService::LEVEL_PLATFORM)->values(),
                'department_list'                       => $position_list->where('level', UserService::LEVEL_DEPARTMENT)->values(),
                'department_group_list'                 => $position_list->where('level', UserService::LEVEL_DEPARTMENT_GROUP)->values(),
                'department_group_position_list'        => $position_list->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION)->values(),
                'department_group_position_worker_list' => $position_list->where('level', UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER)->values(),
                'department_six_list'                   => $position_list->where('level', UserService::LEVEL_SIX)->values(),
                'department_seven_list'                 => $position_list->where('level', UserService::LEVEL_SEVEN)->values(),
                'department_eight_list'                 => $position_list->where('level', UserService::LEVEL_EIGHT)->values(),
            ];
        }
        return $co_position_list + ['user_list' => $user_list];
    }

    /**
     * 判断是否是超管
     * 传入level 则根据level判断
     * 不传根据session判断
     *
     * @param int $level
     *
     * @return bool
     */
    static public function isSuperManager($level = -1)
    {
        if ($level !== -1) {
            return $level === UserService::LEVEL_SUPER;
        } else {
            return Container::getSession()->get('super_manager') === self::SUPER_MANAGER;
        }
    }

    /**
     * @param $user_id
     *
     * @return bool
     */
    static public function isSuperManagerByUserId($user_id)
    {
        $user_model = new UserModel();
        $user_info = $user_model->getData($user_id);
        return $user_info->super_manager == 1;
    }

    /**
     * @param array $permission_all_list
     * @return bool
     * @deprecated 错误命名+错误内容 千万别使用
     */
    static public function isPermissionSelectAll($permission_all_list = [])
    {
        if (empty($permission_all_list)) {
            $permission_all_list = Container::getSession()->permission_all_list;
        }

        if ($permission_all_list === -1) {
            return true;
        }

        $types = $permission_all_list->pluck('type')->toArray();
        if (array_search(RankPermissionAllModel::TYPE_AGENT_GROUP, $types) === false &&
            array_search(RankPermissionAllModel::TYPE_LEADER, $types) === false
        ) {
            return false;
        }
        return true;
    }

    static public function getLoginUserLevelAndRankId($module)
    {
        $user_detail = Container::getSession()->getUserDetail($module);
        return [$user_detail['level'], $user_detail['rank_id']];
    }

    /**
     * 获取level同级和下属用户列表(包括自己和下一级用户)
     *
     * @param string $module
     * @param string $state
     *
     * @return Collection
     */
    public function getSameLevelAndNextLevelUserOptions($module, $state = null)
    {
        [$level, $rank_id] = self::getLoginUserLevelAndRankId($module);
        $model = new UserModel();
        $super_manager = Container::getSession()->super_manager;

        if ($super_manager === self::SUPER_MANAGER) {
            //超管：获取其它超管和所有成员的信息
            $super_manager_list = $model->getSuperManagerList();
            $list = $super_manager_list->merge($model->getAllByZeda($module));
        } elseif ($super_manager === self::BOSS) {
            //集团负责人：获取除超管外其它所有成员的信息
            $list = $model->getAllByZeda($module);
        } else {
            if ($level == UserService::LEVEL_EIGHT) {
                $list = collect();
            } else {
                $list = $model->getSameLevelAndNextLevelByRankId($rank_id, $level);
            }
        }
        if (!is_null($state)) {
            $list = $list->where('state', $state);
        }
        return Collection::make([array_merge(Container::getSession()->getUserInfo(), Container::getSession()->getUserDetail($module))])->merge($list)->unique('user_id')->values();
    }

    /**
     * 获取level上级用户列表
     *
     * @param string $module
     * @param string|null $state
     *
     * @return Collection
     */
    public function getUserLeaderOptions(string $module)
    {
        $list = collect();
        //超管没有上级，自身也不需要插任何权限
        if (self::isSuperManager()) {
            return $list;
        }
        $user_detail = Container::getSession()->getUserDetail($module);

        $model = new ViewPositionModel();
        $list = $model->getLeaderLevelByPositionID($user_detail);

        return $list;
    }

    static public function getColumnNameByLevel($level)
    {

        switch ($level) {
            case UserService::LEVEL_PLATFORM:
                $column_name = 'platform_id';
                break;
            case UserService::LEVEL_DEPARTMENT:
                $column_name = 'department_id';
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP:
                $column_name = 'department_group_id';
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                $column_name = 'department_group_position_id';
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                $column_name = 'department_group_position_worker_id';
                break;
            case UserService::LEVEL_SIX:
                $column_name = 'department_six_id';
                break;
            case UserService::LEVEL_SEVEN:
                $column_name = 'department_seven_id';
                break;
            case UserService::LEVEL_EIGHT:
                $column_name = 'department_eight_id';
                break;
            default:
                throw new AppException('参数异常');
        }

        return $column_name;
    }
}
