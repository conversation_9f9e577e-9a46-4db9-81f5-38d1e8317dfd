<?php


namespace App\Service\KPIIndicator;

use App\Model\SqlModel\Tanwan\V2DWDADHourCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDDayCostLogModel;

class Cost extends AbstractIndicator
{
    const NAME = '消耗';

    public function getCurrentValue()
    {
        return $this->item->current_cost;
    }

    public function getUpdateData($start_date, $end_date, $data_game_permission, $data_agent_permission, $game_permission, $agent_permission)
    {
        $v2_dwd_ad_cost_log_model = new V2DWDDayCostLogModel();
        $current_cost = $v2_dwd_ad_cost_log_model->getKpiCurrentCostValue(
            $start_date,
            $end_date,
            $data_game_permission,
            $data_agent_permission,
            $game_permission,
            $agent_permission
        );
        $data['current_cost'] = $current_cost;
        return $data;
    }
}
