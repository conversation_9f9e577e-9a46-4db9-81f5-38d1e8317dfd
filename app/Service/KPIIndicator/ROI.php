<?php


namespace App\Service\KPIIndicator;


use App\Model\SqlModel\Tanwan\V2DWDADHourCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDDayCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDGameUidRegLogModel;
use App\Utils\Math;

class ROI extends AbstractIndicator
{
    const NAME = '回本率';

    public function getCurrentValue()
    {
        return Math::rate($this->item->current_new_pay, $this->item->current_cost);
    }

    public function getUpdateData($start_date, $end_date, $data_game_permission, $data_agent_permission, $game_permission, $agent_permission)
    {
        $v2_dwd_ad_cost_log_model = new V2DWDDayCostLogModel();
        $current_cost = $v2_dwd_ad_cost_log_model->getKpiCurrentCostValue(
            $start_date,
            $end_date,
            $data_game_permission,
            $data_agent_permission,
            $game_permission,
            $agent_permission
        );
        $data['current_cost'] = $current_cost;
        $v2_dwd_game_uid_reg_log_model = new V2DWDGameUidRegLogModel();
        $start_datetime = $start_date . ' 00:00:00';
        $end_datetime = $end_date . ' 23:59:59';
        $game_uid_reg_current_value = $v2_dwd_game_uid_reg_log_model->getKpiCurrentValue(
            $start_datetime,
            $end_datetime,
            $data_game_permission,
            $data_agent_permission,
            $game_permission,
            $agent_permission
        );
        $current_new_pay = $game_uid_reg_current_value->new_pay ?: 0;
        $data['current_new_pay'] = $current_new_pay;
        return $data;
    }
}
