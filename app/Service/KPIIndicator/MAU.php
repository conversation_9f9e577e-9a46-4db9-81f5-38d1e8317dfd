<?php


namespace App\Service\KPIIndicator;

use App\Model\SqlModel\Tanwan\V2DWDGameUidRegLogModel;

class MAU extends AbstractIndicator
{
    const NAME = '月活跃数';

    public function getCurrentValue()
    {
        return $this->item->current_mau;
    }

    public function getUpdateData($start_date, $end_date, $data_game_permission, $data_agent_permission, $game_permission, $agent_permission)
    {
        $start_datetime = $start_date . ' 00:00:00';
        $end_datetime = $end_date . ' 23:59:59';
        $v2_dwd_game_uid_reg_log_model = new V2DWDGameUidRegLogModel();
        $current_mau = $v2_dwd_game_uid_reg_log_model->getKpiCurrentMAUValue(
            $start_datetime,
            $end_datetime,
            $data_game_permission,
            $data_agent_permission,
            $game_permission,
            $agent_permission
        );
        $data['current_mau'] = $current_mau;
        return $data;
    }
}