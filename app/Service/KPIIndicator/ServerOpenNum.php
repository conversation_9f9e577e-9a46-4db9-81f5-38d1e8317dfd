<?php


namespace App\Service\KPIIndicator;

use App\Model\SqlModel\Tanwan\V2DimServerOpenTimeModel;

class ServerOpenNum extends AbstractIndicator
{
    const NAME = '开服数';

    public function getCurrentValue()
    {
        return $this->item->current_server_open_num;
    }

    public function getUpdateData($start_date, $end_date, $data_game_permission, $data_agent_permission, $game_permission, $agent_permission)
    {
        $v2_dim_server_open_model = new V2DimServerOpenTimeModel();
        $start_datetime = $start_date . ' 00:00:00';
        $end_datetime = $end_date . ' 23:59:59';
        $current_server_open_num = $v2_dim_server_open_model->getKpiCurrentServerOpenValue(
            $start_datetime,
            $end_datetime,
            $data_game_permission,
            $game_permission
        );
        $data['current_server_open_num'] = $current_server_open_num;
        return $data;
    }
}