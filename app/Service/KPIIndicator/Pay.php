<?php


namespace App\Service\KPIIndicator;

use App\Model\SqlModel\Tanwan\V2DWDDayPayLogModel;

class Pay extends AbstractIndicator
{
    const NAME = '收入';

    public function getCurrentValue()
    {
        return $this->item->current_pay;
    }


    public function getUpdateData($start_date, $end_date, $data_game_permission, $data_agent_permission, $game_permission, $agent_permission)
    {
        $v2_dws_day_pay_log_model = new V2DWDDayPayLogModel();
        $current_pay = $v2_dws_day_pay_log_model->getKpiCurrentPayValue(
            $start_date,
            $end_date,
            $data_game_permission,
            $data_agent_permission,
            $game_permission,
            $agent_permission
        );
        $data['current_pay'] = $current_pay;
        return $data;
    }
}