<?php

namespace App\Service;

use App\Constant\MediaType;
use App\Container;
use App\Model\HttpModel\OPPO\Communal\OwnerModel;
use App\Param\MediaAccountInfoParam;

class OppoService
{
    /**
     * @param $platform
     * @param $account_content
     * @return string
     */
    public function addAccount($platform, $account_content): string
    {
        $creator_id = Container::getSession()->user_id;
        $creator_name = Container::getSession()->name;
        $account_content = trim($account_content);
        $account_lines = explode("\n", $account_content);

        $message = '';
        $service = new MediaService();
        $model = new OwnerModel();
        foreach ($account_lines as $account_line) {
            $account_item = explode(' ', trim($account_line));
            $account_id = $account_item[0] ?? '';
            $api_id = $account_item[1] ?? '';
            $api_key = $account_item[2] ?? '';
            if (empty($account_id)) {
                continue;
            }
            if (count($account_item) < 3) {
                $message .= "{$account_id}-信息不全，请检查;";
                continue;
            }
            if (!is_numeric($account_id)) {
                $message .= "{$account_id}-含有非数字字符;";
                continue;
            }

            // 获取账户信息
            try {
                $account_info = $model->getLoginInfo($account_id, $api_id, $api_key);
            } catch (\Throwable $e) {
                $message .= "{$account_id}-获取账号信息失败，错误信息：" . $e->getMessage();
                continue;
            }

            $tmp_message = $service->saveAuthAccount(new MediaAccountInfoParam([
                'media_type' => MediaType::OPPO,
                'platform' => $platform,
                'account_id' => $account_id,
                'account_name' => $account_info['ownerName'],
                'access_token' => $api_id,
                'access_token_expires' => 0,
                'refresh_token' => $api_key,
                'refresh_token_expires' => 0,
                'toutiao_majordomo_id' => 0,
                'wechat_account_name' => $account_info['accId'], // 财务账号ID
            ]), $creator_id, $creator_name);
            $message .= "$tmp_message;";
        }

        return $message;
    }
}
