<?php

namespace App\Model\HttpModel\DiDi;


use App\Exception\AppException;
use App\Utils\Helpers;
use Common\EnvConfig;

abstract class AbstractDiDiModel
{
    const URL = 'https://api.es.xiaojukeji.com/';


    /**
     * post请求 不使用json_encode组装请求参数
     * @param       $api
     * @param       $data
     * @param       $sign_key
     * @param array $options
     * @return array
     */
    public function post($api, $data, $sign_key, array $options = [])
    {
        $data['sign'] = $this->genSign($data, $sign_key);
        $response = Helpers::postCurl($api, json_encode($data), $options);
        $decode_data = $this->decode($api, $data, $response);
        if (isset($options['log']) && $options['log'] === true) {
            Helpers::getLogger("didi")->info("$api", [
                'request_data'  => $data,
                'response_data' => json_decode($response, true),
                'options'       => $options,
            ]);
        }
        return $decode_data;
    }


    /**
     * 生成签名
     *
     * @param array $params
     * @param $sign_key
     * @param int $signMethod
     * @return string
     */
    public function genSign(array $params, $sign_key, int $signMethod = 0)
    {
        $params['sign_key'] = $sign_key;
        ksort($params); //对数组(map)根据键名升序排序
        $str = '';

        foreach ($params as $k => $v) {
            if ('' == $str) {
                $str .= $k . '=' . trim($v);
            } else {
                $str .= '&' . $k . '=' . trim($v);
            }

        }
        // 如果$signMethod == 1 ： 采用sha256加密方式
        if ($signMethod == 1) {
            return hash("sha256", $str);
        }
        //默认采用md5加密：此处md5值为小写的32个字符
        return md5($str);
    }

    /**
     * @param $api
     * @param $data
     * @param $json
     * @return array
     * @throws AppException
     */
    public function decode($api, $data, $json)
    {
        if (empty($json)) {
            Helpers::getLogger('didi')->warning("$api return empty response", [
                'request_data' => $data
            ]);
            return [
                'errno' => -1,
                'message' => 'api return empty response'
            ];
        }

        $response_data = json_decode($json, true);

        if ($response_data === null) {
            Helpers::getLogger('didi')->warning("$api return invalid json format", [
                'request_data' => $data,
                'response_str' => $json
            ]);
            return [
                'errno' => -1,
                'message' => 'api return invalid json format'
            ];
        }


        return $response_data;
    }

    public function get($api, $data, array $options = []): array
    {
        $response = Helpers::customCurl('GET', $api, $data, $options);
        dd($response,1);
        $decode_data = $this->decode($api, $data, $response);
        if (isset($options['log']) && $options['log'] === true) {
            Helpers::getLogger("didi")->info("$api", [
                'request_data'  => $data,
                'response_data' => json_decode($response, true),
            ]);
        }
        return $decode_data;
    }
}
