<?php

namespace App\Model\HttpModel\DiDi;

use App\Service\EnterpriseDiDiService;
use Common\EnvConfig;

class ApprovalModel extends AbstractDiDiModel
{
    const URL_CREATE = Parent::URL . 'river/Approval/create';
    const URL_CANCEL = Parent::URL . 'river/Approval/cancel';
    const URL_DETAIL = Parent::URL . 'open-apis/v1/approval/detail';
    const URL_ORDER = Parent::URL . 'river/Approval/getOrder';


    /**
     * 创建申请单
     *
     * @param string $company
     * @param $regulation_id
     * @param $phone
     * @param $employee_no
     * @param int $approval_type
     * @param array $business_trip_detail
     * @return array
     * @throws \RedisException
     * @see https://opendocs.xiaojukeji.com/version2024/10999
     */
    public function create(string $company, $regulation_id, $phone, $employee_no, int $approval_type = 3, array $business_trip_detail = [])
    {
        $client_id = EnvConfig::DD[$company]['client_id'];
        $client_secret = EnvConfig::DD[$company]['client_secret'];
        $sign_key = EnvConfig::DD[$company]['sign_key'];
        $company_id = EnvConfig::DD[$company]['company_id'];

        $access_token = EnterpriseDiDiService::getAccessToken($client_id, $client_secret, $sign_key);

        $request_data = [
            'client_id'       => $client_id,
            'access_token'    => $access_token,
            'company_id'      => $company_id,
            'timestamp'       => time(),
            'approval_type'   => $approval_type,
            'regulation_id'   => $regulation_id,
            'member_type'     => 1, // 员工类型，工号
            'phone'           => $phone,
            'employee_number' => $employee_no,
        ];
        if (empty($business_trip_detail)) {
            $request_data['business_trip_detail'] = json_encode([
                'start_time' => date("Y-m-d H:i:s"),
                'end_time'   => date("Y-m-d H:i:s", strtotime("+8 hour")),
            ]);
        } else {
            $request_data['business_trip_detail'] = json_encode($business_trip_detail);
        }

        $api = self::URL_CREATE;

        $options = [
            'log'    => true,
            'header' => [
                'Content-Type: application/json'
            ],
        ];

        return $this->post($api, $request_data, $sign_key, $options);

    }


    /**
     * 取消申请单
     *
     * @param string $company
     * @param $approval_id
     * @return array
     * @throws \RedisException
     */
    public function cancel(string $company, $approval_id)
    {
        $client_id = EnvConfig::DD[$company]['client_id'];
        $client_secret = EnvConfig::DD[$company]['client_secret'];
        $sign_key = EnvConfig::DD[$company]['sign_key'];
        $company_id = EnvConfig::DD[$company]['company_id'];

        $access_token = EnterpriseDiDiService::getAccessToken($client_id, $client_secret, $sign_key);

        $request_data = [
            'client_id'    => $client_id,
            'access_token' => $access_token,
            'company_id'   => $company_id,
            'timestamp'    => time(),
            'approval_id'  => $approval_id,
        ];

        $api = self::URL_CANCEL;

        $options = [
            'log'    => true,
            'header' => [
                'Content-Type: application/json'
            ],
        ];

        return $this->post($api, $request_data, $sign_key, $options);

    }


    /**
     * @param $company
     * @param $approval_id
     * @return array
     * @throws \RedisException
     */
    public function getOrder($company, $approval_id)
    {
        $client_id = EnvConfig::DD[$company]['client_id'];
        $client_secret = EnvConfig::DD[$company]['client_secret'];
        $sign_key = EnvConfig::DD[$company]['sign_key'];
        $company_id = EnvConfig::DD[$company]['company_id'];

        $access_token = EnterpriseDiDiService::getAccessToken($client_id, $client_secret, $sign_key);

        $request_data = [
            'client_id'    => $client_id,
            'access_token' => $access_token,
            'company_id'   => $company_id,
            'timestamp'    => time(),
            'offset'       => 0,
            'length'       => 50,
            'approval_id'  => $approval_id,
        ];

        $api = self::URL_ORDER;

        $options = [
            'log'    => true,
            'header' => [
                'Content-Type: application/x-www-form-urlencoded'
            ],
        ];
        $sign = $this->genSign($request_data, $sign_key);
        $request_data['sign'] = $sign;
        $query_string = http_build_query($request_data);

        return $this->get($api . '?' . $query_string, [], $options);
    }


    /**
     * @param $company
     * @param $approval_id
     * @return array
     * @throws \RedisException
     */
    public function getDetail($company, $approval_id)
    {
        $client_id = EnvConfig::DD[$company]['client_id'];
        $client_secret = EnvConfig::DD[$company]['client_secret'];
        $sign_key = EnvConfig::DD[$company]['sign_key'];
        $company_id = EnvConfig::DD[$company]['company_id'];

        $access_token = EnterpriseDiDiService::getAccessToken($client_id, $client_secret, $sign_key);

        $request_data = [
            'client_id'    => $client_id,
            'access_token' => $access_token,
            'company_id'   => $company_id,
            'timestamp'    => time(),
            'approval_id'  => $approval_id,
        ];

        $api = self::URL_DETAIL;

        $options = [
            'log'    => true,
            'header' => [
                'Content-Type: application/json'
            ],
        ];
        $sign = $this->genSign($request_data, $sign_key);
        $request_data['sign'] = $sign;
        $query_string = http_build_query($request_data);

        return $this->get($api . '?' . $query_string, [], $options);
    }

    public function getAddr($country_id, $company)
    {
        $client_id = EnvConfig::DD[$company]['client_id'];
        $client_secret = EnvConfig::DD[$company]['client_secret'];
        $sign_key = EnvConfig::DD[$company]['sign_key'];
        $company_id = EnvConfig::DD[$company]['company_id'];

        $access_token = EnterpriseDiDiService::getAccessToken($client_id, $client_secret, $sign_key);

        $request_data = [
            'client_id'    => $client_id,
            'access_token' => $access_token,
            'company_id'   => $company_id,
            'timestamp'    => time(),
            'param_json'   => json_encode([
                'country_id'   => $country_id,
                'product_type' => '10',
            ]),
        ];

        $api = self::URL . 'open-apis/v1/city/list';



        $options = [
            'log'    => true,
            'header' => [
                'Content-Type: application/json'
            ],
        ];

        return $this->post($api, $request_data, $sign_key, $options);

    }

}