<?php
/**
 * Created by Php<PERSON>torm.
 * User: Administrator
 * Date: 2020/03/13
 * Time: 16:05
 */

namespace App\Model\SqlModel\Zeda;

use App\Model\SqlModel\Database\ZDBuilder;
use App\Param\ADAsset\ToutiaoOrangeStationParam;
use App\Param\ADAsset\ToutiaoOrangeStationTaskSearchParam;
use Illuminate\Database\Eloquent\Model;

class ToutiaoOrangeStationModel extends AbstractZedaSqlModel
{
    protected $table = 'toutiao_orange_station_add_task';

    const TASK_NO_START = 1;
    const TASK_FINISH = 2;
    const TASK_ERROR = 3;

    /**
     * @param ToutiaoOrangeStationTaskSearchParam $param
     * @return array
     */
    public function getList(ToutiaoOrangeStationTaskSearchParam $param, $user_list = [])
    {
        $builder = $this->builder;

        $builder->orderBy('id', 'desc');
        $param->create_time && $builder->whereBetween('create_time', $param->create_time);
        $param->account_id && $builder->where('account_id', '=', $param->account_id);
        $param->platform && $builder->where('platform', '=', $param->platform);
        $param->creator && $builder->where('creator', $param->creator);
        $param->target_account_id && $builder->where('target_account_id', '=', $param->target_account_id);
        $param->task_state && $builder->where('task_state', '=', $param->task_state);
        $param->account_name && $builder->where('account_name', 'like', "%{$param->account_name}%");
        $param->target_account_name && $builder->where('target_account_name', 'like', "%{$param->target_account_name}%");

        if ($user_list) {
            $builder->whereIn('creator', $user_list);
        }
        $total = $builder->count();

        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        } else {
            return [
                'total' => $total,
                'list' => $builder->forPage($param->page, $param->rows)->get()
            ];
        }
    }

    /**
     * @param int $id
     * @return ZDBuilder|Model|object|null
     */
    public function getDataById(int $id)
    {
        $builder = $this->builder;
        $builder->where('id', '=', $id);
        return $builder->first();
    }

    /**
     * @param ToutiaoOrangeStationParam $param
     * @return int
     */
    public function add(ToutiaoOrangeStationParam $param)
    {
        $builder = $this->builder;
        return $builder->insertGetId($param->toInsertData());
    }

    /**
     * @param int $id
     * @param array $data
     * @return int
     */
    public function edit(int $id, array $data)
    {
        return $this->builder->where('id', '=', $id)->update($data);
    }
}
