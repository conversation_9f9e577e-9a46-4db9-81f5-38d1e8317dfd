<?php
/**
 * Server事件回调
 * 因为swoole热重启机制，所以需要此类做个中转才能做到热重启
 * User: dhq
 * Date: 2019/07/31
 * Time: 10:52
 */

namespace App\Server;

use App\Constant\Environment;
use App\Constant\ResponseCode;
use App\Container;
use App\Controller\Controller;
use App\ElasticsearchConnection;
use App\Exception\AppException;
use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\Param\LogParam;
use App\Struct\Input;
use App\Response\Send;
use App\Logic\LogLogic;
use App\Struct\Session;
use App\MysqlConnection;
use App\Tick\TickList;
use App\Utils\Helpers;
use App\Utils\Snowflake;
use Common\EnvConfig;
use Swoole\Http\Request;
use Swoole\Http\Response;
use Swoole\Timer;
use Swoole\WebSocket\Frame;
use Swoole\Websocket\Server;
use App\Struct\WebSocketManager;
use UAParser\Parser;


class ServerCallback
{

    /**
     * @var Server
     */
    private $server;

    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    /**
     *
     * @param Server $server
     * @param int    $workId
     */
    public function onWorkerStart(Server $server, $workId)
    {
        // 注入snowflake对象
        Container::setSnowflake(new Snowflake($workId));

        // 设置MySQL连接对象
        MysqlConnection::setConnection();

        // 设置ck数据库链接对象
        ClickhouseConnection::setConnection();

        // 设置Elasticsearch连接对象
        ElasticsearchConnection::setConnection();

        // 设置websocket管理对象
        Container::setWSManager(new WebSocketManager());

        Container::setCtrlAnnotation();

        // 启动定时任务
        if ($workId === 0) {
            TickList::run();
        }
        print_r("worker id: {$workId} start" . PHP_EOL);
    }


    public function onTaskStart(Server $server, $workId)
    {
        // 设置MySQL连接对象
        MysqlConnection::setConnection();
        // 设置ck数据库链接对象
        ClickhouseConnection::setConnection();
        // 设置Elasticsearch连接对象
        ElasticsearchConnection::setConnection();
        // 注入snowflake对象
        Container::setSnowflake(new Snowflake($workId));
        // 设置websocket管理对象
        Container::setWSManager(new WebSocketManager());
        print_r("task id: {$workId} start" . PHP_EOL);
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param Server  $server
     *
     */
    public function onOpen(Server $server, Request $request)
    {
        ob_start();

        // this continues the normal flow of the app, and will return the proper body
        $path_info = trim($request->server['path_info'], '/');
        $path_info = ($pos = strripos($path_info, '.' . EnvConfig::URL_SUR_FIX)) ? substr($path_info, 0, $pos) : $path_info;
        $path_info_array = explode('/', $path_info);
        $controller_name = $path_info_array[0] ? array_shift($path_info_array) : 'portal';
        $method_name = $path_info_array[0] ? array_shift($path_info_array) : 'index';
        $path_info_length = isset($path_info_array[0]) && $path_info_array[0] ? count($path_info_array) : 0;
        for ($i = 0; $i < $path_info_length; $i += 2) {
            !isset($request->get[$path_info_array[$i]]) && $request->get[$path_info_array[$i]] = $path_info_array[$i + 1];
        }

        $input = new Input();
        $input->setData($request);
        if ((isset($request->cookie['x-token']) && $request->cookie['x-token']) || (isset($input['x-token']) && $input['x-token'])) {
            $token = $request->cookie['x-token'] ?? $input['x-token'];
        } else {
            $server->push($request->fd, json_encode([
                'code' => ResponseCode::FAILURE,
                'message' => '非法链接'
            ], JSON_UNESCAPED_UNICODE));
            $server->close($request->fd);
            return;
        }

        $session = new Session($token);

        if (empty($session->get('user_id'))) {
            if ($server->exist($request->fd)) {
                $server->push($request->fd, json_encode([
                    'code' => ResponseCode::FAILURE,
                    'message' => '登录已过期'
                ], JSON_UNESCAPED_UNICODE));
                $server->close($request->fd);
                return;
            }
        }
        Container::setSession(new Session($token));
        Container::setRequest($request);
        $controller_class = '\\App\\Controller\\' . ucfirst($controller_name) . 'Controller';
        $response_obj = [];
        try {
            $controller = new $controller_class($request);
            $response_obj = $controller->$method_name($input);
        } catch (AppException $app_exception) {
            $response_obj = ['code' => $app_exception->getCode(), 'message' => $app_exception->getMessage()];
        } catch (\Throwable $throwable) {
            echo $throwable->getMessage() . ' on ' . $throwable->getFile() . ' line:' . $throwable->getLine();
        }

        $stdout = ob_get_contents();
        ob_end_clean();

        if (!empty($stdout) && EnvConfig::DEBUG) {
            $server->push($request->fd, json_encode([
                'code' => ResponseCode::FAILURE,
                'message' => $stdout
            ], JSON_UNESCAPED_UNICODE));
            $server->close($request->fd);
            return;
        }

        $server->push($request->fd, json_encode($response_obj, JSON_UNESCAPED_UNICODE));
        Container::clearSession();
    }

    /**
     * 处理请求
     *
     * @param Server $server
     * @param Frame  $frame
     */
    public function onMessage(Server $server, frame $frame)
    {

    }

    /**
     * 处理请求
     *
     * @param Request  $request
     * @param Response $response
     *
     * @throws \UAParser\Exception\FileNotFoundException
     */
    public function onRequest(Request $request, Response $response)
    {
        ob_start();
        // this continues the normal flow of the app, and will return the proper body
        $path_info = trim($request->server['path_info'], '/');
        $path_info = ($pos = strripos($path_info, '.' . EnvConfig::URL_SUR_FIX)) ? substr($path_info, 0, $pos) : $path_info;
        $path_info_array = explode('/', $path_info);
        if (count($path_info_array) === 3) {
            $module_name = array_shift($path_info_array);
            $controller_name = array_shift($path_info_array);
            $method_name = array_shift($path_info_array);
            if (in_array($module_name, ['api', 'dms', 'dsp', 'ly', 'fa'])) {
                $controller_class = '\\App\\Controller\\' . strtoupper($module_name) . '\\' . ucfirst($controller_name) . 'Controller';
            } else {
                $controller_class = '\\App\\Controller\\' . ucfirst($module_name) . '\\' . ucfirst($controller_name) . 'Controller';
            }

        } else {
            $module_name = ''; // 公共模块
            $controller_name = isset($path_info_array[0]) && $path_info_array[0] ? array_shift($path_info_array) : 'portal';
            $method_name = isset($path_info_array[0]) && $path_info_array[0] ? array_shift($path_info_array) : 'index';
            $controller_class = '\\App\\Controller\\' . ucfirst($controller_name) . 'Controller';
        }

        $path_info_length = isset($path_info_array[0]) && $path_info_array[0] ? count($path_info_array) : 0;
        for ($i = 0; $i < $path_info_length; $i += 2) {
            !isset($request->get[$path_info_array[$i]]) && $request->get[$path_info_array[$i]] = $path_info_array[$i + 1];
        }

        $input = new Input();
        $input->setData($request);

        // 设置session
        if ((isset($request->cookie['x-token']) && $request->cookie['x-token']) || (isset($input['x-token']) && $input['x-token'])) {
            $token = $request->cookie['x-token'] ?? $input['x-token'];
        } else {
            $token = md5(Container::getUUID());
            $expires = Helpers::getCookieExpire();
            $response->cookie('x-token', $token, $expires, '/', '', false, false);
        }

        if (!isset($input->getData()['request_id'])) {
            $input->setDataField('request_id', date('YmdHis') . Container::getUUID());
        }

        Container::setSession(new Session($token));
        Container::setRequest($request);

        // 先记录session的用户名和id，因为登出的时候会清空session，从而记录不到用户
        $user_id = Container::getSession()->user_id;
        $username = Container::getSession()->name;

        if (!method_exists($controller_class, $method_name)) {
            // 非线上环境可以输出部分静态资源，方便调试
            if (EnvConfig::ENV !== Environment::PROD && file_exists(SRV_DIR . $request->server['request_uri'])) {
                $response->sendFile(SRV_DIR . $request->server['request_uri']);
                return;
            }
            Helpers::getLogger()->warning('非法访问', [
                'ip' => Helpers::getClientIP($request->header),
                'api' => "/{$controller_name}/$method_name",
                'request_time' => time(),
            ]);
            $response->header('Content-Type', 'text/html; charset=UTF-8');
            $response->end('404');
            return;
        }

        $user_agent = $request->header['user-agent'];

        $parser = Parser::create();
        $ua_result = $parser->parse($user_agent);

        if ($ua_result->os->toString() === 'Windows' && $ua_result->ua->toString() === 'QQ Browser 6') {
            $response->header('Content-Type', 'application/json; charset=UTF-8');
            $response->end(json_encode(['code' => ResponseCode::SERVER_REJECT, 'message' => '该浏览器版本过低，请升级到最新版本（推荐使用谷歌浏览器）']));
            return;
        }

        $log_param = new LogParam();
        $log_param->request_time = time();
        $log_param->user_agent = $user_agent;
        $log_param->query_timestamp = microtime(true);
        try {
            /* @var $controller Controller */
            $controller = new $controller_class($request, $response);
            $controller->verify($method_name);
            $response_obj = $controller->$method_name($input);
            if (is_array($response_obj)) {
                $log_param->response_data = json_encode($response_obj, JSON_UNESCAPED_UNICODE); // 记录响应数据
            }
        } catch (AppException $app_exception) {
            $response_obj = ['code' => $app_exception->getCode(), 'message' => $app_exception->getMessage()];
            $log_param->respond_message = json_encode($response_obj);
        } catch (\PhpOffice\PhpSpreadsheet\Exception $office_exception) {
            $response_obj = ['code' => ResponseCode::FAILURE, 'message' => $office_exception->getMessage()];
            $log_param->respond_message = json_encode($response_obj);
        } catch (\Throwable $throwable) {
            $error_message = $throwable->getMessage() . ' on ' . $throwable->getFile() . ' line:' . $throwable->getLine() . PHP_EOL . $throwable->getTraceAsString();
            echo $error_message;
            Helpers::getLogger()->error($error_message, [
                'ip' => Helpers::getClientIP($request->header),
                'module' => $module_name,
                'api' => "/{$controller_name}/$method_name",
                'request_time' => time(),
                'name' => Container::getSession()->get('name'),
                'request_data' => array_merge($request->get, $request->post, $request->files),
                'request_id' => $input->getData()['request_id'] ?? ''
            ]);
            $log_param->respond_message = json_encode(['code' => $throwable->getCode(), 'message' => $throwable->getMessage()]);
            $response_obj = [
                'code' => ResponseCode::SYS_ERROR,
                'message' => '系统内部出错，请联系管理员'
            ];
        }

        $stdout = ob_get_contents();
        ob_end_clean();

        $input->setDataField('token', $token);
        $log_param->ip = Helpers::getClientIP($request->header);
        $log_param->response_time = microtime(true) - $log_param->query_timestamp;
        $log_param->router = $request->header['router'] ?? $request->get['router'] ?? '';
        $log_param->api = empty($module_name) ? "/{$controller_name}/$method_name" : "/$module_name/{$controller_name}/$method_name";
        $log_param->module = $module_name;
        $log_param->controller_class = $controller_class;
        $log_param->method_name = $method_name;
        $log_param->user_id = Container::getSession()->user_id ?: $user_id;
        $log_param->username = Container::getSession()->name ?: $username;
        $log_param->request_message = json_encode($input->getData(), JSON_UNESCAPED_UNICODE);
        $log_param->ua_client = $ua_result->ua->toString();
        $log_param->ua_os = $ua_result->os->toString();
        $log_param->ua_device = $ua_result->device->toString();
        $log_param->request_id = $input->getData()['request_id'] ?? '';


        if (!empty($stdout) && EnvConfig::DEBUG) {
            $response->header('Content-Type', 'text/html; charset=UTF-8');
            $response->end($stdout);
        } elseif (is_array($response_obj)) {
            //如果有设置sql 并且用户id在能打印sql的列表
            if (isset($response_obj['data']) && is_array($response_obj['data']) && isset($response_obj['data']['sql'])
                && !Container::getSession()->get('is_manager_login')
                && !in_array(Container::getSession()->user_id, EnvConfig::PRINT_SQL_USER_ID)) {
                unset($response_obj['data']['sql']);
            }
            $response_obj['request_id'] = $input->getData()['request_id'];
            // HTTP启动gzip压缩并分片返回
            $response_json = json_encode($response_obj, JSON_UNESCAPED_UNICODE);
            if (isset($request->header['accept-encoding']) && strpos($request->header['accept-encoding'], 'gzip') !== false) {
                $response->header('Content-Type', 'application/json; charset=UTF-8');
                $response->header('Content-Encoding', "gzip");
                $response_str = gzencode($response_json, 6);
                $chunk_arr = str_split($response_str, 1024);
                foreach ($chunk_arr as $chunk) {
                    $response->write($chunk);
                }
                $response->end();
            } else {
                $response->end($response_json);
            }

        } elseif ($response_obj instanceof Send) {
            $response_obj->send($response);
        } else {
            $response_obj = [
                'code' => ResponseCode::SYS_ERROR,
                'message' => '系统响应异常'
            ];
            $response->header('Content-Type', 'text/html; charset=UTF-8');
            $response->end(json_encode($response_obj, JSON_UNESCAPED_UNICODE));
            Helpers::getLogger()->error('系统响应异常', [
                'ip' => Helpers::getClientIP($request->header),
                'module' => $module_name,
                'api' => "/{$controller_name}/$method_name",
                'request_time' => time(),
                'name' => Container::getSession()->get('name')
            ]);
        }

        $is_manager_login = Container::getSession()->get('is_manager_login');
        if (!$is_manager_login) {
            (new LogLogic())->addLog($log_param);
        }
        Container::clearSession();
    }

    /**
     * task会接收一个data['action'],这个action是按照className.Method格式传入。
     * 例如：
     * PermissionTask.syncPermission
     * 其中PermissionTask是类名，syncPermission是方法名。
     *
     * @param Server $server
     * @param        $taskId
     * @param        $fromId
     * @param        $data
     */
    public function onTask(Server $server, $taskId, $fromId, $data)
    {
        [$class_name, $method_name] = explode('.', $data['action']);
        $task_name = '\\App\\Task\\' . $class_name;
        if (method_exists($task_name, $method_name)) {
            $task = new $task_name;
            try {
                $task->$method_name($data['data']);
            } catch (\Throwable $throwable) {
                Helpers::getLogger("task")->error("任务出错", [
                    'task_name' => $task_name,
                    'method_name' => $method_name,
                    'data' => $data['data'],
                    'error_code' => $throwable->getCode(),
                    'error_message' => substr($throwable->getMessage(), 0, 1000),
                    'error_trace' => $throwable->getTrace(),
                ]);
            }
        }
    }


    public function onClose(Server $server, int $fd, int $reactorId)
    {
        // 判断当前的请求是否来自websocket客户端
        $info = $server->getClientInfo($fd);
        if ($info && isset($info["websocket_status"]) && intval($info["websocket_status"]) > 0) {
            $ws_manager = Container::getWSManager();
            $ws_manager->onClose($fd);
        }
    }

    public function onWorkerStop(Server $server, $workId)
    {
        print_r("worker id: {$workId} stop" . PHP_EOL);
    }

    public function onWorkerExit(Server $server, $workId)
    {
        print_r("worker id: {$workId} exit" . PHP_EOL);

        if ($workId === 0) {
            if (Timer::clearAll()) {
                print_r("清除 worker id:{$workId} 定时器成功" . PHP_EOL);
            } else {
                print_r("清除 worker id:{$workId} 定时器失败" . PHP_EOL);
            }
        }
    }
}
