<?php
/**
 * 维度、维度筛选工具类
 */

namespace App\Utils;


class ClickhouseDimensionTool
{


    /**
     * 处理维度筛选，按表归类
     *
     * @param array $dimension_filter
     *
     * @return array
     */
    public static function handleDimensionFilter(array $dimension_filter)
    {
        $main = $site_id = $agent_id = $game_id = $ori_server_id =
        $leader_group = $is_cost = $device = $settle_company =
        $is_root_reg = $csite = $user_group = $acc = $live_room =
        $interface_person_group = $ods_company_name = $game_dimension = [];
        foreach ($dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value'] || $key === 'selected') {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'campaign_id':
                case 'adgroup_id':
                case 'adcre_id':
                case 'system_version':
                case 'is_simulator':
                case 'ctype':
                case 'is_mix_os':
                    $main[$key] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'account_id':
                    $main[$key] = ClickhouseSqlParser::get($dimension, 'true_account_id', 't');
                    break;
                case 'site':
                case 'apple_ppid':
                    $site_id[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'agent_leader':
                case 'ad_platform_name':
                case 'agent_group_name':
                case 'team_config_name':
                case 'agent':
                case 'agent_account_id':
                case 'proxy_type':
                case 'channel':
                case 'media_type_id':
                    if ($dimension['column'] === 'platform-account_id') {
                        $dimension['column'] = 'platform-agent_account_id';
                    }
                    $agent_id[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'agent_leader_group_id':
                    $leader_group[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'clique_id':
                case 'root_game':
                case 'main_game':
                case 'game':
                case 'plat_id':
                case 'os':
                case 'contract_game_name':
                    $game_id[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'ori_server_id':
                case 'game_version':
                    $ori_server_id[] = ClickhouseSqlParser::get($dimension, '', '');
                    break;
                case 'create_type':
                case 'port_version':
                case 'convert_type':
                case 'deep_bid_type':
                    $acc[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'is_cost': // is_cost直接拿原始值 比较特殊
                    $is_cost[] = $dimension['value'];
                    break;
                case 'is_old_game': // is_old_game 直接拿原始值  比较特殊
                    $game_dimension[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'device_name':
                case 'device_price_section':
                    $device[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'settle_company_name':
                    $settle_company[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'csite':
                    $csite[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'is_root_reg': // 直接拿原始值 比较特殊
                    $is_root_reg[] = $dimension['value'];
                    break;
                case 'user_group': // 直接拿原始值
                    $user_group = $dimension;
                    break;
                case 'aweme_account':
                case 'aweme_account_type':
                case 'interface_person':
                    $live_room[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                case 'interface_person_group_name':
                    $interface_person_group[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
                // 字段名：机构  原本字段标识应该是company_name，和聚合时的字段统一，因为adb那边company_name已经被使用过了,为了兼容adb那边，故key改为company_name，column为ods_company_name
                case 'ods_company_name':
                    $ods_company_name[] = ClickhouseSqlParser::get($dimension, '', 't');
                    break;
            }
        }

        return [
            'main'                   => $main,
            'game_id'                => $game_id,
            'agent_id'               => $agent_id,
            'leader_group'           => $leader_group,
            'site_id'                => $site_id,
            'ori_server_id'          => $ori_server_id,
            'is_cost'                => $is_cost,
            'device'                 => $device,
            'is_root_reg'            => $is_root_reg,
            'settle_company'         => $settle_company,
            'csite'                  => $csite,
            'user_group'             => $user_group,
            'acc'                    => $acc,
            'live_room'              => $live_room,
            'interface_person_group' => $interface_person_group,
            'ods_company_name'       => $ods_company_name,
            'game_dimension'         => $game_dimension,
        ];

    }


    /**
     * 处理维度聚合分组，按表归类
     *
     * @param $dimension
     *
     * @return array
     */
    public static function handleDimension(&$dimension)
    {
        $main = $leader = $agent_id = $game_id = $site_id = $leader_group = $ext = $device =
        $is_root_reg = $ori_server_id = $settle_company = $csite = $acc =
        $interface_person_group = $game_dimension = [];
        foreach ($dimension as $key => $item) {
            switch ($item) {
                case 'platform':
                case 'system_version':
                case 'is_simulator':
                case 'ctype':
                case 'is_mix_os':
                    // 以下四个指标已经被sql的handleDimension处理完了，在这里直接填写即可
                case 'is_old_clique_game_muid':
                case 'is_old_clique_pay_muid':
                case 'is_old_root_game_muid':
                case 'is_old_root_pay_muid':
                    $main[] = $item;
                    break;
                case 'account_id':
                case 'adgroup_id':
                case 'campaign_id':
                case 'adcre_id':
                case 'aweme_account':
                case 'aweme_account_type':
                case 'interface_person':
                case 'create_type':
                case 'port_version':
                case 'company_name':
                case 'convert_type':
                case 'deep_bid_type':
                    $acc[] = $item;
                    break;
                case 'site_id':
                case 'apple_ppid':
                    $site_id[] = $item;
                    break;
                case 'agent_leader':
                case 'agent_group_id':
                case 'agent_id':
                case 'agent_account_id':
                case 'team_config_name':
                case 'proxy_type':
                case 'channel_id':
                case 'media_type_id':
                    $agent_id[] = $item;
                    break;
                case 'root_game_id':
                case 'game_id':
                case 'main_game_id':
                case 'plat_id':
                case 'os':
                case 'clique_id':
                case 'contract_game_name':
                    $game_id[] = $item;
                    break;
                case 'agent_leader_group_id':
                    $leader_group[] = $item;
                    break;
                case 'device_name':
                case 'device_price_section':
                    $device[] = $item;
                    break;
                case 'ori_server_id':
                case 'game_version':
                    $ori_server_id[] = $item;
                    break;
                case 'settle_company_name':
                    $settle_company[] = $item;
                    break;
                case 'is_root_reg':
                    $is_root_reg[] = $item;
                    break;
                case 'csite':
                    $csite[] = $item;
                    break;
                case 'is_old_game':
                    $game_dimension[] = $item;
                    break;
                case 'interface_person_group_name':
                    $interface_person_group[] = $item;
                    break;
                default:
                    $ext[] = $item;
                    break;
            }
        }

        return [
            'main'                   => $main,
            'game_id'                => $game_id,
            'agent_id'               => $agent_id,
            'site_id'                => $site_id,
            'leader_group'           => $leader_group,
            'device'                 => $device,
            'ori_server_id'          => $ori_server_id,
            'settle_company'         => $settle_company,
            'is_root_reg'            => $is_root_reg,
            'csite'                  => $csite,
            'acc'                    => $acc,
            'interface_person_group' => $interface_person_group,
            'game_dimension'         => $game_dimension,
            'ext'                    => $ext
        ];
    }

    /**
     * id to name
     *
     * @param $column
     *
     * @return array|string|string[]
     */
    public static function idToNameKey($column)
    {
        if ($column === 'csite') {
            return 'csite_name';
        }

        if ($column === 'aweme_account') {
            return 'aweme_name';
        }

        if ($column === 'apple_ppid') {
            return 'ppid_name';
        }

        return str_replace('_id', '_name', $column);
    }
}
