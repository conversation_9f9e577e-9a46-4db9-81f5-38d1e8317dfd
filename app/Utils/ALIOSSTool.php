<?php


namespace App\Utils;


use App\Exception\AppException;
use Common\EnvConfig;
use OSS\Core\OssException;
use OSS\OssClient;

class ALIOSSTool
{
    /**
     * 下载阿里OSS文件到内存
     * @param $object
     * @return string
     */
    static public function download($object)
    {
        $accessKeyId = EnvConfig::ALIBABACLOUD['oss']['access_key_id'];
        $accessKeySecret = EnvConfig::ALIBABACLOUD['oss']['access_key_secret'];
        $endpoint = EnvConfig::ALIBABACLOUD['oss']['endpoint'];
        $bucket= EnvConfig::ALIBABACLOUD['oss']['bucket'];
        // <yourObjectName>表示您下载OSS文件时需要指定的文件路径，例如abc/123.jpg。
        // $object = "1nskOHvpV73EIYcf.png";
        try{
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            $content = $ossClient->getObject($bucket, $object);
        } catch(OssException $e) {
            throw new AppException('OSS文件获取失败: err:' . $e->getErrorMessage());
        }
        return $content;
    }

    /**
     * 下载阿里OSS文件到本地服务器
     * @param $object
     * @param null $target_path
     * @return string
     */
    static public function download2Server($object, $target_path = null)
    {
        $accessKeyId = EnvConfig::ALIBABACLOUD['oss']['access_key_id'];
        $accessKeySecret = EnvConfig::ALIBABACLOUD['oss']['access_key_secret'];
        $endpoint = EnvConfig::ALIBABACLOUD['oss']['endpoint'];
        $bucket= EnvConfig::ALIBABACLOUD['oss']['bucket'];

        if (empty($target_path)) {
            $dir = EnvConfig::UPLOAD_PATH . '/' . EnvConfig::RESUME_DIR_NAME;
            $download_path = SRV_DIR . $dir;
            if (!file_exists($download_path)) {
                mkdir($download_path, 0755, true);
            }
            $local_file = $download_path . '/' . $object;
        } else {
            $local_file = $target_path;
        }

        $options = array(
            OssClient::OSS_FILE_DOWNLOAD => $local_file
        );

        // 使用try catch捕获异常，如果捕获到异常，则说明下载失败；如果没有捕获到异常，则说明下载成功。
        try{
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);

            $ossClient->getObject($bucket, $object, $options);
        } catch(OssException $e) {
            throw new AppException('OSS文件下载失败: err:' . $e->getErrorMessage());
        }
        return isset($dir) ? $dir . '/' . $object : '';//此处写法是为了兼容ResumeController->downloadOSSFile2Server(),实则不应该返回$dir . '/' . $object
    }

    /**
     * 获取文件名
     * @param $url
     * @return false|string
     */
    static public function getObject($url)
    {
        $endpoint = EnvConfig::ALIBABACLOUD['oss']['endpoint'];
        $endpoint = ltrim($endpoint, 'http://');
        $pos = strpos($url, $endpoint);
        if ($pos === false) {
            return $url;
        }
        return substr($url, $pos + strlen($endpoint) + 1);
    }

    /**
     * 分片上传本地文件
     * @param $object @desc oss目录
     * @param $file @desc 本地文件目录
     */
    static public function multiuploadFile($object, $file)
    {
        $accessKeyId = EnvConfig::ALIBABACLOUD['oss']['access_key_id'];
        $accessKeySecret = EnvConfig::ALIBABACLOUD['oss']['access_key_secret'];
        $endpoint = EnvConfig::ALIBABACLOUD['oss']['endpoint'];
        $bucket = EnvConfig::ALIBABACLOUD['oss']['bucket'];

        $options = array(
            OssClient::OSS_CHECK_MD5 => true,
            OssClient::OSS_PART_SIZE => 1,
        );

        try {
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            return $ossClient->multiuploadFile($bucket, $object, $file, $options);
        } catch (\Exception $e) {
            throw new AppException('OSS文件上传失败: err:' . $e->getMessage());
        }
    }

    /**
     * 获取文件全部信息
     * @param $object
     * @return array
     */
    static public function getObjectMeta($object)
    {
        $accessKeyId = EnvConfig::ALIBABACLOUD['oss']['access_key_id'];
        $accessKeySecret = EnvConfig::ALIBABACLOUD['oss']['access_key_secret'];
        $endpoint = EnvConfig::ALIBABACLOUD['oss']['endpoint'];
        $bucket = EnvConfig::ALIBABACLOUD['oss']['bucket'];

        try {
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            return $ossClient->getObjectMeta($bucket, $object);
        } catch (OssException $e) {
            throw new AppException('获取文件全部信息失败: err:' . $e->getErrorMessage());
        }
    }
}