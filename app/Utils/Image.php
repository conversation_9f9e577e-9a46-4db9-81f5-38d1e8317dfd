<?php


namespace App\Utils;


use App\Exception\AppException;

class Image
{

    /**
     * +----------------------------------------------------------
     * 取得图像信息
     * +----------------------------------------------------------
     * @static
     * @access public
     * +----------------------------------------------------------
     * @param string $img 图像文件名
     * +----------------------------------------------------------
     * @return mixed
     * +----------------------------------------------------------
     */
    static public function getImageInfo($img)
    {
        $imageInfo = getimagesize($img);
        if ($imageInfo !== false) {
            $imageType = strtolower(substr(image_type_to_extension($imageInfo[2]), 1));
            $imageSize = filesize($img);
            return array(
                "width" => $imageInfo[0],
                "height" => $imageInfo[1],
                "type" => $imageType,
                "size" => $imageSize,
                "mime" => $imageInfo['mime']
            );
        } else {
            return false;
        }
    }

    /**
     * +----------------------------------------------------------
     * 生成缩略图
     * +----------------------------------------------------------
     * @static
     * @access public
     * +----------------------------------------------------------
     * @param string $image 原图
     * @param string $thumb_name 缩略图文件名
     * @param string $type 图像格式
     * @param int $max_width 宽度
     * @param int $max_height 高度
     * @param boolean $interlace 启用隔行扫描
     * +----------------------------------------------------------
     * @return mixed
     * +----------------------------------------------------------
     */
    static public function thumb($image, $thumb_name, $type = '', $max_width = 200, $max_height = 50, $interlace = true)
    {
        // 获取原图信息
        $info = Image::getImageInfo($image);
        if ($info !== false) {
            $srcWidth = $info['width'];
            $srcHeight = $info['height'];
            $type = empty($type) ? $info['type'] : $type;
            $type = strtolower($type);
            $interlace = $interlace ? 1 : 0;
            unset($info);
//            $scale = min($max_width / $srcWidth, $max_height / $srcHeight); // 计算缩放比例
//            if ($scale >= 1) {
//                // 超过原图大小不再缩略
//                $width = $srcWidth;
//                $height = $srcHeight;
//            } else {
//                // 缩略图尺寸
//                $width = $max_width;
//                $height = $max_height;
//            }
            $width = $max_width;
            $height = $max_height;

            // 载入原图
            $createFun = 'ImageCreateFrom' . ($type == 'jpg' ? 'jpeg' : $type);
            $srcImg = $createFun($image);

            //创建缩略图
            if ($type != 'gif' && function_exists('imagecreatetruecolor'))
                $thumbImg = imagecreatetruecolor($width, $height);
            else
                $thumbImg = imagecreate($width, $height);

            // 复制图片
            if (function_exists("imagecopyresampled")) {
                imagecopyresampled($thumbImg, $srcImg, 0, 0, 0, 0, $width, $height, $srcWidth, $srcHeight);
            } else {
                imagecopyresized($thumbImg, $srcImg, 0, 0, 0, 0, $width, $height, $srcWidth, $srcHeight);
            }
            if ('gif' == $type || 'png' == $type) {
                $background_color = imagecolorallocate($thumbImg, 0, 255, 0);  //  指派一个绿色
                imagecolortransparent($thumbImg, $background_color);  //  设置为透明色，若注释掉该行则输出绿色的图
            }

            // 对jpeg图形设置隔行扫描
            if ('jpg' == $type || 'jpeg' == $type) {
                imageinterlace($thumbImg, $interlace);
            }

            // 生成图片
            $imageFun = 'image' . ($type == 'jpg' ? 'jpeg' : $type);
            $imageFun($thumbImg, $thumb_name);
            imagedestroy($thumbImg);
            imagedestroy($srcImg);
            return $thumb_name;
        }
        return false;
    }

    /**
     * 压缩图片
     * @param $source_path
     * @param $target_path
     * @param int $max_size_kb
     * @return bool
     */
    static function compress($source_path, $target_path, $max_size_kb = 10): bool
    {
        $max_quality = 90;
        $min_quality = 5;
        $target_size_bytes = $max_size_kb * 1024;

        // 原始图片信息
        $info = getimagesize($source_path);
        $mime = $info['mime'];

        // 根据MIME类型创建图像资源
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source_path);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source_path);
                imagepalettetotruecolor($image); // 确保真彩色
                imagealphablending($image, true); // 保留透明度
                imagesavealpha($image, true);
                break;
            default:
                throw new AppException('错误的压缩图片格式');
        }

        // 循环质量压缩
        $quality = $max_quality;
        do {
            ob_start();
            if ($mime == 'image/jpeg') {
                imageinterlace($image, 1); // 启用渐进式JPEG
                imagejpeg($image, null, $quality);
            } else {
                imagepng($image, null, 9); // PNG固定最高压缩率
            }
            $img_data = ob_get_clean();

            // 检查是否达标
            if (strlen($img_data) <= $target_size_bytes || $quality <= $min_quality) {
                break;
            }

            $quality -= 10; // 每次降低10%质量
        } while ($quality >= $min_quality);

        if (strlen($img_data) > $target_size_bytes) {
            echo date('Y-m-d H:i:s') . '最终压缩失败,路径为:' . $target_path . ' ,最终大小是:' . strlen($img_data) . PHP_EOL;
        }

//        // 若仍未达标，转换为WebP格式 这玩意要php8.1+才有
//        if (function_exists('imagewebp')) {
//            $quality = $max_quality;
//            do {
//                ob_start();
//                imagewebp($image, null, $quality);
//                $img_data = ob_get_clean();
//
//                // 检查是否达标
//                if ((strlen($img_data) <= $target_size_bytes || $quality <= $min_quality)) {
//                    break;
//                }
//
//                $quality -= 10; // 每次降低10%质量
//            } while ($quality >= $min_quality);
//        }

//        // 若仍未达标，转换为avif格式  这玩意要php8.1+才有
//        if (function_exists('imageavif')) {
//            $quality = $max_quality;
//            do {
//                ob_start();
//                imageavif($image, null, $quality);
//                $img_data = ob_get_clean();
//
//                // 检查是否达标
//                if ((strlen($img_data) <= $target_size_bytes || $quality <= $min_quality)) {
//                    break;
//                }
//
//                $quality -= 10; // 每次降低10%质量
//            } while ($quality >= $min_quality);
//        }


        // 保存最终结果
        file_put_contents($target_path, $img_data);
        imagedestroy($image);
        return true;
    }


    /**
     * 合并图片
     * @param $pic_list
     * @param $width
     * @param $height
     * @param $new_file_path
     */
    static function combine($pic_list, $new_file_path, $width = 200, $height = 200)
    {
        $pic_list = array_slice($pic_list, 0, 9); // 只操作前9个图片

        $bg_w = $width; // 背景图片宽度
        $bg_h = $height; // 背景图片高度

        $background = imagecreatetruecolor($bg_w, $bg_h); // 背景图片
        // 为真彩色画布创建白色背景，再设置为透明
        $color = imagecolorallocate($background, 202, 201, 201);
        imagefill($background, 0, 0, $color);
        imageColorTransparent($background, $color);

        $pic_count = count($pic_list);
        $lineArr = array(); // 需要换行的位置
        $space_x = 3;
        $space_y = 3;
        $line_x = 0;
        $start_x = 0;
        $start_y = 0;
        $pic_w = 0;
        $pic_h = 0;
        switch ($pic_count) {
            case 1: // 正中间
                $start_x = intval($bg_w / 4); // 开始位置X
                $start_y = intval($bg_h / 4); // 开始位置Y
                $pic_w = intval($bg_w / 2); // 宽度
                $pic_h = intval($bg_h / 2); // 高度
                break;
            case 2: // 中间位置并排
                $start_x = 2;
                $start_y = intval($bg_h / 4) + 3;
                $pic_w = intval($bg_w / 2) - 5;
                $pic_h = intval($bg_h / 2) - 5;
                $space_x = 5;
                break;
            case 3:
                $start_x = 5; // 开始位置X
                $start_y = ($bg_h - (intval($bg_w / 3) - 5)) / 2; // 开始位置Y
                $pic_w = intval($bg_w / 3) - 5; // 宽度
                $pic_h = intval($bg_w / 3) - 5; // 高度
                $space_x = 5;
                break;
            case 4:
                $start_x = 5; // 开始位置X
                $start_y = 5; // 开始位置Y
                $pic_w = intval($bg_w / 2) - 5; // 宽度
                $pic_h = intval($bg_h / 2) - 5; // 高度
                $lineArr = array(3);
                $line_x = 5;
                break;
            case 5:
                $start_x = 30; // 开始位置X
                $start_y = 30; // 开始位置Y
                $pic_w = intval($bg_w / 3) - 5; // 宽度
                $pic_h = intval($bg_h / 3) - 5; // 高度
                $lineArr = array(3);
                $line_x = 5;
                break;
            case 6:
                $start_x = 5; // 开始位置X
                $start_y = ($bg_h - ((intval($bg_w / 3) - 5) * 2)) / 2; // 开始位置Y
                $pic_w = intval($bg_w / 3) - 5; // 宽度
                $pic_h = intval($bg_w / 3) - 5; // 高度
                $lineArr = array(4);
                $line_x = 5;
                break;
            case 7:
                $start_x = 53; // 开始位置X
                $start_y = 5; // 开始位置Y
                $pic_w = intval($bg_w / 3) - 5; // 宽度
                $pic_h = intval($bg_h / 3) - 5; // 高度
                $lineArr = array(2, 5);
                $line_x = 5;
                break;
            case 8:
                $start_x = 30; // 开始位置X
                $start_y = 5; // 开始位置Y
                $pic_w = intval($bg_w / 3) - 5; // 宽度
                $pic_h = intval($bg_h / 3) - 5; // 高度
                $lineArr = array(3, 6);
                $line_x = 5;
                break;
            case 9:
                $start_x = 5; // 开始位置X
                $start_y = 5; // 开始位置Y
                $pic_w = intval($bg_w / 3) - 5; // 宽度
                $pic_h = intval($bg_h / 3) - 5; // 高度
                $lineArr = array(4, 7);
                $line_x = 5;
                break;
        }
        foreach ($pic_list as $k => $pic_path) {
            $kk = $k + 1;
            if (in_array($kk, $lineArr)) {
                $start_x = $line_x;
                $start_y = $start_y + $pic_h + $space_y;
            }
            $pathInfo = pathinfo($pic_path);
            switch (strtolower($pathInfo['extension'])) {
                case 'jpg':
                case 'jpeg':
                    $imagecreatefrom = 'imagecreatefromjpeg';
                    break;
                case 'png':
                    $imagecreatefrom = 'imagecreatefrompng';
                    break;
                case 'gif':
                default:
                    $imagecreatefrom = 'imagecreatefromstring';
                    $pic_path = file_get_contents($pic_path);
                    break;
            }
            $resource = $imagecreatefrom($pic_path);
            // $start_x,$start_y copy图片在背景中的位置
            // 0,0 被copy图片的位置
            // $pic_w,$pic_h copy后的高度和宽度
            // 最后两个参数为原始图片宽度和高度，倒数两个参数为copy时的图片宽度和高度
            imagecopyresized($background, $resource, $start_x, $start_y, 0, 0, $pic_w, $pic_h, imagesx($resource), imagesy($resource));
            $start_x = $start_x + $pic_w + $space_x;
        }

        imagejpeg($background, $new_file_path);
    }
}
