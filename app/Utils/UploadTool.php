<?php


namespace App\Utils;

use App\Constant\MIMEType;
use App\Exception\AppException;
use Common\EnvConfig;
use FFMpeg\FFMpeg;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use Throwable;

class UploadTool
{
    /**
     * 上传图片
     *
     * @param       $files
     * @param       $sub_path
     * @param array $verify_options
     *      [
     *      type=> ['image/gif', 'image/jpg'],
     *      height => ['>', 100],
     *      width => ['>', 200],
     *      scale => 100/200, 宽高比
     *      ]; // 图片宽高为像素值
     *
     * @return string
     */
    static public function image($files, $sub_path, $verify_options = [])
    {
        if ($files["file"]["error"] > 0) {
            throw new AppException("错误：: " . $files["file"]["error"]);
        }
        $type = ['image/gif', 'image/jpeg', 'image/jpg', 'image/pjpeg', 'image/x-png', 'image/png'];
        $type = isset($verify_options['type']) ? $verify_options['type'] : $type;
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        if (!in_array($files["file"]["type"], $type)) {
            throw new AppException("非法的文件格式,只允许" . implode(',', $type));
        }
        if (isset($verify_options['width']) || isset($verify_options['height']) || isset($verify_options['scale']) || isset($verify_options['size'])) {
            $image_info = getimagesize($files["file"]["tmp_name"]);
            $operator = function ($left, $operator, $right) {
                switch ($operator) {
                    case '===':
                    case '==':
                        return $left == $right;
                    case '!=':
                        return $left != $right;
                    case '<':
                        return $left < $right;
                    case '>':
                        return $left > $right;
                    case '<=':
                        return $left <= $right;
                    case '>=':
                        return $left >= $right;
                    default:
                        throw new AppException("不支持该运算符");
                }
            };
            if (isset($verify_options['width']) && !$operator($image_info[0], $verify_options['width'][0], $verify_options['width'][1])) {
                throw new AppException("宽度需要{$verify_options['width'][0]}{$verify_options['width'][1]}");
            }
            if (isset($verify_options['height']) && !$operator($image_info[1], $verify_options['height'][0], $verify_options['height'][1])) {
                throw new AppException("高度需要{$verify_options['height'][0]}{$verify_options['height'][1]}");
            }
            if (isset($verify_options['scale']) && bccomp($image_info[1] / $image_info[0], $verify_options['scale'], 2) !== 0) {
                throw new AppException("宽高比需要等于{$verify_options['scale']}");
            }
            if (isset($verify_options['size']) && !$operator($files["file"]['size'], $verify_options['size'][0], $verify_options['size'][1])) {
                throw new AppException("大小需要{$verify_options['size'][0]}{$verify_options['size'][1]}");
            }
        }

        $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
        $upload_path = SRV_DIR . "/{$access_path}";
        // 判断上传目录是否存在，不存在则创建
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0755, true);
        }

        // 保存的文件名
        $filename = md5_file($files["file"]["tmp_name"]) . '.' . $extension;
        $fullname = "{$upload_path}/{$filename}";
        // 将文件上传到指定目录下
        move_uploaded_file($files["file"]["tmp_name"], $fullname);
        return $access_path . '/' . $filename;
    }

    /**
     * 上传落地页文件
     *
     * @param $files
     *
     * @return array
     */
    static public function file($files)
    {
        $source_name = $files["file"]["name"];
        $temp = explode(".", $source_name);
        $extension = end($temp);// 获取文件后缀名
        if ($files["file"]["error"] > 0) {
            throw new AppException("错误：: " . $files["file"]["error"]);
        }

        $access_path = TMP_DIR;
        // 保存的文件名
        $filename = md5_file($files["file"]["tmp_name"]) . ".{$extension}";
        $fullname = "{$access_path}/{$filename}";
        // 将文件上传到指定目录下
        move_uploaded_file($files["file"]["tmp_name"], $fullname);
        return [
            'access_path' => $fullname,
            'filename'    => $source_name
        ];
    }

    /**
     * 处理上传的csv文件并返回数据
     *
     * @param $files
     *
     * @return array  返回csv的每一行数据
     */
    static public function csv($files)
    {
        $extension = strtolower(pathinfo($files['file']['name'], PATHINFO_EXTENSION));
        if ($extension !== 'csv') {
            throw new AppException("非法的文件格式");
        }
        if ($files["file"]["error"] > 0) {
            throw new AppException("错误：: " . $files["file"]["error"]);
        }

        // 读取上传的文件内容
        $list = [];
        $file = fopen($files['file']['tmp_name'], 'r');

        // 处理BOM头（例如UTF-8 BOM）
        $bom = fread($file, 3);
        if ($bom !== "\xEF\xBB\xBF") {
            rewind($file); // 不是BOM则重置指针
        }

        while (($data = fgetcsv($file, 10000)) !== false) {
            foreach ($data as &$field) {
                // 更精确的编码检测（添加常见编码如CP936）
                $encode = mb_detect_encoding($field, ['CP936', 'GB18030', 'GB2312', 'GBK', 'ASCII', 'UTF-8'], true);
                if ($encode && $encode !== 'UTF-8') {
                    $field = mb_convert_encoding($field, 'UTF-8', $encode);
                }
            }
            $list[] = $data;
        }
        fclose($file);
        return $list;
    }

    /**
     * 处理上传的xls文件并返回数据
     *
     * @param $files
     *
     * @return array  返回表格的每一行数据
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \Exception
     */
    static public function xlsx($files)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        if ($extension === 'xls' || $extension === 'xlsx') {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $file = $files['file']['tmp_name'];
                // 读取上传的文件内容
                $objRead = IOFactory::createReader('Xlsx');

                if (!$objRead->canRead($file)) {
                    /** @var Xls $objRead */
                    $objRead = IOFactory::createReader('Xls');

                    if (!$objRead->canRead($file)) {
                        throw new \Exception('只支持导入Excel文件！');
                    }
                }

                $objRead->setReadDataOnly(TRUE);
                //载入excel表格
                $spreadsheet = $objRead->load($file);
                // 读取第一個工作表
                $sheet = $spreadsheet->getSheet(0);

                return $sheet->toArray();
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传素材图片
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function materialImage($files, $sub_path)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }
        if (in_array($files["file"]["type"], [
            MIMEType::IMAGE_GIF, MIMEType::IMAGE_JPEG, MIMEType::IMAGE_JPG, MIMEType::IMAGE_PJPEG, MIMEType::IMAGE_X_PNG, MIMEType::IMAGE_PNG
        ])) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $md5 = md5_file($files["file"]["tmp_name"]);
                $filename = $md5 . '_' . $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                $size = getimagesize($fullname);
                return [
                    'name'      => $filename,
                    'md5'       => $md5,
                    'base_name' => $base_name,
                    'width'     => $size[0],
                    'height'    => $size[1],
                    'path'      => $access_path . '/' . $filename
                ];
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传素材视频
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function materialVideo($files, $sub_path)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }
        if (in_array($files["file"]["type"], [
            MIMEType::VIDEO_MP4, MIMEType::VIDEO_AVI, MIMEType::VIDEO_MPEG, MIMEType::VIDEO_MPG, MIMEType::VIDEO_M4V,
            MIMEType::VIDEO_MOV, MIMEType::VIDEO_WMV, MIMEType::VIDEO_ASX,
        ])) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $md5 = md5_file($files["file"]["tmp_name"]);
                $filename = $md5 . '_' . $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                $ffmpeg = FFMpeg::create();
                try {
                    $open_video = $ffmpeg->open($fullname);
                    $width = $open_video->getStreams()->videos()->first()->get("width");
                    $height = $open_video->getStreams()->videos()->first()->get("height");
                    $duration = round($open_video->getFormat()->get("duration"), 3);
                    return [
                        'name'      => $filename,
                        'md5'       => $md5,
                        'base_name' => $base_name,
                        'width'     => $width,
                        'height'    => $height,
                        'path'      => $access_path . '/' . $filename,
                        'duration'  => $duration
                    ];
                } catch (Throwable $exception) {
                    throw new AppException("视频格式有误");
                }
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传素材音频
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function materialAudio($files, $sub_path, $allow_file_type = [MIMEType::AUDIO_MPEG])
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }
        if (in_array($files["file"]["type"], $allow_file_type)) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $md5 = md5_file($files["file"]["tmp_name"]);
                $filename = $md5 . '_' . $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                $ffmpeg = FFMpeg::create();
                try {
                    $audio = $ffmpeg->open($fullname);
                    $duration = round($audio->getFormat()->get("duration"), 3);
                    $file_size = filesize($fullname);
                    return [
                        'name'      => $filename,
                        'md5'       => $md5,
                        'base_name' => $base_name,
                        'path'      => $access_path . '/' . $filename,
                        'duration'  => $duration,
                        'file_size' => $file_size
                    ];
                } catch (Throwable $exception) {
                    throw new AppException("音频格式有误：" . $exception->getMessage());
                }
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传素材zip
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function materialAep($files, $sub_path)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }

        if (in_array($files["file"]["type"], [MIMEType::APPLICATION_OCTET_STREAM])) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $md5 = md5_file($files["file"]["tmp_name"]);
                $filename = $md5 . '_' . $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                return [
                    'name'      => $filename,
                    'md5'       => $md5,
                    'base_name' => $base_name,
                    'path'      => $access_path . '/' . $filename
                ];
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传素材txt
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function materialTxt($files, $sub_path)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }

        if (in_array($files["file"]["type"], [MIMEType::APPLICATION_TEXT])) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $md5 = md5_file($files["file"]["tmp_name"]);
                $filename = $md5 . '_' . $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                return [
                    'name'      => $filename,
                    'md5'       => $md5,
                    'base_name' => $base_name,
                    'path'      => $access_path . '/' . $filename
                ];
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传素材zip
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function materialZip($files, $sub_path)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }
        if (in_array($files["file"]["type"], [MIMEType::APPLICATION_ZIP, MIMEType::APPLICATION_X_ZIP_COMPRESSED])) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $md5 = md5_file($files["file"]["tmp_name"]);
                $filename = $md5 . '_' . $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                return [
                    'name'      => $filename,
                    'md5'       => $md5,
                    'base_name' => $base_name,
                    'path'      => $access_path . '/' . $filename
                ];
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传通用文件，这里的文件可能是word，也可能是图片/视频， 根据$sub_path区分文件用途
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function commonFile($files, $sub_path)
    {
        $source_name = $files["file"]["name"];
        $temp = explode(".", $source_name);
        $extension = end($temp);// 获取文件后缀名
        if ($files["file"]["error"] > 0) {
            throw new AppException("错误：: " . $files["file"]["error"]);
        }

        $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
        $upload_path = SRV_DIR . "/{$access_path}";
        // 判断上传目录是否存在，不存在则创建
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0755, true);
        }
        // 保存的文件名
        $filename = md5_file($files["file"]["tmp_name"]) . ".{$extension}";
        $full_name = "{$upload_path}/{$filename}";
        // 将文件上传到指定目录下
        move_uploaded_file($files["file"]["tmp_name"], $full_name);
        return [
            'path'     => $access_path . '/' . $filename,
            'url'      => EnvConfig::DOMAIN . "{$access_path}/{$filename}",
            'filename' => $source_name
        ];
    }

    /**
     * 上传财务截图
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function financeImage($files, $sub_path)
    {
        $temp = explode(".", $files["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($files["file"]["name"], '.' . $extension);
        if ((($files["file"]["type"] == "image/gif")
            || ($files["file"]["type"] == "image/jpeg")
            || ($files["file"]["type"] == "image/jpg")
            || ($files["file"]["type"] == "image/pjpeg")
            || ($files["file"]["type"] == "image/x-png")
            || ($files["file"]["type"] == "image/png")
            || ($files["file"]["type"] == "application/vnd.ms-excel")
            || ($files["file"]["type"] == "application/octet-stream"))) {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }
                // 保存的文件名
                $filename = $files["file"]["name"];
                $fullname = "{$upload_path}/{$filename}";
                // 将文件上传到指定目录下
                move_uploaded_file($files["file"]["tmp_name"], $fullname);
                $size = getimagesize($fullname);
                return ['name'      => $filename,
                        'base_name' => $base_name,
                        'path'      => $access_path . '/' . $filename];
            }
        } else {
            throw new AppException("非法的文件格式");
        }
    }

    /**
     * 上传APK文件
     *
     * @param $files
     * @param $sub_path
     *
     * @return array
     */
    static public function APK($files, $sub_path)
    {
        $source_name = $files["file"]["name"];
        $temp = explode(".", $source_name);
        $extension = end($temp);// 获取文件后缀名
        if ($extension === 'apk') {
            if ($files["file"]["error"] > 0) {
                throw new AppException("错误：: " . $files["file"]["error"]);
            }

            $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
            $upload_path = SRV_DIR . "/{$access_path}";
            // 判断上传目录是否存在，不存在则创建
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0755, true);
            }
            // 保存的文件名
//            $filename = md5_file($files["file"]["tmp_name"]) . ".{$extension}";
            $full_name = "{$upload_path}/{$source_name}";
            // 将文件上传到指定目录下
            move_uploaded_file($files["file"]["tmp_name"], $full_name);
            return [
                'path'     => $access_path . '/' . $source_name,
                'filename' => $source_name
            ];
        } else {
            throw new AppException("非法文件格式");
        }

    }

    /**
     * 上传素材图片
     *
     * @param $file
     * @param $sub_path
     *
     * @return array
     */
    static public function materialExpert($file, $expert_name)
    {
        $temp = explode(".", $file["file"]["name"]);
        $extension = end($temp);// 获取文件后缀名
        $base_name = basename($file["file"]["name"], '.' . $extension);
        if (strpos($base_name, ' ')) {
            throw new AppException("文件名不能有空格");
        }
        if (in_array($file["file"]["type"], [MIMEType::IMAGE_X_PNG, MIMEType::IMAGE_PNG, MIMEType::IMAGE_JPG, MIMEType::IMAGE_JPEG])) {
            if ($file["file"]["error"] > 0) {
                throw new AppException("错误：: " . $file["file"]["error"]);
            } else {
                $access_path = EnvConfig::UPLOAD_PATH . '/material_expert';
                $upload_path = SRV_DIR . "/{$access_path}";
                // 判断上传目录是否存在，不存在则创建
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0755, true);
                }

                // 保存的文件名
                $time = time();
                $full_name = "{$upload_path}/{$expert_name}_$time.{$extension}";
                // 将文件上传到指定目录下
                move_uploaded_file($file["file"]["tmp_name"], $full_name);
                return [
                    'base_name' => $base_name,
                    'url'       => EnvConfig::DOMAIN . "/upload/material_expert/{$expert_name}_$time.{$extension}"
                ];
            }
        } else {
            throw new AppException("非法的文件格式,只接收PNG或者JPG格式");
        }
    }
}
