<?php
/**
 * Created by PhpStorm.
 * User: zhenorzz
 * Date: 2019/11/23
 * Time: 9:34
 */

namespace App\Utils;


use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use Common\EnvConfig;
use function AlibabaCloud\Client\json;

class SMS
{
    const ACCESS_KEY_ID = EnvConfig::ALIBABACLOUD['sms']['access_key_id'];
    const ACCESS_SECRET = EnvConfig::ALIBABACLOUD['sms']['access_secret'];
    const REGION_ID = 'cn-hangzhou';
    const HOST = 'dysmsapi.aliyuncs.com';
    const PRODUCT = 'Dysmsapi';
    const VERSION = '2017-05-25';
    const SIGN_NAME = '中旭未来';

    const AUTH_CODE = 'SMS_250931969';
    const KPI_CODE = 'SMS_263615015';
    const DEVICE_CODE = 'SMS_263530012';
    const AUDIENCE_CODE = 'SMS_263600012';
    const DATAHUB_ERROR_LOG_CODE = 'SMS_263455009';
    const ORDER_CODE = 'SMS_263420010';
    const LOG_INPUT_VERIFY_CODE = 'SMS_263410014';
    const NEW_SERVER_WARNING_CODE = 'SMS_263595012';
    const TRUE_NAME_WARNING_CODE = 'SMS_263590009';

    // 考勤确认提醒
    const ATTENDANCE_CODE = 'SMS_263465008';
    // 工时确认提醒
    const WORKFLOW_WORK_HOUR_CODE = 'SMS_263560015';
    // 部门负责人项目提交提醒
    const WORKFLOW_PROJECT_CODE = 'SMS_263600009';


    static public function auth($phone_numbers, $code)
    {
        $template_param = json_encode(['code' => $code]);
        return self::send(self::AUTH_CODE, $phone_numbers, $template_param);
    }

    static public function kpi($phone_numbers, $username, $kpi_name, $process_rate)
    {
        $template_param = json_encode([
            'username' => $username,
            'kpi_name' => $kpi_name,
            'process_rate' => $process_rate,
        ]);
        return self::send(self::KPI_CODE, $phone_numbers, $template_param);
    }

    static public function device($phone_numbers, $task_name, $state_name)
    {
        $template_param = json_encode([
            'task_name' => $task_name,
            'state_name' => $state_name,
        ]);
        return self::send(self::DEVICE_CODE, $phone_numbers, $template_param);
    }


    static public function audience($phone_numbers, $company, $task_name, $state_name)
    {
        $template_param = json_encode([
            'user' => $company,
            'task_name' => $task_name,
            'status' => $state_name
        ]);
        return self::send(self::AUDIENCE_CODE, $phone_numbers, $template_param);
    }

    static public function datahubErrorLog($phone_numbers, $platform, $table_name, $num)
    {
        $template_param = json_encode([
            'platform' => $platform,
            'tablename' => $table_name,
            'num' => $num,
        ]);
        return self::send(self::DATAHUB_ERROR_LOG_CODE, $phone_numbers, $template_param);
    }

    static public function order($phone_numbers, $order_name, $status)
    {
        $template_param = json_encode([
            'mission_name' => $order_name,
            'status' => $status,
        ]);
        return self::send(self::ORDER_CODE, $phone_numbers, $template_param);
    }

    static public function logInputVerify($phone_numbers,$platform, $table_name, $field, $percent)
    {
        $template_param = json_encode([
            'platform' => $platform,
            'tablename' => $table_name,
            'field' => $field,
            'percent' => $percent,
        ]);
        return self::send(self::LOG_INPUT_VERIFY_CODE, $phone_numbers, $template_param);
    }

    static public function newServerWarning($phone_numbers, $platform, $product_name, $product_id, $time, $count)
    {
        $template_param = json_encode([
            'platform' => $platform,
            'product_name' => $product_name,
            'product_id' => $product_id,
            'time' => $time,
            'count' => $count
        ]);
        return self::send(self::NEW_SERVER_WARNING_CODE, $phone_numbers, $template_param);
    }

    static public function trueNameWarning($phone_numbers, $product_name)
    {
        $template_param = json_encode([
            'product_name' => $product_name,
        ]);
        return self::send(self::TRUE_NAME_WARNING_CODE, $phone_numbers, $template_param);
    }

    public static function attendanceWarning($phone_numbers)
    {
        return self::send(self::ATTENDANCE_CODE, $phone_numbers, '');
    }

    public static function workflowWorkHourWarning($phone_numbers)
    {
        return self::send(self::WORKFLOW_WORK_HOUR_CODE, $phone_numbers, '');
    }

    public static function workflowProjectWarning($phone_numbers)
    {
        return self::send(self::WORKFLOW_PROJECT_CODE, $phone_numbers, '');
    }

    /**
     * @param $template_code
     * @param $phone_numbers
     * @param $template_param
     * @return bool
     * <AUTHOR>
     */
    static private function send($template_code, $phone_numbers, $template_param)
    {
        if (!is_array($phone_numbers)) {
            $phone_numbers = [$phone_numbers];
        }
        $phone_numbers = implode(',', $phone_numbers);
        AlibabaCloud::accessKeyClient(self::ACCESS_KEY_ID, self::ACCESS_SECRET)
            ->regionId(self::REGION_ID)
            ->asDefaultClient();
        try {
            $res_json = AlibabaCloud::rpc()
                ->product(self::PRODUCT)
                // ->scheme('https') // https | http
                ->version(self::VERSION)
                ->action('SendSms')
                ->method('POST')
                ->host(self::HOST)
                ->options([
                    'query' => [
                        'RegionId' => self::REGION_ID,
                        'PhoneNumbers' => $phone_numbers,
                        'SignName' => self::SIGN_NAME,
                        'TemplateCode' => $template_code,
                        'TemplateParam' => $template_param,
                    ],
                ])
                ->request();
        } catch (ClientException $e) {
            Helpers::getLogger('sms')->error("send fail.", [
                'phone_numbers' => $phone_numbers,
                'message' => $e->getErrorMessage(),
            ]);
            return false;
        } catch (ServerException $e) {
            Helpers::getLogger('sms')->error("send fail.", [
                'phone_numbers' => $phone_numbers,
                'message' => $e->getErrorMessage(),
            ]);
            return false;
        }
        $res_arr = json_decode($res_json, true);
        if ($res_arr['Code'] !== 'OK') {
            Helpers::getLogger('sms')->error("send fail.", [
                'phone_numbers' => $phone_numbers,
                $res_arr
            ]);
            return false;
        }
        return true;
    }
}
