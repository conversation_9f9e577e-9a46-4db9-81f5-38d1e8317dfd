<?php

namespace App\Utils;


use App\Constant\PlatId;
use App\Model\SqlModel\Zeda\MaterialLabelModel;
use App\Model\SqlModel\Zeda\MaterialThemeModel;
use Common\EnvConfig;

final class ClickhouseSqlParser
{
    // 以下字段需要去dict字典表查询出具体值
    public static $dict_column = [
        'agent_leader',
        'agent_group_id',
        'agent_id',
        'apple_ppid',
        'agent_account_id',
        'team_config_name',
        'proxy_type',
        'channel_id',
        'is_old_game',

        'root_game_id',
        'main_game_id',
        'plat_id',
        'os',
        'clique_id',
        'team',
        'contract_game_name',

        'settle_company_name',
        'agent_leader_group_id',
        'interface_person_group_name',

        'device_name',
        'device_price_section',

        // 直播间、直播间对接人 使用 joinGet 获取具体值
        'aweme_account',
        'aweme_account_type',
        'interface_person',
        'create_type',
        'port_version',
        'ods_company_name',
        'action_track_type'
    ];

    public static $dict_flag = 'DICT-';

    /**
     * @param        $filter
     * @param string $rename_column 可以将查询的字段换名字
     * @param string $alias
     *
     * @return mixed
     */
    static public function get($filter, $rename_column = '', $alias = '')
    {
        $method = Helpers::camelize($filter['condition']);
        $format = self::$method(!empty($rename_column) ? $rename_column : $filter['column'], $filter['value'], $alias);
        return $format;
    }

    /**
     * 查库时使用的字段名
     *
     * @param        $column
     * @param string $alias
     *
     * @return mixed|string
     */
    static private function getFieldColumn($column, $alias = '')
    {
        // 特殊：此类需要dictGet出数据，再进一步过滤
        if (in_array($column, self::$dict_column)) {
            $field_column = "dict-{$column}";
        } else {
            $field_column = $alias ? "{$alias}.{$column}" : $column;
        }
        return $field_column;
    }

    static private function in($column, $values, $alias)
    {
        if (!is_array($values)) {
            $values = [$values];
        }
        $c = explode('-', $column);
        if (count($c) > 1) {
            $data = [];
            foreach ($values as &$value) {
                if ($c[1] === 'agent_leader' || $c[1] === 'apple_ppid') {
                    $v = explode('-', $value, 2);
                } else {
                    $v = explode('-', $value);
                }
                $platform = array_search($v[0], EnvConfig::PLATFORM_MAP);
                if (!isset($data[$platform])) {
                    $data[$platform] = [];
                }
                $data[$platform][] = $v[1];
            }
            $platform_column = self::getFieldColumn($c[0], $alias);
            $field_column = self::getFieldColumn($c[1], $alias);

            // 这个判断是混服专用
            if ($c[1] === 'theme_pid') {
                $condition = '(';
                foreach ($data as $platform => $bind) {
                    $field_column = $alias ? "{$alias}.theme_id" : 'theme_id';
                    $condition .= "({$platform_column} = '{$platform}' AND (";
                    foreach ($bind as $theme_pid) {
                        $new_values = MaterialThemeModel::subThemeRange($theme_pid);
                        $condition .= "({$field_column} BETWEEN '{$new_values[0]}' AND '{$new_values[1]}') OR ";
                    }
                    $condition = substr($condition, 0, -3) . ')) OR ';
                }
                $condition = substr($condition, 0, -3) . ')';
            } else {
                $condition = '';
                foreach ($data as $platform => $bind) {
                    if (($c[1] === 'main_game_id' || $c[1] === 'game_id') && count($bind) === 1 && $bind[0] >= 1000000) {
                        $field_column = 'mix.mix_id';
                    }

                    foreach ($bind as $tmp_val) {
                        $condition .= "('$platform', '$tmp_val'),";
                    }
                }
                $condition = "($platform_column, $field_column) IN ( " . rtrim($condition, ',') . ')';
            }
        } else {
            $field_column = self::getFieldColumn($column, $alias);
            if ($column === 'label_pid') {
                $field_column = $alias ? "{$alias}.label_id" : 'label_id';
                $condition = "(";
                foreach ($values as $value) {
                    $new_values = MaterialLabelModel::subLabelRange($value);
                    $condition .= "({$field_column} BETWEEN '{$new_values[0]}' AND '{$new_values[1]}') OR ";
                }
                $condition = substr($condition, 0, -3) . ')';
            } else {
                if ($column === 'platform') {
                    foreach ($values as &$value) {
                        $value = (string)array_search($value, EnvConfig::PLATFORM_MAP);
                    }
                } else if ($column === 'plat_id') {
                    foreach ($values as &$value) {
                        $value = array_search($value, PlatId::MAP);
                    }
                }

                $new_values = "'" . implode("','", $values) . "'";
                $condition = "{$field_column} IN (" . $new_values . ')';
            }

            // 负责人组为空的特殊处理
            if ($column == 'interface_person_group_name' && count($values) === 1 && $values[0] === '未分组') {
                $condition = "{$field_column} = ''";
            }
        }
        return $condition;
    }

    static private function notIn($column, $values, $alias)
    {
        if (!is_array($values)) {
            $values = [$values];
        }
        $c = explode('-', $column);
        if (count($c) > 1) {
            $data = [];
            foreach ($values as &$value) {
                if ($c[1] === 'agent_leader' || $c[1] === 'apple_ppid') {
                    $v = explode('-', $value, 2);
                } else {
                    $v = explode('-', $value);
                }
                $platform = array_search($v[0], EnvConfig::PLATFORM_MAP);
                if (!isset($data[$platform])) {
                    $data[$platform] = [];
                }
                $data[$platform][] = Helpers::isInt($v[1]) ? (int)$v[1] : $v[1];
            }
            $platform_column = self::getFieldColumn($c[0], $alias);
            $field_column = self::getFieldColumn($c[1], $alias);

            $condition = '';
            foreach ($data as $platform => $bind) {
                foreach ($bind as $tmp_val) {
                    $condition .= "('$platform', '$tmp_val'),";
                }
            }

            $condition = "($platform_column, $field_column) NOT IN ( " . rtrim($condition, ',') . ')';
        } else {
            $field_column = self::getFieldColumn($column, $alias);
            if ($column === 'platform') {
                foreach ($values as &$value) {
                    $value = (string)array_search($value, EnvConfig::PLATFORM_MAP);
                }
            } else if ($column === 'plat_id') {
                foreach ($values as &$value) {
                    $value = array_search($value, PlatId::MAP);
                }
            }
            $new_values = "'" . implode("','", $values) . "'";
            $condition = "{$field_column} NOT IN (" . $new_values . ')';

            // 负责人组为空的特殊处理
            if ($column == 'interface_person_group_name' && count($values) === 1 && $values[0] === '未分组') {
                $condition = "{$field_column} = ''";
            }
            // 负责人组为空的特殊处理
            if ($column == 'agent_leader_group_id' && count($values) === 1 && $values[0] == 0) {
                $condition = "{$field_column} = ''";
            }
        }

        return $condition;
    }

    static private function eq($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} = '{$value}'";
    }

    static private function yes($column, $value, $alias = '')
    {
        if (is_array($value)) {
            $value = $value[0];
        }
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} = '{$value}'";
    }

    static private function no($column, $value, $alias = '')
    {
        if (is_array($value)) {
            $value = $value[0];
        }
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} = '{$value}'";
    }

    static private function neq($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} != '{$value}'";
    }

    static private function between($column, $value, $alias = '')
    {
        $c = explode('-', $column);
        if (count($c) > 1) {
            $field_column = self::getFieldColumn($c[1], $alias);
        } else {
            $field_column = self::getFieldColumn($column, $alias);
        }
        return "{$field_column} between '{$value[0]}' and '{$value[1]}'";
    }

    static private function gt($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} > '{$value}'";
    }

    static private function egt($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} >= '{$value}'";
    }

    static private function lt($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} < '{$value}'";
    }

    static private function elt($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} <= '{$value}'";
    }

    /**
     * 使用like代替ADB where = 'json字符串'查不出来的问题
     *
     * @param        $column
     * @param        $value
     * @param string $alias
     *
     * @return string
     */
    static private function likeInsteadEq($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} like '{$value}'";
    }

    /**
     * 使用like代替ADB where = 'json字符串'查不出来的问题
     *
     * @param        $column
     * @param        $value
     * @param string $alias
     *
     * @return string
     */
    static private function like($column, $value, $alias = '')
    {
        $field_column = self::getFieldColumn($column, $alias);
        return "{$field_column} like '%{$value}%'";
    }
}
