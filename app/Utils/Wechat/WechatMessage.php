<?php


namespace App\Utils\Wechat;


class WechatMessage
{
    static public function text($to_user, $from_user, $content)
    {
        $template = "
            <xml>
              <ToUserName><![CDATA[%s]]></ToUserName>
              <FromUserName><![CDATA[%s]]></FromUserName>
              <CreateTime>%s</CreateTime>
              <MsgType><![CDATA[%s]]></MsgType>
              <Content><![CDATA[%s]]></Content>
            </xml>
           ";
        return sprintf($template, $to_user, $from_user, time(), 'text', $content);
    }

    static public function video($to_user, $from_user, $media_id)
    {

        $template = "
            <xml>
              <ToUserName><![CDATA[%s]]></ToUserName>
              <FromUserName><![CDATA[%s]]></FromUserName>
              <CreateTime>%s</CreateTime>
              <MsgType><![CDATA[%s]]></MsgType>
              <Video>
                <MediaId><![CDATA[%s]]></MediaId>
                <Title><![CDATA[AI数据助手功能展示]]></Title>
                <Description><![CDATA[功能演示]]></Description>
               </Video>
            </xml>
           ";
        return sprintf($template, $to_user, $from_user, time(), 'video', $media_id);
    }
}