<?php

namespace App\Utils;

use App\Exception\AppException;
use DateTime;
use Exception;

class DateTool
{
    /**
     * 求两个日期之间相差的天数
     * @param $start_date
     * @param $end_date
     * @return int
     */
    static function diffDate($start_date, $end_date): int
    {
        if ($start_date > $end_date) {
            $start_time = strtotime($start_date);
            $end_time = strtotime($end_date);
        } else {
            $start_time = strtotime($end_date);
            $end_time = strtotime($start_date);
        }
        $diff = $start_time - $end_time;
        $day = $diff / 86400;

        return intval($day);
    }


    static function getDiffMonthNum($start_date, $end_date)
    {
        // 将字符串日期转换为 DateTime 对象，添加 `-01` 作为默认日
        $d1 = DateTime::createFromFormat('Y-m', $start_date);
        $d2 = DateTime::createFromFormat('Y-m', $end_date);

        // 检查日期格式是否正确
        if (!$d1 || !$d2) {
            return "Invalid date format. Please use YYYY-MM.";
        }

        // 计算两个日期的差异
        $diff = $d1->diff($d2);

        // 返回年份差异转换为月份加上月份差异的总月份数
        return ($diff->y * 12) + $diff->m;
    }

    /**
     * @param $start_month
     * @param $end_month
     * @return array
     */
    static function getMonthRangeDataByDateRange($start_month, $end_month): array
    {
        // 创建起始和结束的 DateTime 对象
        $start_date = DateTime::createFromFormat('Y-m', $start_month);
        $end_date = DateTime::createFromFormat('Y-m', $end_month);

        // 如果日期格式不正确，返回空数组
        if (!$start_date || !$end_date) {
            throw new AppException("日期格式不正确");
        }

        // 如果结束日期早于起始日期，返回空数组或错误信息
        if ($end_date < $start_date) {
            throw new AppException("结束日期不能早于起始日期");
        }

        $months = [];
        $currentDate = clone $start_date;

        // 循环从起始日期到结束日期
        while ($currentDate <= $end_date) {
            $months[] = $currentDate->format('Y-m');
            $currentDate->modify('+1 month');
        }

        return $months;

    }

    /**
     * 根据日期区间获取按月日期区间
     * @param $start_date
     * @param $end_date
     * @return array
     * @throws Exception
     */
    static function getMonthlyDateRangeByDateRange($start_date, $end_date): array
    {
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $end->modify('last day of this month'); // Move to the end of the month

        $result = [];

        while ($start <= $end) {
            $month_start = $start->format('Y-m-01');
            $month_end = $start->format('Y-m-t'); // 't' gives the last day of the month

            if (new DateTime($month_end) > $end) {
                $month_end = $end->format('Y-m-d');
            }

            $result[] = ['month_start' => $month_start, 'month_end' => $month_end];

            $start->modify('first day of next month');
        }

        return $result;
    }
}