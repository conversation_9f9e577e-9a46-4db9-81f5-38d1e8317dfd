<?php


namespace App\Utils;

use App\Container;
use App\Exception\AppException;
use Common\EnvConfig;
use DateInterval;
use DatePeriod;
use DateTime;
use DateTimeImmutable;
use FilesystemIterator;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use RecursiveIteratorIterator;
use RecursiveDirectoryIterator;

class Helpers
{
    const LOG_FORMAT = "[%datetime%] [%extra.wrole%-%extra.wid%] [%extra.uuid%] %channel%.%level_name%: %message% %context% %extra%\n";
    const OPERATION_LOG_FORMAT = "[%channel%] %message% %context%\n";
    const WEEK_CH = ['日', '一', '二', '三', '四', '五', '六'];

    static public function getClientIP($header)
    {
        if (isset($header['ali-cdn-real-ip'])) {
            return $header['ali-cdn-real-ip'];
        } elseif (isset($header['x-forwarded-for'])) {
            return explode(',', $header['x-forwarded-for'])[0];
        } elseif (isset($header['x-real-ip'])) {
            return $header['x-real-ip'];
        } else {
            return '0.0.0.0';
        }
    }

    static public function formatURL($api, array $data)
    {
        $url = $api;
        if (!empty($data)) {
            $param = http_build_query($data);
            $pos = strpos($api, '?');
            if ($pos > 0) {
                $url = $api . '&' . $param;
            } else {
                $url = $api . '?' . $param;
            }
        }
        return $url;
    }

    /**
     * 分离返回数据的header和body
     *
     * @param $response
     *
     * @return array
     */
    static public function parseResponseData($response)
    {
        $result = explode("\r\n\r\n", $response);
        if (count($result) < 2) {
            return [[], ''];
        } else if (count($result) == 3) {
            array_shift($result);
        } else if (count($result) == 4) {
            array_shift($result);
            array_shift($result);
        }

        [$header_str, $body_str] = $result;

        // 解析header
        $headers = [];
        $header_lines = explode("\r\n", $header_str);
        foreach ($header_lines as $line) {
            $kv = explode(':', $line, 2);
            if (count($kv) != 2) {
                continue;
            }
            $key = trim($kv[0]);
            $value = trim($kv[1]);
            $headers[$key] = $value;
        }

        return [$headers, $body_str];
    }

    static public function postCurl($url, $post_data, array $options = [])
    {
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, $options['return_with_header'] ?? 0);  //设置header
        if (isset($options['header'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $options['header']);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_POST, 1);  //post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout'] ?? 60);
        curl_setopt($ch, CURLOPT_CAINFO, EnvConfig::CURL_CERT_FILE_PATH);  //设置CA证书

        if ($options['curl_http_version'] ?? false) {
            curl_setopt($ch, CURLOPT_HTTP_VERSION, $options['curl_http_version']);
        }

        if (($options['proxy'] ?? false) === true) {
            if ($options['connect_abroad'] === true) {
                $rand_key = array_rand(EnvConfig::PROXY_CONNECT_ABROAD_IP_POOL);
                curl_setopt($ch, CURLOPT_PROXY, EnvConfig::PROXY_CONNECT_ABROAD_IP_POOL[$rand_key]);
            } else {
                $rand_key = array_rand(EnvConfig::PROXY_IP_POOL);
                curl_setopt($ch, CURLOPT_PROXY, EnvConfig::PROXY_IP_POOL[$rand_key]);
            }
        }
        $result = curl_exec($ch);  //运行curl

        curl_close($ch);

        return $result;
    }

    static public function putCurl($url, $post_data, array $options = [])
    {
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, $options['return_with_header'] ?? 0);  //设置header
        if (isset($options['header'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $options['header']);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout'] ?? 60);
        curl_setopt($ch, CURLOPT_CAINFO, EnvConfig::CURL_CERT_FILE_PATH);  //设置CA证书

        if (($options['proxy'] ?? false) === true) {
            if ($options['connect_abroad'] === true) {
                $rand_key = array_rand(EnvConfig::PROXY_CONNECT_ABROAD_IP_POOL);
                curl_setopt($ch, CURLOPT_PROXY, EnvConfig::PROXY_CONNECT_ABROAD_IP_POOL[$rand_key]);
            } else {
                $rand_key = array_rand(EnvConfig::PROXY_IP_POOL);
                curl_setopt($ch, CURLOPT_PROXY, EnvConfig::PROXY_IP_POOL[$rand_key]);
            }
        }
        $result = curl_exec($ch);  //运行curl
        curl_close($ch);

        return $result;
    }

    static public function getCurl($url, array $options = [])
    {
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, $options['return_with_header'] ?? 0);  //设置header
        if (isset($options['header'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $options['header']);
        }

        if (isset($options['user_pwd'])) {
            curl_setopt($ch, CURLOPT_USERPWD, $options['user_pwd']);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout'] ?? 5);
        curl_setopt($ch, CURLOPT_CAINFO, EnvConfig::CURL_CERT_FILE_PATH);  //设置CA证书
        if (($options['proxy'] ?? false) === true) {
            if ($options['connect_abroad'] === true) {
                $rand_key = array_rand(EnvConfig::PROXY_CONNECT_ABROAD_IP_POOL);
                curl_setopt($ch, CURLOPT_PROXY, EnvConfig::PROXY_CONNECT_ABROAD_IP_POOL[$rand_key]);
            } else {
                $rand_key = array_rand(EnvConfig::PROXY_IP_POOL);
                curl_setopt($ch, CURLOPT_PROXY, EnvConfig::PROXY_IP_POOL[$rand_key]);
            }
        }
        $result = curl_exec($ch);  //运行curl

        curl_close($ch);

        return $result;
    }

    static public function customCurl($method, $url, $data, $option = [])
    {
        $option['timeout'] = $option['timeout'] ?? 30;
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0);  //设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, $option['timeout']);
        $option['header'] && curl_setopt($ch, CURLOPT_HTTPHEADER, $option['header']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $result = curl_exec($ch);  //运行curl
        curl_close($ch);

        return $result;
    }


    static public function getHttpStatus($url, $options = [])
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url); //设置URL
        curl_setopt($curl, CURLOPT_HTTPHEADER, ["Cache-Control: no-cache"]);
        curl_setopt($curl, CURLOPT_HEADER, 1); //获取Header
        curl_setopt($curl, CURLOPT_NOBODY, true); //不要Body
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, $options['timeout'] ?? 5);
        curl_setopt($curl, CURLOPT_CAINFO, EnvConfig::CURL_CERT_FILE_PATH);  //设置CA证书
        if ($options['proxy'] === true) {
            //多代理多次请求
            if (!isset($options['multi_proxy']) || true !== $options['multi_proxy']) {
                $rand_key = array_rand(EnvConfig::PROXY_IP_POOL);
                curl_setopt($curl, CURLOPT_PROXY, EnvConfig::PROXY_IP_POOL[$rand_key]);
                curl_exec($curl);
                $return = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            } else {
                $return = [];
                foreach (EnvConfig::PROXY_IP_POOL as $proxy) {
                    curl_setopt($curl, CURLOPT_PROXY, $proxy);
                    curl_exec($curl);
                    $return[] = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                }
            }

        } else {
            curl_exec($curl); //开始执行啦～
            $return = curl_getinfo($curl, CURLINFO_HTTP_CODE); //HTTP STATUS码
        }

        curl_close($curl); //用完记得关掉他

        return $return;
    }

    static public function logProcessor($record)
    {
        $server = Container::getServer();
        $worker_id = empty($server->worker_id) ? 0 : $server->worker_id;
        $taskworker = empty($server) ? 'SCRIPT' : ($server->taskworker ? 'TASK' : 'WORKER');
        $record['extra']['wid'] = $worker_id;
        $record['extra']['uuid'] = uniqid();
        $record['extra']['wrole'] = $taskworker;
        return $record;
    }

    static public function getLogger($name = 'app')
    {
        $log = new Logger($name);
        $handler = new StreamHandler(LOG_DIR . '/' . date('Ymd') . '.log', Logger::INFO, true, 0666);
        $formatter = new LineFormatter(static::LOG_FORMAT, null, false, true);

        $handler->pushProcessor(array('\App\Utils\Helpers', "logProcessor"));
        $handler->setFormatter($formatter);
        $log->pushHandler($handler);
        return $log;
    }

    static public function getEsLogger($name = 'app')
    {
        $log = new Logger($name);
        $handler = new StreamHandler(LOG_DIR . '/' . date('Ymd') . '.log', Logger::INFO);
        $formatter = new LineFormatter(static::OPERATION_LOG_FORMAT, null, false, true);

        $handler->pushProcessor(array('\App\Utils\Helpers', "logProcessor"));
        $handler->setFormatter($formatter);
        $log->pushHandler($handler);
        return $log;
    }

    static public function getWXLogger($name = 'app')
    {
        $log = new Logger($name);
        $handler = new StreamHandler(LOG_DIR . '/' . date('Ymd') . '.log', Logger::INFO);
        $wx_handler = new WXAlertLogHandler();
        $formatter = new LineFormatter(static::LOG_FORMAT, null, false, true);

        $handler->pushProcessor(array('\App\Utils\Helpers', "logProcessor"));
        $wx_handler->pushProcessor(array('\App\Utils\Helpers', "logProcessor"));
        $handler->setFormatter($formatter);
        $wx_handler->setFormatter($formatter);
        $log->pushHandler($handler);
        $log->pushHandler($wx_handler);
        return $log;
    }


    /**
     * 给定一个字符串，判断是不是时间格式
     *
     * @param $datetime
     *
     * @return bool
     */
    static public function isDatetime($datetime)
    {
        $ret = strtotime($datetime);
        return $ret !== FALSE && $ret != -1;
    }

    /**
     * Generate an array of string dates between 2 dates
     *
     * @param string $start Start date
     * @param string $end End date
     * @param string $format Output format (Default: Y-m-d)
     *
     * @return array
     * @throws \Exception
     */
    static public function getDatesValueArrFromRange($start, $end, $format = 'Y-m-d')
    {
        $array = array();
        $interval = new DateInterval('P1D');

        $realEnd = new DateTime($end);
        $realEnd->add($interval);

        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);

        foreach ($period as $date) {
            $array[] = $date->format($format);
        }

        return $array;
    }

    /**
     * Generate an array of string dates between 2 dates
     *
     * @param string $start Start date
     * @param string $end End date
     * @param string $format Output format (Default: Y-m-d)
     *
     * @return array
     * @throws \Exception
     */
    static public function getDatesKeyArrFromRange($start, $end, $format = 'Y-m-d')
    {
        $array = array();
        $interval = new DateInterval('P1D');

        $realEnd = new DateTime($end);
        $realEnd->add($interval);

        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);

        foreach ($period as $date) {
            $array[$date->format($format)] = 0;
        }

        return $array;
    }

    /**
     * @param string $time
     *
     * @return string
     * @throws \Exception
     */
    static public function getSameDayOfPrevMonth($time = "now")
    {
        if ($time !== "now") {
            $date = new DateTimeImmutable($time);
        } else {
            $date = new DateTimeImmutable();
        }
        $previous = $date->sub(new DateInterval('P1M'));
        $lastMonth = $date->modify('last day of previous month');
        if ($previous > $lastMonth) {
            $previous = $lastMonth;
        }
        return $previous->format('Y-m-d');
    }

    /**
     * @param string $time
     *
     * @return string
     * @throws \Exception
     */
    static public function getSameDayOfNextMonth($time = "now")
    {
        if ($time !== "now") {
            $date = new DateTimeImmutable($time);
        } else {
            $date = new DateTimeImmutable();
        }
        $previous = $date->add(new DateInterval('P1M'));
        $lastMonth = $date->modify('last day of next month');
        if ($previous > $lastMonth) {
            $previous = $lastMonth;
        }
        return $previous->format('Y-m-d');
    }


    static public function multipleSort(&$list, $sorts, $sort_natural = false)
    {
        foreach ($sorts as $field => $order) {
            $arr[] = array_column($list, $field);
            $arr[] = $order;
            if ($sort_natural) {
                $arr[] = SORT_NATURAL;
            }
        }
        $arr[] = &$list;
        array_multisort(...$arr);
    }

    /**
     * @param     $bytes
     * @param int $precision
     *
     * @return string
     */
    static public function toSize($bytes, $precision = 2)
    {
        $rank = 0;
        $size = $bytes;
        $unit = "B";
        while ($size > 1024) {
            $size = $size / 1024;
            $rank++;
        }
        $size = round($size, $precision);
        switch ($rank) {
            case "1":
                $unit = "KB";
                break;
            case "2":
                $unit = "MB";
                break;
            case "3":
                $unit = "GB";
                break;
            case "4":
                $unit = "TB";
                break;
            default :

        }
        return $size . "" . $unit;
    }

    /**
     * 转驼峰
     *
     * @param        $uncamelized_words
     * @param string $separator
     *
     * @return mixed
     * <AUTHOR>
     *
     */
    static public function camelize($uncamelized_words, $separator = '_')
    {
        return str_replace($separator, '', lcfirst(ucwords(strtolower($uncamelized_words), $separator)));
    }

    /**
     * 转帕斯卡命名法
     *
     * @param        $unpascaled_words
     * @param string $separator
     *
     * @return mixed
     * <AUTHOR>
     *
     */
    static public function pascal($unpascaled_words, $separator = '_')
    {
        return str_replace($separator, '', ucfirst(ucwords(strtolower($unpascaled_words), $separator)));
    }

    /**
     * 清空文件夹，不删除根目录
     *
     * @param $rootPath
     */
    static public function cleanDir($rootPath)
    {
        $di = new RecursiveDirectoryIterator($rootPath, FilesystemIterator::SKIP_DOTS);
        $ri = new RecursiveIteratorIterator($di, RecursiveIteratorIterator::CHILD_FIRST);
        foreach ($ri as $file) {
            $file->isDir() ? rmdir($file->getRealPath()) : unlink($file->getRealPath());
        }
    }

    /**
     * 删除文件夹，该目录只能包含文件
     *
     * @param string $dirname
     *
     * @return boolean
     */
    static public function deleteDir($dirname)
    {
        if (!file_exists($dirname)) {
            return false;
        }
        array_map('unlink', glob("$dirname/*"));
        @rmdir($dirname);
        return true;
    }

    /**
     * 获取$row_limit行的文件内容
     *
     * @param resource $fp
     * @param int $row_limit
     *
     * @return string
     */
    static public function getFileContent($fp, $row_limit, $need_md5 = false)
    {
        $content = '';
        for ($i = 0; $i < $row_limit; ++$i) {
            $line = fgets($fp);
            if (empty($line)) {
                break;
            }
            if ($need_md5) {
                $line = trim($line);
                $line = md5($line) . PHP_EOL;
            }
            $content .= $line;
        }
        return $content;
    }

    /**
     * 检查指定字符串是否为日期格式 年-月(-日) | 年/月(/日) |
     *
     * @param string $date 日期字符串 年月
     *
     * @return bool  true 是日期格式     false 不是日期格式
     */
    static public function validDate($date)
    {
        //匹配日期格式
        if (preg_match("/^([0-9]{4})[-|\/]([0-9]{1,2})([-|\/]([0-9]{1,2}))?$/", $date, $parts)) {
            $date = str_replace('/', '-', $date);
            $date_arr = explode('-', $date);

            return checkdate($date_arr[1], $date_arr[2] ?? 1, $date_arr[0]);
        }

        return false;
    }


    /**
     * @param $date
     * @param $formats
     * @return bool
     */
    static function validDateFormat($date, $formats = ['Y-m-d'])
    {
        $unix_time = strtotime($date);
        if (!$unix_time) return false;

        foreach ($formats as $format) {
            if (date($format, $unix_time) == $date) {
                return true;
            }
        }
        return false;
    }


    /**
     *
     * @param int $start_date 某一周的开始时间 时间戳
     * @param int $end_date 某一周的结束时间 时间戳
     *
     * @return array
     */
    static function genWeekDateMap($start_date, $end_date)
    {
        $week_start = $start_date;
        $week_end = $start_date + 86400 * 6; // 加6天
        $map = [];
        while (true) {
            $year_week = date("oW", $week_end);
            $map[$year_week] = [date("Y-m-d", $week_start), date("Y-m-d", $week_end)];
            if ($week_end >= $end_date) break;

            $week_start = $week_end + 86400;
            $week_end += 86400 * 7;
        }

        return $map;
    }

    static function getDateByYearWeek($year_week_map, $year_week)
    {
        $res = '';
        $date_arr = $year_week_map[$year_week] ?? '';
        if ($date_arr) {
            $res = $date_arr[0] . '至' . $date_arr[1];
        }

        return $res;
    }

    static function mapWeek($week)
    {
        $map = [
            '1' => '一',
            '2' => '二',
            '3' => '三',
            '4' => '四',
            '5' => '五',
            '6' => '六',
            '0' => '日',
        ];
        return $map[$week] ?? '';
    }

    /**
     * 判断一个输入是否为整型数字，兼容字符串整型
     *
     * @param $str
     *
     * @return bool
     */
    static public function isInt($str)
    {
        if (is_string($str)) {
            return ctype_digit($str);
        } else {
            return is_int($str);
        }
    }


    /**
     * 阿拉伯数字转中文大写 不能超过一亿
     *
     * @param $num
     *
     * @return string
     */
    static public function moneyToString($num)
    {
        $c1 = "零壹贰叁肆伍陆柒捌玖";
        $c2 = "分角元拾佰仟万拾佰仟亿";
        // 精确到分后面就不要了，所以只留两个小数位
        $num = round($num, 2);

        // 将数字转化为整数
        $num = intval(round($num * 100));  // 使用round确保四舍五入到最近的整数
        if (strlen((string)$num) > 10) {
            return "金额太大，请检查";
        }

        $i = 0;
        $c = "";
        while (1) {
            $n = $num % 10;
            $p1 = substr($c1, 3 * $n, 3);
            $p2 = substr($c2, 3 * $i, 3);
            if ($n != 0 || ($n == 0 && ($p2 == '亿' || $p2 == '万' || $p2 == '元'))) {
                $c = $p1 . $p2 . $c;
            } else {
                if ($i > 0 && !preg_match('/零$/u', $c)) {
                    $c = '零' . $c;
                }
            }
            $i++;
            $num = intval($num / 10);
            if ($num == 0) {
                break;
            }
        }
        $c = preg_replace('/零(元|万|亿)/u', '\\1', $c);
        $c = preg_replace('/零+/u', '零', $c);
        $c = rtrim($c, '零');
        if (substr($c, -3) == '元' || substr($c, -3) == '角') {
            $c .= '整';
        }

        return $c;
    }


    /**
     * 仅判断中国大陆18位身份证号码格式
     *
     * @param $id
     *
     * @return bool
     */
    static public function idcardVerify($id = '')
    {
        if (!is_numeric(substr($id, 0, 3))) return true;
        $set = array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
        $ver = array('1', '0', 'x', '9', '8', '7', '6', '5', '4', '3', '2');
        $arr = str_split($id);
        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            if (!is_numeric($arr[$i])) {
                return false;
            }
            $sum += $arr[$i] * $set[$i];
        }
        $mod = $sum % 11;
        if (strcasecmp($ver[$mod], $arr[17]) != 0) {
            return false;
        }
        return true;
    }

    /**
     * 某天在N个月后的日期。若N个月不存在，则匹配下一天
     *
     * @param     $date
     * @param int $month
     *
     * @return false|string
     */
    static public function getNextMonthDate($date, $month = 1)
    {
        $cur_month_day = date('d', strtotime($date));
        $cur_month_first_date = date('Y-m-01', strtotime($date));
        $next_month_first_date = date('Y-m-01', strtotime("$cur_month_first_date +$month month"));

        $next_month_max_day = date('t', strtotime($next_month_first_date)); //获取下个月份的最后一天

        //29-31号可能在其他月份不存在，不存在则匹配下一天
        if ($cur_month_day == 29 && $next_month_max_day < 29) {
            $next_month_date = date('Y-m-d', strtotime(date('Y-m-t', strtotime($next_month_first_date))) + 86400);
        } elseif ($cur_month_day == 30 && $next_month_max_day < 30) {
            $next_month_date = date('Y-m-d', strtotime(date('Y-m-t', strtotime($next_month_first_date))) + 86400);
        } elseif ($cur_month_day == 31 && $next_month_max_day < 31) {
            $next_month_date = date('Y-m-d', strtotime(date('Y-m-t', strtotime($next_month_first_date))) + 86400);
        } else {
            $next_month_date = date('Y-m', strtotime($next_month_first_date)) . '-' . $cur_month_day;
        }
        return $next_month_date;
    }

    static public function transHeadersToStrList(array $headers)
    {
        $header = [];
        foreach ($headers as $key => $value) {
            $header[] = "{$key}: {$value}";
        }
        return $header;
    }

    /**
     * 格式化百度禁止投放时段 JSON转二进制
     *
     * @param $json_schedule_time
     *
     * @return string
     */
    public static function formatBaiduScheduleFromJsonToBinary($json_schedule_time)
    {
        $array_schedule_time = json_decode($json_schedule_time, true);
        if (!$array_schedule_time) {
            return '';
        }
        //一天的投放二进制数组
        $one_day_schedule = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
        //构建7x24的投放时段，之后进行禁止投放时段替换大法
        for ($i = 1; $i <= 7; $i++) {
            $all_schedule[$i] = $one_day_schedule;
        }

        foreach ($array_schedule_time as $key => $item) {
            //循环替换停止投放的时间
            for ($i = $item['endHour'] - 1; $i >= $item['startHour']; $i--) {
                $all_schedule[$item['weekDay']][$i] = 0;
            }
        }

        $binary_schedule_time = '';
        //组合二进制字符串
        foreach ($all_schedule as $schedule_time) {
            $binary_schedule_time .= implode('', $schedule_time);
        }

        return $binary_schedule_time;
    }

    /**
     * 格式化百度禁止投放时段 二进制转JSON
     *
     * @param $binary_schedule_time
     *
     * @return array
     */
    public static function formatBaiduScheduleFromBinaryToJson($binary_schedule_time)
    {
        $result = [];
        $stop_time = [];
        //按照每24个元素，把二进制分成7个数组，每个数组24个元素代表一天24小时
        foreach (str_split($binary_schedule_time, 24) as $day => $hours) {
            //遍历24小时
            $hours_arr = str_split($hours);
            foreach ($hours_arr as $k => $v) {
                $stop_time['weekDay'] = $day + 1;
                //0为停止投放，1为投放
                if (0 === (int)$v) {
                    if (isset($stop_time['startHour'])) {
                        $stop_time['endHour'] = $k + 1;
                    } else {
                        $stop_time['startHour'] = $k;
                        $stop_time['endHour'] = $k + 1;
                    }
                    //如果下一个小时是允许投放的（1）或 循环到今天最后一个小时，则将现有的$day放入结果集，清空$stop_time
                    if (23 === (int)$k || 1 === (int)$hours_arr[$k + 1]) {
                        $result[] = $stop_time;
                        $stop_time = [];
                    }
                }
            }
        }
        return $result;
    }

    /**
     * 获取广告投放时所用到的文案长度(中文算2个，其他算1个)
     *
     * @param string $str
     *
     * @return int
     */
    public static function ADServingStrLen(string $str)
    {
        return (int)((strlen($str) + mb_strlen($str)) / 2);
    }

    /**
     * 计算年龄
     *
     * @param $birth_date
     *
     * @return int|mixed|string
     */
    public static function getAge($birth_date)
    {
        $age = strtotime($birth_date);
        if ($age === false) {
            return 0;
        }
        list($y1, $m1, $d1) = explode("-", date("Y-m-d", $age));
        $now = strtotime("now");
        list($y2, $m2, $d2) = explode("-", date("Y-m-d", $now));
        $age = $y2 - $y1;
        if ((int)($m2 . $d2) < (int)($m1 . $d1))
            $age -= 1;
        return $age;
    }

    static public function getWeekCN($timestamp = 0)
    {
        if ($timestamp == 0) {
            $timestamp = time();
        }
        return self::WEEK_CH[date("w", $timestamp)];
    }


    static public function coPostCurl($url, $post_data, array $options = [])
    {
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, $options['return_with_header'] ?? 0);  //设置header
        if (isset($options['header'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $options['header']);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_POST, 1);  //post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout'] ?? 60);
        $result = curl_exec($ch);  //运行curl
        curl_close($ch);

        return $result;
    }

    static public function coGetCurl($url, array $options = [])
    {
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, $options['return_with_header'] ?? 0);  //设置header
        if (isset($options['header'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $options['header']);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout'] ?? 5);
        $result = curl_exec($ch);  //运行curl
        curl_close($ch);

        return $result;
    }

    static public function coCustomCurl($method, $url, $data, $option = [])
    {
        $option['timeout'] = $option['timeout'] ?? 30;
        $ch = curl_init();  //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url);  //抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0);  //设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);  //设置不输出直接返回字符串
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
        curl_setopt($ch, CURLOPT_TIMEOUT, $option['timeout']);
        $option['header'] && curl_setopt($ch, CURLOPT_HTTPHEADER, $option['header']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $result = curl_exec($ch);  //运行curl
        curl_close($ch);

        return $result;
    }

    /**
     * 调用jar解析apk获取信息
     *
     * @param $dir
     * @param $file
     * @param $file_name
     *
     * @return mixed
     */
    static public function parseAPK($dir, $file, $file_name)
    {
        //校验文件名是否包含空格，有空格会影响下面jar包命令的执行
        if (preg_match_all('/[!|@|◎|#|(\$)|%|(\^)|\[|\]|……|(\&)|※|(\*)|×|(\()|（|(\))|）|——|(\+)|＋|(\|)|§|=|<|<=|>|>=|<>|\?|\/|!=|^=|\s|]/', $file_name, $matches)) {
            $match = implode(' ', $matches[0]);
            throw new AppException("文件名包含特殊符号{$match}或空格，请去除后重试");
        }

        //母包解析app_name和app_package_name
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
        $tmp_file_path = $dir . '/' . $file_name;
        copy($file, $tmp_file_path);
        try {
            $apk_info = exec("/usr/bin/java -jar " . EnvConfig::APK_PARSER_FILE_PATH . ' ' . $tmp_file_path);
            $info_array = json_decode($apk_info, true);
            if (!isset($info_array['displayName']) || !isset($info_array['packageName'])) {
                throw new AppException();
            }
            unlink($tmp_file_path);
            return $info_array;
        } catch (AppException $e) {
            unlink($tmp_file_path);
            throw new AppException('apk解析错误，请重试');
        }

    }

    /**
     *
     * 取某个日期到现在时间的月份差值。如果不够一个月，按一个月算
     *
     * @param $date
     *
     * @return mixed
     */
    static public function diffMonth($date)
    {
        if (strtotime($date) < time()) {
            $start = $date;
            $end = date('Y-n-j');
        } else {
            $start = date('Y-n-j');
            $end = $date;
        }
        $star_y = date("Y", strtotime($start));
        $star_m = date("n", strtotime($start));
        $star_d = date("j", strtotime($start));

        $now_y = date("Y", strtotime($end));
        $now_m = date("n", strtotime($end));
        $now_d = date("j", strtotime($end));

        if ($star_y == $now_y) {
            if ($star_m == $now_m) {
                if ($star_d < $now_d) {
                    $diff_m = 1;
                } elseif ($star_d = $now_d) {
                    $diff_m = 0;
                } else {
                    $diff_m = false;
                }
            } elseif ($star_m < $now_m) {
                if ($star_d < $now_d) {
                    $diff_m = $now_m - $star_m + 1;
                } else {
                    $diff_m = $now_m - $star_m;
                }
            } else {
                $diff_m = false;
            }
        } elseif ($star_y < $now_y) {
            $diff_y = $now_y - $star_y;
            if ($star_d < $now_d) {
                $diff_m = (12 - $star_m + $now_m + 1) + 12 * ($diff_y - 1);
            } else {
                $diff_m = (12 - $star_m + $now_m) + 12 * ($diff_y - 1);
            }

        } else {
            $diff_m = false;
        }

        return $diff_m;
    }


    /**
     * 获取cookie过期时间戳，固定为第二天凌晨4点
     */
    static public function getCookieExpire()
    {
        $to_time = time() + 86400; // 确保足够24小时
        $four_hour = strtotime(date("Y-m-d 04:00:00", $to_time)); // 第二天的4点
        if ($four_hour < $to_time) {
            // 登录时间超过4点,过期时间应该为第二天四点
            return strtotime("+1 day", $four_hour);
        } else {
            // 登录时间小于等于4点 过期时间应该为四点
            return $four_hour;
        }
    }

    static public function filterArray($arr, $filter = [null, '', false, 0, '0', []])
    {
        foreach ($arr as $k => $v) {
            if (is_array($v) && count($v) > 0) {
                $arr[$k] = self::filterArray($v, $filter);
            }
            foreach ($filter as $value) {
                if ($v === $value) {
                    unset($arr[$k]);
                    break;
                }
            }
        }
        return $arr;
    }

    /**
     * 随机生成指定位数字符串
     *
     * @param $str_length
     *
     * @return string 生成的字符串
     */
    static public function getRandomStr($str_length)
    {
        $str = "";
        $str_pol = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
        $max = strlen($str_pol) - 1;
        for ($i = 0; $i < $str_length; $i++) {
            $str .= $str_pol[mt_rand(0, $max)];
        }
        return $str;
    }

    /**
     * 给出一个日期时间戳，判断这个日期是否在当前月里面
     */
    static public function inCurrentMonth($timestamp_given_date)
    {
        // 获取当前年份和月份
        $current_year = date('Y');
        $current_month = date('m');

        // 获取当前月份的第一天和最后一天
        $first_day_of_month = date('Y-m-01', strtotime("{$current_year}-{$current_month}-01"));
        $last_day_of_month = date('Y-m-t', strtotime("{$current_year}-{$current_month}-01"));


        // 将第一天和最后一天转换为时间戳
        $timestampFirstDay = strtotime($first_day_of_month);
        $timestampLastDay = strtotime($last_day_of_month);

        // 检查给定日期是否在当前月份内
        if ($timestamp_given_date >= $timestampFirstDay && $timestamp_given_date <= $timestampLastDay) {
            // 给定的日期在当前月份内;
            return true;
        } else {
            // 给定的日期不在当前月份内。
            return false;
        }
    }

    /**
     * @param $s_date
     * @param $e_date
     * @return false|float
     */
    public static function dateDiff($s_date, $e_date)
    {
        return floor((strtotime($e_date) - strtotime($s_date)) / (60 * 60 * 24));
    }

    /**
     * 检查一组时间段是否有交集[["08:00","09:00"],["09:00","19:00"]]
     * @param $intervals
     * @return bool
     */
    public static function hasOverlappingIntervals($intervals): bool
    {
        $convert_to_minutes = function ($time) {
            list($hours, $minutes) = explode(':', $time);
            return $hours * 60 + intval($minutes);
        };
        $converted_intervals = array_map(function ($interval) use ($convert_to_minutes) {
            return ['start' => $convert_to_minutes($interval[0]), 'end' => $convert_to_minutes($interval[1])];
        }, $intervals);
        usort($converted_intervals, function ($a, $b) {
            return $a['start'] <=> $b['start'];
        });
        for ($i = 1; $i < count($converted_intervals); $i++) {
            if ($converted_intervals[$i]['start'] < $converted_intervals[$i - 1]['end']) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查一组时间段是否有交集[["2025-01-03","2025-01-07"],["2025-01-06","2025-01-17"]]
     * @param $intervals
     * @return bool
     */
    public static function hasOverlappingDateTimes(array $intervals)
    {
        if (count($intervals) < 2) return false;

        $ts = [];
        foreach ($intervals as $i) {
            $ts[] = array_map('strtotime', $i);
        }
        usort($ts, function($a, $b) {
            return $a[0] <=> $b[0];
        });
        for ($i = 1; $i < count($ts); $i++) {
            if ($ts[$i][0] < $ts[$i - 1][1]) {
                return true;
            }
        }
        return false;
    }

    static public function imageToBase64($image_path)
    {
        // 检查文件是否存在
        if (!file_exists($image_path)) {
            return false;
        }

        // 获取文件内容
        $image_data = file_get_contents($image_path);

        // 将文件内容编码为 Base64
        $base64 = base64_encode($image_data);

        // 获取文件的 MIME 类型
        $mime_type = mime_content_type($image_path);

        // 返回带有 MIME 类型的 Base64 字符串
        return 'data:' . $mime_type . ';base64,' . $base64;
    }


    /**
     * 组装特定请求参数  一个key名多个不同参数值
     * user_ids=1&user_ids=2&user_ids=3 ....
     *
     * @param $key
     * @param array $data
     * @return string
     */
    static public function assembleSpecificUrlParam($key, array $data): string
    {
        $param = "?";
        foreach ($data as $value) {
            $param .= $key . "=" . $value . "&";
        }
        return substr($param, 0, -1);
    }


    /**
     * 粗略的估算token数量
     *
     * @param $text
     * @return float
     */
    static public function estimateTokenCount($text)
    {
        // 计算中文字符数量（包含中文标点）
        preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $text, $matches);
        $chineseChars = count($matches[0]);

        // 计算总字符数
        $totalChars = mb_strlen($text, 'UTF-8');

        // 计算非中文字符数量
        $nonChineseChars = $totalChars - $chineseChars;

        // 计算token总数
        $tokens = ($chineseChars * 0.6) + ($nonChineseChars * 0.3);

        return round($tokens); // 四舍五入
    }

    /**
     * @throws \Exception
     */
    static public function getDatesBetween($start_date, $end_date)
    {
        $dates = [];
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $end = $end->modify('+1 day'); // 包含结束日期

        $interval = new DateInterval('P1D');
        $period = new DatePeriod($start, $interval, $end);

        foreach ($period as $date) {
            $dates[] = $date->format('Y-m-d');
        }

        return $dates;
    }

    static public function deleteDirectory($dir)
    {
        // 解压完成后清理
        if (is_dir($dir)) {
            // 递归删除文件夹
            $files = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, FilesystemIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            foreach ($files as $file) {
                if ($file->isDir()) {
                    rmdir($file->getRealPath());
                } else {
                    @unlink($file->getRealPath());
                }
            }
            rmdir($dir);
        }
    }

    /**
     * 去除docx文档的水印
     * @param $file_path
     * @return void
     */
    static public function removeWatermarks($file_path)
    {
        // 调用的py文件路径
        $python_path = SCRIPT_DIR . "/py/removeWatermarks.py";

        $python_bin_path = EnvConfig::PY_BIN_PATH;

        // 输入输出是同一份文件
        $command = "$python_bin_path {$python_path} '{$file_path}' '{$file_path}' 2>&1";

        Helpers::getLogger('remove_watermarks')->info("开始执行命令", ['command' => $command]);
        exec($command, $out_array, $result_code);
        // 执行异常的任务跳过
        if ($result_code !== 0) {
            Helpers::getLogger('remove_watermarks')->error("py命令执行异常。。", ['out_array' => $out_array, 'command' => $command]);
        }
    }
}
