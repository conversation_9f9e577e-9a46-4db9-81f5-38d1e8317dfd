<?php
/**
 * Created by PhpStorm.
 * User: zhenorzz
 * Date: 2019/7/26
 * Time: 14:38
 */

namespace App\Utils;


use App\Constant\PlatId;
use App\Model\SqlModel\Zeda\MaterialLabelModel;
use App\Model\SqlModel\Zeda\MaterialThemeModel;
use Common\EnvConfig;

final class SqlParser
{
    /**
     * 挑选出某些需要的column
     * @param array $filters
     * @param array $columns [filter_field_name => target_table_field_name]
     * @param string $alias
     * @return array
     */
    static public function pick(array $filters, array $columns, string $alias = ''): array
    {
        $ret = [];
        foreach ($filters as $filter) {
            if (empty($filter['value'])) {
                continue;
            }
            if (isset($columns[$filter['column']])) {
                $ret[] = self::get($filter, $columns[$filter['column']], $alias);
            }
        }
        return $ret;
    }

    /**
     * @param        $filter
     * @param string $rename_column 可以将查询的字段换名字
     * @param string $alias
     *
     * @return mixed
     * <AUTHOR>
     */
    static public function get($filter, $rename_column = '', $alias = '')
    {
        $method = Helpers::camelize($filter['condition']);
        $format = self::$method(!empty($rename_column) ? $rename_column : $filter['column'], $filter['value'], $alias);
        return $format;
    }

    static private function in($column, $values, $alias)
    {
        $c = explode('-', $column);
        if (count($c) > 1) {
            $data = [];
            foreach ($values as &$value) {
                if (in_array($c[1], ['agent_leader', 'apple_ppid'], true)) {
                    $v = explode('-', $value, 2);
                } else {
                    $v = explode('-', $value);
                }
                $platform = array_search($v[0], EnvConfig::PLATFORM_MAP);
                if (!isset($data[$platform])) {
                    $data[$platform] = [];
                }
                $data[$platform][] = $v[1];
            }
            $platform_column = $alias ? "{$alias}.{$c[0]}" : $c[0];
            $field_column = $alias ? "{$alias}.{$c[1]}" : $c[1];
            $condition = '(';
            $values = [];
            foreach ($data as $platform => $bind) {
                $platform = strval($platform);
                // 这个判断是混服专用
                if ($c[1] === 'theme_pid') {
                    $field_column = $alias ? "{$alias}.theme_id" : 'theme_id';
                    $condition .= "({$platform_column} = '{$platform}' AND (";
                    foreach ($bind as $theme_pid) {
                        $condition .= "({$field_column} BETWEEN ? AND ?) OR ";
                        $values = array_merge($values, MaterialThemeModel::subThemeRange($theme_pid));
                    }
                    $condition = substr($condition, 0, -3) . ')) OR ';
                } else {
                    if (($c[1] === 'main_game_id' || $c[1] === 'game_id') && count($bind) === 1 && $bind[0] >= 1000000) {
                        $field_column = 'mix.mix_id';
                    }
                    $condition .= rtrim("({$platform_column} = '{$platform}' AND {$field_column} IN (" . str_repeat('?,', count($bind)), ',') . ')) OR ';
                    $values = array_merge($values, $bind);
                }
            }
            $condition = substr($condition, 0, -3) . ')';

        } else {
            $field_column = $alias ? "{$alias}.{$column}" : $column;
            if ($column === 'label_pid') {
                $field_column = $alias ? "{$alias}.label_id" : 'label_id';
                $condition = "(";
                $new_values = [];
                foreach ($values as $value) {
                    $condition .= "({$field_column} BETWEEN ? AND ?) OR ";
                    $new_values = array_merge($new_values, MaterialLabelModel::subLabelRange($value));
                }
                $values = $new_values;
                $condition = substr($condition, 0, -3) . ')';
            } else {
                if ($column === 'platform') {
                    foreach ($values as &$value) {
                        $value = strval(array_search($value, EnvConfig::PLATFORM_MAP));
                    }
                } else if ($column === 'plat_id') {
                    foreach ($values as &$value) {
                        $value = array_search($value, PlatId::MAP);
                    }
                } else if ($column === 'project_team_id') {
                    foreach ($values as &$value) {
                        $value = explode('-', $value)[0];
                    }
                }
                $condition = rtrim("{$field_column} IN (" . str_repeat('?,', count($values)), ',') . ')';
            }

            // 负责人组的特殊处理
            if (in_array($column, ['interface_person_group_name', 'agent_leader_group_id'])) {
                if ($column == 'agent_leader_group_id') {
                    $condition = rtrim('IFNULL(' . $field_column . ', 0) IN (' . str_repeat('?,', count($values)), ',') . ')';
                } else {
                    $condition = rtrim('IFNULL(' . $field_column . ', "未分组") IN (' . str_repeat('?,', count($values)), ',') . ')';
                }
            }
        }
        return [$condition, $values];
    }

    static private function notIn($column, $values, $alias)
    {
        $c = explode('-', $column);
        if (count($c) > 1) {
            $data = [];
            foreach ($values as &$value) {
                if (in_array($c[1], ['agent_leader', 'apple_ppid'], true)) {
                    $v = explode('-', $value, 2);
                } else {
                    $v = explode('-', $value);
                }
                $platform = array_search($v[0], EnvConfig::PLATFORM_MAP);
                if (!isset($data[$platform])) {
                    $data[$platform] = [];
                }
                $data[$platform][] = Helpers::isInt($v[1]) ? (int)$v[1] : $v[1];
            }
            $platform_column = $alias ? "$alias.$c[0]" : $c[0];
            $field_column = $alias ? "$alias.$c[1]" : $c[1];
            $condition = '';
            $values = [];
            foreach ($data as $platform => $bind) {
                $platform = strval($platform);
                $condition .= str_repeat("('$platform',?),", count($bind));
                $values = array_merge($values, $bind);
            }

            $condition = "($platform_column, $field_column) NOT IN ( " . rtrim($condition, ',') . ')';
        } else {
            $field_column = $alias ? "{$alias}.{$column}" : $column;
            if ($column === 'platform') {
                foreach ($values as &$value) {
                    $value = strval(array_search($value, EnvConfig::PLATFORM_MAP));
                }
            } else if ($column === 'plat_id') {
                foreach ($values as &$value) {
                    $value = array_search($value, PlatId::MAP);
                }
            }
            $condition = rtrim("{$field_column} NOT IN (" . str_repeat('?,', count($values)), ',') . ')';

            // 负责人组的特殊处理
            if (in_array($column, ['interface_person_group_name', 'agent_leader_group_id'])) {
                if ($column == 'agent_leader_group_id') {
                    $condition = rtrim('IFNULL(' . $field_column . ', 0) NOT IN (' . str_repeat('?,', count($values)), ',') . ')';
                } else {
                    $condition = rtrim('IFNULL(' . $field_column . ', "未分组") NOT IN (' . str_repeat('?,', count($values)), ',') . ')';
                }
            }
        }

        return [$condition, $values];
    }

    static private function eq($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} = ?", $value];
        } else {
            return ["$column = ?", $value];
        }
    }

    static private function yes($column, $value, $alias = '')
    {
        if (is_array($value)) {
            $value = $value[0];
        }
        if ($alias) {
            return ["{$alias}.{$column} = ?", $value];
        } else {
            return ["$column = ?", $value];
        }
    }

    static private function no($column, $value, $alias = '')
    {
        if (is_array($value)) {
            $value = $value[0];
        }
        if ($alias) {
            return ["{$alias}.{$column} = ?", $value];
        } else {
            return ["$column = ?", $value];
        }
    }

    static private function neq($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} != ?", $value];
        } else {
            return ["$column != ?", $value];
        }
    }

    static private function between($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} between ? and ?", $value];
        } else {
            return ["{$column} between ? and ?", $value];
        }

    }

    static private function gt($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} > ?", $value];
        } else {
            return ["$column > ?", $value];
        }

    }

    static private function egt($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} >= ?", $value];
        } else {
            return ["$column >= ?", $value];
        }

    }

    static private function lt($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} < ?", $value];
        } else {
            return ["$column < ?", $value];
        }

    }

    static private function elt($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} <= ?", $value];
        } else {
            return ["$column <= ?", $value];
        }

    }

    /**
     * 使用like代替ADB where = 'json字符串'查不出来的问题
     *
     * @param        $column
     * @param        $value
     * @param string $alias
     *
     * @return array
     */
    static private function likeInsteadEq($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} like ?", $value];
        } else {
            return ["$column like ?", $value];
        }
    }

    /**
     * 使用like代替ADB where = 'json字符串'查不出来的问题
     *
     * @param        $column
     * @param        $value
     * @param string $alias
     *
     * @return array
     */
    static private function like($column, $value, $alias = '')
    {
        if ($alias) {
            return ["{$alias}.{$column} like ?", "%{$value}%"];
        } else {
            return ["$column like ?", "%{$value}%"];
        }
    }
}
