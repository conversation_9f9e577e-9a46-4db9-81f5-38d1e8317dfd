<?php
/**
 * 维度、维度筛选工具类
 */

namespace App\Utils;


use App\Exception\AppException;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Param\LY\OverviewListFilterParam;
use Common\EnvConfig;

class DimensionTool
{


    /**
     * 处理维度筛选，按表归类
     *
     * @param array $dimension_filter
     *
     * @return array
     */
    static public function handleDimensionFilter(array $dimension_filter, $apportion_type = 0)
    {
        $main = $site_id = $agent_id = $game_id = $ori_server_id =
        $leader_group = $is_cost = $device = $settle_company =
        $is_root_reg = $csite = $user_group = $acc = $live_room = $ods =
        $interface_person_group = $cp = $cost_settle_company = $ad_slot = $team = $game_dimension = $project_team = [];
        foreach ($dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value'] || $key === 'selected') {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'campaign_id':
                case 'adgroup_id':
                case 'adcre_id':
                case 'system_version':
                case 'is_simulator':
                case 'ctype':
                    $main[] = $dimension; // 由于各个主表的 alias都不一样，所以这里主表不处理，在model里面处理
                    break;
                case 'site':
                case 'apple_ppid':
                    $site_id[] = SqlParser::get($dimension, '', 'site');
                    break;
                case 'agent_leader':
                case 'ad_platform_name':
                case 'agent_group_name':
                case 'agent':
                case 'agent_account_id':
                case 'proxy_type':
                case 'channel':
                case 'media_type_id':
                    if ($key == 'proxy_type') {
                        // 任务《后台“买量/发行”维度逻辑迭代，兼容GR》by仇哥
                        $agent_id[] = SqlParser::get($dimension, "IF( game.platform = 'GR', game.is_channel+1, agent.proxy_type )", '');
                    } else {
                        $agent_id[] = SqlParser::get($dimension, '', 'agent');
                    }
                    break;
                case 'agent_leader_group_id':
                    $leader_group[] = SqlParser::get($dimension, '', 'alg');
                    break;
                case 'clique_id':
                case 'root_game':
                case 'main_game':
                case 'game':
                case 'plat_id':
                case 'os':
                case 'contract_game_name':
                    $game_id[] = SqlParser::get($dimension, '', 'game');
                    break;
                case 'ori_server_id':
                case 'game_version':
                    $ori_server_id[] = SqlParser::get($dimension, '', 'ser');
                    break;
                case 'create_type':
                case 'port_version':
                case 'deep_bid_type':
                case 'agency_full_name':
                case 'agency_short_name':
                case 'is_mix_os':
                    $acc[] = SqlParser::get($dimension, '', 'acc');
                    break;
                case 'convert_type':
                    if ($apportion_type === 1) {
                        $live_room[$key] = $dimension;
                    } else {
                        $acc[] = SqlParser::get($dimension, '', 'acc');
                    }
                    break;
                case 'account_id':
                    $acc[] = SqlParser::get($dimension, 'true_account_id', 'acc');
                    break;
                case 'is_cost': // is_cost直接拿原始值 比较特殊
                    $is_cost[] = $dimension['value'];
                    break;
                case 'device_name':
                case 'device_price_section':
                    $device[] = SqlParser::get($dimension, '', 'device');
                    break;
                case 'settle_company_name':
                    $settle_company[] = SqlParser::get($dimension, '', 'sc');
                    break;
                case 'cost_settle_company_name':
                    $cost_settle_company[] = SqlParser::get($dimension, '', 'acc_change');
                    break;
                case 'csite':
                    $csite[] = SqlParser::get($dimension, '', 'cs');
                    break;
                case 'is_root_reg': // 直接拿原始值 比较特殊
                    $is_root_reg[] = $dimension['value'];
                    break;
                case 'user_group': // 直接拿原始值
                    $user_group = $dimension;
                    break;
                case 'aweme_account':
                case 'aweme_account_type':
                case 'interface_person':
                    $live_room[$key] = $dimension;
                    break;
                case 'interface_person_group_name':
                    $interface_person_group[] = SqlParser::get($dimension, '', 'ipg');
                    break;
                case 'company_name' :
                    $cp[] = SqlParser::get($dimension, '', 'cp');;
                    break;
                case 'ad_slot_id':
                case 'company':
                case 'mini_program':
                    $ad_slot[] = SqlParser::get($dimension);;
                    break;
                case 'team_config_name':
                    $team[$key] = $dimension;
                    break;
                case 'ods_company_name':
                    $ods[] = SqlParser::get($dimension, 'company_name', 'cp_oaal');
                    break;
                case 'is_old_game':
                    $game_dimension[] = SqlParser::get($dimension, '', 'game_dimension');
                    break;
                case 'project_team_id':
                    $project_team[] = SqlParser::get($dimension, '', 'alg');
                    break;
            }
        }

        return [
            'main'                   => $main,
            'game_id'                => $game_id,
            'agent_id'               => $agent_id,
            'cost_settle_company'    => $cost_settle_company,
            'site_id'                => $site_id,
            'ori_server_id'          => $ori_server_id,
            'leader_group'           => $leader_group,
            'is_cost'                => $is_cost,
            'device'                 => $device,
            'settle_company'         => $settle_company,
            'is_root_reg'            => $is_root_reg,
            'csite'                  => $csite,
            'live_room'              => $live_room,
            'user_group'             => $user_group,
            'acc'                    => $acc,
            'interface_person_group' => $interface_person_group,
            'cp'                     => $cp,
            'ad_slot'                => $ad_slot,
            'ods'                    => $ods,
            'team'                   => $team,
            'game_dimension'         => $game_dimension,
            'project_team'           => $project_team,
        ];

    }


    /**
     * 处理维度聚合分组，按表归类
     *
     * @param $dimension
     *
     * @return array
     */
    static public function handleDimension(&$dimension)
    {
        $main = $agent_id = $game_id = $site_id = $leader_group = $ext = $device =
        $is_root_reg = $ori_server_id = $settle_company = $csite = $acc =
        $interface_person_group = $company = $cost_settle_company = $ad_slot = $team = $game_dimension = $project_team = [];
        foreach ($dimension as $key => $item) {
            switch ($item) {
                case 'platform':
                case 'system_version':
                case 'is_simulator':
                case 'ctype':
                    $main[] = $item;
                    break;
                case 'account_id':
                case 'adgroup_id':
                case 'campaign_id':
                case 'adcre_id':
                case 'aweme_account':
                case 'interface_person':
                case 'create_type':
                case 'deep_bid_type':
                case 'port_version':
                case 'is_mix_os':
                case 'convert_type':
                case 'agency_full_name':
                case 'agency_short_name':
                case 'aweme_account_type':
                    $acc[] = $item;
                    break;
                case 'group_belong':
                    $main[] = 'is_old_clique_game_muid';
                    $main[] = 'is_old_clique_pay_muid';
                    $dimension[] = 'is_old_clique_game_muid';
                    $dimension[] = 'is_old_clique_pay_muid';
                    unset($dimension[$key]);
                    break;
                case 'platform_belong':
                    $main[] = 'is_old_root_game_muid';
                    $main[] = 'is_old_root_pay_muid';
                    $dimension[] = 'is_old_root_game_muid';
                    $dimension[] = 'is_old_root_pay_muid';
                    unset($dimension[$key]);
                    break;
                case 'site_id':
                case 'apple_ppid':
                    $site_id[] = $item;
                    break;
                case 'agent_leader':
                case 'agent_group_id':
                case 'agent_id':
                case 'agent_account_id':
                case 'proxy_type':
                case 'channel_id':
                case 'media_type_id':
                    $agent_id[] = $item;
                    break;
                case 'root_game_id':
                case 'clique_id':
                case 'game_id':
                case 'main_game_id':
                case 'plat_id':
                case 'os':
                case 'contract_game_name':
                    $game_id[] = $item;
                    break;
                case 'agent_leader_group_id':
                    $leader_group[] = $item;
                    break;
                case 'device_name':
                case 'device_price_section':
                    $device[] = $item;
                    break;
                case 'cost_settle_company_name':
                    $cost_settle_company[] = $item;
                    break;
                case 'ori_server_id':
                case 'game_version':
                    $ori_server_id[] = $item;
                    break;
                case 'settle_company_name':
                    $settle_company[] = $item;
                    break;
                case 'company_name':
                    $company[] = $item;
                    break;
                case 'is_root_reg':
                    $is_root_reg[] = $item;
                    break;
                case 'csite':
                    $csite[] = $item;
                    break;
                case 'interface_person_group_name':
                    $interface_person_group[] = $item;
                    break;
                case 'ad_slot_id':
                case 'company':
                case 'mini_program':
                    $ad_slot[] = $item;
                    break;
                case 'team_config_name':
                    $team[] = $item;
                    break;
                case 'is_old_game':
                    $game_dimension[] = $item;
                    break;
                case 'project_team_id':
                    $project_team[] = $item;
                    break;
                default:
                    $ext[] = $item;
                    break;
            }
        }

        return [
            'main'                   => $main,
            'agent_id'               => $agent_id,
            'game_id'                => $game_id,
            'site_id'                => $site_id,
            'acc'                    => $acc,
            'leader_group'           => $leader_group,
            'device'                 => $device,
            'ori_server_id'          => $ori_server_id,
            'settle_company'         => $settle_company,
            'cost_settle_company'    => $cost_settle_company,
            'is_root_reg'            => $is_root_reg,
            'csite'                  => $csite,
            'interface_person_group' => $interface_person_group,
            'ext'                    => $ext,
            'company'                => $company,
            'ad_slot'                => $ad_slot,
            'team'                   => $team,
            'game_dimension'         => $game_dimension,
            'project_team'           => $project_team,
        ];
    }

    /**
     * 处理维度聚合分组，按表归类
     *
     * @param $dimension
     *
     * @return array
     */
    static public function handleLYDimension($dimension)
    {
        // 维度分两个表 主表、 site表
        $main = $site = $device = [];
        foreach ($dimension as $item) {
            switch ($item) {
                case 'system_version':
                case 'subsite_name':
                    $main[] = $item;
                    break;
                case 'device_name':
                case 'device_price_section':
                    $device[] = $item;
                    break;
                default:
                    $site[] = $item;
                    break;
            }
        }

        return [
            'main'   => $main,
            'site'   => $site,
            'device' => $device,
        ];
    }


    /**
     * 处理维度筛选，按表归类
     *
     * @param array $dimension_filter
     *
     * @return array
     */
    static public function handleLYDimensionFilter(array $dimension_filter)
    {
        $main = $site = $ori_server_id = $device = [];
        foreach ($dimension_filter as $key => $dimension) {
            // 过滤空value
            if (!$dimension['value'] || $key === 'selected') {
                continue;
            }
            if ($key === 'ori_server_id') {
                $ori_server_id[] = SqlParser::get($dimension, '', 'ser');
                continue;
            }
            if ($key === 'device_name' || $key === 'device_price_section') {
                $device[] = SqlParser::get($dimension, '', 'device');
                continue;
            }
            if (in_array($key, ['system_version', 'subsite_name'])) {
                $main[] = SqlParser::get($dimension);
            } else {
                $site[] = SqlParser::get($dimension, '', 'site');
            }

        }

        return [
            'main'          => $main,
            'site'          => $site,
            'ori_server_id' => $ori_server_id,
            'device'        => $device,
        ];
    }

    /**
     * 维度筛选的游戏权限处理
     *
     * @param $filter
     *
     * @param $alias
     *
     * @return array
     */
    static public function getPlatformFilterOptions($filter, $alias = '')
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!isset($column['value']) || !$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                    $options[] = SqlParser::get($column, '', $alias);
                    break;
            }
        }

        return $options;
    }

    /**
     * 维度筛选的游戏权限处理
     *
     * @param $filter
     *
     * @param $alias
     *
     * @return array
     */
    static public function getMediaTypeFilterOptions($filter, $alias = '')
    {
        $options = [];
        foreach ($filter as $key => $column) {
            switch ($key) {
                case 'platform':
                case 'media_type':
                    $options[] = SqlParser::get($column, '', $alias);
                    break;
            }
        }

        return $options;
    }

    /**
     * 维度筛选的游戏权限处理
     *
     * @param      $filter
     * @param bool $alias
     *
     * @return array
     */
    static public function getGameFilterOptions($filter, $alias = false)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!is_numeric($column['value']) && !$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'clique_id':
                case 'root_game':
                case 'main_game':
                case 'game':
                case 'os':
                case 'plat_id':
                case 'app_id':
                case 'team':
                    $options[] = $alias ? SqlParser::get($column, '', $alias) : SqlParser::get($column);
                    break;
                case 'root_game_id':
                case 'main_game_id':
                case 'game_id':
                    $column['column'] = "platform-$key";
                    $options[] = $alias ? SqlParser::get($column, '', $alias) : SqlParser::get($column);
                    break;
            }
        }

        return $options;
    }

    static public function getPlatformFilterArray($filter)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!is_numeric($column['value']) && !$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                    $options = $column['value'];
                    break;
            }
        }
        $map = array_flip(EnvConfig::PLATFORM_MAP);
        foreach ($options as $key => $platform_name) {
            $options[$key] = $map[$platform_name];
        }

        return $options;
    }

    /**
     *
     * @param $filter
     *
     * @return array
     */
    static public function getServerFilterOptions($filter)
    {
        // 先处理root_game, server不存在root_game_id数据，把root_game包含的main_game_id保存到main_game filter里面
        if (!empty($filter['root_game'])) {
            $root_option = SqlParser::get($filter['root_game'], '', 'game');
            $main_game = (new V2DimGameIdModel())->getListLikeMainGame('', [$root_option]);
            if (empty($filter['main_game'])) {
                $filter['main_game']['column'] = 'platform-main_game_id';
                $filter['main_game']['type'] = 'string';
                $filter['main_game']['condition'] = 'in';
            }
            $values = [];
            foreach ($main_game as $item) {
                $values[] = EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->main_game_id}-{$item->main_game_name}";
            }
            if (isset($filter['main_game']['value']) && $filter['main_game']['value']) {
                $filter['main_game']['value'] = array_intersect($filter['main_game']['value'], $values);
            } else {
                $filter['main_game']['value'] = $values;
            }
        }
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'main_game':
                    $options[] = SqlParser::get($column);
                    break;
            }
        }

        return $options;
    }

    /**
     * 维度筛选的site权限处理
     *
     * @param $filter
     *
     * @return array
     */
    static public function getSiteFilterOptions($filter)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'agent_leader':
                case 'agent_group_name':
                case 'agent':
                case 'site':
                case 'apple_ppid':
                case 'proxy_type':
                    $options[] = SqlParser::get($column);
                    break;
            }
        }
        return $options;
    }

    /**
     * 维度筛选的site权限处理
     *
     * @param $filter
     *
     * @return array
     */
    static public function getChannelFilterOptions($filter)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'main_game':
                case 'root_game':
                case 'game':
                case 'agent_group_name':
                case 'site':
                case 'agent':
                case 'channel':
                    $options[] = SqlParser::get($column);
                    break;
            }
        }
        return $options;
    }

    /**
     * 维度筛选的游戏权限处理
     *
     * @param $filter
     *
     * @param $alias
     *
     * @return array
     */
    static public function getSubThemeFilterOptions($filter, $alias = '')
    {
        $options = [];
        foreach ($filter as $key => $column) {
            switch ($key) {
                case 'platform':
                    $options[] = SqlParser::get($column, '', $alias);
                    break;
                case 'theme_pid':
                    $options[] = SqlParser::get($column, 'platform-theme_pid', $alias);
                    break;
            }
        }

        return $options;
    }

    /**
     * 维度筛选的游戏权限处理
     *
     * @param      $filter
     * @param bool $alias
     *
     * @return array
     */
    static public function getADAccontFilterOptions($filter, $alias = false)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!isset($column['value']) || !$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'campaign_id':
                case 'adgroup_id':
                case 'adcre_id':
                case 'account_id':
                case 'site':
                    $options[] = $alias ? SqlParser::get($column, '', $alias) : SqlParser::get($column);
                    break;
                case 'agent_leader':
                case 'agent_group_name':
                case 'agent':
                    $options[] = SqlParser::get($column, '', 'agent');
            }
        }

        return $options;
    }

    /**
     * 判断是否需要插入platform到维度里面去
     *
     * @param array $dimension 选中的维度
     *
     * @return bool
     */
    static public function needPushPlatform($dimension)
    {
        // 没有任何维度的时候 不需要插入
        if (empty($dimension)) {
            return false;
        }
        // 已经有platform就不需要插入了
        if (in_array('platform', $dimension)) {
            return false;
        }

        // 只出现以下几个维度的时候 也不需要自动插入platform 注意是 "只出现" ！
        $diff = array_diff($dimension, [
            'is_simulator', 'os', 'device_name', 'system_version', 'device_price_section', 'agent_leader_group_name',
            'agent_leader', 'clique_id', 'create_type', 'interface_person', 'aweme_account', 'interface_person_group_name'
        ]);
        if (empty($diff)) {
            return false;
        }


        return true;
    }

    /**
     *
     * @param $filter
     *
     * @return array
     */
    static public function getLYServerFilterOptions($filter)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                case 'game':
                case 'agent':
                    $options[] = SqlParser::get($column);
                    break;
            }
        }

        return $options;
    }

    /**
     * 给出指定的集合数组，按给定的按维度分行
     *
     * @param mixed $list
     * @param array $dimensions
     *
     * @return array
     */
    static public function groupByDimension($list, $dimensions)
    {
        $res_list = [];
        foreach ($list as $item) {
            $key = 'p|';
            foreach ($dimensions as $dimension) {
                if (is_array($item)) {
                    $key .= $item[$dimension] . '|';
                } else {
                    $key .= $item->$dimension . '|';
                }
            }
            $res_list[$key][] = $item;
        }

        return $res_list;
    }

    /**
     * 区服维度检查
     *
     * @param $dimensions
     * @param $filter
     */
    static public function checkServerDimension($dimensions, $filter)
    {
        // 区服维度必须包含三个 1.平台 2. 主游戏 3.区服id ori_server_id
        $res = array_intersect(['platform', 'main_game_id', 'ori_server_id'], $dimensions);
        if (count($res) !== 3) {
            throw new AppException('区服缺少必要的维度');
        }

        // 不支持的维度
        $no_support_dimension = ['game_id', 'is_simulator', 'group_belong', 'platform_belong'];
        if (array_intersect($no_support_dimension, $dimensions)) {
            throw new AppException('含有区服不支持的维度，请重新选择维度');
        }

        $filter_key = array_keys($filter);
        // 维度筛选白名单
        $support_dimension_filter = [
            'ori_server_id', 'os', 'game_version', 'main_game', 'plat_id', 'platform', 'root_game',
            'device_name', 'device_price_section','clique_id',
        ];
        if (array_diff($filter_key, $support_dimension_filter)) {
            throw new AppException('含有区服不支持的维度筛选，请重新选择条件');
        }

    }


    /**
     * 非区服维度检查
     *
     * @param $dimensions
     * @param $filter
     */
    static public function checkNotServerDimension($dimensions, $filter)
    {
        // 不支持的维度
        $no_support_dimension = ['ori_server_id', 'game_version'];
        if (array_intersect($no_support_dimension, $dimensions)) {
            throw new AppException('含有不支持的维度，请重新选择维度');
        }

        $filter_key = array_keys($filter);
        // 维度筛选黑名单
        $no_support_dimension_filter = ['ori_server_id', 'game_version'];
        if (array_intersect($filter_key, $no_support_dimension_filter)) {
            throw new AppException('含有不支持的维度筛选，请重新选择条件');
        }

    }

    /**
     * 判断非回流的维度和维度筛选
     *
     * @param $dimensions
     * @param $filter
     * @param $dimension_type
     */
    static public function checkNotBackDimension($dimensions, $filter, $dimension_type)
    {
        // 不支持的维度
        $no_support_dimension = ['is_root_reg'];
        if (array_intersect($no_support_dimension, $dimensions) && $dimension_type != 3) {
            throw new AppException('含有不支持的维度，请重新选择维度');
        }

        $filter_key = array_keys($filter);
        // 维度筛选黑名单
        $no_support_dimension_filter = ['is_root_reg'];
        if (array_intersect($filter_key, $no_support_dimension_filter) && $dimension_type != 3) {
            throw new AppException('含有不支持的维度筛选，请重新选择条件');
        }
    }

    static public function checkApportionDimension($dimensions, $filter)
    {
        // 这些维度不支持自然量分摊
        $no_support_dimension = [
            "account_id"         => "媒体账户ID",
            "campaign_id"        => "广告一级",
            "adgroup_id"         => "广告二级",
            "adcre_id"           => "广告三级",
            "create_type"        => "创建方式",
            "csite"              => "资源位/搜索位",
            "ctype"              => "创意样式",
            "port_version"       => "接口版本",
            "is_mix_os"          => "投放方式",
            "company_name"       => "机构",
            "deep_bid_type"      => "深度转化方式",
            "aweme_account_type" => "公司号",
            "site_id"            => '广告位ID',
            "site"               => '广告位ID筛选',
            "apple_ppid"         => 'IOS产品页',
        ];

        $arr_dimension = array_intersect($dimensions, array_keys($no_support_dimension));

        $filter_key = array_keys($filter);
        $arr_filter = array_intersect($filter_key, array_keys($no_support_dimension));
        if ($arr_dimension || $arr_filter) {
            $dimension_list = array_unique(array_merge($arr_dimension, $arr_filter));
            $str = '';
            foreach ($dimension_list as $dimension) {
                $str .= $no_support_dimension[$dimension];
                $str .= '、';
            }
            $str = rtrim($str, '、');
            throw new AppException('含有不支持自然量分摊的维度：' . $str);
        }
    }


    /**
     * id to name
     *
     * @param $column
     *
     * @return array|string|string[]
     */
    static public function idToNameKey($column)
    {
        if ($column === 'csite') {
            return 'csite_name';
        } else if ($column === 'aweme_account') {
            return 'aweme_name';
        } else if ($column === 'apple_ppid') {
            return 'ppid_name';
        } else {
            return str_replace('_id', '_name', $column);
        }
    }

    /**
     * 维度筛选的项目组相关权限处理
     * @param $filter
     * @return array
     */
    static public function getProjectTeamFilterOptions($filter)
    {
        $options = [];
        foreach ($filter as $key => $column) {
            // 过滤空value
            if (!$column['value']) {
                continue;
            }
            switch ($key) {
                case 'platform':
                    $options[] = SqlParser::get($column, '', 'game');
                    break;
                case 'business_type':
                case 'project_team_id':
                case 'proxy_type':
                    $options[] = SqlParser::get($column);
                    break;
            }
        }

        return $options;
    }
}
