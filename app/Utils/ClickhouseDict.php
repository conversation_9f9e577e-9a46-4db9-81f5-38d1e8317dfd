<?php

namespace App\Utils;

use App\Constant\ClickhouseDictConfig;
use App\Exception\AppException;
use Tinderbox\ClickhouseBuilder\Integrations\Laravel\Builder;

final class ClickhouseDict
{
    /**
     * 字典表查询dictGet语句
     * @param string $dict_name
     * @param string $attr_name
     * @param mixed $use_key_name
     * @param string $alias
     * @param string $to_as
     * @param string $date_table_name
     * @param bool $is_any
     * @param bool $to_string
     * @param  $apportion_type
     * @return string
     */
    public static function dictSelect(
        string $dict_name,
        string $attr_name,
               $use_key_name = false,
        string $alias = 't',
        string $to_as = '',
        string $date_table_name = '',
        bool   $is_any = false,
        bool   $to_string = false,
               $apportion_type = 0
    )
    {
        if (!isset(ClickhouseDictConfig::DICT_TABLE[$dict_name])) {
            throw new AppException("简称{$dict_name}匹配不到对应字典表，请前往\App\Constant\ClickhouseDictConfig::DICT_TABLE配置");
        }
        $raw_alias = $alias;// 保存一个原始的 alias，直播间分摊需要用到


        /**
         * 查询表的日期字段名与日期的字段格式
         */
        // 条件：!in_array($dict_name, ['game', 'main_game'])解析，这么做是因为preWhere优化过后，在进行自然量分摊时，别名必须使用t表的，
        // 不然会报错。具体问鸿爷，关键词：preWhere和自然量分摊时的oda冲突
        if ($apportion_type && !in_array($dict_name, ['game', 'main_game'])) {
            $alias = 'oda';
            $date_field = 'log_date';
            $date_type = 'date';
        }

        // 需求：官网分摊时，要求game_id，game_key都从oda取。
        // 吐槽：业务太多特殊处理逻辑了，有的是分摊类别的逻辑，有些是sql写法的特殊逻辑，比如查注册数据直接连分摊表就可以实现，
        //        但是消耗查询可能会搞个嵌套子查询，就导致某些别名（oda和t）或者指标不能使用，哎。。。
        elseif ($apportion_type == 1 && $dict_name == 'game') {
            $alias = 'oda';
            $date_field = 'log_date';
            $date_type = 'date';
        } else {
            // 又有一个特殊处理，麻了
            if ($attr_name === 'is_old_game') {
                $date_field = ClickhouseDictConfig::GAME_DIMENSION_MAP[$date_table_name]['name'] ?? '';
                $date_type = ClickhouseDictConfig::GAME_DIMENSION_MAP[$date_table_name]['type'] ?? '';
            } else {
                $date_field = ClickhouseDictConfig::TABLE_DATE_COLUMN[$date_table_name]['name'] ?? '';
                $date_type = ClickhouseDictConfig::TABLE_DATE_COLUMN[$date_table_name]['type'] ?? '';
            }

        }

        $dict_config = ClickhouseDictConfig::DICT_TABLE[$dict_name];
        $dict_table = $dict_config['table'];

        // 若use_key_name为真，且不是bool类型，则使用输入的 use_key_name 当作组合键
        // 若use_key_name为真，且是bool类型，则使用对应字典表配置的 key_name 当作组合键
        // 若use_key_name为假，则使用对应字典表配置的 union_type 与 union_column 匹配组合键
        if (!is_bool($use_key_name) && $use_key_name) {
            $dict_key = substr($use_key_name, -4, 4) === '_key' ? $use_key_name : $dict_config['union_type'] . "(" . $use_key_name . ")";
        } elseif (is_bool($use_key_name) && $use_key_name) {
            if ($dict_name === 'agent' || $dict_name == 'game') {
                $dict_key = "$alias." . $dict_config['key_name'];
            } else {
                // 直播间分摊特殊处理
                if ($apportion_type != 2) {
                    $dict_key = $dict_config['key_name'];
                } else {
                    $dict_key = "$raw_alias." . $dict_config['key_name'];
                }
            }

        } else {
            $column = '';
            foreach ($dict_config['union_column'] as $item) {
                // 特殊：渠道消耗字典表hash_key包含日期
                if ($item === 'cost_date') {
                    if ($date_type !== 'date') {
                        $column .= "toDate({$alias}.$date_field),";
                    } else {
                        $column .= "{$alias}.$date_field,";
                    }
                } else {
                    $column .= ($apportion_type ? "$alias." : "") . "$item,";
                }
            }
            $column = rtrim($column, ',');
            $dict_key = $dict_config['union_type'] . "({$column}) as " . $dict_config['key_name'];
        }

        // DictGet方法
        $dict_method = ClickhouseDictConfig::DICT_METHOD[$attr_name] ?? "dictGet";

        // 特殊：RANGE_HASHED()字典的dictGet要多加一个date参数
        if ($dict_config['type'] === 'RANGE_HASHED') {
            if (!$date_field || !$date_type) {
                throw new AppException("RANGE_HASHED字典表({$dict_table})使用Dict时需要使用数据表({$date_table_name})的日期字段与日期格式，，请前往\App\Constant\ClickhouseDictConfig::TABLE_DATE_COLUMN配置");
            }
            if (isset($dict_config['date_type'])) {
                $dict_str = "{$dict_method}({$dict_table}, '{$attr_name}', {$dict_key}, {$dict_config['date_type']}({$alias}.{$date_field}))";
            } else {
                if ($date_type !== 'date') {
                    $dict_str = "{$dict_method}({$dict_table}, '{$attr_name}', {$dict_key}, {$alias}.{$date_field})";
                } else {
                    $dict_str = "{$dict_method}({$dict_table}, '{$attr_name}', {$dict_key}, toDateTime({$alias}.{$date_field}))";
                }
            }
        } else {
            $dict_str = "{$dict_method}({$dict_table}, '{$attr_name}', {$dict_key})";
        }

        // 聚合函数不需要额外的group by
        if ($is_any) {
            if ($to_string) {
                return "any(toString(" . $dict_str . "))" . ($to_as ? " AS {$to_as}" : "");
            }
            return "any(" . $dict_str . ")" . ($to_as ? " AS {$to_as}" : "");
        }

        if ($to_string) {
            return "toString(" . $dict_str . ")" . ($to_as ? " AS {$to_as}" : "");
        }
        return $dict_str . ($to_as ? " AS {$to_as}" : "");
    }

    public static function getTableDateColumn($table_name)
    {
        return ClickhouseDictConfig::TABLE_DATE_COLUMN[$table_name] ?? [];
    }

    public static function getDictTableByAlias($alias)
    {
        return ClickhouseDictConfig::DICT_TABLE[$alias] ?? [];
    }

    /**
     * 特殊获取 main_game_id  main_game_name
     * @return string[]
     */
    public static function dictMainGameId()
    {
        // main_game_id
        $dict_main_id = "dictGet(tanwan_datahub.dim_main_game_id_dict, 'main_game_id', main_game_key)";
        $dict_mix_id = "dictGet(tanwan_datahub.dim_mix_main_game_id_dict, 'mix_id', main_game_key)";

        // main_game_name
        $dict_main_name = "dictGet(tanwan_datahub.dim_main_game_id_dict, 'main_game_name', main_game_key)";
        $dict_mix_name = "dictGet(tanwan_datahub.dim_mix_main_game_id_dict, 'mix_name', main_game_key)";
        return [$dict_main_id, $dict_mix_id, $dict_main_name, $dict_mix_name];
    }
}
