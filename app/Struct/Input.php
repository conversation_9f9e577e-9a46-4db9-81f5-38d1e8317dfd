<?php

namespace App\Struct;

use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use Swoole\Http\Request;
use Valitron\Validator;

/**
 * request类
 *
 * <AUTHOR>
 * @since  2017-08-24
 */
class Input implements \ArrayAccess, \IteratorAggregate
{
    /**
     * @var array request的post和get合并的数组
     */
    private $data;

    /**
     * @param Request $request
     */
    public function setData($request)
    {
        $post_json = $request->rawContent();
        $request->post = $request->post ?? [];
        $request->get = $request->get ?? [];
        $request->files = $request->files ?? [];
        if ($post_json) {
            $post_data = json_decode($post_json, true);
            if (is_array($post_data)) {
                $request->post = array_merge($request->post, $post_data);
            }
        }

        $this->data = array_merge($request->get, $request->post, $request->files);
    }

    public function setDataByArray(array $data)
    {
        $this->data = $data;
    }

    public function setDataField($key, $value)
    {
        $this->data[$key] = $value;
    }

    /**
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * 接口必要参数验证
     * @see https://packagist.org/packages/vlucas/valitron
     * $input->verify(['account' => ['required', ['lengthMin', 5]], 'password' => [['lengthMin', 8]]]);
     *
     * @param array $need_param 需要验证的参数
     * @param boolean $empty 是否验非空，默认严格验证 不能为空
     * @param array $verify_data 被验证的源数据 默认为空 空则取$this->data
     */
    public function verify(array $need_param, $empty = false, $verify_data = [])
    {
        $isset_data = empty($verify_data) ? $this->data : $verify_data;
        if (is_array(current($need_param))) {
            $v = new Validator($isset_data);
            $v->mapFieldsRules($need_param);
            if (!$v->validate()) {
                $errors = $v->errors();
                foreach ($errors as $field => $error) {
                    $errors[$field] = implode(',', $error);
                }
                $error_message = implode(' | ', array_values($errors));
                throw new AppException($error_message, ResponseCode::PARAM_ERROR);
            }
        } else if ($empty) {
            foreach ($need_param as $item) {
                if (!isset($isset_data[$item])) {
                    throw new AppException('缺少必要参数：' . $item, ResponseCode::MISSING_PARAM);
                }
            }
        } else {
            foreach ($need_param as $item) {
                if (!isset($isset_data[$item])) {
                    throw new AppException('缺少必要参数：' . $item, ResponseCode::MISSING_PARAM);
                }

                if (is_string($isset_data[$item]) && trim($isset_data[$item]) !== '') {
                    continue;
                } elseif (is_array($isset_data[$item]) && $isset_data[$item] !== []) {
                    continue;
                } elseif (is_int($isset_data[$item])) {
                    continue;
                } elseif (!empty($isset_data[$item])) {
                    continue;
                }

                throw new AppException('缺少必要参数：' . $item, ResponseCode::MISSING_PARAM);
            }
        }

    }


    public function __get($name)
    {
        return $this->data[$name] ?? null;
    }

    public function __set($name, $value)
    {
        $this->data[$name] = $value;
    }

    public function offsetExists($offset)
    {
        return array_key_exists($offset, $this->data);
    }

    public function offsetGet($offset)
    {
        return $this->data[$offset] ?? null;
    }

    public function offsetSet($offset, $value)
    {
        $this->data[$offset] = $value;
    }

    public function offsetUnset($offset)
    {
        unset($this->data[$offset]);
        return true;
    }

    public function getIterator()
    {
        return new \ArrayIterator($this->data);
    }
}