<?php

namespace App\Struct;

use App\Utils\RoutePermissionValidator;
use Illuminate\Support\Collection;

/**
 * 存在session里面的各个字段
 *
 * @property array user_info
 * @property int id
 * @property int user_id
 * @property string avatar
 * @property string name //中文
 * @property string account //拼音
 * @property string mobile
 * @property string open_id
 * @property int super_manager
 * @property array user_level
 * @property int state
 * @property array permission
 * @property array permission_uri
 * @property array permission_all_list
 * @property Collection platform_list
 * @property string phrase
 * @property int show_phrase
 * @property RoutePermissionValidator $route_permission_validator
 * @property string staff_number
 */
class Session
{

    /**
     * 会话唯一标识 类似session_id
     *
     * @var string
     */
    private $token;

    /**
     * session版本前缀，强制所有人重登时更换
     */
    const SESSION_VERSION_PREFIX = 'session_v1.5_';

    /**
     * session数据缓冲区。在Redis中拿出来的数据放到此处
     *
     * @var array
     */
    private $session_data = [];

    public function __construct($token, $expire = 86400)
    {
        $this->token = self::SESSION_VERSION_PREFIX . $token;
        RedisCache::getInstance()->hSet($this->token, 'placeholder', 1);
        RedisCache::getInstance()->expire($this->token, $expire);
    }

    public function setSessionData($data)
    {
        $this->session_data = $data;
    }

    public function getToken()
    {
        return $this->token;
    }

    public function getUserInfo()
    {
        return [
            'id'               => $this->get('id'),
            'user_id'          => $this->get('user_id'),
            'account'          => $this->get('account'),
            'avatar'           => $this->get('avatar'),
            'name'             => $this->get('name'),
            'mobile'           => $this->get('mobile'),
            'open_id'          => $this->get('open_id'),
            'super_manager'    => $this->get('super_manager'),
            'merge_dimension'  => $this->get('merge_dimension') === false ? 1 : intval($this->get('merge_dimension')),
            'dimension_type'   => $this->get('dimension_type') === false ? 3 : intval($this->get('dimension_type')),
            'daily_report_dim' => json_decode($this->get('daily_report_dim')),
        ];
    }

    public function getUserDetail($module)
    {
        $user_level = $this->get("user_level.$module");
        return [
            'level'                               => $user_level === -1 ? 0 : $user_level['level'],
            'rank_id'                             => $user_level === -1 ? 0 : $user_level['rank_id'],
            'platform_id'                         => $user_level === -1 ? null : $user_level['platform_id'],
            'department_id'                       => $user_level === -1 ? null : $user_level['department_id'],
            'department_group_id'                 => $user_level === -1 ? null : $user_level['department_group_id'],
            'department_group_position_id'        => $user_level === -1 ? null : $user_level['department_group_position_id'],
            'department_group_position_worker_id' => $user_level === -1 ? null : $user_level['department_group_position_worker_id'],
            'leader'                              => $user_level === -1 ? 1 : $user_level['leader'],
            'type'                                => $user_level === -1 ? 0 : $user_level['type'],
        ];
    }

    /**
     * 设置session某个字段的值
     *
     * @param $filed
     * @param $value
     *
     * @return bool
     */
    public function set($filed, $value)
    {
        if (!$this->token) return false;
        RedisCache::getInstance()->hSet($this->token, $filed, serialize($value));
        return true;
    }

    /**
     * 设置多个session字段
     *
     * $this->mSet(array('key0' => 'value0', 'key1' => 'value1'));
     *
     * @param array $data
     */
    public function mSet(array $data)
    {
        foreach ($data as &$item) {
            $item = serialize($item);
        }
        RedisCache::getInstance()->hMSet($this->token, $data);
    }

    /**
     * 获取session某个字段的值
     *
     * @param $field
     *
     * @return mixed
     */
    public function get($field)
    {
        if (!$this->token) return false;

        // session_data缓冲区为空的情况下  或者 字段值不存在的情况下 都去获取一下Redis的session数据放到缓冲区
        if (empty($this->session_data)) {
            $this->session_data = RedisCache::getInstance()->hGetAll($this->token);
        }
        $field_list = [];
        if (strpos($field, 'user_level.') === 0) {
            $field_list = explode('.', $field);
            $field = 'user_level';
        }


        if (!isset($this->session_data[$field]) || empty($this->session_data[$field])) {
            $this->session_data = RedisCache::getInstance()->hGetAll($this->token);
        }

        // 最终如果还没该字段的值，则返回false
        if (!isset($this->session_data[$field])) return false;

        $data = unserialize($this->session_data[$field]);
        if ($field_list) {
            if ($data === -1) {
                return -1;
            } else {
                if (count($field_list) === 2) {
                    return $data[$field_list[1]];
                } elseif (count($field_list) === 3) {
                    return $data[$field_list[1]][$field_list[2]];
                } else {
                    return false;
                }
            }

        } else {
            return $data;
        }
    }

    /**
     * 获取session所有数据
     *
     * @return array
     */
    public function getAll()
    {
        if (!$this->token) return [];
        return $this->session_data;
    }

    /**
     * 销毁session并清空session缓冲区
     */
    public function destroy()
    {
        if (!$this->token) return;
        RedisCache::getInstance()->del($this->token);
        $this->session_data = [];
    }

    public function bindToken($account)
    {
        RedisCache::getInstance()->hSet('session_token_bind', $account, $this->token);
    }

    public function __get($filed)
    {
        return $this->get($filed);
    }

    public function __set($filed, $value)
    {
        $this->set($filed, $value);
    }
}
